//go:generate gen_sql -types=CAFlowParams
syntax = "proto3";

package connected_account;

import "api/connected_account/enums/enums.proto";
import "api/frontend/deeplink/deeplink.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/connected_account";
option java_package = "com.github.epifi.gamma.api.connected_account";

// A flow corresponds to a client showing an intention to start the process of connecting a new account
message ConnectionFlow {
  string id = 1;
  string actor_id = 2;

  // A unique name identifying a particular type of flows
  enums.CAFlowName ca_flow_name = 3;

  // Parameters that may be used later in the flow to take actions like redirecting to a specific screen
  CAFlowParams ca_flow_params = 4;

  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  google.protobuf.Timestamp deleted_at = 7;

  // Identifier provided by client to identify and reuse a flow later
  string client_req_id = 8;
}

// Parameters related to an account connection flow
// like the deeplink to redirect after consent for connecting account is provided by user
// or after account data is pulled successfully for the user.
message CAFlowParams {
  // Deeplink to redirect to after data for an account has been pulled successfully from AA
  // E.g., a summary of all accounts connected by user till date
  // https://www.figma.com/design/ff6C5LsDIkwpPfw84k43rM/FFF-%E2%80%A2%C2%A0Connected-Accounts?node-id=2801-33301&t=pceySVGEr9uDzqgI-4
  frontend.deeplink.Deeplink data_pull_success_redirection_deeplink = 1;

  // Deeplink to redirect to if data for an account could not be pulled successfully from AA
  // E.g., a screen to retry connecting account
  // https://www.figma.com/design/ff6C5LsDIkwpPfw84k43rM/FFF-%E2%80%A2%C2%A0Connected-Accounts?node-id=2801-33344&t=pceySVGEr9uDzqgI-4
  frontend.deeplink.Deeplink data_pull_failure_redirection_deeplink = 2;
}
