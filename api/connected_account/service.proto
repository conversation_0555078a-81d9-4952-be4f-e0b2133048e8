syntax = "proto3";

package connected_account;

import "api/accounts/account_type.proto";
import "api/connected_account/enums/enums.proto";
import "api/connected_account/external/external.proto";
import "api/connected_account/internal/account.proto";
import "api/connected_account/internal/connection_flow.proto";
import "api/connected_account/internal/consent.proto";
import "api/connected_account/internal/data.proto";
import "api/connected_account/internal/transaction.proto";
import "api/connected_account/transaction/aa_transaction.proto";
import "api/dynamic_elements/dynamic_elements.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/order/aa/transaction.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/timeline/service.proto";
import "api/typesv2/bank.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/document_proof.proto";
import "api/typesv2/file.proto";
import "api/user/user.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/connected_account";
option java_package = "com.github.epifi.gamma.api.connected_account";


message StartConsentFlowRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  string vua = 2 [(validate.rules).string.min_len = 1];
  // Optional parameter as of now to pass in request so that backend can connect to the AA which is passed in request
  // This is being added for aujus certification as it requires backend to connect to multiple AA's simultaneously
  // If this is not passed in the request, we by default connect to the configure AA ID in the config: ONE MONEY
  connected_account.enums.AaEntity aa_entity = 3;
  connected_account.enums.ConsentRequestPurpose consent_request_purpose = 4;
  // ca_flow_name represents the name of other service which is trying to use connected account flow
  connected_account.enums.CAFlowName ca_flow_name = 5;
  // number of consent handles to generate
  int32 num_of_consent_handles_to_generate = 6;
}

message StartConsentFlowResponse {
  rpc.Status status = 1;
  string consent_handle = 2 [deprecated = true];
  repeated string consent_handle_list = 3;
}

message StartDataFlowRequest {
  // Optional field to initiate data flow request only for a particular customer
  string vua = 1;
}

message StartDataFlowResponse {
  rpc.Status status = 1;
}

message GetConsentRequest {
  string consent_id = 1 [(validate.rules).string.min_len = 1];
}

message GetConsentResponse {
  rpc.Status status = 1;
  connected_account.Consent consent = 2;
  connected_account.enums.AaEntity aa_entity = 3;
  string consent_handle = 4;
}

message GetConsentRequestDetailsRequest {
  string consent_handle = 1 [(validate.rules).string.min_len = 1];
}

message GetConsentRequestDetailsResponse {
  rpc.Status status = 1;
  connected_account.ConsentRequest consent_request = 2;
}

message GetDataFetchAttemptRequest {
  string session_id = 1 [(validate.rules).string.min_len = 1];
}

message GetDataFetchAttemptResponse {
  rpc.Status status = 1;
  connected_account.DataFetchAttempt data_fetch_attempt = 2;
  connected_account.enums.AaEntity aa_entity = 3;
  connected_account.Consent consent = 4;
}

message GetConsentParamsRequest {

}

message GetConsentParamsResponse {
  // TODO: (ismail) returning consent mode, fetch type, fi types list and data life
  rpc.Status status = 1;
  google.protobuf.Timestamp data_range_from = 2;
  google.protobuf.Timestamp data_range_to = 3;
  string purpose = 4;
  google.protobuf.Timestamp consent_expiry = 5;
  connected_account.ConsentTypes consent_types = 6;
  connected_account.Frequency frequency = 7;
  connected_account.DataLife data_life = 8;
}

message GetLinkedAaAccountsRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message GetLinkedAaAccountsResponse {
  rpc.Status status = 1;
  repeated connected_account.external.AccountDetails accounts = 2;
}

message GetAccountsRequest {
  // mandatory
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // mandatory
  repeated connected_account.external.AccountFilter account_filter_list = 2 [(validate.rules).repeated.min_items = 1];
  // optional field to add filter for account instrument type
  repeated connected_account.enums.AccInstrumentType acc_instrument_type_list = 7;
}

message GetAccountsResponse {
  rpc.Status status = 1;
  repeated connected_account.external.AccountDetails account_details_list = 2;
}

message GetAccountDetailsRequest {
  // mandatory
  string account_id = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.uuid = true];
  repeated connected_account.external.AccountDetailsMask account_details_mask_list = 2;
  // Optional
  // To validate the account fetched corresponds to actor or not
  // TODO(sainath): Need to make it mandatory once we are sure that actor_id coming from all client callers
  string actor_id = 3;
}

message GetAccountDetailsResponse {
  rpc.Status status = 1;
  connected_account.external.AccountDetails account_details = 2;
  connected_account.external.ProfileDetails profile_details = 3 [deprecated = true];
  oneof Summary {
    connected_account.external.DepositSummary deposit_summary = 4;
    connected_account.external.RecurringDepositSummary recurring_deposit_summary = 5;
    connected_account.external.TermDepositSummary term_deposit_summary = 6;
    connected_account.external.EquitySummary equity_summary = 7;
    connected_account.external.EtfSummary etf_summary = 12;
    connected_account.external.ReitSummary reit_summary = 14;
    connected_account.external.InvitSummary invit_summary = 15;
    connected_account.external.NpsSummary nps_summary = 18;
  }
  oneof profile {
    connected_account.external.HoldersDetails deposit_profile = 8;
    connected_account.external.HoldersDetails recurring_deposit_profile = 9;
    connected_account.external.HoldersDetails term_deposit_profile = 10;
    connected_account.external.EquityProfileDetails equity_profile = 11;
    connected_account.external.EtfProfileDetails etf_profile = 13;
    connected_account.external.ReitProfileDetails reit_profile = 16;
    connected_account.external.InvitProfileDetails invit_profile = 17;
    connected_account.external.NpsProfileDetails nps_profile_details = 19;
  }
}

message DeleteAccountRequest {
  // mandatory
  string account_id = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.uuid = true];
  // mandatory
  // To validate the account fetched corresponds to actor or not
  string actor_id = 2 [(validate.rules).string.min_len = 1];
}

message DeleteAccountResponse {
  rpc.Status status = 1;
}

message GetRelatedAccountsForDisconnectRequest {
  // mandatory
  string account_id = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.uuid = true];
  // mandatory
  // To validate the account fetched corresponds to actor or not
  string actor_id = 2 [(validate.rules).string.min_len = 1];
}

message GetRelatedAccountsForDisconnectResponse {
  rpc.Status status = 1;
  // List of accounts which need to be disconnected with current one
  repeated connected_account.external.AccountDetails account_detail_list = 2;
  // List of consent handles which need to be revoked to disconnect the accounts
  repeated string consent_handle_list = 3;
  // List of consent ids to disconnect the accounts
  repeated string consent_id_list = 4;
  // Bottom-sheet screen for disconnect confirmation
  frontend.deeplink.Deeplink confirm_bottom_sheet = 5;
}

message GetRelatedAccountsForDeleteRequest {
  // Account id for which related accounts are to be fetched for delete
  string account_id = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.uuid = true];
  // mandatory
  // To validate the account fetched corresponds to actor or not
  string actor_id = 2 [(validate.rules).string.min_len = 1];
}

message GetRelatedAccountsForDeleteResponse {
  // Status of the request
  rpc.Status status = 1;
  // List of accounts which need to be deleted with current one
  repeated connected_account.external.AccountDetails account_detail_list = 2;
  // confirmation bottom sheet screen for delete
  frontend.deeplink.Deeplink confirm_bottom_sheet = 3;
}

message CheckReoobeRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  string vua = 2 [deprecated = true];
}

message CheckReoobeResponse {
  rpc.Status status = 1;
  // List of accounts which need to be deleted in case of reoobe by a user
  repeated connected_account.external.AccountDetails account_detail_list = 2;
  // In case user went through reoobe, we will return old vua of the user
  string old_vua = 3;
}

message HandleReoobeRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  string vua = 2 [deprecated = true];
  repeated string account_id_list = 3 [deprecated = true];
}

message HandleReoobeResponse {
  rpc.Status status = 1;
}

message GetAvailableFipsRequest {
  string actor_id = 1;
  // app_platform and app_version needed for percentage rollout
  api.typesv2.common.Platform app_platform = 2;
  uint32 app_version = 3;
}

message GetAvailableFipsResponse {
  rpc.Status status = 1;
  repeated api.typesv2.Bank bank_list = 2;
}

message CreateBankPreferenceRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  repeated api.typesv2.Bank bank_list = 2 [(validate.rules).repeated.min_items = 1];
}

message CreateBankPreferenceResponse {
  rpc.Status status = 1;
}

message GetAllAccountsRequest {
  // page context for pagination
  rpc.PageContextRequest page_context = 1;
  // mandatory
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // optional param for account statues
  repeated connected_account.external.AccountFilter account_filter_list = 3;
  // optional param to fetch accounts created after given timestamp : inclusive
  google.protobuf.Timestamp created_after = 4;
  // optional param to fetch accounts created before given timestamp : exclusive
  google.protobuf.Timestamp created_before = 5;
  // optional param on add filter on fip ids
  repeated string fip_id_list = 6;
  // optional param on acc instrument types
  repeated connected_account.enums.AccInstrumentType acc_instrument_type_list = 7;
  // optional param to only fetch accounts with given PAN number.
  // Note : PAN Filtering will not work for account types other than Deposit, Recurring Deposit and Term Deposit.
  // this is due to inconsistency in the way we store Profile Details ( which contains PAN ) for Deposit, RD, TD vs Equity
  string panNumber = 8;
  // filter accounts based on sub type (savings, current, fixed deposit, sweep deposit etc)
  repeated connected_account.AccountSubType account_sub_type_list = 9;
}

message GetAllAccountsResponse {
  // status
  rpc.Status status = 1;
  // page context response
  rpc.PageContextResponse page_context = 2;
  // List of accounts returned
  repeated connected_account.external.AccountDetails account_details_list = 3;
}

message GetConsentsForAccountRequest {
  // mandatory account id for which consents are to be fetched
  string account_id = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.uuid = true];
  // Optional consent status filter to fetch only consents with these statuses
  repeated connected_account.enums.ConsentStatus consent_status_list = 2;
}

message GetConsentsForAccountResponse {
  // status
  rpc.Status status = 1;
  // List of consents for that account
  repeated connected_account.external.ConsentDetails consent_details_list = 2;
}

message GetDataFetchAttemptsRequest {
  // page context for pagination
  rpc.PageContextRequest page_context = 1;
  // search parameter
  oneof Filter {
    string consent_handle = 2;

    string account_id = 3;
  }
}

message GetDataFetchAttemptsResponse {
  enum Status {
    OK = 0;
    // This means that consent for which attempts are being requested had failed. Caller can handle this status as
    // per the business logic needed.
    CONSENT_HANDLE_STATUS_FAILED = 101;
  }
  // status
  rpc.Status status = 1;
  // page context response
  rpc.PageContextResponse page_context = 2;
  // list of data fetch attempt details
  repeated connected_account.external.DataFetchAttemptDetails data_fetch_attempt_details_list = 3;
  // consent request
  connected_account.ConsentRequest consent_request = 4;
}

message GetAllowedConfigRequest {
  string actor_id = 1;
  // app_platform and app_version needed for percentage rollout
  api.typesv2.common.Platform app_platform = 2;
  uint32 app_version = 3;
  enums.CAFlowName ca_flow_name = 4;
}

message GetAllowedConfigResponse {
  // status
  rpc.Status status = 1;
  repeated external.FipConfig allowed_fip_list = 2;
  // Phone number of the user
  api.typesv2.common.PhoneNumber phone_number = 3 [deprecated = true];
  // user profile which contains Phone number, PAN details, DOB, etc
  user.Profile user_profile_details = 4;
}

message ReplayAccountEventRequest {
  repeated string account_id_list = 1 [(validate.rules).repeated.min_items = 1];
}

message ReplayAccountEventResponse {
  rpc.Status status = 1;
  repeated string account_id_failure_list = 2;
}

message ReplayTxnEventRequest {
  repeated string actor_id_list = 1 [(validate.rules).repeated.min_items = 1];
  // txns created after given timestamp : inclusive
  google.protobuf.Timestamp created_after = 2;
  // txns created before given timestamp : exclusive
  google.protobuf.Timestamp created_before = 3;
}

message ReplayTxnEventResponse {
  rpc.Status status = 1;
  // list of actor_ids for which atleast one txn failed
  repeated string actor_id_failure_list = 2;
}

message CheckAccountSyncRequest {
  string account_id = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.uuid = true];
}

message CheckAccountSyncResponse {
  rpc.Status status = 1;
  connected_account.enums.AccountSyncStatus account_sync_status = 2;
  connected_account.enums.AccountSyncAction account_sync_action = 3;
  connected_account.enums.AccountSyncActionReason account_sync_action_reason = 4;
  int32 transaction_count = 5;
}

message SyncAccountRequest {
  string account_id = 1 [(validate.rules).string.min_len = 1, (validate.rules).string.uuid = true];
}

message SyncAccountResponse {
  rpc.Status status = 1;
  connected_account.enums.AccountSyncStatus account_sync_status = 2;
  connected_account.enums.AccountSyncAction account_sync_action = 3;
  connected_account.enums.AccountSyncActionReason account_sync_action_reason = 4;
  int32 transaction_count = 5;
}

message StartDataFlowV2Request {
}

message StartDataFlowV2Response {
  rpc.Status status = 1;
}

message GetAaEntityForConnectRequest {
  api.typesv2.common.Platform app_platform = 1;
  uint32 app_version = 2;
  string actor_id = 3;
  string fip_id = 4;
}

message GetAaEntityForConnectResponse {
  rpc.Status status = 1;
  connected_account.enums.AaEntity aa_entity = 2;
}

message CheckAaHeartbeatStatusRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // aa entity to check status for
  connected_account.enums.AaEntity aa_entity = 2;
}

message CheckAaHeartbeatStatusResponse {
  rpc.Status status = 1;
  // aa entity status
  connected_account.enums.AaHeartbeatStatus aa_heartbeat_status = 2;
}

message FipMetaIdentifier {
  oneof identifier {
    string fip_id = 1;
    api.typesv2.Bank bank = 2;
  }
}

message GetFipMetaRequest {
  // either one of fip_id or bank enum should be passed in request while trying to fetch fip meta
  // deprecated in favour of repeated identifiers to support bulk fetching
  oneof identifier {
    string fip_id = 1 [deprecated = true];
    api.typesv2.Bank bank = 2 [deprecated = true];
  }

  repeated FipMetaIdentifier identifiers = 3;
}

message GetFipMetaResponse {
  rpc.Status status = 1;
  // bank meta
  // deprecated in favour of FipMetaList to support bulk fetching
  connected_account.external.FipMeta fip_meta = 2 [deprecated = true];
  repeated connected_account.external.FipMeta fip_meta_list = 3;
}

message GetAllFipMetasRequest {
  // ca_flow_name denotes the ca flows for which user connected account flow will be initiated
  enums.CAFlowName ca_flow_name = 2;
  api.typesv2.common.Platform app_platform = 3;
}

message GetAllFipMetasResponse {
  rpc.Status status = 1;
  // list of bank metas
  repeated connected_account.external.FipMeta fip_meta_list = 2;
}

message GetAccountDetailsBulkRequest {
  // list of account ids for which bulk fetch request is being made
  repeated string account_id_list = 1 [(validate.rules).repeated.min_items = 1];
  // account details mask
  repeated connected_account.external.AccountDetailsMask account_details_mask_list = 2;
}

message GetAccountDetailsBulkResponse {
  rpc.Status status = 1;
  // Map of account details
  // Key can be one of the account id's for which the request was made
  // Value consists of the account, profile and summary details for that particular account id
  map<string, AccountProfileSummaryDetails> account_details_map = 2;
}

message AccountProfileSummaryDetails {
  connected_account.external.AccountDetails account_details = 1;
  connected_account.external.ProfileDetails profile_details = 2 [deprecated = true];
  oneof Summary {
    connected_account.external.DepositSummary deposit_summary = 3;
    connected_account.external.RecurringDepositSummary recurring_deposit_summary = 4;
    connected_account.external.TermDepositSummary term_deposit_summary = 5;
    connected_account.external.EquitySummary equity_summary = 6;
    connected_account.external.EtfSummary etf_summary = 7;
    connected_account.external.ReitSummary reit_summary = 13;
    connected_account.external.InvitSummary invit_summary = 14;
    connected_account.external.NpsSummary nps_summary = 17;
  }
  oneof profile {
    connected_account.external.HoldersDetails deposit_profile = 8;
    connected_account.external.HoldersDetails recurring_deposit_profile = 9;
    connected_account.external.HoldersDetails term_deposit_profile = 10;
    connected_account.external.EquityProfileDetails equity_profile = 11;
    connected_account.external.EtfProfileDetails etf_profile = 12;
    connected_account.external.ReitProfileDetails reit_profile = 15;
    connected_account.external.InvitProfileDetails invit_profile = 16;
    connected_account.external.NpsProfileDetails nps_profile_details = 18;
  }
}

message PublishTxnEventsByTxnIdsRequest {
  repeated string txn_ids = 1;
}

message PublishTxnEventsByTxnIdsResponse {
  rpc.Status status = 1;
}

message GetAuthTokenRequest {
  connected_account.enums.AaEntity aa_entity = 1;
}

message GetAuthTokenResponse {
  rpc.Status status = 1;
  string token = 2;
  int32 expiry_duration_minutes = 3;
}

message GetTxnDetailsRequest {
  // primary key (id) of txns table
  string id = 1 [(validate.rules).string.min_len = 1];
}

message GetTxnDetailsResponse {
  rpc.Status status = 1;
  connected_account.external.Transaction transaction = 2;
}

message GetTxnListByActorIdAndAccRefIdRequest {
  // mandatory
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // mandatory
  string account_reference_id = 2 [(validate.rules).string.min_len = 1];
  // page context for pagination
  rpc.PageContextRequest page_context = 3;
}

message GetTxnListByActorIdAndAccRefIdResponse {
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  repeated external.Transaction aa_transaction = 3;
}

message GetTotalTransactionsCountRequest {
  // mandatory
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // it is optional if client doesn't provide then it will return all transaction for that actor
  repeated string account_reference_id = 2;
  // return all transactions count for which transaction_date is after given start_time
  google.protobuf.Timestamp start_time = 3;
}

message GetTotalTransactionsCountResponse {
  rpc.Status status = 1;
  repeated AccountIdAndTotalTransactions account_id_and_total_transactions = 2;
}

message AccountIdAndTotalTransactions {
  string account_reference_id = 1;
  int64 total_transactions = 2;
}

message GetValidAccountDetailsForUSStocksRequest {
  message ValidityParams {
    // min no of txns required for a valid connected account should be greater or equal to min_txn_count
    int32 min_txn_count = 1;
  }

  // mandatory
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // considers all those transactions which are created after start_time
  google.protobuf.Timestamp start_time = 2 [(validate.rules).timestamp.required = true];
  // considers all those transactions which are created before end_time
  google.protobuf.Timestamp end_time = 3 [(validate.rules).timestamp.required = true];
  // ValidityParams used to define constraints for a valid connected account
  ValidityParams validity_params = 4;
}

message GetValidAccountDetailsForUSStocksResponse {
  enum Status {
    // if connected account has sufficient number of transaction
    OK = 0;
    // if no connected account is present of given actor
    NOT_FOUND = 5;
    // if no connected account is valid because of insufficient transaction in given time period
    INSUFFICIENT_TRANSACTION = 105;
    // Data sync for the account is pending and has not started yet
    DATA_SYNC_PENDING = 106;
  }
  rpc.Status status = 1;
  // to identify the reason of not having any valid connected account
  external.AccountDetails account_details = 3;
}

message DownloadConnectedAccountBankStatementRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // unique account id for a connected account
  string account_reference_id = 2 [(validate.rules).string.min_len = 1];
  // considers all those transactions which are created after start_time
  google.protobuf.Timestamp start_time = 3 [(validate.rules).timestamp.required = true];
  // considers all those transactions which are created before end_time
  google.protobuf.Timestamp end_time = 4 [(validate.rules).timestamp.required = true];
  // file type to generate in(eg: pdf, binary etc)
  api.typesv2.FileType file_type = 5;
  // optional: if true, transactions will be sorted by updated_at timestamp
  // Otherwise, by default it will get sorted by transaction_date
  bool sort_by_updated_at = 6;
}

message DownloadConnectedAccountBankStatementResponse {
  enum Status {
    // if connected account has sufficient number of transaction
    OK = 0;
    // if there are no transactions during the specified time period for given connected account
    NO_TRANSACTION = 101;
  }
  rpc.Status status = 1;
  // it contains s3Path and temporary signed url of connected account statement pdf
  // Deprecated: Use AccountStatementFile instead
  api.typesv2.DocumentProof account_statement_file = 2 [deprecated = true];

  message AccountStatementFile {
    // generated account statement file url
    string file_url = 1;
  }
  AccountStatementFile account_statement_file_v2 = 5;

  // Timestamp of the earliest txn included in account statement
  google.protobuf.Timestamp start_time = 3 [(validate.rules).timestamp.required = true];

  // Timestamp of the latest txn included in account statement
  google.protobuf.Timestamp end_time = 4 [(validate.rules).timestamp.required = true];
}

message GetScreenerDecisionRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // created_after indicates to perform screener decision for accounts connected after created_after timestamp
  google.protobuf.Timestamp created_after = 2;
}

message GetScreenerDecisionResponse {
  rpc.Status status = 1;
  // represents if screener can be passed or not
  api.typesv2.common.BooleanEnum screener_pass = 2;
}

// GetSdkDeeplinkForCaFlowRequest consist of fields required for initialising the AA SDK
message GetSdkDeeplinkForCaFlowRequest {
  // mandatory
  string actor_id = 1 [(validate.rules).string.min_len = 1];

  // app version for user
  uint32 app_version = 2;

  // app platform - Android, iOS etc.
  api.typesv2.common.Platform app_platform = 3;

  // ca_flow_name represents identifier of other service which is trying to use connected account flow
  // The value for this enum has to be passed as is which is given with deeplink at the time of initializing SDK
  // In case enum identifier is not specified, then by default connected account service flow will be followed
  // mandatory
  enums.CAFlowName ca_flow_name = 4 [(validate.rules).enum = {not_in: [0]}];
}

// GetSdkDeeplinkForCaFlowRequestResponse consist of the deeplink to initiate AA SDK.
message GetSdkDeeplinkForCaFlowResponse {
  // RPC Status: INTERNAL in case when AA Entity/V2 Flow or is token auth enabled can't be determined and OK in case of no internal error
  rpc.Status status = 1;
  // deeplink to initiate AA SDK required by service which is integrating CA flow,
  frontend.deeplink.Deeplink sdk_deeplink_ca_flow = 2;
}

// GetAccountsForRenewalRequest is the request body for the GetAccountsForRenewal RPC
message GetAccountsForRenewalRequest {
  // actor_id to find accounts for the actor
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // optional: // by default returns all expiring accounts for the actor
  // if AccountIds is given, only the requested accounts is checked for consent expiry details
  repeated string account_ids = 7;
  // if OnlyExpired flag is true, accounts that are already expired alone are returned
  // else both expired and accounts that are getting expired in some threshold time is returned
  bool only_expired = 4;
  // CheckSegmentForEligibility tells if the user should be checked for eligibility from segment service for consent expiry
  // and checks db only if eligible
  // optional: by default fetches db for real time results
  bool check_segment_for_eligibility = 5;
  // when SortAccounts flag is set, the responses are sorted with account expiring sooner coming first in the list
  bool sort_accounts = 6;
}

message RenewalAccountDetails {
  // details of account
  connected_account.external.AccountDetails account_details = 1;
  // timestamp for consent expiry
  google.protobuf.Timestamp consent_expiry = 2;
}

// GetAccountsForRenewalResponse is the response body for GetAccountsForRenewal RPC
message GetAccountsForRenewalResponse {
  // RPC Status:
  // - OK in case of accounts are successfully fetched
  // - INTERNAL in all other cases of error
  rpc.Status status = 1;
  // List of accounts details eligible for renewal
  repeated RenewalAccountDetails renewal_account_details_list = 2;
}

message UpdateConsentExpiryRequest {
  // consent handle id generated by AA
  string consent_handle_id = 1;
  // consent expiry timestamp
  google.protobuf.Timestamp consent_expiry = 2;
}

message UpdateConsentExpiryResponse {
  // RPC Status:
  // - OK in case of accounts are successfully fetched
  // - INTERNAL in all other cases of error
  rpc.Status status = 1;
}

message CheckEligibilityForConsentRenewalRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message CheckEligibilityForConsentRenewalResponse {
  rpc.Status status = 1;
  // tells if an actor is eligible for consent renewal
  bool eligible = 2;
}

message ParseAATransactionRequest {
  connected_account.transaction.RawTransaction raw_txn = 1;
}

message ParseAATransactionResponse {
  rpc.Status status = 1;
  connected_account.transaction.ParsedTransaction parsed_txn = 2;
}

message ResolveInternalActorPiDetailsForAATxnRequest {
  string internal_actor_id = 1;
  AccountInfo account_info = 2;
  string account_id = 3;
}

message ResolveInternalActorPiDetailsForAATxnResponse {
  rpc.Status status = 1;
  string internal_actor_pi_id = 2;
  string internal_actor_name = 3;
}

message EnrichAATransactionRequest {
  connected_account.transaction.ParsedTransaction parsed_txn = 1;
  // timeline_resolution_source is used to decide the topic to which the Timeline created will be published
  timeline.TimelineResolutionSource timeline_resolution_source = 2;
}

message EnrichAATransactionResponse {
  rpc.Status status = 1;
  order.aa.Transaction enriched_txn = 2;
}

message AccountInfo {
  string account_number = 1;
  string masked_account_number = 2;
  string ifsc_code = 3;
  accounts.Type account_type = 4;
}

message PublishListEnrichedAATransactionRequest {
  repeated order.aa.Transaction enriched_txn_list = 1;
}

message PublishListEnrichedAATransactionResponse {
  rpc.Status status = 1;
}

// CheckIfAccountFiFedSavingAccRequest is the request for CheckIfAccountFiFedSavingAcc RPC
message CheckIfAccountFiFedSavingAccRequest {
  // mandatory field.
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // account id of the connected account, if this field is empty then make all connected accounts for user and check if Fi Fed saving acc
  // is connected or not.
  string account_id = 2;
}

// CheckIfAccountFiFedSavingAccResponse is the response for CheckIfAccountFiFedSavingAcc RPC
message CheckIfAccountFiFedSavingAccResponse {
  rpc.Status status = 1;
  // is_account_fi_fed_saving_acc is a boolean depicting if the connected account in consideration is Fi Fed saving bank account
  bool is_account_fi_fed_saving_acc = 2;
}
// BackupEnrichedTransactionsRequest is the request for BackupEnrichedTransactions RPC
message BackupEnrichedTransactionsRequest {
  EnrichedTransactionBatch enriched_txn_batch = 1;
  string path = 2;
}

message EnrichedTransactionBatch {
  repeated order.aa.Transaction enriched_txn_list = 1;
}

// BackupEnrichedTransactionsResponse is the response for BackupEnrichedTransactions RPC
message BackupEnrichedTransactionsResponse {
  rpc.Status status = 1;
}

message GetEnrichedTransactionsProgressRequest {
  string accountId = 1;
}

message GetEnrichedTransactionsProgressResponse {
  enum Status {
    // Success
    OK = 0;
    // Transient Failure
    TRANSIENT_FAILURE = 101;
  }
  // Below are the possible status codes
  // 1. 200 OK
  // 2. 101 Transient_ERROR (Data Fetch is going on. Retry in some time)
  // 3. 13 INTERNAL
  rpc.Status status = 1;
  // Percentage of raw transactions that have been enriched
  double persistencePercentage = 2;
  // Count of transactions in aa_transactions for given accountId
  uint32 rawTxnCount = 3;
  // Count of transactions in aa_enriched_transactions for given accountId
  uint32 enrichedTxnCount = 4;
}

message InitiateDataRefreshForAccountsRequest {
  // AccountId list for which data refresh is to be done
  repeated string account_id_list = 1 [(validate.rules).repeated = {min_items: 1, max_items: 50}];
}

message InitiateDataRefreshForAccountsResponse {
  rpc.Status status = 1;
  map<string, connected_account.enums.AccountDataRefreshRequestStatus> account_status_map = 2;
}

// Service to connect non-Epifi accounts of the users.
service ConnectedAccount {

  // Start the consent flow for an actor
  // Backend stores the consent parameters in config by default to post consent request for the user
  rpc StartConsentFlow (StartConsentFlowRequest) returns (StartConsentFlowResponse) {
  };

  // Start the data flow for all consents stores in DB
  // Will fetch all active consents and place data request for each one of them
  // Once session id is generated, event is published to delay queue to fetch data from AA when ready
  // will also change states accordingly as per the process
  // This RPC is meant to be called from job only and not any client facing
  rpc StartDataFlow (StartDataFlowRequest) returns (StartDataFlowResponse) {
  };

  // Get consent entity based on consent id
  // In case consent id is not found in system, RECORD NOT FOUND
  // For example : VN uses this API to validate consent id in callbacks received from AA
  rpc GetConsent (GetConsentRequest) returns (GetConsentResponse) {
  };

  // Get consent request entity by consent handle
  // In case consent handle itself is not found in system, RECORD NOT FOUND
  // For example : VN uses this API to validate consent handle in callbacks received from AA
  rpc GetConsentRequestDetails (GetConsentRequestDetailsRequest) returns (GetConsentRequestDetailsResponse) {
  };

  // Get data fetch attempt entity by session id
  // In case session id is not found in system, RECORD NOT FOUND
  // For example : VN uses this API to validate session id in callbacks received from AA
  rpc GetDataFetchAttempt (GetDataFetchAttemptRequest) returns (GetDataFetchAttemptResponse) {
  };

  // RPC to fetch parameters of consent stored in our config
  rpc GetConsentParams (GetConsentParamsRequest) returns (GetConsentParamsResponse) {
  };

  // RPC to fetch linked AA accounts of an actor
  rpc GetLinkedAaAccounts (GetLinkedAaAccountsRequest) returns (GetLinkedAaAccountsResponse) {
  };

  // RPC to fetch linked accounts of an actor, will be used by downstream systems like Pay and Search
  // Filter array for type of accounts to be fetched is mandatory, currently returns ALL or ACTIVE accounts of an actor
  // RPC status supported
  // STATUS_OK - Returns the list of all the account connected for an actor (depend on the filter, returns ALL or only ACTIVE accounts)
  // STATUS_NOT_FOUND - No accounts linked to the actor_id is found
  // STATUS_INTERNAL - Internal server error
  rpc GetAccounts (GetAccountsRequest) returns (GetAccountsResponse) {
  };

  // RPC to fetch details of a specific linked account of an actor, will be used by downstream systems like Pay and Search
  // Details of an account involve properties like fip_id, linked_ref_number, masked_account_number, type of account (Deposits, RD, TD)
  // Profile and Summary of account can be retrieved using mask
  // Behaviour of RPC is as follows
  // 1) No mask is passed - Returns only the details of the Account, without Profile or Summary
  // 2) Mask list passed - Returns details of the Account along with Profile and Summary (depending on which mask is passed in request)
  // RPC status supported
  // STATUS_OK - Returns account details of given the account_id in request
  // STATUS_NOT_FOUND - No account with the given account_id is found
  // STATUS_INTERNAL - Internal server error, may occur due to failure in parsing raw data in DB
  rpc GetAccountDetails (GetAccountDetailsRequest) returns (GetAccountDetailsResponse) {
  };

  // RPC to delete an account for a user. Account is soft deleted in DB
  // This is an idempotent rpc
  // RPC status supported
  // OK : request accepted successfully for delete
  // NOT FOUND : account not found with provided account id
  // INTERNAL : some error in executing request
  rpc DeleteAccount (DeleteAccountRequest) returns (DeleteAccountResponse) {
  };

  // RPC to fetch list of related accounts for an account which is to be disconnected.
  // An account can be related to other accounts if they are part of the same consent.
  // Any activity done on consent will directly affect all the accounts part of that consent
  // RPC status supported
  // OK : request accepted successfully for delete
  // NOT FOUND : no accounts found with provided account id
  // INTERNAL : some error in executing request
  rpc GetRelatedAccountsForDisconnect (GetRelatedAccountsForDisconnectRequest) returns (GetRelatedAccountsForDisconnectResponse) {
  }

  // RPC to check if a user went through re oobe and is coming to connect new accounts
  // In this case the vua of the user changes and hence we need to raise a flag to user to delete
  // all their existing data with previous phone number if they have done reoobe
  // Will return list of accounts for the user only if they have done reoobe
  // which need to be deleted before they can proceed further
  // RPC status supported
  // OK : success
  // INTERNAL : some error in determining reoobe check
  rpc CheckReoobe (CheckReoobeRequest) returns (CheckReoobeResponse) {
  }

  // RPC to handle user data in case they have done reoobe. We will delete all the accounts and consents for the user
  // linked to their previous phone number before they can proceed to connect bank accounts with new phone number
  // List of account id's is validated by backend and error is thrown if validation fails
  // OK : success
  // INTERNAL : some error in handling reoobe
  rpc HandleReoobe (HandleReoobeRequest) returns (HandleReoobeResponse) {
  }

  // RPC to get the list of available FIPs to be selected for preference
  // list of FIPs returned is based on the accounts for which CA has not been made available yet
  // OK : success
  // NOT FOUND : no FIPs found
  // INTERNAL : error while fetching available FIPs
  rpc GetAvailableFips (GetAvailableFipsRequest) returns (GetAvailableFipsResponse) {
  }

  // RPC to create bank preference(s) for an actor_id
  // service called only if there is at least one preference
  // entry not created in database if the same (actor_id,bank) entry has already been recorded
  // OK : success
  // INTERNAL : error in creation
  rpc CreateBankPreference (CreateBankPreferenceRequest) returns (CreateBankPreferenceResponse) {
  }

  // Paginated RPC to fetch all connected accounts of an actor
  // Will return accounts in increasing order of connection time ie created_at
  // OK : success
  // INTERNAL : any internal service error
  // NOT FOUND : no accounts found for actor
  // INVALID ARGUMENT : invalid arguments in request
  // Note : PAN Filtering will not work for account types other than Deposit, Recurring Deposit and Term Deposit.
  // this is due to inconsistency in the way we store Profile Details ( which contains PAN ) for Deposit, RD, TD vs Equity
  rpc GetAllAccounts (GetAllAccountsRequest) returns (GetAllAccountsResponse) {
  }

  // RPC to fetch all consents for an account. An account can be linked to multiple consents this will return all
  // of them if no optional filters are passed on consent status level
  // OK : success
  // INTERNAL : any internal service error
  // NOT FOUND : No consents found for account for given criteria
  // INVALID ARGUMENT : invalid arguments in request
  rpc GetConsentsForAccount (GetConsentsForAccountRequest) returns (GetConsentsForAccountResponse) {
  }

  // RPC to get data fetch attempt details
  // OK : success
  // NOT FOUND : no details found for the given consent handle
  // INTERNAL : any internal service error
  rpc GetDataFetchAttempts (GetDataFetchAttemptsRequest) returns (GetDataFetchAttemptsResponse) {
  }

  // RPC to get allowed config based on actor id
  // For different user groups different banks and different financial instruments are available
  // for controlled testing.
  // OK : success
  // INTERNAL : any internal service error
  rpc GetAllowedConfig (GetAllowedConfigRequest) returns (GetAllowedConfigResponse) {
  }

  // RPC to sync account status
  // request : list of account ids to be synced
  // OK : success
  // NOT FOUND : not found in db
  // INTERNAL : internal service error
  rpc ReplayAccountEvent (ReplayAccountEventRequest) returns (ReplayAccountEventResponse) {
  }

  // RPC to replay txn events of actor_id(s) within a specific data range
  // request : list of actor_ids, date range
  // OK : success
  // NOT FOUND : not found in db
  // INTERNAL : internal service error
  rpc ReplayTxnEvent (ReplayTxnEventRequest) returns (ReplayTxnEventResponse) {
  }

  // RPC to check account sync status, action for that account which can be done in terms of data refresh
  // Will return transaction count as well in case account data sync was successful to allow downstream to
  // reconcile with connected account. Transaction count will be returned only if account action is allowed.
  // Will return account status action along with reason.
  // OK : success
  // NOT FOUND : account not found in db
  // INVALID ARGUMENT : account id is not UUID/valid format
  // INTERNAL : internal service error
  rpc CheckAccountSync (CheckAccountSyncRequest) returns (CheckAccountSyncResponse) {
  }

  // RPC to sync data for account, given the account id.
  // In case the account is not eligible for data sync, account action will be denied with reason mentioned
  // In case the account data is refreshed successfully, account sync status will be returned as successful
  // In case account data refresh does not get completed in some time threshold, account sync status will be in progress
  // In case account data refresh fails, account sync status will be failed
  // NOTE : data syncing is a two step process and this API internally has a poll mechanism so response times for this
  // API are expected to be in range of ~15 seconds.
  // OK : success
  // NOT FOUND : account not found in db
  // INVALID ARGUMENT : account id is not UUID/valid format
  // INTERNAL : internal service error
  rpc SyncAccount (SyncAccountRequest) returns (SyncAccountResponse) {
  }

  // RPC to fetch all active consent ids from DB and push them to the consent data refresh queue to be consumed whenever possible
  // OK : success
  // NOT FOUND : no eligible consent ids found in db
  // INTERNAL : internal service error
  rpc StartDataFlowV2 (StartDataFlowV2Request) returns (StartDataFlowV2Response) {
  }

  // RPC to fetch related accounts which needs to be deleted if a user tries to delete an account
  // An account can be linked with multiple consents and consents can in turn have multiple accounts hence
  // If a user requests to delete an account all it's related accounts with consents need to be deleted as well
  // This RPC just gives the list of accounts along with current account which will be deleted as a consequence
  // of this action and client needs to call DeleteAccount RPC separately to take delete action if user confirms
  // OK : success
  // INTERNAL : internal error
  rpc GetRelatedAccountsForDelete (GetRelatedAccountsForDeleteRequest) returns (GetRelatedAccountsForDeleteResponse) {
  }

  // RPC to fetch aa entity while connecting
  // Takes care of routing logic using hash buckets
  // Upper and lower bounds can be adjusted for an aa entity, within a platform, within a user group
  // OK : success
  // INTERNAL : internal error
  // INVALID ARGUMENT : invalid argument/parameter
  rpc GetAaEntityForConnect (GetAaEntityForConnectRequest) returns (GetAaEntityForConnectResponse) {
  }

  // RPC to fetch heartbeat status of aa entity
  // if the system is down, update the status in aa_user_heartbeats table
  // to send notification when aa system is up
  // Returns Heartbeats status whether UP or DOWN
  // OK : success
  // INTERNAL : internal error
  // INVALID ARGUMENT : invalid argument/parameter
  rpc CheckAaHeartbeatStatus (CheckAaHeartbeatStatusRequest) returns (CheckAaHeartbeatStatusResponse) {
  }

  // bulk fetch RPC to fetch FIP information by list of FIP ID or bank enum
  // FIP ID is a string which uniquely identifies every FIP/Bank
  // Bank enums defined in api/types/bank.proto are also unique for every FIP/Bank (except test banks)
  // OK : success
  // NOT FOUND : bank not found, rpc is failed with not found status even if one fip in request is not found
  // INTERNAL : internal error
  rpc GetFipMeta (GetFipMetaRequest) returns (GetFipMetaResponse) {
  }

  // RPC to fetch list of FIP information for all banks defined in config - including all test banks
  // Test banks will not be returned in prod
  // OK : success
  // NOT FOUND : bank not found
  rpc GetAllFipMetas (GetAllFipMetasRequest) returns (GetAllFipMetasResponse) {
  }

  // Batch RPC to fetch account details for a given list of account ids and an account details mask
  // Returns a map where key is account id and value is account, profile and/or summary details
  // Since all account id's might not have summary details, the value of summary for some account id's will be nil
  // If an invalid account id is passed in request, that id will not have an entry in the response map
  // OK : success
  // INTERNAL : internal error
  // NOT FOUND : details not found for none of the account ids in the list
  rpc GetAccountDetailsBulk (GetAccountDetailsBulkRequest) returns (GetAccountDetailsBulkResponse) {

  }

  // Batch RPC to publish transaction events for given txnIds
  // publishes to pay downstream for processing the aa transaction
  // RPC is idempotent and in case of any error, rpc will throw a overall error and needs to replay the whole batch
  // OK : success
  // INTERNAL : internal error
  // INVALID_ARGUMENT : if invalid transaction id is passed
  rpc PublishTxnEventsByTxnIds (PublishTxnEventsByTxnIdsRequest) returns (PublishTxnEventsByTxnIdsResponse) {
  }

  // RPC to fetch auth token
  // Takes aa entity as input
  // OK : success
  // INTERNAL : internal error
  // INVALID ARGUMENT : invalid aa entity
  rpc GetAuthToken (GetAuthTokenRequest) returns (GetAuthTokenResponse) {
  }

  // RPC to get transaction details by id of the transaction
  // OK : Success
  // INTERNAL : internal error
  // NOT FOUND : transaction not found in db
  rpc GetTxnDetails (GetTxnDetailsRequest) returns (GetTxnDetailsResponse) {
  }

  // This is a pagination RPC to get transaction details list by actorId, account_ref_id and given time range
  // OK : Success
  // INTERNAL : internal error
  rpc GetTxnListByActorIdAndAccRefId (GetTxnListByActorIdAndAccRefIdRequest) returns (GetTxnListByActorIdAndAccRefIdResponse) {
  }

  // RPC to get the total transaction count for given actorId, account_reference_id and time this can be used to check the number of
  // transactions for a connected account of a user b/w a given start time of the transaction_date to the current time
  // start_time is optional if client doesn't provide start_time then RPC returns all transaction count for that connected account
  rpc GetTotalTransactionsCount (GetTotalTransactionsCountRequest) returns (GetTotalTransactionsCountResponse) {
  }

  // RPC to get that connected account details which have maximum numbers of transaction and vintage time is more than given time if multiple accounts have same
  // numbers of txn then it will return any one of them randomly.
  // it considers those transactions which are executed b/w given start_time and end_time.
  // This is for US-Stock use case, it can't be implemented on us stock side because we can't share connected account transaction related data from epifi_wealth to us_stocks db
  rpc GetValidAccountDetailsForUSStocks (GetValidAccountDetailsForUSStocksRequest) returns (GetValidAccountDetailsForUSStocksResponse);

  // will be used to get user's connected account statement pdf for given actorId, bank reference id, start and end time.
  // it includes those transactions in statement pdf that are generated b/w given start time and end time.
  // This is for US-Stock use case, it can't be implemented on us stock side because we can't share connected account transaction related data from epifi_wealth to us_stocks db
  // There can be too many transactions in the requested time range to add to the account statement.
  // In these cases a reduced time range is used for generating account statement, which is returned in the response.
  // Currently, the latest X transactions are used for such cases, where X is configurable.
  rpc DownloadConnectedAccountBankStatement (DownloadConnectedAccountBankStatementRequest) returns (DownloadConnectedAccountBankStatementResponse);

  // This RPC makes a screener pass/fail decision for an actor using a set of rules based on accounts linked.
  // rpc status OK: Successfully made screener decision
  // rpc status IN_PROGRESS: data is being synced
  // rpc status NOT_FOUND: no consent has been collected to link a connected account
  rpc GetScreenerDecision (GetScreenerDecisionRequest) returns (GetScreenerDecisionResponse) {
  }

  // GetSdkDeeplinkForCaFlowRequest is used to fetch deeplink required for initialising the AA SDK.
  // @params : GetSdkDeeplinkForCaFlowRequest consists of
  //  - Actor ID
  //  - App Version : app version for user
  //  - App Platform : app platform - Android, iOS etc.
  //  - CA Flow Name : SDK deeplink will be fetched based on the ca_flow_name (identifier) of the service
  // @returns : GetSdkDeeplinkForCaFlowResponse  consists of
  //  - RPC Status : INTERNAL in case when AA Entity/V2 Flow or is token auth enabled can't be determined
  //                 OK in case of no internal error
  //  - Deeplink : nil in case of INTERNAL error else returns deeplink required for initialising the AA SDK.
  // NOTE: This RPC use appPlatform and appVersion from request, if any of these turn out to be nil, use the respective value from
  // context, if the value turn out be nil in context as well, then return error
  rpc GetSdkDeeplinkForCaFlow (GetSdkDeeplinkForCaFlowRequest) returns (GetSdkDeeplinkForCaFlowResponse);

  // GetAccountsForRenewal RPC returns list of all accounts which are eligible for renewal. An account is
  // eligible for renewal if the max expiry date among all the consents associated with it, is within 3 months
  // from the current date (the threshold is configurable with config param `ConsentRenewalThreshold`)
  // It returns a list of accounts along with a list of timestamp specifying the time for consent expiry
  // Returns an empty array list of account details if no account is expiring
  rpc GetAccountsForRenewal (GetAccountsForRenewalRequest) returns (GetAccountsForRenewalResponse) {
  }

  // RPC to update expiry date of a consent given its consent handle Id
  rpc UpdateConsentExpiry (UpdateConsentExpiryRequest) returns (UpdateConsentExpiryResponse) {}

  // RPC to check if an actor is eligible for consent renewal
  // uses release evaluator and evaluates user on Feature_AA_CONSENT_RENEWAL release constraint
  // and checks if the user is member of consent renewal segment in segment service
  // OK : success
  // INTERNAL : internal error
  rpc CheckEligibilityForConsentRenewal (CheckEligibilityForConsentRenewalRequest) returns (CheckEligibilityForConsentRenewalResponse) {}

  rpc ParseAATransaction (ParseAATransactionRequest) returns (ParseAATransactionResponse) {}

  // RPC to Resolve Internal Actor Pi Details for an AA Transaction
  rpc ResolveInternalActorPiDetailsForAATxn (ResolveInternalActorPiDetailsForAATxnRequest) returns (ResolveInternalActorPiDetailsForAATxnResponse) {}

  rpc EnrichAATransaction (EnrichAATransactionRequest) returns (EnrichAATransactionResponse) {}

  // RPC to publish enriched transactions to a topic where each message will be a list of Enriched Transactions
  rpc PublishListEnrichedAATransaction (PublishListEnrichedAATransactionRequest) returns (PublishListEnrichedAATransactionResponse) {}

  // CheckIfAccountFiFedSavingAcc RPC returns if the given connected account is Fi Fed connected account or not
  // if accountId is not given then if Fi Fed connected account or not is searched all the connected accounts for the actor.
  // OK : success
  // INTERNAL : any internal service error
  // INVALID ARGUMENT : invalid arguments in request e.g. if account_id in the request is invalid/not found
  rpc CheckIfAccountFiFedSavingAcc (CheckIfAccountFiFedSavingAccRequest) returns (CheckIfAccountFiFedSavingAccResponse) {}

  // BackupEnrichedTransactions RPC to write Enriched Transactions to s3 for backup or later consumption
  rpc BackupEnrichedTransactions (BackupEnrichedTransactionsRequest) returns (BackupEnrichedTransactionsResponse) {}

  // GetEnrichedTransactionsProgress RPC gives the ratio of enriched transaction to total transactions, total raw transactions and total enriched transactions for a given accountId
  rpc GetEnrichedTransactionsProgress (GetEnrichedTransactionsProgressRequest) returns (GetEnrichedTransactionsProgressResponse) {}

  // InitiateDataRefreshForAccounts RPC initiates data refresh for accountIds provided in the request and returns a map containing refresh request status (Initiated, Ignored, Internal, consent not active) for each account
  rpc InitiateDataRefreshForAccounts (InitiateDataRefreshForAccountsRequest) returns (InitiateDataRefreshForAccountsResponse) {}

  // GetRawTransactionsInDateRange RPC returns raw transactions for a given accountId
  // deprecated: use GetRawTxnsForAccountV2 instead
  rpc GetRawTxnsForAccount (GetRawTxnsForAccountRequest) returns (GetRawTxnsForAccountResponse) {}

  // GetRawTransactionsInDateRangeV2 RPC returns raw transactions for a given accountId
  rpc GetRawTxnsForAccountV2 (GetRawTxnsForAccountV2Request) returns (GetRawTxnsForAccountV2Response) {}

  // GetAccountIdsByConsentHandleList returns account ids with their corresponding consent handle
  // If we don't find any account with the given consent handle map won't have the account id for that
  // In case of any error while finding single account by consent handle then INTERNAL will be returned
  rpc GetAccountIdsByConsentHandleList (GetAccountIdsByConsentHandleListRequest) returns (GetAccountIdsByConsentHandleListResponse) {}

  // StartConnectionFlow stores the parameters related to a new flow for connecting accounts.
  // These parameters can later be used for scenarios like getting the exit deeplink, etc.
  rpc StartConnectionFlow (StartConnectionFlowRequest) returns (StartConnectionFlowResponse);

  // GetConnectionFlow returns the details of an existing flow for connecting accounts.
  rpc GetConnectionFlow (GetConnectionFlowRequest) returns (GetConnectionFlowResponse);

  // RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  // ActorId is a mandatory parameter in the Request
  // Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no elements found for the given user on this screen
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the list of targeted comms elements is fetched successfully
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse) {}

  // RPC used by the Dynamic Elements service to callback on user action on a dynamic element
  // ActorId and ElementId are mandatory parameters in the Request
  // Response contains status code
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no element exists with the given ElementId
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the callback is registered successfully
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse) {}
}

message GetAccountIdsByConsentHandleListRequest {
  repeated string consent_handle_list = 1 [(validate.rules).repeated.min_items = 1];
}

message GetAccountIdsByConsentHandleListResponse {
  rpc.Status status = 1;
  message AccountIdList {
    repeated string account_id_list = 1;
  }
  map<string, AccountIdList> consent_handle_to_account_ids_map = 2;
}

message GetRawTxnsForAccountRequest {
  // page context for pagination
  rpc.PageContextRequest page_context = 1;
  // mandatory
  string account_id = 2 [(validate.rules).string.min_len = 1];
  // optional param to fetch transactions satisfying the given filters
  RawTransactionFilters filters = 3;
}

message GetRawTxnsForAccountResponse {
  // below are the status codes that can be returned by this RPC
  // 200 - OK
  // 5 - NOTFOUND - ( when no transactions for an account are found )
  // 13 - INTERNAL
  rpc.Status status = 1;
  // page context response
  rpc.PageContextResponse page_context = 2;
  // list of raw transactions satisfying the criteria
  repeated connected_account.AaTransaction raw_txn_list = 3;
}

message GetRawTxnsForAccountV2Request {
  // page context for pagination
  rpc.PageContextRequest page_context = 1;
  // mandatory
  string account_id = 2 [(validate.rules).string.min_len = 1];

  connected_account.enums.AccInstrumentType fi_type = 3;
  // optional param to fetch transactions satisfying the given filters
  RawTransactionFilters filters = 4;
}

message GetRawTxnsForAccountV2Response {
  // below are the status codes that can be returned by this RPC
  // 200 - OK
  // 5 - NOTFOUND - ( when no transactions for an account are found )
  // 13 - INTERNAL
  rpc.Status status = 1;
  // page context response
  rpc.PageContextResponse page_context = 2;
  // list of raw transactions satisfying the criteria
  repeated connected_account.RawAaTransaction raw_txn_list = 3;
}

message RawTransactionFilters {
  // optional param to only fetch transactions done after given timestamp : inclusive
  google.protobuf.Timestamp transaction_date_after = 1;
  // optional param to only fetch transactions done before given timestamp : inclusive
  google.protobuf.Timestamp transaction_date_before = 2;
}

message StartConnectionFlowRequest {
  string actor_id = 1;

  enums.CAFlowName ca_flow_name = 2;

  CAFlowParams ca_flow_params = 3;

  // Clients can provide this to re-use a flow later if needed
  string client_req_id = 4;
}

message StartConnectionFlowResponse {
  rpc.Status status = 1;

  ConnectionFlow connection_flow = 2;
}

message GetConnectionFlowRequest {
  string flow_id = 1;
}

message GetConnectionFlowResponse {
  rpc.Status status = 1;

  ConnectionFlow connection_flow = 2;
}
