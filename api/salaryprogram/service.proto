//go:generate gen_sql -types=SalaryTxnValidation
syntax = "proto3";

package salaryprogram;

import "api/dynamic_elements/dynamic_elements.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/recurringpayment/enach/enums/enums.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/salaryprogram/aa/aa_salary_txn_verification_request.proto";
import "api/salaryprogram/activation_history.proto";
import "api/salaryprogram/enums/salary_band.proto";
import "api/salaryprogram/registration.proto";
import "api/salaryprogram/salary_estimation.proto";
import "api/salaryprogram/salary_lite_mandate_execution_request.proto";
import "api/salaryprogram/salary_lite_mandate_request.proto";
import "api/salaryprogram/salary_txn_verification_request.proto";
import "api/salaryprogram/whitelisted_b2b_user.proto";
import "api/typesv2/bank.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/salaryprogram";
option java_package = "com.github.epifi.gamma.api.salaryprogram";

service SalaryProgram {
  // useful for creating a new registration.
  rpc CreateRegistration (CreateRegistrationRequest) returns (CreateRegistrationResponse);
  // useful for fetching the current registration status and the next stage of registration that needs to completed.
  rpc GetCurrentRegStatusAndNextRegStage (CurrentRegStatusAndNextRegStageRequest) returns (CurrentRegStatusAndNextRegStageResponse);
  // useful to fetch the details of a registration stage for a given registration.
  rpc GetRegistrationStageDetails (GetRegistrationStageDetailsRequest) returns (GetRegistrationStageDetailsResponse);
  // useful to update the employment confirmation stage of salary program registration.
  rpc UpdateEmploymentConfirmationStage (UpdateEmploymentConfirmationStageRequest) returns (UpdateEmploymentConfirmationStageResponse);
  // useful to raise a manual (user-initiated) salary verification request for verifying a given txn as salary.
  // this rpc will validate if the given txn is a salary txn or not, if the verification (using realtime checks) fails, it will escalate the
  // request to the ops team.
  rpc RaiseManualSalaryVerification (RaiseManualSalaryVerificationRequest) returns (RaiseManualSalaryVerificationResponse);
  // useful to update the status of a manual salary txn verification request.
  // used by salary program ops team to manually verify salary txn verification requests and update this status.
  rpc UpdateManualSalaryTxnVerificationStatus (UpdateManualSalaryTxnVerificationStatusRequest) returns (UpdateManualSalaryTxnVerificationStatusResponse);
  // useful for marking that a given verification request's status was acknowledged by the user,
  // mostly useful for manually raised salary verification requests where ack is needed for dismissing the request from the app.
  // status acknowledgement is allowed only if the verification request has reached a terminal state (VERIFIED or FAILED).
  rpc AckSalaryVerificationRequestStatus (AckSalaryVerificationRequestStatusRequest) returns (AckSalaryVerificationRequestStatusResponse);
  // useful to fetch salary txn verification requests with filters.
  // returns the requests in desc order of their creation time.
  rpc GetSalaryTxnVerificationRequests (GetSalaryTxnVerificationRequestsRequest) returns (GetSalaryTxnVerificationRequestsResponse);
  // useful to fetch the activation details due to which salary program was active at a given time.
  // If activation_kind is passed as SALARY_PROGRAM_ACTIVATION_KIND_BEST it will fetch the activation with the highest band. If multiple activations are present with the same highest band, the most recent one will be taken.
  // If activation_kind is not passed or unspecified, by default it will fetch the latest activation
  // e.g. can pass current time as active_at time to fetch the activation details due to which salary program is currently active.
  // return RecordNotFound status if no activation details are found.
  // NOTE: There are two types of salary activations (FULL_SALARY_ACTIVATION and SALARY_LITE_ACTIVATION) and some fields in the response are specific to either of the activation type
  rpc GetLatestActivationDetailsActiveAtTime (LatestActivationDetailsActiveAtTimeRequest) returns (LatestActivationDetailsActiveAtTimeResponse);
  // useful to fetch the activation history for a given salaryprogram registration.
  rpc GetSalaryProgramActivationHistories (GetSalaryProgramActivationHistoriesRequest) returns (GetSalaryProgramActivationHistoriesResponse);
  // useful to fetch the min required amount for detecting a txn as salary for an actor.
  // This is useful for salary programme B2B partnerships where min salary amount would be governed by the b2b contract.
  rpc GetMinRequiredAmountForSalaryTxnDetection (MinRequiredAmountForSalaryTxnDetectionRequest) returns (MinRequiredAmountForSalaryTxnDetectionResponse);
  // useful to get the count of salary program active users who received their last salary credit from the given employer.
  // Note : data returned by this rpc can be a bit stale as this data is periodically calculated and persisted by a cron job and is not fetched in realtime.
  rpc GetSalaryProgramActiveUsersCountForEmployer (GetSalaryProgramActiveUsersCountForEmployerRequest) returns (GetSalaryProgramActiveUsersCountForEmployerResponse);
  // useful to get the mapping of all the possible salary transaction verification failed reasons categories and sub-categories.
  rpc GetSupportedSalaryVerificationFailureReasons (GetSupportedSalaryVerificationFailureReasonsRequest) returns (GetSupportedSalaryVerificationFailureReasonsResponse);
  // CheckEmployerEligibilityForSalaryProgram is useful to check the employer eligibility for salary program
  rpc CheckEmployerEligibilityForSalaryProgram (CheckEmployerEligibilityForSalaryProgramRequest) returns (CheckEmployerEligibilityForSalaryProgramResponse);
  // rpc GetRegistrationDetails is used to get registration status, completion time, etc
  rpc GetRegistrationDetails (GetRegistrationDetailsRequest) returns (GetRegistrationDetailsResponse);
  // GetSalaryProgramEmployerChannel is useful to get the channel through which the employees from the employer onboard for salary program
  // deprecated, use Employment Service to get salary program channel
  rpc GetSalaryProgramEmployerChannel (GetSalaryProgramEmployerChannelRequest) returns (GetSalaryProgramEmployerChannelResponse);
  // InitiateSalaryLiteMandateRequest Initiates the mandate for salary lite program
  // it initiates a mandate creation with pay and creates a record in SalaryLiteMandateRequest table for the same
  rpc InitiateSalaryLiteMandate (InitiateSalaryLiteMandateRequest) returns (InitiateSalaryLiteMandateResponse);
  // GetSalaryLiteMandateExecutionRequests gets all SalaryLiteMandateExecutionRequest matching the given filters
  rpc GetSalaryLiteMandateExecutionRequests (GetSalaryLiteMandateExecutionRequestsRequest) returns (GetSalaryLiteMandateExecutionRequestsResponse);
  // GetSalaryLiteMandateRequests fetch SalaryLiteMandateRequests according to the filters passed
  rpc GetSalaryLiteMandateRequests (GetSalaryLiteMandateRequestsRequest) returns (GetSalaryLiteMandateRequestsResponse);
  // UpdateSalaryLiteMandateRequest updates the salary lite mandate request entry
  rpc UpdateSalaryLiteMandateRequest (UpdateSalaryLiteMandateRequestRequest) returns (UpdateSalaryLiteMandateRequestResponse);
  // InitiateSalaryLiteMandateExecution create the mandate execution req entry in salaryprogram db and initiate execution request with recurring payment service
  rpc InitiateSalaryLiteMandateExecution (InitiateSalaryLiteMandateExecutionRequest) returns (InitiateSalaryLiteMandateExecutionResponse);
  // FetchDynamicElements will be invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse) {};
  // DynamicElementCallback will be processes callback received on user action on any of the dynamic elements
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse) {};
  // IsEligibleForSalaryPromoWidget checks eligibility of displaying salary promo widget for an actor
  // This can be used to power other widget, Tiering promo widget for example shouldn't be shown when salary widget is being shown
  rpc IsEligibleForSalaryPromoWidget (IsEligibleForSalaryPromoWidgetRequest) returns (IsEligibleForSalaryPromoWidgetResponse) {};
  // GetSalaryTxnVerificationRequestsCount will get the number of salary transaction verification requests as per the filters passed
  rpc GetSalaryTxnVerificationRequestsCount (GetSalaryTxnVerificationRequestsCountRequest) returns (GetSalaryTxnVerificationRequestsCountResponse);
  // CreateWhitelistedB2BUsersInBulk creates WhitelistedB2bUser in bulk
  rpc CreateWhitelistedB2bUsersInBulk (CreateWhitelistedB2bUsersInBulkRequest) returns (CreateWhitelistedB2bUsersInBulkResponse);

  // This rpc checks whether the passed txn is salary txn based on the passed validation.
  // if no validations are passed then all default validations will run
  rpc IsValidSalaryTxn (IsValidSalaryTxnRequest) returns (IsValidSalaryTxnResponse);

  // GetAaSalaryDetails returns aa salary related details(salary band etc..) for a given actor
  // TBD
  rpc GetAaSalaryDetails (GetAaSalaryDetailsRequest) returns (GetAaSalaryDetailsResponse) {};
  // EstimateAndStoreSalary rpc
  // 1. checks required preconditions (if the AA data pull is successful for the requested salary registration id)
  // 2. Estimates the salary from the data
  // 3. Stores the estimated salary in db
  // 4. Updates the salary registration status as salary estimation completed
  rpc EstimateAndStoreSalary (EstimateAndStoreSalaryRequest) returns (EstimateAndStoreSalaryResponse);
  // EstimateAndStoreSalaryFromAccStmt rpc
  // 1. Transfers data to tech s3 bucket
  // 2. Parses transactions from data
  // 2. Estimates the salary from the transactions
  // 3. Stores the estimated salary in db
  // 4. Updates the salary registration status as salary estimation completed
  rpc EstimateAndStoreSalaryFromAccStmt (EstimateAndStoreSalaryFromAccStmtRequest) returns (EstimateAndStoreSalaryFromAccStmtResponse);
  // EstimateAndStoreSalaryFromSmsParser estimates salary from SMS parser and stores in db
  // and updates the salary registration status as salary estimation completed
  rpc EstimateAndStoreSalaryFromSmsParser (EstimateAndStoreSalaryFromSmsParserRequest) returns (EstimateAndStoreSalaryFromSmsParserResponse);
  // rpc for getting the necessary parameters for Transfer setup screen based on estimated user salary
  rpc GetAaSalaryTransferSetupParams (GetAaSalaryTransferSetupParamsRequest) returns (GetAaSalaryTransferSetupParamsResponse);
  // RecordAaSalaryCommittedByUser rpc records the salary amount committed by the user for AA salary plan
  rpc RecordAaSalaryCommittedByUser (RecordAaSalaryCommittedByUserRequest) returns (RecordAaSalaryCommittedByUserResponse);
  // CreateRegistrationStageDetails will create a new registration stage details record for the given registration
  rpc CreateRegistrationStageDetails (CreateRegistrationStageDetailsRequest) returns (CreateRegistrationStageDetailsResponse);
  // GetAASalaryEstimate rpc gets salary estimate for a given accountId based on its aa transactions
  rpc GetAASalaryEstimate (GetAASalaryEstimateRequest) returns (GetAASalaryEstimateResponse);
  // GetAaSalaryTxnVerificationRequest rpc returns the txn verification request for aa salary plan
  rpc GetAaSalaryTxnVerificationRequest (GetAaSalaryTxnVerificationRequestRequest) returns (GetAaSalaryTxnVerificationRequestResponse);
  // soft deletes a given salary registration
  rpc SoftDeleteRegistration (SoftDeleteRegistrationRequest) returns (SoftDeleteRegistrationResponse);
  // GetSalaryEstimation provides SalaryEstimationsDao methods to fetch salary estimations data
  // data will be fetched using filters that are supported by each method
  // for example for GetByActorIdAndSourceTypeAndStatus method, actor_id, source_type and status are mandatory
  // and for GetById method, id is mandatory
  rpc GetSalaryEstimation (GetSalaryEstimationRequest) returns (GetSalaryEstimationResponse);
  // rpc to expose the config params of salaryprogram service
  rpc GetConfigParams (GetConfigParamsRequest) returns (GetConfigParamsResponse);
}

// GetSalaryEstimationRequest will need either id or actor_id, source_type and status
message GetSalaryEstimationRequest {
  oneof filter {
    Id id = 1;
    ActorIdSrcTypeStatus actor_id_src_type_status = 2;
  }
  message Id {
    string id = 1;
  }
  message ActorIdSrcTypeStatus {
    string actor_id = 1 [(validate.rules).string.min_len = 1];
    SalaryAccountSourceType source_type = 2 [(validate.rules).enum = {not_in: [0]}];
    SalaryEstimationStalenessStatusType status = 3 [(validate.rules).enum = {not_in: [0]}];
  }
}

message GetSalaryEstimationResponse {
  rpc.Status status = 1;
  // salary estimation data
  SalaryEstimation salary_estimation = 2;
}

message GetAaSalaryDetailsRequest {
  // Actor id identifier(MANDATORY)
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message GetAaSalaryDetailsResponse {
  // Rpc response status
  rpc.Status status = 1;
  // salary band of the user's current activation request will be returned if present
  enums.SalaryBand salary_band = 2;
  // Current stage of the user's aa salary flow
  enums.AaSalaryStage current_stage = 3;
  // Timestamp for last credit transaction which led to activation
  google.protobuf.Timestamp last_transfer_date = 4;
  // upcoming transfer criteria for reward activation
  TransferCriteria upcoming_transfer_criteria = 5;
  // latest activation details
  SalaryProgramActivationHistory latest_activation = 6;
  // latest salary txn verification request
  aa.AASalaryTxnVerificationRequest latest_salary_txn_verification_request = 7;
}

message IsEligibleForSalaryPromoWidgetRequest {
  // Actor id identifier(MANDATORY)
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message IsEligibleForSalaryPromoWidgetResponse {
  // Rpc response status
  rpc.Status status = 1;
  // Boolean value to represent eligibility of displaying salary promo widget
  bool is_eligible = 2;
  // Ineligibility reason if the user is not eligible for salary promo widget
  string ineligibility_reason = 3;
}

message IsValidSalaryTxnRequest {
  // Id of the Order which needs to be validated
  string orderId = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // this is an array of validations which needs to be run on the passed txn/order
  // this will be populated by the caller and BE will run corresponding validations
  // if this is passed empty then all the default validations will run
  repeated SalaryTxnValidation salary_txn_validations = 2;
}

message IsValidSalaryTxnResponse {
  // rpc response status
  rpc.Status status = 1;

  bool is_valid_salary_txn = 2;
  // this denotes the failure for corresponding SalaryTxnValidationFailureType incase validation failed for the passed the txn
  SalaryTxnValidation failed_salary_txn_validation = 3;
}

enum SalaryTxnValidation {
  SALARY_TXN_VALIDATION_UNSPECIFIED = 0;
  SALARY_TXN_VALIDATION_WORKFLOW = 1;
  SALARY_TXN_VALIDATION_PROVENANCE = 2;
  SALARY_TXN_VALIDATION_NOT_DEPOSIT_TXN = 3;
  SALARY_TXN_VALIDATION_MIN_AMOUNT = 4;
  SALARY_TXN_VALIDATION_TERMINAL_STATE = 5;
  SALARY_TXN_VALIDATION_PAYMENT_PROTOCOL = 6;
  SALARY_TXN_VALIDATION_TXN_CATEGORY_NON_INCOME = 7;
  SALARY_TXN_VALIDATION_FI_USER = 8;
  SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_USER_DECLARED_EMPLOYER = 9;
  SALARY_TXN_VALIDATION_MATCH_REMITTER_TO_ANY_EMPLOYER_IN_DB = 10;
}

message GetSalaryTxnVerificationRequestsCountRequest {
  // list of request sources
  repeated SalaryTxnVerificationRequestSource request_source_in = 1;
  // salary txn verification status
  SalaryTxnVerificationRequestStatus verification_status = 2;
  // salary txn verification sub status
  SalaryTxnVerificationRequestSubStatus verification_sub_status = 3;
}

message GetSalaryTxnVerificationRequestsCountResponse {
  // rpc response status
  rpc.Status status = 1;
  // number of salary verification transaction requests
  int64 number_of_salary_txn_verification_requests = 2;
}

message CreateRegistrationRequest {
  // actor who needs to be registered.
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // todo (utkarsh) : add account_id field also once we start supporting multiple savings account in Fi
  // account_id denotes the accounts (like Fi fed savings account/ AA connected savings account) for which the registration has to be created.
  string account_id = 2;
  // account_type denotes the types of Account Id which can (FI_AA_SAVINGS_ACC, FI_FED_SAVINGS_ACC, etc)
  SalaryProgramRegistrationAccountType account_type = 3 [(validate.rules).enum = {not_in: [0]}];
  // registration_flow_type indicates the flow type for which the registration was done.
  SalaryProgramRegistrationFlowType registration_flow_type = 4 [(validate.rules).enum = {not_in: [0]}];
}

message CreateRegistrationResponse {
  // rpc response status
  rpc.Status status = 1;
  // salary program registration entry just created
  SalaryProgramRegistration registration = 2;
}

message CurrentRegStatusAndNextRegStageRequest {
  // actor for whom registration details need to be fetched.
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // flow_type indicates the flow for which the current and next registration stage is asked.
  SalaryProgramRegistrationFlowType flow_type = 2 [(validate.rules).enum = {not_in: [0]}];

  // todo (utkarsh) : add account_id field also once we start supporting multiple savings account in Fi
}

message CurrentRegStatusAndNextRegStageResponse {
  // rpc response status
  rpc.Status status = 1;
  // salary program registration id
  string registration_id = 2;
  // current status of registration
  SalaryProgramRegistrationStatus registration_status = 3;
  // if the registration status is INITIATED, then  next_stage denotes the next stage to be COMPLETED in the registration flow.
  SalaryProgramRegistrationStage next_stage = 4;
}

message GetRegistrationStageDetailsRequest {
  // registration for which stage details need to be fetched.
  string registration_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // stage for which details need to be fetched.
  SalaryProgramRegistrationStage stage_name = 2 [(validate.rules).enum = {not_in: [0]}];
}

message GetRegistrationStageDetailsResponse {
  // rpc response status
  rpc.Status status = 1;
  // current status of registration stage
  SalaryProgramRegistrationStageStatus stage_status = 2;
}

message UpdateEmploymentConfirmationStageRequest {
  // salary program registration id
  string registration_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // denotes the updated status of employment confirmation stage.
  SalaryProgramRegistrationStageStatus update_stage_status_to = 2 [(validate.rules).enum = {not_in: [0]}];
}

message UpdateEmploymentConfirmationStageResponse {
  // rpc response status
  rpc.Status status = 1;
  // updated stage details of employment confirmation stage
  SalaryProgramRegistrationStageDetails emp_confirmation_stage_details = 2;
}

message RaiseManualSalaryVerificationRequest {
  // actor for whose txn the verification request is being raised.
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // txn for which salary verification request is to be raised
  // the above actor should be the beneficiary of the given order.
  string order_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // source from which verification request is raised.
  SalaryTxnVerificationRequestSource req_source = 3;
}

message RaiseManualSalaryVerificationResponse {
  // rpc response status
  rpc.Status status = 1;
  // salary verification request with latest status
  SalaryTxnVerificationRequest salary_verification_req = 2;
}

message UpdateManualSalaryTxnVerificationStatusRequest {
  // id of salary txn verification req whose status is to be updated.
  string salary_txn_ver_request_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // denotes the status, sub updated required for the given verification request (represented by salary_txn_ver_request_id).
  SalaryTxnVerificationRequestStatus update_status_to = 2;
  SalaryTxnVerificationRequestSubStatus update_sub_status_to = 3;
  // denotes the internal id of employer who initiated the salary txn for which verification request was raised.
  // for status update to VERIFIED, salary_txn_employer_id also needs to be present.
  string salary_txn_employer_id = 4;
  // denotes who performed the verification.
  // should be not null in case status needs to be updated to VERIFIED/FAILED.
  SalaryTxnVerificationRequestVerifiedBy verified_by = 5;
  // denotes the reason category due to which the verification is marked as failed for this txn,
  // should be UNSPECIFIED if the verification status is not FAILED.
  SalaryTxnVerificationFailureReasonCategory verification_failure_reason_category = 6;
  // denotes the reason sub-category due to which the verification is marked as failed for this txn,
  // should be UNSPECIFIED if the verification status is not FAILED.
  SalaryTxnVerificationFailureReasonSubCategory verification_failure_reason_sub_category = 7;
  // remark for salary txn verification.
  string verification_remark = 8;
}

message UpdateManualSalaryTxnVerificationStatusResponse {
  // rpc response status
  rpc.Status status = 1;
  // updated salary txn verification request
  SalaryTxnVerificationRequest updated_verification_req = 2;
}

message AckSalaryVerificationRequestStatusRequest {
  // id of salary txn verification req which needs to be acknowledged.
  string salary_txn_ver_request_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message AckSalaryVerificationRequestStatusResponse {
  // rpc response status
  rpc.Status status = 1;
}

message GetSalaryTxnVerificationRequestsRequest {
  message Filters {
    string actor_id = 1;
    google.protobuf.Timestamp from_date = 2;
    google.protobuf.Timestamp upto_date = 3;
    // Deprecated in favour of req_sources
    SalaryTxnVerificationRequestSource req_source = 4 [deprecated = true];
    SalaryTxnVerificationRequestStatus status = 5;
    repeated SalaryTxnVerificationRequestSubStatus sub_statuses = 6;
    AcknowledgmentStatus user_ack_status = 7;
    string id = 8;
    repeated SalaryTxnVerificationRequestSource req_sources = 9;
  }
  // one of actor_id or req_source is mandatory in filters
  Filters filters = 2;

  // page context to help server fetch the page
  rpc.PageContextRequest page_context = 3;

  // default sort order is DESC
  SortOrder sort_order = 4;
}

message GetSalaryTxnVerificationRequestsResponse {
  // rpc response status
  rpc.Status status = 1;
  // salary txn verification requests list
  repeated SalaryTxnVerificationRequest salary_txn_verification_requests = 2;
  // page context to help client fetch the next page
  rpc.PageContextResponse page_context = 3;
}
// List of activation kinds
enum SalaryProgramActivationKind {
  SALARY_PROGRAM_ACTIVATION_KIND_UNSPECIFIED = 0;
  // fetches the activation with the highest band at that point of time from the activation history. If multiple activations are present with the same highest band, the most recent one will be taken.
  SALARY_PROGRAM_ACTIVATION_KIND_BEST = 1;
}

message LatestActivationDetailsActiveAtTimeRequest {
  // salary program registration id for which activation details need to be fetched
  string registration_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // to fetch the activation details which lead to salary program being active at given time.
  // e.g. can pass current time as active_at time to fetch the activation details due to which salary program is currently active.
  google.protobuf.Timestamp active_at_time = 2 [(validate.rules).timestamp.required = true];
  // kind of activation needed by the caller (latest or the one with highest band (best) (if multiple activations are present with the same highest band, the most recent one will be taken.)). If not passed, it is considered as latest activation
  SalaryProgramActivationKind activation_kind = 3;
}

message LatestActivationDetailsActiveAtTimeResponse {
  // rpc response status
  rpc.Status status = 1;

  // id of activation (from activation history) whose details are returned.
  // useful if we need to fetch more details regarding the activation.
  string activation_history_id = 2;

  // salary program active since/till date.
  google.protobuf.Timestamp active_from = 3;
  google.protobuf.Timestamp active_till = 4;

  // todo (utkarsh) : replace txn related fields with salary_txn_ver_request_id if we start supporting salary identification spanning multiple txns.
  // salary txn id which lead to the current activation.
  // NOTE: only present for Full Salary ActivationType
  string salary_txn_id = 5;
  // txn timestamp of salary txn which lead to the current activation.
  // NOTE: only present for Full Salary ActivationType
  google.protobuf.Timestamp salary_txn_timestamp = 6;
  // id of employer who credited the salary.
  // NOTE: only present for Full Salary ActivationType
  string salary_txn_employer_id = 7;
  // timestamp at which activation was done.
  google.protobuf.Timestamp activated_at = 8;
  // denotes the salary activation type, ex- salary lite activation, full salary activation
  SalaryActivationType activation_type = 9;
  // reference id of action against which the activation was done
  string activation_action_ref_id = 10;
  // salary band based on the salary txn value
  enums.SalaryBand salary_band = 11;
  // B2B salary band based on the salary txn value
  enums.B2BSalaryBand b2b_salary_band = 12;
}

message GetSalaryProgramActivationHistoriesRequest {
  // salary program registration id for which activation histories needs to be fetched
  string registration_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // type of activation : FULL_SALARY_ACTIVATION / SALARY_LITE_ACTIVATION
  SalaryActivationType activation_type = 2;
  // intentional gap in field number to accommodate new fields
  // default sort order is DESC on created_at
  SortOrder sort_order = 7;
  // page context to help server fetch the page
  rpc.PageContextRequest page_context = 8;
}

message GetSalaryProgramActivationHistoriesResponse {
  // rpc response status
  rpc.Status status = 1;
  // activation histories
  repeated SalaryProgramActivationHistory activation_histories = 2;
  // page context to help client fetch the next page
  rpc.PageContextResponse page_context = 3;
}

message MinRequiredAmountForSalaryTxnDetectionRequest {
  // actor for which the min amount needs to be fetched
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message MinRequiredAmountForSalaryTxnDetectionResponse {
  // rpc response status
  rpc.Status status = 1;
  // min amount required for salary txn detection.
  google.type.Money amount = 2;
}

message GetSalaryProgramActiveUsersCountForEmployerRequest {
  // id of employer for whom the salaryprogram active users count is to be fetched.
  string employer_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message GetSalaryProgramActiveUsersCountForEmployerResponse {
  // rpc response status
  rpc.Status status = 1;
  // count of salary program active users with given employer.
  int64 count = 2;
  // time at which the count was last computed.
  // this field helps in determining the staleness of the count.
  google.protobuf.Timestamp last_computed_at = 3;
}

message GetSupportedSalaryVerificationFailureReasonsRequest {
  // Does not require any parameter for getting SupportedSalaryVerificationFailureReasons
}

message GetSupportedSalaryVerificationFailureReasonsResponse {
  // rpc response status
  rpc.Status status = 1;

  // mapping of supported salary txn verification failure reasons category to sub-categories
  // There cannot be 0 sub-categories for a category.
  map<string, VerificationFailureReasonSubCategories> category_to_subcategories_failure_reasons_map = 2;
}

message CheckEmployerEligibilityForSalaryProgramRequest {
  // id of the employer whose eligibility for salary program is needed to be checked
  string employer_id = 1;
}

message CheckEmployerEligibilityForSalaryProgramResponse {
  // rpc response status
  rpc.Status status = 1;
  // eligibility status of a employer for salary program
  SalaryProgramEmployerEligibilityStatus eligibility_status = 2;
}

// SalaryProgramEmployerEligibilityStatus represents eligibility status of a employer for salary program
enum SalaryProgramEmployerEligibilityStatus {
  ELIGIBILITY_STATUS_UNSPECIFIED = 0;
  // employer is eligible for salary program
  ELIGIBLE = 1;
  // employer is either fintech or single proprietor and need to be verified further by UAN check.
  SOFT_BLACKLISTED = 2;
}

// SalaryProgramEmployerChannel represents channel through which the employees from the employer onboard for salary program, ex- B2B, B2C etc
enum SalaryProgramEmployerChannel {
  SALARY_PROGRAM_EMPLOYER_CHANNEL_UNSPECIFIED = 0;
  // b2b onboarded employer
  EMPLOYER_CHANNEL_B2B = 1;
  // b2c onboarded employer
  EMPLOYER_CHANNEL_B2C = 2;
}

enum SortOrder {
  DESC = 0;
  ASC = 1;
}

message GetRegistrationDetailsRequest {
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // flow_type indicates the flow for which the current and next registration stage is asked.
  SalaryProgramRegistrationFlowType flow_type = 2 [(validate.rules).enum = {not_in: [0]}];
}

message GetRegistrationDetailsResponse {
  rpc.Status status = 1;

  // current status of registration
  SalaryProgramRegistrationStatus registration_status = 2;

  // timestamp at which registration was completed, will be nil if registration is not completed yet
  google.protobuf.Timestamp registration_completion_time = 3;

  // salary program registration id
  string registration_id = 4;
}

message GetSalaryProgramEmployerChannelRequest {
  // id of the employer whose onboarding channel is to be fetched.
  string employer_id = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
}

message GetSalaryProgramEmployerChannelResponse {
  // rpc response status
  rpc.Status status = 1;
  // salary program employer channel
  SalaryProgramEmployerChannel employer_channel = 2;
}

message InitiateSalaryLiteMandateRequest {
  string actor_id = 1;
  // amount to be transferred via mandate
  google.type.Money amount = 2;
  // preferred execution date of mandate
  google.protobuf.Timestamp preferred_execution_date = 3;
  // remitter account number
  string account_number = 4 [(validate.rules).string = {min_len: 1, max_len: 40}];
  // remitter account ifsc code
  string ifsc_code = 5 [(validate.rules).string = {min_len: 4, max_len: 32}];
  // remitter account name
  string account_holder_name = 6 [(validate.rules).string = {min_len: 1, max_len: 128}];
  // mode used for authorising the mandate, ex- Net Banking
  recurringpayment.enach.enums.EnachRegistrationAuthMode auth_mode = 7;
  // remitter bank
  api.typesv2.Bank bank = 8;
}

message InitiateSalaryLiteMandateResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Indicates mandate setup account failure as remitter account can not be same as FI Fed savings account.
    MANDATE_SETUP_FAILED_REMITTER_ACCOUNT_IS_FI_FED_SAVING_ACC = 100;
    // Indicates mandate setup account failure as Fi Federal saving account is in either INACTIVE, DORMANT state.
    MANDATE_SETUP_FAILED_FI_FED_SAVING_ACCOUNT_NOT_ACTIVE = 101;
  }
  // response status of rpc
  rpc.Status status = 1;
  // Deeplink corresponding to the next action to be done post recurring payment creation initiation
  frontend.deeplink.Deeplink next_action_deeplink = 3;
}

message GetSalaryLiteMandateRequestsRequest {
  message Filters {
    string id = 1;
    // id of the actor whose mandate requests needs to be fetched
    string actor_id = 2;
    // for fetching entries having preferred execution day of month >= passed value
    uint32 preferred_execution_day_of_month_from = 3;
    // for fetching entries having preferred execution day of month <= passed value
    uint32 preferred_execution_day_of_month_till = 4;
    // recurring payment id of the mandate
    string recurring_payment_id = 5;
    // mandates request with the following request statuses will be fetched
    repeated SalaryLiteMandateRequestStatus request_statuses = 6;
    // for fetching entries created at >= passed CreatedFrom
    google.protobuf.Timestamp created_from = 7;
    // for fetching entries created at < passed CreatedTill
    google.protobuf.Timestamp create_till = 8;
  }

  Filters filters = 1;
  // default sort order is DESC
  SortOrder sort_order = 2;
  // page context to help server fetch the page
  rpc.PageContextRequest page_context_request = 3;
}

message GetSalaryLiteMandateRequestsResponse {
  rpc.Status status = 1;
  repeated SalaryLiteMandateRequest salary_lite_mandate_requests = 2;
  // page context to help client fetch the next page
  rpc.PageContextResponse page_context_response = 3;
}

message GetSalaryLiteMandateExecutionRequestsRequest {
  // either id or recurring_payment_id is required
  message Filters {
    string id = 1;
    string recurring_payment_id = 2;
    google.protobuf.Timestamp created_at_from_time = 3;
    google.protobuf.Timestamp created_at_till_time = 4;
    SortOrder sort_order = 5;
    // actor id of the user for whom we want to fetch the execution request, this is indexed in SalaryLiteMandateExecutionRequests table
    string actor_id = 6;
    // mandate request id for which we want to fetch execution request
    string mandate_request_id = 7;
    repeated SalaryLiteMandateExecutionRequestStatus request_status_in = 8;
  }
  Filters filters = 1;
  // page context to help server fetch the page
  rpc.PageContextRequest page_context_request = 2;
}

message GetSalaryLiteMandateExecutionRequestsResponse {
  // response status of rpc
  rpc.Status status = 1;
  repeated SalaryLiteMandateExecutionRequest salary_lite_mandate_execution_requests = 2;
  // page context to help client fetch the next page
  rpc.PageContextResponse page_context_response = 3;
}

message UpdateSalaryLiteMandateRequestRequest {
  // only fields passed in the update field mask list will be updated
  // Id of the entry to be updated is mandatory
  SalaryLiteMandateRequest salary_lite_mandate_request = 1;
  // field mask list for fields to updated
  repeated SalaryLiteMandateRequestFieldMask update_field_masks = 2;
}

message UpdateSalaryLiteMandateRequestResponse {
  // response status of rpc
  rpc.Status status = 1;
}

message InitiateSalaryLiteMandateExecutionRequest {
  // actor id of the user for whom mandate execution to be initiated
  string actor_id = 1;
  // represents different entry provenance of salary lite mandate execution request in the system
  SalaryLiteMandateExecutionProvenance provenance = 2 [(validate.rules).enum = {not_in: [0]}];
}

message InitiateSalaryLiteMandateExecutionResponse {
  // response status of rpc
  rpc.Status status = 1;
}

message CreateWhitelistedB2bUsersInBulkRequest {
  // list of WhitelistedB2bUsers which needs to be created
  repeated WhitelistedB2bUser whitelisted_b2b_users = 1;
}

message CreateWhitelistedB2bUsersInBulkResponse {
  // response status of rpc
  rpc.Status status = 1;
}

message EstimateAndStoreSalaryRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // SalaryEstimationProvenance indicates the source or provenance of the salary detection
  SalaryEstimationProvenance salary_estimation_provenance = 2 [(validate.rules).enum = {not_in: [0]}];
}

message EstimateAndStoreSalaryResponse {
  enum Status {
    OK = 0;
    // used when preconditions not satisfied like AA data fetch has not completed
    FAILED_PRECONDITION = 9;
    // for internal server errors
    INTERNAL = 13;
    // used when unable to determine salary given the account's transactions
    SALARY_NOT_FOUND = 101;
  }
  rpc.Status status = 1;
}

message GetAaSalaryTransferSetupParamsRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message GetAaSalaryTransferSetupParamsResponse {
  enum Status {
    OK = 0;
    // used when preconditions not satisfied like Income not estimated and not available in db etc...
    FAILED_PRECONDITION = 9;
    // for internal server errors
    INTERNAL = 13;
    // if user is not eligible for aa salary plan and not eligible for any cashback
    NOT_ELIGIBLE = 101;
  }
  rpc.Status status = 1;
  // this is the range we allow the user to setup transfer
  // the slider for setting the transfer amount will be made based on these ranges
  // salary band will be assigned to the user based on the bucket user falls when selecting the committed salary
  repeated AmountRangeForSalaryBand amount_range_for_salary_bands = 2;
  // default value we ask the user to transfer - the value the slider points to by default
  google.type.Money default_transfer_amount_for_user = 3;
}

message AmountRangeForSalaryBand {
  // minimum amount eligible for salary_band (inclusive)
  google.type.Money min_amount = 1;
  // maximum amount eligible for salary_band (inclusive)
  google.type.Money max_amount = 2;
  // salary band eligible for above amount range
  enums.SalaryBand salary_band = 3;
}

message RecordAaSalaryCommittedByUserRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  google.type.Money committed_salary_amount = 2;
  aa.AASalaryTxnVerificationRequestSource request_source = 3;
}

message RecordAaSalaryCommittedByUserResponse {
  enum Status {
    OK = 0;
    // used when preconditions not satisfied like Income not estimated and not available in db etc...
    FAILED_PRECONDITION = 9;
    // for internal server errors
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message CreateRegistrationStageDetailsRequest {
  // salary program registration id
  string registration_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // denotes the targeted status of stage
  SalaryProgramRegistrationStageStatus target_stage_status = 2 [(validate.rules).enum = {not_in: [0]}];
  // registration stage to be created
  SalaryProgramRegistrationStage registration_stage = 3 [(validate.rules).enum = {not_in: [0]}];
}

message CreateRegistrationStageDetailsResponse {
  // rpc response status
  rpc.Status status = 1;
}

message GetAASalaryEstimateRequest {
  // AA accountId whose transactions we want to use to estimate salary
  string account_id = 1;
}

message GetAASalaryEstimateResponse {
  enum Status {
    // if salary is estimated successfully
    OK = 0;
    // used for unexpected internal errors
    INTERNAL = 13;
    // used when unable to determine salary given the account's transactions
    SALARY_NOT_FOUND = 101;
    // used when invalid argument is passed in request (Example : empty accountId)
    INVALID_ARGUMENT = 3;
  }
  // response status of rpc
  rpc.Status status = 1;
  // estimated salary based on aa trasnactions
  uint32 estimated_salary = 2;
}

message GetAaSalaryTxnVerificationRequestRequest {
  // unique identifier of aa salary txn verification request
  string id = 1;
  repeated aa.AASalaryTxnVerificationRequestFieldMask field_masks = 2;
}

message GetAaSalaryTxnVerificationRequestResponse {
  rpc.Status status = 1;
  aa.AASalaryTxnVerificationRequest salary_txn_verification_request = 2;
}

message EstimateAndStoreSalaryFromAccStmtRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // SalaryEstimationProvenance indicates the source or provenance of the salary detection
  SalaryEstimationProvenance salary_estimation_provenance = 2 [(validate.rules).enum = {not_in: [0]}];
  // pre-signed s3 url containing aa transactions
  string presigned_s3_url = 3;
  // ca account id for which salary needs to be estimated
  // this is used by auto upgrade script
  string ca_account_id = 4;
}

message EstimateAndStoreSalaryFromAccStmtResponse {
  enum Status {
    OK = 0;
    // used when preconditions not satisfied like AA data fetch has not completed
    FAILED_PRECONDITION = 9;
    // for internal server errors
    INTERNAL = 13;
    // used when unable to determine salary given the account's transactions
    SALARY_NOT_FOUND = 101;
  }
  rpc.Status status = 1;
}

message EstimateAndStoreSalaryFromSmsParserRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // SalaryEstimationProvenance indicates the source or provenance of the salary detection
  SalaryEstimationProvenance salary_estimation_provenance = 2 [(validate.rules).enum = {not_in: [0]}];
}

message EstimateAndStoreSalaryFromSmsParserResponse {
  enum Status {
    OK = 0;
    // used when preconditions not satisfied like AA data fetch has not completed
    FAILED_PRECONDITION = 9;
    // for internal server errors
    INTERNAL = 13;
    // used when unable to determine salary given the account's transactions
    SALARY_NOT_FOUND = 101;
  }
  rpc.Status status = 1;
}

message SoftDeleteRegistrationRequest {
  string registration_id = 1 [(validate.rules).string.min_len = 1];
}

message SoftDeleteRegistrationResponse {
  rpc.Status status = 1;
}

message TransferCriteria {
  // denotes start date of transfer window to get activated for rewards for reward_activation_month
  google.protobuf.Timestamp transfer_window_start_time = 1;
  // denotes end date of transfer window to get activated for rewards for reward_activation_month
  google.protobuf.Timestamp transfer_window_end_time = 2;
  // denotes month and year for which above transfer window is applicable
  int32 reward_activation_month = 3;
  int32 reward_activation_year = 4;
}

message GetConfigParamsRequest {}

message GetConfigParamsResponse {
  rpc.Status status = 1;
  google.protobuf.Duration aa_salary_min_duration_for_reactivation_eligibility = 2;
  google.protobuf.Duration aa_salary_new_activation_duration = 3;
  int32 aa_salary_monthly_reward_evaluation_date = 4;
  int32 aa_salary_max_reward_activation_advance_months = 5;
}
