syntax = "proto3";
package varys;

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";



option go_package = "github.com/epifi/gamma/api/varys";
option java_package = "com.github.epifi.gamma.api.varys";

service Varys {
  rpc AlertmanagerWebhook (AlertmanagerWebhookRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/varys/alertmanagerWebhook"
      body: "*"
    };
  }
}

message Alert {
  string status = 1;
  Label labels = 2;
  Annotations annotations = 3;
  string starts_at = 4 [json_name = "startsAt"];
  string ends_at = 5 [json_name = "endsAt"];
  string generator_url = 6 [json_name = "generatorURL"];
  string fingerprint = 7;
}

message Label {
  string grpc_service = 1;
  string severity = 2;
  string grpc_method = 3;
  string environment = 4;
  string service = 5;
  string alert_name = 6 [json_name = "alertname"];
  string team = 7;
  // Vendor name identifies the vendor for which the alert is triggered
  string vendor_name = 8;
  // API identifier represent the vendor url/url_template for which the alert is triggered
  string api_identifier = 9 [json_name = "api"];
  // Redacted HTTP status code in format '{first_digit}xx' (e.g., '2xx', '4xx', '5xx')
  // sends in vendor api response
  string redacted_http_code = 10;
  // Vendor-specific error code returned in successful HTTP responses
  string error_code = 11;
}

message Annotations {
  string summary = 1;
  string description = 2;
}

message AlertmanagerWebhookRequest {
  string version = 1;
  string group_key = 2 [json_name = "groupKey"];
  int32 truncated_alerts = 3 [json_name = "truncatedAlerts"];
  string status = 4;
  string receiver = 5;
  Label group_labels = 6 [json_name = "groupLabels"];
  Label common_labels = 7 [json_name = "commonLabels"];
  Annotations common_annotations = 8 [json_name = "commonAnnotations"];
  string external_url = 9 [json_name = "externalURL"];
  repeated Alert alerts = 10;
}


