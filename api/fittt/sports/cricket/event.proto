// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package api.fittt.sports.cricket;

import "google/protobuf/timestamp.proto";
import "api/fittt/sports/sports.proto";

option go_package = "github.com/epifi/gamma/api/fittt/sports/cricket";
option java_package = "com.github.epifi.gamma.api.fittt.sports.cricket";

// BattingStats contains info about batting stats for all the batsmen in the match.
// This includes batsman from both the teams.
message BattingStats {
  Match match = 1;
  repeated BatsmanStat batting_stats = 2;
  // Innings in match to which all the stats belong
  uint32 innings = 3;
}

message BatsmanStatsEvent{
  Match match = 1;
  BatsmanStat batting_stats = 2;
}

// contains info about bowling stats for all the bowlers in the match.
// This includes bowlers from both the teams.
message BowlingStats {
  Match match = 1;
  repeated BowlerStat bowler_stats = 2;
  // Innings in match to which all the stats belong
  uint32 innings = 3;
}

message BowlerStatsEvent {
  Match match = 1;
  BowlerStat bowler_stats = 2;
  // Innings in match to which all the stats belong
  uint32 innings = 3;
}

// contains info about match stats like winning team.
message MatchResult {
  Match match = 1;
  string winning_team_id = 2;
  MatchResultType result_type = 3;
}

message TeamMaidenOvers {
  Match match = 1;
  repeated TeamMaiden maiden_stats = 2;
  uint32 innings = 3;
}

enum MatchResultType {
  MATCH_RESULT_TYPE_UNSPECIFIED = 0;
  DRAWN = 1;
  WON = 2;
  // more specific WON type can be added based on requirement
  //  WON_BY_WICKETS = 3;
  //  WON_BY_INNINGS = 4;
}


// contains info about all the maiden overs bowled in a match(from both the teams) like the team & bowler details.
message MaidenOvers {
  Match match = 1;
  repeated MaidenOver maiden_over = 2;
  // Innings in match to which all the stats belong
  uint32 innings = 3;
}

message TeamPartnershipStats {
  Match match = 1;
  uint32 innings = 2;
  repeated TeamPartnershipStat stats = 3;
}

message TeamPartnershipStat {
  string team_id = 1;
  repeated PartnershipStat stats = 2;
}

// OverNumber is used to decimal representation of a over in a cricket, useful in following cases:
// 1. current over (eg. 17.5) 17 units, 5 balls
// 2. total number of overs in the match eg: 20.0 for a T20 match i.e. 20 units, 0 balls
// 3. total overs bowled by a bowler in the spell eg: 4.5 overs bowled by a bowler B in match M
//
// `num_completed` number of whole overs already completed
// `balls_in_current_over` number of balls in the current over being referred to
message OverNumber {
  int32 units = 1;
  int32 balls_in_current_over = 2;
}

// MaidenOver contains info about a maiden over like the bowler/team which bowled etc
message MaidenOver {
  // bowler who bowled the maiden over
  sports.Player bowler = 1;
  // team the bowler belongs to
  sports.Team team = 2;
  // the over num for the maiden over in the inning
  int32 over_num = 3 [deprecated = true];
  // the number of maiden overs of player
  int32 maiden_count = 4;
}

// Information about the match like the teams playing, venue etc
message Match {
  string match_id = 1;
  sports.Team home_team = 2;
  sports.Team away_team = 3;
  string venue = 4;
  google.protobuf.Timestamp started_at = 5;
}

// stats for a particular batsman in a match
message BatsmanStat {
  sports.Player batsman = 1;
  int32 runs_scored = 2;
  int32 balls_faced = 3;
  int32 fours = 4;
  int32 sixes = 5;
}

// stats for a particular bowler in a match
message BowlerStat {
  sports.Player bowler = 1;
  OverNumber over = 2;
  int32 wickets = 3;
  int32 maiden_overs = 4;
  int32 runs_conceded = 5;
}

// stats for a partnership between two players in an innings
message PartnershipStat {
  string team_id = 1;
  int32 runs_scored = 2;
  int32 balls_faced = 3;
}

message TeamMaiden {
  sports.Team team = 1;
  // Key to the map is player id
  // maiden count per player is deprecated from this message.
  // Use BowlerStat message instead of this message
  map<string, int32> maiden_count = 2 [deprecated = true];
  // Total maiden overs of a team in a match
  int32 total_maiden_overs = 3;
}
