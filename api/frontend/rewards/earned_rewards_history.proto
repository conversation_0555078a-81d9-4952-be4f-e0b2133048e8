// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package frontend.rewards;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/frontend/rewards";
option java_package = "com.github.epifi.gamma.api.frontend.rewards";


// Figma: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=4131-2739&t=FIlllzPYbEBoMo2E-4
message MonthlyEarnedRewardsView {
  EarnedRewardsHeader header_view = 1;
  api.typesv2.common.ui.widget.BackgroundColour card_background_colour = 2;
  // EX: BENEFITS EARNED
  api.typesv2.common.Text total_benefits_earned_title = 3;
  // EX: ₹1,000
  api.typesv2.common.Text total_benefits_earned_value = 4;
  api.typesv2.common.ui.widget.BackgroundColour benefit_card_background_colour = 5;
  repeated EarnedRewardsCard benefit_card_items = 6;
}

message EarnedRewardsHeader {
  // Ex: Background color for header
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 1;
  //Ex: December 2023
  api.typesv2.common.Text title = 2;
  // EX: ARRIVED ON 15 AUG
  api.typesv2.ui.IconTextComponent tag = 3;
}

message EarnedRewardsCard {
  // Ex: Fi-COINS EARNED
  api.typesv2.common.Text title = 1;
  // Ex: f899
  api.typesv2.ui.IconTextComponent value = 2;
}

// Figma: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=4456-1271&t=FIlllzPYbEBoMo2E-4
message EarnedRewardsHistoryHeaderView {
    // Ex: [ICON] - magnifi
    api.typesv2.common.VisualElement header_icon = 1;
    // Ex: TOTAL BENEFITS EARNED
    api.typesv2.ui.IconTextComponent total_earned_rewards_title = 2;
    // EX: f5,000
    api.typesv2.ui.IconTextComponent total_earned_rewards_value = 3;
    // EX: How is this calculated?
    api.typesv2.ui.IconTextComponent bottom_text = 4;
}

// Figma: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=4131-2723&t=aFvGP6oqkOn7xehj-4
message RewardsProcessingTimelineInfo {
  // Ex: August 2025
  EarnedRewardsHeader header_view = 1;
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 2;
  // Ex: Fi-Points take up to 10 days to get credited post, statement generation.
  api.typesv2.ui.IconTextComponent processing_info = 3;
}
