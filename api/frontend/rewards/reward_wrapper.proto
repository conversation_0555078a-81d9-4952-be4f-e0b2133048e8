syntax = "proto3";

package frontend.rewards;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/rewards/reward.proto";
import "api/frontend/rewards/exchanger_order.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/frontend/rewards";
option java_package = "com.github.epifi.gamma.api.frontend.rewards";

// A wrapper on reward and exchanger_order for handling cases where we need to
// return (to the client) the reward earned from either of them in same rpc response.
message RewardWrapperV1 {
  oneof data {
    Reward reward = 1;
    ExchangerOrder exchanger_order = 2;
  }

  // common properties relevant to both reward and exchanger_order
  // display state of reward tile. if unspecified, default tile display state will be in full color
  RewardTileDisplayState reward_tile_display_state = 3;

  // tile text (to be shown on reward tile to denote any info related to the tile)
  // contains info like "PROCESSING", "ARRIVING ON 12th May", etc.
  api.typesv2.common.Text tile_text = 4;

  // bool to decide whether to show the reward tile as:
  // opened (https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=15531-86510&mode=design&t=eD9pD7w13Lvdkjh7-0) or
  // closed (https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=15531-86545&mode=design&t=eD9pD7w13Lvdkjh7-0)
  bool show_closed_reward_tile = 5;

  enum RewardTileDisplayState {
    REWARD_TILE_DISPLAY_STATE_UNSPECIFIED = 0;
    // slightly translucent tile display state. figma - https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=15556-88848&t=37KfKtrEBkExoaLW-0
    REWARD_TILE_DISPLAY_STATE_WASHED_OUT = 1;
    // grey scaled tile. figma - https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=15556-88742&t=37KfKtrEBkExoaLW-0
    REWARD_TILE_DISPLAY_STATE_GREYED_OUT = 2;
  }

  // The deeplink which is used for clicks on the various Rewards cards in Rewards landing screen. For V1 implementation
  // Clients will continue to handle the existing hard-coded client behaviour, but will honor this deeplink when available
  // in response
  deeplink.Deeplink reward_tile_deeplink = 6;

  // reward tile display info
  // NOTE: this doesn't contain all the details shown on reward tile, some fields like- top right icon, top left text, show_washed tile etc are used from existing Reward proto
  // figma: https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=3504-6876&t=p7SHxZ2m4j4bvV0w-1
  // (optional) client will use this only if passed from BE, else client will fallback to existing logic
  RewardTileDisplayInfo reward_tile_display_info = 7;

  // This will be set to true if the reward is in a pending, failed, or manual intervention state for more than x days
  // If set to true, client will render a retry button and call the retry api only when user clicks on it
  // figma: https://www.figma.com/design/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=15556-88768&t=LDhxz04YljDu0p4X-1
  bool allow_retry_action = 8;

  // background color supporting gradient
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 9;
}

// reward tile display info
// NOTE: this doesn't contain all the details shown on reward tile, some fields like- top right icon, top left text, show_washed tile etc are used from existing Reward proto
// figma: https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=3504-6876&t=p7SHxZ2m4j4bvV0w-1
message RewardTileDisplayInfo {
  // icon on open reward tile
  // figma: https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=3504-6876&t=p7SHxZ2m4j4bvV0w-1
  // [optional]
  api.typesv2.common.VisualElement icon = 1;

  // reward tile title, ex- ₹5000
  // figma: https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=3504-6876&t=p7SHxZ2m4j4bvV0w-1
  // [optional]
  api.typesv2.common.Text title = 2;

  // reward tile subtitle, ex- Cashback
  // figma: https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=3504-6876&t=p7SHxZ2m4j4bvV0w-1
  // [optional]
  api.typesv2.common.Text subtitle = 3;

  // reward tile bg color, ex- night color if reward is unopened
  // https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=3504-6876&t=p7SHxZ2m4j4bvV0w-1
  // [optional]: If this is present, then it will take priority over already present client handling for bg color
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;

  // bottom visual element on the tile, ex- money plant image on closed tile
  // figma: https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=3504-6339&t=p7SHxZ2m4j4bvV0w-1
  // [optional]: If this is present, then it will take priority over already present client handling
  api.typesv2.common.VisualElement bottom_visual_element = 5;
}
