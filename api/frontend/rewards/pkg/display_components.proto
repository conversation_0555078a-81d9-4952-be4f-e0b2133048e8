syntax = "proto3";

package frontend.rewards.pkg;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/frontend/rewards/pkg";
option java_package = "com.github.epifi.gamma.api.frontend.rewards.pkg";

// NOTE: DON'T IMPORT ANYTHING FROM frontend.rewards or typesv2.deeplink_screen_option.rewards PACKAGE TO AVOID CYCLIC DEPENDENCY


// moved frontend.rewards.CtaV1 here, it was causing cyclic
message Cta {
  // icon text component to be displayed in the CTA
  api.typesv2.ui.IconTextComponent itc = 1;
  // Shadow displayed below the CTA
  api.typesv2.ui.Shadow shadow = 2;
  // deeplink or custom action to be performed
  oneof action {
    deeplink.Deeplink deeplink_action = 3;
    CustomAction custom_action = 4;
  }
}

// CustomAction describes the action to be taken by the parent component using this.
// For e.g.,
// Action -> share code, open tnc page.
message CustomAction {
  // custom action to be performed.
  ActionType action_type = 1;

  // API to be called for the action, will be present if actionType is MAKE_API_CALL.
  CustomActionApi action_api = 2;

  // action specific data
  oneof ActionData {
    // action data for GET_REDEEM_OFFER_INPUT_SCREEN
    GetRedeemOfferInputScreenApiActionData get_redeem_offer_input_screen_api_action_data = 3;
    // action data for INITIATE_REDEMPTION_RPC
    GetInitiateRedemptionApiActionData  get_initiate_redemption_api_action_data = 4;
  }

  message GetRedeemOfferInputScreenApiActionData {
    // offer id for which the redemption input screen is to be fetched
    string offer_id = 1;
    uint32 redemption_price = 2;
  }

  message GetInitiateRedemptionApiActionData {
    // offer id for which the redemption input screen is to be fetched
    string offer_id = 1;
  }

  // Action types to be performed for the particular action.
  enum ActionType {
    // no action to be performed
    ACTION_TYPE_UNSPECIFIED = 0;
    // this action is used to make rpc calls
    MAKE_API_CALL = 1;
  }

  // custom api to be called is defined here
  enum CustomActionApi {
    CUSTOM_ACTION_API_UNSPECIFIED = 0;
    // Custom API action to trigger initiate redemption RPC
    // rpc name : InitiateRedemption
    INITIATE_REDEMPTION_RPC = 1;
    // Get redeemed offer details redirection info rpc
    // rpc name : GetRedeemOfferInputScreen
    GET_REDEEM_OFFER_INPUT_SCREEN = 2;
  }
}

// Banner component
// can be used for promo banners etc
// Figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11136-7837&node-type=frame&t=S8bbx1vBnvAXai1n-0
message Banner {
  // left visual element to be displayed
  // usually the brand logo
  api.typesv2.common.VisualElement left_visual_element = 1;
  // banner title
  api.typesv2.common.Text title = 2;
  // right visual element to be displayed
  // usually a product image
  api.typesv2.common.VisualElement right_visual_element = 3;
  // PageControlDetails is used to represent the indicator details of the banner
  PageControlDetails indicator_details = 4;
  // background color of the banner
  api.typesv2.common.ui.widget.BackgroundColour background_color = 5;
  // shadow of the banner
  api.typesv2.common.ui.widget.Shadow shadow = 6;
  // Banner CTA
  Cta cta = 7;
  // analytics details for the banner
  AnalyticsDetails analytics_details = 8;

  // PageControlDetails defines the indicator details of the banner
  // indicator is used to represent each banner as a dot and a dash when selected
  message PageControlDetails {
    api.typesv2.common.ui.widget.BackgroundColour default_indicator_color = 1;
    api.typesv2.common.ui.widget.BackgroundColour selected_indicator_color = 2;
  }

  // AnalyticsDetails stores the details required for analytic events for the banner
  message AnalyticsDetails {
    // generic map of key value pairs for event properties for the banner
    // e.g. { "offerId": "offer-id-1", "category_tag": "CATEGORY_TAG_VOUCHERS" }
    map<string, string> event_properties = 1;
  }
}

// ScrollBehaviour defines the scroll behaviour of the component
// Please note that this needs to be implemented by the client where the component is used
message ScrollBehaviour {
  // scroll orientation
  ScrollOrientation scroll_orientation = 1;
  // scroll mode
  ScrollMode scroll_mode = 2;
  // scroll type
  ScrollType scroll_type = 3;
  // scroll type data defines any scroll type specific data
  oneof scroll_type_data {
    ScrollTypeAutoScrollData scroll_type_auto_scroll_data = 4;
  }

  // ScrollTypeAutoScrollData stores SCROLL_TYPE_AUTO_SCROLL specific data
  message ScrollTypeAutoScrollData {
    // time in milliseconds after which the first scroll should start
    uint32 first_scroll_delay = 1;
    // time in milliseconds between each scroll
    uint32 scroll_interval = 2;
  }
}

// ScrollOrientation defines the orientation of the scroll i.e. vertical or horizontal
enum ScrollOrientation {
  // default scroll orientation
  SCROLL_ORIENTATION_UNSPECIFIED = 0;
  // vertical scroll
  SCROLL_ORIENTATION_VERTICAL = 1;
  // horizontal scroll
  SCROLL_ORIENTATION_HORIZONTAL = 2;
}

// ScrollMode defines the mode of scroll like infinite or finite scroll
enum ScrollMode {
  // default scroll mode
  SCROLL_MODE_UNSPECIFIED = 0;
  // scroll mode for infinite scroll
  SCROLL_MODE_INFINITE = 1;
  // scroll mode for finite scroll
  SCROLL_MODE_FINITE = 2;
}

// ScrollType defines the type of scroll like user scroll or auto scroll
enum ScrollType {
  // default scroll type
  SCROLL_TYPE_UNSPECIFIED = 0;
  // user scroll
  SCROLL_TYPE_USER_SCROLL = 1;
  // auto scroll
  SCROLL_TYPE_AUTO_SCROLL = 2;
}

// GenericServerDrivenSection is a wrapper for types.ui.sdui.sections.Section
// This is used to represent a generic server-driven section that can be used in various adhoc context to improve design flexibility
message GenericServerDrivenSection {
  api.typesv2.ui.sdui.sections.Section section = 1;
}
