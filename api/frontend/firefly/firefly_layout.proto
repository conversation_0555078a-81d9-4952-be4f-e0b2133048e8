syntax = "proto3";

package frontend.firefly;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/ui/visual_element_title_subtile_itc.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";

option go_package = "github.com/epifi/gamma/api/frontend/firefly";
option java_package = "com.github.epifi.gamma.api.frontend.firefly";

message CardSection {
  string card_id = 1;
  // Should contain partner logo, card type, card vendor like VISA, Rupay etc.
  api.typesv2.common.VisualElement card_image = 2;
  // View card details cta
  api.typesv2.ui.IconTextComponent view_card = 3;
  // Info for the cta
  api.typesv2.common.Text view_card_info = 4;
}

// Contact section displays contact federal details and carousels like track card details etc.
// Figma : https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5809&t=Ekwmqb6h9aiQwbFD-4
message ContactSection {
  // Displays need help contact federal info
  api.typesv2.ui.VisualElementTitleSubtitleItcElement contact_federal_info = 1;
  // To display email, phone etc
  repeated api.typesv2.ui.IconTextComponent contacts = 2;
  // display tracking info, other details etc.
  repeated deeplink.InfoItemWithCta carousel = 3;
}

// Display list of actionable items e.g, card controls, my collected offers, lounge access etc.
// Figma : https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-6015&t=Ekwmqb6h9aiQwbFD-4
message BottomInfoSection {
  repeated deeplink.InfoItemWithCta bottom_info_list = 1;
}

// display the offers/reward-offers widgets
// https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5860&t=Ekwmqb6h9aiQwbFD-4
// https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5976&t=Ekwmqb6h9aiQwbFD-4
message OffersSection {
  api.typesv2.ui.sdui.sections.Section section = 1;
}

//  To display the rewards widget with earned Fi-Coins, benefits, earnings history etc.
// https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2262-4270&t=p5UEeQjX94cUzRLH-4
message RewardsSection {
  api.typesv2.ui.sdui.sections.Section section = 1;
}
