syntax = "proto3";

package frontend.firefly;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/firefly/cards.proto";
import "api/frontend/firefly/drawable_properties.proto";
import "api/frontend/firefly/enums/enums.proto";
import "api/frontend/firefly/firefly_layout.proto";
import "api/frontend/firefly/transaction.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/home/<USER>";
import "api/frontend/vendor.proto";
import "api/rpc/method_options.proto";
import "api/rpc/page.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/date.proto";
import "api/typesv2/deposit.proto";
import "api/typesv2/device_unlock.proto";
import "api/typesv2/firefly.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/card/card.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/ui/text_with_hyperlinks.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/firefly";
option java_package = "com.github.epifi.gamma.api.frontend.firefly";

service Firefly {
  // FreezeUnfreezeCard rpc for freezing or unfreezing a card, we will call backend rpc for freezing
  // and unfreezing a card and return next action for the client based on response from backend.
  // In case of any error we will return corresponding error view to the client with CTA for next user actions
  rpc FreezeUnfreezeCard (FreezeUnfreezeCardRequest) returns (FreezeUnfreezeCardResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to check current status of any card request
  // this will return the next action for the user based on the current request status
  // In case of failure we will return the corresponding error views
  rpc GetRequestStatus (GetRequestStatusRequest) returns (GetRequestStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // CollectCardDeliveryAddress rpc for collecting the address at which card is to be delivered for the user
  // This rpc returns the next action for the client.
  // This rpc will be used in card onboarding where user can select any of the existing addresses for shipping the card.
  rpc CollectCardDeliveryAddress (CollectCardDeliveryAddressRequest) returns (CollectCardDeliveryAddressResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // StartCardOnboarding rpc to start card onboarding for a user, we will return next action in response to
  // redirect user to next screens.
  rpc StartCardOnboarding (StartCardOnboardingRequest) returns (StartCardOnboardingResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetLandingInfo rpc for checking the current card state of the user and redirecting user to the corresponding
  // screen based on their card states, if card is not created yet we will redirect the user to the next screen
  // based on their onboarding journey.
  rpc GetLandingInfo (GetLandingInfoRequest) returns (GetLandingInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // Frontend RPC to perform the CreateCard ba`ckend RPC .
  // This will be triggered on a CTA for activating the card
  // This will trigger the backend RPC CreateCard which will
  // send a signal to the CreateCard activity in the onboarding workflow
  rpc CreateCard (CreateCardRequest) returns (CreateCardResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // frontend rpc to return a list of available days for bill generation
  // this rpc will hit the backend rpc to get the list and will return the same
  rpc GetBillingDatesInfo (GetBillingDatesInfoRequest) returns (GetBillingDatesInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // frontend RPC to accept the billing dates from the list given to the user
  //  the dates will include the bill generation date as well as the payment due date
  rpc SetCardBillingDates (SetCardBillingDatesRequest) returns (SetCardBillingDatesResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc sends all the details needed for the dashboard
  rpc GetDashboard (GetDashboardRequest) returns (GetDashboardResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc sends the txn details needed for the dashboard
  rpc GetRecentTransactions (GetRecentTransactionsRequest) returns (GetRecentTransactionsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to fetch the credit card limits for controls like ATM, POS, ECOM
  rpc FetchCardLimits (FetchCardLimitsRequest) returns (FetchCardLimitsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to verify QR code of credit card
  rpc VerifyQRCode (VerifyQRCodeRequest) returns (VerifyQRCodeResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC for setting card pin
  rpc SetCardPin (SetCardPinRequest) returns (SetCardPinResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC for setting card preferences
  rpc SetCardPreferences (SetCardPreferencesRequest) returns (SetCardPreferencesResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC for collecting auth flow from the user for a given workflow, for example in case of limits or controls user
  // can choose either liveness or secure pin validation as auth
  rpc CollectAuthFlow (CollectAuthFlowRequest) returns (CollectAuthFlowResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC for initiating card request for any workflow, it takes user input in the request and return the next action for
  // the client
  rpc InitiateCardReq (InitiateCardReqRequest) returns (InitiateCardReqResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC for fetching card control CTAs
  rpc FetchCardControlsCTAs (FetchCardControlsCTAsRequest) returns (FetchCardControlsCTAsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to fetch card usage set by the user
  rpc FetchCardUsage (FetchCardUsageRequest) returns (FetchCardUsageResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to get all transactions for a user sorted desc by transaction timestamp
  // Deprecated: in favour of GetTransactionsV2
  rpc GetTransactions (GetTransactionsRequest) returns (GetTransactionsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to fetch FAQs and their answers wrt credit cards
  rpc GetKnowMoreInfo (GetKnowMoreInfoRequest) returns (GetKnowMoreInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to fetch the card component in cc dashboard screen
  rpc GetDashboardCardInfo (GetDashboardCardInfoRequest) returns (GetDashboardCardInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to fetch unmasked card details for a user.
  rpc GetCardDetails (GetCardDetailsRequest) returns (GetCardDetailsResponse) {
    option (rpc.skip_tokenization) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // rpc to fetch card delivery details for physical card
  rpc GetCardDeliveryDetails (GetCardDeliveryDetailsRequest) returns (GetCardDeliveryDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to fetch details for a specific transaction
  rpc FetchTxnReceipt (FetchTxnReceiptRequest) returns (FetchTxnReceiptResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to check if dispute allowed
  rpc IsDisputeAllowed (IsDisputeAllowedRequest) returns (IsDisputeAllowedResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to get next question for dispute details collection
  rpc GetNextDisputeQuestion (GetNextDisputeQuestionRequest) returns (GetNextDisputeQuestionResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to generate txn id for card pin set or reset for a vendor.
  // This RPC will return a unique transaction id for a vendor. TxnId will be used in generating cred block.
  rpc GenerateTransactionId (GenerateTransactionIdRequest) returns (GenerateTransactionIdResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to fetch different amounts that a customer can choose to pay from
  rpc GetBillPaymentDetails (GetBillPaymentDetailsRequest) returns (GetBillPaymentDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to fetch ongoing credit card in app payment status
  rpc GetCreditCardPaymentStatus (GetCreditCardPaymentStatusRequest) returns (GetCreditCardPaymentStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to fetch dispute journey for a txn to display in the txn receipt. This will be called while
  // loading the txn receipt .
  rpc GetDisputeDetails (GetDisputeDetailsRequest) returns (GetDisputeDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to fetch statement details for in application statement view.
  // This will be called from credit dashboard screen
  rpc GetStatementDetails (GetStatementDetailsRequest) returns (GetStatementDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  //RPC to export statement details as a pdf for given actor id and statement month.
  rpc ExportStatement (ExportStatementRequest) returns (ExportStatementResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to fetch pin set payload details
  rpc GetPinSetDetails (GetPinSetDetailsRequest) returns (GetPinSetDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get reward details for cc dashboard
  rpc GetRewardDetails (GetRewardDetailsRequest) returns (GetRewardDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get eligible merchants and rewards info
  rpc GetEligibleMerchantsAndRewards (GetEligibleMerchantsAndRewardsRequest) returns (GetEligibleMerchantsAndRewardsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to set pin v2
  rpc SetCardPinV2 (SetCardPinV2Request) returns (SetCardPinV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get top active loan accounts
  // deprecated in favour of GetTopActiveLoanAccountsV2
  rpc GetTopActiveLoanAccounts (GetTopActiveLoanAccountsRequest) returns (GetTopActiveLoanAccountsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get all active loan accounts
  // deprecated in favour of GetAllActiveLoanAccountsV2
  rpc GetAllActiveLoanAccounts (GetAllActiveLoanAccountsRequest) returns (GetAllActiveLoanAccountsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get all closed loan accounts
  // deprecated in favour of GetAllClosedLoanAccountsV2
  rpc GetAllClosedLoanAccounts (GetAllClosedLoanAccountsRequest) returns (GetAllClosedLoanAccountsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get top eligible transactions
  // deprecated in favour of GetTopEligibleTransactionsV2
  rpc GetTopEligibleTransactions (GetTopEligibleTransactionsRequest) returns (GetTopEligibleTransactionsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get all eligible transactions
  // deprecated in favour of GetAllEligibleTransactionsV2
  rpc GetAllEligibleTransactions (GetAllEligibleTransactionsRequest) returns (GetAllEligibleTransactionsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get loan offers
  // deprecated in favour of GetTransactionLoanOffersV2
  rpc GetTransactionLoanOffers (GetTransactionLoanOffersRequest) returns (GetTransactionLoanOffersResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to create loan
  rpc CreateLoan (CreateLoanRequest) returns (CreateLoanResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get loan account details by Loan Id
  // deprecated in favour of GetLoanAccountDetailsV2
  rpc GetLoanAccountDetails (GetLoanAccountDetailsRequest) returns (GetLoanAccountDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get top active loan accounts
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17336-75209&mode=design&t=QR0yS8ICA08dR2pQ-4
  rpc GetTopActiveLoanAccountsV2 (GetTopActiveLoanAccountsV2Request) returns (GetTopActiveLoanAccountsV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get all active loan accounts
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=16896-72866&mode=design&t=QR0yS8ICA08dR2pQ-4
  rpc GetAllActiveLoanAccountsV2 (GetAllActiveLoanAccountsV2Request) returns (GetAllActiveLoanAccountsV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get all closed loan accounts
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17503-71471&mode=design&t=QR0yS8ICA08dR2pQ-4
  rpc GetAllClosedLoanAccountsV2 (GetAllClosedLoanAccountsV2Request) returns (GetAllClosedLoanAccountsV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get top eligible transactions
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17336-75299&mode=design&t=QR0yS8ICA08dR2pQ-4
  rpc GetTopEligibleTransactionsV2 (GetTopEligibleTransactionsV2Request) returns (GetTopEligibleTransactionsV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get all eligible transactions
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17503-73225&mode=design&t=QR0yS8ICA08dR2pQ-4
  rpc GetAllEligibleTransactionsV2 (GetAllEligibleTransactionsV2Request) returns (GetAllEligibleTransactionsV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get loan offers
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17389-71261&mode=design&t=QR0yS8ICA08dR2pQ-11
  rpc GetTransactionLoanOffersV2 (GetTransactionLoanOffersV2Request) returns (GetTransactionLoanOffersV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to get loan account details by Loan Id
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15576-66172&mode=design&t=QR0yS8ICA08dR2pQ-4
  rpc GetLoanAccountDetailsV2 (GetLoanAccountDetailsV2Request) returns (GetLoanAccountDetailsV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to fetch status of cc related rewards disbursment
  rpc GetRewardDisbursementStatus (GetRewardDisbursementStatusRequest) returns (GetRewardDisbursementStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to preview pre close loan
  // deprecated in favour of PreviewPreCloseLoanV2
  rpc PreviewPreCloseLoan (PreviewPreCloseLoanRequest) returns (PreviewPreCloseLoanResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to pre close loan
  rpc PreCloseLoan (PreCloseLoanRequest) returns (PreCloseLoanResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to preview pre close loan
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15971-68591&mode=design&t=QR0yS8ICA08dR2pQ-11
  rpc PreviewPreCloseLoanV2 (PreviewPreCloseLoanV2Request) returns (PreviewPreCloseLoanV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC provides summary and quick actions for the credit card
  // Used in Home screen for rendering the Home dashboard card
  // Figma : https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=20054-78854&t=nlnYO7Fg9WtOLRyL-0
  rpc GetCardSummaryForHome (GetCardSummaryForHomeRequest) returns (GetCardSummaryForHomeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetTransactionsV2 rpc to fetch transactions for all txns view
  // A paginated rpc to fetch the txns belonging to a actor.
  // If page size is not passed in the request, the default page size is 30
  //
  // For First call request PageContextRequest is nil
  // For Last page response PageContextResponse is nil
  rpc GetTransactionsV2 (GetTransactionsV2Request) returns (GetTransactionsV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to calculate the worth of benefits that is applicable once the user has spent a particular amount in the month
  // Figma : https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=5582-176745&t=xQCK2sqK107vCzbD-0
  rpc GetBenefitsForMonthlySpendValue (GetBenefitsForMonthlySpendValueRequest) returns (GetBenefitsForMonthlySpendValueResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };
  // rpc to record the submission done by the user for their rewards for future claim
  // This takes in the reward id and reward option id and stores it for a conditional claim
  rpc RecordRewardSelectionInfo (RecordRewardSelectionInfoRequest) returns (RecordRewardSelectionInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to get lounge access reward info for a user
  // rpc will return next action to the client based on the reward status for a user :
  // 1. User not eligible for lounge offer
  // 2. User has claimed the lounge offer and offer in progress or offer processed
  // 3. User has not claimed the offer
  rpc GetLoungeAccessInfo (GetLoungeAccessInfoRequest) returns (GetLoungeAccessInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to claim lounge access
  rpc ClaimLoungeAccess (ClaimLoungeAccessRequest) returns (ClaimLoungeAccessResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to get credit card details and benefits screen
  rpc GetCreditCardDetailsAndBenefits (GetCreditCardDetailsAndBenefitsRequest) returns (GetCreditCardDetailsAndBenefitsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to calculate interest and other details related to a deposit
  rpc GetSecuredCreditCardDepositCalculations (GetSecuredCreditCardDepositCalculationsRequest) returns (GetSecuredCreditCardDepositCalculationsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // a wrapper rpc  to call backend CreateDeposit rpc which updates card request with details required for fd creation, updates new next action and signals the workflow
  // to initiate fd creation with deposit client.
  rpc CreateDeposit (CreateDepositRequest) returns (CreateDepositResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // TriggerRealtimeCardEligibilityCheck rpc to trigger real time card eligibility check for the user and
  // return next screen to be shown to the user
  rpc TriggerRealtimeCardEligibilityCheck (TriggerRealtimeCardEligibilityCheckRequest) returns (TriggerRealtimeCardEligibilityCheckResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to proceed an ongoing task like onboarding post user performs an action. This action could be
  // a continue button etc.
  rpc ProceedWithUserAction (ProceedWithUserActionRequest) returns (ProceedWithUserActionResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // Rpc to get dashboard billing info v2
  rpc GetDashboardBillingInfoV2 (GetDashboardBillingInfoV2Request) returns (GetDashboardBillingInfoV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // Rpc to get the server driven intro details
  rpc GetCCIntroScreen (GetCCIntroScreenRequest) returns (GetCCIntroScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  rpc GetCCIntroScreenV2 (GetCCIntroScreenV2Request) returns (GetCCIntroScreenV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Rpc to get the consent of the user for cc onboarding and record it
  rpc RecordCreditCardOnboardingConsent (RecordCreditCardOnboardingConsentRequest) returns (RecordCreditCardOnboardingConsentResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // Rpc to sync poll workflow
  rpc GetRequestStatusSync (GetRequestStatusSyncRequest) returns (GetRequestStatusSyncResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // UpdateStatementDate will update the statement date for the user .
  rpc UpdateStatementDate (UpdateStatementDateRequest) returns (UpdateStatementDateResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetLoungePassesForUser returns a list of lounges that have been claimed/currently claimed by the user
  rpc GetLoungePassesForUser (GetLoungePassesForUserRequest) returns (GetLoungePassesForUserResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetLoungeAccessIntroScreen will send data to display the lounge intro screen.
  rpc GetLoungeAccessIntroScreen (GetLoungeAccessIntroScreenRequest) returns (GetLoungeAccessIntroScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetAllEligibleCreditCardsScreen will send data to display the all credit cards screen.
  rpc GetAllEligibleCreditCardsScreen (GetAllEligibleCreditCardsScreenRequest) returns (GetAllEligibleCreditCardsScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetSecuredFDScreen will send the screen data for the new Secured cards FD Screen
  // Figma: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=334-17111&mode=design&t=AtwqdcJuU4BRleWh-0
  rpc GetSecuredFixedDepositScreenDetails (GetSecuredFixedDepositScreenDetailsRequest) returns (GetSecuredFixedDepositScreenDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc CollectCommunicationAddress (CollectCommunicationAddressRequest) returns (CollectCommunicationAddressResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  rpc GetCreditCardCommunicationAddresses (GetCreditCardCommunicationAddressesRequest) returns (GetCreditCardCommunicationAddressesResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC method for recording user consent and proceeding with a user action, signaling the workflow according to context to move forward.
  rpc RecordConsentAndProceedWithUserAction (RecordConsentAndProceedWithUserActionRequest) returns (RecordConsentAndProceedWithUserActionResponse) {
    // Option to indicate whether device registration is required.
    option (rpc.device_registration_required) = true;
  };

  // RPC for stopping the credit card onboarding process.
  rpc StopCreditCardOnboarding (StopCreditCardOnboardingRequest) returns (StopCreditCardOnboardingResponse) {
    // Option to indicate whether device registration is required for this operation.
    option (rpc.device_registration_required) = false;
  };

  // GetDashboardSections returns the details required to render the cc revamped dashboard.
  // It will return sd-ui compatible responses to the client.
  rpc GetDashboardSections (GetDashboardSectionsRequest) returns (GetDashboardSectionsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetGenericScreen returns sdui components to render the UI.
  rpc GetGenericScreen (GetGenericScreenRequest) returns (GetGenericScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // RPC to generate an authentication token for client to authenticate with credit card sdk
  rpc GenerateCreditCardSdkAuthToken (GenerateCreditCardSdkAuthTokenRequest) returns (GenerateCreditCardSdkAuthTokenResponse) {
    option (rpc.device_registration_required) = true;
    option (rpc.auth_required) = true;
    option (rpc.savings_account_required) = false;
  };

  // rpc sends all the details needed for the dashboard v2
  rpc GetDashboardV2 (GetDashboardV2Request) returns (GetDashboardV2Response);
}

message GenerateCreditCardSdkAuthTokenRequest {
  // Common request header for frontend services.
  frontend.header.RequestHeader req = 1;
}

message GenerateCreditCardSdkAuthTokenResponse {
  // Common response header for frontend services.
  frontend.header.ResponseHeader resp_header = 1;
  // Generated authentication token to invoke/auth the credit card SDK.
  string auth_token = 2;
  // Token type of the auth_token
  api.typesv2.CreditCardSdkTokenType token_type = 3;
}

message GetDashboardSectionsRequest {
  frontend.header.RequestHeader req = 1;
  api.typesv2.CreditCardRequestHeader credit_card_header = 2;
}

message GetDashboardSectionsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // list of sections to be shown on the screen
  repeated api.typesv2.ui.card.Section sections = 2;
}

// Request message for stopping credit card onboarding.
message StopCreditCardOnboardingRequest {
  frontend.header.RequestHeader req = 1;
  string card_request_id = 2;
}

message StopCreditCardOnboardingResponse {
  // Response header containing metadata.
  frontend.header.ResponseHeader resp_header = 1;
  // Deeplink for the next action to be performed.
  deeplink.Deeplink next_action = 2;
}

// Request message for recording user consent and proceeding with a user action.
message RecordConsentAndProceedWithUserActionRequest {
  // Request header containing metadata.
  frontend.header.RequestHeader req = 1;
  // Consent for credit card onboarding.
  frontend.firefly.enums.CreditCardOnboardingConsent credit_card_onboarding_consent = 2;
  // Unique identifier for the card request.
  string card_request_id = 3;
}

// Response message for recording user consent and proceeding with a requested action.
message RecordConsentAndProceedWithUserActionResponse {
  // Response header containing metadata.
  frontend.header.ResponseHeader resp_header = 1; // Response header containing metadata.
  // Deeplink for the next action to be performed.
  deeplink.Deeplink next_action = 2; // Deeplink for the next action to be performed.
}


message GetAllEligibleCreditCardsScreenRequest {
  frontend.header.RequestHeader req = 1;
}

// Figma - https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4743-29973&mode=design&t=J4NxEIKvEI7Qj1v3-4
message GetAllEligibleCreditCardsScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // text to be shown at the top of the screen.
  api.typesv2.common.Text toolbar_title = 2;
  // list of screen actions to display all credit cards
  api.typesv2.ui.sdui.sections.Section section = 3;
}

message GetLoungeAccessIntroScreenRequest {
  frontend.header.RequestHeader req = 1;
}

// Figma : https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=27659-84019&mode=dev
message GetLoungeAccessIntroScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // text to be shown at the top of the screen.
  api.typesv2.common.Text heading = 2;
  // container containing the top component i.e the
  LoungeAccessTopContainer lounge_access_top_container = 3;
  // list of bottom screen actions. to display already collected passes
  api.typesv2.ui.sdui.sections.Section bottom_sections = 4;
  message LoungeAccessTopContainer {
    api.typesv2.ui.sdui.sections.Section top_section_view = 1;
    frontend.deeplink.Cta primary_action = 2;
    api.typesv2.ui.IconTextComponent secondary_action = 3;
  }

}

message GetLoungePassesForUserRequest {
  frontend.header.RequestHeader req = 1;
}

// Figma : https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=27502-82619&mode=dev
message GetLoungePassesForUserResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // text to be shown at the top of the screen.
  api.typesv2.common.Text heading = 2;
  // to display the lounge passes of the user as a vertical section.
  api.typesv2.ui.sdui.sections.Section sections = 3;
  api.typesv2.ui.IconTextComponent claim_action = 4;
}

message UpdateStatementDateRequest {
  frontend.header.RequestHeader req = 1;
  string card_id = 2;
  int32 new_statement_date = 3;
  int32 new_due_day = 4;
}

message UpdateStatementDateResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message TriggerRealtimeCardEligibilityCheckRequest {
  frontend.header.RequestHeader req = 1;
}

message TriggerRealtimeCardEligibilityCheckResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message GetCreditCardDetailsAndBenefitsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetCreditCardDetailsAndBenefitsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink credit_card_details_and_benefits_screen = 2;
}

message RecordRewardSelectionInfoRequest {
  frontend.header.RequestHeader req = 1;
  string reward_id = 2;
  string reward_option_id = 3;
  string card_request_id = 4;
  enums.CCRewardType reward_type = 5;
}

message RecordRewardSelectionInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message GetBenefitsForMonthlySpendValueRequest {
  frontend.header.RequestHeader req = 1;
  double monthly_spend_value = 2;
}

message GetBenefitsForMonthlySpendValueResponse {
  frontend.header.ResponseHeader resp_header = 1;
  ValueBack value_back = 2;
  message ValueBack {
    message IconWithText {
      api.typesv2.common.Text text = 3;
      string icon = 4;
      string bg_color = 5;
      string title_text = 6;
    }
    IconWithText percent_return = 6;
    deeplink.InfoItem monthly_benefits = 7;
    deeplink.InfoItem annual_benefits = 8;
    string footer_text = 9;
  }
  ValueBackCalculation value_back_calculation = 10;
  message ValueBackCalculation {
    string text = 11;
    string sub_text = 12;
    repeated string columns = 13;
    repeated Row rows = 14;
    message Row {
      repeated api.typesv2.common.Text column_values = 15;
    }
    Row resultant = 16;
  }
  deeplink.InfoItem bottom_info_item = 17;
}

message GetRewardDisbursementStatusRequest {
  frontend.header.RequestHeader req = 1;
  string client_request_id = 2;
  string card_request_id = 3;
  enums.CCRewardOfferType reward_offer_type = 4;
  // needed for pulling users out of long polling periods
  int32 retry_attempt_number = 5;
}

message GetRewardDisbursementStatusResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message PreviewPreCloseLoanRequest {
  frontend.header.RequestHeader req = 1;
  string loan_account_id = 2;
}

message PreviewPreCloseLoanResponse {
  frontend.header.ResponseHeader resp_header = 1;
  string merchant_name = 2;
  string merchant_logo = 3;
  api.typesv2.Money emi_amount = 4;
  PreCloseLoanSummary pre_close_loan_summary = 5;
  string title = 6;
  string tnc_text = 7;
  string sub_title = 8;

  message PreCloseLoanSummary {
    api.typesv2.Money remaining_principal = 1;
    api.typesv2.Money interest = 2;
    api.typesv2.Money processing_fee = 3;
    api.typesv2.Money taxes = 4;
    api.typesv2.Money pre_closure_fee = 5;
    api.typesv2.Money total_payable_amount = 6;
  }
}

message PreviewPreCloseLoanV2Request {
  frontend.header.RequestHeader req = 1;
  string loan_account_id = 2;
}

// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15971-68591&mode=design&t=QR0yS8ICA08dR2pQ-11
message PreviewPreCloseLoanV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  // Title: merchant name
  // Icon: merchant logo
  // SubTitle: transaction amount
  // Desc: additional description
  deeplink.InfoItemV3 merchant_info_with_additional_details = 2;
  api.typesv2.common.Text title = 3;
  api.typesv2.ui.TextWithHyperlinks tnc_text = 4;
  api.typesv2.common.Text swipe_button_text = 6;
  repeated api.typesv2.common.TitleDescriptionComponent pre_close_loan_summary = 7;
  enums.EmiFlowType emi_flow_type = 8;
  api.typesv2.common.VisualElement ve_bank_info = 9;
}

message PreCloseLoanRequest {
  frontend.header.RequestHeader req = 1;
  string loan_account_id = 2;
  enums.EmiFlowType emi_flow_type = 3;
}

message PreCloseLoanResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink for pre close loan success screen
  deeplink.Deeplink next_action = 2;
}

message GetTopActiveLoanAccountsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetTopActiveLoanAccountsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated LoanAccount loan_accounts = 2;
  api.typesv2.Money total_emi = 3;
  frontend.deeplink.Cta view_all = 4;
  string title = 5;
  deeplink.InfoToolTip info_tool_tip = 6;
}

message GetTopActiveLoanAccountsV2Request {
  frontend.header.RequestHeader req = 1;
}

// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17336-75209&mode=design&t=QR0yS8ICA08dR2pQ-4
message GetTopActiveLoanAccountsV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.common.Text toolbar_title = 2;
  api.typesv2.ui.IconTextComponent view_all_button = 3;
  DrawableProperties drawable_properties = 4;
  api.typesv2.ui.IconTextComponent total_emi_amount = 5;
  repeated LoanAccountV2 loan_accounts = 6;
  api.typesv2.common.VisualElement loan_accounts_zero_state_visual_element = 7;
  deeplink.InfoToolTipV2 info_tool_tip = 8;
}

message LoanAccount {
  frontend.deeplink.Cta loan_account_details = 1;
  string merchant_name = 2;
  string merchant_logo = 3;
  api.typesv2.Money emi_amount = 4;
  string payments_left_text_dark_color_code = 5;
  string payments_left_text = 6;
  int64 tenure_in_months = 7;
  int64 number_of_due_repayments = 8;
  string payments_left_text_light_color_code = 9;
}

message LoanAccountV2 {
  api.typesv2.common.Text merchant_name = 1;
  api.typesv2.common.VisualElement merchant_logo = 2;
  api.typesv2.common.Text payments_left = 3;
  api.typesv2.ui.IconTextComponent emi_amount_info = 4;
  ProgressBar emi_progress_bar = 5;
  DrawableProperties drawable_properties = 6;
}

message ProgressBar {
  int32 progress = 1;
  api.typesv2.common.ui.widget.BackgroundColour active_colour = 2;
  api.typesv2.common.ui.widget.BackgroundColour inactive_colour = 3;
}

message GetAllActiveLoanAccountsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetAllActiveLoanAccountsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated LoanAccount loan_accounts = 2;
  string title = 3;
  //Federal bank logo
  string partner_logo_url = 4;
}

message GetAllActiveLoanAccountsV2Request {
  frontend.header.RequestHeader req = 1;
}

// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=16896-72866&mode=design&t=QR0yS8ICA08dR2pQ-4
message GetAllActiveLoanAccountsV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.VisualElement bank_logo = 3;
  repeated LoanAccountV2 loan_accounts = 4;
  DrawableProperties drawable_properties = 5;
  api.typesv2.common.VisualElement loan_accounts_zero_state_visual_element = 6;
}

message GetAllClosedLoanAccountsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetAllClosedLoanAccountsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated LoanAccount loan_accounts = 2;
}

message GetAllClosedLoanAccountsV2Request {
  frontend.header.RequestHeader req = 1;
}

// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17503-71471&mode=design&t=QR0yS8ICA08dR2pQ-4
message GetAllClosedLoanAccountsV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.common.Text title = 2;
  repeated LoanAccountV2 loan_accounts = 3;
  DrawableProperties drawable_properties = 4;
  api.typesv2.common.VisualElement collapse_icon = 5;
}

message GetAllEligibleTransactionsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetAllEligibleTransactionsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated EligibleTransaction eligible_transactions = 2;

  message EligibleTransaction {
    api.typesv2.Money transaction_amount = 1;
    string merchant_name = 2;
    string merchant_logo = 3;
    api.typesv2.Date transaction_date = 4;
    string external_transaction_id = 5;
    string icon_url = 6;
  }
}

message GetAllEligibleTransactionsV2Request {
  frontend.header.RequestHeader req = 1;
}

// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17503-73225&mode=design&t=QR0yS8ICA08dR2pQ-4
message GetAllEligibleTransactionsV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  repeated EligibleTransaction eligible_transactions = 2;
}

message GetTopEligibleTransactionsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetTopEligibleTransactionsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated EligibleTransaction eligible_transactions = 2;
  frontend.deeplink.Cta view_all = 3;

  message EligibleTransaction {
    frontend.deeplink.Cta loan_offers = 1;
    api.typesv2.Money transaction_amount = 2;
    string merchant_name = 3;
    string merchant_logo = 4;
    string icon_url = 5;
  }
}

message GetTopEligibleTransactionsV2Request {
  frontend.header.RequestHeader req = 1;
}

// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17336-75299&mode=design&t=QR0yS8ICA08dR2pQ-4
message GetTopEligibleTransactionsV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.ui.IconTextComponent see_all_button = 2;
  api.typesv2.common.VisualElement eligible_transaction_zero_state_visual_element = 3;
  repeated EligibleTransaction eligible_transactions = 4;
}

message EligibleTransaction {
  api.typesv2.ui.IconTextComponent loan_offers_button = 1;
  // Title: merchant name
  // Icon: merchant logo
  // SubTitle: transaction amount
  deeplink.InfoItemV3 transaction_info = 2;
  string external_transaction_id = 3;
}

message GetTransactionLoanOffersRequest {
  frontend.header.RequestHeader req = 1;
  string external_transaction_id = 2;
}

message GetTransactionLoanOffersResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated LoanOffer loan_offers = 2;
  string merchant_name = 3;
  string merchant_logo = 4;
  deeplink.InfoToolTip info_tool_tip = 5;
  string info_text = 6;
  string tnc_text = 7;

  message LoanOffer {
    string loan_offer_id = 1;
    int64 tenure_in_months = 2;
    api.typesv2.Money loan_amount = 3;
    api.typesv2.Money emi_amount = 4;
    double interest_rate = 5;
    api.typesv2.Money processing_fee = 6;
    api.typesv2.Money tax = 7;
    api.typesv2.Money total_expected_repayment = 8;
  }
}

message GetTransactionLoanOffersV2Request {
  frontend.header.RequestHeader req = 1;
  string external_transaction_id = 2;
}

// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=17389-71261&mode=design&t=QR0yS8ICA08dR2pQ-11
message GetTransactionLoanOffersV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  repeated EmiDataHolder emi_data = 2;
  // Title: merchant name
  // Icon: merchant logo
  // SubTitle: transaction amount
  deeplink.InfoItemV3 merchant_info = 3;
  deeplink.InfoToolTipV2 info_tool_tip = 4;
  api.typesv2.common.Text pay_back_text = 5;
  api.typesv2.common.Text emi_title = 6;
  api.typesv2.common.TextWithIcon pre_close_info = 7;
  api.typesv2.common.Text slider_text = 8;
  api.typesv2.ui.TextWithHyperlinks tnc_text = 9;

  message EmiDataHolder {
    int64 tenure_in_months = 1;
    api.typesv2.common.Text emi_amount = 2;
    repeated api.typesv2.common.TitleDescriptionComponent emi_details = 3;
    string loan_offer_id = 4;
  }
  api.typesv2.common.VisualElement ve_bank_info = 10;
}


message CreateLoanRequest {
  frontend.header.RequestHeader req = 1;
  string loan_offer_id = 2;
}

message CreateLoanResponse {
  frontend.header.ResponseHeader resp_header = 1;
  string loan_account_id = 2;
  frontend.deeplink.Cta loan_account_details = 3;
  // deeplink for create loan success screen
  deeplink.Deeplink next_action = 4;
}

message GetLoanAccountDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string loan_account_id = 2;
}

message GetLoanAccountDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated LoanDetails loan_details = 2;
  string merchant_name = 3;
  string merchant_logo = 4;
  deeplink.Cta pre_close_loan = 5;
  repeated deeplink.Cta additional_menu_cta = 6;
  message LoanDetails {
    api.typesv2.Date due_date = 1;
    api.typesv2.Money emi_amount = 2;
    bool emi_paid = 3;
  }
}

message GetLoanAccountDetailsV2Request {
  frontend.header.RequestHeader req = 1;
  string loan_account_id = 2;
}

// https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=15576-66172&mode=design&t=QR0yS8ICA08dR2pQ-4
message GetLoanAccountDetailsV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  repeated LoanDetails loan_details = 2;
  api.typesv2.common.Text merchant_name = 3;
  api.typesv2.common.VisualElement merchant_logo = 4;
  api.typesv2.ui.IconTextComponent pre_close_button = 5;
  repeated deeplink.Cta additional_menu_cta = 6;
  api.typesv2.common.Text emi_amount = 7;
  deeplink.InfoToolTipV2 info_tool_tip_v2 = 8;
  api.typesv2.common.Text heading = 9;
  ProgressBar progress_data = 10;

  message LoanDetails {
    // date value
    api.typesv2.common.Text title = 1;
    // emi amount value
    api.typesv2.common.Text desc = 2;
    api.typesv2.common.VisualElement installment_status = 3;
    api.typesv2.common.Text installment_number = 4;
    int32 progress = 5;
  }
}

message GetDisputeDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string txn_id = 2;
}

message GetDisputeDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // The title text of the dispute section.
  //  Client may display this string as-is to the user.
  string title_text = 2;
  // List of all the states in chronological order . Useful for
  // displaying the dispute journey timeline
  repeated DisputeState dispute_states = 3;
  // bool for deciding whether to show the refresh button .
  // This will be true for non-terminal states.
  bool show_force_refresh_button = 4;
  // CTA to display details of the dispute
  frontend.deeplink.Cta details = 5;
  // string to display the "Updated <x> <units> ago" string in dispute journey
  string updated_at_string = 6;
}

message DisputeState {
  enums.DisputeStageState dispute_stage_state = 1;
  string text = 2;
  string sub_text = 3;
}

message FreezeUnfreezeCardRequest {
  // common request header for frontend rpc's
  frontend.header.RequestHeader req = 15;

  // card id of the card which needs to be frozen or unfrozen
  string card_id = 1 [(validate.rules).string = {min_len: 2, max_len: 100}];

  // request type denoting if its a freeze or unfreeze request
  // in case of any different request_type we have added proto validations
  firefly.enums.CardRequestType request_type = 2 [(validate.rules).enum = {in: [1, 2]}];

  // reason for freezing/unfreezing a card
  string reason = 3;

  api.typesv2.DeviceUnlockMechanism device_unlock_mechanism = 4;
  api.typesv2.DeviceUnlockMechanismStrength device_unlock_mechanism_strength = 5;
}

message FreezeUnfreezeCardResponse {
  // next action for the user
  frontend.deeplink.Deeplink next_action = 1;
  // response header common across frontend rpc's
  // this has the status and error view
  frontend.header.ResponseHeader resp_header = 15;
}

message GetRequestStatusRequest {
  // common request header for frontend rpc's
  frontend.header.RequestHeader req = 15;

  // unique identifier for a given card request
  string card_request_id = 1;

  int32 attempt_number = 2;
}

message GetRequestStatusResponse {
  enum Status {
    OK = 0;

    IN_PROGRESS = 50;
  }
  // next action for the user
  frontend.deeplink.Deeplink next_action = 1;
  // response header common across frontend rpc's
  // this has the status and error view
  frontend.header.ResponseHeader resp_header = 15;
}

message CollectCardDeliveryAddressRequest {
  frontend.header.RequestHeader req = 15;
  // address type of the address at which card is to be delivered
  // actual address of the user will be present with the user service
  api.typesv2.AddressType address_type = 1 [(validate.rules).enum = {not_in: [0]}];
  // unique identifier for the card onboarding request
  string card_request_id = 2 [(validate.rules).string.min_len = 1];
}

message CollectCardDeliveryAddressResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // next action for the client
  deeplink.Deeplink next_action = 2;
}

message StartCardOnboardingRequest {
  frontend.header.RequestHeader req = 15;
  firefly.enums.CardProgramType card_program_type = 16;
  int32 screen_identifier = 1;
  // card network type selected by user
  frontend.firefly.enums.CardNetworkType card_network_type = 2;
  api.typesv2.CardProgram card_program = 3;
  api.typesv2.CreditCardRequestHeader credit_card_request_header = 4;
  // will be passed in cases where consent is shown in the intro screen.
  enums.CreditCardOnboardingConsent card_onboarding_consent = 5;
}

message StartCardOnboardingResponse {
  enum Status {
    OK = 0;

    ALREADY_EXIST = 5;

    INTERNAL = 13;
  }
  // unique identifier for card onboarding request
  string card_request_id = 1;

  // next action for the user
  frontend.deeplink.Deeplink next_action = 2;

  // offer id based on which request is made
  string offer_id = 3;

  api.typesv2.CardProgram card_program = 4;

  CreditCardHeader credit_card_header = 5;

  frontend.header.ResponseHeader resp_header = 15;
}

message GetLandingInfoRequest {
  frontend.header.RequestHeader req = 15;
  api.typesv2.CreditCardRequestHeader credit_card_request_header = 1;
}

message GetLandingInfoResponse {
  // next action for the user
  frontend.deeplink.Deeplink next_action = 1;

  // offer id based on which request is made
  string offer_id = 2;

  frontend.header.ResponseHeader resp_header = 15;
  frontend.firefly.enums.CardProgramType card_program_type = 16;
  // Credit card details for client side event instrumentation
  CreditCardHeader credit_card_header = 3;
}

message CreateCardRequest {
  frontend.header.RequestHeader req = 1;
  google.type.Date bill_gen_date = 2;
  string card_request_id = 3;
  int64 bill_gen_date_in_long = 4;
  int64 payment_due_date = 5;
  api.typesv2.AddressType address_type = 6;
}

message CreateCardResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 3;
  CreditCardHeader credit_card_header = 4;
}

message GetBillingDatesInfoRequest {
  frontend.header.RequestHeader req = 1;
  // We need screen identifier to determine from which screen this gRPC is being invoked, to cater the response accordingly
  //Screen1: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80[…]pe=design&node-id=31069-94375&mode=design&t=WKVq3KMwDyjON9W6-4
  //Screen2: https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8583%3A34202
  int32 screen_identifier = 2;
  // [OPTIONAL] it will be required to pass in the request when we are fetching billing dates
  // for an onboarded user.
  string card_id = 3;
}

message GetBillingDatesInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated CreditCardPaymentInfo days = 2;
  api.typesv2.common.Text title = 3;
  api.typesv2.common.Text desc = 4;
}

// struct to hold the display strings for bill gen day of month and payment due day of month
message CreditCardPaymentInfo {
  string bill_gen_date_str = 1;
  string payment_date_str = 2;
  int64 bill_gen_date = 3;
  int64 payment_due_date = 4;
  bool default = 5;
  api.typesv2.common.Text title = 6;
  api.typesv2.common.Text sub_title = 7;
  string card_bg_color = 8;
  api.typesv2.ui.IconTextComponent badge = 9;
}

message SetCardBillingDatesRequest {
  frontend.header.RequestHeader req = 1;
  int64 bill_gen_date = 2;
  int64 payment_due_date = 3;
  string card_request_id = 4;
}

message SetCardBillingDatesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetDashboardRequest {
  frontend.header.RequestHeader req = 1;
}

message GetDashboardResponse {
  message BasicInfo {
    string card_id = 1;
    api.typesv2.Money outstanding_balance = 2 [deprecated = true];
    api.typesv2.Money available_balance = 3 [deprecated = true];
    api.typesv2.Money total_balance = 4 [deprecated = true];
    deeplink.Cta card_details_cta = 5;
  }
  message BillInfo {
    api.typesv2.common.Text banner_text = 1;
    api.typesv2.common.Text banner_sub_text = 2;
    deeplink.Cta action_cta = 3;
    // adding a oneOf field for the info on the left side of the banner
    // it can either be an image or a countdown to the payment due date
    oneof banner_icon {
      string img_url = 4;
      BannerIconInfo banner_icon_info = 5;
    }
    api.typesv2.common.Text fees_apply_badge = 6;
  }
  message BannerIconInfo {
    api.typesv2.common.Text days_value = 1;
    api.typesv2.common.Text icon_text = 2;
    string img_url = 3;
  }

  message RewardsInfo {
    // TODO: add details once design is finalized
  }
  message TopNote {
    deeplink.InfoItemWithCta info = 1;
    string bg_color = 2;
  }
  frontend.header.ResponseHeader resp_header = 1;
  BasicInfo basic_info = 2;
  BillInfo bill_info = 3;
  repeated deeplink.InfoItemWithCta bottom_info_list = 4;
  // this is for https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8684%3A35050
  TopNote top_note = 7;
  // this is for https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=8684%3A34782
  deeplink.InfoItemWithCta card_additional_info = 5;
  string partnership_icon_url = 6;
  repeated deeplink.InfoItemWithCta carousel = 8;
  // card image to be shown based on card program : half is shown when we land on dashboard
  api.typesv2.common.VisualElement card_visual_element_half = 9;
  // card image to be shown based on card program : full is shown when a user clicks on view card details and
  // unmasked card details are shown with this visual element in background
  api.typesv2.common.VisualElement card_visual_element_full = 10;
  // fall back background color to be used in case image does not load.
  api.typesv2.common.ui.widget.BackgroundColour fall_background_colour = 11;
  // Background image which is to be rendered on the screen
  // e.g, https://www.figma.com/design/FWW6omNbhzvzwBqlMT03am/D'fixit---workfile?node-id=816-102143&t=w5rTulyf7feLh2yk-4
  api.typesv2.common.VisualElement page_background = 12;
}

message GetRecentTransactionsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetRecentTransactionsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated Transaction recent_transactions = 2;
  deeplink.Cta view_all_transactions_cta = 3;
  deeplink.InfoItem reward_info_item = 4;
}

message FetchCardLimitsRequest {
  frontend.header.RequestHeader req = 1;
  string credit_card_id = 2;
}

message FetchCardLimitsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deprecated in favour of control_limit_details
  repeated ControlLimits control_limits = 2 [deprecated = true];
  // deprecated because of this information getting added separately in every
  deeplink.InfoItem disabled_limits_explanation_info_item = 3 [deprecated = true];
  // map to make the card limits extendable on terms such as location, etc.
  // this will allow the user to set different limits on a different location
  // type
  repeated ControlLimitDetails card_limits = 4;

  message ControlLimitDetails {
    repeated ControlLimits control_limits = 1;
    // the title that is going to be displayed on the tab corresponding to the given list
    // of card control limits
    api.typesv2.common.Text tab_title = 2;
    // location type of the limit. currently INTERNATIONAL and DOMESTIC
    firefly.enums.CardControlLocationType card_control_location_type = 3;
    // component to display some info wrt card controls. For eg. reasons for which certain controls are
    // disabled, reasons for which a particular location type is disabled, etc.
    api.typesv2.ui.IconTextComponent bottom_info_item = 4;
    // bool to specify if this tab is to be selected by default
    bool is_default = 5;
  }

  // struct for showing daily limits values set for each transaction
  message ControlLimits {
    // control type (ATM, POS, ECOM) for which value is being fetched
    enums.CardControlType control_type = 1;
    api.typesv2.Money daily_limit_value = 2;
    deeplink.Cta cta = 3;
  }
}

message VerifyQRCodeRequest {
  frontend.header.RequestHeader req = 1;
  // card_id of the card for which we need to verify data
  string card_id = 2;
  // encrypted data present in the qr code
  string qr_data = 3;
  // card request id needed to create stage
  string card_request_id = 4;
}

message VerifyQRCodeResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message SetCardPinRequest {
  frontend.header.RequestHeader req = 1;
  string card_id = 2;
  string cred_block = 3;
  string card_request_id = 4;
}

message SetCardPinResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message SetCardPreferencesRequest {
  frontend.header.RequestHeader req = 1;
  string card_id = 2;
  message CardPreferences {
    // Enable or disable International
    api.typesv2.common.BooleanEnum international = 1;
    // Enable or disable contactless
    api.typesv2.common.BooleanEnum contactless = 2;
    // Enable or disable ATM transactions
    api.typesv2.common.BooleanEnum atm = 3;
    // Enable or disable POS transactions
    api.typesv2.common.BooleanEnum pos = 4;
    // Enable or disable ECOM transacations
    api.typesv2.common.BooleanEnum ecom = 5;
  }
  CardPreferences card_preferences = 3;
  string card_request_id = 4;
  api.typesv2.DeviceUnlockMechanism device_unlock_mechanism = 5;
  api.typesv2.DeviceUnlockMechanismStrength device_unlock_mechanism_strength = 6;
}

message SetCardPreferencesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message CollectAuthFlowRequest {
  frontend.header.RequestHeader req = 1;
  // card request id for the workflow for which auth flow has to be collected from user
  string card_request_id = 2;
  // auth flow
  frontend.firefly.enums.CardAuthFlow card_auth_flow = 3;
}

message CollectAuthFlowResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // next action for the client
  frontend.deeplink.Deeplink next_action = 2;
}

message InitiateCardReqRequest {
  frontend.header.RequestHeader req = 1;

  string card_id = 2;

  frontend.firefly.enums.CardRequestWorkflow card_request_workflow = 3;

  // request data con contains user provided data for initiating a card request
  oneof RequestData {
    // contains user provided data for changing card limits
    frontend.firefly.LimitsChangeData limits_change_data = 4;
    // contains user provided data for changing card controls
    frontend.firefly.ControlsChangeData controls_change_data = 5;

    frontend.firefly.CardReissueData card_reissue_data = 6;

    // contains user provided data for raising dispute
    frontend.firefly.DisputeData dispute_data = 7;

    //contains data for initiating credit card payments
    frontend.firefly.BillPaymentData bill_payment_data = 8;
  }

  api.typesv2.DeviceUnlockMechanism device_unlock_mechanism = 14;
  api.typesv2.DeviceUnlockMechanismStrength device_unlock_mechanism_strength = 15;
}

message InitiateCardReqResponse {
  frontend.header.ResponseHeader resp_header = 1;

  frontend.deeplink.Deeplink next_action = 2;
}

message LimitsChangeData {
  // This is used to convey if the updated daily value is increased from previous value or not
  // If true, then auth will be done; otherwise not
  bool is_value_increase = 1;

  // control type (ATM, POS, ECOM, Contactless) for which new value is being set
  firefly.enums.CardControlType control_type = 2;

  api.typesv2.Money updated_limit_value = 3;

  firefly.enums.CardControlLocationType card_control_location_type = 4;
}

message ControlsChangeData {
  // Enable or disable International
  api.typesv2.common.BooleanEnum international = 1;
  // Enable or disable contactless
  api.typesv2.common.BooleanEnum contactless = 2;
  // Enable or disable ATM transactions
  api.typesv2.common.BooleanEnum atm = 3;
  // Enable or disable POS transactions
  api.typesv2.common.BooleanEnum pos = 4;
  // Enable or disable ECOM transacations
  api.typesv2.common.BooleanEnum ecom = 5;
}

message CardReissueData {
  // block card reason
  string block_card_reason = 1;
}

message DisputeData {
  string txn_id = 1;
  api.typesv2.Money amount = 2;
  string reason = 3;
  string description = 4;
  frontend.firefly.enums.DisputeType dispute_type = 5;
  string url = 6;
  repeated AnswerMeta answers = 7;
}

message BillPaymentData {
  //card id for which payment has to be done.
  string card_id = 2 [(validate.rules).string = {min_len: 2, max_len: 100}];
  //Amount to be paid
  api.typesv2.Money amount = 3;
  // encoded account id containing the source account. This will allow an extension to
  // allow tpap accounts as an addition to the fi savings account . In case this is empty,
  // it will be assumed to be a savings account payment to ensure backward compatibility
  string derived_account_id = 4;
}

message FetchCardControlsCTAsRequest {
  frontend.header.RequestHeader req = 1;
  string credit_card_id = 2;
}

message FetchCardControlsCTAsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated frontend.deeplink.InfoItemWithCta controls_ctas = 2;
  CreditCardHeader credit_card_header = 3;
}

message FetchCardUsageRequest {
  frontend.header.RequestHeader req = 1;
  string credit_card_id = 2;
}

message FetchCardUsageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated CardUsage card_usages = 2;

  // struct for showing if card control type is enabled/disabled
  message CardUsage {
    // control type (ATM, POS, ECOM) for which value is being fetched
    enums.CardControlType control_type = 1;
    bool is_enabled = 2;
    string title = 3;
    string sub_title = 4;
    bool is_device_unlock_required = 5;
    bool setting_blocked = 6;
  }
}

message GetTransactionsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetTransactionsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated Transaction transactions = 2;
  deeplink.InfoItem reward_info_item = 3;
  int32 start_date = 4;
  int32 end_date = 5;
  string card_id = 6;
  deeplink.InfoItemWithCta view_statement_cta = 7;
  bool is_latest_statement_generated = 8;
  repeated AggregatedRewardsInfo aggregated_rewards = 9;
}

message GetTransactionsV2Request {
  frontend.header.RequestHeader req = 1;
  // page context to help server fetch the page
  rpc.PageContextRequest page_context_request = 2;
}

message GetTransactionsV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  repeated Transaction transactions = 2 [deprecated = true];
  deeplink.InfoItem reward_info_item = 3 [deprecated = true];
  int32 start_date = 4 [deprecated = true];
  int32 end_date = 5 [deprecated = true];
  string card_id = 6 [deprecated = true];
  deeplink.InfoItemWithCta view_statement_cta = 7 [deprecated = true];
  bool is_latest_statement_generated = 8 [deprecated = true];
  repeated AggregatedRewardsInfo aggregated_rewards = 9 [deprecated = true];
  // page context to help client fetch next page
  rpc.PageContextResponse page_context_response = 10;
  // repeated view list corresponding to views for each part of the all txn view
  repeated AllTransactionView all_transaction_view_list = 11;
}

message AllTransactionView {
  oneof View {
    // Transaction view containing details required to view a transaction
    Transaction transaction = 1;
    // Aggregated rewards view containing 2X and 5X rewards and other rewards when added later
    AggregatedRewardsInfoView aggregated_rewards_info_view = 2;
    // billing window view will be used to show the billing window and view statement cta
    deeplink.InfoItemWithCtaV2 billing_window_view = 3;
    // date view to club the transactions on the same day
    deeplink.InfoItem date_view = 4;
  }
  // time corresponding to each view
  google.protobuf.Timestamp view_time = 15;
}

message AggregatedRewardsInfoView {
  repeated RewardsInfoWithHeaderIcon rewards_multipliers = 1;
}

message AggregatedRewardsInfo {
  repeated RewardsInfoWithHeaderIcon rewards_multipliers = 1;
  google.protobuf.Timestamp bill_gen_timestamp = 2;
}

message RewardsInfoWithHeaderIcon {
  deeplink.InfoItem rewards_info = 1;
  deeplink.InfoItem header_info_item = 2;
}

message GetKnowMoreInfoRequest {
  frontend.header.RequestHeader req = 1;
}

message GetKnowMoreInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  string icon = 2;
  string title = 3;
  repeated deeplink.InfoBlock additional_info = 4;
}

message GetDashboardCardInfoRequest {
  frontend.header.RequestHeader req = 1;
  string card_id = 2;
}

message GetDashboardCardInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  oneof card_component {
    EcomDisabledCardComponent ecom_disabled_card_component = 2;
    MaskedDetailsCardComponent masked_details_card_component = 3;
    FrozenCardDetailsComponent frozen_card_details_component = 4;
    BlockedCardDetailsComponent blocked_card_details_component = 5;
  }
  string partner_url = 6;
}

message EcomDisabledCardComponent {
  string ecom_disabled_text = 1;
  frontend.deeplink.Cta enable_online_txn = 2;
  firefly.enums.CardControlType card_control_type = 3;
}

message MaskedDetailsCardComponent {
  string masked_card_number = 1;
  api.typesv2.Money outstanding_balance = 2 [deprecated = true];
  api.typesv2.Money available_balance = 3 [deprecated = true];
  api.typesv2.Money total_balance = 4 [deprecated = true];
  frontend.deeplink.Cta view_card_details = 5;
  deeplink.Cta settings = 6;
  deeplink.InfoToolTip outstanding_balance_tooltip = 7;
  double fraction_limit_available = 8;
  deeplink.InfoItem limit_utilised = 9;
  deeplink.InfoItem limit_available = 10;

}

message FrozenCardDetailsComponent {
  string text = 1;
  deeplink.Cta unfreeze_card = 2;
}

message BlockedCardDetailsComponent {
  string text = 1;
  deeplink.Cta reissue_card = 2;
}

message GetCardDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string card_request_id = 2;
}

message GetCardDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  string expiry_date = 2;
  string clear_card_number = 3;
  enums.CardNetworkType card_network_type = 4;
  string cvv = 5;
  api.typesv2.common.Name name = 6;
  // boolean to determine if copy card details feature is enabled or not
  bool enable_copy_card_details = 7;
  // expiry in seconds for copy card details
  int64 copy_card_details_expiry = 8;
  // expiry in seconds for view card details
  int64 view_card_details_expiry = 9;
  // card image to be shown based on card program : half is shown when we land on dashboard
  api.typesv2.common.VisualElement card_visual_element_half = 10;
  // card image to be shown based on card program : full is shown when a user clicks on view card details and
  // unmasked card details are shown with this visual element in background
  api.typesv2.common.VisualElement card_visual_element_full = 11;
  // fall back background color to be used in case image does not load.
  api.typesv2.common.ui.widget.BackgroundColour fall_background_colour = 12;
}

message GetCardDeliveryDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string card_id = 2;
}

message GetCardDeliveryDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Url of the image to be shown on card tracking screen
  string image_url = 2;
  // Delivery states and their desc.
  repeated DeliveryTile delivery_tiles = 3;
  // informative string regarding the expected delivery time
  deeplink.InfoItem additional_info = 4;
  oneof DeliveryInfo {
    // Informative text to be shown in case of awb number and courier partner details not available
    string fallback_string = 5;
    // Delivery details such as awb number and carrier partner for identifying shipment at delivery vendor's end.
    ShipmentDetails shipment_details = 6;
  }
  message ShipmentDetails {
    // unique identifier using which user can track their card
    string awb = 1;
    // courier partner
    string carrier = 2;
  }
}

message FetchTxnReceiptRequest {
  frontend.header.RequestHeader req = 1;
  string txn_id = 2;
}

message FetchTxnReceiptResponse {
  frontend.header.ResponseHeader resp_header = 1;
  TransactionReceipt receipt = 2;
}

message TransactionReceipt {
  // url for icon for the other party with which the txn has taken place.
  string icon_url = 1;
  // terminal status of the transaction
  enums.TransactionStatus txn_status = 2;
  // list of info items describing transaction info .
  // currently includes FROM, TO, TXN_MODE, TXN_ID
  repeated frontend.deeplink.InfoItem receipt_info_items = 3;
  // formatted string containing date and time of the txn
  // eg. April 14 at 3:14 pm
  string txn_time_string = 4;
  string partner_url = 5;
  // url to power the Get Help functionality
  string help_url = 6;
  repeated TransactionDisplayCategory txn_display_categories = 7;
  // object to populate header of the receipt
  ReceiptHead txn_receipt_head = 8;
  repeated frontend.deeplink.Cta additional_actions = 9;
  frontend.deeplink.Cta emi_cta = 10;
  // figma for rewardsInfo : https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=23004-84719&t=Exf8L7BuICX2g4uQ-0
  RewardInfo reward_info = 11;
  deeplink.InfoItemWithCtaV2 emi_details_info = 12;
  message RewardInfo {
    string title = 1;
    string desc = 2;
    string icon = 3;
    string fi_coins = 4;
    bool negative = 5;
  }
}

message ReceiptHead {
  // text describing whether the amount was paid to or from
  // could be PAID TO or PAID FROM
  string paid_text = 1;
  // name of the other party involved in the txn
  api.typesv2.common.Name other_party_name = 2;
  // badge icon url for the txn .
  // defaulting to Fi badge icon
  string badge_url = 3;
  // icon for the other party with which the transaction has taken place
  string other_party_icon = 4;
  // color code for the other party icon
  string other_party_color_code = 5;
  // the amount of money transacted over
  api.typesv2.Money amount = 6;
  // description of the transaction.
  // could be something automatically generated or user entered
  string remarks = 7;
  // boolean specifying whether the txn was a reversal
  bool is_reversal = 8;
}

message IsDisputeAllowedRequest {
  frontend.header.RequestHeader req = 1;
  // internal credit card transaction id
  string txn_id = 2;
}

message IsDisputeAllowedResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetNextDisputeQuestionRequest {
  frontend.header.RequestHeader req = 1;
  string question_id = 2;
  string answer = 3;
}

message GetNextDisputeQuestionResponse {
  frontend.header.ResponseHeader resp_header = 1;
  QuestionMeta question = 2;
}

message QuestionMeta {
  string channel = 1;

  string question_code = 2;

  string actual_question = 3;

  repeated string answer_options = 4;

  frontend.firefly.enums.AnswerDataType answer_data_type = 5;

  bool is_optional = 6;

  string placeholder_text = 7;
}

message DeliveryTile {
  // Title of the delivery state
  string title = 1;
  // Description of the delivery status
  string description = 2;
  // url of the image to shown with each state
  string image_url = 3;
  // status of the delivery state
  // If shipment delivery state is IN_TRANSIT then for SHIPPED state this flag will be true
  frontend.firefly.enums.DeliveryTileState delivery_tile_state = 4;
}

message GenerateTransactionIdRequest {
  // card-id for which txn-id will be generated
  string card_id = 1 [(validate.rules).string = {min_len: 2, max_len: 100}];

  // vendor for which txn id need to be generated
  frontend.Vendor vendor = 2 [(validate.rules).enum = {not_in: [0]}];

  frontend.header.RequestHeader req = 15;
}

message GenerateTransactionIdResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // unique txn id
  string txn_id = 1;

  // masked card number to use in salt
  string masked_card_number = 2;
}

message GetBillPaymentDetailsRequest {
  frontend.header.RequestHeader req = 1;
  //card id for which payment amount has to be fetched.
  string card_id = 2 [(validate.rules).string = {min_len: 2, max_len: 100}];
}

message GetBillPaymentDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated AmountDetail amount_details = 2;
  //color code for the bill amount panel
  string bg_color = 3;
  //top banner text depending on amount due and due date
  deeplink.InfoItem top_banner_details = 4;
  // bottom banner details and cta
  deeplink.InfoItemWithCta bottom_banner_details = 5;
  //Account details through which payment will be made.
  AccountDetails account_details = 6;
  //CTAs for proceeding with payment
  deeplink.InfoItemWithCta insufficient_balance = 8;
  deeplink.Cta swipe_to_pay = 9;
  deeplink.Cta custom_amount = 10;
  //Federal bank logo
  string partner_logo_url = 11;
  CreditCardHeader credit_card_header = 12;
  // CTA to add another account to facilitate payment
  deeplink.Cta add_account = 13;
  // bool to switch on/off tpap payment. If this flag is false, then the
  // client should not show the option to link an account
  bool enable_tpap = 14;
}

message AmountDetail {
  api.typesv2.Money amount = 1;
  string amount_type = 2;
  api.typesv2.common.Text badge_note = 3;
  bool is_custom_amount = 4;
}

message AccountDetails {
  deeplink.InfoItem account_info = 1;
  api.typesv2.Money balance_amount = 2;
}

message GetStatementDetailsRequest {
  //Actor id will be used from req header
  frontend.header.RequestHeader req = 1;
  //Statement duration is used to fetch the statement for given month
  StatementDuration statement_duration = 2;
  //card id used to fetch associated account.
  string card_id = 3;
}

message StatementDuration {
  api.typesv2.Date from_date = 1;
  api.typesv2.Date to_date = 2;
  bool selected = 3;
}

message GetStatementDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated StatementDuration statement_durations = 2;
  StatementBreakupDetails statement_breakup_details = 3;
  deeplink.Cta view_transactions = 4;
  deeplink.Cta export_statement = 5;
  deeplink.Cta pay_bill = 6;
  string partner_bank_logo = 7;
  CreditCardHeader credit_card_header = 8;
}

message StatementBreakupDetails {
  AmountInfo total_amount_due_info = 1;
  AmountInfo minimum_amount_due_info = 2;
  deeplink.InfoItem due_amount_info = 3;
  repeated StatementAmountDetails statement_amount_categories = 4;
  deeplink.InfoItem reward_coins = 5;
  MerchantRewardsInfo merchant_rewards = 6;
  SecuredCardRewardsInfo secured_card_rewards_info = 7;
}
message StatementAmountDetails {
  string statement_amount_title = 1;
  string statement_amount_desc = 2;
  api.typesv2.Money amount = 3;
  string display_amount = 4;
  firefly.enums.AmountCategory amount_category = 5;
}

message ExportStatementRequest {
  //Actor id will be fetched from req
  frontend.header.RequestHeader req = 1;
  //Statement duration will be used to fetch statement for given window.
  StatementDuration statement_duration = 2;
}

message ExportStatementResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink export_response = 2;
}

message GetCreditCardPaymentStatusRequest {
  // common request header for frontend rpc's
  frontend.header.RequestHeader req = 15;
  // unique identifier for a given card request which will be used to identify the status of the
  // payment workflow
  string payment_req_id = 1;
  int32 attempt_number = 2;
  // order id which will be used to check status of the order that has been created
  string order_id = 3;
}

message GetCreditCardPaymentStatusResponse {
  // response header common across frontend rpc's
  // this has the status and error view
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink deeplink = 2;
}

message AmountInfo {
  string amount_title = 1;
  string display_amount = 2;
  api.typesv2.Money amount = 3;
}

message MerchantRewardsInfo {
  string reward_title = 1;
  repeated MerchantWiseReward merchant_wise_rewards = 2;
  message MerchantWiseReward {
    string merchant_name = 1;
    string merchant_logo = 2;
    api.typesv2.Money amount = 3;
    string rewards_logo = 4;
    int32 reward_coins = 5;
  }
}

message GetPinSetDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string card_id = 2;
}

message GetPinSetDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  message Payload {
    string entity_id = 1;
    string kit_no = 2;
    // Due to compliance regulations we cannot have raw expiry passing through our backend layer, we will be using the
    // expiry token to pass the expiry which will be converted to actual expiry date by atlas
    api.typesv2.Date expiry_date = 3;
    api.typesv2.Date dob = 4;
    string masked_card_no = 5;
    string expiry_token = 6;
  }
  Payload payload = 2;
}

message GetRewardDetailsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetRewardDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  BillingCycleInfo billing_cycle_info = 2 [deprecated = true];
  //current rewards info are processed/processing rewards without extra rewards.
  CurrentRewardsInfo current_rewards_info = 3 [deprecated = true];
  ExtraRewardsInfo extra_rewards_info = 4 [deprecated = true];
  oneof RewardsConstructInformation {
    UnsecuredRewardsConstructInformation unsecured_rewards_construct_information = 5 [deprecated = true];
    // figma: https://www.figma.com/file/6Ymhg0sYQpOQQZAV1QAwG9/Secured-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1701-57585&mode=design&t=XXOqhPAzN4NrCbgc-0
    SecuredRewardsConstructInformation secured_rewards_construct_information = 6 [deprecated = true];
  }
  // rewards information to be shown on the credit card dashboard (deprecated in favour of SDUI Section)
  DashboardRewardsInformation dashboard_rewards_information = 7 [deprecated = true];

  // display the rewards tracker widget v2
  // https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43315-54515&t=I8gEcpfA7vSBfp3c-4
  api.typesv2.ui.sdui.sections.Section section = 8;

  // https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43394-37729&t=W6IglNDq0ztZ7d3G-4
  api.typesv2.ui.card.OffersAndPromotionsSection offers_and_promotions_section = 9;
}

// Generic rewards construct to power rewards on the credit card dashboard
message DashboardRewardsInformation {
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=33804-43841&mode=dev
  BillingCycleInfo billing_cycle_info = 1;
  // https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=2413-8680&mode=dev
  repeated deeplink.InfoItemV2 rewards_constructs = 2 [deprecated = true];
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=33804-43841&mode=dev
  api.typesv2.common.Text rewards_information = 3 [deprecated = true];
  // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--Unsecured-CC-%E2%80%A2-Workfile?type=design&node-id=33804-43614&mode=dev
  ExtraRewardsInfo extra_rewards_info = 4 [deprecated = true];
  // https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=2413-8680&mode=dev
  repeated RewardsConstructInformation rewards_construct_information = 5;
}

message UnsecuredRewardsConstructInformation {
  BillingCycleInfo billing_cycle_info = 1;
  CurrentRewardsInfo current_rewards_info = 2;
  ExtraRewardsInfo extra_rewards_info = 3;
}

message SecuredRewardsConstructInformation {
  BillingCycleInfo billing_cycle_info = 1;
  repeated RewardsConstructInformation rewards_construct_information = 2;
}

// This struct will be used to power rewards construct on dashboard
message RewardsConstructInformation {
  oneof RewardsInformationCategory {
    // Body type will be used to show base/accelerated rewards and other details
    RewardsInformationCategoryBody rewards_information_category_body = 1;
    // Footer will be used to show some additional details and close the rewards construct information
    RewardsInformationCategoryFooter rewards_information_category_footer = 2;
  }
}

message RewardsInformationCategoryBody {
  // Primary visual element will be used for showing the rewards info (base/accelerated) logo.
  api.typesv2.common.VisualElement primary_visual_element = 1;
  // Secondary visual element will be used for showing the rewards type status icon. For ex: Fi coin processing logo etc..
  api.typesv2.common.VisualElement secondary_visual_element = 2;
  // Primary text provides information about the rewards construct
  api.typesv2.common.Text primary_text = 3;
  // Secondary text provides information about the actual units of rewards earned for the construct
  api.typesv2.common.Text secondary_text = 4;
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 5;
  // Use this field in case we want to navigate a user to another screen / story / bottom to show a detailed view of rewards construct.
  api.typesv2.ui.IconTextComponent rewards_info_cta = 7;
}

message RewardsInformationCategoryFooter {
  api.typesv2.common.Text footer_text = 1;
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 2;
}

message BillingCycleInfo {
  // deprecated in favor of title_text, for controlling font and style from backend.
  string title = 1 [deprecated = true];
  // deprecated in favor of current_billing_cycle_info, for better control from backend.
  deeplink.InfoItem current_billing_cycle_details = 2 [deprecated = true];
  // deprecated in favor of rewards_construct_cta, for better control from backend.
  deeplink.InfoItemWithCta billing_cycle_cta = 3 [deprecated = true];
  api.typesv2.common.Text title_text = 4;
  deeplink.Cta rewards_construct_cta = 5;
  // Required for following text and icon component: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=2413-8355&mode=design&t=rscIbOkzhIwCLHFP-0
  api.typesv2.common.TextWithIcon current_billing_cycle_info = 6;
  // Required for following text and icon component: https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=2413-8355&mode=design&t=rscIbOkzhIwCLHFP-0
  api.typesv2.common.TextWithIcon rewards_construct_info = 7;
}

message CurrentRewardsInfo {
  string title = 1;
  deeplink.InfoItem processing_rewards = 2;
  deeplink.InfoItem processed_rewards = 3;
  string bottom_text = 4;
}

message ExtraRewardsInfo {
  string title = 1;
  api.typesv2.common.TextWithIcon rewards_arrival_info = 2;
  repeated DashboardRewardsInfo dashboard_rewards_infos = 3;
  UpgradeRewardTierInfo upgrade_reward_tier_info = 4;
  deeplink.Cta eligible_merchant_cta = 5;
}

message SetCardPinV2Request {
  frontend.header.RequestHeader req = 1;
  string card_request_id = 2;
  string cred_block = 3;
}

message SetCardPinV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message DashboardRewardsInfo {
  string title = 1;
  deeplink.InfoItemWithCta view_reward_info_cta = 2;
  api.typesv2.common.TextWithIcon total_reward_coins = 4;
  string bg_color = 5;
  TopMerchantsInfo top_merchants_info = 6;
  string bottom_text = 7;
  string bg_shadow_color = 8;
}

message UpgradeRewardTierInfo {
  api.typesv2.common.TextWithIcon reward_upgrade_info = 1;
  string progress_bar_color = 2;
  api.typesv2.common.Text progress_bar_text = 3;
  float progress_percentage = 4;
  api.typesv2.common.Text bottom_text = 5;
}

message TopMerchantsInfo {
  api.typesv2.common.Text title = 1;
  repeated MerchantInfo merchant_infos = 2;
}

message MerchantInfo {
  string merchant_icon = 1;
  string merchant_name = 2;
  int32 rank = 3;
  deeplink.InfoItem reward_coin_details = 4;
  string no_fi_coins_text = 5;
}

message GetEligibleMerchantsAndRewardsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetEligibleMerchantsAndRewardsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  TopEligibleMerchantInfo top_eligible_merchant_info = 2;
  OtherEligibleMerchantInfo other_eligible_merchant_info = 3;
}

message TopEligibleMerchantInfo {
  string title = 1;
  repeated MerchantInfo merchant_infos = 2;
  string bottom_text = 3;
}

message OtherEligibleMerchantInfo {
  string title = 1;
  repeated MerchantInfo merchant_infos = 2;
  deeplink.Cta show_full_list = 3;
  deeplink.Cta hide_full_list = 4;
  // Page size means min no of merchants to be shown when hide
  // full list cta is clicked/ the screen is loaded for the first time/
  int32 page_size = 5;
}

message GetCardSummaryForHomeRequest {
  frontend.header.RequestHeader req = 1;
  // Request param to control which Dashboard UI version is shown on clients. This param will be not set for old clients
  // and may/may not be set for new clients based on experimentation. See DashboardVersion docs for default handling
  home.DashboardVersion dashboard_version = 2;
  // Optional field to control zero state card variant to be shown for DASHBOARD_VERSION_V2.
  // This may only be set if dashboard_version is set to DASHBOARD_VERSION_V2
  // Even if not set, the base variant of zero state dashboard cards would be shown
  home.ZeroStateDashboardCardVariant zero_state_dashboard_variant = 3;
}

message GetCardSummaryForHomeResponse {
  frontend.header.ResponseHeader resp_header = 1;
  home.HomeDashboard dashboard_info = 2;
}

// message to aggregate the data from Be RPCs to power the Home Dashboard for credit card
message HomeDashboardData {
  enums.HomeDashboardCardType home_dashboard_card_type = 1;

  oneof home_card_info {
    CreditCardInfo card_info = 2;
  }
  message CreditCardInfo {
    google.type.Money available_limit = 1;
    google.type.Money utilised_limit = 2;
    google.type.Money bill_amount = 3;
    bool is_card_freeze = 4;
    string card_id = 5;
    google.type.Date bill_to_date = 6;
    google.type.Date bill_from_date = 7;
    bool is_bill_partially_paid = 8;
    google.type.Date soft_due_date = 9;
  }

  home.DashboardVersion dashboard_version = 3;
  home.ZeroStateDashboardCardVariant zero_state_dashboard_card_variant = 4;
  api.typesv2.CardProgram card_program = 5;
}

message AnswerMeta {
  string question_code = 1;
  string question = 2;
  string answer = 3;
  frontend.firefly.enums.AnswerDataType answer_data_type = 4;
}

message GetLoungeAccessInfoRequest {
  frontend.header.RequestHeader req = 1;
}

message GetLoungeAccessInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message ClaimLoungeAccessRequest {
  frontend.header.RequestHeader req = 1;
}

message ClaimLoungeAccessResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message GetSecuredCreditCardDepositCalculationsRequest {
  frontend.header.RequestHeader req = 1;
  api.typesv2.Money deposit_amount = 2;
  api.typesv2.Money credit_limit = 3;
  api.typesv2.DepositTerm deposit_term = 4;
}

message GetSecuredCreditCardDepositCalculationsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.Money deposit_amount = 2;
  api.typesv2.Money maturity_amount = 3;
  api.typesv2.Money credit_limit = 4;
  repeated frontend.deeplink.InfoItemV2 deposit_display_details = 5 [deprecated = true];
  api.typesv2.Money current_account_balance = 6;
  repeated frontend.deeplink.InfoItemWithCtaV3 deposit_editable_details = 7;
}

message CreateDepositRequest {
  frontend.header.RequestHeader req = 1;
  // Deprecated in favour of deposit_amount and credit_limit
  api.typesv2.Money amount = 2 [(validate.rules).message.required = true, deprecated = true];
  repeated NomineeDetails nominee_details = 3;
  string card_request_id = 4;
  api.typesv2.DepositTerm deposit_term = 5;
  api.typesv2.Money deposit_amount = 6;
  api.typesv2.Money credit_limit = 7;
}

message CreateDepositResponse {
  frontend.header.ResponseHeader resp_header = 1;
  string order_id = 2;
  frontend.deeplink.Deeplink next_action = 3;
}

message NomineeDetails {
  string nominee_id = 1;
  double nominee_share_percentage = 2;
}

// Request message for proceeding with a user action.
message ProceedWithUserActionRequest {
  // Request header containing metadata.
  frontend.header.RequestHeader req = 1;
  // Deprecated field: deeplink to the screen.
  deeplink.Screen screen = 2 [deprecated = true];
  // Unique identifier for the card request.
  string card_request_id = 3;
  //Todo: pass context here instead of screen for future cases.
}

message ProceedWithUserActionResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message GetDashboardBillingInfoV2Request {
  frontend.header.RequestHeader req = 1;
}

message GetDashboardBillingInfoV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  BillInfoProperties bill_info_properties = 2;
  BillInfoHeaderDetails bill_info_header_details = 3;
  deeplink.InfoItemWithCtaV3 bill_info_more_details = 4;
  api.typesv2.common.Text badge_details = 5;
}

message BillInfoHeaderDetails {
  deeplink.InfoItemV3 text_with_icon = 1;
  api.typesv2.common.Text additional_info = 2;
  api.typesv2.common.VisualElement show_more_details = 3;
}

message BillInfoProperties {
  frontend.firefly.enums.BillInfoType bill_info_type = 1;
  string header_separator_color = 2;
  string info_separator_color = 3;
  string background_color = 4;
  bool is_collapsed = 5;
}

message GetCCIntroScreenRequest {
  frontend.header.RequestHeader req = 1;
  int32 screen_identifier = 2;
  // Deprecated: in favour of ypes.CreditCardRequestHeader
  api.typesv2.CardProgramType card_program_type = 3;
  // Deprecated: in favour of ypes.CreditCardRequestHeader
  api.typesv2.CardProgram card_program = 4;
  api.typesv2.CreditCardRequestHeader credit_card_request_header = 5;
}

// Figma: https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-20302&mode=dev
message GetCCIntroScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated IntroScreenTemplate templates = 2;
  PageIndicatorWidget page_indicator = 3;
  WrappedHyperLinksWidget consent_text = 4;
  WrappedButtonInfo wrapped_btn_info = 5;
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 6;
  WrappedVisualElement partner_icon = 7;
  PagerAutoScrollProperties auto_scroll_properties = 8;
  WrappedIconTextToolBar tool_bar = 9;
  // By-default the horizontal scroll will be enabled, field will override the default and enable the vertical scroll.
  bool enable_vertical_scroll = 10;
  CcStickyChoiceComponent cc_choices_cta_component = 11;
  CcScrollableChoiceComponent cc_scrollable_choice_component = 12;
  api.typesv2.common.ui.widget.BackgroundColour sticky_bottom_background_colour = 13;
  // Credit card details for client side event instrumentation
  CreditCardHeader credit_card_header = 14;
  // Nil hide the checkbox else show
  ConsentBox consent_box = 15;
}

message GetCCIntroScreenV2Request {
  frontend.header.RequestHeader req = 1;
  api.typesv2.CreditCardRequestHeader credit_card_request_header = 2;
  // marshalled string of api/typesv2/firefly.proto -> CreditCardMetadata
  string metadata = 3;
}

message GetCCIntroScreenV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.ui.sdui.sections.Section section = 2;
  api.typesv2.ui.sdui.sections.Section floating_section = 3;
  // background along with card rays in case of Magnifi Onboard Screen (optional)
  api.typesv2.common.VisualElement background_image = 4;
}


// Serve as 90-10 use case
// Figma: https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4743-29939&mode=dev
message CcStickyChoiceComponent {
  api.typesv2.ui.IconTextComponent icon_text_component = 1;
}

// Serve as 80-20 use case
message CcScrollableChoiceComponent {
  oneof CardChoice {
    // Figma: https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=9942-47971&mode=design&t=gKnmNUycLbIaqXUh-4
    api.typesv2.ui.IconTextComponent icon_text_component = 1;
    //Figma: https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4737-21344&mode=dev
    CcChoicesListComponent choices_list_component = 2;
  }
}

message CcChoicesListComponent {
  api.typesv2.common.Text title_text = 1;
  repeated api.typesv2.ui.IconTextComponent icon_text_component = 2;
}

message GetRequestStatusSyncRequest {
  frontend.header.RequestHeader req = 1;
  string card_request_id = 2;
  string workflow_id = 3;
  int32 attempt_number = 4;
}

message GetRequestStatusSyncResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
  int32 attempt_number = 5;
}

// Credit card header will contain details that will enable
// client to enrich details on their end.
// for ex: Client side events will use this for distinguishing
// funnels based on card program. Client has to cache this header and use
// handle it in a centralised manner for events.
message CreditCardHeader {
  // Card program string will be an arn string
  // denoting the card program of the user.
  string card_program = 1;
  // card program attributes will contain more details about
  // card program. For ex: Vendor,Source,ProgramType,Collateral,Origin
  // are attributes of a card program.
  map<string, string> card_program_attributes = 2;
}

message SecuredCardRewardsInfo {
  api.typesv2.common.Text reward_title = 1;
  repeated RewardsInformationCategoryBody secured_cards_reward_components = 2;
  DrawableProperties drawable_properties = 3;
}

message RecordCreditCardOnboardingConsentRequest {
  frontend.header.RequestHeader req = 1;
  frontend.firefly.enums.CreditCardOnboardingConsent credit_card_onboarding_consent = 2;
}

message RecordCreditCardOnboardingConsentResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message GetSecuredFixedDepositScreenDetailsRequest {
  frontend.header.RequestHeader req = 1;
  api.typesv2.DepositTerm deposit_term = 2;
  api.typesv2.Money deposit_amount = 3;
}

message GetSecuredFixedDepositScreenDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.ui.IconTextComponent screen_header = 2;
  FdTopSectionDetails fd_top_section_details = 3;
  FdBottomSectionDetails fd_bottom_section_details = 4;
  api.typesv2.common.ui.widget.BackgroundColour screen_bg_color = 5;
  // will be zero INR for fi lite cases
  api.typesv2.Money current_account_balance = 6;
  api.typesv2.DepositTerm selected_deposit_term = 7;
  WrappedIconTextToolBar tool_bar = 8;
  // will disable balance validations on client.
  // for ex: fi lite cases where there is no savings account
  bool disable_balance_check = 9;
}

message FdTopSectionDetails {
  api.typesv2.common.VisualElement ve_credit_limit_background = 1;
  api.typesv2.common.Text credit_limit_heading_text = 2;
  api.typesv2.common.Text credit_limit_text = 3;
}

message FdBottomSectionDetails {
  frontend.firefly.DrawableProperties bottom_section_bg_properties = 1;
  api.typesv2.common.Text fd_description_text = 2;
  api.typesv2.common.Text deposit_amount_display_text = 3;
  SliderInfo fd_slider_info = 4;
  FixedDepositDetails fd_details = 5;
  frontend.deeplink.Cta open_deposit_cta = 6;
  api.typesv2.common.Text balance_error_text = 7;
  deeplink.Cta add_funds_cta = 8;
  api.typesv2.ui.TextWithHyperlinks tnc = 9;
  api.typesv2.common.VisualElement ve_bank_logo = 10;
}

message SliderInfo {
  frontend.firefly.SliderProperties properties = 1;
  // will be sent in sorted order always from backend.
  repeated SliderElement slider_elements = 2;
  SliderElement selected_slider_element = 3;
}

message SliderElement {
  api.typesv2.Money deposit_amount = 1;
  api.typesv2.Money credit_limit = 2;
  api.typesv2.common.Text slider_element_text = 3;
  int32 amount_slider_value = 5;
}

message FixedDepositDetails {
  api.typesv2.common.Text title = 1;
  frontend.firefly.DrawableProperties bottom_section_bg_properties = 2;
  repeated FixedDepositDetailItem fixed_deposit_detail_items = 3;
  api.typesv2.ui.IconTextComponent add_nominee_cta = 4;
  message FixedDepositDetailItem {
    api.typesv2.ui.VerticalIconTextComponent vertical_icon_text_component = 1;
    repeated api.typesv2.ui.IconTextComponent icon_text_components = 2;
  }
}

message ConsentBox {
  bool is_checked = 1;
  enums.CreditCardOnboardingConsent card_onboarding_consent = 2;
}

message CollectCommunicationAddressRequest {
  frontend.header.RequestHeader req = 1;
  // oneof for address data. client will either send address type that for an already saved address on user side
  // or complete postal address to be updated in user entity
  oneof AddressData {
    api.typesv2.AddressType address_type = 2;
    api.typesv2.PostalAddress communication_address = 3;
  }
  // unique identifier for the card onboarding request
  string card_request_id = 4 [(validate.rules).string.min_len = 1];
}

message CollectCommunicationAddressResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // next action for the client
  deeplink.Deeplink next_action = 2;
}

message GetCreditCardCommunicationAddressesRequest {
  frontend.header.RequestHeader req = 1;
}

message GetCreditCardCommunicationAddressesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated AddressWithType addresses_with_type = 2;
  message AddressWithType {
    api.typesv2.AddressType type = 1;
    api.typesv2.PostalAddress address = 2;
  }
}

message GetGenericScreenRequest {
  // Request header containing metadata.
  frontend.header.RequestHeader req = 1;
  // Unique identifier for the card request.
  // Should be mapped with [Deeplink screen]
  int32 screen_id = 2;

  // Use this field to pass any additional data required for the screen
  // For example, we can pass the offer id to display the offer details
  bytes metadata = 4;
}

message GetGenericScreenRequestMetadata {
  string description = 1;
}

message GetGenericScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // tool bar to display the title and subtitle
  WrappedIconTextToolBar tool_bar = 2;
  // list of sections to be shown on the screen
  api.typesv2.ui.sdui.sections.Section sections = 3;
  // background color of screen
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 4;
}

message GetDashboardV2Request {
  frontend.header.RequestHeader req = 1;
}

// Figma : https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5716&t=NpQCwd13A6FBIgnw-4
message GetDashboardV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  CardSection card_section = 2;
  repeated DashboardSection dashboard_sections = 3;
  BottomInfoSection bottom_info_section = 4;
  // Background image which is to be rendered on the screen
  // e.g, https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=1397-5717&t=OSqjKALpG2xwABJR-4
  api.typesv2.common.VisualElement page_background = 5;

  message DashboardSection {
    oneof section {
      ContactSection contact_section = 1;
      OffersSection offers_section = 2;
      RewardsSection rewards_section = 3;
    }
  }
}
