syntax = "proto3";

package frontend.inapphelp.contact_us;

import "api/frontend/analytics/analytics_screen_name.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/inapphelp/contact_us/contact_us.proto";
import "api/frontend/inapphelp/contact_us/enums.proto";
import "api/frontend/search/widget/summary_v2.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";

option go_package = "github.com/epifi/gamma/api/frontend/inapphelp/contact_us";
option java_package = "com.github.epifi.gamma.api.frontend.inapphelp.contact_us";

service ContactUs {
  // This rpc is to be called when the user reaches the contact us landing page. This will fetch the issue lists (Trending Issues and Recent Issues) that will be shown to the user in the case the search bar is empty
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6729-10107&t=7CnImuJganWDyCbD-0
  rpc GetContactUsLandingScreen (GetContactUsLandingScreenRequest) returns (GetContactUsLandingScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // This rpc is to be called when the user edits the text in search bar on the contact us landing screen
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6729-10314&t=wM2LnsgwmDT1mLz9-0
  // TODO(smit) https://monorail.pointz.in/p/fi-app/issues/detail?id=82259
  // The rpc is to be called by the user when the user edits the search query, making sure a gap of 500ms between making the rpc call and the moment the user edits the search query
  rpc GetIssuesBasedOnPrompt (GetIssuesBasedOnPromptRequest) returns (GetIssuesBasedOnPromptResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10511&t=nt4zIX1M3XYpriMW-0
  // This rpc fetches the terminal screen details of the contact us flow
  rpc GetContactUsTerminalScreen (GetContactUsTerminalScreenRequest) returns (GetContactUsTerminalScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetHelpLandingScreen returns the details to be shown on Help landing screen
  // for now this RPC only supports the navbar ctas
  rpc GetHelpLandingScreen (GetHelpLandingScreenRequest) returns (GetHelpLandingScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14676&t=9qzkLma7WCLdgcSg-0
  // This RPC fetches the category selection screen in contact us flow
  // Request takes UserIssueIdentifier and analytics screen name as request both are mandatory
  // Response status codes:
  // Internal server error: if the server fails to serve the request
  // InvalidArgument: if the request object is invalidly
  // OK: success
  rpc GetCategorySelectionScreen (GetCategorySelectionScreenRequest) returns (GetCategorySelectionScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetNextActionForIssue RPC makes the decision whether to land user to terminal screen, or category selection screen
  // Request takes UserIssueIdentifier, which is a required field
  // Response contains a deeplink to which user should be navigated
  // in-case this RPC fails for unknown reason, the client should navigate user to category selection screen
  rpc GetNextActionForIssue (GetNextActionForIssueRequest) returns (GetNextActionForIssueResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // This RPC is called when the user selects a contact channel to contact customer care.
  // The objective of this RPC is to pass all the relevant context of user issue to the agent when they reach out.
  // Request takes the selected contact option (mandatory), available contact options, user issue identifier (mandatory) and meta data
  // if this RPC fails, the client should ignore the failure and continue with the flow, this should happen on best-effort basis
  rpc SelectContactChannel (SelectContactChannelRequest) returns (SelectContactChannelResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // This RPC fetches the content for the new Contact Us Landing Screen (V2).
  // It provides all the necessary sections and elements to render the screen.
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35748&t=4SzjTwXgOKZ9sTE9-4
  rpc GetContactUsLandingScreenV2 (GetContactUsLandingScreenV2Request) returns (GetContactUsLandingScreenV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // This RPC fetches the content for the Open Incidents Screen.
  // It provides a list of active incidents or service disruptions.
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35931&t=4SzjTwXgOKZ9sTE9-4
  rpc GetOpenIncidentsScreen (GetOpenIncidentsScreenRequest) returns (GetOpenIncidentsScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message GetContactUsLandingScreenRequest {
  frontend.header.RequestHeader req = 1;
}

message GetContactUsLandingScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // The set of lists of issues presented to the user
  // deprecated in favour of LandingScreenItems
  oneof result {
    IssueDirectory issue_directory = 2 [deprecated = true];
    ErrorState failure = 3 [deprecated = true];
  }
  // title of the screen which will be displayed on top of the search bar on the search screen
  api.typesv2.common.Text screen_title = 4;
  // title of the screen which will be displayed on top of the search bar on the landing screen
  api.typesv2.common.Text landing_screen_title = 10;
  // title of the screen which will be displayed below the screen title
  api.typesv2.common.Text screen_description = 7;
  // icon to be shown on top of the screen
  api.typesv2.common.Image banner_image = 8;
  // define the minimum number of characters a user needs to type before they can search an issue
  int64 min_query_length = 5 [deprecated = true];
  // define the maximum number of characters a user can type in the search bar
  int64 max_query_length = 6 [deprecated = true];
  // criteria on which user query should be validated
  QueryValidationConfig query_validation_config = 12;
  // screen items to be displayed in the given order
  repeated LandingScreenItems landing_screen_items = 9;
  // if this deeplink is not nil, land user on chatbot when they initiate search
  deeplink.Deeplink chatbot_deeplink = 13;
}

message LandingScreenItems {
  oneof item {
    IssueDirectory issue_directory = 1;
    ErrorState failure = 2;
    // ticket details to be shown on the landing screen
    // (optional might not be present if user doesn't have any ticket)
    TicketDetails ticket_details = 3;
    // represents a list of FAQ categories user can browse through
    FaqCategories faq_categories = 4;
    // this section will show a list of issues we have predicted that user may have
    // figma: https://www.figma.com/design/HqMQEG4wBLPvhjdq1VEj3j/%E2%98%8E%EF%B8%8F-CX-workfile?node-id=442-9442&t=IMGqbfa18tnq4Kxh-0
    PredictedIssuesSection predicted_issues = 5;
  }
}

message GetIssuesBasedOnPromptRequest {
  frontend.header.RequestHeader req = 1;
  string prompt = 2;
}

message GetIssuesBasedOnPromptResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // The list of prompts presented to the user
  oneof SearchResult {
    PromptList prompts = 2;
    ErrorState empty_state = 3;
  }
}

message GetContactUsTerminalScreenRequest {
  frontend.header.RequestHeader req = 1;
  string issue_id = 2 [deprecated = true];
  TerminalScreenParams request_params = 3;
}

message GetContactUsTerminalScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated TerminalScreenItems screen_items = 2;
  ContactUsCtaSection cta_section = 3;
  // This would serve as the title of the screen
  api.typesv2.common.Text user_query = 4;
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14593&t=9qzkLma7WCLdgcSg-0
  api.typesv2.ui.IconTextComponent category_breadcrumb = 5;
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14676&t=4TyzSsorGvzbSJa9-0
  api.typesv2.common.Text screen_title = 6;
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14406&t=9qzkLma7WCLdgcSg-0
  // define the minimum number of characters a user needs to type before they can search an issue
  int64 min_query_length = 7 [deprecated = true];
  // define the maximum number of characters a user can type in the search bar
  int64 max_query_length = 8 [deprecated = true];
  // displays the details related to the identified issue
  // For example, key can be "We identified your issue as:" and value can be "“want to issue a chequebook” "
  api.typesv2.ui.VerticalKeyValuePair issue_identification_details = 9;
  // Contains details to be shown in case user has reached the terminal screen after selecting the "my issue is not listed" option
  IssueNotListedDetail issue_not_listed_detail = 10;
  // text to be shown above the model response, for example : "No worries, here's what you can do:"
  api.typesv2.common.Text model_response_title = 11;
  // criteria on which user query should be validated
  QueryValidationConfig query_validation_config = 12;
  // feedback component to be shown at the bottom of model response in the terminal screen
  FeedbackComponent feedback_component = 13;
  // time delay in second after which the contact us channel is to be displayed
  int64 animation_delay = 14;
}

message TerminalScreenItems {
  oneof screen_items {
    // The ticket details section
    // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10390&t=nt4zIX1M3XYpriMW-0
    TicketDetailsSection ticket_details_section = 1;
    // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10511&t=nt4zIX1M3XYpriMW-0
    api.typesv2.ui.sdui.sections.Section result = 2;
    // The more results section we show at the terminal screen
    // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10390&t=nt4zIX1M3XYpriMW-0
    PromptList more_results = 3;
    // Ex: "My issue is not listed here"
    // https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10390&t=nt4zIX1M3XYpriMW-0
    api.typesv2.ui.IconTextComponent cta = 4;
    // summary of ask fi response for the user query
    search.widget.SummaryV2 ask_fi_response_summary = 5;
    // terminal screen redirection to get help component
    // figma -> https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5760-10137&p=f&t=Ei9BiFl9tKlE10L9-0
    InAppHelpRedirectionSection in_app_help_redirection_section = 6;
  }
}

message GetHelpLandingScreenRequest {
  frontend.header.RequestHeader req = 1;
}

message GetHelpLandingScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  HelpMainNavBar nav_bar = 2;
}

message GetCategorySelectionScreenRequest {
  frontend.header.RequestHeader req = 1;
  // issue identifier helps identify the issue for which we need to perform category selection
  UserIssueIdentifier issue_identifier = 2;
  // identifier for screen so BE can take decision whether to show top options only or whole list
  // in category screen we will show top options, in view more bottom sheet we provide the whole list
  analytics.AnalyticsScreenName screen_name = 3;
  // denotes if the user is coming to category selection screen after tapping
  // the "my issue is not listed" CTA
  bool is_issue_not_listed = 4;
}

message GetCategorySelectionScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  oneof response {
    CategorySelectionScreen category_selection_screen = 2;
    deeplink.Deeplink deeplink = 3;
  }
}

message GetNextActionForIssueRequest {
  frontend.header.RequestHeader req = 1;
  UserIssueIdentifier user_issue_identifier = 2;
}

message GetNextActionForIssueResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_screen = 2;
}

message SelectContactChannelRequest {
  frontend.header.RequestHeader req = 1;
  ContactOption selected_contact_option = 2;
  repeated ContactOption available_contact_options = 3;
  string user_issue_identifier = 4;
  // meta-data can be used to send any additional information, which will help our agent resolve user issue
  map<string, string> meta_data = 5;
}

message SelectContactChannelResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

// ContactUsButtonOnClickActionPayload is a custom proto message used to send
// all necessary payload objects to the client for the Contact Us button's on-click behavior.
// The client will handle parsing the payload and taking the required action.
message ContactUsButtonOnClickActionPayload {
  // The deeplink to be opened when the contact us button is clicked.
  frontend.deeplink.Deeplink deeplink = 1;

  // Request object to be used for the SelectContactChannel RPC call.
  // Note: The frontend request header will not be populated by this message;
  // clients must populate the header before making the request.
  SelectContactChannelRequest select_contact_channel_request = 2;
}

// Request message for the GetContactUsLandingScreenV2 RPC.
message GetContactUsLandingScreenV2Request {
  frontend.header.RequestHeader req = 1;
}

// Response message for the GetContactUsLandingScreenV2 RPC.
// Contains all the sections and elements for the Contact Us V2 landing page.
message GetContactUsLandingScreenV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  // The main top section of the page including banners, toolbar, and title details.
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35765&t=CgwZtdcbPv8ocESk-4
  PageTopSection page_top_section = 2;
  // A list of help sections to be displayed on the landing page, like incidents or FAQs.
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35748 (Refers to sections like Incident & FAQs within the main page)
  repeated HelpSections help_sections = 3;
  // A floating icon, typically a CTA like "Chat With Us".
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35748 (Floating chat icon at the bottom)
  FloatingIcon floating_icon = 4;
}

// Request message for GetOpenIncidentsScreen RPC.
message GetOpenIncidentsScreenRequest {
  frontend.header.RequestHeader req = 1;
}

// Response message for GetOpenIncidentsScreen RPC.
// Contains the content to display on the Open Incidents screen.
// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35931&t=4SzjTwXgOKZ9sTE9-4
message GetOpenIncidentsScreenResponse {
  // Standard response header.
  frontend.header.ResponseHeader resp_header = 1;
  // screen details for this screen eg: toolbar, widgets, page background color etc;
  IncidentScreenDetails screen_details = 2;
}



