syntax = "proto3";

package frontend.inapphelp.contact_us;

import "api/frontend/cx/ticket/ticket.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/inapphelp/faq/faq.proto";
import "api/frontend/home/<USER>";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "api/typesv2/ui/sdui/components/spacer.proto";

option go_package = "github.com/epifi/gamma/api/frontend/inapphelp/contact_us";
option java_package = "com.github.epifi.gamma.api.frontend.inapphelp.contact_us";

// The top issues section of the contact us landing screen https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10088&t=oNJpcBHlPox5Cb7v-0
message IssuesList {
  // The title of the top issues section
  // deprecated in favour of title_v2
  api.typesv2.common.Text title = 1 [deprecated = true];
  // List of possible issues faced by the customer
  repeated Issue issues = 2;
  // The title of the top issues section
  api.typesv2.ui.IconTextComponent title_v2 = 3;
}

// These are the issues shown to a user under different section
// This can include issues from sections like recent issues,predicted issues ,trending issues
message Issue {
  // user-friendly issue title that will be shown
  // Eg: Issue with your payment?, Account is on Freeze?
  api.typesv2.common.Text description = 1;
  // redirection deeplink which will take user to next flow when user chooses an issue
  // example: for txn related issues we can redirect user to all transactions to let user select a txn
  frontend.deeplink.Deeplink deeplink = 2;
  // unique identifier that can be used to map an issue. May not be applicable for all sections implementing it
  string issue_id = 3;
  // icon representing the redirection option
  api.typesv2.common.Image trailing_icon = 4;
}

// The suggestions shown to the user when the user enters into the text field
message Prompt {
  api.typesv2.ui.IconTextComponent prompt = 1;
  api.typesv2.common.Image trailing_icon = 2;
  string prompt_id = 3;
  api.typesv2.common.Image leading_icon = 4;
}

message PromptList {
  repeated Prompt prompts = 1;
  string title = 2 [deprecated = true];
  api.typesv2.common.Text title_v2 = 3;
}

// figma -> https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5760-10137&p=f&t=Ei9BiFl9tKlE10L9-0
message InAppHelpRedirectionSection {
  api.typesv2.common.VisualElement search_image = 1;
  api.typesv2.ui.IconTextComponent title = 2;
  api.typesv2.ui.IconTextComponent subtitle = 3;
  frontend.deeplink.Deeplink deeplink = 4;
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 5;
}

message IssueNotListedDetail {
  // standard text that will be shown to the user after tapping the "my issue is not listed here" option
  api.typesv2.common.Text response_text = 1;
  // background color of the container of the standard text
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;
}

// This is the screen to be shown when there are no results for the search query
// https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10149&t=oNJpcBHlPox5Cb7v-0
message ErrorState {
  api.typesv2.common.Image icon = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text subtitle = 3;
  IssuesList possible_issues = 4;
}

// The contact us CTA section serves as the footer in Contact Us screens
// https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-7818&t=PtnHNmfm5M1k6omi-0
message ContactUsCtaSection {
  api.typesv2.ui.sdui.sections.Section result = 2;
}

// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=6778-10390&t=nt4zIX1M3XYpriMW-0
message TicketDetailsSection {
  frontend.cx.ticket.TicketDetailsForBanner ticket_details = 1;
  api.typesv2.common.Text title = 2;
}

message IssueDirectory {
  repeated IssuesList issue_directory = 1;
}

message TerminalScreenParams {
  oneof request {
    string query_string = 1;
    string issue_id = 2;
  }
}

message HelpMainNavBar {
  // represents buttons (CTA) to be shown on the nav bar
  repeated api.typesv2.ui.IconTextComponent icon_text_components = 1;
}

message CategorySelectionList {
  // deprecated in favour of menu_heading
  api.typesv2.common.Text title = 1 [deprecated = true];
  repeated Option option_list = 2;
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14676&t=FsqgsDlfNkLSN4ch-0
  // populated with deeplink of a bottom sheet in-case there are more options to be shown to user
  api.typesv2.ui.IconTextComponent view_more = 3;
  // heading of the menu : for example, "Help us understand your issue better"
  // refer figma : https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=9117-20501&node-type=FRAME&t=j8gjdiwC6j4K0Awf-0
  // deprecated in favour of menu_heading_v2
  api.typesv2.ui.IconTextComponent menu_heading = 4 [deprecated = true];
  // title of the screen as per new design
  api.typesv2.ui.VerticalKeyValuePair menu_heading_v2 = 5;
}

message Option {
  // unique identifier for each option, also this id needs to be sent in the next RPC call if an option is selected
  string issue_id = 1;
  api.typesv2.common.Text display_value = 2;
  // boolean sent by BE in-case we need to pre-populate an option (only one option will have this flag set to true)
  // the pre-populated option will always be the first option in the list
  bool is_selected = 3;
  // text values that will be used to perform matching with what user is searching
  repeated string search_terms = 4;
}

message SelectedIssueDetail {
  // details of the selected issue, for example : "Category" : "Instant loans"
  api.typesv2.ui.IconTextComponent issue_detail = 1;
}

message IssueDescription {
  // details of the selected issue, for example : product category, product category details
  repeated SelectedIssueDetail selected_issue_details = 1;
}

message CategorySelectionScreen {
  api.typesv2.common.Text screen_title = 1;
  api.typesv2.common.Text user_query = 2;
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14593&t=9qzkLma7WCLdgcSg-0
  api.typesv2.ui.IconTextComponent category_breadcrumb = 3;
  CategorySelectionList category_selection_list = 4;
  // Ex: "My issue is not listed here"
  // https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14593&t=9qzkLma7WCLdgcSg-0
  api.typesv2.ui.IconTextComponent fallback_flow_cta = 5;
  // Figma Ref: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=7870-14593&t=9qzkLma7WCLdgcSg-0
  // this CTA will always be enabled, because by default BE will pre-select the first option if there is nothing selected yet by user
  deeplink.Cta next_cta = 6;
  // define the minimum number of characters a user needs to type before they can search an issue
  int64 min_query_length = 7 [deprecated = true];
  // define the maximum number of characters a user can type in the search bar
  int64 max_query_length = 8 [deprecated = true];
  // description of the issue : for example category and details
  IssueDescription issue_description = 9;
  // criteria on which user query should be validated
  QueryValidationConfig query_validation_config = 10;
}

message UserIssueIdentifier {
  oneof identifier {
    // populated with user typed text if user has performed custom search
    string query_string = 1;
    // populated if user has selected an already existing issue (recent issue, trending issue, or search suggestion)
    string issue_id = 2;
  }
}

message QueryValidationConfig {
  // the minimum number of characters a user needs to type before they can search an issue
  int64 min_character_count = 1;
  // the maximum number of characters a user can type in the search bar
  int64 max_character_count = 2;
  // the minimum number of words a user needs to type before they can search an issue
  int64 min_word_count = 3;
  // message to be displayed to user on app if a user query doesn't meet the criteria
  api.typesv2.common.Text validation_failure_msg = 4;
}

message FeedbackComponent {
  // whether to show the feedback component or not
  bool is_required = 1;
  // title to be displayed (i.e., Did that Help?)
  api.typesv2.common.Text title = 2;
}

message TicketDetails {
  api.typesv2.common.Text title = 1;
  cx.ticket.TicketDetailsForBanner details = 2;
  // ITC will redirect user to ticket list screen
  api.typesv2.ui.IconTextComponent view_all = 3;
}

message FaqCategories {
  api.typesv2.common.Text title = 1;
  repeated faq.Category categories = 2;
  // ITC will redirect user to all category screen
  api.typesv2.ui.IconTextComponent view_more = 3;
}

// List of predicted issues for the user
// Figma -> https://www.figma.com/design/HqMQEG4wBLPvhjdq1VEj3j/%E2%98%8E%EF%B8%8F-CX-workfile?node-id=442-9453&t=jMp2b46nqjD8jy7x-4
message PredictedIssuesSection {
  // title of the widget Eg: Is your issue one of these?
  api.typesv2.common.Text title = 1;
  // List of different issues that were predicted for user
  repeated Issue predicted_issue = 2;
}

// Figma -> https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35765&t=CgwZtdcbPv8ocESk-4
message PageTopSection {
  // Toolbar configuration for the top of the screen.
  // Typically includes back navigation and action icons.
  ToolBar tool_bar = 2;
  // Contains the main visual and textual elements of the top hero section.
  TopSectionDetails top_section_details = 3;
  // Background image for the entire top hero section.
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35765 (Background of top section)
  api.typesv2.common.VisualElement top_section_bg_image = 4;
  // List of incident widgets to display prominently.
  // E.g., Ongoing system maintenance notifications.
  repeated IncidentWidgetSection incident_section = 5;
  // Optional "See more" CTA, potentially for incident_section if there are many.
  // Example: "View all updates".
  api.typesv2.ui.IconTextComponent see_more = 6;
}

// Figma -> https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35841&t=CgwZtdcbPv8ocESk-4
message ToolBar {
  // Icon on the left of the toolbar.
  // Example: A back arrow icon for navigation.
  api.typesv2.common.VisualElement left_icon = 1;
  // title of the current screen
  api.typesv2.common.Text title = 2;
  // List of icons on the right of the toolbar.
  // Example: Search icon, notification bell.
  repeated api.typesv2.ui.IconTextComponent right_icons = 3;
}

// Figma -> https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35850&t=CgwZtdcbPv8ocESk-4
message TopSectionDetails {
  // Main branding image in the top section.
  // Example: "Fi Care" logo.
  api.typesv2.common.VisualElement fi_care_image = 1;
  // Main title text of the help screen.
  // Example: "Hello, How can we help?".
  api.typesv2.ui.IconTextComponent title = 2;
  // Subtitle text providing a brief welcome or instruction.
  // Example: "Find answers to your questions or contact support."
  api.typesv2.ui.IconTextComponent subtitle = 3;
}

// List of page sections in the contactUsLandingV2 screen
// Defines the various sections that make up the Contact Us v2 landing page.
message HelpSections {
  // figma -> https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28132-12690&t=Px1PkwrvyU23MOeA-4
  // [Optional] spacing to be rendered on top of this Home
  api.typesv2.ui.sdui.components.Spacer top_spacer = 1;
  oneof Section{
    // A section displaying information about active incidents or outages.
    // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35769&t=CgwZtdcbPv8ocESk-4
    IncidentWidgetSection incident_widget_section = 5;
    // A section displaying Frequently Asked Questions.
    // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35790&t=CgwZtdcbPv8ocESk-4
    FAQWidgetSection faq_widget_section = 6;
  }
}

// Figma -> https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35769&t=CgwZtdcbPv8ocESk-4
// Represents a section displaying incident widgets in Contact Us v2.
// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35858&t=CgwZtdcbPv8ocESk-4
message IncidentWidgetSection {
  // The top part of the incident widget, usually containing title and icon.
  TopSection top_section = 1;
  // The middle part of the incident widget, for brief updates or status.
  MiddleSection middle_section = 2;
  // The bottom part of the incident widget, for detailed descriptions and CTAs.
  BottomSection bottom_section = 3;
  // Background color for this part of the widget.
  api.typesv2.common.ui.widget.BackgroundColour border_color = 4;
  // map which carries all the event properties related to this widget
  map<string,string> event_properties = 5;

  message TopSection {
    // Icon or image for the incident.
    // Example: A warning triangle for a service issue.
    api.typesv2.common.VisualElement left_image = 1;
    // Title and optional tag for the incident.
    // Example Title: "System Maintenance", Example Tag: "Ongoing".
    api.typesv2.ui.VerticalKeyValuePair title_tag = 2;
    // IconTextComponent on the right, could be a timestamp or status indicator.
    // Example: "Last updated: 5 mins ago".
    api.typesv2.ui.IconTextComponent right_itc = 3;
    // Background color for this part of the widget.
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;
  }

  message MiddleSection {
    // Brief update or status text for the incident.
    // Example: "Login services are temporarily unavailable."
    api.typesv2.ui.IconTextComponent middle_itc = 3;
    // Background color for this part of the widget.
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;
  }

  message BottomSection {
    // Detailed message about the incident.
    // Example: "We are working to resolve this issue and expect services to be restored by 2 PM."
    api.typesv2.common.Text description = 1;
    // Primary Call-to-Action for the incident.
    // Example: "View ticket.
    api.typesv2.ui.IconTextComponent primary_cta = 2;
    // Secondary Call-to-Action for the incident.
    // Example: "chat with expert"
    api.typesv2.ui.IconTextComponent secondary_cta = 3;
    // Background color for this part of the widget.
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;
  }
}

// Figma -> https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35790&t=CgwZtdcbPv8ocESk-4
// Represents a section displaying FAQ widgets in Contact Us v2.
// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35886&t=CgwZtdcbPv8ocESk-4
message FAQWidgetSection {
  // Title for the FAQ section.
  // Example: "Frequently Asked Questions".
  api.typesv2.common.Text title = 1;
  // list of the faq items to be shown in this widget.
  repeated FAQListItem faq_items = 2;
  // border color of the widget
  api.typesv2.common.ui.widget.BackgroundColour border_color = 3;
  // map which carries all the event properties related to this widget
  map<string,string> event_properties = 4;

  message FAQListItem {
    oneof Item {
      // An individual FAQ item, containing a question and navigation to its answer.
      FAQItem faq_item = 1;
      // A list of FAQ categories, for browsing.
      CategoryListItem category_list_item = 2;
    }

    message FAQItem {
      // Icon for the FAQ item, typically a question mark or relevant symbol.
      api.typesv2.common.VisualElement left_icon = 1;
      // The FAQ question text itself.
      // Example: "How do I reset my password?".
      api.typesv2.common.Text faq_title = 2;
      // Icon on the right, typically a chevron to indicate it's clickable.
      api.typesv2.common.VisualElement right_icon = 3;
      // Deeplink to navigate to the detailed answer for this FAQ.
      frontend.deeplink.Deeplink deeplink = 4;
    }

    message CategoryListItem {
      // List of FAQ categories to display.
      // Example Categories: "Account Management", "Payments", "Security".
      repeated faq.Category categories = 1;
    }
  }
}

// Figma -> https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35748 (Floating chat icon at the bottom)
message FloatingIcon {
  // Configuration for the floating icon, likely a CTA button like "Chat with us".
  // This uses the generic home.Icon definition, allowing for flexible icon and action setup.
  // Example: A chat bubble icon that opens a support chat window.
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-35748 (Floating chat icon at the bottom right)
  home.Icon icon = 1;
}

message IncidentScreenDetails {
  // Optional toolbar for the screen, for navigation or actions.
  ToolBar tool_bar = 1;
  // Background colour of the screen
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 2;
  IncidentsListWidget incidents = 3;
  // Optional message to display when there are no open incidents.
  // Example: "All systems normal." or "No active incidents to report currently."
  api.typesv2.common.Text no_incidents_message = 5;
}

message IncidentsListWidget {
  // Title for the open incidents screen.
  // Example: "Stay Updated" or "Active Issues".
  api.typesv2.common.Text incidents_title = 3;
  // Subtitle or description for the screen.
  // Example: "Here are the current issues we are tracking."
  repeated IncidentWidgetSection incident_widget_sections = 4;
}
