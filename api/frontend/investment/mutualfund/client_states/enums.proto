syntax = "proto3";

package frontend.investment.mutualfund.clientstates;

option go_package = "github.com/epifi/gamma/api/frontend/investment/mutualfund/clientstates";
option java_package = "com.github.epifi.gamma.api.frontend.investment.mutualfund.clientstates";

enum MutualFundListEntryPoint {
  MF_LIST_ENTRY_POINT_UNSPECIFIED = 0;
  // we might have scenarios where we do not want to show certain funds for FIT. Same for other EP
  MF_LIST_ENTRY_POINT_FIT = 1;
  // Entry point from an investment tile in MF Summary pull down tab
  MF_LIST_ENTRY_POINT_DIGEST = 2 [deprecated=true];
  // Entry point to Mutual Fund Invested details from an activated Fit Rule details screen
  MF_LIST_ENTRY_POINT_FIT_ACTIVE_RULE = 3;
  // Entry point from search suggestions
  MF_LIST_ENTRY_POINT_SEARCH_SUGGESTIONS = 4;
  // Entry point from home
  MF_LIST_ENTRY_POINT_HOME_LANDING = 5;
  // Entry point from mutual funds landing page
  MF_LIST_ENTRY_POINT_MF_LANDING = 6;
  // Entry point from investment landing page
  MF_LIST_ENTRY_POINT_Investment_LANDING = 7;
  // Entry point from investment profile dashboard
  MF_LIST_ENTRY_POINT_INVESTMENT_PROFILE_DASHBOARD = 8;
  // Entry point from money secrets
  MF_LIST_ENTRY_POINT_MONEY_SECRETS = 9;
}

enum CollectionType {
  COLLECTION_TYPE_UNSPECIFIED = 0;
  // MF_WATCH_LIST is for watchlist page
  MF_WATCH_LIST = 1;
  // MF_RECENT is for recent page
  MF_RECENT = 2;
  // ADVANCED_FILTERS
  //  - with collection id is for funds for filters page with a collection selected
  //  - with filters id is for funds for filters page with filter ids selected
  ADVANCED_FILTERS = 3;
  // MF_COLLECTION is for collections landing screen
  // if no collection id is passed default collection will be fetched
  // if collection id is passed a particular collection will be selected
  MF_COLLECTION = 4;
  // ALL_FUNDS is for all funds page
  // no collection id will be present
  ALL_FUNDS = 5;
}

enum FilterScreen {
  FILTER_SCREEN_UNSPECIFIED = 0;
  // To be used for screens when no customisation is needed.
  // Eg: collection home, search, specific collection screen where no customization etc.
  FILTER_SCREEN_DEFAULT = 1;
  // To be used when called from Fittt flow
  // We need to disable preset filters when accessing filters from Fittt flow
  FILTER_SCREEN_FITTT = 2;
}
