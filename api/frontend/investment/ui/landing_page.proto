syntax = "proto3";

package frontend.investment.ui;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/investment/ui/investment_activity.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";

option go_package = "github.com/epifi/gamma/api/frontend/investment/ui";
option java_package = "com.github.epifi.gamma.api.frontend.investment.ui";

message LandingPageRequestParams {
  // string value of 'api.typesv2.InvestmentInstrumentType' enum
  string instrument_type = 1;
  // string value of 'LandingToggleValue' enum
  // empty string and 'unspecified' to be treated as default
  string toggle_value = 2;
  // string value of 'BottomComponentTabValue' enum
  // empty string and 'unspecified' to be treated as default
  string bottom_component_tab_value = 3;
}

enum LandingToggleValue {
  LANDING_TOGGLE_VALUE_UNSPECIFIED = 0;
  LANDING_TOGGLE_VALUE_FI = 1;
  LANDING_TOGGLE_VALUE_ALL = 2;
  LANDING_TOGGLE_VALUE_RUPEE = 3;
  LANDING_TOGGLE_VALUE_DOLLAR = 4;
  LANDING_TOGGLE_VALUE_TRACK = 5;
  LANDING_TOGGLE_VALUE_HIDE = 6;
}

enum BottomComponentTabValue {
  BOTTOM_COMPONENT_TAB_VALUE_UNSPECIFIED = 0;
}

message AssetLandingPageTopComponent {
  oneof component {
    // Figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7756-1789&t=G29TgFA3ZHCjSKJC-4
    // SS: https://drive.google.com/file/d/1_OuTQrk-Y6HSSexhGx2jEGVuEmMdXknT/view?usp=drive_link
    ZeroStatePromotionalComponent promo_component = 1;
    // Figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7954-49699&t=G29TgFA3ZHCjSKJC-4
    // SS: https://drive.google.com/file/d/1iI2JW6qqhxSXa-vsqUK9ZVq1SU7zTCo_/view?usp=drive_link
    AssetInvestmentSummaryCard summary_card = 2;
    // Figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7954-49698&t=G29TgFA3ZHCjSKJC-4
    // SS: https://drive.google.com/file/d/1wwPHHik1tY66Acv3fhEV3y7SNhMvvdqV/view?usp=drive_link
    AssetLandingCTAs ctas = 3;
    // Currently only seen in USS zero state
    // Figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7756-1794&t=G29TgFA3ZHCjSKJC-4
    api.typesv2.ui.IconTextComponent compliance_info = 4;
  }
}
// figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7756-4976&t=Wj2bIQWtVkFUKYcg-4
// drive: https://drive.google.com/file/d/1WLOydoInHOZ7duG4Iu7ZswNS7zH_JcGl/view?usp=sharing
message AssetLandingPageTabComponent {
  repeated LandingPageTab tabs = 1;
  // represent bg color during tab selected by user
  string select_bg_color = 2;
  // represent bg color for tab unselected by user
  string bg_color = 3;
}

message LandingPageTab {
  // string value of 'BottomComponentTabValue' enum
  // empty string and 'unspecified' to be treated as default
  string bottom_component_tab_value = 1;
  api.typesv2.common.VisualElement icon = 2;
  // text to be show if tab is unselected
  api.typesv2.common.Text text = 3;
  // text to be show if tab is  selected
  api.typesv2.common.Text selected_text = 4;
  // if the tab is selected or not
  bool is_selected = 5;
}

message BottomComponent {
  oneof component {
    // figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7756-9989&t=akFjUrU8tvT2vpnC-4
    // drive: https://drive.google.com/file/d/1sW4b0F3LnqSnuu0jFo1Z1oMLvwEMKxVr/view?usp=sharing
    frontend.investment.ui.AssetActivityGroup activity_group = 1;
    // Figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=9163-12925&t=UPboFeIAhnGYizDI-4
    // SS: https://drive.google.com/file/d/1SMcDnX2MKhgCSEepjkHCnl3jHYeMlMN7/view?usp=sharing
    ZeroStateBottomComponent zero_state_bottom_component = 2;
  }
}

// Figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=9163-12925&t=UPboFeIAhnGYizDI-4
// SS: https://drive.google.com/file/d/1SMcDnX2MKhgCSEepjkHCnl3jHYeMlMN7/view?usp=sharing
message ZeroStateBottomComponent {
  // This is the title for the component e.g
  //  Add Bonds to your networth
  api.typesv2.ui.IconTextComponent text = 1;
  // image to show in zero state
  api.typesv2.common.VisualElement image = 2;
  // bgColor for the zero component
  string bg_color = 3;
}

// Figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7756-1789&t=G29TgFA3ZHCjSKJC-4
// SS: https://drive.google.com/file/d/1_OuTQrk-Y6HSSexhGx2jEGVuEmMdXknT/view?usp=drive_link
message ZeroStatePromotionalComponent {
  // This is the title for the component e.g
  // Build your wealth with World's best brands
  api.typesv2.common.Text title = 1;
  // This is the lottie or image where we can show an asset icon and a platform below it
  api.typesv2.common.VisualElement flip_component = 2;
  // This is to show the benefits or rewards
  api.typesv2.common.VisualElement bottom_component = 3;
  // this is the radial gradient shown behind the view
  api.typesv2.common.ui.widget.BackgroundColour radial_gradient = 4;
  // This is the deeplink to open some screen when tapped on bottom component
  // Will be useful in case of rewards state where we want to land user to different screen
  deeplink.Deeplink bottom_component_deeplink = 5;
}

// Figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7954-49698&t=G29TgFA3ZHCjSKJC-4
// SS: https://drive.google.com/file/d/1wwPHHik1tY66Acv3fhEV3y7SNhMvvdqV/view?usp=drive_link
message AssetLandingCTAs {
  // Client should handle following cases and corresponding sizing according to the figma
  // - left, right and more options cta
  // - left/right cta + more options cta
  // - left/right cta
  // - no ctas
  // In case there is no deeplink
  AssetLandingCTA left_cta = 1;
  AssetLandingCTA right_cta = 2;
  // figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=8497-37841&t=DUICfdevQyIZn5Tm-4
  // drive: https://drive.google.com/file/d/1Ev2kVkT0vFThpBtWJAzC-NcTHkowWcDC/view?usp=sharing
  message MoreOptionsCTA {
    api.typesv2.common.VisualElement icon = 1;
    string bg_color = 2;
    int32 corner_radius = 3;
    // on clicking cta MoreOptions bottom sheet is displayed
    MoreOptionsBottomSheet more_options = 4;
  }

  // if user clicks on this CTA
  // then  MoreOptionsBottomSheet is opened by client
  MoreOptionsCTA more_options_cta = 4;
}

// figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7756-1264&t=Wj2bIQWtVkFUKYcg-4
// drive: https://drive.google.com/file/d/1br60b1arKb9W_TDCgLXZ4Kqwh3si5StI/view?usp=sharing
message AssetLandingCTA {
  api.typesv2.ui.IconTextComponent cta = 1;
  // extra tag for populating for above design
  api.typesv2.ui.IconTextComponent tag = 2;
}

// Figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7994-50679&t=G29TgFA3ZHCjSKJC-4
// SS: https://drive.google.com/file/d/18KWdqN6FW8zwDAwuMI5Xk1Tp5DDrtmAE/view?usp=drive_link
message MoreOptionsBottomSheet {
  // More Options
  api.typesv2.common.Text title = 1;
  message OptionList {
    api.typesv2.common.VisualElement left_icon = 1;
    api.typesv2.common.Text title = 2;
    api.typesv2.common.Text sub_title = 3;
    frontend.deeplink.Deeplink deeplink = 4;
  }
  // list of options
  repeated OptionList options = 2;
}

// Figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7954-49699&t=G29TgFA3ZHCjSKJC-4
// SS: https://drive.google.com/file/d/1iI2JW6qqhxSXa-vsqUK9ZVq1SU7zTCo_/view?usp=drive_link
message AssetInvestmentSummaryCard {
  // Client should handle all following cases and corresponding separators according to the figma
  // - All 4 values
  // - Three values include 'top_centre' value
  // - 'top_centre' value + either left/right bottom value
  // - Only 'top_centre' value
  // Rest of cases should not occur and we would be adding validations for the same
  api.typesv2.ui.VerticalKeyValuePair top_center_value = 1;
  api.typesv2.ui.VerticalKeyValuePair left_bottom_value = 2;
  api.typesv2.ui.VerticalKeyValuePair centre_bottom_value = 3;
  api.typesv2.ui.VerticalKeyValuePair right_bottom_value = 4;
  // Color of the card
  string card_bg_color = 5;
  // Color of the divider line
  string divider_color = 6;
  // Bottom banner attached to the above card
  // The border/corner radius for this ITC should be extended from the above card
  // If empty, should be handled accordingly
  api.typesv2.ui.IconTextComponent attached_banner = 7;
}
// figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7756-12934&t=akFjUrU8tvT2vpnC-4
// drive: https://drive.google.com/file/d/1Q96PeNQelItI-cZ2z_jqGKuS7BwEZ0xD/view?usp=sharing
message AssetLandingToggleComponent {
  AssetLandingToggleValue toggle_value_1 = 1;
  AssetLandingToggleValue toggle_value_2 = 2;
  // represent bg color for toggle selected by user
  string select_bg_color = 3;
  // represent bg color for toggle unselected by user
  string bg_color = 4;
}

message AssetLandingToggleValue {
  // string value of 'LandingToggleValue' enum
  // empty string and 'unspecified' to be treated as default
  string toggle_value = 1;
  // text if toggle is not selected
  api.typesv2.common.Text text = 2;
  // text if toggle is selected
  api.typesv2.common.Text selected_text = 3;
  // if the toggle is selected or not
  bool is_selected = 4;
}
