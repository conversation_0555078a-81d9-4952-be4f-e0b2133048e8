syntax = "proto3";

package frontend.investment.aggregator;

option go_package = "github.com/epifi/gamma/api/frontend/investment/aggregator";
option java_package = "com.github.epifi.gamma.api.frontend.investment.aggregator";


enum InstrumentType {
  INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED = 0;
  MUTUAL_FUNDS = 1;
  P2P_JUMP = 2;
  SMART_DEPOSIT = 3;
  FIXED_DEPOSIT = 4;
  US_STOCKS = 5;
  INDIAN_STOCKS = 6;
  US_FUNDS = 7;
}
