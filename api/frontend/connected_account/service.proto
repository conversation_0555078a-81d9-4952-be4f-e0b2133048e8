// protolint:disable MAX_LINE_LENGTH

// RPCs related to connected_account FE service .
// Ref material - https://www.onemoney.in/docs/api/

syntax = "proto3";

package frontend.connected_account;

import "api/accounts/account_type.proto";
import "api/frontend/connected_account/account.proto";
import "api/frontend/connected_account/common/aa.proto";
import "api/frontend/connected_account/common/enums.proto";
import "api/frontend/connected_account/entry_point.proto";
import "api/frontend/connected_account/features/consent_renewal.proto";
import "api/frontend/connected_account/screens/benefit_screen.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/home/<USER>";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/aa.proto";
import "api/typesv2/analyser/consent_screen.proto";
import "api/typesv2/bank.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/date.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "google/protobuf/duration.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/connected_account";
option java_package = "com.github.epifi.gamma.api.frontend.connected_account";

/*
Frontend APIs for connected accounts service
All the account discovery, account linking flows reside in the vendor SDK integrated in the APP
Backend owns the consent management and data management process for the linked accounts
*/
service ConnectedAccount {
  // When a user goes through account linking/discovery process in the APP, client will initiate a consent request
  // for a user. Backend will call vendor API and pass relevant consent parameters and generate a consent handle.
  // In case consent handle generation fails, client is expected to retry this API
  // In case consent handle was generated successfully, it is returned to the client
  // This is NOT AN IDEMPOTENT API and will generate a new consent handle every time this is called
  // In future when we decide to allow editing consent params by the user, request needs to accommodate
  // those parameters as well so backend can use the one selected by user rather than using default
  rpc InitiateConsent (InitiateConsentRequest) returns (InitiateConsentResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch parameters of consent request
  // Backend stores default consent parameters since we are NOT giving users the flexibility to edit these
  // We do show them on the UI hence this API is called on the consent screen where user is shown what params are present
  rpc FetchConsentParams (FetchConsentParamsRequest) returns (FetchConsentParamsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch Financial information accounts that have been synced via AA for a given user
  // A user can have multiple active consents and each consent can have multiple accounts associated with it
  // We fetch all the active consents for the user and calculate the list of active accounts from these and return
  // to the client. This API only returns accounts which are part of active consent at epiFi and does not mean
  // that all the LINKED accounts with AA will be returned. To fetch all the linked accounts vendor SDK already has
  // those methods/apis exposed. Client will call vendor SDK to get linked accounts and then this API to get accounts
  // linked to active consents and then show users only the accounts which are not part of any consent to start the
  // consent initiation/account linking process.
  // This API will also be used at other places in the APP like profile -> Account Manager to show list of ACTIVE linked
  // accounts of the user. NOTE : The api returns account_id only if the data was successfully persisted for the account
  // In case account_id is not returned in response cannot call the GetAccountDetails API and need to handle this
  // gracefully telling user that data is still being synced.
  // DEPRECATED IN FAVOUR OF GetConnectedAccounts
  rpc GetLinkedAaAccounts (GetLinkedAaAccountsRequest) returns (GetLinkedAaAccountsResponse) {
    option (rpc.auth_required) = true;
    option deprecated = true;
  }

  // RPC to fetch account details for a particular account as fetched from Account aggregator
  // This can be a combination of profile and summary of the account which AA returns. List of fields available
  // in these : https://specifications.rebit.org.in/api_schema/account_aggregator/documentation/deposit.html
  // Check other FI types here : https://api.rebit.org.in/schema
  // Fields in response are added as per : https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/Workfile-%E2%80%A2-412-Connected-Accounts?node-id=38%3A6982
  rpc GetAccountDetails (GetAccountDetailsRequest) returns (GetAccountDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch all connected accounts of a user based on account status
  // Client can pass list of statuses of account to fetch the type of accounts needed on different screens
  // Account states can be used to control the options to be shown on UI
  rpc GetConnectedAccounts (GetConnectedAccountsRequest) returns (GetConnectedAccountsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch list of related accounts for an account which is to be disconnected.
  // An account can be related to other accounts if they are part of the same consent.
  // Any activity done on consent will directly affect all the accounts part of that consent hence
  // client needs to show users pop warning that taking action XYZ for this account will lead to change in following
  // related accounts as well. we return an account in related list for disconnection if the accounts are part of
  // same consent and and their is no other active consent for the related account
  rpc GetRelatedAccountsForDisconnect (GetRelatedAccountsForDisconnectRequest) returns (GetRelatedAccountsForDisconnectResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to delete the account completely for the user from epiFi
  // Account deletion will be an sync process in backend and will
  // take some time to reflect. Delete will lead to deleting all related accounts with this account
  // hence client needs to call the GetRelatedAccountsForDelete RPC first and inform the user the list
  // of accounts which will be deleted on taking this action.
  rpc DeleteAccount (DeleteAccountRequest) returns (DeleteAccountResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to check if a user went through re oobe and is coming to connect new accounts
  // In this case the vua of the user changes and hence we need to raise a flag to user to delete
  // all their existing data with previous phone number if they have done reoobe
  // Will return list of accounts for the user in case of reoobe
  // which need to be deleted before they can proceed further
  // RPC status supported
  // OK : success
  // INTERNAL : some error in determining reoobe check
  // DEPRECATED : Call GetLandingPageOnConnect and that should return appropriate deeplinks
  rpc CheckReoobe (CheckReoobeRequest) returns (CheckReoobeResponse) {
    option (rpc.auth_required) = true;
    option deprecated = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to handle user data in case they have done reoobe. We will delete all the accounts and consents for the user
  // linked to their other phone numbers before they can proceed to connect bank accounts with other phone number
  // OK : success
  // INTERNAL : some error in handling reoobe
  // To be called when backend gives CONNECTED_ACCOUNT_HANDLE_REOOBE deeplink
  rpc HandleReoobe (HandleReoobeRequest) returns (HandleReoobeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch FIP Metas of banks for which connected_accounts has not been made available yet
  // OK : success
  // INTERNAL : error in fetching
  rpc GetAvailableFips (GetAvailableFipsRequest) returns (GetAvailableFipsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to create and record bank preferences for users
  // In case a user has banks for which connected_accounts is yet to be made available
  // OK : success
  // INTERNAL : error while creating
  rpc CreateBankPreference (CreateBankPreferenceRequest) returns (CreateBankPreferenceResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to resume data sync for an account for which consent is PAUSED. Only accounts with status as
  // DATA_SYNC_OFF_PAUSED can be resumed. Backend will return all the consent handles for the account which can be resumed
  // OK : success
  // INTERNAL : server error
  rpc ResumeAccountSync (ResumeAccountSyncRequest) returns (ResumeAccountSyncResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to get data pull status from a particular consent handle by examining the data fetch attempt status
  // return (data pull status), on (data fetch attempt status)
  // return SUCCESSFUL, on COMPLETED
  // return FAILED, on MANUAL_INTERVENTION
  // return FAILED, on FAILED_START
  // return IN_PROGRESS, for rest
  // return next poll seconds
  // return deeplink to land on the proper page
  // OK : success
  // INTERNAL : internal error
  rpc GetDataPullStatusFromConsentHandle (GetDataPullStatusFromConsentHandleRequest) returns (GetDataPullStatusFromConsentHandleResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to get list of allowed FIP's and list of allowed FI TYPES for a FIP in a particular env
  // In future this RPC can support all possible connected account backend driven config params which are needed
  // for controlled testing based on user nature example FNF, INTERNAL etc
  // OK : success
  // INTERNAL : internal error
  rpc GetAllowedConfig (GetAllowedConfigRequest) returns (GetAllowedConfigResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to get home page summary for all connected accounts of a user. We might show the connected accounts
  // of a user grouped by certain statuses hence client just renders the grouped accounts as home page card list
  // returned from backend. Effective balance is computed by backend and returned based on some logic at backend.
  // Individual account details are also returned which include balance, etc. Each card will have a deeplink attached
  // Which will redirect user to that particular account details screen in profile.
  // OK : success
  // NOT FOUND : No connected accounts found for the user
  // INTERNAL : internal error
  rpc GetHomeSummary (GetHomeSummaryRequest) returns (GetHomeSummaryResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch related accounts which needs to be deleted if a user tries to delete an account
  // An account can be linked with multiple consents and consents can in turn have multiple accounts hence
  // If a user requests to delete an account all it's related accounts with consents need to be deleted as well
  // This RPC just gives the list of accounts along with current account which will be deleted as a consequence
  // of this action and client needs to call DeleteAccount RPC separately to take delete action if user confirms
  // OK : success
  // INTERNAL : internal error
  rpc GetRelatedAccountsForDelete (GetRelatedAccountsForDeleteRequest) returns (GetRelatedAccountsForDeleteResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to be called on connect account entry point anywhere on the app. It is a wrapper API and takes care of
  // wealth onboarding status checks and even initiating wealth onboarding for a user if not done already. Will
  // return appropriate deeplink to client to navigate further in the flow. Currently it will return
  // CONNECTED_ACCOUNT_BENEFITS_SCREEN in case wealth onboarding is completed else it will return the deeplink
  // returned by wealth service.
  // OK : success
  // INTERNAL : internal error
  rpc GetLandingPageOnConnect (GetLandingPageOnConnectRequest) returns (GetLandingPageOnConnectResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // RPC to be called after pressing proceed on benefits screen. It is a wrapper API which has
  // the same functionality as GetLandingPageOnConnect proceed CTA on later versions
  // older versions will still use CTA from benefits screen
  // Error view if any will be passed in response header eg: system down bottom sheet error view
  // OK : success
  // INTERNAL : internal error
  // Deprecated in favor of `GetLandingPageOnConnect` RPC
  rpc ConnectAccount (ConnectAccountRequest) returns (ConnectAccountResponse) {
    option deprecated = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // RPC to be called to show connected accounts entry point in different places like Home, Profile sections
  // client to call this RPC everytime on app load
  // OK : success
  // INTERNAL : internal error
  rpc GetConnectedAccountEntryPoints (GetConnectedAccountEntryPointsRequest) returns (GetConnectedAccountEntryPointsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // RPC to fetch auth token
  // Takes aa entity as input
  // OK : success
  // INTERNAL : internal error
  // INVALID ARGUMENT : invalid aa entity
  rpc GetAuthToken (GetAuthTokenRequest) returns (GetAuthTokenResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // RPC provides balances of all connected accounts along with fi balance
  // Used in Home screen for rendering the Connected Accounts card
  // Figma: https://www.figma.com/file/qOV7SpYbTdXlpzE1JxHa4I/%F0%9F%9B%A0-Home-Workfile?node-id=6273%3A106686
  // OK : success
  // INTERNAL : internal error
  rpc GetConnectedAccountsSummaryForHome (GetConnectedAccountsSummaryForHomeRequest) returns (GetConnectedAccountsSummaryForHomeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // GetSdkExitDeeplink RPC provides the deeplink for exit from SDK.
  // This RPC is used to get the deeplink show the next screen when the user is exited from SDK, According to the exit point or the exit operation
  // that was last performed along with its reason, This RPC also takes CA Flow nome to show the exit screens specific to service which
  // is using the connected account flow.
  rpc GetSdkExitDeeplink (GetSdkExitDeeplinkRequest) returns (GetSdkExitDeeplinkResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetLandingPageForRenewal RPC is called by the client to initiate renewal of consent. It returns a deeplink which
  // takes the user to AA SDK login. The rest of the flow is similar to a new consent flow.
  // This RPC can be called from various consent renewal entrypoints in the app
  rpc GetLandingPageForRenewal (GetLandingPageForRenewalRequest) returns (GetLandingPageForRenewalResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetLandingPageForConnectingFiToFi RPC is used for initiating Fi to Fi flow to connect Federal saving bank account.
  // it provides the deeplink to start the SDK with screen name as CA_LANDING_PAGE_FI_TO_FI_FLOW_SCREEN
  // and screen options which contains parameters used at the time of SDK initialisation.
  // It also returns display texts used in the flow.
  // returns, OK : success
  // INTERNAL : internal error
  // figma:https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=10505-118757&t=Dofku7gqaVuhUWZu-0
  rpc GetLandingPageForConnectingFiToFi (GetLandingPageForConnectingFiToFiRequest) returns (GetLandingPageForConnectingFiToFiResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = true;
  }

  // GetConsentHandleStatus RPC is used to poll consent handle status required while Connecting Fi Federal saving banks account
  // returns OK : success with deeplink for either next screen or next poll counter
  // INTERNAL : internal error
  rpc GetConsentHandleStatus (GetConsentHandleStatusRequest) returns (GetConsentHandleStatusResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = true;
  }

  // GetAllDepositAccounts RPC is used as part of net worth to show all the deposit accounts summary
  //  figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-V0-%E2%80%A2-FFF?type=design&node-id=317-27417&mode=design&t=Esbx9KMg1fFOFK4Q-0
  rpc GetAllDepositAccounts (GetAllDepositAccountsRequest) returns (GetAllDepositAccountsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RegisterFiTncConsent is used to register users acknowledgment of Fi TnC
  rpc RegisterFiTncConsent (RegisterFiTncConsentRequest) returns (RegisterFiTncConsentResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetBenefitsScreenParams returns params to build the connected accounts benefits screen
  rpc GetBenefitsScreenParams (GetBenefitsScreenParamsRequest) returns (GetBenefitsScreenParamsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc GetBenefitScreen (GetBenefitScreenRequest) returns (GetBenefitScreenResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc GetBanks (GetBanksRequest) returns (GetBanksResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc GetSdkDeeplink (GetSdkDeeplinkRequest) returns (GetSdkDeeplinkResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}


message RegisterFiTncConsentRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  repeated string consent_ids = 2 [(validate.rules).repeated.min_items = 1];
}

message RegisterFiTncConsentResponse {
  enum Status {
    // fi consent recorded successfully
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
}

message GetBenefitsScreenParamsRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // ca_flow_name represents the name of other service which is trying to use connected account flow
  string ca_flow_name = 2;

  // A unique identifier of the flow started to connect accounts
  string ca_flow_id = 3;
}

message GetBenefitsScreenParamsResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  BenefitsScreenType benefits_screen_type = 2;
  // screen rendering and behaviour will change on the basis of the oneof param
  oneof params {
    api.typesv2.analyser.ConsentScreen analyser_consent = 3;
  }
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
  }
  enum BenefitsScreenType {
    BENEFITS_SCREEN_TYPE_UNSPECIFIED = 0;
    BENEFITS_SCREEN_TYPE_ANALYSER_CONSENT = 1;
  }
  // redirect deeplink to another screen if consent is not required
  // client should ignore benefits screen params and jump to redirect deeplink
  frontend.deeplink.Deeplink redirect_deeplink = 4;
}

message GetLandingPageForConnectingFiToFiRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // MANDATORY, Reason: we have finalised which AA Entity to be used for the flow at the entry point level itself,
  // now here while sending SDK initialisation params, the same AA Entity should be used.
  api.typesv2.AaEntity aa_entity = 2 [(validate.rules).enum = {not_in: [0]}];
}

message GetLandingPageForConnectingFiToFiResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink to initiate connecting Fi to Fi flow
  deeplink.Deeplink init_fi_to_fi_flow_deeplink = 2;
}

message InitiateConsentRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // vua generated at the AA end for the user
  string vua = 2;
  // entity for which request is being made, defaults to one money aa if unspecified
  frontend.connected_account.AaEntity aa_entity = 3;
  // purpose for which request is being made, defaults to consent flow if unspecified
  frontend.connected_account.ConsentRequestPurpose consent_request_purpose = 4;
  // ca_flow_name represents identifier of other service which is trying to use connected account flow
  // The value for this string has to be passed as is which is given with deeplink at the time of initializing SDK
  // In case string does not match with respective service identifier, then by default connected account service flow will be followed
  string ca_flow_name = 5;
  // number of consent handles to generate
  // We usually take consent at FIP level. In this case this parameter will have value 1.
  // But for some FIPs like NSDL we take consent at account level. In this case this parameter will have value equal to number of accounts for that FIP
  // In future when we take consent for all FIPs in one step, this parameter will be mostly equal to number of FIPs ( Though In some cases like NSDL, we will take consent at account level )
  int32 num_of_consent_handles_to_generate = 6;

  // Unique identifier of a flow to connect an external account
  string connection_flow_id = 7;
}

// Purpose for Consent Request
enum ConsentRequestPurpose {
  CONSENT_REQUEST_PURPOSE_UNSPECIFIED = 0;
  // Consent Request made for the normal consent flow
  CONSENT_REQUEST_PURPOSE_CONSENT_FLOW = 1;
  // Consent Request made for authentication purposes
  // Currently only used by Finvu AA, since it requires a consent handle while taking OTP requests
  CONSENT_REQUEST_PURPOSE_AA_AUTH_FLOW = 2;
  // Consent purpose made for renewal
  CONSENT_REQUEST_PURPOSE_RENEWAL = 3;
}

message InitiateConsentResponse {
  enum Status {
    // consent initiated successfully
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
  // consent_handle generated from the consent request.
  // Deprecated in favour of consent_handle_list
  string consent_handle = 2 [deprecated = true];
  frontend.header.ResponseHeader resp_header = 15;
  // deeplink to land on the post consent approval screen
  deeplink.Deeplink next_action = 3;
  // list of consent handles generated
  repeated string consent_handle_list = 4;
}

message FetchConsentParamsRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // aa_entity is required for forming AA Entity specific consent parameters, e.g. footer note in response is different for Finvu and Onemoney
  // Optional param
  api.typesv2.AaEntity aa_entity = 2;
}

message FetchConsentParamsResponse {
  enum Status {
    // consent params fetched successfully
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
  Consent consent = 2 [deprecated = true];
  // more details about the consent
  ConsentMeta consent_meta = 3 [deprecated = true];
  // ConsentParameter is a map of consent parameters which consist of key (name/title of the consent parameter) and value of the consent parameter,
  // all the consent parameters are displayed in sequence of keys.
  map<string, string> consent_params = 4;
  // aa_entity_consent_bottom_info_text denotes the consent note displayed according to the AA entity.
  string aa_entity_consent_bottom_info_text = 5;
  frontend.header.ResponseHeader resp_header = 15;
  // consent_details_html is the html string used to display consent details with a tappable view details text
  // e.g, Get 2 years of insights from May 8, 2024 to May 8, 2026, with daily updates. View consent details
  string consent_details_html_string = 6;
}

message Consent {
  // date of data fetch range start
  api.typesv2.Date from = 1;
  // date of data fetch range end
  api.typesv2.Date to = 2;
  // text for displaying range of data fetch
  string display_text_range = 3;
  // text for displaying validity time period of a consent
  string display_text_valid_period = 4;
}

message ConsentMeta {
  // purpose of the consent
  string display_text_consent_purpose = 1;
  // types of consent shared, ex - Transactions, Summary, Profile
  string display_text_consent_types_shared = 2;
  repeated string consent_types_shared = 3;
  // frequency of data fetch
  string display_text_fetch_frequency = 4;
  Frequency fetch_frequency = 5;
}

message Frequency {
  FrequencyUnit unit = 1;
  // Define how many times consumer can access the financial information
  int32 val = 2;
}

// Unit of frequency at which data is queried
enum FrequencyUnit {
  FREQUENCY_UNIT_UNSPECIFIED = 0;
  FREQUENCY_UNIT_HOUR = 1;
  FREQUENCY_UNIT_DAY = 2;
  FREQUENCY_UNIT_MONTH = 3;
  FREQUENCY_UNIT_YEAR = 4;
}

message GetLinkedAaAccountsRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}

message GetLinkedAaAccountsResponse {
  enum Status {
    // Financial information accounts fetched successfully for a user
    OK = 0;
    // no linked accounts found for user
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
  // List of synced accounts of the user
  repeated Account accounts = 2;
}

message GetAccountDetailsRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // Account id as returned in get accounts API for which details are to be fetched
  string account_id = 2;
}

message GetAccountDetailsResponse {
  enum Status {
    // Financial information accounts fetched successfully for a user
    OK = 0;
    // no linked accounts found for user
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
  // details of the account
  frontend.connected_account.AccountDetail account_detail = 2;
  // List of all available actions for this account
  repeated AccountActionOption account_action_option_list = 3;
  // optional: popup for consent renewal
  // https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9200%3A106553&mode=dev
  features.AaConsentRenewalPopupOptions consent_renewal_popup = 4;
  // optional: action banner in account details page
  //  https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=12564%3A112648&mode=dev
  api.typesv2.ui.IconTextComponent action_banner = 5;
}

message GetConnectedAccountsRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // Mandatory parameter to pass with minimum length 1
  repeated AccountStatus account_status_list = 2;
}

message GetConnectedAccountsResponse {
  enum Status {
    // Financial information accounts fetched successfully for a user
    OK = 0;
    // no linked accounts found for user
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
  repeated AccountDetail account_detail_list = 2;
}

message GetRelatedAccountsForDisconnectRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // Account id for which related accounts are to be fetched for disconnect
  string account_id = 2 [(validate.rules).string.min_len = 1];
}

message GetRelatedAccountsForDisconnectResponse {
  enum Status {
    // Financial information accounts fetched successfully for a user
    OK = 0;
    // no related accounts for disconnection
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
  // List of accounts which need to be disconnected with current one
  repeated frontend.connected_account.AccountDetail account_detail_list = 2;
  // List of consent handles which need to be revoked to disconnect the accounts
  repeated string consent_handle_list = 3;
  // List of consent ids
  repeated string consent_id_list = 4;
  // Bottom-sheet screen for disconnect confirmation
  deeplink.Deeplink confirm_bottom_sheet = 5;
}

message GetRelatedAccountsForDeleteRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // Account id for which related accounts are to be fetched for delete
  string account_id = 2 [(validate.rules).string.min_len = 1];
}

message GetRelatedAccountsForDeleteResponse {
  enum Status {
    // Financial information accounts fetched successfully for a user
    OK = 0;
    // internal server error in fetching accounts
    INTERNAL = 13;
  }
  // Status of the request
  frontend.header.ResponseHeader resp_header = 1;
  // List of accounts which need to be deleted with current one
  repeated frontend.connected_account.AccountDetail account_detail_list = 2;
  // Bottom-sheet screen for delete confirmation
  deeplink.Deeplink confirm_bottom_sheet = 3;
}

message DeleteAccountRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // Account id for the account which is to be deleted
  string account_id = 2;
}

message DeleteAccountResponse {
  enum Status {
    // Account deletion request successfully accepted by the server
    OK = 0;
    // account not found in system
    NOT_FOUND = 5;
    // account was not in disconnected state hence not deleted by the server
    FAILED_PRECONDITION = 9;
    // internal server error
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
}

message CheckReoobeRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // No longer needed to be passed by client
  string vua = 2 [deprecated = true];
}

message CheckReoobeResponse {
  rpc.Status status = 1;
  // List of accounts which need to be deleted in case of reoobe by a user
  // will be returned only in case user has done reoobe
  repeated frontend.connected_account.AccountDetail account_detail_list = 3;
  // In case user went through reoobe, we will return old vua of the user
  string old_vua = 4;
}

message HandleReoobeRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // No longer needed to be passed by client
  string vua = 2 [deprecated = true];
  // Backend will determine which accounts to delete automatically, no need to pass
  repeated frontend.connected_account.AccountDetail account_detail_list = 3 [deprecated = true];
}

message HandleReoobeResponse {
  rpc.Status status = 1;
}

// account metadata
message Account {
  // Type of Financial Information, ex - DEPOSIT, TERM_DEPOSIT, RECURRING_DEPOSIT
  string fi_type = 1;
  // Identifier for Financial information provider
  string fip_id = 2;
  // Linked ref number of the account
  string linked_ref_number = 3;
  // Account number in masked format
  string masked_acc_number = 4;
  // account id of the account, internally generated
  // Will be returned only if data was successfully persisted for the account
  string account_id = 5;
  // FIP logo
  string fip_logo_url = 6;
  // FIP name, each FIP ID will be mapped to FIP name if needed
  string fip_name = 7;
}

message GetAvailableFipsRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}

message GetAvailableFipsResponse {
  rpc.Status status = 1;
  repeated FipMeta fip_meta_list = 2;
}

message CreateBankPreferenceRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  repeated api.typesv2.Bank bank_list = 2 [(validate.rules).repeated.min_items = 1];
}

message CreateBankPreferenceResponse {
  rpc.Status status = 1;
}

message ResumeAccountSyncRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  string account_id = 2;
}

message ResumeAccountSyncResponse {
  rpc.Status status = 1;
  // List of consent handles which need to be made active
  repeated string consent_handle_list = 2;
}

enum DataPullStatus {
  DATA_PULL_STATUS_UNSPECIFIED = 0;
  DATA_PULL_STATUS_SUCCESSFUL = 1;
  DATA_PULL_STATUS_IN_PROGRESS = 2;
  DATA_PULL_STATUS_FAILED = 3;
}

message GetDataPullStatusFromConsentHandleRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // consent handle for which data pull status has to be fetched
  string consent_handle = 2 [deprecated = true];
  // current_poll_count represents what is the current polling count of data pull status
  // Currently, the data pull status poll and retry logic is handled at the client side, and thus to make it backend driven,
  // we have to maintain a field that stores the current count of the poll which will be checked against the max allowed attempt.
  // Thus, backend can handle the logic when maximum attempts are exhausted
  // Initially, field value will be 0, and in further polls, client will re-send the field in request received from backend.
  int32 current_poll_count = 3;
  // consent handle list for which data pull status has to be fetched
  repeated string consent_handle_list = 4 [(validate.rules).repeated.max_items = 30];

  // Unique identifier of a flow to connect an external account
  string connection_flow_id = 5;
}

message GetDataPullStatusFromConsentHandleResponse {
  rpc.Status status = 1;
  // data pull status
  DataPullStatus data_pull_status = 2;
  // number of seconds client needs to wait before polling this API again
  // In case backend returns -1, client can stop polling. In case backend returns 0, poll immediately
  int32 next_poll_seconds = 3;
  // deeplink to land on the proper page
  deeplink.Deeplink deeplink = 4;
  // max number of attempts allowed for polling data pull status for consent handle. For example : If max attempts are 5 and next poll duration is 3 client will wait for 15 seconds on the screen for data pull to be moved to terminal state.
  int32 max_allowed_attempts = 5;
  // current_poll_count represents what is the current polling count of data pull status
  // Currently, the data pull status poll and retry logic is handled at the client side, and thus to make it backend driven,
  // we have to maintain a field that stores the current count of the poll which will be checked against the max allowed attempt.Thus, backend can handle the logic when maximum attempts are exhausted
  // The current poll count will be incremented by 1, in each polling response.
  int32 current_poll_count = 6;
}

message GetAllowedConfigRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // optional argument - list of fip ids to get config - if empty, all bank metadata is returned
  repeated string fip_id_list = 2;
  // optional argument - list of aa entities to get config - if empty, all aa metadata is returned
  repeated api.typesv2.AaEntity aa_entity_list = 3;
  // ca_flow_name represents the name of other service which is trying to use connected account flow
  string ca_flow_name = 5;
}

message GetAllowedConfigResponse {
  rpc.Status status = 1;
  repeated FipMeta fip_meta_list = 2;
  // Backend driven title text on account discovery screen
  string account_discovery_title_text = 3;
  // Backend driven subtitle text on account discovery screen
  string account_discovery_subtitle_text = 4;
  // Timeout for finvu account discovery API calls.
  // Example : Finvu has a separate API call per FIP hence if we want to discover account for users in N FIPs
  // client initiates N parallel calls. Some calls get completed in X seconds while some take Y. We do not want
  // user to remain blocked due to some particular FIP call taking large times hence client will abort the operation
  // after this value returned by backend.
  // Deprecated in favour of AaEntityMeta.account_discovery_timeout_seconds
  int32 finvu_account_discovery_timeout_seconds = 5 [deprecated = true];
  // Timeout for onemoney account discovery API calls.
  // Example : Onemoney has a Single discovery API call per FIP hence if we want to discover account for users in N FIPs
  // client initiates N parallel calls. Some calls get completed in X seconds while some take Y. We do not want
  // user to remain blocked due to some particular FIP call taking large times hence client will abort the operation
  // after this value returned by backend.
  // Deprecated in favour of AaEntityMeta.account_discovery_timeout_seconds
  int32 onemoney_account_discovery_timeout_seconds = 6 [deprecated = true];
  // parameters needed for Connected accounts v2 flow
  V2FlowParams v2_flow_params = 7;
  // flag to be used by client to decide whether to use async discovery end point of finvu or not
  // Async discovery endpoint fires discovery calls in parallel at finvu AA's end and makes sure websocket connection
  // is not choked due to some bank taking long time in discovery
  // Deprecated in favour of AaEntityMeta.async_discovery_enable
  bool use_finvu_async_discovery = 8 [deprecated = true];
  // list of metadata related to all AA entities (account aggregator) as provided in the request argument
  // if aa_entity_list is empty, returns metadata of all entities
  repeated frontend.connected_account.common.AaEntityMeta aa_entity_meta_list = 9;
  // NoAccountsDiscoveredTextParams will be used to show textual components on No accounts discovered screen
  NoAccountsDiscoveredTextParams no_accounts_discovered_text_params = 10;
  //  accounts_discovery_identifiers denotes the map of FIP id and the identifiers required for the account discovery
  map<string, AccountDiscoveryIdentifiers> accounts_discovery_identifiers = 11;
  // SDKFUiFlowParams can be used for all the UI parameters needed for CA SDK.
  SDKUiFlowParams sdk_flow_params = 12;
  // grouped FIP data
  repeated GroupedFipMetaDataByTypes grouped_fip_meta_data_by_types = 13;
  // Auto read timeout for reading all discovered FIPs OTP's, this is only used in android for now
  int32 auto_read_timeout_all_discovered_fips_otp = 14;
}

// AccountDiscoveryIdentifiers denotes all the identifier details that are required in request parameter to initiate account discovery
message AccountDiscoveryIdentifiers {
  // AccDiscoveryIdentifier consist of three params:
  // category: signifies if identifier type is WEAK or STRONG
  // value: signifies the actual value of the identifier
  // type: signifies the identifier type e.g. PAN, MOBILE (phone number), DOB (date of birth)
  message AccDiscoveryIdentifier {
    string category = 1;
    string value = 2;
    string type = 3;
  }
  repeated AccDiscoveryIdentifier acc_discovery_identifiers = 1;
}

message NoAccountsDiscoveredTextParams {
  string title_text = 1;
  string sub_title_text = 2;
  string fip_icon = 3;
  string fip_not_discovery_text = 4;
  string proceed_deeplink_text = 5;
}

// V2FlowParams will contain all the parameters(backend driven) for the V2 flow
// going forward we may deprecate v1 entirely
message V2FlowParams {
  // Backend driven title text on account discovery screen in V2 flow
  string account_discovery_title_text = 1;
  // Backend driven subtitle text on account discovery screen in V2 flow
  string account_discovery_subtitle_text = 2;
  // text to show on CTA
  string cta_text = 3;
  // subtitle text to show when account discovery is still happening
  string account_discovery_subtitle_searching_text = 4;
  // Text to show for discovery loading.
  // https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=14233-46818&mode=dev
  string account_discovery_loading_text = 7;
  // Text to be shown on the bottom of discovery page when user not able to find his bank/demat account
  // https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=14179-18175&mode=dev
  string cant_see_your_accounts_text = 8;
}

message SDKUiFlowParams {
  // text for showing flow header
  string flow_header = 1;
  // tells the completion status in percentage
  int32 progress_bar_status = 2;
  // bool to decide displaying progress bar
  bool display_progress_bar = 3;
  // text to be displayed during account discovery loading
  string account_discovery_searching_text = 4;
  // text to show on cta to connect discovered accounts, Looks good, Connect
  string connect_discovered_accounts_cta_text = 5;
  // cta text for retrying accounts failed to generate otp
  string retry_cta_text = 6;
  // cta text to ignore accounts failed to generate otp
  string skip_failed_accounts_cta_text = 7;
  // text to show the title when no accounts discovered
  string no_accounts_found_title = 8;
  // text to show the sub title when no accounts discovered
  string no_accounts_found_sub_title = 9;
  // text to show on CTA when no accounts found
  string no_account_found_cta_text = 10;
  // icon url for no accounts found
  string no_accounts_found_icon_url = 11;
  // message for showing disconnect account
  // Figma : https://www.figma.com/design/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile---Connected-Accounts?node-id=15476-71028&t=5g608Q1MkqwiKNQ8-4
  string disconnect_account_text = 12;
}

message GroupedFipMetaDataByTypes {
  // text to diaplay for account type groups
  string group_name = 1;
  // tells the FIP belongs to which entity type e.g., bank, demat, NPS, insurance etc
  string entity_type = 2;
  // contains the Fip meta of set of Fi's
  repeated FipMeta fip_meta = 3;
}

message GetHomeSummaryRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}

message GetHomeSummaryResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // collective balance to show to user
  BalanceMeta collective_balance = 2;
  // List of tiles to be shown in home summary in order
  repeated HomeAccountTile home_account_tile_list = 3;
  // deprecated in favour of ConsentRenewalPopup
  // Popup for renewal
  // https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9200%3A106553&t=eCpIS0IiiCrWdshM-1
  BalanceDashboardRenewalPopupOptions popup = 4 [deprecated = true];
  // renewal below balance
  api.typesv2.ui.IconTextComponent renewal_cta = 5;
  // Popup for renewal
  // https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9200%3A106553&t=eCpIS0IiiCrWdshM-1
  features.AaConsentRenewalPopupOptions consent_renewal_popup = 6;
}

message GetLandingPageOnConnectRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // ca_flow_name represents the name of other service which is trying to use connected account flow
  string ca_flow_name = 5;

  // A unique identifier of the flow started to connect accounts
  string ca_flow_id = 6;
}

message GetLandingPageOnConnectResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // Deeplink to tell client which screen to load
  // Deprecated in favour of new deeplink added which supports all the flows like reoobe etc.
  // Backend will keep on populating this only with wealth onb dl or benefits dl for backward compatibility
  deeplink.Deeplink deeplink = 2 [deprecated = true];
  // New deeplink to be used going forward by client to understand where to land on connect action
  // Few examples :
  // In case user has not completed wealth onboarding return deeplink to complete that
  // In case user has completed wealth onboarding return deeplink for benefits screen with proceed CTA
  // Proceed CTA on benefits can land user directly on SDK in case user passes re oobe check in case they do not
  // return deeplink to handle re oobe first and ask user to complete that action and than open SDK
  deeplink.Deeplink new_deeplink = 3;
}

message ConnectAccountRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // entity to check aa heartbeat status for
  frontend.connected_account.AaEntity aa_entity = 2;
  // deprecated since ConnectAccount RPC is not being used
  option deprecated = true;
}

message ConnectAccountResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // Deeplink to tell client which screen to load
  // Few examples :
  // In case user has not completed wealth onboarding return deeplink to complete that
  // In case user has completed wealth onboarding return deeplink for benefits screen
  // return deeplink to handle re oobe first and ask user to complete that action and than open SDK
  deeplink.Deeplink deeplink = 2;
  // deprecated since ConnectAccount RPC is not being used
  option deprecated = true;
}

message GetConnectedAccountEntryPointsRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}

message GetConnectedAccountEntryPointsResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // map of EntryPointType(string) as key to value defined via EntryPointOptions
  // For eg:
  // {
  //    "HOME" : {"enabled":true,"text":"text1"}
  //    "PROFILE" : {"enabled":true,"text":"text2"}
  // }
  // All keys in this map will be one of Enum values in EntryPointType always and client should do a enum.String() to access map values
  map<string, frontend.connected_account.EntryPointOptions> entry_point_map = 2;
  // Global connected account flag which denotes whether connected account is enabled/disabled for all users
  api.typesv2.common.BooleanEnum is_connected_account_enabled = 3;
}

message GetAuthTokenRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // aa entity to be passed in the request
  // if auth token is not supported for the entity, invalid status is returned
  frontend.connected_account.AaEntity aa_entity = 2;
}

message GetAuthTokenResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // auth token
  string token = 2;
  // expiry duration of token in minutes with delta
  int32 expiry_duration_minutes = 3;
}

message GetConnectedAccountsSummaryForHomeRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // Request param to control which Dashboard UI version is shown on clients. This param will be not set for old clients
  // and may/may not be set for new clients based on experimentation. See DashboardVersion docs for default handling
  home.DashboardVersion dashboard_version = 2;
  // Optional field to control zero state card variant to be shown for DASHBOARD_VERSION_V2.
  // This may only be set if dashboard_version is set to DASHBOARD_VERSION_V2
  // Even if not set, the base variant of zero state dashboard cards would be shown
  home.ZeroStateDashboardCardVariant zero_state_dashboard_variant = 3;
}

message GetConnectedAccountsSummaryForHomeResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // dashboard info in new home summary format
  home.HomeDashboard dashboard_info = 2;
}

message GetSdkExitDeeplinkRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // ExitOperation represents the last performed operation before user exited the SDK
  frontend.connected_account.ExitOperation exit_operation = 2;
  // ExitReason signifies the reason for exit from SDK
  frontend.connected_account.ExitReason exit_reason = 3;
  // ca_flow_name represents identifier of other service which is trying to use connected account flow
  // The value for this string has to be passed as is which is given with deeplink at the time of initializing SDK
  // In case string does not match with respective service identifier, then by default connected account service flow will be followed
  string ca_flow_name = 5;
  // AaEntity represents which AA was used for starting the connected account flow,
  // it will be used in cases to show AA specific error screen or bottom sheet after exiting SDK
  api.typesv2.AaEntity aa_entity = 6;

  // Unique identifier of a flow to connect an external account
  string connection_flow_id = 7;
}

message GetSdkExitDeeplinkResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink to land on screen on exit from SDK
  deeplink.Deeplink exit_screen_deeplink = 2;
}

// ExitOperation represents the last operation performed while user exited the SDK.
// This is used to signal the client that what action/deeplink should be provided to land on next screen after exit from SDK.
enum ExitOperation {
  EXIT_OPERATION_UNSPECIFIED = 0;
  EXIT_OPERATION_LOGIN_OTP = 1;
  EXIT_OPERATION_ACC_DISCOVERY = 2;
  EXIT_OPERATION_ACC_LINK = 3;
  EXIT_OPERATION_CONSENT = 4;
}
// ExitReason represents the reason for the exit from SDK.
// While signalling the client about the next action on exit, client also provide the reason of exit within an operation.
// for e.g. If user Accounts are not discovered then the reason can be either Timeout issue or No accounts discovered
enum ExitReason {
  EXIT_REASON_UNSPECIFIED = 0;
  EXIT_REASON_OTP_VERIFICATION_FAILED = 2;
  EXIT_REASON_ACC_DISCOVERY_TIMEOUT = 3;
  EXIT_REASON_NO_ACC_DISCOVERED = 4;
  EXIT_REASON_REQUEST_CONSENT_FAILURE = 5;
  EXIT_REASON_APPROVE_CONSENT_FAILURE = 6;
}

// deprecated in favour of features.AAConsentRenewalPopupOptions
// pop-up screen to show when user lands on balance dashboard page and they are eligible for renewal
// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9200%3A106553&t=1nHxu44cjRq40YI6-1
message BalanceDashboardRenewalPopupOptions {
  option deprecated = true;
  bool show_popup = 1;
  api.typesv2.common.Image header_icon_url = 2;
  api.typesv2.common.Text title = 3;
  api.typesv2.common.Text subtitle = 4;
  api.typesv2.common.Text body = 5;
  repeated frontend.deeplink.Cta cta_list = 6;
  // background color for popup
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 7;
  // time after which popup would be shown to user after dismissing or remind me later
  google.protobuf.Duration dismiss_popup_duration = 8;

}

message GetLandingPageForRenewalRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // optional argument - list of fip ids
  repeated string fip_id_list = 2;
}

message GetLandingPageForRenewalResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink to initiate consent renewal flow
  deeplink.Deeplink renewal_deeplink = 2;
}

message GetConsentHandleStatusRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // consent handle for which consent handle status has to be checked
  string consent_handle = 2 [(validate.rules).string.min_len = 1];
  // current_poll_count represents what is the current polling count for checking consent handle status
  // Initially, field value will be 0, the value will be increased by 1 in further poll counts.
  // This poll count max value can reach a max allowed poll count.
  // if consent handle status remained pending and polling max value is breached then failure consent handle status deeplink,
  // else if consent handle status value is failed or success corresponding deeplink is returned according to designs
  // ref: https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=10505-118757&t=DKswshjsJq3CwCv3-0
  int32 current_poll_count = 3;

  // Unique identifier of a flow to connect an external account
  string connection_flow_id = 4;
}

message GetConsentHandleStatusResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // consent handle status
  frontend.connected_account.common.ConsentHandleStatus consent_handle_status = 2;
  // the delay duration in two consecutive poll
  // initially polling value will be returned as 0, and if value returned is -1 stop polling
  google.protobuf.Duration next_poll_duration = 3;
  // deeplink to land on the appropriate page according to failure/success
  deeplink.Deeplink deeplink = 4;
  // current_poll_count represents the current polling count of consent handle status
  int32 current_poll_count = 5;
}

message GetAllDepositAccountsRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}

message GetAllDepositAccountsResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // deposit_account_details consist of details of all the accounts of all deposit type i.e. Fixed Deposit(FI created, AA fetched, manually added)
  // Smart Deposit(Fi created) and Recurring Deposit(AA fetched and manually added)
  repeated DepositAccountsSection deposit_accounts_sections = 2;
  // total_balance_text represents the title of the balance to be shown
  api.typesv2.common.Text total_balance_text = 3;
  // total_balance represents the collective balance across all the deposits
  BalanceMeta total_balance = 4;
  // CTA to connect further deposit accounts
  frontend.deeplink.Cta connect_deposit_acc_cta = 5;
  // bottom_banner represents banner to track net worth
  api.typesv2.ui.IconTextComponent bottom_banner = 6;
}

message DepositAccountsSection {
  api.typesv2.common.Text title = 1;
  // type of the DepositAccount (can be Smart Deposit, Recurring Deposit, Fixed Deposit), can be more in future
  accounts.Type type = 2;
  // List of tiles to be shown in home summary in order
  repeated HomeAccountTile home_account_tile_list = 3;
  // info_cta used to deposit information per deposit type
  api.typesv2.ui.IconTextComponent info_cta = 4;
}

message GetBenefitScreenRequest {
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // ca_flow_name represents the name of other service which is trying to use connected account flow
  string ca_flow_name = 2;

  // A unique identifier of the flow started to connect accounts
  string ca_flow_id = 3;
}

message GetBenefitScreenResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 2;
  // Benefit screen to be rendered
  screens.BenefitScreen screen = 3;
}

message GetBanksRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // ca_flow_name represents the name of other service which is trying to use connected account flow
  string ca_flow_name = 2;
  // A unique identifier of the flow started to connect accounts
  string ca_flow_id = 3;
}

message GetBanksResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // Popular fips list title
  api.typesv2.common.Text popular_bank_list_title = 2;
  // Popular fips
  repeated BankInfo popular_bank_list = 3;
  // All fips list title
  api.typesv2.common.Text all_bank_list_title = 4;
  // All the fips which we want user to select
  repeated BankInfo all_bank_list = 5;
  // Issue to message mapping which will be shown to user
  // e.g, if fip is not supported, fip is facing some issues
  // Client will replace key {BANK_DISPLAY_NAME} with the bank display name.
  // Values e.g, NOT_SUPPORTED - IssueMessage, FIP_DOWN - IssueMessage
  map<string, IssueMessage> fip_issue = 6;
  // Default issue message to be displayed if not found in fip_issue
  IssueMessage default_issue_message = 7;
  //  either onemoney tnc or finvu tnc will be showed in bank selection screen based on what AA entity we use for a bank
  api.typesv2.common.ui.widget.CheckboxItem finvu_tnc = 8;
  api.typesv2.common.ui.widget.CheckboxItem onemoney_tnc = 9;

  message BankInfo {
    // unique fip id
    string fip_id = 1;
    // display name for the fip - human-readable name for the user
    api.typesv2.common.Text display_name = 2;
    // url logo
    api.typesv2.common.VisualElement logo_url = 3;
    // Issue if any e.g, fip is not supported, fip is facing some issues
    // Values e.g, NOT_SUPPORTED, FIP_DOWN
    string issue = 4;
    api.typesv2.AaEntity aa_entity = 5;
  }
  // Figma : https://www.figma.com/design/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?node-id=5201-51768&t=6mKN7q19PSHVnJUk-4
  // https://www.figma.com/design/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?node-id=5201-51735&t=6mKN7q19PSHVnJUk-4
  message IssueMessage {
    api.typesv2.common.ui.widget.VisualElementTitleSubtitleElement message = 1;
    frontend.deeplink.Cta cta = 2;
  }
}

message GetSdkDeeplinkRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // ca_flow_name represents the name of other service which is trying to use connected account flow
  string ca_flow_name = 2;
  // A unique identifier of the flow started to connect accounts
  string ca_flow_id = 3;
  // Fip id user selected
  string fip_id = 4;
}

message GetSdkDeeplinkResponse {
  // standard response header
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink
  deeplink.Deeplink deeplink = 2;
}
