syntax = "proto3";

package frontend.user;

import "api/typesv2/nominee.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/user";
option java_package = "com.github.epifi.gamma.api.frontend.user";

message Nominee {
  // Id of the nominee
  string id = 1;

  // This field indicates the type of relationship between the nominee and account holder.
  api.typesv2.RelationType relationship = 3 [(validate.rules).enum = {not_in: [0, 27, 28, 29]}];

  // Name of the nominee
  string name = 4;

  // date of birth of the nominee
  google.protobuf.Timestamp dob = 5;

  // Contact details of the nominee like address, email, phone number etc.
  api.typesv2.ContactInfo contact_info = 6;

  // if nominee is minor then guardian of nominee.
  api.typesv2.GuardianInfo guardian_info = 7;

  // Document type user is giving
  // maps to NomineeDocumentType
  string document_type = 9;

  // document number associated with the nominee
  // For document type PAN, it need to be PAN
  // document type AADHAAR, last 4 digits
  // document type driving license, full DL number
  string document_number = 10;
}
