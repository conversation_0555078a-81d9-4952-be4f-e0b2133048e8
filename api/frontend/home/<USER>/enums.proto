syntax = "proto3";

package frontend.home.orchestrator;

option go_package = "github.com/epifi/gamma/api/frontend/home/<USER>";
option java_package = "com.github.epifi.gamma.api.frontend.home.orchestrator";

// RetryType is an enum to indicate the kind of retry strategy of a component
enum RetryType {
  // do not retry
  RETRY_TYPE_UNSPECIFIED = 0;
  // show retry button to the user
  RETRY_TYPE_USER_INITIATED = 1;
  // retry once automatically
  RETRY_TYPE_CLIENT_INITIATED = 2;
}
