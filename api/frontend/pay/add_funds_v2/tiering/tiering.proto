// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package frontend.pay.add_funds_v2.tiering;

import "api/typesv2/common/image.proto";
import "api/typesv2/money.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/frontend/pay/add_funds_v2/tiering";
option java_package = "com.github.epifi.gamma.api.frontend.pay.add_funds_v2.tiering";

// Options related to the tiering card to be shown in the add funds page
message TieringCardOptions {
  // Title text of the tiering card
  api.typesv2.common.Text title = 1;
  // Background colour of the tiering card
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 2;
  // Details pertaining to the progress bar for upgrade
  ProgressBarDetails progress_bar_details = 3;
  // Info related to bottom tiering card footer
  // Deprecated: use TieringCardBottomInfoV2 instead
  TieringCardBottomInfo bottom_info = 4 [deprecated = true];
  TieringCardBottomInfoV2 bottom_info_v2 = 5;
}

message TieringCardBottomInfoV2 {
  oneof bottom_info {
    // bottom info to show tiering related info as IconTextComponent
    // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1586-62110&mode=design&t=x4uoemlNigT4wMXl-0
    api.typesv2.ui.IconTextComponent bottom_itc = 1;
  }
}

// Figma link - https://www.figma.com/file/C5US4W4er2caIJQmLkdzHI/Account-Tiering-%E2%80%A2-FFF?node-id=21%3A1723&t=bOmkqPbbriPI6Ggv-4
// Suggestion box with text related to tiering
message TieringSuggestionOptions {
  // Image url
  api.typesv2.common.Image image_url = 1;
  // Background colour of the suggestion box
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 2;
  // Text to be shown
  api.typesv2.common.Text text = 3;
}

// Figma link - https://www.figma.com/file/C5US4W4er2caIJQmLkdzHI/Account-Tiering-%E2%80%A2-FFF?node-id=21%3A1786&t=bOmkqPbbriPI6Ggv-4
// Details related to the progress bar
// Contains only start node and end node - does not support multiple tiers
message ProgressBarDetails {
  ProgressBarNode start_node = 1;
  // Node having details about the current progress of the user
  ProgressBarNode current_progress_node = 2;
  ProgressBarNode end_node = 3;
  // Colour of the filled progress bar
  api.typesv2.common.ui.widget.BackgroundColour filled_progress_bar_colour = 4;
  // Colour of the unfilled progress bar
  api.typesv2.common.ui.widget.BackgroundColour unfilled_progress_bar_colour = 5;
  // Percentage progress between start_node and current_progress_node
  int32 progress = 6;
}

message ProgressBarNode {
  // Image to be shown for the node
  api.typesv2.common.Image node_image_url = 1;
  // Image/icon to be shown with title
  api.typesv2.common.Image node_title_image = 2;
  // Title of the node
  api.typesv2.common.Text node_title = 3;
  // Progress bar has units associated with both start and end point, and for every single step in between
  // The unit here is money
  api.typesv2.Money node_amount = 4;
}

// Figma link - https://www.figma.com/file/C5US4W4er2caIJQmLkdzHI/Account-Tiering-%E2%80%A2-FFF?node-id=21%3A1787&t=bOmkqPbbriPI6Ggv-4
// Info related to tiering bottom card
message TieringCardBottomInfo {
  // Title of bottom info
  api.typesv2.common.Text title = 1;
  // Subtitle containing more details related to tier upgrade
  api.typesv2.common.Text sub_title = 2;
  // Background colour
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 3;
}
