syntax = "proto3";

package frontend.nudge;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/frontend/nudge";
option java_package = "com.github.epifi.gamma.api.frontend.nudge";

message Nudge {
  // header to be displayed on top of nudge card
  // for eg - GET STARTED, PAYMENTS
  api.typesv2.common.Text header = 1;

  // title to be displayed on nudge card
  // for eg - Let's start by adding money to your account
  api.typesv2.common.Text title = 2;

  // subtitle to be displayed on nudge card
  // for eg - 12346 users have tried this
  api.typesv2.common.Text subtitle = 3;

  // image to be displayed on nudge card
  api.typesv2.common.Image image = 4 [deprecated = true];

  // background color of the nudge card
  api.typesv2.ui.BackgroundColour bg_color = 5;

  // background shadow of the nudge card
  repeated api.typesv2.ui.Shadow shadows = 6;

  // cta to redirect to when nudge card is clicked
  // may or may not contain text depending on screen
  api.typesv2.ui.IconTextComponent cta = 7;

  // tag to be displayed on top of nudge card
  // for eg - New
  // client will decide whether to show tag or not
  api.typesv2.ui.IconTextComponent tag = 8;

  // nudge id to be cached for animation on client side
  string id = 9;

  // new tag to be shown till how many days
  uint32 new_till = 10;

  // background color of the dismiss nudge animation
  api.typesv2.ui.BackgroundColour checkmark_fill_color = 11;

  // nudge type is used by client to render nudge displays accordingly
  NudgeType nudge_type = 12;

  // visual element to be displayed on nudge card
  api.typesv2.common.VisualElement visual_element = 13;

  // used only for 'STANDARD_ROW' Nudge type
  api.typesv2.ui.IconTextComponent standard_row = 14;

  // optional: dismiss button to be displayed in top right corner of nudge
  // client will call DismissNudge RPC on click of this button
  // figma: https://www.figma.com/file/Sqs3y3hNKojUuX0RDbLcg3/%F0%9F%93%8D-Home-Workfile-3?type=design&node-id=10623%3A166528&mode=design&t=xm6YGNl8YgMh5iuC-1
  api.typesv2.common.VisualElement dismiss_button = 15;

  // [Optional] Border colour of the nudge
  api.typesv2.common.ui.widget.BackgroundColour border_color = 16;
}

// figma - https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0-to-2.1?type=design&node-id=15307%3A101995&t=NpuHslRB8PRJC4cF-1
enum NudgeType {
  NUDGE_TYPE_UNSPECIFIED = 0;
  /*
  To theme nudge in a gtm specific style i.e -
  1. show bigger image format
  2. trigger animation on cta chevron and text
  figma - https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%9B%A0%EF%B8%8F-Home-Workfile-2?type=design&node-id=990%3A36171&t=KicO2fAdI7ZSOriB-1
  */
  NUDGE_TYPE_GTM = 1;

  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0-to-2.1?type=design&node-id=15387%3A104694&t=oOSG1DZzBdK2E3I3-1
  NUDGE_TYPE_STANDARD_BOTTOM_VISUAL = 2;

  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0-to-2.1?type=design&node-id=15387%3A104737&t=oOSG1DZzBdK2E3I3-1
  NUDGE_TYPE_STANDARD_TOP_VISUAL = 3;

  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0-to-2.1?type=design&node-id=15396%3A105157&t=oOSG1DZzBdK2E3I3-1
  NUDGE_TYPE_FULL_VISUAL = 4;

  /*
    Rendered using 'standard_row' field
    figma: https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=7920-16341&mode=dev
   */
  NUDGE_TYPE_STANDARD_ROW = 5;
}

message Banner {
  // title of the banner
  api.typesv2.common.Text title = 1;
  // image to be shown on the banner
  api.typesv2.common.VisualElement visual_element = 2;
  // background color for the banner.
  api.typesv2.ui.BackgroundColour background_color = 3;
  // Deeplink for click on banner
  frontend.deeplink.Deeplink deeplink = 4;
  // Parameters used to show time counter
  api.typesv2.ui.IconTextComponent time_counter_view = 5;
  // The Visual to render on the Full Banner space. If this is set, Banner will only display the Full visual and no other
  // elements will be visible, apart from timer and the Indicator points when applicable. Ref:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-27875&mode=dev
  api.typesv2.common.VisualElement visual_element_full_banner = 6;
  // The background color of the selected indicator view/dot, when this banner element is visible. Ref:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-24373&mode=dev
  api.typesv2.ui.BackgroundColour indicator_selected_color = 7;
  // The background color of the default views/dots, when this banner element is visible. Ref:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-24373&mode=dev
  api.typesv2.ui.BackgroundColour indicator_default_color = 8;
  // Unique identifier for the banner, passed in client analytics
  string id = 9;
  // map of meta data passed through from Client in analytics
  map<string, string> biz_analytics_data = 10;
  // optional: dismiss button to be displayed in top right corner of banner
  // client will call DismissNudge RPC on click of this button
  // figma: https://www.figma.com/file/Sqs3y3hNKojUuX0RDbLcg3/%F0%9F%93%8D-Home-Workfile-3?type=design&node-id=10650%3A178206&mode=design&t=xm6YGNl8YgMh5iuC-1
  api.typesv2.common.VisualElement dismiss_button = 11;
}
