// RPCs related to TOTP frontend service

syntax = "proto3";

package frontend.auth.totp;

import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/frontend/auth/totp";
option java_package = "com.github.epifi.gamma.api.frontend.auth.totp";

// Frontend RPC service for TOTPs
service Totp {
  rpc GenerateTotp (GenerateTotpRequest) returns (GenerateTotpResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };
}


message GenerateTotpRequest {
  frontend.header.RequestHeader req = 1;
  // auth.totp.enums.Purpose.String()
  // eg: NET_WORTH_MCP
  string purpose = 2;
}

message GenerateTotpResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // totp as string. Eg: 123456
  string totp = 2;
  // Totp expiry timestamp
  google.protobuf.Timestamp expiry_timestamp = 3;
}
