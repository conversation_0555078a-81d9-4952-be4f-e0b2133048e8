syntax = "proto3";

package frontend.preapprovedloan;

import "api/accounts/account_type.proto";
import "api/frontend/analytics/analytics_screen_name.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/home/<USER>";
import "api/frontend/pay/transaction/service.proto";
import "api/frontend/preapprovedloan/pal_enums/enums.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/date.proto";
import "api/typesv2/deeplink_screen_option/preapprovedloans/components.proto";
import "api/typesv2/deeplink_screen_option/preapprovedloans/enums.proto";
import "api/typesv2/deeplink_screen_option/preapprovedloans/screen_options.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/form.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/money.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/frontend/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.frontend.preapprovedloan";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

service PreApprovedLoan {
  // this will be used to get the landing screen of pre approved loan
  rpc GetLandingInfo (GetLandingInfoRequest) returns (GetLandingInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // used to show the details of the loan offer
  // (returns S1 screen)
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=38152-41696&mode=design&t=gAvKvoozF63OTacy-4
  rpc GetOfferDetails (GetOfferDetailsRequest) returns (GetOfferDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // used to get the info post user selects loan amount and tenure
  // (returns S2 screen)
  // https://www.figma.com/file/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%AA%99-Lending-%E2%80%A2-FFF?type=design&node-id=38152-42046&mode=design&t=gAvKvoozF63OTacy-4
  rpc GetApplicationDetails (GetApplicationDetailsRequest) returns (GetApplicationDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // gets called when user request for resending of confirmation code(for now OTP)
  rpc GenerateConfirmationCode (GenerateConfirmationCodeRequest) returns (GenerateConfirmationCodeResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // returns deeplink according to the data needed from client for loan application
  rpc GetApplicationStatus (GetApplicationStatusRequest) returns (GetApplicationStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // this rpc is apply for the loan async
  // GetApplicationStatus will be used to poll status
  rpc ApplyForLoan (ApplyForLoanRequest) returns (ApplyForLoanResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // return screen for loan application confirmation
  // for now OTP confirmation screen
  rpc ConfirmApplication (ConfirmApplicationRequest) returns (ConfirmApplicationResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  rpc GetLivenessStatus (GetLivenessStatusRequest) returns (GetLivenessStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc GetDashboard (GetDashboardRequest) returns (GetDashboardResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // this rpc will be used to get deeplink by screen
  // it will help us avoid creating rpc like GetDashboard which basically returns copy of screen options
  // it will be used as a fallback for deeplink when screen options for the corresponding screen is not present
  rpc GetDeeplink (GetDeeplinkRequest) returns (GetDeeplinkResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // to cancel an ongoing loan application
  // the RPC can only be called if not already initiated with the vendor
  rpc CancelApplication (CancelApplicationRequest) returns (CancelApplicationResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // to initiate E-Sign (KFS) process
  rpc InitiateESign (InitiateESignRequest) returns (InitiateESignResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // to get loan details for a user for a loan
  rpc GetLoanDetails (GetLoanDetailsRequest) returns (GetLoanDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // to fill PrePay details with change in amount to repay
  rpc GetPrePayDetails (GetPrePayDetailsRequest) returns (GetPrePayDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // to make loan pre payment
  rpc PrePayLoan (PrePayLoanRequest) returns (PrePayLoanResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // to check the make payment order status for pre-pay/close
  // RPC to check the status related to any pre-approved loan activity.
  // The RPC can be used to fetch he status of pre-payment of a loan account of pre-closure of a loan account.
  rpc GetLoanActivityStatus (GetLoanActivityStatusRequest) returns (GetLoanActivityStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to fetch all transactions/activities for a loan account
  // fetches the data from BE and populate the fields accordingly date wise sorted in desc order (latest first)
  rpc GetAllTransactions (GetAllTransactionsRequest) returns (GetAllTransactionsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to fetch the transaction receipt/details according to a loan activity
  rpc GetTransactionReceipt (GetTransactionReceiptRequest) returns (GetTransactionReceiptResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  //rpc provides summary of loan amount taken by the actor
  // Used in Home screen for rendering the Home dashboard card
  // Figma :https://www.figma.com/file/wm2ChrGH6OEY1uFvZgYL4V/Pre-approved-Loans-%E2%80%A2-Workfile?node-id=3097%3A78815&t=igugVLVZLQCmd55D-4
  rpc GetLoanSummaryForHome (GetLoanSummaryForHomeRequest) returns (GetLoanSummaryForHomeResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };
  // rpc to add address details for a user. The RPC can be used in any flow in a loan application to add address
  // details. The request takes in the address and the loan request corresponding to the loan application.
  rpc AddAddressDetails (AddAddressDetailsRequest) returns (AddAddressDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.location_required) = LOCATION_CHECK_REQUIRED;
  }
  // rpc to get form details for a particular employment type
  rpc GetEmploymentFormDetails (GetEmploymentFormDetailsRequest) returns (GetEmploymentFormDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to add employment details for a user.
  rpc AddEmploymentDetails (AddEmploymentDetailsRequest) returns (AddEmploymentDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to let customer confirm the revised loan details based on their occupation. This is called from the hustle screen and takes user to poll screen
  rpc ConfirmRevisedLoanDetails (ConfirmRevisedLoanDetailsRequest) returns (ConfirmRevisedLoanDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // this rpc is to acknowledge client callbacks
  // whenever clients have to send any callback to BE, they can use this RPC. Not necessarily only for success cases,
  // callbacks could just be that the SDK flow has started.
  rpc ClientCallback (ClientCallbackRequest) returns (ClientCallbackResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to fetch loan communication addresses to be shown to the user
  rpc GetLoanAddresses (GetLoanAddressesRequest) returns (GetLoanAddressesResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // used to show the custom offer selection to user
  rpc CustomOfferSelection (CustomOfferSelectionRequest) returns (CustomOfferSelectionResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // used to show the custom offer details to the user
  rpc GetCustomOfferDetails (GetCustomOfferDetailsRequest) returns (GetCustomOfferDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // used for the PAN Verification of the user in IDFC flow
  rpc VerifyDetails (VerifyDetailsRequest) returns (VerifyDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // used for CKYC PAN Verification of the user
  rpc VerifyCkycDetails (VerifyCkycDetailsRequest) returns (VerifyCkycDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to fetch web view from vendor
  rpc GetMandateViewData (GetMandateViewDataRequest) returns (GetMandateViewDataResponse) {}
  // rpc to persist the user entered Pan and Dob
  rpc AddPanAndDobData (AddPanAndDobDataRequest) returns (AddPanAndDobDataResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to persist the user entered Name and Gender
  rpc AddNameAndGender (AddNameAndGenderRequest) returns (AddNameAndGenderResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };
  // rpc to onboard a user for loan journey and starts workflow for loan eligibility
  // creates loan applicant and loan request if not present already
  rpc CheckLoanEligibility (CheckLoanEligibilityRequest) returns (CheckLoanEligibilityResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc FetchCreditReport (FetchCreditReportRequest) returns (FetchCreditReportResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };
  // rpc to persist the user's banking details
  rpc AddBankingDetails (AddBankingDetailsRequest) returns (AddBankingDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to submit post user reviews the loan application/eligibility summary details
  rpc SubmitReviewLoanDetails (SubmitReviewLoanDetailsRequest) returns (SubmitReviewLoanDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // rpc to show AcqToLend Intro Screen
  rpc GetAcqToLendLandingScreen (GetAcqToLendLandingScreenRequest) returns (GetAcqToLendLandingScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // rpc to initiate standing instruction flow in backend
  rpc InitiateSiSetup (InitiateSiSetupRequest) returns (InitiateSiSetupResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // rpc to let the backend know which income verification option is selected by the user
  rpc SubmitIncomeVerificationOption (SubmitIncomeVerificationOptionRequest) returns (SubmitIncomeVerificationOptionResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // rpc to fetch mandate view deeplink screen
  rpc GetMandateViewDataV2 (GetMandateViewDataV2Request) returns (GetMandateViewDataV2Response) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetPWARedirectionDeeplink rpc is useful to fetch the pwa redirection deeplink for a given loan request/account.
  // **Note** : should only be used for loan programmes where the PWA flow is supported for LOS or Loan Servicing flows, currently it's only supported for Moneyview lender partner.
  rpc GetPWARedirectionDeeplink (GetPWARedirectionDeeplinkRequest) returns (GetPWARedirectionDeeplinkResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RecordUserAction rpc can be used to record some client action in a loan flow and return some action based on the response.
  rpc RecordUserAction (RecordUserActionRequest) returns (RecordUserActionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetVkycDeeplink can be used to get the VKYC deeplink to which we can redirect user to complete his/her VKYC
  rpc GetVkycDeeplink (GetVkycDeeplinkRequest) returns (GetVkycDeeplinkResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetLoanDocument rpc to download or send any loan document to the user
  rpc GetLoanDocument (GetLoanDocumentRequest) returns (GetLoanDocumentResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetLoanRepaymentMethods rpc is called to fetch screen data where we show user the amount and break ups for repaying amount with the payment methods
  rpc GetLoanRepaymentMethods (GetLoanRepaymentMethodsRequest) returns (GetLoanRepaymentMethodsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetLoanRepaymentMethods rpc is called to fetch screen data where we show user the amount and break ups for repaying amount with the payment methods
  rpc GetIfscCodeSuggestions (GetIfscCodeSuggestionsRequest) returns (GetIfscCodeSuggestionsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc SaveContactDetails (SaveContactDetailsRequest) returns (SaveContactDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // ModifyLoanTerms will modify the loan terms (loan amount and tenure) for an active loan application
  // this is required when the offer is changed during the loan application and user needs to select the amount and tenure again after checking the revised offer
  rpc ModifyLoanTerms (ModifyLoanTermsRequest) returns (ModifyLoanTermsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // To collect and save data like user selected loan amount, consents etc
  rpc CollectData (CollectDataRequest) returns (CollectDataResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to be called when user submits the data on LOANS_FORM_ENTRY_SCREEN
  rpc CollectFormData (CollectFormDataRequest) returns (CollectFormDataResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to get fields that need to be conditionally shown to the user based on the selected option
  rpc GetProgressiveDisclosureFormFields (GetProgressiveDisclosureFormFieldsRequest) returns (GetProgressiveDisclosureFormFieldsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message GetProgressiveDisclosureFormFieldsRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  // field type for which the additional fields are needed
  // this value should be same as the value passed in api.typesv2.deeplink_screen_option.preapprovedloans.CompositeFormField->field_type
  string field_id = 3;
  // this value should be same as the value passed in api.typesv2.ui.OptionSelectionItem->identifier
  string selected_option_id = 4;
}

message GetProgressiveDisclosureFormFieldsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // This will contain only the additional fields and not the entire list to be shown on the screen
  repeated api.typesv2.deeplink_screen_option.preapprovedloans.CompositeFormField additional_form_fields = 2;
}

message CollectFormDataRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  repeated FormFieldValue form_field_values = 4;
  message FormFieldValue {
    // this value should be same as the value passed in api.typesv2.deeplink_screen_option.preapprovedloans.CompositeFormField->field_type
    string field_id = 1;
    oneof field_value {
      string string_value = 2;
      int32 int_value = 3;
      api.typesv2.Money money_value = 4;
      // this value should be same as the value passed in api.typesv2.ui.OptionSelectionItem->identifier
      string selected_option_id = 5;
      api.typesv2.Date date_value = 6;
    }
  }
}

message CollectFormDataResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message CollectDataRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  pal_enums.LoanStepExecutionFlow flow = 3;
  string loan_request_id = 4;
  oneof Data {
    LoanPreferences loan_preferences = 5;
    Consent consent = 6;
    ModifiedInterestRate modified_interest_rate = 8;
  }
  // Deprecated
  pal_enums.Vendor loec_owner = 7;

  message LoanPreferences {
    int64 loan_amount = 1;
    int64 interest = 2;
  }

  message Consent {
    repeated string consent_ids = 1;
  }

  message ModifiedInterestRate {
    double interest_rate = 1;
  }
}

message CollectDataResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetVkycDeeplinkRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
}

message GetVkycDeeplinkResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message RecordUserActionRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;

  // these can be used to identify user actions that needs to be done.
  // for eg: lrId, lseId can be used for user actions in loan application flow.
  // loan_id can be used for actions after disbursal, for eg: loan closure via CX.
  string loan_request_id = 3;
  string loan_step_id = 4;
  string loan_id = 7;

  frontend.deeplink.Screen screen = 5;
  // encoded action details
  string action_details = 6;
}

message RecordUserActionResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
  // [NOTE] screen specific params can be added under a oneof field if required.
}

message SubmitIncomeVerificationOptionRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  // id of the ListItem in LoansIncomeVerificationIntroScreenOptions
  string income_verification_option_id = 4;
  // ids present in the CheckboxItems (if CheckboxItems are available for the selected item)
  repeated string consent_ids = 5;
}

message SubmitIncomeVerificationOptionResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetLoanAddressesRequest {
  frontend.header.RequestHeader req = 1;
  bool skip_address_auto_fill = 2;
}

message GetLoanAddressesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deprecated, use addresses_with_type_v2 = 4;
  repeated AddressWithType addressesWithType = 2;
  // field to signify if the user can be given an option to add a new address
  bool enable_new_address = 3;
  // moved to new due to better naming. Old version wasn't seperated by '_', which is not according to our convention
  repeated AddressWithType addresses_with_type_v2 = 4;
  // zero state text that needs to be shown in case of list of address is empty
  api.typesv2.common.Text hint_address = 5;
  // icon that needs to be shown to select address or add address
  api.typesv2.common.VisualElement hint_icon = 6;
  message AddressWithType {
    api.typesv2.AddressType type = 1;
    api.typesv2.PostalAddress address = 2;
  }
}

message GetLandingInfoRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  pal_enums.LoanType loan_type = 3;
  repeated string landing_info_filters = 4;
}

message GetLandingInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetOfferDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string offer_id = 2;
  // used to send user inputs for the details like emi, loan amount etc.
  UserInput user_input = 3;
  pal_enums.LoanHeader loan_header = 4;
  // to identify which screen the RPC is being called from
  frontend.deeplink.Screen screen = 5;
  pal_enums.LoanParamsSelectionFlow flow = 6;
  // mandatory for ACTIVE_APPLICATION_REVISED_OFFER flow
  // loan_request_id is needed to carry the context of the exiting loan application across the S1/S2 screens
  // and should be passed in the ModifyLoanTerms request when user confirms the amount and tenure based on the revised offer
  string loan_request_id = 7;

  message UserInput {
    api.typesv2.Money loan_amount = 1;
    int32 tenure_in_months = 2;
    MutualFundsPledgeDetails mutual_funds = 3;
    api.typesv2.Money emi_amount = 4;
    // this is used to determine if user has made a custom loan plan
    bool is_custom_loan_plan_chosen = 5;
  }
}

message MutualFundsPledgeDetails {
  repeated MutualFund funds = 1;
  message MutualFund {
    string isin = 1;
    // number of funds user is willing to pledge
    double quantity = 2;
  }
}

message GetOfferDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // loan offer details deeplink
  deeplink.Deeplink deeplink = 2;
}

message GetApplicationDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string offer_id = 2;
  api.typesv2.Money loan_amount = 3;
  int32 tenure_in_months = 4;
  pal_enums.LoanHeader loan_header = 5;
  MutualFundsPledgeDetails mutual_funds = 6;
  pal_enums.LoanParamsSelectionFlow flow = 7;
  // mandatory for ACTIVE_APPLICATION_REVISED_OFFER flow
  // loan_request_id is needed to carry the context of the exiting loan application across the S1/S2 screens
  // and should be passed in the ModifyLoanTerms request when user confirms the amount and tenure based on the revised offer
  string loan_request_id = 8;
}

message GetApplicationDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // loan application details deeplink
  deeplink.Deeplink deeplink = 2;
}

message GenerateConfirmationCodeRequest {
  frontend.header.RequestHeader req = 1;
  string loan_request_id = 2;
  pal_enums.LoanHeader loan_header = 3;
  string loan_step_execution_id = 4;
  pal_enums.OtpFlow otp_flow = 5;
}

message GenerateConfirmationCodeResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // otp screen
  deeplink.Deeplink deeplink = 2;
}

message ConfirmApplicationRequest {
  frontend.header.RequestHeader req = 1;
  string loan_request_id = 2;
  string otp = 3;
  // deprecating this and replacing by global enums to avoid converting at multiple places
  OtpFlow otp_flow = 4 [deprecated = true];
  pal_enums.LoanHeader loan_header = 5;
  // If OTP is generated internally at Fi, using auth service, this token is needed along with the OTP user entered
  // for verification
  string token = 6;
  string loan_step_execution_id = 7;
  pal_enums.OtpFlow otp_flow_identifier = 8;
  enum OtpFlow {
    OTP_FLOW_UNSPECIFIED = 0;
    OTP_FLOW_E_SIGN = 1;
  }
}

message ConfirmApplicationResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // otp screen in case of otp verification error
  // application status poll screen in case of status poll needs to be done
  deeplink.Deeplink deeplink = 2;
}

message ModifyLoanTermsRequest {
  frontend.header.RequestHeader req = 1;
  // revised offer id based on which the interest rate, amount, tenure, PF etc. were shown to the user
  string offer_id = 2;
  api.typesv2.Money loan_amount = 3;
  int32 tenure_in_months = 4;
  pal_enums.LoanHeader loan_header = 5;
  // loan_request_id of the active application for which the loan terms (amount and tenure) need to be modified
  string loan_request_id = 6;
  // ids of the TermInfo object in the TnC component of RevisedLoanOfferApplicationDetailsScreen
  repeated string consent_ids = 7;
}

message ModifyLoanTermsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message ApplyForLoanRequest {
  frontend.header.RequestHeader req = 1;
  string offer_id = 2;
  api.typesv2.Money loan_amount = 3;
  int32 tenure_in_months = 4;
  pal_enums.LoanHeader loan_header = 5;
  MutualFundsPledgeDetails mutual_funds = 6;
  repeated string consent_ids = 7;
  // if true, cancel any active loan request and start a new one
  // if active loan request is in a non-cancellable state, return appropriate screen
  bool cancel_current_loan_request = 8;
}

message ApplyForLoanResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // otp screen in case of otp verification error
  // application status poll screen in case of status poll needs to be done
  deeplink.Deeplink deeplink = 2;
}

message GetApplicationStatusRequest {
  frontend.header.RequestHeader req = 1;
  string loan_request_id = 2;
  // used to specify the retry attempt number based on this server will send next retry delay in status pool deeplink
  int32 retry_attempt_number = 3;
  pal_enums.LoanHeader loan_header = 4;
  // this is will used to inform from which screen exactly this status poll has been called
  // deprecated: use screen_name instead as analytics screen name can be different for a single screen for which will need to handle cases, which is unnecessary
  frontend.analytics.AnalyticsScreenName analytics_screen_name = 5 [deprecated = true];
  // this is will used to inform from which screen exactly this status poll has been called
  frontend.deeplink.Screen screen_name = 6;
  // flag to be send from the polling screen options in case next action needed to be fetched in sync
  bool get_next_action_in_sync = 7;
  // deprecated as this can be fetched from loan request id
  string workflow_id = 8 [deprecated = true];
  oneof parameters {
    // in case of webview with polling screen, client needs to the send the received url in screen options back in this rpc as params
    WebviewWithPollingParams webview_params = 10;
  }
  message WebviewWithPollingParams {
    // webview entry url
    string url = 1;
    // exit url to which the webview redirects after completion
    // this should be passed only when the url matches with the base path (excluding params) of the exit url specified in the webview screen options
    string exit_url = 2;
  }
}

message GetApplicationStatusResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // otp screen in case of otp verification error
  // application status poll screen in case of status poll needs to be done
  // application status result screen in case of result
  deeplink.Deeplink deeplink = 2;
  // for some external deeplinks like check liveness we don't have an option to pass client info to services
  // passing this so that loan request identifier can be used if needed in the flow
  string loan_request_id = 3;
}

message GetLivenessStatusRequest {
  frontend.header.RequestHeader req = 1;
  string loan_request_id = 2;
  pal_enums.LoanHeader loan_header = 3;
}

message GetLivenessStatusResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
  // passing this so that loan request identifier can be used to call check liveness status again
  string loan_request_id = 3;
}

message GetDashboardRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string entry_point = 3;
}

message GetDashboardResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetDeeplinkRequest {
  frontend.header.RequestHeader req = 1;
  // screen for which deeplink need to be shared
  deeplink.Screen screen = 2;
  pal_enums.LoanHeader loan_header = 3;
  // meta data needed for BE rpc's which need some identifier
  string loan_request_id = 4;
  oneof screen_params {
    KnowMoreScreenParams know_more_screen_params = 5;
    EsignScreenParams esign_screen_params = 6;
  }
  string loan_account_id = 10;
  message KnowMoreScreenParams {
    preapprovedloan.pal_enums.FaqTopic faq_topic = 1;
  }
  message EsignScreenParams {
    pal_enums.LoanDocType loan_doc_type = 3;
  }
}

message GetDeeplinkResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink with screen options
  deeplink.Deeplink deeplink = 2;
}

message CancelApplicationRequest {
  frontend.header.RequestHeader req = 1;
  string loan_request_id = 2;
  pal_enums.LoanHeader loan_header = 3;
}

message CancelApplicationResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message InitiateESignRequest {
  frontend.header.RequestHeader req = 1;
  string loan_request_id = 2;
  pal_enums.LoanHeader loan_header = 3;
}

message InitiateESignResponse {
  frontend.header.ResponseHeader resp_header = 1;
  string sign_url = 2;
  string exit_url = 3;
  deeplink.Deeplink next_step = 4;
}

message GetLoanDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string loan_id = 2;
  pal_enums.LoanHeader loan_header = 3;
}

message GetLoanDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetPrePayDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string loan_id = 2;
  api.typesv2.Money amount = 3;
  bool is_amount_selection_skipped = 4;
  pal_enums.LoanHeader loan_header = 5;
  bool is_pre_close = 6;
  // if this is true it will fetch the screen options for loan account selection screen.
  bool is_loan_account_selection_screen = 7;
  // the account against which payment is being made, used in flows like LAMF where we have separate accounts for
  // principal outstanding and interest payment
  string loan_account_type_identifier = 8;
}

message GetPrePayDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message PrePayLoanRequest {
  frontend.header.RequestHeader req = 1;
  string loan_id = 2;
  api.typesv2.Money amount = 3;
  pal_enums.LoanHeader loan_header = 4;
  bool is_pre_close = 5;
  // in case of TPAP, derived account id is sent
  string derived_account_id = 6;
  // in case of TPAP, account type for selected account is sent
  .accounts.Type account_type = 7;
  // the account against which payment is being made, used in flows like LAMF where we have separate accounts for
  // principal outstanding and interest payment
  string loan_account_type_identifier = 8;
}

message PrePayLoanResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
  // Transaction attributes are required for the client to:
  //  i. Generate the credentials using a common library i.e., NPCI CL or Partner bank's CL
  //  ii. Present the transaction information e.g., payment protocol to the user
  frontend.pay.transaction.TransactionAttribute transaction_attribute = 3;
  // If pin based authorization is required to execute payment for a given order.
  frontend.pay.transaction.PinRequiredType pin_required = 4;
  // reference ID for Pre-Payment to be used for payment authorization
  // Could be order's client request ID
  string reference_id = 5;
  // order id needed by client for UPI flows
  string order_id = 6;
}

message GetLoanActivityStatusRequest {
  frontend.header.RequestHeader req = 1;
  ActivityType activity_type = 2;
  string ref_id = 3;
  int32 attempt_number = 4;
  pal_enums.LoanHeader loan_header = 5;

  enum ActivityType {
    UNSPECIFIED = 0;
    PRE_PAY = 1;
    PRE_CLOSE = 2;
  }
}

message GetLoanActivityStatusResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetAllTransactionsRequest {
  frontend.header.RequestHeader req = 1;
  string loan_account_id = 2;
  pal_enums.LoanHeader loan_header = 3;
}

message GetAllTransactionsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetTransactionReceiptRequest {
  frontend.header.RequestHeader req = 1;
  string loan_activity_id = 2;
  pal_enums.LoanHeader loan_header = 3;
}

message GetTransactionReceiptResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetLoanSummaryForHomeRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  // Request param to control which Dashboard UI version is shown on clients. This param will be not set for old clients
  // and may/may not be set for new clients based on experimentation. See DashboardVersion docs for default handling
  home.DashboardVersion dashboard_version = 3;
  // Optional field to control zero state card variant to be shown for DASHBOARD_VERSION_V2.
  // This may only be set if dashboard_version is set to DASHBOARD_VERSION_V2
  // Even if not set, the base variant of zero state dashboard cards would be shown
  home.ZeroStateDashboardCardVariant zero_state_dashboard_variant = 4;
}

message GetLoanSummaryForHomeResponse {
  frontend.header.ResponseHeader resp_header = 1;
  home.HomeDashboard dashboard_info = 2;
}

message AddAddressDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string loan_request_id = 2;
  // address to be added in user's loan application
  api.typesv2.PostalAddress address = 3;
  pal_enums.LoanHeader loan_header = 4;
  bool use_address_checkbox = 5;
  bool add_address_details_in_sync = 6;
  bool is_vpn_connected = 7;
}

message AddAddressDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetEmploymentFormDetailsRequest {
  frontend.header.RequestHeader req = 1;
  api.typesv2.EmploymentType employment_type = 2;
  pal_enums.LoanHeader loan_header = 3;
}

message GetEmploymentFormDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated DetailsFormField form_field_list = 2;
  api.typesv2.EmploymentType default_employment_type = 3;
}

message AddEmploymentDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string loan_request_id = 2;
  repeated EmploymentDetails employment_details_list = 3;
  pal_enums.LoanHeader loan_header = 4;
  bool add_employment_details_in_sync = 5;

  message EmploymentDetails {
    DetailsFormField.DetailFormFieldInfoType details_type = 1;
    oneof detail_value {
      api.typesv2.EmploymentType employment_value = 2;
      string string_value = 3;
      api.typesv2.Money money_value = 4;
      api.typesv2.PostalAddress address_value = 5;
    }
  }
}

message AddEmploymentDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message DetailsFormField {
  string in_focus_text = 1 [deprecated = true];
  string placeholder_text = 2 [deprecated = true];
  bool is_editable = 3;
  deeplink.Cta navigation_cta = 4;
  // Icon on the left in some cases when data is filled.
  string icon_url = 5;
  oneof default_values {
    string string_default_value = 6;
    api.typesv2.Money money_default_value = 7;
    int32 int_default_value = 8;
    api.typesv2.PostalAddress address_value = 12;
  }
  api.typesv2.common.FontStyle value_font_style = 9;
  // This is needed to pass information back to BE as without type
  // we will not be able to differentiate between field types for info
  DetailFormFieldInfoType detail_form_field_info_type = 10;
  bool is_mandatory = 11;

  api.typesv2.common.Text placeholder_text_v2 = 13;

  // this field for storing text style for updated field text
  api.typesv2.common.Text updated_field_style = 14;

  api.typesv2.common.Text in_focus_text_v2 = 15;

  enum DetailFormFieldInfoType {
    OCCUPATION_TYPE = 0;
    OCCUPATION_NAME = 1;
    WORK_EMAIL_ADDRESS = 2;
    MONTHLY_INCOME = 3;
    OFFICE_ADDRESS = 4;
    ANNUAL_REVENUE = 5;
    BUSINESS_NAME = 6;
    GSTIN_BUSINESS_PAN_NUMBER = 7;
    EMPLOYER_CLASSIFICATION = 8;
    HIGHEST_EDUCATION = 9;
    DESIRED_LOAN_AMOUNT = 10;
  }
}

message ConfirmRevisedLoanDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string loan_request_id = 2;
  pal_enums.LoanHeader loan_header = 3;
}

message ConfirmRevisedLoanDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message ClientCallbackRequest {
  frontend.header.RequestHeader req = 1;
  string loan_request_id = 2 [deprecated = true];
  Type type = 3;
  Result result = 4;
  int32 vendor_code = 5;
  pal_enums.LoanHeader loan_header = 6;
  string client_callback_req = 7;
  // contains payload regarding call back events like mandate sdk
  CallbackPayload callback_payload = 8;
  // Since this is a generic api, we don't want to couple it with just one identifier (i.e. loan_request_id)
  // and hence deprecated this field with backward compatible service changes
  // For newer implementations, need to use these identifiers
  oneof Identifier {
    // intentional index gap

    // e.g. loanId, loanRequestId, etc
    string loan_req_id = 10;
    // e.g. orchId, clientRequestID, etc
    string step_orch_id = 11;
  }

  enum Type {
    TYPE_UNSPECIFIED = 0;
    MANDATE = 1;
    E_SIGN = 2;
    SI = 3;
  }

  enum Result {
    RESULT_UNSPECIFIED = 0;
    SUCCESS = 1;
    FAILED = 2;
    PENDING = 3;
    INITIATED = 4;
  }
}

message CallbackPayload {
  oneof payload {
    MandatePayload mandate_payload = 1;
  }
}

message MandatePayload {
  api.typesv2.deeplink_screen_option.preapprovedloans.MandateViewType mandate_view_type = 1;
  api.typesv2.deeplink_screen_option.preapprovedloans.MandateSdkVendor mandate_sdk_vendor = 2;
  MandateSdkEvent mandate_sdk_event = 3;
}

message MandateSdkEvent {
  // expecting json here which will be unmarshalled to the respective event type eg digiosdksuccessresponse,digiosdkfailureresponse and digiosdkgateway
  string event_payload = 1;
  enum EventType {
    EVENT_TYPE_UNSPECIFIED = 0;
    EVENT_TYPE_SUCCESS = 1;
    EVENT_TYPE_FAILURE = 2;
    EVENT_TYPE_GATEWAY = 3;
  }
  EventType event_type = 2;
}

message ClientCallbackResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message CustomOfferSelectionRequest {
  frontend.header.RequestHeader req = 1;
  string offer_id = 2;
  // used to send user inputs for the details like emi, loan amount etc.
  UserInput user_input = 3;
  pal_enums.LoanHeader loan_header = 4;


  message UserInput {
    api.typesv2.Money loan_amount = 1;
    int32 tenure_in_months = 2;
  }
}

message CustomOfferSelectionResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // loan offer details deeplink
  deeplink.Deeplink deeplink = 2;
}

message GetCustomOfferDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string offer_id = 2;
  // used to send user inputs for the details like emi, loan amount etc.
  UserInput user_input = 3;
  pal_enums.LoanHeader loan_header = 4;
  message UserInput {
    api.typesv2.Money loan_amount = 1;
    int32 tenure_in_months = 2;
  }
}

message GetCustomOfferDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // loan offer details deeplink
  deeplink.Deeplink deeplink = 2;
}

message VerifyDetailsRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_req_id = 3;
  api.typesv2.deeplink_screen_option.preapprovedloans.DetailsType details_type = 4;
  string value = 5;
  api.typesv2.EmploymentType employment_type = 6;
  // id to be sent by client from the screen options, for backward compatibility, BE has logic to fallback to default values in case this value is not passed
  // by the client
  string loan_step_execution_id = 7;
  UserInput user_input = 8;
}

message VerifyDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  string error_response = 2;
  deeplink.Deeplink deeplink = 3;
}

message VerifyCkycDetailsRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_req_id = 3;
}

message VerifyCkycDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetMandateViewDataRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
}

message GetMandateViewDataResponse {
  frontend.header.ResponseHeader resp_header = 1;
  string base64EncodedHtml = 2;
}
message AddNameAndGenderRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  string name = 4;
  api.typesv2.Gender gender = 5;
}

message AddNameAndGenderResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message AddPanAndDobDataRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  string dob = 4;
  string pan = 5;
}

message AddPanAndDobDataResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message CheckLoanEligibilityRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  // if true, cancel any active loan request and start a new one
  // if active loan request is in a non-cancellable state, return appropriate screen
  bool cancel_current_loan_request = 3;
}

message CheckLoanEligibilityResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // returns application status poll screen for success cases
  // for error cases, returns error deeplink
  deeplink.Deeplink deeplink = 2;
}

message FetchCreditReportRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  string loan_step_execution_id = 4;
  api.typesv2.deeplink_screen_option.preapprovedloans.CreditReportVendor credit_report_vendor = 5;
}

message FetchCreditReportResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message AddBankingDetailsRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  BankingDetails banking_details = 4;
  // lse id to be used from screen options
  string lse_id = 5;
  message BankingDetails {
    string account_number = 1 [deprecated = true];
    string account_holder_name = 2;
    string ifsc_code = 3;
    string bank_name = 4;
    oneof account_num {
      string complete_account_number = 5;
      string account_number_prefix = 6;
    }
  }
}

message AddBankingDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message SubmitReviewLoanDetailsRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
}

message SubmitReviewLoanDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetAcqToLendLandingScreenRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
}

message GetAcqToLendLandingScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message UserInput {
  oneof user_input {
    PhoneAndEmailDetails phone_and_email_details = 1;
    AdditionalKycDetails additional_kyc_details = 2;
    SelfieDetails selfie_details = 3;
    FormDetails form_details = 4;
  }
}

message FormDetails {
  repeated api.typesv2.FormField form_fields = 1;
}

message PhoneAndEmailDetails {
  api.typesv2.common.PhoneNumber phone_number = 1;
  string email = 2;
}

message AdditionalKycDetails {
  string employment_type = 1;
  string marital_status = 2;
  string residence_type = 3;
  string father_name = 4;
  string mother_name = 5;
}

message SelfieDetails {
  api.typesv2.common.Image selfie_image = 1;
}

message InitiateSiSetupRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  string loan_account_id = 3;
}

message InitiateSiSetupResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetMandateViewDataV2Request {
  frontend.header.RequestHeader req = 1;
  // loan header, loan_request_id and lse_id to come from screen options
  pal_enums.LoanHeader loan_header = 2;
  string loan_request_id = 3;
  string lse_id = 4;
  // user selected bank account for mandate
  api.typesv2.deeplink_screen_option.preapprovedloans.MandateBankAccountDetails bank_account_details = 5;
  // for choosing the authentication method for initiating mandate
  // used and mandatory field to be used in Fed A2L NTB flow.
  // e.g. debit card, net banking, upi
  // this will be string converted from enum in deeplink screen options
  string authentication_method = 6;
}

message GetMandateViewDataV2Response {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}


message GetPWARedirectionDeeplinkRequest {
  frontend.header.RequestHeader req = 1;

  // loan header
  pal_enums.LoanHeader loan_header = 2;

  // denotes the unique identifier for the loan request/account for which the pwa redirection deeplink needs to be fetched.
  oneof loan_identifier {
    string loan_request_id = 3;
    string loan_account_id = 4;
  }
}

message GetPWARedirectionDeeplinkResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // denotes the deeplink where the user should be redirected to.
  deeplink.Deeplink deeplink = 2;
}

message GetLoanDocumentRequest {
  frontend.header.RequestHeader req = 1;
  // loan header, loan_request_id and lse_id to come from screen options
  pal_enums.LoanHeader loan_header = 2;
  // meta data for loan account identifier
  string loan_account_id = 3;
  // meta data to identify the loan document, to be passed as it is that client gets in screen options
  string document_id = 4;
}

message GetLoanDocumentResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetLoanRepaymentMethodsRequest {
  frontend.header.RequestHeader req = 1;
  // loan header, loan_request_id and lse_id to come from screen options
  pal_enums.LoanHeader loan_header = 2;
  // meta data for loan account identifier
  string loan_account_id = 3;
  // to select the repayment type identifier
  pal_enums.RepaymentDetailsType repayment_details_type = 4;
  api.typesv2.Money amount = 5;
  // the account against which payment is being made, used in flows like LAMF where we have separate accounts for
  // principal outstanding and interest payment
  string loan_account_type_identifier = 8;
}

message GetLoanRepaymentMethodsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message GetIfscCodeSuggestionsRequest {
  frontend.header.RequestHeader req = 1;
  pal_enums.LoanHeader loan_header = 2;
  // query for autocomplete
  string query = 3 [(validate.rules).string.pattern = "(?i)^[a-zA-Z0-9.\\-@\\.& ]+$"];
  pal_enums.SearchIfscType search_ifsc_type = 4;
}

message GetIfscCodeSuggestionsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated Suggestion suggestions = 2;

  message Suggestion {
    // ifsc of the bank
    string ifsc = 1;
    // name of the bank
    string bank_name = 2;
    // branch of the bank
    string bank_branch = 3;
    // district of the bank
    string bank_district = 4;
    // field defines error msg that we need to show with the suggestion in case this suggestion is not available for selection
    // [NOTE]: if this field is not nil, client is expected to show this suggestion disabled.
    api.typesv2.common.Text reason_for_unavailability = 5;
  }
}

message SaveContactDetailsRequest {
  frontend.header.RequestHeader req = 1;
  // loan header, loan_request_id and lse_id to come from screen options
  pal_enums.LoanHeader loan_header = 2;
  string lse_id = 3;
  api.typesv2.common.PhoneNumber phone_number = 4;
}

message SaveContactDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}
