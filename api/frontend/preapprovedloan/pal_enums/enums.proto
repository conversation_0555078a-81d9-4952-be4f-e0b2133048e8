syntax = "proto3";

package frontend.preapprovedloan.pal_enums;

option go_package = "github.com/epifi/gamma/api/frontend/preapprovedloan/pal_enums";
option java_package = "com.github.epifi.gamma.api.frontend.preapprovedloan.pal_enums";

enum LoanProgram {
  LOAN_PROGRAM_UNSPECIFIED = 0;
  LOAN_PROGRAM_PRE_APPROVED_LOAN = 1;
  LOAN_PROGRAM_EARLY_SALARY = 2;
  LOAN_PROGRAM_FLDG = 3;
  LOAN_PROGRAM_FI_LITE_PL = 4;
  LOAN_PROGRAM_FED_REAL_TIME = 5;
  LOAN_PROGRAM_ACQ_TO_LEND = 6;
  LOAN_PROGRAM_LAMF = 7;
  // program to identify real time eligibility check for ETB users
  LOAN_PROGRAM_REAL_TIME_ETB = 8;
  // Distribution program where we do not have pre-approved offers from vendor
  LOAN_PROGRAM_REAL_TIME_DISTRIBUTION = 9;
  // small token personal loan
  LOAN_PROGRAM_STPL = 10;
  LOAN_PROGRAM_REALTIME_SUBVENTION = 11;
  // STPL program with realtime BRE capability
  LOAN_PROGRAM_REALTIME_STPL = 12;
  // generic program for Lending eligibility flows
  LOAN_PROGRAM_ELIGIBILITY = 13;
  LOAN_PROGRAM_NON_FI_CORE_STPL = 14;
  LOAN_PROGRAM_NON_FI_CORE_SUBVENTION = 15;
  LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_V1 = 16 [deprecated = true];
  // program to identify real time eligibility check for NTB users
  LOAN_PROGRAM_REAL_TIME_DISTRIBUTION_NTB = 17;
  //program for early salaryV2
  LOAN_PROGRAM_EARLY_SALARY_V2 = 18;
}

enum LoanType {
  // LoanType specifies the particular kind of loan product.
  // Ex: Personal loan, home loan, etc.
  // Please ensure that there is a one-to-one mapping between frontend &
  // backend loanType enum values, so that the conversion is automatically handled.
  LOAN_TYPE_UNSPECIFIED = 0;
  LOAN_TYPE_PERSONAL = 1;
  LOAN_TYPE_EARLY_SALARY = 2;
  LOAN_TYPE_SECURED_LOAN = 3;
}

enum ComponentName {
  COMPONENT_NAME_UNSPECIFIED = 0;
  COMPONENT_NAME_LOAN_OFFER_DETAILS_SCREEN_V2_LOAN_CONSTRAINTS = 1;
  COMPONENT_NAME_LOAN_OFFER_DETAILS_SCREEN_V2_LOAN_INFO = 2;
  COMPONENT_NAME_APPLICATION_DETAILS_SCREEN_V2_APPLICATION_LOAN_INFO = 3;
  COMPONENT_NAME_APPLICATION_DETAILS_SCREEN_V2_APPLICATION_EMI_INFO = 4;
  COMPONENT_NAME_APPLICATION_DETAILS_SCREEN_V2_APPLICATION_TNC_INFO = 5;
  COMPONENT_NAME_APPLICATION_DETAILS_SCREEN_V2_APPLICATION_BENEFITS_INFO = 6;
}

message LoanHeader {
  pal_enums.LoanProgram loan_program = 1;
  Vendor vendor = 2;
  // Deprecated: Use loan type in GetLandingInfoRequest instead.
  LoanType loan_type = 3;
  // EventData is used to set the event related data like entry point, component identifier etc..
  EventData event_data = 4;
  // owner of data, this field will be used in flows like eligibility where workflow owner and data owner are not necessarily same.
  Vendor data_owner = 5;
}

message EventData {
  // marked deprecated in favour of entry_point_v2
  EntryPoint entry_point = 1 [deprecated = true];
  // component identifier can be used to set the version of the components that leads to lending screen deeplink
  // like design for nudge or widget can be changed, this id will be unique for each such change, and events can be used to track info about which version was active
  string component_identifier = 2;
  // to achieve backward compatibility we updated entry point to string
  string entry_point_v2 = 3;
}

enum Vendor {
  VENDOR_UNSPECIFIED = 0;
  FEDERAL_BANK = 1;
  LIQUILOANS = 2;
  IDFC = 3;
  FIFTYFIN = 4;
  ABFL = 5;
  EPIFI_TECH = 6;
  MONEYVIEW = 7;

  // stock guardian lsp is in scope of LSP
  STOCK_GUARDIAN_LSP = 8;
  LENDEN = 9;
}

enum EntryPoint {
  ENTRY_POINT_UNSPECIFIED = 0;
  // communication
  ENTRY_POINT_PUSH_NOTIFICATION = 1;
  ENTRY_POINT_SMS = 2;
  ENTRY_POINT_EMAIL = 3;
  ENTRY_POINT_WHATSAPP = 8;
  ENTRY_POINT_IN_APP_NOTIFICATION = 4;
  // home entry points
  ENTRY_POINT_HOME_DASHBOARD = 5;
  ENTRY_POINT_HOME_BANNER = 6;
  ENTRY_POINT_HOME_CARD = 7;
  ENTRY_POINT_HOME_WIDGET = 9;
  ENTRY_POINT_HOME_SHORTCUT = 10;
  ENTRY_POINT_HOME_NAV_BAR = 11;
  ENTRY_POINT_HOME_RECENT_ACTIVITY = 12;
  // explore
  ENTRY_POINT_EXPLORE_TAB = 13;
  // search/ Ask Fi
  ENTRY_POINT_SEARCH_OR_ASK_FI = 14;
  // Cx
  ENTRY_POINT_FAQ = 15;
  ENTRY_POINT_CHATBOT = 16;

  // Analyser
  ENTRY_POINT_CREDIT_SCORE_ANALYSER_INSIGHT = 17;
  ENTRY_POINT_SPENDS_ANALYSER_INSIGHT = 18;
  ENTRY_POINT_MF_ANALYSER_INSIGHT = 19;

  // Miscellaneous
  ENTRY_POINT_CATALOG_OFFER_REDEMPTION_SCREEN = 20;

  ENTRY_POINT_SECOND_LOOK_V1 = 21;
}

// Faq Topic to be shown
enum FaqTopic {
  FAQ_TOPIC_UNSPECIFIED = 0;
  FAQ_TOPIC_MIN_MONTHLY_DUE = 1;
  FAQ_TOPIC_LOAN_AMOUNT_CALCULATION = 3;
  FAQ_TOPIC_LMS_AUTO_PAY = 4;
  FAQ_TOPIC_ALTERNATE_CONTACT_NUMBER = 5;
}

enum RepaymentDetailsType {
  REPAYMENT_DETAILS_UNSPECIFIED = 0;
  // when user selects some custom amount
  REPAYMENT_DETAILS_CUSTOM = 1;
  // when user is paying the EMI amount
  REPAYMENT_DETAILS_EMI = 2;
  // when user is closing the loan
  REPAYMENT_DETAILS_LOAN_CLOSURE = 3;
  // when user is paying all the outstanding loan amount
  REPAYMENT_DETAILS_OUTSTANDING = 4;
  // when user is closing loan via cx ticket
  REPAYMENT_DETAILS_LOAN_CLOSURE_VIA_CX = 5;
}

// SearchIfscType is an identifier based on which ifsc search results will be decided.
// In case we want to blacklist/modify certain codes from the response, that can be done based on this enum type.
enum SearchIfscType {
  SEARCH_IFSC_TYPE_UNSPECIFIED = 0;
  SEARCH_IFSC_TYPE_SUBVENTION_MIN_AND_RE_KYC_USERS = 1;
  SEARCH_IFSC_TYPE_FI_ACCOUNT_NOT_OPERATIONAL = 2;
}

enum OtpFlow {
  OTP_FLOW_UNSPECIFIED = 0;
  OTP_FLOW_ADD_ALTERNATE_PHONE_NUMBER = 1;
  OTP_FLOW_E_SIGN = 2;
  OTP_FLOW_CKYC_OTP_VERIFCATION = 3;
}

// LoanParamsSelectionFlow denotes the flow in which the user is selecting loan params like loan amount, tenure etc.
enum LoanParamsSelectionFlow {
  // this denotes the flow where user is selecting the params to start a new application
  LOAN_PARAMS_SELECTION_FLOW_NEW_APPLICATION = 0;
  // this denotes the flow where user is selecting the params based on a revised offer during an application journey
  LOAN_PARAMS_SELECTION_FLOW_ACTIVE_APPLICATION_REVISED_OFFER = 1;
}

enum LoanStepExecutionFlow {
  LOAN_STEP_EXECUTION_FLOW_UNSPECIFIED = 0;
  LOAN_STEP_EXECUTION_FLOW_ELIGIBILITY = 1;
}

enum LoanDocType {
  // (default) Used when there is a single esign document
  LOAN_DOC_TYPE_UNSPECIFIED = 0;
  // Used to generate the KFS document
  LOAN_DOC_TYPE_KFS = 1;
  // Used to generate Loan agreement document
  LOAN_DOC_TYPE_LOAN_AGREEMENT = 2;
}

// For certain lenders, interest and principal payments should be made against separate accounts
// this will be used to differentiate between loan payment account type in a loan payment request
enum LoanPaymentAccountType {
  LOAN_PAYMENT_ACCOUNT_TYPE_UNSPECIFIED = 0;
  // for only interest loan payments
  LOAN_PAYMENT_ACCOUNT_TYPE_INTEREST = 1;
  // for only principal loan payments
  LOAN_PAYMENT_ACCOUNT_TYPE_PRINCIPAL = 2;
  // for all types of loan payments
  LOAN_PAYMENT_ACCOUNT_TYPE_COMMON = 3;
}
