syntax = "proto3";

package frontend.stockguardian.matrix;

import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/frontend/deeplink/deeplink.proto";


option go_package = "github.com/epifi/gamma/api/frontend/stockguardian/matrix";
option java_package = "com.github.epifi.gamma.api.frontend.stockguardian.matrix";


service Matrix {
  rpc SkipCustomerApplicationStage(SkipCustomerApplicationStageRequest) returns (SkipCustomerApplicationStageResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  rpc ResetCustomerApplicationStage(ResetCustomerApplicationStageRequest) returns (ResetCustomerApplicationStageResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };
}

message SkipCustomerApplicationStageRequest {
  frontend.header.RequestHeader req = 1;
  // loan application id
  string application_id = 2;
  // loan application client request id
  string client_req_id = 3;
  // stage to skip, string corresponding to the BE stage enum
  string stage_to_skip = 4;
}

message SkipCustomerApplicationStageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink for the next action redirection on the rpc success
  deeplink.Deeplink next_action_deeplink = 2;
}

message ResetCustomerApplicationStageRequest {
  frontend.header.RequestHeader req = 1;
  // loan application id
  string application_id = 2;
  // loan application client request id
  string client_req_id = 3;
  // stage to reset, string corresponding to the BE stage enum
  string stage_to_reset = 4;
}

message ResetCustomerApplicationStageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink for the next action redirection on the rpc success
  deeplink.Deeplink next_action_deeplink = 2;
}
