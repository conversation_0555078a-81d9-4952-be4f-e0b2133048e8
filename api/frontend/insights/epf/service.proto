syntax = "proto3";

package frontend.insights.epf;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/insights/enums.proto";
import "api/frontend/insights/epf/epf.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/insights/epf/screen_options.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/text_with_hyperlinks.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights/epf";
option java_package = "com.github.epifi.gamma.api.frontend.insights.epf";

service Epf {
  // DiscoverUANs is used to discover all uan accounts for a given phone number
  rpc DiscoverUANs (DiscoverUANsRequest) returns (DiscoverUANsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;    // skipping device reg to enable for fi lite
  }
  // GetConnectUANsDashboard returns all uans linked with a phone number with their connect status (connect, connected, processing, failed)
  rpc GetConnectUANsDashboard (GetConnectUANsDashboardRequest) returns (GetConnectUANsDashboardResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;    // skipping device reg to enable for fi lite
  }
  // GenerateOtp generates otp to fetch a given uan account by uan_number
  // otp is sent to phone number registered with the uan
  rpc GenerateOtp (GenerateOtpRequest) returns (GenerateOtpResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;    // skipping device reg to enable for fi lite
  }
  // VerifyOtp verifies the input otp and fetches and stores uan account details (if otp is correct)
  rpc VerifyOtp (VerifyOtpRequest) returns (VerifyOtpResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;    // skipping device reg to enable for fi lite
  }

  // GetEpfLandingPage fetches the EPF landing page
  rpc GetEpfLandingPage (GetEpfLandingPageRequest) returns (GetEpfLandingPageResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // InitiateEpfImportSession creates the new epf import session and redirects to confirm phone number screen
  rpc InitiateEpfImportSession (InitiateEpfImportSessionRequest) returns (InitiateEpfImportSessionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message GetEpfLandingPageRequest {
  frontend.header.RequestHeader req = 1;
}

message GetEpfLandingPageResponse {
  // In case of no UANs, RecordNotFound RPC Status will be returned and user needs to be redirected to `redirection_deeplink`
  frontend.header.ResponseHeader resp_header = 1;
  repeated frontend.insights.epf.LandingPageComponent landing_page_components = 2;
  frontend.insights.epf.EpfSummary epf_summary = 3; // EPF Summary component.

  // redirection_deeplink will be populated in case of no UANs (status: RecordNotFound),
  // and will be nil in all other cases.
  deeplink.Deeplink redirection_deeplink = 4;
}

message DiscoverUANsRequest {
  frontend.header.RequestHeader req = 1;
  // pan can be added to get_by later when required
  oneof get_by {
    api.typesv2.common.PhoneNumber phone_number = 2;
  }
  // epf import session id
  string epf_import_session_id = 3;
}

message DiscoverUANsResponse {
  // fullScreenErrorView is returned in case of no uan found (https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=304-16531&mode=design&t=W0TWBpcMefRfg6BC-4)
  // fullScreenErrorView for any any other not ok status (https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=393-34327&mode=design&t=W0TWBpcMefRfg6BC-4)
  frontend.header.ResponseHeader resp_header = 1;
  // in case of OK status, redirection can be to ConnectUANsDashboard (if there are more than 1 uan)
  // or if there is only 1 uan then directly to Otp screen
  deeplink.Deeplink redirection_deeplink = 2;
}

message GetConnectUANsDashboardRequest {
  frontend.header.RequestHeader req = 1;
  // parameter to filter uan accounts (pan can be added later)
  oneof get_by {
    // get all uan accounts for this phone number
    api.typesv2.common.PhoneNumber phone_number = 2;
  }
  // source defines the source of the request
  // the text and content varies basis the source
  UanListScreenSource source = 3;
  // epf import session id
  string epf_import_session_id = 4;
}

// figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=1-5405&mode=design&t=W0TWBpcMefRfg6BC-4
message GetConnectUANsDashboardResponse {
  // in case of failure, a not ok status is sent with a bottom sheet error view
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text subtitle = 3;
  // list of uan accounts with their connection status (connect, connected, processing, failed)
  repeated ConnectUANWidget connect_uan_widgets = 4;
  // bottom button with text, bg color, action on tap
  Button bottom_button = 5;
  // bottom footer to connect uan manually
  // Deprecated: use connect more deeplink instead
  repeated api.typesv2.ui.IconTextComponent footer = 6 [deprecated = true];
  // bottom text with hyperlink to connect more uan accounts manually
  // https://drive.google.com/file/d/1k24JKoKCk_UwBQM4QuRsModriQnf5l9G/view?usp=drive_link
  api.typesv2.ui.TextWithHyperlinks connect_more_deeplink = 7;
}

message Button {
  // button text (with properties like color, font style)
  // bg_color determines the color of button
  api.typesv2.common.Text text = 1;
  // redirection on tapping button (e.g. redirection to net worth screen)
  deeplink.Deeplink redirection = 2;
}

// figma: https://www.figma.com/file/IhigcxQ5Hfg1LgaD0zPCZ6/Net-Worth-%E2%80%A2-FFF?type=design&node-id=1-5414&mode=design&t=W0TWBpcMefRfg6BC-4
message ConnectUANWidget {
  api.typesv2.common.VisualElement left_icon = 1;
  api.typesv2.ui.IconTextComponent uan_number = 2;
  // status tag is below uan_number with status of uan (processing, failed, connected, etc)
  api.typesv2.ui.IconTextComponent status_tag = 3;
  // info can be a text or lottie/image (visual_component)
  api.typesv2.ui.IconTextComponent connect_info = 4;
  // action on tapping the connect uan widget
  deeplink.Deeplink deeplink = 5;
}

message GenerateOtpRequest {
  frontend.header.RequestHeader req = 1;
  string uan_number = 2 [(validate.rules).string.min_len = 1];
  // client_req_id is a uuid generated by client. This need to be unique every time a generate otp request
  // is made, even in case of retrying for same uan
  string client_req_id = 3 [(validate.rules).string = {min_len: 1, max_len: 50}];
  // is_consent_taken is true if user was shown consent text
  bool is_consent_taken = 4;
  // epf import session id
  string epf_import_session_id = 5;
}

message GenerateOtpResponse {
  enum Status {
    // Request has been processed successfully
    OK = 0;
    // internal server error
    INTERNAL = 13;
    // uan is not yet activated for the given phone number
    NO_ACCOUNT_ATTACHED = 101;
    // epf service unavailable from vendor
    SERVICE_UNAVAILABLE = 102;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // text to display on enter otp screen (text contains masked phone number where otp is sent)
  // e.g. "Enter the OTP sent to +91 ••••• ••49 to connect UAN **********"
  api.typesv2.ui.IconTextComponent info_text = 2;
  // if not ok status, client displays the failure footer text on otp screen
  // in case of success, this field is nil
  // if the status is resource_exhausted, client appends a retry button to the failure_footer_text
  api.typesv2.ui.IconTextComponent failure_footer_text = 3;
}

message VerifyOtpRequest {
  frontend.header.RequestHeader req = 1;
  string otp = 2;
  // client_req_id sent here need to be the same as sent in GenerateOtp call
  string client_req_id = 3;
  // epf import session id
  string epf_import_session_id = 4;
}

message VerifyOtpResponse {
  enum Status {
    // Request has been processed successfully
    OK = 0;

    // internal server error
    INTERNAL = 13;

    // passbook request status mismatch status
    PASSBOOK_STATUS_MISMATCH = 101;
  }
  // in case of failure, a not ok status is sent with a bottom sheet error view
  frontend.header.ResponseHeader resp_header = 1;
  // in success case (ok status), redirect can be to connect uan dashboard (in case of multiple uans)
  // or directly to net worth screen if only 1 uan
  deeplink.Deeplink redirection_deeplink = 2;
}

message InitiateEpfImportSessionRequest {
  frontend.header.RequestHeader req = 1;
  api.typesv2.deeplink_screen_option.insights.epf.InitiateEpfImportSessionRequestParams request_params = 2;
}

message InitiateEpfImportSessionResponse {
  // in case of failure, a not ok status is sent with a bottom sheet error view
  frontend.header.ResponseHeader resp_header = 1;
  // in success case (ok status), redirect can be to confirm phone number screen
  deeplink.Deeplink redirection_deeplink = 2;
}
