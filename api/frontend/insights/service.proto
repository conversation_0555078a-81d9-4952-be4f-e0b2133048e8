syntax = "proto3";

package frontend.insights;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/insights/enums.proto";
import "api/frontend/insights/insight_data_elements.proto";
import "api/rpc/method_options.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights";
option java_package = "com.github.epifi.gamma.api.frontend.insights";

service Insights {
  // RPC method to fetch insights from server and return to the client
  // RPC does not return insight every time (to maintain the rarity factor)
  rpc GetInsight (GetInsightRequest) returns (GetInsightResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
  }

  // RPC method to update engagement details for the insight served to user
  // RPC is idempotent in general
  // When the client submits a different engagement detail (Rating), last engagement will overwrite the existing one
  // Client to use this request for 2 different use-cases
  // 1. <PERSON><PERSON> is expected to send a UpdateInsightEngagementDetailsRequest with mark_noticed set to true and Rating as unspecified
  // after x seconds to mark the status of insight engagement to NOTICED
  // 2. When a user rates (positive/negative), client sends UpdateInsightEngagementDetailsRequest with Rating (positive or negative),
  // this marks the status of insight engagement to ACTED_ON and also save the rating provided in request
  // Note: In case the user provides Rating first i.e. 2nd request happens before 1st, client can chose not to make the 1st request since it
  // longer affects the state of engagement action (ACTED_ON is final state for any engagement)
  rpc UpdateInsightEngagementDetails (UpdateInsightEngagementDetailsRequest) returns (UpdateInsightEngagementDetailsResponse) {
    option (rpc.auth_required) = true;
  }

}

// InsightContext to determine what insights to show. (TODO: add more contexts in further phases e.x. location, time, etc)
message InsightContext {
  // REQUIRED. Screen on which insight will be shown.
  frontend.deeplink.Screen screen_name = 1;
}

message GetInsightRequest {
  frontend.header.RequestHeader req = 1;
  InsightContext insight_context = 2;
}

// Custom cta object with cta_text object and not a string
message Cta {
  TextElement cta_text = 1;
  frontend.deeplink.Deeplink deeplink = 2;
}

// Data of each field of an insight and the card type that can vary based on channel e.g., AppScreen, Whatsapp
message InsightData {
  TextElement title = 1;
  TextElement body = 2;
  string icon_url = 3;
  TextElement source_info = 4;
  Cta cta = 5;
  InsightCardType insight_card_type = 6;
}

message GetInsightResponse {
  frontend.header.ResponseHeader resp_header = 1;

  enum Status {
    OK = 0;

    // The client specified an invalid argument.
    // ex. when client sends an updateInsightEngagement request with mark_noticed as false and also Rating is unspecified
    INVALID_ARGUMENT = 3;

    // ex. status used when server has no available insight to serve to client
    NOT_FOUND = 5;

    // Used when client requests for an insight but has already exhausted the max Insight to be served in a session/day limit.
    RESOURCE_EXHAUSTED = 8;

    // Internal Server Error
    INTERNAL = 13;

    UNAUTHENTICATED = 16;
  }

  // Used in feedback for this insight (determines framework id, segment id and template id used to generate this insight)
  string engagement_id = 2;

  InsightData insight_data = 3;

  // Time in seconds after which the client sends an UpdateInsightEngagement request to mark the status of insight engagement to NOTICED
  int32 mark_noticed_after = 4;
}


message UpdateInsightEngagementDetailsRequest {
  frontend.header.RequestHeader req = 1;

  // Used in feedback for this insight (determines framework id, segment id and template id used to generate this insight)
  string engagement_id = 2;

  // Client is expected to initiated this request after Insight is noticed for `x` seconds (mark_noticed_after)
  // Value of x will be part of the the `GetInsights` response
  bool mark_noticed = 3;

  // Rating (Positive / Negative) from the user for the insight served
  Rating rating = 4;
}

message UpdateInsightEngagementDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  enum Status {
    OK = 0;

    // The client specified an invalid argument.
    // ex. when client sends an updateInsightEngagement request with mark_noticed as false and also Rating is unspecified
    INVALID_ARGUMENT = 3;

    // ex. status used when server has no available insight to serve to client
    NOT_FOUND = 5;

    // Used when client requests for an insight but has already exhausted the max Insight to be served in a session/day limit.
    RESOURCE_EXHAUSTED = 8;

    // Internal Server Error
    INTERNAL = 13;

    UNAUTHENTICATED = 16;
  }
}


