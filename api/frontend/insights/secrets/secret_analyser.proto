syntax = "proto3";

package frontend.insights.secrets;

import "api/frontend/analyser/service.proto";
import "api/frontend/analyser/visualisation.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/insights/secrets/secret_landing_page.proto";
import "api/frontend/investment/ui/investment_activity.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/insights/secrets/component/secret_components.proto";
import "api/typesv2/ui/border_property.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/insights/secrets/secrets.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights/secrets";
option java_package = "com.github.epifi.gamma.api.frontend.insights.secrets";

message AddSecretToFavouritesRequestParams {
  string secret_id = 1;
}

message SecretAnalyser {
  SecretAnalyserHeader header = 1;
  repeated SecretAnalyserComponent component_list = 2;
  SecretAnalyserFooter footer = 3;
  api.typesv2.deeplink_screen_option.insights.secrets.component.SecretsAnimationDetails reveal_animation_details = 4;
  string bg_color = 5;
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16176-32027&t=EsQ3Ld0ZMnAleBrx-4
message SecretAnalyserHeader {
  // Money Secrets
  api.typesv2.common.Text title = 1;
  // Star icon
  // Client to call 'AddSecretToFavourites' with 'secret_favourite_req' in the request
  // Client also needs to update the icon based on the response from 'AddSecretToFavourites' api
  api.typesv2.common.VisualElement favourite_icon = 2;
  AddSecretToFavouritesRequestParams secret_favourite_req = 3;
  // [Deprecated] In favour of static share icon with default text that needs to be sent with the screenshot
  // Client needs to take screenshot and open bottom sheet for sharing on clicking
  api.typesv2.common.VisualElement share_icon = 4;

  // One of is added if we want to move to a api based share functionality
  oneof share_icon_v2 {
    // this will just be static type share icon with default text that needs to be shared with the screenshot
    SecretAnalyserShareStaticIcon share_static_icon = 5;
  }
}

message SecretAnalyserShareStaticIcon {
  // Client needs to take screenshot and open bottom sheet for sharing on clicking
  api.typesv2.common.VisualElement share_icon = 1;
  // Default text that needs to be sent with the screenshot
  string share_text = 2;
}

message SecretAnalyserComponent {
  oneof component {
    SecretsAnalyserCard analyser_card = 1;
    // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16175-29793&t=EsQ3Ld0ZMnAleBrx-4
    LineItemCardWithAnalyserFilter line_item_group = 2;
    // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16208-38679&t=ezdoYaNva4ic5RW8-4
    TextExplainerSection text_explainer_section = 3;
    // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16174-29092&t=ymTihgOkWmZf1Pi1-0
    LearnOnTheGoSection learn_on_the_go_section = 5;
    // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20385-34477&t=mFH0i9ItNWwNtcgg-4
    ActionableBanner actionable_banner = 6;
    // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68069-73085&t=T5EO8xnSIdfzK6Nu-4
    ActionableBannerV2 actionable_banner_v2 = 7;
    // https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/%F0%9F%9B%A0%EF%B8%8F-Wealth-builder-2.0?node-id=7579-17018&t=KrsUaOGWONuzLz2z-4
    SecretCollectionComponent secret_collection_component = 8;
  }
  // name of the component. this maps directly with secret builder config
  // client should use component name while emitting events for component interaction
  string component_name = 4;
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16174-29094&t=EsQ3Ld0ZMnAleBrx-4
// The main Analyser card for a secret
message SecretsAnalyserCard {
  oneof visualisation {
    // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16171-24622&t=Ha5X0yvR6oojxBKI-4
    api.typesv2.ui.insights.secrets.NumberCard number_card = 1;
    // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16171-24623&t=EsQ3Ld0ZMnAleBrx-4
    api.typesv2.ui.insights.secrets.ImageCard image_card = 2;
    //https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16171-24615&t=EsQ3Ld0ZMnAleBrx-4
    api.typesv2.ui.insights.secrets.BarChartCard bar_chart_card = 3;
    // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16177-37087&t=EsQ3Ld0ZMnAleBrx-4
    api.typesv2.ui.insights.secrets.MultiLineChartCard multi_line_chart_card = 4;
    // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16175-29400&t=i81dLy318sIAgleI-4
    // Used for credit score card
    SecretsSpiderGraph spider_graph_card = 5;
    // Used for Donut card
    api.typesv2.ui.insights.secrets.DonutCard donut_card = 6;
    // Used for Area Chart Card
    api.typesv2.ui.insights.secrets.AreaChartCard area_chart_card = 8;
    // https://www.figma.com/design/kPyyq85DJiSTgZzE2vDCDT/Crore.club?node-id=555-20436&t=d1y4K7GZwxlyzFCV-4
    // Used for Investment Consistency Calendar
    api.typesv2.ui.insights.secrets.GridVisualisationCard grid_visualisation_card = 9;
  }
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16177-35716&t=EsQ3Ld0ZMnAleBrx-4
  // ITC attached with the above card
  api.typesv2.ui.IconTextComponent pro_tip = 7;
}

// TODO: This is added to launch reusing the older analyser filter handling at client and backend. When time permits
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16175-29793&t=EsQ3Ld0ZMnAleBrx-4
message LineItemCardWithAnalyserFilter {
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16177-38561&t=EsQ3Ld0ZMnAleBrx-4
  message FilterSection {
    // Values shown for each 'filter'
    repeated FilterParameter filter_params = 1;
    // On clicking any of the above ITCs, Client needs to pop up this 'Filter Widget'
    string bg_color = 2;
  }

  message FilterParameter {
    // Display value shown for each 'filter'
    // '3 Accounts' 'Duration: 01 Jan' etc
    api.typesv2.ui.IconTextComponent display_value = 1;
    // On clicking any of the above filter_params, client needs to pop up a sheet with the filter widget's for all the filter_params
    analyser.FilterWidget filter_widget = 2;
  }

  FilterSection filter_section = 1;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16175-29793&t=EsQ3Ld0ZMnAleBrx-4
  // 'TRANSACTIONS'
  api.typesv2.common.Text left_aligned_heading = 2;
  // 'AMOUNT'
  api.typesv2.common.Text right_aligned_heading = 3;
  repeated frontend.investment.ui.LineItem line_items = 4;
  // https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16328-14805&t=EsQ3Ld0ZMnAleBrx-4
  // To be hidden if not present
  api.typesv2.ui.IconTextComponent see_more = 5;
  // Limit of line items to be seen when the user lands on the secrets page
  int32 items_limit_on_landing = 6;
  // Limit of line items to be seen when the user taps on see more
  // Keeping it here not to overwhelm the user by showing all the data at once on tapping on see more
  // We can keep a limit to show how many we want to show on tapping on show more just like we do on pagination
  // If this value is zero we will show all the items on tapping see more
  int32 items_limit_on_see_more = 7;
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16208-38679&t=ezdoYaNva4ic5RW8-4
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20388-36979&t=BrI8BABHxnZIivAo-4
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20385-34871&t=qr3BnYxY7QJa3RDI-4
message TextExplainerSection {
  // 'JUST FYI'
  api.typesv2.ui.IconTextComponent title = 1;
  repeated api.typesv2.common.Text description = 2;
  string bg_color = 3;
  // corner radius for the container
  int32 corner_radius = 4;
  string border_color = 5;
  message ExpandableControl {
    // Marks the default state
    bool is_expanded = 1;
    // chevron image to show the collapsed state of card. The same image will be rotated when the card expands
    api.typesv2.common.Image chevron_image = 2;
  }
  // This is an optional field. Will be populated only if the card is collapsible
  ExpandableControl card_expandable_control = 6;
  repeated api.typesv2.ui.IconTextComponent ctas = 7;
}

message SecretAnalyserFooter {
  string bg_color = 1;
  repeated api.typesv2.ui.IconTextComponent tiles = 2;
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16175-29400&t=i81dLy318sIAgleI-4
// Used for credit score card
// We are creating this here, due to dependency on 'analyser.SpiderWebParams'
// If needed elsewhere we should move this to 'api.typesv2.ui.insights.secrets' package after removing dependency on analyser
message SecretsSpiderGraph {
  // 'Your credit score'
  // '840'
  api.typesv2.ui.VerticalKeyValuePair title_value_pair = 1;
  // '+4 points improved this month'
  api.typesv2.ui.IconTextComponent score_insight = 2;
  analyser.SpiderWebParams spider_web_params = 3;
  // 'powered by 'Experian'
  api.typesv2.ui.IconTextComponent footer = 4;
  string bg_color = 5;
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=16174-29092&t=ymTihgOkWmZf1Pi1-0
message LearnOnTheGoSection {
  api.typesv2.common.Text title = 1;
  repeated LearnOnTheGoSectionItem section_items = 2;
}

message LearnOnTheGoSectionItem {
  api.typesv2.common.VisualElement icon = 1;
  // The label that overlaps on the icon bottom ex: 10K VIEWS
  api.typesv2.ui.IconTextComponent info_tag = 2;
  api.typesv2.common.Text description = 3;
  // The color of the outer ring of the item
  api.typesv2.common.ui.widget.BackgroundColour overlay_colour = 4;
  frontend.deeplink.Deeplink deeplink = 5;
  string bg_color = 6;
}

// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20385-34477&t=mFH0i9ItNWwNtcgg-4
// https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=20385-34476&t=mFH0i9ItNWwNtcgg-4
message ActionableBanner {
  // Left icon
  api.typesv2.common.VisualElement left_image = 1;
  // Description eg. Your result can be incorrect. Sync accounts again.
  api.typesv2.common.Text description = 2;
  // Right component
  api.typesv2.ui.IconTextComponent right_component = 3;
  int32 corner_radius = 4;
  string bg_color = 5;
  frontend.deeplink.Deeplink deeplink = 6;
  // these are the properties we need for actionable banner like cta
  map<string, string> event_properties = 8;
  api.typesv2.ui.BorderProperty border_property = 9;
}

// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68069-73085&t=T5EO8xnSIdfzK6Nu-4
message ActionableBannerV2 {
  // text to be shown on the banner
  api.typesv2.common.Text text = 1;
  // image to be shown on the right side of the banner
  api.typesv2.common.Image image = 2;
  // background color of the banner
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  // corner radius of the banner
  int32 corner_radius = 4;
  // deeplink to go when the banner is clicked
  frontend.deeplink.Deeplink deeplink = 6;
  // cta to be shown on the banner
  api.typesv2.ui.IconTextComponent cta = 7;
  // these are the properties we need for banner like cta
  map<string, string> event_properties = 8;
}
