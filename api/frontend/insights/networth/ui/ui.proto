syntax = "proto3";

package frontend.insights.networth.ui;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/insights/networth/enums/enums.proto";
import "api/frontend/investment/ui/landing_page.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/border_property.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/properties/visual_properties.proto";
import "api/typesv2/ui/sdui/sections/section.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights/networth/ui";
option java_package = "com.github.epifi.gamma.api.frontend.insights.networth.ui";

// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46664&t=Zhxqm8mShovbvbVH-4
// Ref: https://drive.google.com/file/d/1mIzDVbBr8T3oEBdhyqhxWcA2wUXjdw7D/view?usp=drive_link
message WealthBuilderLandingComponent {
  WealthBuilderLandingDashboard wealth_builder_landing_dashboard = 1;
  // primary cta at the footer of component, e.g. "Track bank balances", "Get Mutual Funds summary"
  // DEPRECATED : In support of  wealth_builder_landing_dashboard.footer_ctas [WealthBuilderLandingCta]
  api.typesv2.ui.IconTextComponent footer_cta = 2 [deprecated = true];
  // bg color of the dashboard
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 3;
}

// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/%F0%9F%9B%A0%EF%B8%8F-Wealth-builder-2.0?node-id=8122-14943&t=syacNv3WDBOjOMCS-4
// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/%F0%9F%9B%A0%EF%B8%8F-Wealth-builder-2.0?node-id=8067-33174&t=syacNv3WDBOjOMCS-4
// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/%F0%9F%9B%A0%EF%B8%8F-Wealth-builder-2.0?node-id=8122-15146&t=syacNv3WDBOjOMCS-4
message WealthBuilderLandingCta {
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
  int32 corner_radius = 2;
  api.typesv2.ui.sdui.properties.BorderProperty border_property = 3;
  api.typesv2.common.VisualElement icon = 4;
  api.typesv2.common.Text title = 5;
  api.typesv2.ui.IconTextComponent tag = 6;
  bool is_primary = 7; // If the cta is primary cta, then cta will take remaining space otherwise will wrap
  deeplink.Deeplink deeplink = 8;
  // Identifier based on this client will decide whether the user has already seen the banner for this cta or not
  string identifier = 9;
  // If nil, client won't draw any arrow.
  // Note: There should be one arrow color and arrow color should be present if footer_banner is present
  api.typesv2.common.ui.widget.BackgroundColour arrow_color = 10;
}

// Ref: https://drive.google.com/file/d/1RGgTZd-dwqLGa-kdyhWtP7INQGbDNvYr/view?usp=drive_link
message WealthBuilderLandingDashboard {
  // dashboard header,
  // contains either zero state when no assets are connected
  // or contains section title, money value and other details when any single asset is connected
  DashboardHeader dashboard_header = 1;
  repeated WealthBuilderLandingSection wealth_builder_landing_sections = 2;
  // to show at bottom of the section, used to expand and collapse the dashboard
  // this will only be shown when user has more than 5 assets connected
  CollapsibleDetails collapsible_details = 3;
  // more cta to show at bottom of dashboard e.g. add more cta
  repeated api.typesv2.ui.IconTextComponent action_cta = 4;
  // bg color of the component
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 5;
  // corner radius of the component
  // DEPRECATED : As going forward we will be not showing any rounded corner.
  double corner_radius = 6 [deprecated = true];
  // Footer ctas
  repeated WealthBuilderLandingCta footer_ctas = 7;
  // Footer banner
  api.typesv2.ui.sdui.sections.Section footer_banner = 8;
}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-47472&t=Zhxqm8mShovbvbVH-4
message WealthBuilderLandingSection {
  // This will be the unique identifier for the section. Each widget will be specific to a section. Caching of a widget should be done with the section.
  // If section id is updated then all the widgets that are cached in the section should be invalidated.
  string id = 1;
  // networth section type, asset or liability
  enums.NetworthSectionType section_type = 2;
  // header of the section, contains title, money value and other details
  SectionHeaderGenericState section_header_generic_state = 3;
  // list of all widgets in the section
  repeated WidgetV2 widgets = 4;
}

message DashboardHeader {
  oneof dashboard_header {
    DashboardHeaderZeroState section_header_zero_state = 1;
    DashboardHeaderConnectedState section_header_connected_state = 2;
  }
}

// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-45905&t=sJQwPE7SVHainEDH-4
// Ref: https://drive.google.com/file/d/1nocV-YhLQb7DQ9bQNct50pMKn67LGg4Z/view?usp=drive_link
message DashboardHeaderZeroState {
  api.typesv2.common.Text title = 1;
  // cta for video to play stating "How this works"
  api.typesv2.ui.IconTextComponent action_cta = 2;
}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46669&t=sJQwPE7SVHainEDH-4
// Ref: https://drive.google.com/file/d/1cko0WMZs8hSQIqdOhOKkWEZ-pN0-8Esn/view?usp=drive_link
message DashboardHeaderConnectedState {
  // title of the section including info icon (info icon is optional)
  api.typesv2.ui.IconTextComponent title = 1;
  // money details, contains money value and rupee symbol for display along with raw money value
  WealthDisplayDetails wealth_display_details = 2;
  // tags to show at the right of money value, contains daily percentage increase and refresh tags etc.
  repeated api.typesv2.ui.IconTextComponent tags = 3;
  // show/hide details of the component, includes show/hide icon
  VisibilityDetails visibility_details = 4;
}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46117&t=qVfYTMo2zXmi7a8g-4
// Ref: https://drive.google.com/file/d/1p4tpp-05ylud2s7hH_3wXpzXwh7LN3zx/view?usp=drive_link
message SectionHeaderGenericState {
  // title of the section including info icon (info icon is optional)
  api.typesv2.ui.IconTextComponent title = 1;
  // money details, contains money value and rupee symbol for display along with raw money value
  WealthDisplayDetails wealth_display_details = 2;
  // toggle for liabilities
  investment.ui.AssetLandingToggleComponent toggle_component = 3;
}

message WidgetV2 {
  // unique identifier for the widget. This is to be used by client for caching.
  string id = 1;
  // type of widget that should be rendered.
  WidgetTypeV2 type = 2;
  oneof params {
    // parameters for wealth builder landing widget
    // used only in case of WIDGET_TYPE_WEALTH_BUILDER type
    WealthBuilderLandingWidgetParams wealth_landing_widget_params = 3;
  }
  // This enum will tell if client should use the widget from cache or not.
  CacheControlV2 cache_control = 4;
  // State of widget i.e if it is uninitialized, initialized etc.
  WidgetStateV2 state = 5;
  // payload to be sent in analytics event from client
  // if cache_control is set to use cache, use the payload as well from cache
  string widget_analytics_payload = 6;
  // Value to be used for calculating the total section value and net worth. This can be negative in case of liabilities.
  api.typesv2.Money total_value = 7;
}

enum WidgetTypeV2 {
  WIDGET_TYPE_V2_UNSPECIFIED = 0;
  WIDGET_TYPE_V2_WEALTH_BUILDER = 1;
}

enum CacheControlV2 {
  //  // If cache control is unspecified then client should not use cache
  CACHE_CONTROL_V2_UNSPECIFIED = 0;
  //  // Client is supposed to read from cache for overall computation.
  CACHE_CONTROL_V2_USE_CACHE = 1;
}

enum WidgetStateV2 {
  WIDGET_STATE_V2_UNSPECIFIED = 0;
  //  // This refers to the state where user had not successfully completed the category process.
  WIDGET_STATE_V2_UNINITIALIZED = 1;
  //  // This refers to the state where the user has completed the category process successfully. The data can be zero or contain some value.
  //  // If a widget is in initialised state and client has to use cache, then it should count this widget in category added.
  WIDGET_STATE_V2_INITIALIZED = 2;
}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-46684&t=sJQwPE7SVHainEDH-4
// Ref: https://drive.google.com/file/d/1HXnf1NZ5yNRIwxT0vsRUNx3WPLQzx2Jd/view?usp=sharing
message WealthBuilderLandingWidgetParams {
  // weight of card specifies the width of the card in percentage
  float weight = 1;
  // card title
  api.typesv2.common.Text widget_title = 2;
  // money details, contains money value and rupee symbol for display along with raw money value
  WealthDisplayDetails wealth_display_details = 3;
  // tag to show at the right of money value
  api.typesv2.ui.IconTextComponent right_tag = 4;
  // tag to show at the bottom of the widget
  api.typesv2.ui.IconTextComponent bottom_tag = 5;
  // Icon to show at bottom right corner of card
  api.typesv2.common.VisualElement bottom_right_icon = 6;
  // deeplink to open when user clicks on the widget, this will be for the entire card
  deeplink.Deeplink deeplink = 7;
  // bg color of the widget
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 8;
  // border properties including border style
  api.typesv2.ui.BorderProperty border_property = 9;
  // top right nudge to show on widget
  api.typesv2.common.VisualElement top_right_nudge = 10;
}

message CollapsibleDetails {
  // show more cta to show at the bottom of the section, this will only be shown when user has more than 5 assets connected
  // tapping on this will expand the section to show all asets
  api.typesv2.ui.IconTextComponent show_more_cta = 1;
  // tapping on this will collapse the section to show only 5 assets
  api.typesv2.ui.IconTextComponent show_less_cta = 2;
}

message WealthDisplayDetails {
  // money symbol in rupee (₹)
  api.typesv2.common.Text currency_symbol = 1;
  // total asset value display text
  // Total value. The value should be overridden by client and formatting should be used as it is.
  api.typesv2.common.Text total_display_value = 2;
  // Value to be used for calculating the total section value and net worth. This can be negative in case of liabilities.
  api.typesv2.Money total_value = 3;
}

message VisibilityDetails {
  api.typesv2.common.VisualElement hide = 1;
  api.typesv2.common.VisualElement show = 2;
}
