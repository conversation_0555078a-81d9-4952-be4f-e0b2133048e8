syntax = "proto3";

package frontend.usstocks;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/usstocks/account_activity.proto";
import "api/frontend/usstocks/enums.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";


option go_package = "github.com/epifi/gamma/api/frontend/usstocks";
option java_package = "com.github.epifi.gamma.api.frontend.usstocks";


message WalletPage {
  // page title is displayed at the top of the screen
  // eg: Your USD Wallet
  api.typesv2.ui.IconTextComponent page_title = 1;
  // Wallet section is displayed right below the page title.
  // It contains info about user's US Stocks account wallet balance and
  // provides options to add/withdraw funds.
  Wallet wallet = 2;
  // bottom section of the wallet page.
  WalletPageBottomSection bottom_section = 3;
  // icon text component for the faq icon on wallet add funds page
  api.typesv2.ui.IconTextComponent faq_icon = 4;
}

message Wallet {
  // Wallet image to be shown above balance.
  api.typesv2.common.VisualElement wallet_icon = 1;
  // Wallet balance sections displays current balance present in the wallet in both USD and INR
  // Key will be used to show wallet balance in USD. text to be displayed in larger font.
  // Value will be used to show wallet Balance in INR. It will also contain an info icon and a deeplink to open a info popup screen
  // as shown in this figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-39440&mode=design&t=VHoHJm7HnaCF6dSz-0
  // wallet balance figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-35008&mode=design&t=VHoHJm7HnaCF6dSz-0
  api.typesv2.ui.VerticalKeyValuePair wallet_balance = 2;
  // footer_cta is a CTA to be displayed at the bottom of the wallet section
  // eg: To buy your favourite US stocks, transfer funds
  api.typesv2.ui.IconTextComponent footer = 3;
  api.typesv2.ui.IconTextComponent add_funds_cta = 4;
  api.typesv2.ui.IconTextComponent withdraw_funds_cta = 5 [deprecated = true];
  WithdrawFundsInfo withdraw_funds_info = 6;
}

message WithdrawFundsInfo {
  api.typesv2.ui.IconTextComponent cta = 1;
  // Bottom sheet designed to encourage users to reconsider withdrawing funds.
  // Note: if the bottom_sheet is nil then client should redirect according to CTA irrespective of bottom_sheet_display_interval_in_seconds value
  deeplink.Deeplink bottom_sheet = 2;
  // Specify the duration in seconds after which the client will redirect to the "bottom_sheet" screen upon clicking the CTA.
  // Example: Redirect to the "bottom_sheet" screen once every two weeks.
  int64 bottom_sheet_display_interval_in_seconds = 3;
}

message WalletPageBottomSection {
  repeated WalletPageBottomSectionComponent components = 1;
}

message WalletPageBottomSectionComponent {
  oneof component {
    WalletPageBannerCollection banners = 1;
    // list of past wallet activities for the user.
    // Deprecated in favor of AccountActivities
    AccountActivities wallet_activities = 2 [deprecated = true];
    // can be used to show a text section
    // currently will be used to show footer text
    api.typesv2.ui.IconTextComponent text_component = 3;
    WalletActivityZeroState wallet_zero_state = 4;
    // Used for showing the tabs currently would contain latest and upcoming tabs.
    // Also, encompasses the activities that each of this tab contains as well
    // Latest activity | Upcoming
    AccountActivityTabList activity_tab_list = 5;
  }
}

// WalletPageBannerCollection is used to show a list of banners.
// Client needs to fetch the banners by calling dynamic elements rpc.
message WalletPageBannerCollection {}

message AccountActivityTabList {
  repeated AccountActivityTabInfo activity_tab_infos = 1;
  // represent bg color during tab selected by user
  string select_bg_color = 2;
  // represent bg color for tab unselected by user
  string bg_color = 3;
}

message AccountActivityTabInfo {
  // text to be show if tab is unselected
  api.typesv2.ui.IconTextComponent text = 1;
  // text to be show if tab is selected
  api.typesv2.ui.IconTextComponent selected_text = 2;
  // if the tab is selected or not
  bool is_selected = 3;
  // list of activities for the tab
  AccountActivities activities = 4;
}

// WalletActivityZeroState introduces users to the Wallet and how wallet transfers work.
// This will be shown to users who don't have any past wallet activity.
// figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-36304&mode=design&t=gJA8e0O2Xn1AVdpt-0
message WalletActivityZeroState {
  api.typesv2.ui.IconTextComponent title = 1;
  repeated api.typesv2.ui.IconTextComponent info_tiles = 2;
}

// figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-36697&mode=design&t=OAa8FxPM5nECXshD-0
message WalletAddFundsPage {
  // Details required for rendering the amount input section.
  // User has option to input amount in both USD and INR. Client sends the amount entered for
  // validation to BE and displays the error (if any) to the user.
  // It also has a amount suggestion bar from which user can directly choose the amount to be added.
  WalletAddFundsInputComponent amount_input_component = 1;
  // option for user to choose if users want to use Instant fund transfer facility.
  WalletInstantTransferOption instant_transfer = 2;
  // Contains details of the partner entity helping us with international remittance
  // is displayed below the amount input component.
  api.typesv2.ui.IconTextComponent partner_info = 3;
  // properties to be used when user hasn't selected instant transfer option.
  VariableProperties normal_transfer_page_properties = 4;
  // properties to be used when user has opted for instant transfer.
  VariableProperties instant_transfer_page_properties = 5;
  // icon text component for the faq icon on wallet add funds page
  api.typesv2.ui.IconTextComponent faq_icon = 6;

  // contains properties that will change
  // based on whether the option has been selected or unselected by the user.
  message VariableProperties {
    // background color of the page
    api.typesv2.common.ui.widget.BackgroundColour bg_colour = 1;
    // title of the page
    // Eg. "Transfer Funds to your USD Wallet"
    api.typesv2.ui.IconTextComponent title = 2;
  }
}

message WalletInstantTransferOption {
  // text to be displayed on left side.
  api.typesv2.ui.IconTextComponent text = 1;
  // should option be selected by default
  bool default_selected = 2;
  // nudge for user to choose instant fund transfer flow.
  // to be shown if the Instant Funding box is unchecked.
  InstantFundingNudge nudge = 3;
  // properties to be used when user has opted for instant transfer.
  VariableProperties selected_config = 4;
  // properties to be used when user has not opted for instant transfer.
  VariableProperties unselected_config = 5;

  // contains properties that will change
  // based on whether the option has been selected or unselected by the user.
  message VariableProperties {
    // background color of the component
    api.typesv2.common.ui.widget.BackgroundColour bg_colour = 1;
    // text to be shown alongside the toggle switch that displays user's
    // currently selected choice in text. eg: Yes!, No
    api.typesv2.ui.IconTextComponent selection_choice_text = 2;
  }
}

message InstantFundingNudge {
  oneof nudge {
    // figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-36812&mode=design&t=OAa8FxPM5nECXshD-0
    api.typesv2.ui.IconTextComponent icon_text = 1;
    // figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-36908&mode=design&t=OAa8FxPM5nECXshD-0
    TextWithSymbol text_with_symbol = 2;
  }
  message TextWithSymbol {
    Symbol symbol = 1;
    api.typesv2.ui.IconTextComponent text = 2;
  }
  // Symbol is used to display a US Stocks symbol (eg. Microsoft) along with it's current price and logo.
  message Symbol {
    api.typesv2.common.VisualElement icon = 1;
    api.typesv2.ui.VerticalKeyValuePair symbol_name_and_price = 2;
  }
}

message WalletAddFundsInputComponent {
  // exchange rate for converting $1 to rupee
  api.typesv2.Money usd_inr_exchange_rate = 1;
  deeplink.InformationPopupOptions info_icon = 2;
  message CurrencyOptions {
    // currency type - USD, INR
    InputCurrencyType currency_type = 1;
    // default value to be shown for that currency type
    api.typesv2.Money default_value = 2;
    // options to be displayed for the currency type.
    WalletAmountSuggestionBar suggestion_bar = 3;
  }
  repeated CurrencyOptions currency_options = 3;
  // cta to proceed further
  deeplink.Cta add_funds_cta = 4;
  // details required by BE to validate the amount entered by the user
  // client needs to just pass this to user input validation rpc.
  // deprecated above two money fields in favor of generic validation details.
  WalletAmountValidationDetails validation_details = 5 [deprecated = true];
  // list of validations that UI needs to perform on the add funds page.
  repeated WalletValidationDetails validations = 6;
}

// amount suggestions for user's convenience so that they don't have to manually add the amount
message WalletAmountSuggestionBar {
  repeated WalletAmountSuggestion options = 1;
}

// figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-37141&mode=design&t=ZN7LvvUobozkHRI0-0
message WalletAmountSuggestion {
  // amount to be displayed in user input section when user selects the suggestion.
  api.typesv2.Money amount = 1;
  // display text to be shown for the option in the suggestion bar
  api.typesv2.ui.IconTextComponent display_text = 2;
  // OPTIONAL: tag to be shown along with the option
  // eg: MOST TAX EFFICIENT
  api.typesv2.ui.IconTextComponent tab = 3;
}

// details required by BE to validate the amount entered by the user
message WalletAmountValidationDetails {
  // user's savings a/c balance. we ask users to add balance to
  // bank a/c if amount entered on add funds page is higher.
  api.typesv2.Money savings_account_balance = 1;
  // user's US stocks wallet balance. Will be used to validate the amount
  // entered by user on withdraw funds page.
  api.typesv2.Money wallet_withdrawable_balance = 2;
}

// WalletValidationDetails defines the a validation that needs to be done on the wallet add/withdraw funds page.
// The message also has error message and CTA to be displayed on validation failure.
// Currently, only amount validations are defined but could be extended to other generic validations as well.
message WalletValidationDetails {
  WalletValidationType type = 1;
  // amount against which the validation for the entered amount needs to be done.
  api.typesv2.Money validation_amount = 2;
  // inline error message to be displayed to the user.
  api.typesv2.ui.IconTextComponent inline_error_msg = 3;
  // cta to be used on validation failure
  deeplink.Cta cta = 4;
}

// WalletValidationType denotes different types of validations that are needed on entered amount on ADD/WITHDRAW funds
// screen.
enum WalletValidationType {
  WALLET_VALIDATION_TYPE_UNSPECIFIED = 0;
  // validate entered amount is >= min allowed value
  WALLET_VALIDATION_TYPE_MIN_AMOUNT = 1;
  // validate entered amount is <= max allowed value
  WALLET_VALIDATION_TYPE_MAX_AMOUNT = 2;
  // validate entered amount is <= savings account balance
  WALLET_VALIDATION_TYPE_SAVINGS_BALANCE = 3;
  // validate entered amount to withdraw is <= wallet balance
  WALLET_VALIDATION_TYPE_WALLET_BALANCE = 4;
}

// figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-37003&mode=design&t=OAa8FxPM5nECXshD-0
message WalletWithdrawFundsPage {
  // title of the amount input page
  api.typesv2.ui.IconTextComponent title = 1;
  // Details required for rendering the amount input section.
  // User has option to input amount in both USD and INR. Client sends the amount entered for
  // validation to BE and displays the error (if any) to the user.
  // It also has an option to withdraw all funds.
  WalletWithdrawFundsInputComponent amount_input_component = 2;
  // Contains details of the partner entity helping us with international remittance
  // is displayed below the amount input component.
  api.typesv2.ui.IconTextComponent partner_info = 4;
  // icon text component for the faq icon on wallet withdraw funds page
  api.typesv2.ui.IconTextComponent faq_icon = 5;
}

message WalletWithdrawFundsInputComponent {
  // exchange rate for converting $1 to rupee
  api.typesv2.Money usd_inr_exchange_rate = 1;
  deeplink.InformationPopupOptions info_icon = 2;
  repeated CurrencyOptions currency_options = 3;
  // cta to proceed further
  deeplink.Cta cta = 4;
  // details required by BE to validate the amount entered by the user
  // client needs to just pass this to user input validation rpc.
  WalletAmountValidationDetails validation_details = 5 [deprecated = true];

  message CurrencyOptions {
    // currency type - USD, INR
    InputCurrencyType currency_type = 1;
    // default value to be shown for that currency type
    api.typesv2.Money default_value = 2;
    // checkbox to update the amount
    Checkbox withdraw_all_checkbox = 3;
  }

  message Checkbox {
    api.typesv2.ui.IconTextComponent text = 1;
    deeplink.InformationPopupOptions info_popup = 2;
    bool default_selected = 3;
    // amount to be displayed in the input section when this option
    // is selected.
    api.typesv2.Money amount = 4;
  }
  // if this flag is true, client will not render forex rate conversion in UI
  // figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-35337&mode=design&t=ZHMrIIexxymsafnA-4
  bool should_disable_forex_conversion_rate = 6;
  // list of validations that UI needs to perform on the withdraw funds page.
  repeated WalletValidationDetails validations = 7;
}
