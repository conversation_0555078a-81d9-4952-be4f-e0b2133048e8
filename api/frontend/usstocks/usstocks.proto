syntax = "proto3";

package frontend.usstocks;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/usstocks/client_states/enums.proto";
import "api/frontend/usstocks/enums.proto";
import "api/frontend/usstocks/symbol.proto";
import "api/frontend/usstocks/uss_sip.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/date.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/swipe_button.proto";
import "api/typesv2/ui/text_with_hyperlinks.proto";
import "api/typesv2/ui/toggle.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "api/typesv2/ui/widget_themes.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/frontend/usstocks";
option java_package = "com.github.epifi.gamma.api.frontend.usstocks";

// Represent Buy Screen Data to be fetched as per currency
message BuyDetails {
  // Represent title of screen
  // Eg: BUY
  api.typesv2.common.Text buy_title_text = 1;
  // deprecated in favor of AccountOverview to use same UI space for display different views
  // eg: show either wallet balance, market status
  MarketDetails market_details = 2 [deprecated = true];
  OrderCard order_card = 3;

  // Represent description text during order
  // Eg: (emoji) Place your order instantly during market hours (emoji)
  api.typesv2.common.Text description_text = 4;

  // Represent suggested amount and default amount
  SuggestedAmount suggested_amount = 5;

  // top_right_accessory_view is a generic component to display different view to user at the top right corner of screen
  // eg: market status (open, closed) or wallet balance etc
  AccessoryView top_right_accessory_view = 6;
}

message AccessoryView {
  oneof component {
    // market details is used to show market status to user
    MarketDetails market_details = 1;
    // wallet balance summary presenting current wallet balance to user
    WalletBalanceSummary balance_summary = 2;
  }

  message WalletBalanceSummary {
    api.typesv2.ui.IconTextComponent balance = 1;
  }
}

// This pattern is adopted to extensibility
// Eg: empty screen or different component
message SymbolsListCard {
  oneof Card {
    SymbolDetails symbol_details = 1;
  }
}

message SymbolUpdate {
  // Represent current market price of symbol
  api.typesv2.Money stock_price = 1;

  // Represent growth in symbol from last trading session
  // Eg: +1.3% (it is gain then text color is green)
  // Eg: -1.3% (it is loss then text color is red)
  // Deprecated: Use percentage_returns instead
  api.typesv2.common.Text growth_text = 2 [deprecated = true];

  // timestamp at which stock price was updated
  google.protobuf.Timestamp updated_at = 3;

  // e.g: "^ 0.55%"
  // Use price_change instead
  api.typesv2.ui.IconTextComponent percentage_returns = 4 [deprecated = true];

  // Represent current market price of symbol, for money value of stock price use stock_price
  api.typesv2.common.Text stock_price_text = 5;

  // Change in trading price of a stock, compared to a baseline
  // e.g., if the change in price is 12.5%, the value if this field will be 12.5
  double percentage_price_change = 6;
}

// Represent basic common details in buy/sell screen
// Eg: symbol logo,min_price,max_price,validation error message,forex_rate
message OrderCard {
  SymbolDetails symbol_details = 1;

  // min price and max price of the stocks
  // help in finding range quantity interval of stock
  // Eg: 1.00 - 1.05 stock quantity
  // it is in USD
  api.typesv2.Money min_stock_price = 2;
  api.typesv2.Money max_stock_price = 3;

  // represent info format as per figma text
  // Figma https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1013%3A5697#283734526
  deeplink.InformationPopupOptions info_popup_options = 4;

  // Represent validation data on order screen
  ValidationDetails validation_details = 5;

  // Represent forex rate
  api.typesv2.Money forex_rate_usd_to_inr = 6 [deprecated = true];

  // Represent  qty_text
  // Eg: Apprx. Qty
  // [Deprecated] Use text_fields to send expression for calculating qty_text
  api.typesv2.common.Text qty_text = 7 [deprecated = true];

  // Represent estimate_unit_text
  // Eg: Est. unit price
  api.typesv2.common.Text unit_price_text = 8;

  // Inline error message, if there was an issue in fetching forex rate
  // client should provide retry option to user. Page should be refreshed again on retry
  // forex_rate_fetch_error should be displayed only in case `forex_rate_usd_to_inr` is null
  api.typesv2.common.Text forex_rate_fetch_error = 9 [deprecated = true];

  // text_fields are used to present additional information to user which may or may not change on change in user input. eg: Buy Amount
  // It may contain fields that might need evaluation at client
  // eg: Commission applicable on the amount entered by user for a trade order
  repeated UserInputBasedComputedField text_fields = 10;

  // value of the expression is expected to be passed in CreateBuyOrder/CreateSellOrder request
  Expression trade_order_amount_expression = 11;
}

// Represent max and min on order and error message for each validation
message ValidationDetails {
  // Represent the minimum amount the user can buy/sell
  // it is in USD
  api.typesv2.Money min_amount = 1;

  // Represent the maximum amount the user can buy with
  // Note: if it is sell screen
  // Represent the maximum amount the user can sell for
  // it is in USD
  api.typesv2.Money max_amount = 2;

  // Represent error key map with string to display
  // Eg: {"ERROR_MESSAGE_MAX_AMOUNT":"Please enter less than 10,000$"}
  // Refer frontend.usstocks.client_state.enum.ErrorMessage
  // this is expected to be used for all the inline error validations
  // Deprecated in favour of error_message_map_v1
  map<string, api.typesv2.common.Text> error_message_map = 3 [deprecated = true];

  // Represent error key map with string to display
  // Eg: {"ERROR_MESSAGE_FRACTIONAL_LIMIT_PRICE":"Limit Price is not applicable on fractional orders (i icon)"}
  // Refer frontend.usstocks.client_state.enum.ErrorMessage
  // this is expected to be used for all the inline error validations
  // mostly used for showing info pop up currently.
  //INFORMATION_POPUP deeplink to be used for the same
  // https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34046-21100&t=oaZK4wdpJUAFWAOh-4
  map<string, api.typesv2.ui.IconTextComponent> error_message_map_v1 = 4;

  // Represent the minimum quantity the user can buy
  // Note: if it is sell screen
  // Represent the minimum quantity the user can sell
  double minimum_quantity = 5;

  // Represent the maximum quantity the user can buy
  // Note: if it is sell screen
  // Represent the maximum quantity the user can sell
  double maximum_quantity = 6;
}

// Represent market status
message MarketDetails {
  // Represent text to display on UI
  // Eg: MARKET OPEN
  api.typesv2.common.Text market_status_text = 1;

  // Represent status of the market is open or not
  bool is_market_open = 2;
}

// Represent the suggested amount and default amount in the screen
// This value are in USD
message SuggestedAmount {
  repeated api.typesv2.Money list_suggested_amount = 1;
  api.typesv2.Money default_suggested_amount = 2;
}

message Invoice {
  // Represent text to display in invoice screen
  InvoiceDisplay invoice_display = 1;
  // Deprecated in favor of disclaimer field.
  // represent disclaimer for buy screen
  Disclaimer buy_disclaimer = 2 [deprecated = true];

  // Note:
  // client can identify if its a Clickable item by the following format in the ConsentItem text string
  // Example: "Just link it from ^^https://eportal.incometax.gov.in/iec/foservices/#/pre-login/bl-link-aadhaar^^here^^ and retry"
  // Client extracts the link and navigates to a webview on tapping the displayString "here
  deeplink.USStocksOnboardingScreenOptions.ConsentItem payment_consent_item = 3 [deprecated = true];

  InvoiceDetails invoice_details = 4;

  // consent text with hyperlinks to TnC and A2 form
  HyperlinkConsent consent = 5;

  // represent info pop up for updated forex rate
  // eg: when user enter an amount then due to constraint their might be change in forex rate
  // so this object is used for updating pop-up option UI
  // which include new forex rate and info accordingly
  deeplink.InformationPopupOptions info_pop_up = 6;

  // represents disclaimer to be shown along with the order details
  Disclaimer disclaimer = 7 [deprecated = true];

  // nudge for providing some extra info to user
  // eg: You'll miss out on USD returns and incur extra tax during transfers!
  api.typesv2.ui.IconTextComponent disclaimer_nudge = 8;
}

message HyperlinkConsent {
  string id = 1;
  // represent if the checkbox check or not
  bool is_checked = 2;
  // consent text with hyperlink
  api.typesv2.ui.TextWithHyperlinks consent_text = 3;
}

// contains required data for cred block processing
message PaymentProcessDetails {
  string order_id = 1;
}

// Represent text and amount details of invoice screen
// This is used for display on screen
message InvoiceDisplay {
  repeated DisplayEntry display_entries = 1 [deprecated = true];
  repeated InvoiceDisplayEntry invoice_display_entries = 2;
  // footer text to be shown below the price breakup
  // Ex: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-36908&mode=design&t=k6imfnYRyiEXOprq-0
  api.typesv2.ui.IconTextComponent footer = 3;
}

// Represent value used for computation and payment
message InvoiceDetails {
  // [Deprecated] use amount_requested instead
  api.typesv2.Money buy_amount = 1 [deprecated = true];
  api.typesv2.Money tcs_amount = 2;
  api.typesv2.Money gst_amount = 3;
  api.typesv2.Money total_amount = 4;
  // [Deprecated] use amount_requested_in_usd instead
  api.typesv2.Money buy_amount_in_usd = 5 [deprecated = true];
  api.typesv2.Money exchange_rate = 6;
  // forex rate's unique identifier applicable for the transaction
  // would be empty if vendor api is used
  string forex_rate_id = 7;
  // source from where forex rate is fetched
  ForexRateProvenance forex_rate_provenance = 8;

  // represents fee to be charged if user opts for instant purchase option
  // Optional for user to opt in or not
  // if user opts for instant purchase, LRS and other IFT checks are performed post order fulfillment by broker else vice versa
  api.typesv2.Money instant_purchase_fee = 9;

  // represent amount request for order by user
  api.typesv2.Money amount_requested = 10;

  // represent amount request for order in usd by user
  api.typesv2.Money amount_requested_in_usd = 11;
}

enum ForexRateProvenance {
  FOREX_RATE_PROVENANCE_UNSPECIFIED = 0;
  // forex rate is fetched from the forex_rates table which is manually uploaded by the ops team
  FOREX_RATE_PROVENANCE_MANUAL_PURCHASE = 1;
  // forex rate is fetched from vendor api
  FOREX_RATE_PROVENANCE_VENDOR_API = 2;
  // forex rate from config
  FOREX_RATE_PROVENANCE_CONFIG = 3 [deprecated = true];
}

message SellDetails {
  api.typesv2.common.Text sell_title_text = 1;

  // Represent market status
  MarketDetails market_details = 2 [deprecated = true];

  // Represent common text details for order
  OrderCard order_card = 3;

  // Represent banner text during order
  api.typesv2.common.Text description_text = 4;

  // Represent consent to sell all stocks
  deeplink.USStocksOnboardingScreenOptions.ConsentItem sell_all_consent_item = 5;

  // Represent text state's change in amount and quantity due to market
  Disclaimer sell_disclaimer = 6;

  // Represent text to be displayed on the amount received
  api.typesv2.common.Text amount_to_be_received_text = 7;

  // Represent pop screen for sell lock info
  // if nil, ignore and do not show info icon.
  // in InformationPopupOptions obj
  // text_title represent  heading
  // eg: Amount in lock-in
  // text_subtitle represent detail info
  // eg: You’ll be able to sell once we finish LRS process. Meanwhile we made sure you won’t be losing any returns in this time.
  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4430%3A19973&t=fnPPNqeOXaf2GNsV-4
  deeplink.InformationPopupOptions sell_lock_info_popup = 8;

  // top_right_accessory_view is a generic component to display different view to user at the top right corner of screen
  // eg: market status (open, closed) or wallet balance etc
  AccessoryView top_right_accessory_view = 9;
}

// Represent text stating change in price and amount during order
message Disclaimer {
  api.typesv2.common.Text text = 1;
  string logo_url = 2;
}

// Represent list of card to display on order receipt
message OrderReceipt {
  repeated OrderScreenCard order_screen_cards = 1;
}

// eg: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=12314%3A33979&t=T5nsJgOjzKlSxOOT-0
message TitleDescriptionCard {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text description = 2;
}

// Represent possible screen card in order receipt
message OrderScreenCard {
  oneof Card {
    SymbolDisplayDetails symbol_display_details = 1;

    // contains order's activity level details
    OrderTimeLine order_timeline = 2;

    // contains price details related to order
    OrderDetails order_details = 3;

    // contains order id and utr
    OrderTransaction order_transaction = 4;

    // this text will empty for terminate states
    // Eg: After your payment is made, it takes up to 1 working day to start earning interest
    // Note: this doesnt have iogo url
    Disclaimer disclaimer_text = 7;

    // eg. Sell option: Locked till Jan 14
    // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=12314%3A33979&t=T5nsJgOjzKlSxOOT-0
    TitleDescriptionCard Sell_lock_info_card = 8;

    // e.g Funds Added $1000 on top of screen
    // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153%3A35676&mode=dev
    api.typesv2.ui.VerticalKeyValuePair wallet_fund_activity_details = 9;

    // itc for cancel button and banner
    // Ex: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153%3A35676&mode=dev
    api.typesv2.ui.IconTextComponent text_component = 10;
  }
}

message SymbolDisplayDetails {
  SymbolDetails symbol_details = 1;

  // Represent order type
  // Eg: BUY/SELL
  api.typesv2.common.Text order_type_text = 2;

  // It represent amount in USD
  // This represents the amount of the order placed by the user
  // Eg:1,999.9
  api.typesv2.common.Text amount_text = 3;

  // Represent CTA for cancel order
  // this is used if order is in processing stage
  // if deeplink inside CTA is nil, cancel operation should be performed on CTA click
  // else deeplink should be opened
  // [deprecated] In favor of using cancel_order_actionf
  deeplink.Cta cancel_order_cta = 4 [deprecated = true];
  // manage sip button for sip orders
  api.typesv2.ui.IconTextComponent manage_sip_cta = 5;
}

message OrderTimeLine {
  // Eg: Order activity
  api.typesv2.common.Text title_text = 1;

  repeated OrderStepDetails order_step_details = 2;
}

message OrderStepDetails {

  // represent status of order with bg color
  // Eg status (green background)
  api.typesv2.common.Text order_status_text = 1;

  // Represent tag text
  // Eg: 23 Jan 8:10 PM, processing
  repeated api.typesv2.common.Text tags = 2;
}

// Represent price details related to order
message OrderDetails {

  // Eg: Price details
  api.typesv2.common.Text title_text = 1;

  // Represent title_text of screen
  // Eg: Price details
  // and
  // represent stock price in USD
  // Eg: ₹177.65
  repeated DisplayEntry display_entries = 2;

  // Represent disclaimer text if non-terminal state
  // if it is terminal state it is empty
  api.typesv2.common.Text disclaimer_text = 3;
}

// Represent details and entries in order details page
message OrderTransaction {
  // Eg: Order details
  api.typesv2.common.Text title_text = 1;

  repeated DisplayEntry display_entries = 2;
}

// Represent either column/row in table
// Eg: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=673%3A3437
// Eg :1) grid in order receipt or
// Eg :2) represent table row of invoice screen while buying
message DisplayEntry {
  api.typesv2.common.Text title_text = 1;
  api.typesv2.common.Text value_text = 2;

  // if nil, ignore and do not show info icon.
  deeplink.Deeplink info_pop_deeplink = 3;

  // Represent if the value is plain string contains clickable item or not
  // if contains_hyperlink is true then value is html
  // Eg: process ^^link^^text^^ hyper link format only if this is true
  bool contains_hyperlink = 4;

  // optional: add it if we want to add a copy capability of the value
  api.typesv2.common.Image copy_image = 5;
}

message SymbolDetailsTab {
  // Eg: Key facts, My activity, etc.
  string display_name = 1;

  // If tab is select then it is true
  // represent tab is selected by default
  bool is_selected = 2;

  // Represent tab type
  clientstate.SymbolDetailsTabType tab_type = 3;

  // URL of icon to show on left side of tab name
  string icon_url = 4;

  // Text for showing count of pending orders
  // eg: 5, 6, 7, 9+
  api.typesv2.common.Text activity_badge = 5;
}

message SymbolDecisionFactorsCard {
  oneof card {
    // Details like what the company does, it's address, market cap, etc.
    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1196%3A5235&t=KVVe7EsjitFElgwD-4
    CompanyDetailsComponent company_details_component = 1;

    // annual statement of the company
    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1196%3A5402&t=KVVe7EsjitFElgwD-4
    CompanyAnnualStatementComponent company_annual_statement_component = 2;

    // Represent similar stock list
    SimilarSymbolsComponent similar_symbol_component = 3;

    // Highlights about the company
    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3795%3A18179&t=KVVe7EsjitFElgwD-4
    CompanyHighlightsCard highlights_card = 4;

    // [Deprecated] in favour of new Analyst Opinion Card
    // Analyst estimates about future of stock
    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3795%3A18184&t=KVVe7EsjitFElgwD-4
    AnalystOpinionCard analyst_opinion_card = 5 [deprecated = true];

    // Financials like revenue, profit, etc.
    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3795%3A18212&t=KVVe7EsjitFElgwD-4
    FinancialsCard financials_card = 6;

    // Financial ratios like debt-to-equity, return-on-earnings, etc.
    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3795%3A18262&t=KVVe7EsjitFElgwD-4
    FinancialRatiosCard financial_ratios_card = 7;

    // All the announcements for the Symbol
    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=14217-31048&t=AqxlkGZoFuTSpuYS-0
    AnnouncementsComponent announcements_component = 8;

    // info card like Expense ratio, Market info, Tracking info
    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=14397-32418&t=cjfzsdlJvFLQ72OR-0
    EtfInfoCard etf_info_card = 9;

    // represent the holding percentage of all major holders of different categories for an ETF
    EtfSummaryCard etf_summary_card = 10;

    // Analyst estimated about future of stocks v2 design
    // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=1196%3A4411&mode=design&t=jEnZyoTcBeq1mP5b-1
    AnalystOpinionCardV2 analyst_opinion_card_V2 = 11;

    SIPProjectionWidget sip_projection_widget = 12;
  }
}

message AnnouncementsComponent {
  // e.g. Announcements
  api.typesv2.common.Text heading = 1;

  // Adding an array as there can be multiple announcements for some stocks
  repeated Announcement announcements = 2;
}

message Announcement {
  // e.g. Apple has declared a dividend of $1.60 per share for all units bought before 23 Jan 2023
  api.typesv2.common.Text title = 1;

  // e.g 🎉 You qualify for a dividend of $0.07 receivable on 27th Jan 2023
  api.typesv2.common.Text sub_title = 2;

  // e.g 27 JAN 2023
  api.typesv2.common.Text footer = 3;
}

message CompanyDetailsComponent {
  // e.g. Apple
  api.typesv2.common.Text company_name_text = 1;

  // e.g. AAPL
  api.typesv2.common.Text symbol_text = 2;

  // e.g. Apple Inc. is an American multinational technology company that specializes in consumer electronics, etc.
  // client is expected to show previews of long descriptions and expand those on user interaction
  api.typesv2.common.Text description_text = 3;

  // list of details like market cap, headquarters, website, etc.
  repeated DisplayEntry display_entries = 4;

  // total_columns is used to show DisplayEntry in given no. of columns
  int32 total_columns = 5;
}

message EtfInfoCard {
  // eg: Tracking info
  api.typesv2.common.Text header = 1;
  // eg. title = TRACKING index , value = S&P 500
  repeated DisplayEntry key_val_infos = 2;
  // total_columns is used to show key_val_infos in given no. of columns
  int32 total_columns = 3;

  // This can be additional info related to the ETF info shown
  deeplink.InformationPopupOptions info_popup_options = 4;
}

message EtfSummaryCard {
  // eg: ETF summary
  api.typesv2.common.Text card_title = 1;

  // tabs for Top 10 stocks, Sectors etc.
  repeated EtfSummaryMetricTab etf_summary_metric_tabs = 2;

  // This can be additional info related to the ETF summary shown
  deeplink.InformationPopupOptions info_popup_options = 3;
}

message EtfSummaryMetricTab {
  // e.g. Top 10 stocks, Sectors etc.
  api.typesv2.common.Text metric_name = 1;

  // represent the holding percentage of all constituent in that ETF
  repeated EtfConstituentHolding etf_constituent_holdings = 2;
}

message EtfConstituentHolding {
  // e.g. Apple
  api.typesv2.common.Text constituent_name = 1;

  // color of company's section in bar
  string color = 2;

  // holding percentage of given company in that ETF
  // clients are expected to assume that that percentages across all company will add up to 100
  float holding_percentage = 3;
}

message CompanyAnnualStatementComponent {
  // Eg: Annual statement 2021-2022
  api.typesv2.common.Text title_text = 1;

  // represent url of annual statement
  // used to download file on client if user is interested
  string document_url = 2;
}

message SimilarSymbolsComponent {
  // Eg: You might also like
  api.typesv2.common.Text title_text = 1;

  // list of similar symbols
  repeated SymbolDetails symbols_details = 2;
}

message CompanyHighlightsCard {
  // Title of the company highlights card
  api.typesv2.ui.IconTextComponent title = 1;

  // short intro description about the company
  api.typesv2.common.Text description = 2;
}

message AnalystOpinionCard {
  // e.g. Analyst opinion
  api.typesv2.common.Text card_title = 1;

  // e.g. Source: Morningstar
  api.typesv2.common.Text source = 2;

  repeated AnalystRecommendation recommendations = 3;

  // e.g. Expected target price $990.50 - $998.50
  // Note: price can be in bold
  api.typesv2.ui.IconTextComponent description = 4;

  // e.g. Updated on 12 May 2022 • By 26 independent analysts
  api.typesv2.common.Text footer = 5;
}

message AnalystOpinionCardV2 {
  // e.g 70% Analyst say Buy with Expected Target price
  api.typesv2.ui.VerticalKeyValuePair recommendation_texts_pair = 1;

  // background colour for recommendation Texts
  string recommendation_texts_view_bg_color = 2;

  // for graph and values
  repeated AnalystRecommendation recommendations = 3;

  // Analysts Count ITC e.g 21 analysts
  api.typesv2.ui.IconTextComponent analysts_count = 4;

  // Updated At ITC e.g Updated on 12th May 2022
  api.typesv2.ui.IconTextComponent card_updated_on = 5;

  // Recommendation source ITC e.g Source: Morningstar
  api.typesv2.ui.IconTextComponent recommendation_source = 6;

  // How to use? ITC
  api.typesv2.ui.IconTextComponent info_popup = 7;
}

message AnalystRecommendation {
  // e.g. Buy, Hold, Sell, etc.
  string recommendation_name = 1 [deprecated = true];

  // color of the recommendation line segment
  string color = 2;

  // percentage of total analysts making a recommendation
  // clients are expected to assume that that percentages across all recommendations will add up to 100
  float percentage = 3;

  // e.g. BUY 30%
  api.typesv2.ui.IconTextComponent recommendation_description = 4;
}

// Financial metric like revenue, profit, etc.
message FinancialsCard {
  // e.g. Financials
  api.typesv2.common.Text card_title = 1;

  // tabs for quarterly and annual metrics
  repeated FinancialMetricTab financial_metric_tabs = 2;

  // This can be additional info related to the financials data shown
  deeplink.InformationPopupOptions info_popup_options = 3;
}

// A tab for a financial metric(like revenue) can have multiple charts, e.g. one for quarterly data points and another for yearly
message FinancialMetricTab {
  // e.g. Revenue, Profit, etc.
  api.typesv2.common.Text metric_name = 1;

  // charts for diff. time periods like quarterly or yearly
  repeated FinancialMetricChart financial_metric_charts = 2;
}

// A chart contains all data required by clients to render it
message FinancialMetricChart {
  // frequency at which a metric like revenue is reported, e.g. Annually, Quarterly, etc.
  api.typesv2.common.Text reporting_frequency = 1;

  // color to fill the bars in chart with
  // [Deprecated] use bar_color inside DateSeriesBarChartDataPoint instead
  string series_color = 2 [deprecated = true];

  // color of a selected data point in bar chart
  // [Deprecated] use focus_color inside DateSeriesBarChartDataPoint instead
  string focus_color = 3 [deprecated = true];

  // data points of a metric like revenue, profit, etc. over a time period
  repeated DateSeriesBarChartDataPoint metric_data_points = 4;

  // explanation of the metric
  api.typesv2.common.Text footer = 5;

  // e.g. BAD <0  AVERAGE: 1-2  GOOD >2
  repeated Legend legends = 6;
}

// e.g. BAD <0  AVERAGE: 1-2  GOOD >2
// https://github.com/PhilJay/MPAndroidChart/wiki/Legend
// ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=14397-32524&t=zxfCqiSlwPy8Uv0X-0
message Legend {
  api.typesv2.common.Text text = 1;
}

// it contains data points of a bar
// ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=14397-32524&t=JNadWATdB6IoN7dL-0
message DateSeriesBarChartDataPoint {
  // e.g. FY '19, AY '20, 2021, Q1 '22, etc.
  string category_name = 1;

  // y-axis value, can be very high, e.g. market cap can be in billions
  // clients are expected to truncate values for labeling
  double value = 2;

  // color to fill the bar in chart
  string bar_color = 3;

  // color of a selected bar
  string focus_color = 4;
}

// Financial ratio like debt-to-equity, return-on-earnings, etc.
message FinancialRatiosCard {
  // e.g. Ratios
  api.typesv2.common.Text card_title = 1;

  // tabs for quarterly and annual financial ratios
  repeated FinancialRatioTab financial_ratio_tabs = 2;
}

// A tab for a financial ratio(like price-to-equity) can have multiple charts, e.g. one for quarterly data points and another for yearly
message FinancialRatioTab {
  // e.g. Debt-to-equity, Return-on-earnings, etc.
  api.typesv2.common.Text ratio_name = 1;

  // charts for diff. time periods like quarterly or yearly
  repeated FinancialRatioChart financial_ratio_charts = 2;
}

// chart data for plotting a multi-line chart with dates on x-axis
message FinancialRatioChart {
  // frequency at which a metric like revenue is reported, e.g. Annually, Quarterly, etc.
  api.typesv2.common.Text reporting_frequency = 1;

  // data for plotting a metric like debt-to-equity of company vs industry, etc. over a time period
  repeated DateSeriesLineChartData multi_ratio_data_points = 2;

  // explanation on the ratio
  // e.g. Debt to Equity highlights a company’s financing with debt rather than its own resources
  api.typesv2.common.Text footer = 3;
}

// chart data for plotting a line with dates on x-axis
message DateSeriesLineChartData {
  // e.g. Apple, Category, etc.
  string series_name = 1;

  // color of a chart series
  string series_color = 2;

  // data points of a metric like debt-to-equity of company or industry over a time period
  repeated DateSeriesLineChartDataPoint series_data_points = 3;
}

// data-point for plotting a line with dates on x-axis
message DateSeriesLineChartDataPoint {
  // e.g. FY '19, Q1 '22, etc.
  string category_name = 1;

  // x-axis value, e.g. 2019, etc.
  api.typesv2.Date date = 2;

  // y-axis value
  double value = 3;
}

// This pattern is adopted to extensibility
// Eg: empty screen or different component
message SymbolActivityCard {
  oneof Card {
    SymbolActivity symbol_activity = 1;
    // empty state should be sent if no activity is present
    EmptyState zero_state = 2;
  }
}

// EmptyState message is used for sending display data in case no data is present for the particular screen
// eg 1: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3547%3A17570&t=mcqGLNVW7dpY0Xtb-4
// eg 2: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=8384%3A25688&t=mcqGLNVW7dpY0Xtb-4
message EmptyState {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Image img = 2;
}

message SymbolActivity {
  // Eg: Sell Apple
  api.typesv2.common.Text activity_text = 1;

  // Eg: $900
  api.typesv2.common.Text amount_text = 2;

  // Represent tag for activity
  // Eg: Processing
  repeated api.typesv2.common.Text tags = 3;
  google.protobuf.Timestamp created_at = 4;

  // represent order id for given activity
  // this can be used by client to show order receipt
  string order_id = 5;

  // Deeplink for order receipt screen for given Symbol Activity
  // if nil then no redirection to screen
  deeplink.Deeplink order_receipt_screen = 6;
}

// This pattern is adopted to extensibility
// Eg: empty screen or different component
message PortfolioCard {
  oneof Card {
    Holding holding = 1;
  }
}

message Holding {
  string symbol_logo_url = 1;

  // Eg: 35,550
  api.typesv2.common.Text amount_text = 2;

  // represent list of tags
  // Eg: 2 PROCESSING
  repeated api.typesv2.common.Text tags = 3;

  // Eg:Amazon
  api.typesv2.common.Text symbol_name_text = 4;

  //Eg: 1,500 at 7.2%
  api.typesv2.common.Text growth_text = 5;

  // Represent url of icon red or green arrow
  string growth_icon_url = 6;

  clientstate.GrowthType growth_type = 7;
}

// Component contains all the supported UI components on the caller screen
// having oneof provides flexibility to change ordering of the component on UI from BE
message Component {
  oneof component {
    // StockCollection component contains list of relevant stocks
    StockCollection collection = 1;
    // stories_placeholder is a placeholder for showing Stories component
    // data for displaying this component will be fetched from GetAllMediaContent RPC
    // GetAllMediaContentRequest should mention UIContext as UI_CONTEXT_US_STOCKS_LANDING_PAGE_STORIES
    // for fetching relevant stories
    // section will list videos/blog for learning about US Stocks
    StoriesPlaceholder stories_placeholder = 2;
    // client should display search bar on explore page based on search_bar field
    // If search_bar is not null, search bar should be displayed with mentioned placeholder
    SearchBarDisplayInfo search_bar = 3;
    // easter egg text
    // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3584%3A17322&t=4GXgGKFnAkk6aIfM-4
    TitleWithSubTitle easter_egg_text = 4;
    PartnerComplianceSection partner_compliance_section = 5;
    MarketStatusSection market_status_section = 7;
    UsStocksDynamicBanner banner = 8;
  }
  // identifier for different variants of stock collection component
  ComponentType type = 6;
}

enum MarketStatus {
  MARKET_STATUS_UNSPECIFIED = 0;
  // market is open to place new orders
  MARKET_STATUS_OPEN = 1;
  // market is closed to place new orders
  MARKET_STATUS_CLOSED = 2;
}

// MarketStatusSection is used to display current market status of the US market
// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=11989%3A30091&t=hV48XpYoDDSA6xTH-4
// the icon in the component is stored in the client side
message MarketStatusSection {
  // e.g: "US MARKETS OPEN NOW, CLOSING AT 2:30 AM"
  api.typesv2.common.Text title = 1;
  // e.g: "Order now to place your orders instantly"
  api.typesv2.common.Text description = 2;
  // inner background color for the component
  // e.g: "#FFFFFF"
  string inner_bg_color = 3;
  // surrounding background color of the component
  // e.g: "F0F3F7"
  string outer_bg_color = 4;
  // tells whether the market is open or closed
  // client can do necessary handling based on this like icon change
  MarketStatus market_status = 5;
}

// Figma link: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3395%3A14745&t=Cev4pnu83ZSh7JOP-4
// Look at bottom-most section
message PartnerComplianceSection {
  // Partnership string. Eg: "In Partnership with our trusted US broker"
  api.typesv2.common.Text in_partnership_with_text = 1;
  // vendor partner image URL string
  string vendor_img_url = 2;
  // Disclaimer string. Eg: "None of the above recommendations should be considered investment advice."
  api.typesv2.common.Text disclaimer_text = 3;
  // Background color for the component
  string bg_color = 4;
}

// Client would have to call 'DynamicElements' FE rpc for fetching the data to render this
message UsStocksDynamicBanner {}

message SearchBarDisplayInfo {
  // Eg: Search 600+ funds, Enter a stock name etc
  // Can vary depending on the current screen provided
  api.typesv2.ui.IconTextComponent placeholder = 1;
  string bg_color = 2;
}

// for users not having any funds added to us stocks wallet or not onboarded
// WalletPromotionHeaderComponent will show promotion component for the wallet
// We are aligned on Bottom Component being an image in the attached Figma
// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=23382%3A34352&mode=design&t=SEFW5WsLoKUEy0te-1
message WalletPromotionHeaderComponent {
  // This is the title for the component e.g
  api.typesv2.common.Text title = 1;

  // This is the lottie or image where we show stocks and a platform below it
  api.typesv2.common.VisualElement flip_component = 2;

  // This is to show the benefits or rewards
  api.typesv2.common.VisualElement bottom_component = 3;

  // footer_cta is a CTA to be displayed at the bottom of bottom_component
  // eg: Setup Your USStocks Account >
  api.typesv2.ui.IconTextComponent footer_cta = 4;

  // compliance_cta is a CTA to be displayed at bottom for compilance
  // eg: APPROVED & INSURED BY FINRA & SIPC
  api.typesv2.ui.IconTextComponent compliance_cta = 5;

  // this is the radial gradient shown behind the view
  api.typesv2.ui.BackgroundColour radial_gradient = 6;

  // This is the deeplink to open some screen when tapped on bottom component
  // Will be useful in case of rewards state where we want to land user to different screen
  deeplink.Deeplink bottom_component_deeplink = 7;

}

// for users not having any investments with US Stocks
// PitchComponent will present a carousel of quick stats or statements to develop interest for US Stocks as a feature in users
// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3395%3A14857&t=ddD12jDMquT0n8CN-4
message PitchComponent {
  repeated TextWithImage text_with_image = 1;

  // footer_cta is a CTA to be displayed at the bottom of investment summary component
  // eg: ⌛ Processing 2 of your orders >
  api.typesv2.ui.IconTextComponent footer_cta = 2;
}

message TextWithImage {
  // text to be displayed
  api.typesv2.common.Text display_text = 1;
  // corresponding img_url
  string img_url = 2;
}

message LandingPageTab {
  // display text and image for tab
  TextWithImage text_with_image = 1;
  // Identifier for tab
  // will be used by client to invoke relevant RPC for getting data to render tab content
  LandingScreenTabType tab = 2;
  // As of now, each tab maps to an API and payload is passed as a request parameter to that API
  bytes payload = 3;
}

enum LandingScreenTabType {
  LANDING_SCREEN_TAB_UNSPECIFIED = 0;
  // explore tab
  LANDING_SCREEN_TAB_EXPLORE = 1;
  // portfolio tab
  LANDING_SCREEN_TAB_YOUR_STOCKS = 2;
  // watchlist tab
  LANDING_SCREEN_TAB_WATCHLIST = 3;
}

// InvestmentSummary will present a summary of existing investments for the user
// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3590%3A21585&t=ddD12jDMquT0n8CN-4
message InvestmentSummary {
  // current value of the investment
  api.typesv2.Money current_value = 1;
  // list of statistics for existing investments
  // eg: 10,408.02 INVESTED, 1,408.02 RETURNS etc
  repeated InvestmentStats investment_stats = 2;
  // footer_cta is a CTA to be displayed at the bottom of investment summary component
  // eg: ⌛ Processing 2 of your orders >
  // [Deprecated]: use LandingPage.footer_cta instead
  api.typesv2.ui.IconTextComponent footer_cta = 3;

  // A disclaimer info for when the current value of stocks might not be in sync with regular trading hours closing price
  // due to extended trading hours
  deeplink.InformationPopupOptions current_value_info = 4;

  // wallet details to be shown in investment summary.
  LandingPageWalletSummary wallet_summary = 5;
}

// figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-36351&mode=design&t=EQtuVtC1SR7uMSrY-0
message LandingPageWalletSummary {
  // wallet balance and deeplink to wallet details page
  api.typesv2.ui.IconTextComponent balance = 3;
}

message InvestmentStats {
  // eg: 10,408.02 INVESTED
  TitleWithSubTitle left_stat = 1;
  // eg: 1,408.02 RETURNS
  TitleWithSubTitle right_stat = 2;
}

message TitleWithSubTitle {
  // eg: 10,408.02 (Amount Invested)
  api.typesv2.common.Text title = 1;
  // eg: INVESTED, RETURNS etc
  api.typesv2.common.Text sub_title = 2;
  // background color for the component (optional)
  string bg_color = 3;
}

message StockCollection {
  // An identifier of a collection
  // Clients may use this for identifying entry points, sending events, or making API calls.
  string id = 8;
  // eg: Brands people love
  api.typesv2.common.Text title = 1;
  // eg: Based on your portfolio, watchlist & history
  api.typesv2.common.Text sub_title = 2;
  // CTA for all collections page
  deeplink.Cta view_all = 3;
  // list of stocks
  repeated SymbolDetails stocks = 4;
  // background color
  string bg_color = 5;
  // icon for collection
  api.typesv2.common.Image collection_icon = 6;
  // this component will be used to show a CTA to see more stocks
  // if exists, client should append this component at the end of stocks list
  // if empty, client should not show this component
  StockCollectionInfoCard last_item = 7;
}

// StockCollectionInfoItem is info stock item for StockCollection which is appended as a final stock item in the collection
// on click of this item, client should follow the action deeplink
// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3654%3A17030&t=4oKhnXQaS5BDGAVA-4
message StockCollectionInfoCard {
  // e.g: "View all 12 stocks"
  api.typesv2.common.Text title = 1;
  // e.g: ">" icon
  api.typesv2.common.Image icon = 2;
  // on click of this card
  deeplink.Deeplink action = 3;
}

// StockCollectionVariant is identifier for different variants of stock collection component
// the components majorly varies in terms for UI layout only
enum ComponentType {
  COMPONENT_TYPE_UNSPECIFIED = 0;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3395%3A16431&t=CdpGDxNW37WH1y94-4
  COMPONENT_TYPE_STOCK_COLLECTION_VARIANT_TITLE_TOP = 1;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3395%3A15876&t=CdpGDxNW37WH1y94-4
  COMPONENT_TYPE_STOCK_COLLECTION_VARIANT_TITLE_LEFT = 2;
  // stock grid of 2x3
  COMPONENT_TYPE_STOCK_COLLECTION_VARIANT_GRID = 3;
  // Knowledge base component to list videos and blog tiles
  COMPONENT_TYPE_STORIES = 4;
  // search bar component
  COMPONENT_TYPE_SEARCH_BAR = 5;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3584%3A17322&t=4GXgGKFnAkk6aIfM-4
  // easter egg text
  COMPONENT_TYPE_EASTER_EGG = 6;
  // compliance section mentioning the partner name and logo
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=10003%3A31559&t=LehkZ6IygjUpARB5-4
  COMPONENT_TYPE_PARTNER_COMPLIANCE_SECTION = 7;
  // MarketStatusSection is used to display current market status of the US market
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=11989%3A30091&t=hV48XpYoDDSA6xTH-4
  COMPONENT_TYPE_MARKET_STATUS_SECTION = 8;
  // Dynamic Banner is used to show promo banner on UsStocks landing page
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=16448-34423&mode=design&t=oMeP59xg8shsIq0E-0
  COMPONENT_TYPE_US_STOCKS_DYNAMIC_BANNER = 9;
}

// Placeholder message for showing the stories section
// client should use UIContext.UI_CONTEXT_US_STOCKS_LANDING_PAGE_STORIES for fetching the stories
// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3588%3A17728&t=SDwVfnE3gMbDHRA2-4
message StoriesPlaceholder {
  // background color for the entire section
  string bg_color = 1;
}

// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3590%3A18420&t=TmKNiMHFbSUmY20x-4
message LockedSection {
  // e.g: "Unlock free Tesla stocks when you buy your first stock"
  api.typesv2.common.Text title = 1;
  // e.g: "Tell me more"
  deeplink.Cta cta = 2;
  // some US stock logo with locked icon on it
  // e.g: "Tesla logo"
  api.typesv2.common.Image image = 3;
  // e.g: "ACCOUNT ACTIVITY"
  api.typesv2.ui.IconTextComponent bottom_cta = 4;
}

// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3590%3A19239&t=TmKNiMHFbSUmY20x-4
message WatchlistSection {
  // e.g: "1 STOCK" & "SORT BY: CURRENT VALUE"
  SortSection sort_options = 1;
  // list of watch-listed stocks
  repeated StockListItem stocks = 2;
}

// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3590%3A23224&t=w1DHFbeYFbeIA41C-4
message SortSection {
  // e.g: "1 STOCK"
  api.typesv2.common.Text title = 1;
  // list of sort options
  repeated SortOption sort_options = 2;
  // default selected sort option
  // if not provided, the first option in sort_options will be selected by default
  SortOptionType selected_sort_option = 3;
}

message SortOption {
  // e.g: "SORT BY: CURRENT VALUE"
  string title = 1;
  // the type of sort option
  SortOptionType type = 2;
}

// different sort options possible for sorting a list of stocks
enum SortOptionType {
  SORT_OPTION_TYPE_UNSPECIFIED = 0;
  SORT_OPTION_TYPE_1_DAY_RETURNS = 1;
  SORT_OPTION_TYPE_1_MONTH_RETURNS = 2;
  SORT_OPTION_TYPE_1_YEAR_RETURNS = 3;
  SORT_OPTION_TYPE_PE_RATIO = 4;
  SORT_OPTION_TYPE_MARKET_CAP = 5;
  SORT_OPTION_TYPE_CURRENT_STOCK_PRICE = 6;
  // applicable only if the user is already invested in the stock
  // example usage is in "Your Stocks" section where all the stocks are already invested
  // Sort by the current market price of amount invested by user (descending by default)
  SORT_OPTION_TYPE_INVESTMENT_CURRENT_VALUE = 7;
  // applicable only if the user is already invested in the stock
  // example usage is in "Your Stocks" section where all the stocks are already invested
  // Sort by the total amount invested (descending by default)
  SORT_OPTION_TYPE_INVESTMENT_INVESTED_VALUE = 8;
  // applicable only if the user is already invested in the stock
  // example usage is in "Your Stocks" section where all the stocks are already invested
  // Sort by the total returns that will be earned if shares are redeemed (descending by default)
  SORT_OPTION_TYPE_INVESTMENT_RETURNS = 9;
  // applicable only if the user is already invested in the stock
  // example usage is in "Your Stocks" section where all the stocks are already invested
  // Sort by the percentage returns that will be earned if shares are redeemed (descending by default)
  SORT_OPTION_TYPE_INVESTMENT_PERCENTAGE_RETURNS = 10;
  // The default sort option of Zinc (aka ElasticSearch) used when getting stocks via Search API of US stocks
  SORT_OPTION_TYPE_RELEVANCY = 11;
  // applicable only for ETF type stocks
  SORT_OPTION_TYPE_TRACKING_ERROR = 12;
  // applicable only for ETF type stocks
  SORT_OPTION_TYPE_EXPENSE_RATIO = 13;
}

// generic stock list item component representing watchlist list item, search list item, portfolio list item...
// watchlist - https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3590%3A19292&t=TmKNiMHFbSUmY20x-4
// search - https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3388%3A15338&t=w1DHFbeYFbeIA41C-4
// portfolio - https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3590%3A22358&t=w1DHFbeYFbeIA41C-4
message StockListItem {
  // e.g: "Tesla logo"
  api.typesv2.common.Image image = 1;
  // e.g: "Tesla"
  api.typesv2.common.Text title = 2;
  // should be displayed below title
  // e.g: "Since you ADDED"
  api.typesv2.common.Text subtitle = 3;
  // should be displayed above title
  // e.g: "LARGEST IN CATEGORY"
  api.typesv2.common.Text secondary_subtitle = 4;
  // showed on the right side of the ListItem
  // e.g: "$4697.11"
  // Represent current market price of stock, for money value of stock price use stock_price
  // can also represent market cap, P/E ratio, etc. depending on chosen sort param in request
  api.typesv2.common.Text amount = 5;
  // showed on the right side of the ListItem
  // e.g: "^ 0.55%"
  api.typesv2.ui.IconTextComponent percentage_returns = 6;

  // denotes the absolute returns for an investment done by user
  // May not be present when amount represents market cap, P/E ratio, etc.
  api.typesv2.common.Text absolute_returns = 7;
  // e.g: "Unit Price ^ 6.20%", "126 users invested"
  // if empty, then don't show
  repeated Tag tags = 8;
  // on click action of the list item
  frontend.deeplink.Deeplink action = 9;
  // represent internal identifier for stock
  string stock_id = 10;
  // if empty, then add/remove button will not be shown
  // if both watchlist_details and investment_details are present,
  // investment_details will be shown first and watchlist_details will be next to it in the UI
  StockWatchlistDetails watchlist_details = 11;
  // if empty, tick icon will not be shown
  // if both watchlist_details and investment_details are present,
  // investment_details will be shown first and watchlist_details will be next to it in the UI
  StockInvestmentDetails investment_details = 12;
  // showed on the right side of the ListItem
  // e.g: $4697.11
  // Represent current market price of symbol, for text value of stock price use amount field
  api.typesv2.Money stock_price = 13;

  // if true, stock prices, returns, etc. should be updated real-time
  bool should_update_real_time = 14;

  // Symbol is expected to be used for URL paths like https://fi.money/us-stocks/aapl (for Apple)
  string symbol = 15;
}

message StockInvestmentDetails {
  // icon to be shown if the user is already invested in the stock
  // e.g: "tick icon" (https://epifi-icons.pointz.in/investments/image+(2).png)
  string icon_url = 1;
}

// StockWatchlistDetails represents the watchlist details of a stock
// client should instantly change the watchlist icon on click of the watchlist button and then call the
// UpdateWatchlist RPC to update the watchlist on the server, if the RPC fails, then revert the watchlist icon and show failure toast
// while the UpdateWatchlist rpc call is in progress, the watchlist button should be un-clickable
// note: client should maintain the watchlisted state in the client side and toggle the state accordingly after every click
message StockWatchlistDetails {
  // if true, stock is already in watchlist then show remove button
  // if false, stock is not in watchlist then show add button
  bool is_watchlisted = 1;
  // icon to show if stock is already in watchlist
  // e.g: https://epifi-icons.pointz.in/investments/watchlist_3x.png
  string watchlisted_icon_url = 2;
  // icon to show if stock is not in watchlist
  // e.g: https://epifi-icons.pointz.in/investments/watchlist-collection.png
  string not_watchlisted_icon_url = 3;
}

// StockList is a list of StockListItem
message StockList {
  repeated StockListItem stocks = 1;
}

// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3590%3A19292&t=TmKNiMHFbSUmY20x-4
message Tag {
  api.typesv2.ui.IconTextComponent content = 1;
}

// PortfolioSection is "Your Stocks" section
// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3590%3A23224&t=w1DHFbeYFbeIA41C-4
message PortfolioSection {
  // e.g: "1 STOCK" & "SORT BY: CURRENT VALUE"
  SortSection sort_options = 1;
  // list of portfolio stocks
  repeated StockListItem stocks = 2;
  // e.g: "ACCOUNT ACTIVITY"
  // this should be sent only as the last item
  api.typesv2.ui.IconTextComponent bottom_cta = 3;
}

enum WatchlistAction {
  WATCHLIST_ACTION_UNSPECIFIED = 0;
  // add stock to user's watchlist
  WATCHLIST_ACTION_ADD = 1;
  // remove stock from user's watchlist
  WATCHLIST_ACTION_REMOVE = 2;
}

message FormComponent {
  // title for the component
  // eg: Declaration, Identity Details, Address Details etc
  api.typesv2.common.Text title = 1;
  // sub-title for the component
  // can be empty, if there is no description for the component
  api.typesv2.common.Text description = 2;
  // list of key value pair of text to be displayed
  // eg: (DATE, 20 Jun 1978) (NAME OF THE APPLICANT, Jolly Joseph) etc
  repeated DisplayEntry entries = 3;
  // Decided layout of list of entries in component
  // eg: List, Grid
  FormComponentLayout type = 4;
}

enum FormComponentLayout {
  FORM_COMPONENT_LAYOUT_UNSPECIFIED = 0;
  FORM_COMPONENT_LAYOUT_LIST = 1;
  FORM_COMPONENT_LAYOUT_GRID = 2;
}

message PreLaunchPage {
  // bg image of the pre-launch page
  api.typesv2.common.Image bg_image = 1;
  // bg color of the pre-launch page
  string bg_color = 2;
  api.typesv2.common.Text title = 3;
  api.typesv2.common.Text sub_title = 4;
  // list of tags to pitch benefits of US Stocks as a feature
  // eg: Invest in top global companies, Buy instantly; no brokerage, Industry-best forex rates
  repeated api.typesv2.ui.IconTextComponent benefits = 5;
  // eg: Be the first to invest in US stocks with us
  api.typesv2.ui.IconTextComponent bonus = 6;

  // CTA to record interest of user to use US stocks as a feature
  Button record_interest = 7;
  // Disabled CTA to signify that request has been submitted
  Button success = 8;
  // CTA to retry record interest action
  Button try_again = 9;

  // disclaimer_msg, success_msg, try_again_msg is shown on same UI position
  // If user has not already submitted interest, disclaimer msg is shown, after submitting interest if request was successful
  // success_msg is shown and in case of failure try_again_msg is shown
  // eg: By proceeding, I consent to Fi checking my LRS limit & eligibility (per RBI) to purchase international stocks.
  api.typesv2.common.Text disclaimer_msg = 10;
  // eg: We'll be in touch. We're super excited for you to try this feature.
  api.typesv2.common.Text success_msg = 11;
  // eg: Our servers are busy sorting submissions! Do retry.
  api.typesv2.common.Text try_again_msg = 12;
}

message Button {
  string bg_color = 1;
  api.typesv2.common.Text text = 2;
  // used to add status on button like whether button is enabled or disabled
  enum ButtonStatus {
    // keeping default behaviour enabled
    BUTTON_STATUS_ENABLED = 0;
    BUTTON_STATUS_DISABLED = 1;
  }
  ButtonStatus status = 3;
  // url for the image to be shown on the button
  string img_url = 4;
}

message LandingPage {
  oneof top_component {
    // list of quick stats or pointers to pitch US Stocks as a feature to user
    // eg: Invest in top global companies
    PitchComponent pitch = 2;
    // presents a summary of existing investments for the user
    InvestmentSummary investment_summary = 3;
    // present us stocks wallet promo header
    WalletPromotionHeaderComponent wallet_promo_header = 7;
  }
  // tabs to be shown on landing page
  repeated LandingPageTab tabs = 4;
  // LandingPageTab Id which should be selected by default
  LandingScreenTabType default_selected_tab = 5 [deprecated = true];

  // Using an index to select the default tab, uses 0 indexing
  int32 default_selected_tab_index = 8;
  // page title is displayed at the top of the screen
  // eg: $ US Stocks
  api.typesv2.ui.IconTextComponent page_title = 6;
}

// represent row entry in invoice screen
// ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=9660%3A28168&t=Mg6zB9NKNoSLexch-4
message InvoiceDisplayEntry {
  api.typesv2.common.Text title_text = 1;
  api.typesv2.common.Text value_text = 2;

  // if nil, ignore and do not show info icon.
  deeplink.Deeplink info_pop_deeplink = 3;

  // Represent if the value is html or not
  // if contains_hyperlink is true then value is html
  // Eg: process ^^link^^text^^ hyper link format only if this is true
  bool contains_hyperlink = 4;

  // represent if the check box is checked or not
  // if true then this will be checked
  // applicable only if `should_show_check_box` is set to true
  bool is_checked = 5;

  // represent if ui need to show check box or not
  // if true ui need to render check box pattern
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=9675%3A28732&t=AQqR5horUHYpoQ2r-4
  bool should_show_check_box = 6;

  // represent tag need to render
  // if it is nil then client need not render ui
  // Eg: LAUNCH OFFER
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=9660%3A28168&t=AQqR5horUHYpoQ2r-4
  api.typesv2.common.Text tag = 7;

  // represent bg color of the entry
  // if send this color is handled
  // else client will be render default color
  string bg_color = 8;

  // represent unique enum value for check box if 'should_show_check_box' is true
  // eg: INVOICE_ENTRY_IDENTIFIER_INSTANT_PURCHASE
  InvoiceEntryType entry_type = 9;

  // represent api.typesv2.Money value of charge applied
  // eg: 10 rupee for instant purchase
  api.typesv2.Money amount = 10;
}


// this enum is send for client identification and help in identification on server if user opted
// And help in finding total amount according to user selected check box
// entry which doesnt require check box need to use INVOICE_ENTRY_TYPE_UNSPECIFIED type
enum InvoiceEntryType {
  INVOICE_ENTRY_TYPE_UNSPECIFIED = 0;
  // represent unique identifier for instant purchase line item
  // only one entry exist of this type in response
  INVOICE_ENTRY_TYPE_INSTANT_PURCHASE = 1;

  // represent unique identifier for total amount row
  // when ever user check any check box client need to amount corresponding
  // it and change total amount accordingly
  // Note: only one entry exist of this type in response
  // eg: total amount :100 when user check instant pay of 10
  // then client need to show total amount as 110 in ui
  INVOICE_ENTRY_TYPE_TOTAL_AMOUNT = 2;
}

enum StockType {
  STOCK_TYPE_UNSPECIFIED = 0;
  STOCK_TYPE_INDIVIDUAL_COMPANY = 1;
  STOCK_TYPE_ETF = 2;
}

// quantity is a oneof type message to specify quantity of stocks associated for a stock
// quantity can be either amount or units of associated stock to sell/buy
message Quantity {
  oneof qty {
    api.typesv2.Money amount = 1;
  }
}

message TradeAmount {
  api.typesv2.Money amount_entered_by_user = 6;
  // calculated_trade_amount = amount_entered_by_user - charges(charges include brokerage etc)
  // value for calculated_trade_amount is obtained by evaluating an Expression sent in OrderCard.trade_order_amount_expression field
  // Client is expected to receive the expression in response of GetBuyDetails or GetSellDetails RPC
  api.typesv2.Money calculated_trade_amount = 7;
}

// UserInputBasedComputedField specifies fields that needs to be calculated at client
// evaluation logic is sent from BE as Expression
// client is responsible for evaluating the expression and assigning the value to specified field
//
// eg: for calculating commission to be charged for a Buy order
// expression logic required is Min(0.0025 * amount_entered, 100)
// since amount_entered field depends upon value entered by user, there are following approaches to calculate commission
// 1. Call BE API with entered amount and get the calculated
// 2. Perform calculation at client, with calculation strategy specified by BE by an enum
// 3. Provide expression for calculating the value from BE along with a replaceable value and perform the evaluation at client for same based on the expression
message UserInputBasedComputedField {
  api.typesv2.common.Text leading_text = 1;
  TextVariant value_text = 2;
  InfoPopUp info_pop_up = 4 [deprecated = true];
  api.typesv2.ui.IconTextComponent info_itc = 5;
}

// TextVariant represents multiple variations of Text
// at present, it can be either a text input of EvaluatedText
message TextVariant {
  oneof text_variant {
    api.typesv2.common.Text text = 2;
    EvaluatedText evaluated_text = 3;
  }
}

// InfoPopup is used to provide additional information
message InfoPopUp {
  // ITC is the clickable component
  // on clicking this itc, pop up will be presented to user
  api.typesv2.ui.IconTextComponent itc = 1;
  PopUpData pop_up_data = 2;
  // PopUpData holds data to be presented on the pop-up
  message PopUpData {
    repeated LineItem line_items = 5;
    int32 corner_radius = 6;
  }
}

message LineItem {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text subtitle = 2;
  TextVariant value_text = 3;
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 4;
  Padding padding = 5;
  message Padding {
    int32 left = 1;
    int32 right = 2;
    int32 top = 3;
    int32 bottom = 4;
  }
}

// EvaluatedText contains expression to be evaluated at client
message EvaluatedText {
  // defines style related params for the expression to be displayed on UI
  api.typesv2.common.Text text = 1;
  // expression to be evaluated
  Expression expression = 2;
  // prefix string to be appended before the value obtained from expression while displaying on the UI
  string prefix = 3;
  // suffix string to be appended after the value obtained from expression while displaying on the UI
  string suffix = 4;
  // value after expression evaluation can be a double. Precision defines the digits to be displayed after decimal
  int32 precision = 5;
}

// Expression is a mathematical expression that supports operations defined in Operator enum
// Operation is applied to the specified operands
// eg: Operator: PLUS
// Operands: 3, 4
// Result: 7
message Expression {
  Operand left_operand = 1;
  // operation that needs to be performed between left and right operands
  Operator operator = 2;
  Operand right_operand = 3;
}

// 0.0025*amount_entered

// 0.0025*15
message Operand {
  // operand can either be another Expression, a numeric value or a variable
  oneof operand {
    Expression expression = 1;
    double value = 2;
    // variable is expected to be replaced by client during evaluation
    ExpressionVariable variable = 3;
  }
}

// https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-18249&t=s1VFrnNT0Tzby2bG-4
// Used for Buy/Sell order page
message TradeDetails {
  // For the top bar
  // Stock logo stock name stock nav and the wallet information
  NavbarDetails nav_bar = 1;

  // https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-18213&t=s1VFrnNT0Tzby2bG-4
  message NavbarDetails {
    StockDetails stock_details = 1;
    api.typesv2.ui.IconTextComponent wallet_summary = 2;
  }

  // Represent current market price of symbol
  // Used for calculations based on the latest stock price
  api.typesv2.Money stock_price = 2;

  repeated TabInfo order_tabs = 3;

  //https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-18272&t=s1VFrnNT0Tzby2bG-4
  message TabInfo {
    // Amount / Buy in Quantity
    api.typesv2.ui.IconTextComponent title = 1;
    // Amount/ Buy In Quantity with colors and background for selected component
    api.typesv2.ui.IconTextComponent selected_title = 2;

    oneof order_card {
      TradeByAmountOrderTab amount_card = 3;
      TradeByQuantityOrderTab quantity_card = 4;
    }
    // BE should use this to send the default seleceted tab
    bool isTabSelected = 5;
    // BE should use this to allow user with an option to sell all units
    // Note: Will only be used in case of sell order type
    api.typesv2.common.ui.widget.CheckboxItem trade_all_consent = 6;
  }

  TradeDetailsFooter footer = 4;

  // value of the expression is expected to be passed in CreateBuyOrder/CreateSellOrder request
  Expression trade_order_amount_expression = 5;

  // Client should handle live streaming of stock price if turned on
  bool should_update_real_time = 6;

  // helps in validation of the amount entered by the user
 // and request the user to add funds
 // it is in USD
  api.typesv2.Money wallet_balance = 7;
}

message StockDetails {
  api.typesv2.common.Image icon = 1;
  // adding title and subtitle in different fields instead of single key value pair
  // because we will be live-streaming the stock price in subtitle
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text subtitle = 3;
}

// https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-18106&t=s1VFrnNT0Tzby2bG-4
message TradeByAmountOrderTab {

  // Represent suggested amount and default amount
  SuggestedAmount suggested_amount = 1;

  ValidationDetails validation_details = 2;
}

// https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-17888&t=s1VFrnNT0Tzby2bG-4
message TradeByQuantityOrderTab {
  // Default will be 0, any value above this should be used by client to display
  double default_quantity = 1;

  // Default value will be false. For stocks which do not allow fractional values mark this true
  bool disable_fractional_qty = 2;

  TradeByQuantityToggle trade_by_control = 3;

  ValidationDetails validation_details = 4;

}

// https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-17634&t=oaZK4wdpJUAFWAOh-4
message TradeByQuantityToggle {
  api.typesv2.ui.Toggle toggle = 1;
  // Will contain details of the view to be displayed for left toggle value
  ToggleView left_toggle_value_view = 2;
  // Will contain details of the view to be displayed for right toggle value
  ToggleView right_toggle_value_view = 3;

  message ToggleView {
    // Currently we donot have any view changes for Market Price toggle hence will only have the Limit Price Details
    oneof toggle_view {
      LimitPriceDetails limit_price = 1;
    }
  }
}

// https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-17635&t=s1VFrnNT0Tzby2bG-4
message LimitPriceDetails {
  // Suggested amounts list and default amount
  SuggestedAmount suggested_amount = 1;
  // "Note: Limit Price set is lower than Market Price"
  api.typesv2.ui.IconTextComponent nudge = 2;
  LimitPriceValidity price_validity = 3;
  // "Only Limit Price is applicable on this stock" Text
  api.typesv2.ui.IconTextComponent info_label = 4;
  string border_color = 5;
  int32 corner_radius = 6;
}

//https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34031-17418&t=s1VFrnNT0Tzby2bG-4
message LimitPriceValidity {
  // "Valid for: " client will have to append the selected validity value here
  api.typesv2.common.Text display_text = 1;
  // User has to tap to change the option
  api.typesv2.common.Image chevron = 2;

  repeated ValidityOption validity_options = 3;

  message ValidityOption {
    string id = 1;
    string validity_display_text = 2;
    bool isSelected = 3;
  }
  string bg_color = 4;
  int32 corner_radius = 5;
}

// https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-16643&t=s1VFrnNT0Tzby2bG-4
message TradeDetailsFooter {
  TradeSummaryDetails trade_summary_details = 1;
  // Swipe button to proceed with the transactions if all validations have passed
  api.typesv2.ui.SwipeButton swipe_button = 2;
  // Add funds button to be used whe the amount entered by user exceed the amount available in wallet
  deeplink.Cta add_fund_cta = 3;
  // Used to track market status (open, closed) and the status text
  // Client will only show the text only if all validations are passed and market is open
  // https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34046-21091&t=cwZoVHZJtNnQQ3cw-4
  MarketDetails market_details = 4;
}

message TradeSummaryDetails {
  string background_color = 1;

  int32 corner_radius = 2;

  // trade_summary_details are used to present additional information to user which may or may not change on change in user input. eg: Buy Amount
  // It may contain fields that might need evaluation at client
  // eg: Commission applicable on the amount entered by user for a trade order
  // https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34031-16100&t=s1VFrnNT0Tzby2bG-4
  repeated UserInputBasedComputedField summary_details = 3;
}
