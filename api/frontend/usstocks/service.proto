syntax = "proto3";

package frontend.usstocks;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/metadata.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/investment/ui/bottom_sheet.proto";
import "api/frontend/pay/transaction/service.proto";
import "api/frontend/usstocks/account_activity.proto";
import "api/frontend/usstocks/client_states/enums.proto";
import "api/frontend/usstocks/collections.proto";
import "api/frontend/usstocks/enums.proto";
import "api/frontend/usstocks/onboarding.proto";
import "api/frontend/usstocks/search.proto";
import "api/frontend/usstocks/symbol.proto";
import "api/frontend/usstocks/uss_sip.proto";
import "api/frontend/usstocks/usstocks.proto";
import "api/frontend/usstocks/wallet.proto";
import "api/rpc/method_options.proto";
import "api/rpc/page.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/image_type.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/screen_header.proto";
import "api/typesv2/ui/swipe_button.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/usstocks";
option java_package = "com.github.epifi.gamma.api.frontend.usstocks";

service USStocks {
  // will be used to get the usstocks onboarding status for given actorid
  // return deeplink of screen to continue onboarding step
  //Used for one time during cycle
  rpc GetNextOnboardingStep (GetNextOnboardingStepRequest) returns (GetNextOnboardingStepResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // primary RPC for collecting data from customer
  // return next screen deeplink and update onboarding stage
  rpc CollectDataFromCustomer (CollectDataFromCustomerRequest) returns (CollectDataFromCustomerResponse) {}

  // RPC to collect data from customer via stream
  // This is expected to be used when the data sent from client exceeds unary RPC payload limit
  rpc CollectStreamingDataFromCustomer (stream CollectStreamingDataFromCustomerRequest) returns (CollectStreamingDataFromCustomerResponse) {}

  // RPC is used for fetching metadata as per the buy screen
  // RPC request requires stock id as mandatory field
  // RPC returns stock details, Suggested amount, default amount
  rpc GetBuyDetails (GetBuyDetailsRequest) returns (GetBuyDetailsResponse) {}

  // RPC is used for getting TCS, GST, Fee etc
  // RPC request requires amount as mandatory field
  // RPC returns Invoice(TCS,GST,stock price,total price) and Forex rate during calculation
  // this rpc is called while order confirmation screen in Buy/Sell screen
  rpc GetInvoice (GetInvoiceRequest) returns (GetInvoiceResponse) {}

  // RPC is used for creating order and sending require payment details to client
  // The request requires stock id and invoice details as mandatory
  // The response consists of payment related attributes
  // which are used for creating cred block. The cred block will be passed onto the partner bank when user authenticates the transaction in subsequent api calls.
  rpc CreateOneTimeBuyOrder (CreateOneTimeBuyOrderRequest) returns (CreateOneTimeBuyOrderResponse) {
    option (rpc.auth_required) = true;
    option (rpc.add_device_verification_nonce) = true;
  }

  // RPC is used to get the status of a one time order already created.
  // RPC request requires cred block and order_id as mandatory fields
  // The response contains the deeplink to the next screen and the screen option having required text details if payment is successful.
  rpc GetOneTimeOrderStatus (GetOneTimeOrderStatusRequest) returns (GetOneTimeOrderStatusResponse) {
    option (rpc.auth_required) = true;
  }

  // RPC is used for fetching metadata as per the sell screen
  // RPC request requires stock id as mandatory field
  // RPC returns stock details, Suggested amount, default amount
  rpc GetSellDetails (GetSellDetailsRequest) returns (GetSellDetailsResponse) {}

  // RPC is used for creating sell order with vendor
  // RPC request requires stock id , amount, or should_sell_all as mandatory field
  // RPC returns deeplink to the next screen and screen option having required text details
  rpc InitiateSellOrder (InitiateSellOrderRequest) returns (InitiateSellOrderResponse) {}

  // RPC is used to fetch order Details
  // RPC request requires order_id as mandatory field
  // RPC returns order details, price details, order activity, ETA
  rpc GetOrderReceipt (GetOrderReceiptRequest) returns (GetOrderReceiptResponse) {}

  // RPC is used to cancel order which are in processing stage
  // RPC request requires order_id as mandatory field
  // RPC returns deeplink to status screen
  rpc CancelOrder (CancelOrderRequest) returns (CancelOrderResponse) {}

  // RPC is used to account level or stock level activities
  // if stock id is empty then it mean account level activities
  // or in My activity of USSTOCKS_SYMBOL_DETAILS_SCREEN screen
  // RPC return list of activity to be displayed
  // It is is paginated rpc
  // Note:
  // For First call request PageContextRequest is nil
  // For Last page response PageContextResponse is nil
  rpc GetSymbolActivity (GetSymbolActivityRequest) returns (GetSymbolActivityResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC is used to get stocks/ETF holding details
  // RPC returns list of Holding for each symbol
  // It is is paginated rpc
  // It is used in USSTOCKS_PORTFOLIO_SCREEN screen
  rpc GetPortfolio (GetPortfolioRequest) returns (GetPortfolioResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC is used to get details like price, returns, name, logo, etc. of a stock / ETF
  // For deeper details like company financials, etc. GetSymbolDecisionFactors is expected to be used
  // RPC requires stock id as mandatory field
  // It is used in USSTOCKS_SYMBOL_DETAILS_SCREEN screen
  rpc GetSymbolDetails (GetSymbolDetailsRequest) returns (GetSymbolDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetSymbolHistoricalPrices returns the company name, logo, current market price for a stock based on its ID.
  // It also returns historical bars (closing prices at periodic intervals) of stock to enable clients to render price charts
  // This is an alternative to GetSymbolDetails RPC for unauthenticated clients
  rpc GetSymbolHistoricalPrices (GetSymbolHistoricalPricesRequest) returns (GetSymbolHistoricalPricesResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }

  // RPC is used to get key facts about a stock / ETF like company details, financials, etc.
  // RPC requires stock id as mandatory field
  // It is used in USSTOCKS_SYMBOL_DETAILS_SCREEN screen
  rpc GetSymbolDecisionFactors (GetSymbolDecisionFactorsRequest) returns (GetSymbolDecisionFactorsResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }

  // GetStockForSymbol is for Fi website to provide a stock symbol from URL of a US stocks related screen
  // e.g., AAPL (publicly traded symbol for Apple) for https://fi.money/us-stocks/aapl and in return get a valid stock ID which
  // can then be used to get stock details via other RPCs
  rpc GetStockForSymbol (GetStockForSymbolRequest) returns (GetStockForSymbolResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }

  // GetLandingPage sends data to client for loading landing page
  // response includes the data for rendering
  // 1. pitch component for non-invested users
  // 2. brief view of existing investments for invested users
  // 3. list of tabs with deeplink (eg: Explore, Your Stocks, Watch listed etc)
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3395%3A14857&t=ddD12jDMquT0n8CN-4
  rpc GetLandingPage (GetLandingPageRequest) returns (GetLandingPageResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetExplorePage is called from US Stocks landing page when user has chosen Explore tab
  // lists all the components for rendering explore page
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3584%3A17295&t=ddD12jDMquT0n8CN-4
  rpc GetExplorePage (GetExplorePageRequest) returns (GetExplorePageResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetWatchlist returns the watchlist-ed stocks for the user
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3590%3A19292&t=czhgbWNf8w4eiOb7-4
  rpc GetWatchlist (GetWatchlistRequest) returns (GetWatchlistResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetSearchLandingScreen returns the search landing screen which is the first screen when user lands on search
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3388%3A11427&t=w1DHFbeYFbeIA41C-4
  rpc GetSearchLandingScreen (GetSearchLandingScreenRequest) returns (GetSearchLandingScreenResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetSearchResults returns the search results for the given query
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3388%3A11888&t=w1DHFbeYFbeIA41C-4
  rpc GetSearchResults (GetSearchResultsRequest) returns (GetSearchResultsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetSymbolUpdates is a server side stream RPC
  // expects list of identifier for stocks in request for which client wants to receive updates
  // RPC streams updates for individual symbol. Update may include changes in price, growth_text etc
  // Note:
  // 1. In case the stream connection is broken (may happen due to deployment, panic etc), client should re-initiate the connection using same RPC
  // 2. Connection should be kept open only when user is on the particular page where updates are expected.
  // 3. Stream RPC should only be used during market open duration. For other cases, latest available price should be sent as part of other unary RPC
  // TODO(shubham.c): Add support to inform client of market non-open hours, so that retries on symbol update connection is not done
  rpc GetSymbolUpdates (GetSymbolUpdatesRequest) returns (stream GetSymbolUpdatesResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }

  // GetCollectionList returns the list of collections
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3939%3A18488&t=GDW2MGrdUiYaVhrF-4
  // this is a non-paginated RPC
  rpc GetCollectionList (GetCollectionListRequest) returns (GetCollectionListResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }

  // GetCollectionDetails returns the collection details for the given collection id
  // it include collection description, list of stocks in the collection etc
  // this is a paginated RPC
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3939%3A18695&t=GDW2MGrdUiYaVhrF-4
  rpc GetCollectionDetails (GetCollectionDetailsRequest) returns (GetCollectionDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetGeneralCollectionDetails returns the collection details for the given collection id
  // it includes collection description, list of stocks in the collection etc
  // this is a paginated RPC
  // auth is not required for this rpc
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3939%3A18695&t=GDW2MGrdUiYaVhrF-4
  rpc GetGeneralCollectionDetails (GetGeneralCollectionDetailsRequest) returns (GetGeneralCollectionDetailsResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }

  // UpdateWatchlist rpc is used to add/remove stock from user's watchlist
  // currently only one watchlist per user is supported, so watchlist_id is not required in the request
  rpc UpdateWatchlist (UpdateWatchlistRequest) returns (UpdateWatchlistResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // A2 Form is an Application cum Declaration form. Mandated for international fund transfer
  // GetPreFilledA2Form expects InvoiceDetails of the corresponding buy intent, prepares and returns the prefilled A2 form
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3595%3A16717&t=Q0AVTdqPD53ea8B5-4
  rpc GetPreFilledA2Form (GetPreFilledA2FormRequest) returns (GetPreFilledA2FormResponse) {}

  // RPC to record user's interest in using US Stocks feature
  // required for cases like, interested user's may be provided early access
  rpc SaveUserInterest (SaveUserInterestRequest) returns (SaveUserInterestResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  rpc GetAccountActivities (GetAccountActivitiesRequest) returns (GetAccountActivitiesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = true;
    option (rpc.skip_device_integrity_check) = true;
  }

  // GetWalletPage rpc sends data to client for loading the wallet screen
  // response includes data for rendering wallet balance, past wallet activity
  // and providing option to users to Add/Withdraw funds from wallet.
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=17153-35008&mode=design&t=gJA8e0O2Xn1AVdpt-0
  rpc GetWalletPage (GetWalletPageRequest) returns (GetWalletPageResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = false;
  }

  // RPC is used for fetching data required to render the wallet add funds screen
  rpc GetWalletAddFundsDetails (GetWalletAddFundsDetailsRequest) returns (GetWalletAddFundsDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = true;
    option (rpc.skip_device_integrity_check) = false;
  }

  // RPC is used for fetching data required to render the wallet withdraw funds screen
  rpc GetWalletWithdrawFundsDetails (GetWalletWithdrawFundsDetailsRequest) returns (GetWalletWithdrawFundsDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = true;
    option (rpc.skip_device_integrity_check) = false;
  }

  // RPC to validate the amount entered for adding/withdrawing funds from their US stocks wallet.
  // Client will call this rpc whenever user changes the amount on the add/withdraw fund pages.
  // Along with the validation, it will also provide it with amount converted to the other currency that
  // it needs to display to the user.
  // If the generate_invoice flag is set to true in request, the rpc will also generate the invoice.
  rpc ValidateUserWalletFundInputAndGenerateInvoice (ValidateUserWalletFundInputAndGenerateInvoiceRequest) returns (ValidateUserWalletFundInputAndGenerateInvoiceResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }

  // RPC is used for creating order and sending require payment details to client
  // The request requires invoice details as mandatory
  // The response consists of payment related attributes
  // which are used for creating cred block. The cred block will be passed onto the partner bank when user authenticates the transaction in subsequent api calls.
  rpc CreateWalletAddFundsOrder (CreateWalletAddFundsRequest) returns (CreateWalletAddFundsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = true;
    option (rpc.skip_device_integrity_check) = false;
  }

  // RPC is used for creating wallet withdraw funds order with vendor
  // RPC request requires amount, or should_withdraw_all as mandatory field
  // RPC returns deeplink to the next screen and screen option having required text details
  rpc CreateWalletWithdrawFundsOrder (CreateWalletWithdrawFundsOrderRequest) returns (CreateWalletWithdrawFundsOrderResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = true;
    option (rpc.skip_device_integrity_check) = false;
  }

  // CreateBuyOrder is used to create us stocks buy order
  // RPC assumes that funding for order will be done using wallet at vendor
  // pre-requisite for executing buy order is that sufficient balance should be present at vendor
  rpc CreateBuyOrder (CreateBuyOrderRequest) returns (CreateBuyOrderResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = true;
    option (rpc.skip_device_integrity_check) = false;
  }

  // CreateBuyOrder is used to create us stocks sell order
  // RPC assumes that amount after selling the security will be credited to wallet at vendor
  rpc CreateSellOrder (CreateSellOrderRequest) returns (CreateSellOrderResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = true;
    option (rpc.skip_device_integrity_check) = false;
  }

  // GetDropOffBottomSheet returns the dropoff bottom sheet to be shown to user if they tap back or exit in a screen
  // Bottom sheets are identified using unique id provided in screen header
  rpc GetDropOffBottomSheet (GetDropOffBottomSheetRequest) returns (GetDropOffBottomSheetResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = true;
    option (rpc.savings_account_required) = true;
    option (rpc.skip_device_integrity_check) = false;
  }

  // GetSimilarStocks is a user-agnostic RPC that returns a short list of popular stocks that are similar to a stock.
  // Additional context like the collection a stock belongs to can be used by clients
  // to get more context-aware similar stocks.
  // If no similar stocks are found, the RPC returns a NotFound code.
  rpc GetSimilarStocks (GetSimilarStocksRequest) returns (GetSimilarStocksResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = false;
  }

  // GetStockTradeDetailsForExchangePage RPC is  used to retrieve details for showing both
  // the Buy and Sell stock pages. This RPC provides stock information needed for
  // executing both market and limit orders.
  //
  // Parameters:
  // - stockID: The unique identifier of the stock (required).
  // - orderSide: The type of order, either 'buy' or 'sell' (required).
  // Figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34022-17853&node-type=frame&t=mwzhgZXcWAcKun4o-0
  rpc GetStockTradeDetailsForExchangePage (GetStockTradeDetailsForExchangePageRequest) returns (GetStockTradeDetailsForExchangePageResponse) {
    option (rpc.auth_required) = true;
  }

  // InitiateTradeOrder RPC is used to place either a buy or sell order for a stock.
  // Assumes that the necessary funds (for buy orders) or quantity of stocks (sell orders) are available in the user's wallet.
  // Pre-requisite for executing a buy order:
  // - The user should have sufficient balance in their vendor wallet.
  // Pre-requisite for executing a sell order:
  // - The user should hold sufficient stock quantity in their account.
  //
  // The client should ensure that idempotency is maintained using the client_order_id,
  // to avoid the creation of multiple orders from a single intent.
  //
  // A successful response will include a deeplink to navigate the user to the order status screen.
  rpc InitiateTradeOrder (InitiateTradeOrderRequest) returns (InitiateTradeOrderResponse) {
    option (rpc.auth_required) = true;
  }

  // GetSIProjection returns the historical returns for a certain amount of SIP investment in a stock
  // over a period of time, in the form of a graph.
  // These projections are based on historical data and are not indicative of future performance.
  rpc GetSIPProjection (GetSIPProjectionRequest) returns (GetSIPProjectionResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }
}

message CreateBuyOrderRequest {
  frontend.header.RequestHeader req = 1;
  // Represent internal identifier for stock
  string stock_id = 2;
  // quantity of stock requested to be bought by user
  // use TradeAmount instead
  Quantity qty = 3 [deprecated = true];
  // client order Id for ensuring idempotency of request,
  // so that multiple orders are not created for user intent to create a single order
  string client_order_id = 4;
  // Trade amount holds amount related details for requested trade order
  TradeAmount trade_amount = 5;

}

message CreateBuyOrderResponse {
  enum Status {
    OK = 0;

    // internal server error
    INTERNAL = 13;

    // day trade limit reached, failed to create buy order
    DAY_TRADE_LIMIT_BREACHED = 101;

    // user doesn't have enough buying power
    // example scenario: user has placed an order to withdraw all funds from their wallet, but then tries to buy stocks.
    INSUFFICIENT_BUYING_POWER = 102;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink to next screen
  // should be order status screen
  deeplink.Deeplink next_screen = 2;
}

message CreateSellOrderRequest {
  frontend.header.RequestHeader req = 1;
  // Represent internal identifier for stock
  string stock_id = 2;
  // quantity of stock requested to be sold by user
  // use TradeAmount instead
  Quantity qty = 3 [deprecated = true];
  // if the user selects sell all then it's true
  bool should_sell_all = 4;
  // client order Id for ensuring idempotency of request,
  // so that multiple orders are not created for user intent to create a single order
  string client_order_id = 5;
  // Trade amount holds amount related details for requested trade order
  TradeAmount trade_amount = 6;
}

message CreateSellOrderResponse {
  enum Status {
    OK = 0;

    // internal server error
    INTERNAL = 13;

    // day trade limit reached, failed to create sell order
    DAY_TRADE_LIMIT_BREACHED = 103;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink to next screen
  // should be order status screen
  deeplink.Deeplink next_screen = 2;
}

message GetGeneralCollectionDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string collection_id = 2;
  // sorts the stocks in the collection based on the given field (optional)
  SortOptionType sort_option = 3;
  // for pagination
  // for first page, page_context is nil
  rpc.PageContextRequest page_context = 4;
}

message GetGeneralCollectionDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // for pagination
  // for last page, page_context is nil
  rpc.PageContextResponse page_context = 2;
  // contains the collection details like description, list of stocks etc
  // details that are user-specific like watchlisted stocks won't be populated in this.
  CollectionDetailsSection details_section = 3;
}

message SaveUserInterestRequest {
  frontend.header.RequestHeader req = 1;
}

message SaveUserInterestResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message GetPreFilledA2FormRequest {
  frontend.header.RequestHeader req = 1;
  // invoice details for corresponding buy intent
  InvoiceDetails invoice_details = 2;
}

message GetPreFilledA2FormResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // title for the page/dialog
  // eg: Application for Remittance Abroad
  api.typesv2.common.Text title = 2;
  // sub-title for the page/dialog
  // eg: FORM A2
  api.typesv2.common.Text sub_title = 3;
  // list of form components
  repeated FormComponent form_components = 4;
}

message UpdateWatchlistRequest {
  frontend.header.RequestHeader req = 1;
  // add or remove stock from watchlist
  WatchlistAction action = 2;
  // stock to be added/removed from watchlist
  string stock_id = 3;
  // watchlist id will be added here if multiple watchlists per user is supported in future
}

message UpdateWatchlistResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message GetCollectionDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string collection_id = 2;
  // sorts the stocks in the collection based on the given field (optional)
  SortOptionType sort_option = 3;
  // for pagination
  // for first page, page_context is nil
  rpc.PageContextRequest page_context = 4;
}

message GetCollectionDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // for pagination
  // for last page, page_context is nil
  rpc.PageContextResponse page_context = 2;
  // contains the collection details like description, list of stocks etc
  CollectionDetailsSection details_section = 3;
}

message GetCollectionListRequest {
  frontend.header.RequestHeader req = 1;
}

message GetCollectionListResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // list of collections which are to be displayed on the screen
  repeated CollectionListItem collections = 2;
}

message GetSymbolUpdatesRequest {
  frontend.header.RequestHeader req = 1;
  // list of symbolIds for which client is expecting updates
  // Note: These are actually stock IDs
  repeated string symbol_ids = 2;
}

message GetSymbolUpdatesResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // updated stock prices
  // Key: stock id
  // value: updated price/growth percentage etc
  map<string, SymbolUpdate> updates = 2 [deprecated = true];

  map<string, StockPriceUpdateDataPoint> stock_price_updates_map = 3;
}

message StockPriceUpdateDataPoint {
  oneof datapoint {
    SymbolUpdate price_update = 1;

    // stock bar for a 1-min interval
    StockBar minute_bar = 2;
  }
}

// A Bar object contains information such as open, high, low, and closing price for a stock.
message StockBar {
  // start-time of the time-interval trades are aggregated over to form the bar
  // x-axis value in price chart
  google.protobuf.Timestamp timestamp = 1;

  // Stock price at which the last trade in the time-interval happened
  // y-axis value in price chart
  double close_price = 2;

  // command to update data in price chart
  // this will contain a data point which web will use to draw price chart
  string update_chart_data_js_command = 3;
}

message GetSearchLandingScreenRequest {
  frontend.header.RequestHeader req = 1;
}

message GetSearchLandingScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  SearchLandingScreen screen = 2;
}

message GetSearchResultsRequest {
  frontend.header.RequestHeader req = 1;
  StockSearchFilter filter = 2;
  // for pagination
  // for first page, page_context is nil
  rpc.PageContextRequest page_context = 3;
}

message GetSearchResultsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // for pagination
  // for last page, page_context is nil
  rpc.PageContextResponse page_context = 2;
  oneof container {
    SearchResultsSection results = 3;
    // example usage: "No stocks found"
    SearchResultsInfoSection info = 4;
  }
}

message GetWatchlistRequest {
  frontend.header.RequestHeader req = 1;
  // for pagination
  // for first page, page_context is nil
  rpc.PageContextRequest page_context = 2;
  // sorts the stocks in the watchlist based on the given field (optional)
  SortOptionType sort_option = 3;
  // Filled using payload field of LandingPageTab from GetLandingPageResponse
  bytes payload = 4;
}

message GetWatchlistResponse {
  frontend.header.ResponseHeader resp_header = 1;
  oneof container {
    // if this section is locked, we show unlock details
    LockedSection locked_section = 2;
    // if this section is unlocked, we show watchlist-ed items
    WatchlistSection watchlist_section = 3;
  }
  // for pagination
  // for last page, page_context is nil
  rpc.PageContextResponse page_context = 4;
}

message GetLandingPageRequest {
  frontend.header.RequestHeader req = 1;
  // Filled using the entry point of usstocks.LandingPageScreenOptions
  string entry_point = 2;
}

message GetLandingPageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  oneof top_component {
    // list of quick stats or pointers to pitch US Stocks as a feature to user
    // eg: Invest in top global companies
    // [Deprecated]: use LandingPage instead
    PitchComponent pitch = 2 [deprecated = true];
    // [Deprecated]: use LandingPage instead
    // presents a summary of existing investments for the user
    InvestmentSummary investment_summary = 3 [deprecated = true];
  }
  // tabs to be shown on landing page
  // [Deprecated]: use LandingPage instead
  repeated LandingPageTab tabs = 4 [deprecated = true];
  // LandingPageTab Id which should be selected by default
  // [Deprecated]: use LandingPage instead
  LandingScreenTabType default_selected_tab = 5 [deprecated = true];
  // page title is displayed at the top of the screen
  // eg: $ US Stocks
  // [Deprecated]: use LandingPage instead
  api.typesv2.ui.IconTextComponent page_title = 6 [deprecated = true];

  // for staged roll out of the feature, different screen is required to be shown for different set of users
  // eg: Internal users should be able to see actual landing page whereas External users will be seeing pre-launch screen
  // In case of staged rollout, there is requirement to show landing page to x% of users and others to show pre-launch page
  oneof page {
    // US Stocks landing page, this page is the entry point of US Stocks for end user
    LandingPage landing_page = 7;
    // Pre launch screen is a screen to record interest of user for US Stocks feature
    PreLaunchPage pre_launch = 8;
  }

  // Flag for wallet active to be sent for events if user has some balance in wallet
  // e.g will be true if wallet balance greater than zero else false
  bool is_wallet_active = 9;

  // Flag for if onboarding is completed to be sent for events if user is onboarded on usstocks
  // e.g will be true if user has onboarded but not added funds to wallet
  bool is_onboarded = 10;
}

message GetExplorePageRequest {
  frontend.header.RequestHeader req = 1;
  // Filled using payload field of LandingPageTab from GetLandingPageResponse
  bytes payload = 2;
}

message GetExplorePageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // list of components
  repeated Component components = 2;
}

message GetNextOnboardingStepRequest {
  frontend.header.RequestHeader req = 1;
}

// Return the next OnBoardingStepType,deeplink of next screen, static data for next screen
message GetNextOnboardingStepResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;

    // Status shown when vkyc is needed for user
    VKYC_REQUIRED = 100;

    // user need to upgrade his app
    APP_UPGRADE_NEEDED = 101;
  }
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
  // Map of all event properties and their values
  map<string, string> event_properties = 3;

}

// Provide the request payload from each onboarding screen
message CollectDataFromCustomerRequest {
  frontend.header.RequestHeader req = 1;
  clientstate.OnboardingStepType onboarding_step = 2;
  OnBoardingRequestPayload payload = 3;
}

// Return the next OnBoardingStepType,deeplink of next screen, static data for next screen
message CollectDataFromCustomerResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message CollectStreamingDataFromCustomerRequest {
  // request header, to authenticate request and get actor id
  frontend.header.RequestHeader req = 1;

  oneof CustomerData {
    // first chunk of the stream must be this
    // later chunks can contain chunks of streaming data (image, video etc.)
    BasicCustomerInfo basic_info = 2;

    // raw PAN card image data in chunks, no need of converting to base-64 encoding
    // clients are free to choose chunk size as long as number of chunks isn't in hundreds
    // ex chunk size can be 1MB since image sizes isn't expected to be more than 100 MB
    bytes pan_img_chunk = 3;
  }
}

message BasicCustomerInfo {
  // used to identify which step of onboarding
  clientstate.OnboardingStepType onboarding_step = 2;

  // image format of PAN
  api.typesv2.common.ImageType pan_img_type = 3;
}

message CollectStreamingDataFromCustomerResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // next screen to be shown to user if some user input is needed
  frontend.deeplink.Deeplink next_action = 2;
}

message GetBuyDetailsRequest {
  frontend.header.RequestHeader req = 1;
  // Represent internal identifier for stock
  string stock_id = 2;
}

message GetBuyDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  BuyDetails buy_details = 2;

  // help in validation of the amount entered by the user
  // and request the user for add the amount
  // it is in USD
  api.typesv2.Money available_balance = 3;

  // CTA to be prompted in case requested buy amount is less than available balance
  // CTA can be to redirect user to Add funds to savings account, Transfer funds to USD wallet
  deeplink.Cta insufficient_balance_cta = 4;

  // CTA to create buy order
  // if null, JIT flow should be followed
  // if not null, the CTA provided should be used to perform next action
  // USSTOCKS_BUY_ORDER_PROCESSING_SCREEN is expected as create_order_cta
  // since amount, qty is entered by user, those values are expected to be populated by client
  deeplink.Cta create_order_cta = 5;
}


message CreateOneTimeBuyOrderRequest {
  frontend.header.RequestHeader req = 1;
  // Represent internal identifier for stock
  string stock_id = 2;

  // represent invoice breakdown shown to the user
  // while placing the order
  InvoiceDetails invoice_details = 3;

  // represent list of the user opted feature (i.e selected)
  // eg: if user is opted to instant purchase then enum for corresponding check box is send back
  repeated InvoiceEntryType invoice_entries_type = 4;
}

// This message is a blatant copy of 'frontend.pay.transaction.CreateFundTransferOrderResponse'
// including the documentation
message CreateOneTimeBuyOrderResponse {

  // contains `device_integrity_nonce` required to generate safetynet attestaion.
  frontend.header.Metadata metadata = 5;

  enum Status {
    OK = 0;
    // The actor does not have permission to execute the specified operation.
    // the reason can be many but not limited to-
    // 1) account id passed in the request doesnt belong to the logged in actor
    // 2) logged in actor has blocked/reported payee actor
    // 3) logged in actor is blocked/reported by payee actor
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // The service is currently unavailable.  This is most likely a
    // transient condition, which can be corrected by retrying after some interval.
    // e.g., Downtime at partner banks, health checks failing for a given payment protocol
    UNAVAILABLE = 14;
  }
  frontend.header.ResponseHeader resp_header = 1;

  // If pin based authorization is required to execute payment for a given order.
  frontend.pay.transaction.PinRequiredType pin_required_type = 2;

  // Transaction attributes are required for the client to:
  //  i. Generate the credentials using a common library i.e., NPCI CL or Partner bank's CL
  //  ii. Present the transaction information e.g., payment protocol to the user
  frontend.pay.transaction.TransactionAttribute transaction_attribute = 3;

  // Used for calling 'AuthoriseFundTransfer' rpc
  string payment_client_request_id = 4;

  // there can be cases when forex rate which was shown to user is no longer available
  // new forex rate should be shown to user and order should be created post confirmation on the new fx rate
  // newFxRateConfirmationDialog holds display fields to be shown to user in such case
  deeplink.Deeplink newFxRateConfirmationDialog = 6;
}

message GetOneTimeOrderStatusRequest {
  frontend.header.RequestHeader req = 1;

  oneof order_identifier {
    // Used when the internal order id is known.
    // preferred identifier
    string order_id = 2;
    // Identifier for the payment(OMS) order in the 'AuthoriseFundTransfer' flow.
    string payment_client_request_id = 3;
  }
  // Timestamp when client started polling. Backend would time out on the basis of this
  // Note: Please ensure this timestamp is the actual poll start time and not getting refreshed for every
  // request. BE has no way of tying multiple poll requests so this can cause an infinite loop if timestamp
  // is refreshed
  google.protobuf.Timestamp polling_start_time = 4;
}

message GetOneTimeOrderStatusResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;
    // payment failed due to failure at bank end. This can be due to various reasons like
    // user account is frozen, etc.
    // in this case response header will also contain the error view
    // with granular level messages to be shown to the user
    PAYMENT_FAILED = 100;
  }
  frontend.header.ResponseHeader resp_header = 1;

  // Contains information for showing status related information
  // If the payment is successful
  // Eg https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=677%3A3894
  deeplink.Deeplink next_action = 2;

  // A timer for the client post which the client should request for status
  // Any attempt prior to this timer may not be honored & result in an error
  google.protobuf.Duration retry_timer = 3;

  enum PaymentStatus {
    PAYMENT_STATUS_UNSPECIFIED = 0;
    // Payment for order is being processed by the system
    // An actor must not retry payment, unless payment reaches an terminal state.
    PAYMENT_STATUS_IN_PROGRESS = 1;
    // Order payment has completed
    // Their would be respective message to display according to deeplink
    PAYMENT_STATUS_SUCCESS = 2;
    // Order payment has failed.
    // Client need to  translate the pay error view to specific error view
    PAYMENT_STATUS_FAILED = 3;
    // Payment is still in progress, but client needs to stop polling
    // and show the deeplink with given data
    PAYMENT_STATUS_IN_PROGRESS_TIMEOUT = 4;
  }

  PaymentStatus payment_status = 4;
}

message GetSellDetailsRequest {
  frontend.header.RequestHeader req = 1;
  // Represent internal identifier for stock
  string stock_id = 2;
}

message GetSellDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  SellDetails sell_details = 2;
  // CTA to create buy order
  // if null, JIT flow should be followed
  // if not null, the CTA provided should be used to perform next action
  // USSTOCKS_SELL_ORDER_PROCESSING_SCREEN deeplink is expected as create_order_cta
  // since amount, qty is entered by user, those values are expected to be populated by client
  deeplink.Cta create_order_cta = 3;
}

message InitiateSellOrderRequest {
  frontend.header.RequestHeader req = 1;
  // Represent internal identifier for stock
  string stock_id = 2;
  api.typesv2.Money amount = 3;

  // if the user selects sell all then it's true
  bool should_sell_all = 4;

  // represent invoice breakdown shown to the user
  // while placing the order
  InvoiceDetails invoice_details = 5;
}

message InitiateSellOrderResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

// Client request for invoice generation for a given amount
// Selected by the user during the buy
message GetInvoiceRequest {
  frontend.header.RequestHeader req = 1;

  // Represent the amount selected by the user during the buy/sell
  // amount is in USD
  api.typesv2.Money amount = 2;

  // represent Invoice screen from which it is being called
  enum InvoiceScreen {
    INVOICE_SCREEN_UNSPECIFIED = 0;
    // represent call from buy screen
    INVOICE_SCREEN_BUY = 1;
    // represent call from sell screen
    INVOICE_SCREEN_SELL = 2;
  }

  // represent screen from which it is being called
  InvoiceScreen invoice_screen = 3;

  string stock_id = 4;
}

// Represent GST,TCS calculation for given amount
// forex_rate_usd_to_inr is sent to the client to maintain consistency and
// improve user experience
// if forex rate not found for given amount then in response will return MaxAmountBreachedErrView
message GetInvoiceResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Represent text and value for invoice
  Invoice invoice = 2 [deprecated = true];
  oneof response {
    // Represent text and value for invoice
    Invoice invoice_details = 3;
    // if forex rate not found for given amount then it returns error msg and maximum available amount that user can enter
    MaxAmountBreachedErrView forex_rate_err_view = 4;
  }
}

// if enter amount by user is more than the deal available amount then it is used to show error msg and maximum available amount that user can enter
message MaxAmountBreachedErrView {
  // message that will be used to show to users below amount screen
  api.typesv2.common.Text msg = 1;
  // maximum available amount that user can enter
  api.typesv2.Money max_available_amount = 2;
}

message GetOrderReceiptRequest {
  frontend.header.RequestHeader req = 1;
  // represent order_id for which we fetch order details
  string order_id = 2 [deprecated = true];

  oneof identifier {
    // orderId for Buying/Selling of stocks
    string stock_order_id = 3;
    // Add/Withdraw funds order id.
    string wallet_order_id = 4;
    string account_activity_id = 5;
  }
}

message GetOrderReceiptResponse {
  frontend.header.ResponseHeader resp_header = 1;
  OrderReceipt order_receipt = 2;
}

message CancelOrderRequest {
  frontend.header.RequestHeader req = 1;

  // represent order_id for which we want to cancel order
  string order_id = 2 [deprecated = true];

  oneof order_identifier {
    string wallet_order_id = 3;
    string stock_order_id = 4;
  }
}

message CancelOrderResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // return order cancel screen deeplink
  frontend.deeplink.Deeplink next_action = 2;
}

message GetSymbolActivityRequest {
  frontend.header.RequestHeader req = 1;

  // Represent internal stock id
  // if empty then it activities for all symbols will be listed
  string stock_id = 2;

  rpc.PageContextRequest page_context = 3;
}

message GetSymbolActivityResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // [Deprecated]: use Activities.page_context instead
  rpc.PageContextResponse page_context = 2 [deprecated = true];
  // [Deprecated]: use Activities.activities instead
  repeated SymbolActivityCard symbol_activitiy_cards = 3 [deprecated = true];

  // Activities holds list of SymbolActivityCard
  // page_context holds information for next/prev page
  message Activities {
    rpc.PageContextResponse page_context = 2;
    repeated SymbolActivityCard activities = 3;
  }

  oneof response {
    // deprecated in favor of AccountActivities object
    Activities activities = 4 [deprecated = true];
    // empty state should be sent if no activity is present
    EmptyState zero_state = 5;
    // to be used instead of Activities.
    AccountActivities account_activities = 6;
  }
  // This would be sent only for the first page of the response
  // This is a static subsection, and would be sent only if the page token is nil
  // Backend would be capping the max number of sips to '5' to manage the latest activities
  VerticalSIPsComponent sip_component = 7;
}

message GetPortfolioRequest {
  frontend.header.RequestHeader req = 1;
  rpc.PageContextRequest page_context = 2;
  // sorts the stocks in the portfolio based on the given field (optional)
  SortOptionType sort_option = 3;
  // Filled using payload field of LandingPageTab from GetLandingPageResponse
  bytes payload = 4;
}

message GetPortfolioResponse {
  frontend.header.ResponseHeader resp_header = 1;
  rpc.PageContextResponse page_context = 2;
  // Deprecated: use StockListItems instead
  repeated PortfolioCard portfolio_cards = 3 [deprecated = true];
  oneof container {
    // if this section is locked, we show unlock details
    LockedSection locked_section = 4;
    // Deprecated: use uss_portfolio_section instead
    PortfolioSection portfolio_section = 5 [deprecated = true];
    // if this section is unlocked, we show usstock items or watchlist-ed items based on the tab selected
    USStocksPortfolioSection uss_portfolio_section = 6;
  }
}

message USStocksPortfolioSection {
  HorizontalSIPsComponent sip_tile_component = 1;
  PortfolioSection portfolio_section = 2;
}

message GetSymbolDetailsRequest {
  frontend.header.RequestHeader req = 1;

  // Represent internal identifier for stock
  string stock_id = 2;
}

message GetSymbolDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // tabs to show in stock details screen, e.g. Key facts, My Activity, etc.
  repeated SymbolDetailsTab tabs = 2;

  SymbolDetails symbol_details = 3 [deprecated = true];

  // details to show when user has invested in the stock
  // e.g. current value of investments, invested value, returns, avg. price, etc.
  repeated DisplayEntry investment_display_entries = 4 [deprecated = true];

  // Represent list of CTAs according to state of user with proper deeplink
  // e.g. buy, sell, onboard, watchlist, etc.
  repeated deeplink.Cta ctas = 5;

  // image of stock (company) logo
  api.typesv2.common.Image stock_logo = 6;

  // name of stock, e.g. Apple
  api.typesv2.common.Text stock_name = 7;

  // image indicating the price change in stock, like a zigzag arrow pointing up
  api.typesv2.common.Image stock_price_indicator_img = 8;

  // current market price of stock
  // clients can subscribe to live price updates during market hours via GetSymbolUpdates RPC
  api.typesv2.Money current_price = 9;

  // tags for company, like Technology, Mega Cap, etc.
  repeated api.typesv2.common.Text tags = 10;

  // time periods available for showing price charts, e.g. 1D, 6M, etc.
  repeated StockPricesForTimePeriod stock_prices_for_time_periods = 11;

  // investment summary for the user for this stock, only present when user has invested in the stock
  StockInvestmentSummary stock_investment_summary = 12;

  // have required details to render the watchlist button in the stock details screen
  // it is positioned next to the CTAs
  // if empty, watchlist icon is not shown
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1196%3A5594&t=SDwVfnE3gMbDHRA2-4
  StockWatchlistDetails stock_watchlist_details = 13;

  // if any stock is not available in that case StockHighlightContainer can be used to show the reason to users
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=13134-30614&t=y7GQ8Fj2sIQI3nhL-0
  StockHighlightContainer stock_highlight_container = 14;

  // eg. ETF, INDIVIDUAL_COMPANY
  StockType stock_type = 15;

  // Background color for header component.
  string bg_color = 16;

  // Related Stocks List
  // Figma: https://figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=1196-5224&mode=design&t=zKAYQATs83oQ3xWx-0
  RelatedStocks related_stocks = 17 [deprecated = true];
}

// StockHighlightContainer can be used to show any alert or highlight of a stock to users
message StockHighlightContainer {
  // eg: alert (⚠️) icon url
  string left_icon_url = 1;
  // eg: arrow (>) icon url
  string right_icon_url = 2;
  // eg: The stock is not available for purchase at the moment due to it’s high risk.
  api.typesv2.common.Text description = 3;
  string background_color = 4;
  deeplink.Deeplink deeplink = 5;
}

message StockPricesForTimePeriod {
  // e.g. 1D, 6M, etc.
  api.typesv2.common.Text period_text_for_tabs = 1;

  // same value as above with diff formatting
  api.typesv2.common.Text period_text_for_returns = 2;

  // change in stock price in percentage
  // Eg: +1.3% (it is gain then text color is green)
  // Eg: -1.3% (it is loss then text color is red)
  api.typesv2.common.Text price_change_in_period = 3 [deprecated = true];

  // start of time period for price, e.g. 09:31 AM 26 Jul 22
  api.typesv2.common.Text start_time = 4;

  // end of time period for price, e.g. 09:31 AM 26 Nov 22
  api.typesv2.common.Text end_time = 5;

  // historical stock price chart
  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3795%3A19129&t=KVVe7EsjitFElgwD-4
  StockPriceChart stock_price_chart = 6;

  // True if the stock prices, returns, etc. should be updated real-time
  // Stock prices on chart should be updated if this is set to true
  // Price change in period should be updated only if this is set to true
  bool should_update_real_time = 7;

  // change in stock price in percentage
  // Eg: +1.3% (it is gain then text color is green)
  // Eg: -1.3% (it is loss then text color is red)
  api.typesv2.ui.IconTextComponent percentage_returns = 8;

  // OHLC metrics for a Stock in given time duration
  OHLCComponent ohlc_component = 9;
}

// Represents OHLC metrics of an Stock for a time frame.
// https://drive.google.com/file/d/1uriVG5feP2ty8a9OzgMbHPu8U7k29lT_/view?usp=drive_link
// https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=14801-31967&mode=dev
message OHLCComponent {
  // Represents "Open" field in OHLC price.
  api.typesv2.ui.VerticalKeyValuePair open_price = 1;

  // Represents "Low" field in OHLC price.
  api.typesv2.ui.VerticalKeyValuePair low_price = 2;

  // Represents "High" field in OHLC price.
  api.typesv2.ui.VerticalKeyValuePair high_price = 3;

  // Represents "Closed" field in OHLC price.
  api.typesv2.ui.VerticalKeyValuePair closed_price = 4;
}

message StockPriceChart {
  // color of line(series) in chart
  string series_color = 1 [deprecated = true];

  // expected min value of x-data points
  google.protobuf.Timestamp min_x_val = 2 [deprecated = true];

  // expected max value of x-data points
  google.protobuf.Timestamp max_x_val = 3 [deprecated = true];

  // expected min value of y-data points
  double min_y_val = 4 [deprecated = true];

  // expected max value of y-data points
  double max_y_val = 5 [deprecated = true];

  // data points of a metric like debt-to-equity of company vs industry, etc. over a time period
  repeated TimeSeriesLineChartDataPoint stock_price_data_points = 6 [deprecated = true];

  // command to initialise the price chart
  // this can include attribute like background colour, chart line colour, start & end date etc.
  string init_chart_js_command = 7;

  // command to populate data in price chart
  // this will contain data points which web will use to draw price chart
  string set_chart_data_js_command = 8;
}

message TimeSeriesLineChartDataPoint {
  // x-axis value
  google.protobuf.Timestamp timestamp = 1;

  // y-axis value
  double value = 2;
}

message StockInvestmentSummary {
  // current market value of user's investments in the stock
  LabeledConvertibleAmount current_market_value = 1;

  // amount that user has invested in the stock
  LabeledConvertibleAmount invested_value = 2;

  // returns that user has earned from the stock
  LabeledConvertibleAmount returns = 3;

  // change in returns of the stock since last market open day
  api.typesv2.ui.IconTextComponent daily_change = 4;

  // avg. price at which the user has bought shares in the stock
  LabeledConvertibleAmount avg_price = 5;
}

// used for displaying an amount in USD and INR with a description of what the amount represents
// ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3795%3A19630&t=BAluUVYUfwHdDZKV-4
message LabeledConvertibleAmount {
  // e.g. CURRENT VALUE, INVESTED, etc.
  api.typesv2.common.Text label = 1;

  ConvertibleAmount convertible_amount = 2;

  // This can be additional info related to amount shown.
  // For example, when the current value of stocks might not be in sync with regular trading hours closing price, etc.
  deeplink.InformationPopupOptions info_popup_options = 3;
}

// used when amount can be shown in both USD and INR based on user interaction
message ConvertibleAmount {
  api.typesv2.common.Text amount_in_usd = 1;

  api.typesv2.common.Text amount_in_inr = 2;
}

message GetSymbolDecisionFactorsRequest {
  frontend.header.RequestHeader req = 1;
  // Represent internal identifier for stock
  string stock_id = 2;
}

message GetSymbolDecisionFactorsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // Represent Card to display the decision factor
  repeated SymbolDecisionFactorsCard symbol_decision_factors_cards = 2;
}

message GetSymbolsListRequest {
  frontend.header.RequestHeader req = 1;
  rpc.PageContextRequest page_context = 2;
}

message GetSymbolsListResponse {
  frontend.header.ResponseHeader resp_header = 1;
  rpc.PageContextResponse page_context = 2;
  repeated SymbolsListCard symbols_list_cards = 3;
}

message GetSymbolHistoricalPricesRequest {
  frontend.header.RequestHeader req = 1;

  // Represent internal identifier for stock
  string stock_id = 2;
}

message GetSymbolHistoricalPricesResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // image of stock (company) logo
  api.typesv2.common.Image stock_logo = 2;

  // name of stock, e.g. Apple
  api.typesv2.common.Text stock_name = 3;

  // current market price of stock
  // clients can subscribe to live price updates during market hours via GetSymbolUpdates RPC
  api.typesv2.Money current_price = 4;

  // tags for company, like Technology, Mega Cap, etc.
  repeated api.typesv2.common.Text tags = 5;

  // time periods available for showing price charts, e.g. 1D, 6M, etc.
  repeated StockPricesForTimePeriod stock_prices_for_time_periods = 6;

  // List of FAQs and their answers
  repeated FaqArticle faq_articles = 7;
}

// TODO(Brijesh): Check with web if HTML needs to be sent in below articles
message FaqArticle {
  // A frequently asked question
  api.typesv2.common.Text question = 1;

  // Answer to a frequently asked question
  api.typesv2.common.Text answer = 2;
}

message GetStockForSymbolRequest {
  frontend.header.RequestHeader req = 1;

  // Symbol/ticker under which a stock is publicly traded
  string symbol = 2;
}

message GetStockForSymbolResponse {
  frontend.header.ResponseHeader resp_header = 1;

  string stock_id = 2;
}

message GetAccountActivitiesRequest {
  frontend.header.RequestHeader req = 1;
  // should be nil while fetching next page.
  rpc.PageContextRequest page_context = 3;
  // filters to be applied while fetching account activity
  AccountActivityFilters filters = 4;
}

message GetAccountActivitiesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  AccountActivities activities = 2;
}

message GetWalletPageRequest {
  frontend.header.RequestHeader req = 1;
}

message GetWalletPageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  WalletPage wallet_page = 2;
}

message GetWalletAddFundsDetailsRequest {
  frontend.header.RequestHeader req = 1;
  frontend.deeplink.Screen entry_point = 2;
}

message GetWalletAddFundsDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  WalletAddFundsPage add_funds_page = 2;
  api.typesv2.ui.ScreenHeader screen_header = 3;
}

message GetWalletWithdrawFundsDetailsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetWalletWithdrawFundsDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  WalletWithdrawFundsPage withdraw_funds_page = 2;
}

message ValidateUserWalletFundInputAndGenerateInvoiceRequest {
  frontend.header.RequestHeader req = 1;
  WalletFundsFlowType fund_flow_type = 2;
  // exchange rate for converting USD to INR.
  api.typesv2.Money usd_to_inr_exchange_rate = 3;
  // currency input option chosen by the user.
  InputCurrencyType currency_type = 4;
  // amount entered by user.
  api.typesv2.Money amount_input = 5;
  // validation details sent by BE for the add/withdraw funds page.
  WalletAmountValidationDetails validation_details = 6;
  // whether to generate invoice or not.
  bool generate_invoice = 7;
}

message ValidateUserWalletFundInputAndGenerateInvoiceResponse {
  enum Status {
    OK = 0;
    // internal server error
    // expected handling: use the inline error view for user visibility. User is expected to try again in sometime.
    INTERNAL = 13;
  }
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.Money entered_amount_in_usd = 2;
  api.typesv2.Money entered_amount_in_inr = 3;
  // details to be displayed below the user amount input section.
  // this section is used to inform users about the approx value of the amount in
  // the other currency and the tentative exchange rate applicable for the txn.
  // ex: ₹4,00,200 . $1 = ₹80.64
  api.typesv2.ui.IconTextComponent currency_conversion_info_section = 4;
  bool amount_valid = 5;
  // validation failure details to be shown to the user
  // this will only be present if amount validation has failed.
  DisplayDetails validation_failure_display_details = 6;
  // invoice response will only be present if generate_invoice boolean flag is true in request.
  oneof invoice_response {
    // Represent text and value for invoice
    Invoice invoice_details = 7;
    // if forex rate not found for given amount then it returns error msg and maximum available amount that user can enter
    MaxAmountBreachedErrView forex_rate_err_view = 8;
  }
  message DisplayDetails {
    // error message to be shown just above the CTA.
    api.typesv2.ui.IconTextComponent inline_error_message = 1;
    // will be present if CTA needs to be changed
    // client needs to remove this CTA and show the original
    // one if user changes the amount and the new amount is valid
    deeplink.Cta cta = 2;
  }
  oneof cta {
    deeplink.Cta button_cta = 9;
    api.typesv2.ui.SwipeButton swipe_button_cta = 10;
  }
}

message CreateWalletAddFundsRequest {
  frontend.header.RequestHeader req = 1;
  // Represent internal identifier for stock

  // represent invoice breakdown shown to the user
  // while placing the order
  InvoiceDetails invoice_details = 2;

  // represent list of the user opted feature (i.e selected)
  // eg: if user is opted to instant purchase then enum for corresponding check box is send back
  repeated InvoiceEntryType invoice_entries_type = 4;

  // client order Id for ensuring idempotency of request,
  // so that multiple orders are not created for user intent to create a single order
  string client_order_id = 5;
  // represent flow which triggered add funds
  CreateWalletAddFundsEntryPoint entry_point = 6;
}

// represent the flow which trigger add fund flow
enum CreateWalletAddFundsEntryPoint {
  CREATE_WALLET_ADD_FUNDS_ENTRY_POINT_UNSPECIFIED = 0;
  CREATE_WALLET_ADD_FUNDS_ENTRY_POINT_DOUBLE_PIN_FLOW = 1;
  CREATE_WALLET_ADD_FUNDS_ENTRY_POINT_ADD_FUNDS_FLOW = 2;
}

// This message is a blatant copy of 'frontend.pay.transaction.CreateFundTransferOrderResponse'
// including the documentation
message CreateWalletAddFundsResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;
    // forex rate has changed, user should be displayed new forex rate and order should be created post confirmation
    FOREX_RATE_CHANGED = 102;
    // order creation failed due to max allowed txn limit for the day already consumed
    BREACHED_MAX_ALLOWED_FUND_ADDITION_LIMIT_FOR_DAY = 103;
    // order creation failed due to max allowed txn limit for the financial year already consumed
    BREACHED_MAX_ALLOWED_FUND_ADDITION_LIMIT_FOR_FINANCIAL_YEAR = 104;
    // order creation failed as user is not a vintage Fi user
    // vintage is config driven. eg: 6 Months
    INSUFFICIENT_SAVINGS_ACCOUNT_VINTAGE = 105;
    // order creation failed as user have not performed min number of required transactions through Fi
    INSUFFICIENT_NO_OF_TRANSACTIONS = 106;
    // order creation failed as user has already consumed defined LRS limit for the financial year
    BREACHED_LRS_LIMIT = 107;
    // order creation failed as partner bank does not allow foreign remittance for the actor
    // reason could be - not a full KYC user, NRI user etc
    FOREIGN_REMITTANCE_NOT_ALLOWED = 108;
    // user blacklisted for foreign fund transfer with Fi
    USER_BLACKLISTED = 109;
    // if forex rate deal amount is not present for manual purchase during market open time
    FOREX_RATE_NOT_FOUND_IN_MKT_HRS = 110;
    // order amount is suspected only if all the following conditions are met:
    // 1. International Transaction amount is >2 L
    // 2. International Transaction amount/ Account Balance > 80%
    // 3. International Transaction Amount/ Max(Last 5 Credit transaction Amount) > 90%
    // https://docs.google.com/document/d/1OYrGhaNFnJDY8CcDq6_XgtmRAXFoLeOBzGW22PQ4nWM/edit#bookmark=id.kqm7896u5dv
    ORDER_AMOUNT_SUSPECTED = 111;
    // kyc check with banking partners failed for user
    KYC_CHECK_FAILED_WITH_BANKING_PARTNER = 112;
    // pan validation check with banking partner failed for user
    PAN_CHECK_FAILED_WITH_BANKING_PARTNER = 113;
    // user is personal loan holder
    PERSONAL_LOAN_HOLDER_USER = 115;
    // order creation failed due to max allowed txn limit on the basis of sof doc year already consumed for the financial
    BREACHED_SOF_BASED_REMITTANCE_LIMIT_FOR_FINANCIAL_YEAR = 116;
  }
  frontend.header.ResponseHeader resp_header = 1;

  // If pin based authorization is required to execute payment for a given order.
  frontend.pay.transaction.PinRequiredType pin_required_type = 2;

  // Transaction attributes are required for the client to:
  //  i. Generate the credentials using a common library i.e., NPCI CL or Partner bank's CL
  //  ii. Present the transaction information e.g., payment protocol to the user
  frontend.pay.transaction.TransactionAttribute transaction_attribute = 3;

  // Used for calling 'AuthoriseFundTransfer' rpc
  string payment_client_request_id = 4;

  // there can be cases when forex rate which was shown to user is no longer available
  // new forex rate should be shown to user and order should be created post confirmation on the new fx rate
  // newFxRateConfirmationDialog holds display fields to be shown to user in such case
  deeplink.Deeplink newFxRateConfirmationDialog = 5;
}

message CreateWalletWithdrawFundsOrderRequest {
  frontend.header.RequestHeader req = 1;
  // Deprecated: use invoice details amount_in_usd field instead
  api.typesv2.Money amount = 2 [deprecated = true];
  // if the user selects withdraw all then it's true
  bool should_withdraw_all = 3;
  // represent invoice breakdown shown to the user
  // while placing the order
  InvoiceDetails invoice_details = 4;
  // client order Id for ensuring idempotency of request,
  // so that multiple orders are not created for user intent to create a single order
  string client_order_id = 5;
}

message CreateWalletWithdrawFundsOrderResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message GetDropOffBottomSheetRequest {
  frontend.header.RequestHeader req = 1;
  string id = 2;
}

message GetDropOffBottomSheetResponse {
  frontend.header.ResponseHeader resp_header = 1;
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }

  BottomSheet bottom_sheet = 2;

  // BottomSheet defines one of supported bottom sheets
  // All investment/ui bottom sheet types may not be supported in us stocks hence this wrapper type is defined within rpc
  message BottomSheet {
    oneof sheet_params {
      frontend.investment.ui.ActionableBottomSheet actionable_bottom_sheet = 1;
      frontend.investment.ui.InformationalBottomSheet informational_bottom_sheet = 2;
      deeplink.Deeplink deeplink = 3;
    }
  }
}

message RelatedStocks {
  // Use GetSimilarStocks RPC instead.
  option deprecated = true;

  api.typesv2.common.Text title = 1;
  repeated api.typesv2.ui.IconTextComponent stocks = 2;
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 3;
}

message GetSimilarStocksRequest {
  frontend.header.RequestHeader req = 1;

  string stock_id = 2 [(validate.rules).string.min_len = 1];

  // The list of similar stocks to show can depend on the journey through which a stock is explored.
  // For example, if a stock details page is visited from a collection of stocks,
  // the rest of the stocks in that collection would act as similar stocks.
  oneof journey_context {
    string collection_id = 3;
  }
}

message GetSimilarStocksResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // Text like "More from collection A" or "Similar stocks"
  api.typesv2.common.Text title = 2;

  // A list of stocks similar to the requested stock.
  // Note: The first stock in this list is always going to be the one requested,
  // and the stocks are returned in descending order of their similarity.
  repeated SimilarStock similar_stocks = 3;
}

message SimilarStock {
  // internal identifier of stock
  string id = 1;

  // stock logo (company/ETF logo)
  api.typesv2.common.VisualElement logo = 6;

  // name of stock, e.g., Apple Inc.
  api.typesv2.common.Text name = 7;
}

message GetStockTradeDetailsForExchangePageRequest {
  frontend.header.RequestHeader req = 1;
  // Represent internal identifier for stock
  string stock_id = 2;
  // represent order side for stock buy/sell
  OrderSide order_side = 3;
}

message GetStockTradeDetailsForExchangePageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // details to show on buy/sell screen for a stock
  // buy or sell depends on the order type in request
  TradeDetails trade_details = 2;
}

message InitiateTradeOrderRequest {
  frontend.header.RequestHeader req = 1;
  // client order id for ensuring idempotency of request,
  // so that multiple orders are not created for user intent to create a single order
  string client_order_id = 2;
  // represent order side for stock buy/sell
  OrderSide order_side = 3;
  // represent internal identifier for stock
  string stock_id = 4;

  oneof order_details {
    // Used when the user enters the amount to buy/sell
    TradeByAmountOrderDetails amount_order_details = 5;
    // Used when the user enters the quantity to buy/sell
    TradeByQuantityOrderDetails quantity_order_details = 6;
  }
}

message TradeByAmountOrderDetails {
  // should_sell_all should be true if user selects sell all on trade details page
  bool should_sell_all = 1;
  api.typesv2.Money amount_entered_by_user = 2;
  // calculated_trade_amount = amount_entered_by_user - charges(charges include brokerage etc)
  // value for calculated_trade_amount is obtained by evaluating an Expression sent in TradeDetails.trade_order_amount_expression field
  // Client is expected to receive the expression in response of GetStockTradeDetailsForExchangePage RPC
  api.typesv2.Money calculated_trade_amount = 3;
}

message TradeByQuantityOrderDetails {
  // should_sell_all should be 'true' if user selects sell all on trade details page
  bool should_sell_all = 1;
  // quantity of stock requested by user to buy/sell
  double quantity_entered_by_user = 2;
  // LimitOrderDetails will have data only if users selects limit order
  LimitOrderDetails limit_order_details = 3;
}

message LimitOrderDetails {
  // limit price at which user wants to buy/sell stock
  api.typesv2.Money limit_price = 1;
  // validity of limit order selected by user (day/gtc)
  string selected_validity_option_id = 2;
}

message InitiateTradeOrderResponse {
  enum Status {
    OK = 0;

    // internal server error
    INTERNAL = 13;

    // day trade limit reached, failed to create buy order
    DAY_TRADE_LIMIT_BREACHED = 101;

    // user doesn't have enough buying power
    // example scenario: user has placed an order to withdraw all funds from their wallet, but then tries to buy stocks.
    INSUFFICIENT_BUYING_POWER = 102;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink to next screen
  // should be order status screen
  deeplink.Deeplink next_screen = 2;
}

message GetSIPProjectionRequest {
  frontend.header.RequestHeader req = 1;

  // An identifier for an stock option selected for SIP projection calculation
  string stock_id = 2;

  // An identifier for an amount option selected for SIP projection calculation
  string amount_option_id = 3;

  // An identifier for a duration option selected for SIP projection calculation
  string duration_id = 4;
}

message GetSIPProjectionResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // Projection details for the SIP
  SIPProjection sip_projection = 2;
}
