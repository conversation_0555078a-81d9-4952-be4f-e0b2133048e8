syntax = "proto3";

package frontend.usstocks;

option go_package = "github.com/epifi/gamma/api/frontend/usstocks";
option java_package = "com.github.epifi.gamma.api.frontend.usstocks";

enum InputCurrencyType {
  INPUT_CURRENCY_TYPE_UNSPECIFIED = 0;
  INPUT_CURRENCY_TYPE_USD = 1;
  INPUT_CURRENCY_TYPE_INR = 2;
}

enum WalletFundsFlowType {
  WALLET_FUNDS_FLOW_TYPE_UNSPECIFIED = 0;
  // add funds to wallet
  WALLET_FUNDS_FLOW_TYPE_ADD_FUNDS = 1;
  // withdraw funds from wallet
  WALLET_FUNDS_FLOW_TYPE_WITHDRAW_FUNDS = 2;
}

// field identifier for https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=23390-38440&mode=design&t=KaDhfBgGfcQPCkIM-4
enum EmploymentFieldIdentifier {
  EMPLOYER_FIELD_IDENTIFIER_UNSPECIFIED = 0;
  EMPLOYER_FIELD_IDENTIFIER_EMPLOYER_NAME = 1;
  EMPLOYER_FIELD_IDENTIFIER_ROLE = 2;
  EMPLOYER_FIELD_IDENTIFIER_EMPLOYERS_ADDRESS = 3;
}

// ExpressionVariable is the replaceable variable in the expression
// eg: for expression Min(0.0025 * amount_entered, 100), amount_entered is the variable
enum ExpressionVariable {
  EXPRESSION_VARIABLE_UNSPECIFIED = 0;
  EXPRESSION_VARIABLE_AMOUNT_ENTERED = 1;
  EXPRESSION_VARIABLE_CURRENT_STOCK_PRICE = 2;
}

// Operations that can be used while defining Expression
enum Operator {
  OPERATOR_UNSPECIFIED = 0;
  OPERATOR_PLUS = 1;
  OPERATOR_MINUS = 2;
  OPERATOR_MULTIPLY = 3;
  OPERATOR_MIN = 4;
  OPERATOR_MAX = 5;
  OPERATOR_DIVIDE = 6;
}

// OrderSide represents the type of trading order (buy or sell).
// This enum will be used to identify the type of order throughout the trading flow.
enum OrderSide {
  ORDER_SIDE_UNSPECIFIED = 0;
  ORDER_SIDE_BUY = 1;
  ORDER_SIDE_SELL = 2;
}


enum LandingScreenEntryPoint {
  LANDING_SCREEN_ENTRY_POINT_UNSPECIFIED = 0;
  LANDING_SCREEN_ENTRY_POINT_STOCKS = 1;
  LANDING_SCREEN_ENTRY_POINT_FUNDS = 2; // Funds map to ETF as of now
}
