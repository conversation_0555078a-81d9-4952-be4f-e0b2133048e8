syntax = "proto3";

package frontend.goals;

import "google/protobuf/timestamp.proto";
import "api/typesv2/money.proto";
import "api/frontend/deeplink/deeplink.proto";

option go_package = "github.com/epifi/gamma/api/frontend/goals";
option java_package = "com.github.epifi.gamma.api.frontend.goals";

message GoalDiscoveryParams {
  oneof params {
    SmartDepositGoalDiscoveryParams sd_params = 1;
    ExistingSmartDepositGoalDiscoveryParams existing_sd_params = 2;
  }
}

// params required to show the goal discovery
message SmartDepositGoalDiscoveryParams {
  // creation amount
  api.typesv2.Money amount = 1;
  // deposit close date
  google.protobuf.Timestamp close_date = 2;
  // include deposit template id if its a template goal
  string deposit_template_id = 3;
}

// params required to show the goal discovery in existing SD
// can be used for both creating new goal or updating existing goal
message ExistingSmartDepositGoalDiscoveryParams {
  string deposit_account_id = 1;
}

// have all the information needed to display goal discovery
message GoalDiscovery {
  GoalDiscoveryType default_discovery_type = 1;
  GoalNudgeCard nudge_card = 2;
  GoalAmountSliderCard amount_slider_card = 3;
}

// tells how to display the goal discovery
enum GoalDiscoveryType {
  GOAL_DISCOVERY_TYPE_UNSPECIFIED = 0;
  // goal amount slider expanded by default
  // expanded card can have the user input parameters(like target amount, target date) visible.
  GOAL_DISCOVERY_TYPE_EXPANDED = 1;
  // goal amount slider collapsed by default
  GOAL_DISCOVERY_TYPE_COLLAPSED = 2;
  // goal creation is mandatory in the flow
  // no cross option to collapse
  GOAL_DISCOVERY_TYPE_NON_COLLAPSIBLE = 3;
  // hides goal discovery
  GOAL_DISCOVERY_TYPE_HIDDEN = 4;
}

// card with information which nudge users to add goal to the investment instrument
message GoalNudgeCard {
  string icon_url = 1;
  // Set a goal amount
  string title = 2;
  // Add now & track your progress
  string description = 3;
  // add/upgrade
  string cta_text = 4;
  CtaType cta_type = 5;
  CtaAction cta_action = 6;
  frontend.deeplink.Deeplink vkyc_deeplink = 7;

  // plus shows "+" to the left of the cta_text
  // action shows ">" to the right of the cta_text
  enum CtaType {
    CTA_TYPE_UNSPECIFIED = 0;
    CTA_TYPE_PLUS = 1;
    CTA_TYPE_ACTION = 2;
  }

  enum CtaAction {
    CTA_ACTION_UNSPECIFIED = 0;
    // on tap, shows goal amount slider
    CTA_ACTION_ADD_GOAL = 1;
    // on tap, directs user to complete video kyc
    CTA_ACTION_COMPLETE_VKYC = 2;
  }
}

// card where we can set the goal amount
message GoalAmountSliderCard {
  string title = 1;
  string icon_url = 2;
  api.typesv2.Money min_amount = 3;
  api.typesv2.Money max_amount = 4;
  api.typesv2.Money default_amount = 5;
  string info_text = 6;
  string info_text_color = 7; // TODO(mounish): use color enum
}

// have all the information needed to display goal discovery in existing investment instrument
message GoalDiscoveryInInvestmentInstrument {
  GoalNudgeCard nudge_card = 1;
  AddGoalBottomSheet add_goal_bottom_sheet = 2;
}

message AddGoalBottomSheet {
  string title = 1;
  string description = 2;
  GoalAmountSliderCard amount_slider_card = 3;
  deeplink.Cta cancel_cta = 4;
  deeplink.Cta proceed_cta = 5;
}

message UpdateGoalBottomSheet {
  string title = 1;
  GoalAmountSliderCard amount_slider_card = 2;
  deeplink.Cta remove_goal_cta = 3;
  deeplink.Cta cancel_cta = 4;
  deeplink.Cta proceed_cta = 5;
}

message RemoveGoalBottomSheet {
  string title = 1;
  string description = 2;
  string icon_url = 3;
  deeplink.Cta cancel_cta = 4;
  deeplink.Cta proceed_cta = 5;
}
