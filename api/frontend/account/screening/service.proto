syntax = "proto3";

package frontend.account.screening;

import "api/frontend/account/screening/ui_states/enum.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/currency_code.proto";
import "api/typesv2/polling_request_info.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/account/screening";
option java_package = "com.github.epifi.gamma.api.frontend.account.screening";

/*
Proto and API definitions for In-App screening frontend service.
Users coming on to Fi app might need to undergo screening to establish if the user is a "good" user. A good user is
one who is expected to be able regularly add more funds.
Based on various checks and user consent, a user might be subjected to one or more of below checks:
1. credit report based health checks.
2. employment verification based on PF data to establish if the user is salaried.
*/
service Screening {

  // CheckCreditReportAvailabilityStatus checks the status for credit report availability. The call to invoke the
  // availability check is invoked in backend right after TNC consent is received.
  //
  // If the report availability check is still in progress, the next action returned is CREDIT_REPORT_AVAILABILITY_STATUS
  //
  // If the report availability check was successful, there can be 2 cases:
  //  1. if the report was found: the next action returned is CREDIT_REPORT_CONSENT
  //  2. if the report was not found: EMPLOYMENT_DECLARATION. This serves as a fallback to the employment verification flow.
  rpc CheckCreditReportAvailabilityStatus (CheckCreditReportAvailabilityStatusRequest) returns (CheckCreditReportAvailabilityStatusResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetConsentAndVerifyCreditReport collects the consent from the user and initiates credit report verification.
  // If consent flag is true,
  //  1. invokes record consent api
  //  2. initiates credit report verification
  //  3. returns next action as CREDIT_REPORT_VERIFICATION_STATUS
  //
  // If consent flag is false,
  //  1. store ‘non-consent’ with credit report service
  //  2. returns EMPLOYMENT_DECLARATION as next action
  rpc GetConsentAndVerifyCreditReport (GetConsentAndVerifyCreditReportRequest) returns (GetConsentAndVerifyCreditReportResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // CheckCreditReportVerificationStatus checks the status of credit report verification to determine if a user is good to
  // use the Fi-app.
  //
  // If the verification is in progress, client polls the same api using CREDIT_REPORT_VERIFICATION_STATUS
  //
  // If the verification was completed, there can be 2 cases:
  // 1. good user: proceed to CKYC screens with pan and dob pre-filled. Next action: REGISTER_CKYC (TODO(anand): confirm with Aditya
  // 2. not a good user: proceed to employment check or reject the user as per internal checks.
  //    Expected deeplinks: EMPLOYMENT_DECLARATION and APP_SCREENING_REJECT respectively.
  rpc CheckCreditReportVerificationStatus (CheckCreditReportVerificationStatusRequest) returns (CheckCreditReportVerificationStatusResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetCompanyNames returns the company name matching the entered prefix.
  // It serves as an auto complete api.
  // TODO(anand): whats the fallback if company name is not found
  rpc GetCompanyNames (GetCompanyNamesRequest) returns (GetCompanyNamesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // ProcessEmploymentData receives the employment declaration data and initiates the screening process.
  // This is an async rpc. The ACK response is returned immediately with next action to poll the employment
  // verification status i.e. EMPLOYMENT_VERIFICATION_STATUS as deeplink.
  //
  // To poll the status, UI is expected to invoke `CheckEmploymentVerificationStatus` rpc
  rpc ProcessEmploymentData (ProcessEmploymentDataRequest) returns (ProcessEmploymentDataResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // Update employment data rpc call
  rpc UpdateEmploymentData (UpdateEmploymentDataRequest) returns (UpdateEmploymentDataResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // CheckEmploymentVerificationStatus checks the status of employment verification process.
  // If process is in progress, next action returned will be EMPLOYMENT_VERIFICATION_STATUS.
  // Once process is complete, a different next action is returned.
  //`
  // Following scenarios are possible:
  // 1. employment type was non-salaried: based on internal checks, user can land into one of 3 screens using next action
  //    a. APP_SCREENING_MANDATE_CREDIT_REPORT_CONSENT
  //    b. APP_SCREENING_MANUAL_INTERVENTION
  //    c. APP_SCREENING_REJECT (TODO(anand): confirm with Piyush)
  //
  // 2. employment type was salaried: based on internal checks, user can land into one of 4 screens using next action
  //    a. REGISTER_CKYC
  //    b. APP_SCREENING_MANDATE_CREDIT_REPORT_CONSENT
  //    c. APP_SCREENING_MANUAL_INTERVENTION
  //    d. APP_SCREENING_REJECT
  rpc CheckEmploymentVerificationStatus (CheckEmploymentVerificationStatusRequest) returns (CheckEmploymentVerificationStatusResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // CheckGmailVerificationStatus polls status of gmail verification.
  // Gmail verification is initiated when the user is in web view, and will be in-progress in background
  //
  // Gmail verification can reach one of the following terminal states,
  // 1. In-progress
  // 2. Success
  // 3. Internal error state
  // 4. Invalid - Gmail verification was not initiated for the actor
  // In each of the above states, the corresponding deeplink will be returned in response
  rpc CheckGmailVerificationStatus (CheckGmailVerificationStatusRequest) returns (CheckGmailVerificationStatusResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // SendWorkEmailOTP validates user entered email
  // If successful, an OTP is sent to the email
  rpc SendWorkEmailOTP (SendWorkEmailOTPRequest) returns (SendWorkEmailOTPResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // VerifyWorkEmailOTP verifies the OTP entered by user
  // On successful verification user is expected to land on ckyc
  rpc VerifyWorkEmailOTP (VerifyWorkEmailOTPRequest) returns (VerifyWorkEmailOTPResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetCompanyNamesV2 returns the company name matching the entered prefix.
  // It serves as an auto complete api. This api returns results based on master employer db.
  rpc GetCompanyNamesV2 (GetCompanyNamesRequestV2) returns (GetCompanyNamesResponseV2) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetScreenerChoiceScreenOptions will be called from Screen_SCREENER_CHOICE to render its screen options.
  // This RPC will skip the screener stages before APP_SCREENING and get the next action that needs to be performed.
  // 1. If the next action is not SCREENER_CHOICE screen, then client will move the user to the corresponding deeplink.
  // 2. If the next action is SCREENER_CHOICE, then the screen options will be used to show the screen as desired.
  rpc GetScreenerChoiceScreenOptions (GetScreenerChoiceScreenOptionsRequest) returns (GetScreenerChoiceScreenOptionsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message SendWorkEmailOTPRequest {
  frontend.header.RequestHeader req = 15;

  // mandatory
  // email that is to be verified
  string email = 2 [(validate.rules).string.min_len = 1];

  // Unique identifier of SendWorkEmailOTPRequest request
  // If token is not sent, a new OTP is generated and emailed to user
  // If token is sent, existing OTP is emailed to user again
  string token = 3 [(validate.rules).string.pattern =
    "(^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[8|9|aA|bB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$|^$)"];
  // client_req_id is the unique identifier of a work email verification process
  string client_req_id = 4;
  // client is the string form of employment.VerificationProcessClient enum
  string client = 5;
}

message SendWorkEmailOTPResponse {
  frontend.header.ResponseHeader resp_header = 15;

  deeplink.Deeplink next_action = 1;
  // Unique identifier of GenerateOtp request
  string token = 2;
  // A timer(in seconds) after which client can send new request
  uint32 resend_after = 3;
  // otp_expiry(in seconds) denotes expiry time for the OTP
  // A timer for the client post which it should raise a NEW request for Otp with no token i.e. token = ""
  // Any RESEND attempt prior to the expiry should ideally be sent along with token received in first call
  uint32 otp_expiry = 4;
}

message VerifyWorkEmailOTPRequest {
  frontend.header.RequestHeader req = 15;

  string email = 1;
  // Unique identifier of OTP request
  string token = 2 [(validate.rules).string.uuid = true];
  // 6-digit OTP that is sent to the email
  string otp = 3 [(validate.rules).string = {len: 6, pattern: "^[0-9]+$"}];
  // client_req_id is the unique identifier of a work email verification process
  string client_req_id = 5;
  // client is the string form of employment.VerificationProcessClient enum
  string client = 6;
}

message VerifyWorkEmailOTPResponse {
  frontend.header.ResponseHeader resp_header = 15;

  deeplink.Deeplink next_action = 1;
  // text to be shown while transitioning
  string transition_title_text = 2;
  // subtitle text to be shown while transitioning
  string transition_subtitle_text = 3;
  // url for display icon
  string icon_url = 4;

}

message CheckCreditReportAvailabilityStatusRequest {
  frontend.header.RequestHeader req = 15;
}

message CheckCreditReportAvailabilityStatusResponse {
  frontend.header.ResponseHeader resp_header = 15;

  deeplink.Deeplink next_action = 1;
  // amount of time(in seconds) the UI is expected to wait for, before polling the api again.
  int32 next_poll_after = 2;
}

message GetConsentAndVerifyCreditReportRequest {
  frontend.header.RequestHeader req = 15;

  bool consent = 1;
}

message GetConsentAndVerifyCreditReportResponse {
  frontend.header.ResponseHeader resp_header = 15;

  deeplink.Deeplink next_action = 1;
}

message CheckCreditReportVerificationStatusRequest {
  frontend.header.RequestHeader req = 15;
  api.typesv2.PollingRequestInfo polling_request_info = 1;
}

message CheckCreditReportVerificationStatusResponse {
  frontend.header.ResponseHeader resp_header = 15;

  deeplink.Deeplink next_action = 1;
  // amount of time(in seconds) the UI is expected to wait for, before polling the api again.
  // Deprecated: retry using retry timer from polling response info
  int32 next_poll_after = 2 [deprecated = true];
  // CreditReportVerificationStatus denotes status credit report verification
  CreditReportVerificationStatus credit_report_verification_status = 3;
  // text to be shown while in progress or transitioning
  string transition_title_text = 4;
  // subtitle text to be shown while in progress or transitioning
  string transition_subtitle_text = 5;
  // url for display icon
  string icon_url = 6;
  api.typesv2.PollingResponseInfo polling_response_info = 7;
}

message GetCompanyNamesRequest {
  frontend.header.RequestHeader req = 15;

  string name_prefix = 1;
}

message GetCompanyNamesRequestV2 {
  frontend.header.RequestHeader req = 1;

  string name_prefix = 2 [(validate.rules).string.min_len = 3];
}

message CompanyInfo {
  string name = 1;
  string vendor_id = 2;
  bool is_epf_registered = 3;
  // text entered by the user in the company name input
  string entered_text = 4;
}

message CompanyInfoV2 {
  // unique internal id of the Employer. can be empty if not a verified employer
  string id = 1;
  // legal name, or name by source
  string legal_name = 2;
  // trade name, can be empty
  string trade_name = 3;
  // whether employer is verified internally or not
  bool is_verified_employer = 4;
}

message UserSelectedEmployerInfo {
  // unique internal id of the Employer. can be empty if not a verified employer
  string id = 1;
  // text entered by the user in the company name input
  string entered_text = 2;
}

message GetCompanyNamesResponseV2 {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  frontend.header.ResponseHeader resp_header = 15;
  // companies matching user given input
  repeated CompanyInfoV2 companies = 1;
}

message GetCompanyNamesResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  frontend.header.ResponseHeader resp_header = 15;

  repeated CompanyInfo companies = 1;
}

message ProcessEmploymentDataRequest {
  frontend.header.RequestHeader req = 15;
  // client_req_id is the unique identifier of a process entry for a client
  string client_req_id = 16;

  EmploymentType employment_type = 1;
  EmploymentProofType employment_proof_type = 2;
  oneof input {
    // company info v2 update
    UserSelectedEmployerInfo employer_info = 12;
    // expected when employment_proof_type is `COMPANY_NAME``
    CompanyInfo company_info = 3;
    // expected when employment_proof_type is `PERSONAL_WEBSITE`
    PersonalProfileInfo personal_info = 5;
    // details of business owner includes GSTIN number for now.
    // Expected when employment_proof_type is `BUSINESS_OWNER_DETAILS`
    BusinessOwnerInfo business_owner_info = 7;
    // Expected when employment_proof_type is `ENROLLMENT_NUMBER`
    EnrollmentNumber enrollment_number = 8;
    // Expected when employment_proof_type is `STUDENT_DETAILS`
    StudentDetails student_details = 9;
  }
  AnnualSalaryRange annual_salary_range = 6 [deprecated = true]; // consume annual_salary instead
  AnnualSalary annual_salary = 10;
  // valid only for cases where income discrepancy exist
  bool income_discrepancy_consent_given = 11;
  // update_source informs about trigger for RPC.
  // it maps to UpdateSource enum in employment service
  // sample values -> UPDATE_SOURCE_ONBOARDING
  string update_source = 13;
  // maps to employment.OccupationType
  string occupation_type = 14;
  // maps to api.typesv2.Qualification
  string qualification = 17;
  // maps to api.typesv2.SourceOfFunds
  string source_of_funds = 18;
  // value selected by the user for EmploymentDeclarationOptions
  AnnualSalaryRange annual_transaction_volume = 19;
}

message ProcessEmploymentDataResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
  }
  frontend.header.ResponseHeader resp_header = 15;

  deeplink.Deeplink next_action = 1;
}

message CheckEmploymentVerificationStatusRequest {
  frontend.header.RequestHeader req = 15;
}

message CheckEmploymentVerificationStatusResponse {
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
    INTERNAL = 13;
  }
  frontend.header.ResponseHeader resp_header = 15;

  deeplink.Deeplink next_action = 1;
  // amount of time(in seconds) the UI is expected to wait for, before polling the api again.
  int32 next_poll_after = 2;
  // text to be shown while in progress or transitioning
  string transition_title_text = 3;
  // subtitle text to be shown while in progress or transitioning
  string transition_subtitle_text = 4;
  // url for display icon
  string icon_url = 5;
}

// It could be linkedIn profile, any personal website, github profile etc
message PersonalProfileInfo {
  string url = 1;
}

// CreditReportVerificationStatus denotes status credit report verification
enum CreditReportVerificationStatus {
  CREDIT_REPORT_VERIFICATION_STATUS_UNSPECIFIED = 0;
  // non terminal state denoting verification is still in progress
  CREDIT_REPORT_VERIFICATION_STATUS_IN_PROGRESS = 1;
  // terminal state denoting verification was successful
  CREDIT_REPORT_VERIFICATION_STATUS_SUCCESSFUL = 2;
  // terminal state denoting verification failed
  CREDIT_REPORT_VERIFICATION_STATUS_FAILED = 3;
}

message CheckGmailVerificationStatusRequest {
  frontend.header.RequestHeader req = 1;
}

message CheckGmailVerificationStatusResponse {
  // deeplink to take the user to in cases of success failure etc
  deeplink.Deeplink next_action = 1;
  // amount of time(in seconds) the UI is expected to wait for, before polling the api again.
  int32 next_poll_after = 2;
  // text to be shown while in progress or transitioning
  string transition_title_text = 3;
  // subtitle text to be shown while in progress or transitioning
  string transition_subtitle_text = 4;
  // response header
  frontend.header.ResponseHeader resp_header = 5;
}

message AnnualSalaryRange {
  int32 min_value = 1;
  int32 max_value = 2;
  // if currency_code is not mentioned (unspecified), it should be considered as INR
  // Supported: INR, AED
  // Note: in case more enums are added to currency code, a client release is required to support this.
  api.typesv2.CurrencyCode currency_code = 3;
  // alternate annual salary value that was displayed to the user in some other currency at the time of data declaration.
  message AlternateDisplayed {
    // min value of salary range
    int32 min_val = 1;
    // max value of salary range
    int32 max_val = 2;
    // currency_code of the annual salary
    // Supported: INR, AED.
    // Note: empty currency code can not be passed here.
    // Note 2: in case more enums are added to currency code, a client release is required to support this.
    api.typesv2.CurrencyCode currency_code = 3;
  }
  AlternateDisplayed alternate_displayed = 4;
}

message BusinessOwnerInfo {
  string gstin_no = 1;
}

message EnrollmentNumber {
  string enrollment_no = 1;
}

message StudentDetails {
  int32 year = 1;
  // student mail id
  string mail_id = 2;
}

message UpdateEmploymentDataRequest {
  frontend.header.RequestHeader req = 1;

  EmploymentType employment_type = 2;
  EmploymentProofType employment_proof_type = 3;
  oneof input {
    // expected when employment_proof_type is `COMPANY_NAME`
    CompanyInfo company_info = 4;
    // company info v2 update
    UserSelectedEmployerInfo employer_info = 11;
    // expected when employment_proof_type is `PERSONAL_WEBSITE`
    PersonalProfileInfo personal_info = 5;
    // details of business owner includes GSTIN number for now.
    // Expected when employment_proof_type is `BUSINESS_OWNER_DETAILS`
    BusinessOwnerInfo business_owner_info = 6;
    // Expected when employment_proof_type is `ENROLLMENT_NUMBER`
    EnrollmentNumber enrollment_number = 7;
    // Expected when employment_proof_type is `STUDENT_DETAILS`
    StudentDetails student_details = 8;
  }
  AnnualSalaryRange annual_salary_range = 9;
  AnnualSalary annual_salary = 10;
  enum Provenance {
    PROVENANCE_UNSPECIFIED = 0;
    // When client makes a call from profile, this enum needs to be set
    PROVENANCE_PROFILE = 1;
    // When client makes a call from vkyc, this enum needs to be set
    PROVENANCE_VKYC = 2;
    // when client makes a call from income occupation popup, this enum needs to be set
    PROVENANCE_INC_OCC_DISCREPANCY_POPUP = 3;
    // When client makes a call during periodic kyc flow, this enum needs to be set
    PROVENANCE_PERIODIC_KYC = 4;
  }
  Provenance provenance = 12;
  // maps to employment.OccupationType
  string occupation_type = 13;
}

message AnnualSalary {
  frontend.account.screening.AnnualSalaryRange range = 1;
  // absolute salary
  float absolute = 2 [deprecated = true]; // currency is always INR in case of absolute annual salary.
}

message UpdateEmploymentDataResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
  }
  frontend.header.ResponseHeader resp_header = 1;

  deeplink.Deeplink next_action = 2;
}

message GetScreenerChoiceScreenOptionsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetScreenerChoiceScreenOptionsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}
