syntax = "proto3";

package usstocks.order;

import "api/celestial/workflow/stage.proto";
import "api/celestial/workflow/stage/status.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/pay/internationalfundtransfer/forex_rates.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/usstocks/catalog/usstock.proto";
import "api/usstocks/enums.proto";
import "api/usstocks/order/account_activity.proto";
import "api/usstocks/order/account_activity_sync_cursor.proto";
import "api/usstocks/order/aggregated_remittance_transaction.proto";
import "api/usstocks/order/order.proto";
import "api/usstocks/order/sip.proto";
import "api/usstocks/order/wallet_order.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/order";
option java_package = "com.github.epifi.gamma.api.usstocks.order";


/*
  OrderManager takes care of on performing stocks buy/sell orders received directly from user or through an
  automated system like FITTT
  OrderManager uses Celestial for order processing
  All the order related APIs should be part of OrderManager
  eg: create a new buy/sell order, track the progress of an existing etc
 */
service OrderManager {
  // CreateOrderAndInitiateProcessing creates a database entry for requested order and Initiates corresponding workflow
  // Orders can be created directly by user or through an automation service eg: FIT
  // Initiated Workflow expects signals from corresponding ForeignFundTransfer workflow.
  // ForeignFundTransfer is a workflow at Pay, responsible for transferring payment from user's Savings account to Broker's account
  // ForeignFundTransfer workflow sends signals to US Stocks workflow on successful progress of stages
  rpc CreateOrderAndInitiateProcessing (CreateOrderAndInitiateProcessingRequest) returns (CreateOrderAndInitiateProcessingResponse) {}
  // GetOrder returns an order for requested id
  // type of id supported are order_id, vendor_order_id, client_request_id
  rpc GetOrder (GetOrderRequest) returns (GetOrderResponse) {};
  // returns list orders for a particular actor
  // RPC provides optional filters which can be applied to orders based on specific requirements
  rpc GetOrders (GetOrdersRequest) returns (GetOrdersResponse) {};
  // GetOrderProcessingDetails expects order_id and returns
  // 1. Corresponding order
  // 2. List of stages with their corresponding current status
  // RPC uses workflow history to get list of stages and their corresponding status
  rpc GetOrderProcessingDetails (GetOrderProcessingDetailsRequest) returns (GetOrderProcessingDetailsResponse) {};
  // RPC updates requested order
  // Only fields present in field_masks list will be updated
  rpc UpdateOrder (UpdateOrderRequest) returns (UpdateOrderResponse) {};

  // RPC is used to fetch whether exchange is open for trade or not
  rpc ExchangeStatus (ExchangeStatusRequest) returns (ExchangeStatusResponse) {};

  // Rpc is used to fetch forex rate usd to inr
  // Deprecated: Use IFT's GetForexRate instead (pay/internationalfundtransfer/service.proto)
  rpc GetForexRate (GetForexRateRequest) returns (GetForexRateResponse) {
    option deprecated = true;
  };

  // Rpc is used to cancel order when user cancel the order
  rpc CancelOrder (CancelOrderRequest) returns (CancelOrderResponse) {};

  // Rpc returns details breakout of invoice as per amount and user
  rpc GetInvoice (GetInvoiceRequest) returns (GetInvoiceResponse) {};

  // SignalWorkFlows is used to signal US Stocks workflow
  // Eg: In case of Inward remittance, since there will be only 1 swift transfer for multiple sell orders across actors,  all the corresponding workflows should be sent a relevant signal to update their stages
  // This RPC is used for bulk signalling of workflow using their client_req_ids
  rpc SignalWorkFlows (SignalWorkFlowsRequest) returns (SignalWorkFlowsResponse) {}

  // This rpc is responsible for sync of Account Activity(this represent users trade, dividend, stock split..etc) of user
  // By using this rpc we are syncing our Account Activities table with vendor data for display use case
  // Eg: By periodic Jenkin Job,we will call this rpc which will
  // get data from vendor(alpaca), we are creating bulk entry of events/info  via upsert into our db
  // Note this wont sync data related to trade like buy and sell because we have different state mapping compare to vendor(Alpaca)
  rpc SyncAccountActivity (SyncAccountActivityRequest) returns (SyncAccountActivityResponse) {}

  // This rpc is responsible generate next AccountActivitySync table entry according to type
  // returns account activity sync object based on execution of sync_type
  // Eg: By using this rpc we will get to know which action to be taken next
  rpc GenerateNextAccountActivitySync (GenerateNextAccountActivitySyncRequest) returns (GenerateNextAccountActivitySyncResponse) {}
  // GetGrossSummary returns gross summary of all the orders with the given from_date and to_date
  rpc GetGrossSummary (GetGrossSummaryRequest) returns (GetGrossSummaryResponse) {}
  // GetOrdersWithWFFilters returns orders which passed the wf_stage with wf_stage_status with given updated/created time range
  // it'll also accept actor_id and order_side as optional filters
  // one of [(updated_after, updated_before), (created_after, created_before)] should be present in the request, this is to avoid large data being returned
  // the difference between updated_after and updated_before, created_after and created_before should be less than 3 days, this is to avoid large data being returned
  rpc GetOrdersWithWFFilters (GetOrdersWithWFFiltersRequest) returns (GetOrdersWithWFFiltersResponse) {}

  // GetStats is a generic RPC for getting order related stats
  // eg: Success order count for an actor, In progress order count for an order etc
  // uses FieldMask to compute required stats only
  rpc GetStats (GetStatsRequest) returns (GetStatsResponse) {}

  // InitiateInwardRemittance RPC takes in a partner bank forex rate and creates inward remittance (TTUM) file, GST reporting file, etc.
  // Actors whose accounts are in a credit-freeze state are not included in the files and the corresponding aggregated transaction states are updated.
  // The generated TTUM file is used by partner bank agents to initiate fund transfers to users' savings accounts.
  rpc InitiateInwardRemittance (InitiateInwardRemittanceRequest) returns (InitiateInwardRemittanceResponse) {}

  // InitiateAdHocInwardRemittance takes in a list use starts the process
  // to transfer money from Fi's inward pool bank account to user's bank account by
  // generating batch money transfer files for the partner bank.
  // Only transactions that are not yet transferred to user are picked up to avoid double transfer.
  rpc InitiateAdHocInwardRemittance (InitiateAdHocInwardRemittanceRequest) returns (InitiateAdHocInwardRemittanceResponse);

  // RegenerateInwardRemittance deletes the existing inward file for client req id and regenerates a new inward file by
  // excluding transactions belongs to given credit frozen account
  rpc RegenerateInwardRemittance (RegenerateInwardRemittanceRequest) returns (RegenerateInwardRemittanceResponse);

  // HandleIncomingPayment is expected to be called by order system whenever usstocks related payment is received
  // unlike Buy orders, IFT is not expected to have any active entity for sell orders. Payment is received at order service and Identified if payment is related to usstocks or not
  // HandleIncomingPayment is responsible for performing action on
  rpc HandleIncomingPayment (HandleIncomingPaymentRequest) returns (HandleIncomingPaymentResponse) {}

  // GetMarketStatus is used to know that market is opened or closed for given time
  // if given time is nil will return status for current time
  rpc GetMarketStatus (GetMarketStatusRequest) returns (GetMarketStatusResponse) {}

  // GetAccountActivities returns all the activities (trade/non-trade) associated to requested account
  // it is Bulk rpc which returns all the activities
  rpc GetAccountActivities (GetAccountActivitiesRequest) returns (GetAccountActivitiesResponse) {}

  // GetAccountActivity is returns activity for requested id
  // it is unary rpc which returns  the activity
  // This RPC is only used for trade order activity
  rpc GetAccountActivity (GetAccountActivityRequest) returns (GetAccountActivityResponse) {}

  // BulkCreateAccountActivities is expected to be used temporarily for
  // creating (persisting) account activities for dividends, when they are
  // not synced using broker's API (could be due to API not working)
  // The persisted activities are later used to uniquely identify the contents of the
  // bulk inward fund transfer (TTUM) file
  rpc BulkCreateAccountActivities (BulkCreateAccountActivitiesRequest) returns (BulkCreateAccountActivitiesResponse);

  // RPC used to update the state of entities during the inward remittance acknowledgement flow
  // Entity refers to an abstract object which can be a trade order, dividend, any corporate action
  // or withdrawals from wallet
  // eg: for inward remittance case partner bank will provide forex rate and gst
  // By using this rpc we can update our entities(order/dividend/withdraw)..etc state and populate above fields
  rpc BulkUpdateEntity (BulkUpdateEntityRequest) returns (BulkUpdateEntityResponse) {}

  // InitiateWalletOrderProcessing creates a database entry for requested order and Initiates corresponding workflow
  rpc InitiateWalletOrderProcessing (InitiateWalletOrderProcessingRequest) returns (InitiateWalletOrderProcessingResponse) {}

  // GetWalletOrder returns an order for requested id
  // type of id supported are order_id, client_request_id
  rpc GetWalletOrder (GetWalletOrderRequest) returns (GetWalletOrderResponse) {};

  // GetWalletOrder returns an order in paginated way according to parameters
  rpc GetWalletOrders (GetWalletOrdersRequest) returns (GetWalletOrdersResponse) {};

  // Returns the latest dividend activity for each actor for each symbol
  // Returns error if no activity can be found for a requested actor-symbol combination
  rpc GetLatestDividendActivities (GetLatestDividendActivitiesRequest) returns (GetLatestDividendActivitiesResponse);

  // GetAggregatedRemittanceTxn returns aggregated transactions for international fund transfer using identifiers of a particular type
  // Any identifiers not found are ignored
  rpc GetAggregatedRemittanceTransactions (GetAggregatedRemittanceTransactionsRequest) returns (GetAggregatedRemittanceTransactionsResponse);

  // GetWalletOrderProcessingDetails expects wallet_order_id and returns
  // 1. Corresponding order
  // 2. List of stages with their corresponding current status
  // RPC uses workflow history to get list of stages and their corresponding status
  rpc GetWalletOrderProcessingDetails (GetWalletOrderProcessingDetailsRequest) returns (GetWalletOrderProcessingDetailsResponse) {};

  // CreateOrder creates a us stocks buy/sell order for requested order entity
  // funds will be deducted/added to wallet for corresponding buy/sell order
  // Note: If user does not have wallet activated, non-success response will be returned
  rpc CreateOrder (CreateOrderRequest) returns (CreateOrderResponse);

  // GetVendorAccountActivitiesFile returns download link to a file containing all vendor account activities
  rpc GetVendorAccountActivitiesFile (GetVendorAccountActivitiesFileRequest) returns (GetAccountActivitiesResponseFileResponse) {}

  // ReconOrder is used to reconcile the order with the vendor
  rpc ReconOrder (ReconOrderRequest) returns (ReconOrderResponse) {}

  // TriggerSIPExecution is used for validating and triggering SIP requests for a user.
  // It triggers a SIP workflow for the given request
  rpc TriggerSIPExecution (TriggerSIPExecutionRequest) returns (TriggerSIPExecutionResponse) {}

  // CheckSIPExecutionStatus is used for polling the SIP execution status
  rpc CheckSIPExecutionStatus (CheckSIPExecutionStatusRequest) returns (CheckSIPExecutionStatusResponse) {}

  // Creates gst invoice id for inward and outward order entities, checks if it is
  // unique by trying to update in the database and returns the corresponding gst number
  // This would be called from IFT workflow for outward remittances, and would be called from  <add place/service> during inward
  rpc CreateAndUpdateGstInvoiceNumber (CreateAndUpdateGstInvoiceNumberRequest) returns (CreateAndUpdateGstInvoiceNumberResponse);

  // GetAggregateWalletOrderAmount aggregate order amount according to requested filters
  // aggregates order value in USD from invoice_details
  rpc GetAggregateWalletOrderAmountInUsd (GetAggregateWalletOrderAmountInUsdRequest) returns (GetAggregateWalletOrderAmountInUsdResponse);
}

message GetAggregateWalletOrderAmountInUsdRequest {
  // [Mandatory] actor_id should be provided in request
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // [Optional]: can be used for cases where a particular type of orders are needed
  repeated usstocks.WalletOrderType order_type = 2;
  // [Optional]: can be used for cases where a particular state of orders are needed
  repeated usstocks.WalletOrderStatus order_status = 3;
  // [Mandatory] orders placed after start_time will be returned in response
  // created_at field is used for filtering
  // aggregates can be queried only for 60 days
  google.protobuf.Timestamp start_time = 4 [(validate.rules).timestamp.required = true];
  // [Mandatory] orders placed before end_time will be returned in response
  // created_at field is used for filtering
  // aggregates can be queried only for 60 days
  google.protobuf.Timestamp end_time = 5 [(validate.rules).timestamp.required = true];
}

message GetAggregateWalletOrderAmountInUsdResponse {
  rpc.Status status = 1;
  google.type.Money total_amount = 2;
  int32 total_count = 3;
}

message GetVendorAccountActivitiesFileRequest {
  string account_id = 1;
  // It is for filter option, if from_date and to_date are not provided then it returns all records from starting time to current time
  google.protobuf.Timestamp from_time = 2;
  google.protobuf.Timestamp to_time = 3;
}

message GetAccountActivitiesResponseFileResponse {
  rpc.Status status = 1;
  string file_download_url = 2;
}

message CreateOrderRequest {
  string client_order_id = 1;
  // actor_id of the user
  string actor_id = 2;
  // order specific details
  // symbol/ticker for which order has been placed
  string symbol = 3;
  // foreign key to us_stocks_catalog table for mapping symbol order is placed for
  string catalog_ref_id = 4;
  // defines type of order placed by user
  OrderSide side = 5;
  // quantity of units user has requested to buy/sell
  // eg: 2.5
  double qty_requested = 6;
  // amount value that user has requested to buy/sell
  // eg: 1000 Rs
  // amount requested is the amount entered by the user. This amount is without deduction of charges
  //
  // amount_requested = amount_entered_by_user
  google.type.Money amount_requested = 7;
  // type of order to be placed with vendor
  // this helps in identifying if market order to be placed should be notional/quantity/limit order
  OrderType type = 9;
  // type of funding applicable for the corresponding trade order
  OrderFundingType funding_type = 10;
  // calculated_trade_amount_after_deduction is the amount for which the actual trade is expected to happen
  // consider user entered $100, charges applicable includes 0.25% brokerage i.e. $0.25
  // so the calculated_trade_amount_after_charges_deduction = $99.75
  // calculated_trade_amount_after_charges_deduction = amount_entered_by_user - charges(eg: brokerage)
  google.type.Money calculated_trade_amount_after_charges_deduction = 8;
  // details of a limit order, specifying conditions under which an order to buy or sell is placed.
  LimitOrderDetails limit_order_details = 11;
}

message LimitOrderDetails {
  // used when placing a trade to indicate how long an order will remain active before it is executed or expires.
  TimeInForce time_in_force = 1;
  // price at which buy/sell the stock order to be placed
  google.type.Money limit_price = 2;
}

message CreateOrderResponse {
  enum Status {
    OK = 0;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // an order exists with same client request id
    DUPLICATE_ORDER = 101;

    // generic status code to denote issue with requested amount
    // eg: currency code is not USD
    INCORRECT_REQUESTED_AMOUNT_FOR_ORDER = 102;

    // day trade limit breached, placing order may lead to user being marked as pattern day trader which can incur heavy penalty
    DAY_TRADE_LIMIT_BREACHED = 103;

    // user doesn't have enough buying power
    // example scenario: user has placed an order to withdraw all funds from their wallet, but then tries to buy stocks.
    INSUFFICIENT_BUYING_POWER = 104;

    // Trade amount requested by client did not match the trade amount calculated at BE
    // eg:
    // Assuming, amount entered by user: $100, Brokerage(0.25%): $0.25, Trade amount: $99.75
    // In parallel, If the config for brokerage percentage is changed at BE to 0.50%, Trade amount becomes $99.50($100-$0.50)
    //
    // Order in above scenario should be blocked by BE with relevant error code, so that the situation of disparity in charges presented
    // to user and actual charges at BE will be failed with Status TRADE_AMOUNT_MISMATCH_AFTER_CHARGES_DEDUCTION
    TRADE_AMOUNT_MISMATCH_AFTER_CHARGES_DEDUCTION = 105;

    // incorrect limit order amount for order
    // eg : if limit price is in INR or units are 0
    INVALID_LIMIT_ORDER_PRICE = 106;

    // insufficient quantity to sell
    // eg : if user has less units in portfolio than requested quantity
    INSUFFICIENT_QUANTITY_FOR_SELL = 107;

    // limit order can only be placed on non fractionable stock
  // and for quantity orders fractional quantity is allowed only when stock is fractional
  // status for fractional orders placed for non fractional stock
    FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL = 108;

    // quantity is not valid
    // ex : quantity is less than or equal to zero
    INVALID_QUANTITY_REQUESTED = 109;
  }
  // rpc status
  rpc.Status status = 1;
  // order created as result of CreateOrder API request
  usstocks.order.Order order = 2;
}

message InitiateWalletOrderProcessingRequest {
  // actor_id of the user
  string actor_id = 1;
  // defines type of order placed by user
  WalletOrderType order_type = 2;
  // further classifies the order
  // e.g. for order type ADD_FUNDS
  // order can have sub_type of INSTANT_WALLET_FUNDING or NON_INSTANT_WALLET_FUNDING
  WalletOrderSubType order_sub_type = 3;
  // amount value that user has requested to add/withdraw from wallet in rs
  // eg: 1000 Rs
  google.type.Money amount_requested = 4;
  // InvoiceDetails holds the component wise amount breakdown
  // eg: GST, TCS, Forex rate etc
  WalletOrderInvoiceDetails invoice_details = 5;
  // order Id generated by client eg: FIT
  string client_order_id = 6;
  // if withdraw_all is set to true
  // applicable for order_type WITHDRAW_FUNDS only
  bool withdraw_all = 7;
}

message InitiateWalletOrderProcessingResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // an order exists with same client request id
    DUPLICATE_ORDER = 101;
    // forex rate has changed, user should be displayed new forex rate and order should be created post confirmation
    FOREX_RATE_CHANGED = 102;
    // order creation failed due to max allowed txn limit for the day already consumed
    BREACHED_MAX_ALLOWED_FUND_ADDITION_LIMIT_FOR_DAY = 103;
    // order creation failed due to max allowed txn limit for the financial year already consumed
    BREACHED_MAX_ALLOWED_FUND_ADDITION_LIMIT_FOR_FINANCIAL_YEAR = 104;
    // order creation failed as user is not a vintage Fi user
    // vintage is config driven. eg: 6 Months
    INSUFFICIENT_SAVINGS_ACCOUNT_VINTAGE = 105;
    // order creation failed as user have not performed min number of required transactions through Fi
    INSUFFICIENT_NO_OF_TRANSACTIONS = 106;
    // order creation failed as user has already consumed defined LRS limit for the financial year
    BREACHED_LRS_LIMIT = 107;
    // order creation failed as partner bank does not allow foreign remittance for the actor
    // reason could be - not a full KYC user, NRI user etc
    FOREIGN_REMITTANCE_NOT_ALLOWED = 108;
    // user blacklisted for foreign fund transfer with Fi
    USER_BLACKLISTED = 109;
    // if forex rate deal amount is not present for manual purchase during market open time
    FOREX_RATE_NOT_FOUND_IN_MKT_HRS = 110;
    // order amount is suspected only if all the following conditions are met:
    // 1. International Transaction amount is >2 L
    // 2. International Transaction amount/ Account Balance > 80%
    // 3. International Transaction Amount/ Max(Last 5 Credit transaction Amount) > 90%
    // https://docs.google.com/document/d/1OYrGhaNFnJDY8CcDq6_XgtmRAXFoLeOBzGW22PQ4nWM/edit#bookmark=id.kqm7896u5dv
    ORDER_AMOUNT_SUSPECTED = 111;
    // credit txn is not allowed for the account
    ACCOUNT_CREDIT_FROZEN = 112;
    // requested amount greater than available withdrawable amount
    WITHDRAWABLE_AMOUNT_LESS_THAN_REQUESTED_AMOUNT = 113;
    // inward remittance limit imposed by banking partner breached
    INWARD_REMITTANCE_MAX_ALLOWED_LIMIT_BREACHED = 114;
    // user is personal loan holder
    PERSONAL_LOAN_HOLDER_USER = 115;
    // kyc check with banking partners failed for user
    KYC_CHECK_FAILED_WITH_BANKING_PARTNER = 116;
    // pan validation check with banking partner failed for user
    PAN_CHECK_FAILED_WITH_BANKING_PARTNER = 117;
    // order creation failed due to max allowed txn limit on the basis of sof doc year already consumed for the financial
    BREACHED_SOF_BASED_REMITTANCE_LIMIT_FOR_FINANCIAL_YEAR = 118;
    // user auth factor (mobile/email) was recently update hence add funds and withdrawal won't be allowed for 24 hr
    USER_AUTH_FACTOR_RECENTLY_UPDATED = 119;
  }
  // rpc status
  rpc.Status status = 1;
  // created order
  usstocks.order.WalletOrder order = 2;
  // orders involving fund transfer from user's savings account, require authentication for making payment
  // deeplink points to authentication page. All the required attributes required by client is expected to be part of deeplink
  // since sell orders does not require fund transfer, deeplink will be used for buy orders only
  frontend.deeplink.Deeplink pool_transfer_deeplink = 3;
  // order_id of the pool account transfer transaction
  string pool_transfer_order_id = 4;
}

message GetWalletOrderRequest {
  oneof id {
    string order_id = 1;
    string client_order_id = 2;
    string external_order_id = 3;
  }
  string actor_id = 4;
}

message GetWalletOrderResponse {
  enum Status {
    // Order was successfully fetched
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Record was not found in the system
    NOT_FOUND = 5;
    // order not associated to the actor
    INVALID_ACTOR_ASSOCIATION = 101;
  }
  // rpc status
  rpc.Status status = 1;
  // requested us stocks order object
  usstocks.order.WalletOrder wallet_order = 2;
}


message BulkUpdateEntityRequest {
  repeated EntityToUpdate entity_to_update = 1;
  api.pay.internationalfundtransfer.ForexRate forex_rate = 2;
}

message EntityToUpdate {
  oneof entity_to_update {
    DividendEntityUpdate dividend_entity_update = 1;
  }
}

message DividendEntityUpdate {
  string actor_id = 1;
  string symbol = 2;

  // Goods and services tax to be deducted when a dividend is transferred to the user's bank account
  google.type.Money gst_charged = 3;

  // Dividend amount received from broker for a user's shares
  // Net dividend amount = Dividend amount released by company - amount withheld by US NRA as tax
  google.type.Money net_dividend_amount_in_inr = 4;
}

message BulkUpdateEntityResponse {
  rpc.Status status = 1;
}

message GetAccountActivityRequest {
  string activity_id = 1 [deprecated = true];

  oneof identifier {
    // orderId for trade order
    string order_id = 2;
    // account activity id
    string account_activity_id = 3;
  }
}

message GetAccountActivityResponse {
  // rpc status
  rpc.Status status = 1;
  usstocks.order.AccountActivity activity = 2;
  api.usstocks.catalog.Stock stock = 3;
}

message GetAccountActivitiesRequest {
  // [Mandatory]
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // [Optional]: can be used for cases where a particular type of activities are needed
  repeated usstocks.order.AccountActivityType activity_type = 2;
  // [Optional]: can be used for cases where all orders for a particular symbol or set of symbols are required
  repeated string symbols = 3;
  // represent page_context of current request
  rpc.PageContextRequest page_context = 4;
  enum FieldMask {
    FIELD_MASK_POPULATE_UNSPECIFIED = 0;
    // this field mask help in returning stock related to activities
    FIELD_MASK_POPULATE_STOCKS_MAP = 1;
    // this field mask help in returning order related to activities
    FIELD_MASK_POPULATE_ORDER_MAP = 2;
    // this field mask help in returning wallet order related to activities
    FIELD_MASK_POPULATE_WALLET_ORDER_MAP = 3;
  }
  // depending on the field mask the corresponding field will be populated
  repeated FieldMask field_mask = 5;
  google.protobuf.Timestamp created_after = 6;
  google.protobuf.Timestamp created_before = 7;
  // [Optional]: filter activities based on their current order state.
  repeated usstocks.OrderState order_states = 8;
}

message GetAccountActivitiesResponse {
  // rpc status
  rpc.Status status = 1;
  // requested us stocks order object
  repeated usstocks.order.AccountActivity account_activities = 2;
  rpc.PageContextResponse page_context = 3;
  // this map help in avoiding multiple call to BE for stock ui
  // the client can use activity.symbol to retrieve corresponding symbol for display
  map<string, api.usstocks.catalog.Stock> symbol_to_stock = 4;
  // this map help in avoiding multiple call to BE for order
  // and activity which has order will get the corresponding order here
  map<string, order.Order> order_mapped_to_activity = 5;
  /// this map help in avoiding multiple call to BE for order
  // and activity which has wallet order will get the corresponding order here
  map<string, order.WalletOrder> wallet_order_mapped_to_activity = 6;
}


message GetMarketStatusRequest {
  // rpc check market status for given timestamp
  google.protobuf.Timestamp time = 1;
}

message GetMarketStatusResponse {
  rpc.Status status = 1;
  // it refers that market is open or close
  MarketStatus market_status = 2;
}

message HandleIncomingPaymentRequest {
  // external order Id for corresponding sell order
  // expected to be empty for categories other than sell order eg: Dividend
  string external_order_id = 1;
  // actor Id for which payment is received
  // useful for cases when payment is received without user placing the order eg: Dividend, Pay out rewards etc
  string actor_id = 2;
  // reason for which payment is received to user
  // eg: sell order, dividend received etc
  IncomingPaymentReason reason = 3;
  // transaction Id for corresponding transaction at pay service
  string txn_id = 4;
  // represent utr number of transaction
  string utr = 5;
}

message HandleIncomingPaymentResponse {
  // rpc status
  rpc.Status status = 1;
}

message GetStatsRequest {
  // [Optional] actor id filter
  string actor_id = 1;
  // [Optional] stock ids filter
  repeated string stock_ids = 4;
  // field mask for getting required stats
  // stats specified in the list will be populated
  repeated StatsFieldMask field_mask = 5;
  // type of order which is being selected
  // apply filter based on type sell/buy
  repeated usstocks.OrderSide sides = 6;
  // type of wallet sub type to be considered while getting stats
  // if nil, all sub type will be considered
  repeated usstocks.WalletOrderSubType wallet_sub_types = 7;
}

message GetStatsResponse {
  rpc.Status status = 1;
  // key is field mask in string form
  // eg: StatsFieldMask.STATS_FIELD_MASK_IN_PROGRESS_ORDERS_COUNT will be "STATS_FIELD_MASK_IN_PROGRESS_ORDERS_COUNT"
  // value is count for corresponding stats
  // count_stats will be used for count related stats only
  map<string, int64> count_stats = 2;

  // represent total pending amount for given filters
  // eg: STATS_FIELD_MASK_IN_PROGRESS_ORDERS_TOTAL_AMOUNT is send as part of request
  // rpc will return pending order
  // note we are calculating pending order with only buy side
  google.type.Money processing_amount = 3 [deprecated = true];

  // key is field mask in string form
  // represent total amount for corresponding field mask
  // eg: StatsFieldMask.STATS_FIELD_MASK_IN_PROGRESS_ORDERS_TOTAL_AMOUNT will be "STATS_FIELD_MASK_IN_PROGRESS_ORDERS_TOTAL_AMOUNT"
  // value is amount for corresponding stats
  map<string, google.type.Money> amount_stats = 4;
}

message GetOrdersWithWFFiltersRequest {
  // optional
  string actor_id = 1;
  // filter buy or sell orders
  // optional
  usstocks.OrderSide order_side = 2;
  // filter by the stage of the order's workflow
  // mandatory
  celestial.workflow.Stage wf_stage = 3;
  // filter by the status of the order's workflow stage
  // mandatory
  celestial.workflow.stage.Status wf_stage_status = 4;
  // orders updated after this time will be returned
  // optional
  google.protobuf.Timestamp updated_after = 5;
  // orders updated before this time will be returned
  // should be less than 3 days from updated_after
  // optional
  google.protobuf.Timestamp updated_before = 6;
  // orders created after this time will be returned
  // optional
  google.protobuf.Timestamp created_after = 7;
  // orders created before this time will be returned
  // should be less than 3 days from created_after
  // optional
  google.protobuf.Timestamp created_before = 8;
  // optional
  repeated usstocks.OrderState order_states = 9;
}

message GetOrdersWithWFFiltersResponse {
  rpc.Status status = 1;
  repeated usstocks.order.Order orders = 2;
}

message GetGrossSummaryRequest {
  // currently, only ALPACA is supported
  vendorgateway.Vendor vendor = 1;
  google.type.Date from_date = 2;
  google.type.Date to_date = 3;
}

message GetGrossSummaryResponse {
  rpc.Status status = 1;
  repeated GrossSummaryOnDate gross_summaries = 2;
}

// GrossSummaryOnDate represents gross summary of all the orders on a particular date
message GrossSummaryOnDate {
  google.type.Date effective_date = 1;
  // if failed, gives the reason for failure
  string comment = 2;
  // inward amount on t0 date
  google.type.Money inward_t0 = 3;
  // inward amount on t1 date
  google.type.Money inward_t1 = 4;
  // inward amount on t2 date
  google.type.Money inward_t2 = 5;
  // outward amount on t0 date
  google.type.Money outward_t0 = 6;
  // outward amount on t1 date
  google.type.Money outward_t1 = 7;
  // outward amount on t2 date
  google.type.Money outward_t2 = 8;
}

message GenerateNextAccountActivitySyncRequest {
  AccountActivitySyncType type = 1;
}

message GenerateNextAccountActivitySyncResponse {
  // rpc status
  rpc.Status status = 1;
  // object is created according to last execution of given type
  AccountActivitySyncCursor account_activity_sync_cursor = 2;
}

message SyncAccountActivityRequest {
  // The cursor is the last synced account activity
  AccountActivitySyncCursor account_activity_sync_cursor = 1;
}

message SyncAccountActivityResponse {
  // rpc status
  rpc.Status status = 1;

  rpc.PageContextResponse page_context = 2;

  // Info about the token in page context
  PageTokenInfo page_token_info = 3;
}

message SignalWorkFlowsRequest {
  // represent client_request_id to signal workflow
  repeated string client_request_ids = 1;
  // name of the signal
  string signal = 2;
}

message SignalWorkFlowsResponse {
  // rpc status
  rpc.Status status = 1;
}

message CreateOrderAndInitiateProcessingRequest {
  // order to be created
  usstocks.order.Order order = 1;
  // if sell_all_units is set to true, corresponding sell order will be created for sending all the present holdings
  // RPC is responsible for getting the quantity from broker and placing the buy order using quantity
  // applicable for order_side SELL only
  bool sell_all_units = 2;
}

message CreateOrderAndInitiateProcessingResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // an order exists with same client request id
    DUPLICATE_ORDER = 101;
    // forex rate has changed, user should be displayed new forex rate and order should be created post confirmation
    FOREX_RATE_CHANGED = 102;
    // order creation failed due to max allowed purchase limit for the day already consumed
    BREACHED_MAX_ALLOWED_PURCHASE_LIMIT_FOR_DAY = 103;
    // order creation failed due to max allowed purchase limit for the financial year already consumed
    BREACHED_MAX_ALLOWED_PURCHASE_LIMIT_FOR_FINANCIAL_YEAR = 104;
    // order creation failed as user is not a vintage Fi user
    // vintage is config driven. eg: 6 Months
    INSUFFICIENT_SAVINGS_ACCOUNT_VINTAGE = 105;
    // order creation failed as user have not performed min number of required transactions through Fi
    INSUFFICIENT_NO_OF_TRANSACTIONS = 106;
    // order creation failed as user has already consumed defined LRS limit for the financial year
    BREACHED_LRS_LIMIT = 107;
    // order creation failed as partner bank does not allow foreign remittance for the actor
    // reason could be - not a full KYC user, NRI user etc
    FOREIGN_REMITTANCE_NOT_ALLOWED = 108;
    // user blacklisted for foreign fund transfer with Fi
    USER_BLACKLISTED = 109;
    // if forex rate deal amount is not present for manual purchase during market open time
    FOREX_RATE_NOT_FOUND_IN_MKT_HRS = 110;
    // order amount is suspected only if all the following conditions are met:
    // 1. International Transaction amount is >2 L
    // 2. International Transaction amount/ Account Balance > 80%
    // 3. International Transaction Amount/ Max(Last 5 Credit transaction Amount) > 90%
    // https://docs.google.com/document/d/1OYrGhaNFnJDY8CcDq6_XgtmRAXFoLeOBzGW22PQ4nWM/edit#bookmark=id.kqm7896u5dv
    ORDER_AMOUNT_SUSPECTED = 111;
  }
  // rpc status
  rpc.Status status = 1;
  // order created as result of CreateOrder API request
  usstocks.order.Order order = 2;
  // orders involving fund transfer from user's savings account, require authentication for making payment
  // deeplink points to authentication page. All the required attributes required by client is expected to be part of deeplink
  // since sell orders does not require fund transfer, deeplink will be used for buy orders only
  frontend.deeplink.Deeplink pool_transfer_deeplink = 3;
  // order_id of the pool account transfer transaction
  string pool_transfer_order_id = 4;
}

message GetOrderRequest {
  oneof id {
    string order_id = 1;
    string client_order_id = 2;
    string external_order_id = 3;
    string vendor_order_id = 4;
  }
  string actor_id = 5;
}

message GetOrderResponse {
  enum Status {
    // Order was successfully fetched
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Record was not found in the system
    NOT_FOUND = 14;
    // order not associated to the actor
    INVALID_ACTOR_ASSOCIATION = 101;
  }
  // rpc status
  rpc.Status status = 1;
  // requested us stocks order object
  usstocks.order.Order order = 2;
}

message GetOrdersRequest {
  // [Mandatory]
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // [Optional]: can be used for cases where a particular type of orders are needed
  repeated usstocks.OrderSide order_sides = 2;
  // [Optional]: can be used for cases where a particular state of orders are needed
  repeated usstocks.OrderState order_states = 3;
  // [Optional]: can be used for cases where all orders for a particular symbol or set of symbols are required
  repeated string symbols = 4;
  rpc.PageContextRequest page_context = 5;
  // [Optional]: orders placed after start_time will be returned in response
  google.protobuf.Timestamp start_time = 6;
  // [Optional]: orders placed before end_time will be returned in response
  google.protobuf.Timestamp end_time = 7;
  // [Optional]: can be used for cases where a particular type of orders are needed
  repeated usstocks.OrderType order_types = 8;
}

message GetOrdersResponse {
  enum Status {
    // Order was successfully fetched
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc status
  rpc.Status status = 1;
  // requested us stocks order object
  repeated usstocks.order.Order orders = 2;
  rpc.PageContextResponse page_context = 3;
}

message GetOrderProcessingDetailsRequest {
  oneof id {
    string order_id = 1;
    string client_order_id = 2;
    string external_order_id = 3;
    string vendor_order_id = 4;
  }
  string actor_id = 5;
}

message GetOrderProcessingDetailsResponse {
  enum Status {
    // Order was successfully fetched
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Record was not found in the system
    NOT_FOUND = 14;
    // order not associated to the actor
    INVALID_ACTOR_ASSOCIATION = 101;
  }
  // rpc status
  rpc.Status status = 1;
  // requested us stocks order object
  usstocks.order.Order order = 2;
  // list of step details order has gone through
  repeated OrderProcessingStageDetails order_processing_stages = 3;
}

message UpdateOrderRequest {
  string order_id = 1;
  // target order
  usstocks.order.Order order = 2;
  // list of fields to be updated
  repeated usstocks.order.OrderFieldMask field_masks = 3;
}

message UpdateOrderResponse {
  enum Status {
    // Order was successfully fetched
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Record was not found in the system
    NOT_FOUND = 14;
  }
  // rpc status
  rpc.Status status = 1;
}

message GetInvoiceRequest {
  // will be used for user group check
  string actor_id = 1;

  // Represent amount the user intended to invest
  google.type.Money amount = 2;

  // flag to identify buy or sell orders. If is_sell_order is set to true, InvoiceDetails corresponding to a sell order should be returned
  bool is_sell_order = 3 [deprecated = true];

  enum OrderType {
    ORDER_TYPE_UNSPECIFIED = 0;
    ORDER_TYPE_BUY_STOCK = 1;
    ORDER_TYPE_SELL_STOCK = 2;
    ORDER_TYPE_ADD_FUNDS_IN_WALLET = 3;
    ORDER_TYPE_WITHDRAW_FUNDS_FROM_WALLET = 4;
  }

  OrderType order_type = 4;
}

message GetInvoiceResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // if forex rate deal is not present for manual purchase during market hours
    FOREX_RATE_NOT_FOUND_IN_MKT_HRS = 105;
  }
  // rpc response status
  rpc.Status status = 1;
  InvoiceDetails invoice_details = 2 [deprecated = true];
  // exchange rate decided for the transaction by partner bank
  google.type.Money partner_exchange_rate = 3;
  // amount in INR to be added/withdrawn from wallet
  google.type.Money amount_in_inr = 4;
  // amount in INR to be added/withdrawn from wallet
  google.type.Money amount_in_usd = 5;
  // GST(tax) for the foreign fund transfer
  google.type.Money GST = 6;
  // TCS(tax) for the foreign fund transfer, this is the amount charged by govt
  google.type.Money TCS = 7;
  // total amount debited from user's account(taxes + order amount)
  google.type.Money total_debit_amount = 8;
  // fee that is charged for creating order
  google.type.Money fee = 9;
  // if forex rate is from DB, then it'll be populated
  string forex_rate_id = 10;
  // source from where forex rate is fetched
  ForexRateProvenance forex_rate_provenance = 11;
  // represent total credit amount show to user in case of withdraw funds
  // total_credit_amount = withdraw_amount - GST - (other charges if any)
  google.type.Money total_credit_amount = 12;
}

message CancelOrderRequest {
  // will be used for user group check
  string actor_id = 1;
  string order_id = 2;
}

message CancelOrderResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;

    // Unable to cancel order since it might be fulfilled by vendor
    ORDER_CANT_BE_CANCELED = 15;

    // order not associated to the actor
    INVALID_ACTOR_ASSOCIATION = 101;
  }

  // rpc response status
  rpc.Status status = 1;
}

message GetForexRateRequest {
  // amount required for the transaction of which forex rates needs to be fetched
  google.type.Money required_amount = 1;
}

message GetForexRateResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  // represent 1$ = X Rupee
  google.type.Money forex_rate_usd_to_inr = 2;
  // source from where forex rate is fetched
  ForexRateProvenance provenance = 3;
  // if forex rate is from DB, then it'll be populated
  string forex_rate_id = 4;
}

message ExchangeStatusRequest {
  // time at which the exchange status is requested
  // if null, current time will be considered
  google.protobuf.Timestamp req_time = 1;
}

message ExchangeStatusResponse {
  enum Status {
    OK = 0;
  }
  // rpc response status
  rpc.Status status = 1;
  // true if market is open, else false
  bool is_open_for_trade = 2;
  // eg: If current time is 01 March 09:00 pm IST then next_market_open_time = 02 March 08:00 pm IST
  // If current time is 01 March 09:00 am IST then next_market_open_time = 01 March 08:00 pm IST
  google.protobuf.Timestamp next_market_open_time = 3;
  // if the market is open, then next_market_close_time will be current us market day's market close time
  // if the market is closed, then next_market_close_time will be upcoming market day's market close time
  // e.g: if current time is 01 March 09:00 pm IST then next_market_close_time = 02 March 02:30 pm IST
  google.protobuf.Timestamp next_market_close_time = 4;

  // The current extended trading time period if the market is open under extended trading hours
  ExtendedTradingHours extended_trading_hours = 5;
}

// More at: https://en.wikipedia.org/wiki/Extended-hours_trading
message ExtendedTradingHours {
  // True if the market is open for stock trading under extended hours
  bool extended_hours_trading_open = 1;

  // At max, there can be one of the extended trading time periods open
  // Only populated if extended hours trading is open
  oneof time_period {
    PreMarketTradingTimePeriod pre_market_trading_time_period = 2;
    AfterMarketTradingTimePeriod after_market_trading_time_period = 3;
  }
}

message PreMarketTradingTimePeriod {
  // If trading is allowed because of pre-market trading hours, this denotes when pre-market trading session opened
  google.protobuf.Timestamp opening_ts = 1;

  // The closing time of this pre-market trading hours
  google.protobuf.Timestamp closing_ts = 2;
}

message AfterMarketTradingTimePeriod {
  // If trading is allowed because of after-market trading hours, this denotes when after-market trading session opens
  google.protobuf.Timestamp opening_ts = 1;

  // The closing time of this after-market trading hours
  google.protobuf.Timestamp closing_ts = 2;
}

message InitiateInwardRemittanceRequest {
  // Unique identifier of a forex deal to use for transferring funds to users
  string forex_rate_id = 1;

  // Unique identifier of a batch of inward remittance requests
  string batch_id = 2;

  // Unique SWIFT transaction reference identifier
  string swift_txn_ref = 3;

  // any actor's orders to ignore for processing inward
  repeated string frozen_actor_ids = 4;
}

message InitiateInwardRemittanceResponse {
  // rpc response status
  rpc.Status status = 1;
}

message InitiateAdHocInwardRemittanceRequest {
  repeated string actor_ids = 1;
}

message InitiateAdHocInwardRemittanceResponse {
  rpc.Status status = 1;
}

message RegenerateInwardRemittanceRequest {
  // Unique identifier of a batch of inward remittance requests
  string batch_id = 1;
  // if forex rate is from DB, then it'll be populated
  string forex_rate_id = 2;
  // credit frozen actor id's to exclude from inward file
  repeated string credit_frozen_actor_ids = 3;
}

message RegenerateInwardRemittanceResponse {
  rpc.Status status = 1;
}

message BulkCreateAccountActivitiesRequest {
  repeated usstocks.order.AccountActivity account_activities = 1;
}

message BulkCreateAccountActivitiesResponse {
  rpc.Status status = 1;
}

message GetLatestDividendActivitiesRequest {
  repeated LatestDividendActivityId latest_dividend_activity_ids = 1;
}

message LatestDividendActivityId {
  string actor_id = 1;

  // e.g., AAPL for Apple
  string symbol = 2;
}

message GetLatestDividendActivitiesResponse {
  rpc.Status status = 1;

  repeated usstocks.order.AccountActivity account_activities = 2;
}

message GetAggregatedRemittanceTransactionsRequest {
  repeated string external_ids = 1;
}

message GetAggregatedRemittanceTransactionsResponse {
  rpc.Status status = 1;

  // map keys are external IDs of aggregated transactions
  map<string, AggregatedRemittanceTransaction> aggregation_external_id_to_txn_map = 2;
}


message GetWalletOrderProcessingDetailsRequest {
  oneof id {
    string order_id = 1;
    string client_order_id = 2;
    string vendor_order_id = 3;
  }
  string actor_id = 4;
}

message GetWalletOrderProcessingDetailsResponse {
  enum Status {
    // Order was successfully fetched
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Record was not found in the system
    NOT_FOUND = 14;
    // order not associated to the actor
    INVALID_ACTOR_ASSOCIATION = 101;
  }
  // rpc status
  rpc.Status status = 1;
  // requested us stocks order object
  usstocks.order.WalletOrder order = 2;
  // list of step details order has gone through
  repeated WalletOrderProcessingStageDetails stage_details = 3;
}

message GetWalletOrdersRequest {
  // [Mandatory] only if order ids is NOT provided
  // either actor_id or order_ids should be provided in request
  string actor_id = 1;
  // [Optional]: can be used for cases where a particular type of orders are needed
  repeated usstocks.WalletOrderType order_type = 2;
  // [Optional]: can be used for cases where a particular state of orders are needed
  repeated usstocks.WalletOrderStatus order_status = 3;
  rpc.PageContextRequest page_context = 4;
  // [Optional]: orders placed after start_time will be returned in response
  google.protobuf.Timestamp start_time = 5;
  // [Optional]: orders placed before end_time will be returned in response
  google.protobuf.Timestamp end_time = 6;
  // either actor_id or order_ids should be provided in request
  repeated string order_ids = 7;
}

message GetWalletOrdersResponse {
  // rpc status
  rpc.Status status = 1;
  // requested us stocks order object
  repeated usstocks.order.WalletOrder orders = 2;
  rpc.PageContextResponse page_context = 3;
}


message ReconOrderRequest {
  // [Mandatory]
  string actor_id = 1;
  oneof id {
    string order_id = 2;
  }
}

message ReconOrderResponse {
  // rpc status
  rpc.Status status = 1;
  oneof order {
    usstocks.order.Order market_order = 2;
  }
}

message TriggerSIPExecutionRequest {
  // [Mandatory]
  string actor_id = 1;
  // [Mandatory] atleast 1 object should be present
  repeated usstocks.order.SIPExecutionInfo sip_execution_info = 2;
  // [Mandatory] Used as dedupe id to prevent multiple processing for the same request
  string client_request_id = 3;
}

message TriggerSIPExecutionResponse {
  // rpc status
  rpc.Status status = 1;
  // Workflow id for tracking SIP workflow triggered as part of this request
  string workflow_id = 2;
}

message CheckSIPExecutionStatusRequest {
  // [Mandatory] Required in order to prevent IDOR requests
  // service ensures request belongs to the requested actor
  string actor_id = 1;
  // [Mandatory] Should be same as client_request_id sent in TriggerSIPExecutionRequest
  string client_request_id = 2;
  // [Mandatory] Fittt execution id for which we are expected to see status
  string fittt_execution_id = 3;
}

message CheckSIPExecutionStatusResponse {
  // rpc status
  rpc.Status status = 1;
  usstocks.order.SIPExecutionStatus sip_execution_status = 2;
  // Would be present in case 'FAILED' status is returned
  string failure_reason = 3;
}

message CreateAndUpdateGstInvoiceNumberRequest {
  string order_id = 1;
  RemittanceType remittance_type = 2;
}

message CreateAndUpdateGstInvoiceNumberResponse {
  rpc.Status status = 1;
  string gst_invoice_number = 2;
}
