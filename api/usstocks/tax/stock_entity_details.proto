syntax = "proto3";

package api.usstocks.tax;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/tax";
option java_package = "com.github.epifi.gamma.api.usstocks.tax";

message StockEntityDetails {
  string symbol = 1;
  repeated DailyClosingPrice daily_closing_prices = 2;
  // address of the company/ETF
  string entity_address = 3;
}

message DailyClosingPrice {
  // The time for which the closing price is recorded.
  google.protobuf.Timestamp closing_price_time = 1;
  // The closing price of the stock on the given timestamp.
  google.type.Money closing_price = 2;
}
