syntax = "proto3";

package usstocks.activity;

import "api/celestial/activity/header.proto";
import "api/celestial/workflow/stage.proto";
import "api/celestial/workflow_req.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/international_fund_transfer.proto";
import "api/usstocks/account/account.proto";
import "api/usstocks/account/investor.proto";
import "api/usstocks/enums.proto";
import "api/usstocks/order/account_activity.proto";
import "api/usstocks/order/aggregated_remittance_transaction.proto";
import "api/usstocks/order/order.proto";
import "api/usstocks/order/remittance_process.proto";
import "api/usstocks/order/sip.proto";
import "api/usstocks/order/wallet_order.proto";
import "api/usstocks/rewards/reward_request.proto";
import "api/usstocks/scheduled_task/scheduled_task.proto";
import "api/vendorgateway/stocks/service.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/activity";
option java_package = "com.github.epifi.gamma.api.usstocks.activity";

message SendBuyOrderRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message SendBuyOrderResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // order response received from vendor
  vendorgateway.stocks.Order vg_order = 2;
}

message SendSellOrderRequest {
  celestial.activity.RequestHeader request_header = 1;
}

message SendSellOrderResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // order response received from vendor
  vendorgateway.stocks.Order vg_order = 2;
}

message ReleaseSellLockRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message ReleaseSellLockResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message CancelBuyOrderWithVendorRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message CancelBuyOrderWithVendorResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message UpdateOrderRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // Id of the order to be updated
  string order_id = 2;
  // target order field
  usstocks.order.Order order = 3;
  // field masks for fields to be updated
  repeated usstocks.order.OrderFieldMask field_masks = 4;
}

message UpdateOrderResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message InitiatePanFetchRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message InitiatePanFetchResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message GetPanRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message GetPanResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message InitiateAmlChecksRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message InitiateAmlChecksResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message GetAmlCheckStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message GetAmlCheckStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message CheckLivenessRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message CheckLivenessResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message FetchUserAddressRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message FetchUserAddressResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message CreateAccountWithVendorRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message CreateAccountWithVendorResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message UploadKycRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message UploadKycResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message GetAccountStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message GetAccountStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message TrackOrderStatusRequest {
  celestial.activity.RequestHeader request_header = 1;
}

message TrackOrderStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // order received from vendor
  vendorgateway.stocks.Order order = 2;
}

message SaveAccountStatementRequest {
  celestial.activity.RequestHeader request_header = 1;
  // document url for the generated account statement
  string document_url = 2;
}

message SaveAccountStatementResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message InitiateAccountStatementGenerationRequest {
  celestial.activity.RequestHeader request_header = 1;
}

message InitiateAccountStatementGenerationResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message GetNextOnboardingStageRequest {
  celestial.activity.RequestHeader request_header = 1;
  celestial.workflow.Stage current_stage = 2;
  api.typesv2.common.Platform platform = 3;
  uint32 app_version = 4;
  string actor_id = 5;
}

message GetNextOnboardingStageResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // next stage to perform
  celestial.workflow.Stage next_stage = 2;
}

message GetNextOnboardingStageV2Request {
  celestial.activity.RequestHeader request_header = 1;
  celestial.workflow.Stage current_stage = 2;
  api.typesv2.common.Platform platform = 3;
  uint32 app_version = 4;
  string actor_id = 5;
}

message GetNextOnboardingStageV2Response {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // next stage to perform
  celestial.workflow.Stage next_stage = 2;
}

message GetNextOnboardingActionRequest {
  celestial.activity.RequestHeader request_header = 1;
  celestial.workflow.Stage current_stage = 2;
  api.typesv2.common.Platform platform = 3;
  uint32 app_version = 4;
  string actor_id = 5;
}

message GetNextOnboardingActionResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  frontend.deeplink.Deeplink deeplink = 2;
  OnboardingStepStatus step_status = 3;
}

// status of the step while getting next action
enum OnboardingStepStatus {
  ONBOARDING_STEP_STATUS_UNSPECIFIED = 0;
  // onboarding step is completed successfully
  ONBOARDING_STEP_STATUS_SUCCESSFUL = 1;
  // onboarding step is failed
  ONBOARDING_STEP_STATUS_FAILED = 2;
  // onboarding step is not required any more, for example if user was not 1 year old user will be taken to sof flow
  // if user drops off and come back after user becomes a year old we will not need to perform sof step
  ONBOARDING_STEP_STATUS_SKIPPED = 3;
}

message ValidateAndSaveDataRequest {
  celestial.activity.RequestHeader request_header = 1;
  celestial.workflow.Stage current_stage = 2;
  string actor_id = 3;
  account.Disclosures disclosures = 4;
  account.Agreements agreements = 5;
  account.UserInvestmentInterest investment_interest = 6;
  account.OnboardingStep onboarding_step = 7;
}

message ValidateAndSaveDataResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  frontend.deeplink.Deeplink deeplink = 2;
}

message CheckDocumentsRequest {
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
}

message CheckDocumentsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  bool isPanAvailable = 2;
  bool isStatementAvailable = 3;
}

message SendNotificationRequest {
  celestial.activity.RequestHeader request_header = 1;
  UsStockNotificationType us_stock_notification_type = 2;
  oneof notificationData {
    DividendGstDebitNotificationData dividendGstDebitData = 3;
  }
}

message DividendGstDebitNotificationData {
  string actor_id = 1;
  // amount of GST debit
  google.type.Money gst_amount = 2;
  // Symbol of stock for which dividend was credited
  // Not needed for aggregated dividends
  string symbol = 3;
  // amount of dividend credited
  google.type.Money dividend_amount = 4;
  // OMS order id for dividend transaction
  string oms_order_id = 5;
  // client id for refund order(b2c payment)
  string b2c_payment_req_id = 6;
}

message SendNotificationResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// request to send signal to a workflow running with different ownership
message SendWorkflowSignalRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  bytes payload = 2;
  // represent client_req_id of workflow
  celestial.ClientReqId to_client_req_id = 3;
  // represent signal id of signal
  string signal_id = 4;
  // represent ownership of where signal need to be send
  api.typesv2.common.Ownership signal_ownership = 5;
}

message SendWorkflowSignalResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message GetPanManualReviewStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message GetPanManualReviewStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  usstocks.account.PanValidationResult pan_validation_result = 2;
}

message SendBankDetailsToBrokerRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
  // vendor represents the broker which facilitates the account management and trading for the actor
  vendorgateway.Vendor broker = 3;
}

message SendBankDetailsToBrokerResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message PollBankStatusFromBrokerRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
  // vendor represents the broker which facilitates the account management and trading for the actor
  vendorgateway.Vendor broker = 3;
}

message PollBankStatusFromBrokerResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message CreateLRSCheckRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
}

message CreateLRSCheckResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message RefundAmountFromBusinessAccountRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
  // money to refund
  google.type.Money amount = 3;
}

message RefundAmountFromBusinessAccountResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // client req id for b2c payment request
  string b2c_payment_req_id = 2;
}

message TrackRefundStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // order id for b2c payment
  string b2c_payment_req_id = 2;
}

message TrackRefundStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message CreateRemittanceProcessesRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // type of account activity for which we need to start remittance process for.
  // This will be needed to filter the requests in transaction_remittance_requests table.
  usstocks.order.AccountActivityType account_activity_type = 2;
  // type of remittance requests to be picked for initiation
  // This will be needed to filter the requests in transaction_remittance_requests table.
  usstocks.RemittanceType remittance_type = 3;
}

message CreateRemittanceProcessesResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // list of process ids created
  repeated string process_ids = 2;
}

message StartRemittanceProcessesRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // list of remittance processes to be initiated
  repeated string process_ids = 2;
}

message StartRemittanceProcessesResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message TrackFundsCreditAtVendorRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // swift transfer id for the corresponding foreign remittance
  string swift_transfer_id = 2;
  // amount in foreign currency that was transferred eg: $500
  google.type.Money transfer_amount = 3;
  // time at which the transfer was initiated
  google.protobuf.Timestamp transfer_initiated_at = 4;
}

message TrackFundsCreditAtVendorResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // status of the funds credit at vendor target account
  usstocks.RemittanceProcessStatus status = 2;
}

message UpdateRemittanceProcessRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // updated remittance process object
  usstocks.order.RemittanceProcess remittance_process = 2;
  // list of fields to be updated
  repeated usstocks.order.RemittanceProcessFieldMask updated_fields = 3;
}

message UpdateRemittanceProcessResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message CreateAggregateRemittanceTxnsRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // remittance process id
  string process_id = 2;
}

message CreateAggregateRemittanceTxnsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // amount to be remitted
  google.type.Money amount = 2;
}

message StartInwardFundTransferRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // remittance process id
  string process_id = 2;
  // amount to be remitted
  google.type.Money amount = 3;
}

message StartInwardFundTransferResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // identifier for the fund transfer initiated at vendor end
  string fund_transfer_id = 2;
}

message PollInwardFundTransferStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // remittance process id
  string process_id = 2;
  // identifier for the fund transfer initiated at vendor end
  string fund_transfer_id = 3;
}

message PollInwardFundTransferStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message StartInwardRemittanceProcessInIftRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // remittance process id
  string process_id = 2;
}

message StartInwardRemittanceProcessInIftResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message UpdateRemittanceTxnAndProcessStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // details required to perform update on remittance process details entries
  RemittanceProcessUpdateDetails remittance_process_update_details = 2;
  // details required to perform update on aggregates remittance txns details
  AggregateRemittanceTxnUpdateDetails aggregate_remittance_txn_update_details = 3;
}

message UpdateRemittanceTxnAndProcessStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message AggregateRemittanceTxnUpdateDetails {
  // this is used to identify the entries to be updated.
  oneof identifier {
    // remittance process id
    string process_id = 1;
  }
  // field to be updated
  repeated usstocks.order.AggregatedRemittanceTransactionFieldMask field_masks = 2;
  // target value of the fields.
  usstocks.order.AggregatedRemittanceTransaction target_value = 3;
}

message RemittanceProcessUpdateDetails {
  // this is used to identify the entries to be updated.
  oneof identifier {
    // remittance process id
    string id = 1;
  }
  // field to be updated
  repeated usstocks.order.RemittanceProcessFieldMask field_masks = 2;
  // target value of the fields.
  usstocks.order.RemittanceProcess target_value = 3;
}

// money can be transferred from/to user's wallet to/from business account
message InitiateWalletFundTransferRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // order id for wallet order
  string wallet_order_id = 2;
}

// money can be transferred from/to user's wallet to/from business account
message InitiateWalletFundTransferResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // Identifier for the transaction provided by vendor
  string vendor_order_id = 2;
  // journal order created at vendor as part of wallet fund transfer
  google.protobuf.Timestamp order_creation_timestamp_at_vendor = 3;
}

message TrackWalletFundTransferStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // Identifier for the transaction provided by vendor
  string vendor_order_id = 2;
}

message TrackWalletFundTransferStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // status of funds transfer
  usstocks.WalletTransferStatus transfer_status = 2;
  // journal order created at vendor as part of wallet fund transfer
  // Deprecated - order creation timestamp should be part of order creation request and not of track status response
  google.protobuf.Timestamp order_creation_timestamp_at_vendor = 3 [deprecated = true];
}

// Initiate Remittance initialises a remittance process and returns a remittance_req_id
message InitiateInwardsRemittanceRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // wallet order id for which remittance needs to be initiated
  string wallet_order_id = 2;
}

message InitiateInwardsRemittanceResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // identifier for remittance request
  string remittance_req_id = 2;
}

message TrackInwardsRemittanceStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // identifier for remittance request
  string remittance_req_id = 2;
}

message TrackInwardsRemittanceStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // transaction status for inwards remittance
  usstocks.AggregatedRemittanceTransactionState state = 2;
  // GST deduction for the remittance
  google.type.Money gst_amount = 3;
  // id of the applicable forex rate
  string forex_rate_id = 4;
  // exchange rate decided for the transaction by partner bank
  google.type.Money partner_exchange_rate = 5;
  // source from where forex rate is fetched
  usstocks.order.ForexRateProvenance forex_rate_provenance = 6;
  // represent total credit amount show to user in case of withdraw funds
  // total_credit_amount = withdraw_amount - GST
  google.type.Money total_credit_amount = 7;
  // amount (in INR) remitted from pool account of Fi to user's account
  google.type.Money remittance_amount_inr = 8;
}

message UpdateWalletOrderRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // Id of the order to be updated
  string wallet_order_id = 2;
  // target order
  usstocks.order.WalletOrder wallet_order = 3;
  // field masks for fields to be updated
  repeated usstocks.order.WalletOrderFieldMask field_masks = 4;
}

message UpdateWalletOrderResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message TrackRemittanceProcessStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // identifier for corresponding swift transfer
  string swift_transfer_id = 2;
}

message TrackRemittanceProcessStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // transaction status for corresponding remittance
  usstocks.RemittanceProcessStatus status = 2;
}

message CreateRewardOrdersRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // identifier for the reward request for which orders
  // need to be created
  string reward_request_id = 2;
  // identifier for the workflow triggering this
  string workflow_request_id = 3;
}

message CreateRewardOrdersResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // wallet order id created which is responsible for journaling the cash to users wallet
  string wallet_order_id = 2;
  // order id for stock buy order which would use the added funds to place a buy order
  string buy_order_id = 3;
}

message UpdateRewardRequestDetailsRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // Reward request struct to be updated
  usstocks.rewards.RewardRequest reward_request = 2;
  // field masks for fields to be updated
  repeated usstocks.rewards.RewardRequestFieldMask field_masks = 3;
}

message UpdateRewardRequestDetailsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message UpdateAccountRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // target account. must contain fields to uniquely identify an account.
  usstocks.account.Account account = 2;

  // field masks for fields to be updated
  repeated usstocks.account.AccountFieldMask field_masks = 3;
}

message UpdateAccountResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message ExecuteUssScheduledTaskHighPriorityRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  scheduled_task.ScheduledTask task = 2;
}

message ExecuteUssScheduledTaskHighPriorityResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message SendAlertForUssScheduledTaskRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  scheduled_task.ScheduledTask task = 2;
}

message SendAlertForUssScheduledTaskResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}


message GetCorporateActionsFromVendorRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // get Corporate Actions from start date from vendor
  google.type.Date start_date = 2;
  // get corporate actions till end_date from vendor
  google.type.Date end_date = 3;
}

message GetCorporateActionsFromVendorResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // VG response of corporate actions
  vendorgateway.stocks.CorporateActions corporate_actions = 2;
}

message UpdateOrCreateSymbolsRequest {
  celestial.activity.RequestHeader request_header = 1;
  //  VG response of corporate actions
  vendorgateway.stocks.CorporateActions corporate_actions = 2;

}

message UpdateOrCreateSymbolsResponse {
  celestial.activity.ResponseHeader response_header = 1;
}

// This activity parses the inward remittance pool account statement,
// matches debits for inward remittances to individual users,
// and fetches transaction details such as transaction ID, transaction time, etc.
// This data is essential for subsequent GST reporting.
message GetInwardRemittanceTransactionInfoRequest {
  celestial.activity.RequestHeader request_header = 1;
  // Represents the remittance process ID used for generating inward remittance SWIFT transfers with Alpaca.
  string batch_id = 2;
}

message GetInwardRemittanceTransactionInfoResponse {
  celestial.activity.ResponseHeader response_header = 1;
  api.typesv2.TTUMTransactionInfo transaction_info = 2;
}

// Initiates the GST reporting workflow using the provided vendor request ID and transaction information.
// This workflow manages GST reporting with the federal vendor.
// Logic:
// 1) Retrieve all transactions (either initiated or reported) for the specified batch ID based on their GST reporting state.
// 2) Process these transactions in parallel using goroutines.
// 3) For transactions not yet initiated, start GST reporting with the vendor, update the status to "initiated," and retry in the next cycle.
// 4) If a transaction is initiated but reaches a terminal state, poll the vendor for status and update the status to "verified."
// 4.2) If initiated but not in a final state, retry after a delay.
// Mark the activity as successful if no aggregated transactions are left to process.
message ReportGstCollectedDuringInwardRemittanceRequest {
  celestial.activity.RequestHeader request_header = 1;
  // Represents the remittance process ID used for generating inward remittance SWIFT transfers with Alpaca.
  string batch_id = 2;
  // Represents the transaction information fetched using GetInwardRemittanceTransactionInfo Response
  api.typesv2.TTUMTransactionInfo transaction_info = 3;
}

message ReportGstCollectedDuringInwardRemittanceResponse {
  celestial.activity.ResponseHeader response_header = 1;
}


// This activity populates the vendor request ID for all aggregate transactions within the batch ID.
// The primary goal is to prevent duplicate reporting to the vendor.
message PopulateGSTReportingIdRequest {
  celestial.activity.RequestHeader request_header = 1;
  // Represents the remittance process ID used for generating inward remittance SWIFT transfers with Alpaca.
  string batch_id = 2;
}

message PopulateGSTReportingIdResponse {
  celestial.activity.ResponseHeader response_header = 1;
}

message CreateSIPWalletOrderRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
  // SIP execution info for each SIP to be executed
  repeated usstocks.order.SIPExecutionInfo sip_execution_info = 3;
  string workflow_id = 4;
  // Client request id shared by Fittt
  // used for deduping for Add funds order
  string client_request_id = 5;
}

message CreateSIPWalletOrderResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // add funds to wallet order id created which is responsible for transferring funds to user's wallet
  string add_funds_order_id = 2;
}

message TriggerPaymentForSIPFundTransferRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
  string add_funds_order_id = 3;
}

message TriggerPaymentForSIPFundTransferResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message UpdateInvoiceDetailsRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
  // To be used for querying the order whose invoice would be updated
  string add_funds_order_id = 3;
  // Sent by IFT on Forex deal consumption to update the invoice details in the wallet order
  order.WalletOrderInvoiceDetails invoice_details = 4;
}

message UpdateInvoiceDetailsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message CreateSIPBuyOrdersRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
  // SIP execution info for each SIP to be executed
  repeated usstocks.order.SIPExecutionInfo sip_execution_info = 3;
  string workflow_id = 4;
  // To be used in formulating client request id of the buy orders
  string add_funds_order_id = 5;
}

message CreateSIPBuyOrdersResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // list of buy order ids created for SIP
  repeated string buy_order_ids = 2;
}

message GetSOFStatusForOnboardingRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // actor id of the user for whom SOF status is being fetched
  string actor_id = 2;
}

message GetSOFStatusForOnboardingResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}
