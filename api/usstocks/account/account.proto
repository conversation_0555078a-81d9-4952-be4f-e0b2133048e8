syntax = "proto3";

package usstocks.account;

import "google/protobuf/timestamp.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/account";
option java_package = "com.github.epifi.gamma.api.usstocks.account";

// Account is a US stock brokerage account. It is an investor's financial account with a licensed brokerage to buy and sell securities.
//go:generate gen_sql -types=AccountStatus,VendorAccountInfo,PostAccountCreationInfo
message Account{
  // unique identifier for an account
  string id = 1;
  // actor for which account is created
  string  actor_id = 2;
  // different status for us stocks broking account
  AccountStatus account_status = 3;
  // vendor on which broking account is opened for the actor
  vendorgateway.Vendor vendor = 4;
  // account id for communication with vendor
  string vendor_account_id = 5;
  // A human-readable account number that can be shown to the end user
  string  external_account_id = 6;
  // additional information for account provided by vendor
  VendorAccountInfo vendor_account_info = 7;
  // account created at time stamp
  google.protobuf.Timestamp created_at = 8;
  // account last updated time stamp
  google.protobuf.Timestamp updated_at = 9;
  // account deleted time stamp
  google.protobuf.Timestamp deleted_at = 10;
  // Orchestration client id used to start orchestrator execution
  string orchestration_id = 11;
  // read-only data specific to steps after account is created successfully
  PostAccountCreationInfo post_account_creation_info = 12;
}

enum AccountStatus{
  ACCOUNT_STATUS_UNSPECIFIED = 0;
  // - Account will be in INITIATED state when we start on-boarding process by collecting user’s consents and disclosures
  INITIATED = 1;
  // - Account creation request is sent to vendor
  IN_PROGRESS = 2;
  // - Account is created by the vendor(It may not be active for investing)
  CREATED = 3;
  // - Account is kyc verified by the vendor
  KYC_VERIFIED = 4;
  // - Account is ready, investor can invest
  ACTIVE = 5;
  // - OPS action needed to create an account
  MANUAL_INTERVENTION = 6;
  // - Verifying user’s kyc details
  KYC_VERIFICATION_IN_PROCESS = 7;
  // - account is temporarily disabled for placing an order
  INACTIVE = 8;
  // - vendor rejected the on-boarding application
  REJECTED = 9;
  // user account is on hold due to some pre-condition failure
  // eg: if user has disclosed to be a politically-exposed person or is not willing to take market risk
  CREATION_ON_HOLD = 10;
}

message VendorAccountInfo{
  // display account number provided by vendor
  // we will use Account.ExternalAccountId for display as there may be some vendor not sending display account id
  string account_number = 1;
  // type of account
  AccountType account_type = 2;
}

// PostAccountCreationInfo denotes the read-only(or seldom updated) data for the post account creation steps.
// Please refer to the implementation of `celestial.workflow.USS_POST_ACCOUNT_CREATION` to understand the steps in the
// post account creation workflow.
// NOTE: please refrain from adding more fields to this message if its expected to be write-heavy.
message PostAccountCreationInfo {
  string orchestration_id = 1;
  // vendor (broker) identifier for the bank details of the user.
  string vendor_bank_id = 2;
  // status of the bank details shared to the vendor
  BankDetailsStatus bank_status = 3;
}

enum AccountType{
  ACCOUNT_TYPE_UNSPECIFIED = 0;
  // Typical brokerage account
  // all our accounts will be trading
  TRADING = 1;
  // Trading account opened on behalf of a minor and ownership will be transferred to the minor when they reach the age of majority
  CUSTODIAL = 2;
  // Trading account established at a public charity which allows donors to receive tax benefits from their charitable contributions
  DONOR_ADVISED = 3;
}

enum AccountFieldMask{
  ACCOUNT_FIELD_MASK_UNSPECIFIED = 0;
  ACCOUNT_FIELD_MASK_ACTOR_ID = 1;
  ACCOUNT_FIELD_MASK_ACCOUNT_STATUS = 2;
  ACCOUNT_FIELD_MASK_VENDOR = 3;
  ACCOUNT_FIELD_MASK_VENDOR_ACCOUNT_ID = 4;
  ACCOUNT_FIELD_MASK_EXTERNAL_ACCOUNT_ID = 5;
  ACCOUNT_FIELD_MASK_VENDOR_ACCOUNT_INFO = 6;
  ACCOUNT_FIELD_MASK_ID = 7;
  ACCOUNT_FIELD_MASK_ORCHESTRATION_ID = 8;
  ACCOUNT_FIELD_MASK_POST_ACCOUNT_CREATION_INFO = 9;
}

// BankDetailsStatus denotes the status of bank details we sent to the vendor. The vendor requires the bank details
// to add to the MT199 files generated during Wire transfer for sell orders.
enum BankDetailsStatus {
  BANK_DETAILS_STATUS_UNSPECIFIED = 0;
  // broker has received the bank details.
  BANK_DETAILS_STATUS_INITIATED = 1;
  // broker has rejected the bank details of the user.
  // NOTE: we do not know in what cases can the bank details be rejected. This is not reachable for now.
  BANK_DETAILS_STATUS_REJECTED = 2;
  // broker has approved the bank details of the user.
  BANK_DETAILS_STATUS_APPROVED = 3;
  // the bank details were deleted by the user. NOTE: this is not reachable currently.
  BANK_DETAILS_STATUS_DELETED = 4;
  // broker has canceled the relationship with the bank
  BANK_DETAILS_STATUS_CANCELED = 5;
}
