// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package inhousebre;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/inhousebre";
option java_package = "com.github.epifi.gamma.api.inhousebre";

//  BRE service for providing rest-based interfaces for all inhouse bre related functionalities
service Bre {
  // GetFeaturesData rpc to fetch data related to the list of features sent in the request
  // Rpc uses feature store sdk to get the data
  rpc GetFeaturesData (GetFeaturesDataRequest) returns (GetFeaturesDataResponse) {
    option (google.api.http) = {
      post: "/inhouse/getFeaturesData"
      body: "*"
    };
  }

  // GetEpfoData rpc to fetch epfo data from epfo service that can be fetched using http or grpc calls
  rpc GetEpfoData (GetEpfoDataRequest) returns (GetEpfoDataResponse) {
    option (google.api.http) = {
      post: "/inhouse/v0/getEpfoData"
      body: "*"
    };
  }

  // GetCibilReportFeatures rpc to fetch the cibil report features for a user
  rpc GetCibilReportFeatures (GetCibilReportFeaturesRequest) returns (GetCibilReportFeaturesResponse) {
    option (google.api.http) = {
      post: "/inhouse/getCibilReportFeatures"
      body: "*"
    };
  }

  // GetPdScore rpc to fetch pd score for a user according to the credit report. This is being evaluated by DS apis
  rpc GetPdScore (GetPdScoreRequest) returns (GetPdScoreResponse) {
    option (google.api.http) = {
      post: "/inhouse/getPdScore"
      body: "*"
    };
  }
}

message GetFeaturesDataRequest {
  // features list for which data needs to be fetched from feature store
  // NOTE : feature store currently only supports fetching data for features which have same set of request identifiers
  // Example : Say a feature F1 has actorId and model name as request identifier, F2 has actorId as request identifier and F3 has actorId and
  // model name as request identifier, then in a single api call we can either support [F2] or [F1,F3].
  repeated string feature_name_list = 1 [json_name = "FeatureNameList"];
  // request identifiers containing list of Identifiers for each request
  // we will evaluate the above feature list for each request identifier
  // Request Identifiers should have same set of list of identifiers for each request as mentioned above
  repeated RequestIdentifiers request_identifiers_list = 2 [json_name = "RequestIdentifiersList"];
}

message RequestIdentifiers {
  // Identifiers for which data needs to be fetched
  // Currently the supported identifiers are actorId, accountId or Model Name and combination of the same
  Identifiers identifiers = 2 [json_name = "Identifiers"];
}

message GetFeaturesDataResponse {
  // list of features data for identifiers sent in the request
  repeated FeaturesResponseData features_response_data_list = 1 [json_name = "FeaturesResponseDataList"];
}

message FeaturesResponseData {
  // Identifiers sent in the request
  Identifiers identifiers = 1 [json_name = "Identifiers"];
  // List of features value for the identifier
  map<string, google.protobuf.Value> feature_value_map = 2 [json_name = "FeatureValueMap"];
}

// Identifiers for which data is fetched from feature store
// Based on the feature we can have any combination of these identifiers
message Identifiers {
  // actor Id
  string actor_id = 1 [json_name = "ActorId"];
  // account Id
  string account_id = 2 [json_name = "AccountId"];
  // Model name
  string model_name = 3 [json_name = "ModelName"];
}

message GetEpfoDataRequest {
  string actor_id = 1 [json_name = "ActorId"];
}

message GetEpfoDataResponse {
  string raw_epfo_data = 1 [json_name = "RawEpfoData"];
}

message GetCibilReportFeaturesRequest {
  string actor_id = 1 [json_name = "actorId"];
  repeated string features_names = 2 [json_name = "featureNames"];
}

message GetCibilReportFeaturesResponse {
  map<string, google.protobuf.Value> feature_value_map = 1 [json_name = "featureValueMap"];
  // this status will define whether the cibil report data is available(With history or Without history) or not
  string cibil_report_data_availability_status = 2 [json_name = "cibilReportDataAvailabilityStatus"];
}

message GetPdScoreRequest {
  string actor_id = 1 [json_name = "actorId"];
  string bureau = 2 [json_name = "bureau"]; // accepts CIBIL and EXPERIAN strings
}

message GetPdScoreResponse {
  double pd_score = 1 [json_name = "pdScore"];
  string pd_score_version = 2 [json_name = "pdScoreVersion"];
}

