syntax = "proto3";

package vendormapping;

import "api/rpc/status.proto";
import "api/vendorgateway/vendor.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendormapping";
option java_package = "com.github.epifi.gamma.api.vendormapping";

service VendorMappingService {
  /*
   This method will be used by the backend micro services.
   This takes the input as GetBEMappingByIdRequest and any id can be passed in it.
   If the system is able to retrieve the id  it will return the mapping or else it'll create the new ones.
   There are only two status that are handled today
   INTERNAL SERVER ERROR error in case of backend error
   rpc.StatusOk() for success message
  */
  rpc GetBEMappingById (GetBEMappingByIdRequest) returns (GetBEMappingByIdResponse) {}
  /*
    This method will be used by the RudderStack or Data Platform.
    This takes the input as GetDPMappingByIdRequest and only prospect_id or actor_id can be passed in it.
    For prospect_id:
    If the system is able to retrieve the prospect_id  it will return the mapping or else it'll create the new ones.
    There are only two status that are handled today
    INTERNAL SERVER ERROR error in case of backend error
    rpc.StatusOk() for success message
    For actor_id:
    If the system is able to retrieve the actor_id  it will return the mapping or else record not found status will be returned.
    There are only three status that are handled today
    INTERNAL SERVER ERROR error in case of backend error
    rpc.StatusOk() for success message
    rpc.StatusRecordNotFound() for actor_id which is not present in the system
  */
  rpc GetDPMappingById (GetDPMappingByIdRequest) returns (GetDPMappingByIdResponse) {}

  /*
     This method will be used by the RudderStack or Data Platform.
     This takes the input as GetInputIdByVendorRequest and only mapped vendor id and vendor type can be passed in it.
     If the system is able to retrieve the input id for the mapped vendor id it will return it
     There are only two status that are handled today
     INTERNAL SERVER ERROR error in case of backend error
     rpc.StatusOk() for success message
     rpc.StatusRecordNotFound() for vendor id which is not present in the system
  */
  rpc GetInputIdByVendor (GetInputIdByVendorRequest) returns (GetInputIdByVendorResponse) {}

  /*
    UpsertActorWithProspect rpc is used to sync an actor_id against a prospect_id.
    It will be called by the user service after the user has given their consent.
    Once the consent is recorded, the user service will asynchronously fire a call to this RPC
    to sync the actor
  */
  rpc UpsertActorWithProspect (UpsertActorWithProspectRequest) returns (UpsertActorWithProspectResponse) {}

  // UpsertClientAfId rpc is used to upsert client_appsflyer_id against a prospect_id
  // or an actor_id depending upon the request type
  rpc UpsertClientAfId (UpsertClientAfIdRequest) returns (UpsertClientAfIdResponse) {}

  // GenerateProspectId rpc is used to generate a unique UUID to be used as prospect id for client. rpc will also check
  // the generated ID against vendorMapping table to make sure that generated UUID is not an already existing duplicate.
  rpc GenerateProspectId (GenerateProspectIdRequest) returns (GenerateProspectIdResponse) {}

  // GetActorIdByVendorId returns the actorId mapped to the given vendorId if it exists,
  // otherwise returns NotFound
  rpc GetActorIdByVendorId (GetActorIdByVendorIdRequest) returns (GetActorIdByVendorIdResponse) {};

  // GetBulkDPVendorMappingsByVendorId returns the list of vendor mappings mapped with given vendor ids if mapping is present
  // supports 100 records in one single call
  rpc GetBulkDPVendorMappingsByVendorIds (GetBulkDPVendorMappingsByVendorIdsRequest) returns (GetBulkDPVendorMappingsByVendorIdsResponse) {};
}

message GetBulkDPVendorMappingsByVendorIdsRequest {
  oneof VendorIds {
    FirehoseIds firehose_ids = 1;
  }
  message FirehoseIds {
    repeated string ids = 1;
  }
}

message GetBulkDPVendorMappingsByVendorIdsResponse {
  // rpc status
  rpc.Status status = 1;

  repeated DPVendorMapping dp_vendor_mappings = 2;
}

message GenerateProspectIdRequest {}

message GenerateProspectIdResponse {
  // rpc status
  rpc.Status status = 1;
  // unique UUID
  string prospect_id = 2;
}

message GetBEMappingByIdRequest {
  string id = 1;
}

message GetBEMappingByIdResponse {
  // rpc status
  rpc.Status status = 1;
  //mapped id for freshdesk for the passed input id
  string freshdesk_id = 2;
  //mapped id for ozonetel for the passed input id
  string ozonetel_id = 3;
  //mapped id for loylty for the passed input id
  string loylty_id = 4;
  //mapped id for firebase for the passed input id
  string fcm_id = 5;
  // mapped id for senseforth for the passed input id
  string senseforth_id = 6;
  // mapped id for tss for the passed input id
  string tss_id = 7;
  // mapped id for riskcovry vendor for the passed input id
  string riskcovry_id = 8;
  // mapped id for thriwe vendor for the passed input id
  string thriwe_id = 9;
  // mapped id for dpanda vendor for the passed input id
  string dpanda_id = 10;
  // mapped id for poshvine vendor for the passed input id
  string poshvine_id = 11;
  // mapped id for razorpay vendor for the passed input id
  string razorpay_id = 12;
  // mapped id for setting custom user id in Ms Clarity:
  // https://learn.microsoft.com/en-us/clarity/mobile-sdk/android-sdk?tabs=kotlin#setcustomuserid
  string ms_clarity_id = 13;
  // mapped id for ignosis vendor for the passed input id
  string ignosis_id = 14;
}

message GetInputIdByVendorRequest {
  // mandatory mapped vendor id for which the input id is to be fetched for
  string id = 1;
  // mandatory vendor type for which the input id is to be fetched for
  vendorgateway.Vendor vendor = 2;
}

message GetInputIdByVendorResponse {
  // rpc status
  rpc.Status status = 1;
  // mapped input id for the vendor
  string input_id = 2;
}

message GetDPMappingByIdRequest {

  oneof identifier {

    string prospect_id = 1;

    string actor_id = 2;

  }
}

message GetDPMappingByIdResponse {
  // rpc status
  rpc.Status status = 1;
  //mapped id for amplitude for the passed actor/prospect id
  string amplitude_id = 2;
  //mapped id for moengage for the passed actor/prospect id
  string moengage_id = 3;
  //mapped id for firehose for the passed actor/prospect id
  string firehose_id = 4;
  //mapped id for appsflyer for the passed actor/prospect id
  string appsflyer_id = 5;
  //client appsflyer id that is stored from the frontend
  string client_appsflyer_id = 6;
  //flag that represents if event should be sent to amplitude
  bool send_to_amplitude = 7;
  string prospect_id = 8;
}

message UpsertActorWithProspectRequest {
  // mandatory actor_id for which sync is to be done
  string actor_id = 1;
  // mandatory prospect_id against which sync of the actor is to be done
  string prospect_id = 2;
}

message UpsertActorWithProspectResponse {
  // rpc status
  rpc.Status status = 1;
}

message UpsertClientAfIdRequest {
  string client_af_id = 1 [(validate.rules).string.min_len = 1];
  oneof UpsertIdentifier {
    string prospect_id = 2 [(validate.rules).string.min_len = 1];
    string actor_id = 3 [(validate.rules).string.min_len = 1];
  }
}

message UpsertClientAfIdResponse {
  // rpc status, currently it only returns StatusOK and StatusInternal
  rpc.Status status = 1;
}

message GetActorIdByVendorIdRequest {
  // vendorId corresponding to which we want to fetch actorId
  oneof VendorId {
    string amplitude_id = 1;
    string moengage_id = 2;
    string firehose_id = 3;
    string appsflyer_id = 4;
    string client_appsflyer_id = 5;
  }
}

message GetActorIdByVendorIdResponse {
  // rpc status
  rpc.Status status = 1;
  // actorId mapped to the input vendor Id
  string actor_id = 2;
}

message DPVendorMapping {
  string prospect_id = 1;
  string actor_id = 2;
  string firehose_id = 3;
  string amplitude_id = 4;
  string moengage_id = 5;
  string appsflyer_id = 6;
  string client_appsflyer_id = 7;
  bool send_to_amplitude = 8;
}
