//go:generate gen_sql -types=Feature,FiLiteDetails,FeatureDetails,FeatureInfo,PanAadharLinkageDetails,StageProcLastResponse
// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package user.onboarding;

import "api/card/card.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/inappreferrals/enums/enums.proto";
import "api/kyc/internal/kyc_attempt.proto";
import "api/kyc/kyc.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/deeplink_screen_option/onboarding/fi_lite_screen_options.proto";
import "api/typesv2/verdict.proto";
import "api/user/onboarding/internal/enums.proto";
import "api/vendorgateway/openbanking/customer/service.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/user/onboarding";
option java_package = "com.github.epifi.gamma.api.user.onboarding";

enum OnboardingStage {
  reserved 178, 179;
  ONBOARDING_STAGE_UNSPECIFIED = 0;

  // This stage allows a user to enter a finite code.
  // Currently finite code is a must have to gain access to the app, in future this will be optional and
  // will act as just referral code.
  // Whether to provide access to the app without finite code or not will be controlled by ONB team.
  REFERRAL_FINITE_CODE = 7;

  // stage to collection consents such as tnc, privacy policy etc
  TNC_CONSENT = 8;
  // Stage to check the presence of credit report of user
  // Valid States:
  // SUCCESS - If the CB report check api was executed without failure
  // FAILURE - If CB report check api failed during execution
  // IN-PROGRESS - If CB report check is being retried
  // SKIPPED - If CB report check is to be skipped. We will have a feature flag to disable CB report presence check.
  CHECK_CREDIT_REPORT_PRESENCE = 101;
  // Stage to take user consent to download credit report
  // Valid States:
  // SUCCESS - If the consent was successfully collected
  // UNSPECIFIED - If the consent was not collected yet, or there was any error in consent collection
  // SKIPPED - We will skip this stage if CB report feature flag is off, or if the user's CB report is not present.
  // Negative consent will be persisted as meta-data in AppScreeningMetaData
  CONSENT_CREDIT_REPORT_DOWNLOAD = 102;
  // Stage to Verify credit report of user
  // Valid States:
  // SUCCESS - If the CB report was successfully verified to valid or in-valid
  // IN-PROGRESS - If the CB report download and verification is in progress
  // FAILURE - If there was any api failure in either downloading or verifying the credit report
  // SKIPPED - This will be skipped if CB report feature flag is off, or if user's CB report is not present.
  // Verification status will be persisted in metadata in AppScreeningMetaData
  CREDIT_REPORT_VERIFICATION = 103;
  // Employment type declaration Stage.
  // 1. Salaried employees: This stage will go to in-progress for salaried employees.
  //    Will move to terminal status SUCCESS/FAILURE after verification is complete
  // 2. For Non-salaried employees: This stage will be made success and the user will
  //    move to Manual screening, or Reject state depending on the user flow
  // SUCCESS - If user's employment verification was done successfully
  // FAILURE - If user was non-salaried or employment details were not found in MCA database
  // SKIPPED - If user was screened through CB report check
  EMPLOYMENT_VERIFICATION = 104;
  // Stage to check the presence of credit report of user, with their PAN and PAN name.
  // Valid States:
  // SUCCESS - If the CB report check api was executed without failure
  // FAILURE - If CB report check api failed during execution
  // IN-PROGRESS - If CB report check is being retried
  // SKIPPED - If CB report check is to be skipped - based on feature flag or a previous stage success.
  CREDIT_REPORT_PRESENCE_CHECK_WITH_PAN = 115;
  // payu affluence check
  AFFLUENCE_CHECK = 161;
  // This stage checks presence of GSTIN number using pan at vendor's end
  // This stage is added after DOB_AND_PAN stage since the input required is the PAN of the user
  // This stage is considered successful and user clears app screening IF -
  // GSTIN number is found at vendor's end OR
  // API failure or retry exhaustion occurs
  // The only case when user is moved to next screening stage is when there's no API failure i.e. we get response from vendor indicating no GSTIN number found
  GSTIN_PRESENCE_CHECK = 113;
  // Linked in based verification of user
  // TBD how this verification is going to be done.
  // This stage is added here because the user choose to wait here until this feature is shipped
  // TODO(keerthana): Update this documentation
  LINKEDIN_VERIFICATION = 109;
  // Current heuristic based screening allows user to pass screening based on following
  // 1. Device model - Premium/non-Premium devices. Premium device users will be allowed to pass screener
  HEURISTIC_VERIFICATION = 112;
  // Form16 check
  // If the user's tax was submitted by their employer, we would allow them
  FORM16_CHECK = 118;
  // Mandate CB consent
  // Valid States:
  // SUCCESS - If the consent was successfully collected
  // UNSPECIFIED - If the consent was not collected yet, or there was any error in consent collection
  // SKIPPED - Stage is skipped if CB report feature flag is off, or CB report verification was already done.
  // Negative consent will be persisted as meta-data in AppScreeningMetaData
  MANDATE_CONSENT_CREDIT_REPORT = 105;
  // Gmail based verification is alternate way to filter user for app screening.
  // The state of this stage only represents if verification was carried out successfully or not.
  // Even if the stage is successful its possible that the user was rejected due to low threshold.
  // Valid states:
  // SUCCESS - Gmail verification was successfully performed for the user
  // FAILED - Gmail verification failed for the user
  // IN-PROGRESS - Gmail verification is in progress for the user
  // SKIPPED - Gmail verification was skipped for them. This is possible if the app-screening was done for the user
  GMAIL_VERIFICATION = 108;
  // work email based OTP verification
  // user will be subjected to this stage only if the user is
  WORK_EMAIL_VERIFICATION = 111;
  // This stage checks presence of UAN number using phone number at vendor's end
  // This stage is added immediately after credit report check to establish salaried history
  // This stage is considered successful and user clears app screening IF -
  // UAN number is found at vendor's end
  // The user is moved to next stage in cases of:
  // API failures, retries reached or UAN number not found for the phone number
  UAN_PRESENCE_CHECK = 114;
  // This stage runs screener check based on income estimation by in-house DS model
  INCOME_ESTIMATE_CHECK = 119;
  // Manual Screening stage
  // Valid States:
  // SUCCESS - Manually accepting user after app screening. User is allowed to proceed with onboarding
  // FAILED - Manually rejecting user after app screening. User is not allowed to proceed with onboarding
  // SKIPPED - This will be skipped if user is screened already through CB report or employment verification
  // INPROGRESS - user is waiting to be screened manually.
  // Reason for manual allowing or rejecting of user is persisted in AppScreeningMetaData
  MANUAL_SCREENING = 106;
  // Parent stage for all stages in app screening.
  // The overall status of app screening will be maintained at this stage.
  // Valid States:
  // SUCCESS - user accepted after app screening. User is allowed to proceed with onboarding
  // FAILED - user rejected app screening. User is not allowed to proceed with onboarding
  // SKIPPED - user was not subjected to screening. Currently, all the pre-waitlisted users and some special users can skip screening.
  APP_SCREENING = 107;

  // Stage for company search in screener 2.0
  EPFO_COMPANY_SEARCH = 116;

  // Stage for connected accounts screener check
  CONNECTED_ACCOUNTS = 117;

  // Dob and pan of user are collected along with KYC initialisation
  DOB_AND_PAN = 12;
  // Stage represents check to validate if user is already a federal user
  DEDUPE_CHECK = 17;
  // Initiate ckyc
  INITIATE_CKYC = 20;
  // Mother father name collection
  MOTHER_FATHER_NAME = 23;
  // Currently KYC_AND_LIVENESS_COMPLETION stage takes care of CKYC, EKYC and Liveness flow of the user.
  // Breaking the above stages into multiple stages to improve communication for stuck users at different stages in the above flow.
  CKYC = 25;
  EKYC = 26;
  LIVENESS = 27;

  // Stage represents CKYC, EKYC & Liveness Facematch steps
  KYC_AND_LIVENESS_COMPLETION = 30;
  // KYC gets expired after x days. This stage ensures that if
  // KYC is expired before customer creation, this stage can be re initiated. The steps to redo KYC
  // are similar to initial KYC but not the same. It's marked success only
  // after CUSTOMER_CREATION is done. Once bank customer is created, we
  // no longer need to ensure KYC availability.
  ENSURE_KYC_AVAILABILITY = 31;
  // Ensure PAN name and KYC name match
  PAN_NAME_CHECK = 32;
  // RISK_SCREENING is a backend stage that does risk analysis of the user and blocks them if fraud probability
  // is high otherwise passes them through. Currently using KYC address Pincode as a signal.
  // New signals can be used in future.
  RISK_SCREENING = 33;
  // Once kyc is done, we need to update some parameters like
  // kyc level, user image from liveness, and user legal name in user service.
  // There are some additional steps performed in customer creation, moving those here as dedupe customers will not have
  // customer creation flow.
  UPDATE_CUSTOMER_DETAILS = 35;
  // Validates customer's name in United Nations sanctions list.
  // If the customer name is sanctioned, the customer is blocked
  // from onboarding until the KYC details are manually verified.
  UN_NAME_CHECK = 40;
  // This stage calls dedupe for the user with kyc details
  KYC_DEDUPE_CHECK = 45;
  // This stage calls partner bank to match Name and DOB received in EKYC response.
  // The stage is skipped if the user is CKYC.
  EKYC_NAME_DOB_VALIDATION = 47;
  // This stage calls partner bank to match Name and DOB received in BKYC or EKYC response.
  // The stage is skipped if the user is CKYC.
  // This is being added to also allow the validation for BKYC users.
  KYC_NAME_DOB_VALIDATION = 48;
  // Stage represents confirming card mailing address from user
  CONFIRM_CARD_MAILING_ADDRESS = 50;
  // Stage represents upi consent collection from user
  UPI_CONSENT = 55;
  // This stage does not allow the user to move ahead IF -
  // The pan entered by the user is used by some other user and this other user is IN or PAST customer creation stage
  PAN_UNIQUENESS_CHECK = 58;
  // Stage represents device registration of the user device with the vendor bank
  DEVICE_REGISTRATION = 60;
  // Stage represents dedupe check as caution mechanism, right before the customer creation
  PRE_CUSTOMER_CREATION_DEDUPE_CHECK = 75;
  // Stage to do precautionary checks before initiating custom creation so that users don't get stuck on it
  PRE_CUSTOMER_CREATION_CHECK = 76;
  // Stage represents customer creation for the user with vendor bank
  CUSTOMER_CREATION = 80;
  // Stage represents account creation of user with vendor bank
  ACCOUNT_CREATION = 100;
  // Stage represents updating shipping address at vendor bank
  SHIPPING_ADDRESS_UPDATE = 110;
  // Stage represents card creation for the user
  CARD_CREATION = 120;
  // Makes UN NAME CHECK API call to the partner bank. If the name
  // is in sanctioned list, partner bank is notified for manual review.
  UN_NAME_CHECK_NOTIFIER = 125;
  // Stage represents upi id creation
  UPI_SETUP = 140;
  // Set Debit Card PIN
  DEBIT_CARD_PIN_SETUP = 145;
  // VKYC step
  VKYC = 147;
  // Add money/funds to the savings account
  ADD_MONEY = 150;
  // optional vkyc step
  OPTIONAL_VKYC = 151;
  // onboarding completed
  ONBOARDING_COMPLETE = 160;

  // This stage checks if a user is located outside india and blocks if so.
  // Currently works on location based on ip address. If no address was found for an ip, we let the user through.
  LOCATION_CHECK = 162;

  // users whitelisted by agent have to gone through this stage
  BKYC = 163;

  // This stage runs risk initial risk checks for user post TnC stage to allow access to fi-lite features.
  FI_LITE_RISK_SCREENING = 164;

  // This stage decides which onboarding journey to put a user through based on intent
  INTENT_SELECTION = 165;

  // This stage polls the next action from firefly service once the orchestration is handed to CC in case of CC onboarding
  CREDIT_CARD_ONBOARDING_STATUS_CHECK = 166;

  // this stage shows savings account introduction screen and collects necessary consent to open and operate a savings account
  SAVINGS_INTRO_CONSENT = 167;

  // stage for PL onboarding status
  PL_ONBOARDING_STATUS_CHECK = 168;

  // Stage for ITR intimation verification - it is a part of app screener
  // This stage fetches income and other basic details of a user from the uploaded ITR intimation
  // It then verifies these details and decides whether to pass the stage on basis of some validations and cutoffs
  ITR_INTIMATION_VERIFICATION = 169;

  // This stage checks the credit report of users and blocks onboarding if any check fails - example, credit score below certain threshold
  CREDIT_REPORT_CHECK = 170;

  // Stage to check if onboarding number matches with the number linked in aadhar
  AADHAR_MOBILE_VALIDATION = 171;

  // Stage for profile update at vendor
  // This stage updates the occupation for dedupe users at the vendor if the user's occupation at the vendor is listed as 'OTHERS', at time of implementation.
  UPDATE_PROFILE_DETAILS = 172;

  // Stage to check lendability of a user
  // Part of app screening
  LENDABILITY_CHECK = 173;

  // Stage to verify user passport
  // Currently applicable for NR onboarding
  PASSPORT_VERIFICATION = 174;

  // Stage to verify user VISA
  // Currently applicable for NR onboarding
  VISA_VERIFICATION = 175;

  // Stage to verify user's country ID
  // Currently applicable for NR onboarding
  COUNTRY_ID_VERIFICATION = 176;

  // Cross Data validation stage for NR onboarding
  NON_RESIDENT_ONBOARDING_CROSS_DATA_VALIDATION = 177;

  // This stage collects user's interest in various products and features to personalise the in-app experience
  SOFT_INTENT_SELECTION = 180;

  // This stage collects the communication address during onboarding flow.
  COMMUNICATION_ADDRESS = 181;

  // This stage trigger the NRO account creation if required
  NRO_ACCOUNT_CREATION = 182;

  // This stage trigger order physical debit card flow based on user's intent
  ORDER_PHYSICAL_CARD = 183;

  // This stage is for users who are allowed to open account with AMB (regular tier account) after failing screener checks
  OPEN_MIN_BALANCE_ACCOUNT = 184;

  // This stage collect consent to process user's SMS data and start sms parsing asynchronously
  SMS_PARSER_CONSENT = 185;

  // Stage for initiating credit report fetch
  INITIATE_CREDIT_REPORT_FETCH = 186;

  // stage for Wealth onboarding status check
  WEALTH_ANALYSER_ONBOARDING_STATUS_CHECK = 187;

  // stage for sms parser data verification
  SMS_PARSER_DATA_VERIFICATION = 188;

  // screener stage for app heuristics data verification
  INSTALLED_APPS_CHECK = 189;

  // stage for pre account creation add money (aka prefunding)
  PRE_ACCOUNT_CREATION_ADD_MONEY = 190;

  // stage to collect additional profile details for federal loans onboarding flow.
  COLLECT_ADDITIONAL_PROFILE_DETAILS_FOR_FEDERAL_LOANS = 191;

  // stage to collect permission from the user during onboarding flow.
  PERMISSION = 192;

  // This stage waits for SMS and Credit Report fetch to complete
  WAIT_FOR_AUTO_PAN = 193;

  FORM_60 = 194;

  // Stage for connected accounts for wealth builder users
  WEALTH_BUILDER_CONNECTED_ACCOUNTS_FLOW = 195;

  // Stage to make wealth builder onboarding as completed.
  WEALTH_ANALYSER_ONBOARDING_COMPLETE = 196;

  // Stage to collect declarations for savings account onboarding
  SA_DECLARATION = 197;
}

enum OnboardingState {
  UNSPECIFIED = 0;
  // INITIATED - State represents a stage (customer creation, account creation) is registered with epifi server
  // and message is enqueued
  INITIATED = 1;
  // INPROGRESS - State represents a stage is attempted at least once.
  INPROGRESS = 2;
  // FAILURE - State represents a stage is failed after attempting a vendor request.
  FAILURE = 3;
  // SUCCESS - State represents a stage was successful
  SUCCESS = 4;
  // MANUAL_INTERVENTION - State represents an internal error occurred during processing of stage that requires manual intervention
  MANUAL_INTERVENTION = 5;
  // SKIPPED - State represents a stage was skipped
  SKIPPED = 6;
  // When a stage is forcefully reset after going into a terminal state.
  // the stage can be retried if it's in this state.
  RESET = 7;
}

enum UNNameCheckStatus {
  UNNC_UNSPECIFIED = 0;
  // Name not in sanctions list. user safe to proceed.
  UNNC_PASSED = 1;
  // Name found in Name check sanctions list.
  // User needs to be scrutinised.
  UNNC_NAME_IN_SANCTIONS_LIST = 2;
}

message OnboardingDetails {
  // Actor id of the user
  string actor_id = 1;
  // Details regarding the states of stage, current stage are maintained in here
  StageDetails stage_details = 2;
  // Parameter dump of all required parameter related to user account
  AccountInformationInternal account_info = 3;
  // Parameter dump of all required parameter related to customer card
  CardInformationInternal card_info = 4;
  // Bank vendor with whom the user holds the account
  vendorgateway.Vendor vendor = 5;
  // onboarding id
  string onboarding_id = 6;
  // User id
  string user_id = 7;
  // completed at to denote when onboarding was complete
  google.protobuf.Timestamp completed_at = 8;
  // This will contain all the detailed information of different stage.
  // TODO (keerthana): account_info, and card_info to be migrated to this json.
  StageMetadata stage_metadata = 9;
  // created_at to denote when onboarding was started
  google.protobuf.Timestamp created_at = 10;
  // current onboarding stage of the user
  user.onboarding.OnboardingStage current_onboarding_stage = 11;

  Feature feature = 12;

  FiLiteDetails fi_lite_details = 13;

  FeatureDetails feature_details = 14;
  // deleted_at to denote when onboarding record was deleted
  google.protobuf.Timestamp deleted_at = 15;

  PanAadharLinkageDetails pan_aadhar_linkage_details = 16;

  // Represents the information about response (action/error) returned by a stage processor in last execution of orchestrator
  StageProcLastResponse stage_proc_last_response = 17;
}

message AccountInformationInternal {
  // Account number of the savings account
  string account_number = 1;
  // Account id of savings account
  string account_id = 2;
  // denotes if the account is primary or not
  bool is_primary = 3;
}

message CardInformationInternal {
  // multiple cards can be issued to the user
  repeated SingleCardInfo card_details = 1;
}

message SingleCardInfo {
  // Card id of the user debit card
  string card_id = 1;
  // Card information of the user debit card
  card.BasicCardInfo basic_card_info = 2;
}

message StageDetails {
  // Details of each stage in onboarding process
  // This will contain the history of all the stages - customer creation, account creation, card creation etc.
  // The key for the map is Stage.string()
  map<string, StageInfo> stage_mapping = 1;
}

message KYCMetadata {
  // Timestamp at which the kyc of user expires,
  // and if the user's kyc expires before customer creation, we will have to do kyc again.
  google.protobuf.Timestamp expiry_at = 1;

  // Cache last updated value of KYC level in user service.
  // Optimises unnecessary update user API calls.
  kyc.KYCLevel last_updated_kyc_level = 2;

  // Cache the error received for KYC.
  // Optimises unnecessary calls for checking KYC failure reasons and sending specific communication for those failures.
  kyc.FailureType failure_type = 3;

  // Cache the error received for CKYC.
  // Storing this explicitly to avoid value change due to ekyc failures
  kyc.FailureType ckyc_failure_type = 4;
}

// Message contains required information of a single stage
message StageInfo {
  // The state of current stage - Initiated, InProgress, Success etc.
  OnboardingState state = 1;
  // The timestamp at which the stage was updated with the state.
  // This information will be useful for debugging in case of failures,
  // and to analyze which stages have taken longer to completion etc.
  google.protobuf.Timestamp last_updated_at = 2;
  // The timestamp at which the stage was deemed to be started.
  google.protobuf.Timestamp started_at = 3;
  // obfuscated GPS coordinates location identifier.
  // Since location identifier is a sensitive user information, it's important
  // for us to restrict the exposure keeping user's privacy in mind.
  string location_token = 4;
}

message StageProcLastResponse {
  // Screen name returned by the stage processor
  string screen_name = 1;
  // Stage from which the response was last returned
  user.onboarding.OnboardingStage stage = 2;
  // A map of analytics properties for the server events
  // key is the event properties name and
  // value is the event properties value
  // Note: Do not send PII in this field.
  map<string, string> analytics_event_properties = 3;
  // error string return by the stage processor
  string error = 4;
}

// FieldMask for helping db updated in onboarding_details table
enum OnboardingDetailsFieldMask {
  ACCOUNT_INFO = 0;
  CARD_INFO = 1;
  VENDOR = 2;
  COMPLETED_AT = 3;
  STAGE_METADATA = 4;
  CURRENT_ONBOARDING_STAGE = 5;
  FEATURE = 6;
  FI_LITE_DETAILS = 7;
  FEATURE_DETAILS = 8;
  PAN_AADHAR_LINKAGE_DETAILS = 9;
  STAGE_PROC_LAST_RESPONSE = 10;
}

message PANNameCheck {
  // Inhouse DS name match API decision & score
  bool inhouse_name_match_passed = 1;
  float inhouse_name_match_score = 2;

  // Old(Already being used) name match API decision & score
  bool old_name_match_passed = 3;
  float old_name_match_score = 4;

  string inhouse_name_match_feature_dict = 5 [deprecated = true];
  int32 inhouse_name_match_risk_match = 6;
}

message StageMetadata {
  // kyc Stage specific details
  KYCMetadata kyc_metadata = 1;
  vendorgateway.openbanking.customer.DedupeStatus dedupe_status = 2;
  vendorgateway.openbanking.customer.DedupeStatus kyc_dedupe_status = 3;
  int32 kyc_dedupe_retry_count = 4;
  PANNameCheck pan_name_check = 5;
  AppScreeningMetaData app_screening_data = 6;
  DebitCardNameCheck debit_card_name_check = 7;
  EKYCNameDOBValidationData ekyc_name_dob_validation = 8;
  PanValidation pan_validation = 9;
  UNNameCheckStatus u_n_name_check_status = 10 [deprecated = true];
  vendorgateway.openbanking.customer.DedupeStatus pre_customer_creation_dedupe_status = 11;
  PanManualReviewAnnotation pan_manual_review_annotation = 12;
  VKYCMetadata vkyc_metadata = 13;
  float gmail_pan_name_match_score = 14;
  RiskScreeningMetadata risk_screening_metadata = 15;
  int32 dedupe_dob_retry_count = 16;
  // We perform dedupe checks for a user multiple times during their onboarding in DEDUPE_CHECK, KYC_DEDUPE_CHECK & PRE_CUSTOMER_CREATION_CHECK stages.
  // This field stores the dedupe status from the latest dedupe status fetched from the vendor.
  vendorgateway.openbanking.customer.DedupeStatus latest_dedupe_status = 17;
  LivenessMetadata liveness_metadata = 18;
  kyc.UNNameCheckStatus u_n_name_check_status_v2 = 19;
  IntentSelectionMetadata intent_selection_metadata = 20;
  PreCustomerCreationMetadata pre_customer_creation_metadata = 21;
  UpdateProfileDetailsMetadata update_profile_details_metadata = 22;
  SoftIntentSelectionMetadata soft_intent_selection_metadata = 23;
  PassportVerificationMetadata passport_verification_metadata = 24;
  CountryIdVerificationMetadata country_id_verification_metadata = 25;
  CrossValidationResult non_resident_cross_validation_result = 26;
  // when user failed screener checks but was allowed to open account with AMB (regular tier account)
  bool user_allowed_to_open_amb_account = 27;
  CreditReportFetchMetadata credit_report_fetch_metadata = 28;
  PreAccountCreationAddMoneyMetadata pre_account_creation_add_money_metadata = 29;
  PermissionMetaData permission_metadata = 30;
  AddMoneyMetadata add_money_metadata = 31;
}

message PermissionMetaData {
  // Indicates whether the client has acknowledged granting the required permission.
  // If true, the PERMISSION stage is marked as successful.
  bool permission_ack_received = 1;
}

message AddMoneyMetadata {
  // only one of skip reason / success reason will be populated
  string skip_reason = 1;
  string success_reason = 2;
}

message PreAccountCreationAddMoneyMetadata {
  // deprecated in favour of success/skip reason
  bool skipped_after_savings_acc_creation = 1 [deprecated = true];
  // marked if add money order is marked as SUCCESS
  // deprecated in favour of success/skip reason
  bool skipped_after_successful_add_money = 2 [deprecated = true];
  // only one of skip reason / success reason will be populated
  string skip_reason = 3;
  string success_reason = 4;
}

// During SA onboarding, there can be multiple attempts to fetch credit report of a user - with PAN and without PAN.
// This message contains the client request ids for both the attempts.
message CreditReportFetchMetadata {
  string client_req_id_fetch_without_pan = 1;
  string client_req_id_fetch_with_pan = 2;
}

message PreCustomerCreationMetadata {
  enum FailureReason {
    FAILURE_REASON_UNSPECIFIED = 0;
    FAILURE_REASON_PAN_UNIQUENESS_CHECK = 1;
  }

  FailureReason failure_reason = 1;
}

message UpdateProfileDetailsMetadata {
  // identifier for profile update request, with mapping in Temporal workflow
  string request_id = 1;
}

message LivenessMetadata {
  // Latest liveness summary requestID for the actor
  string request_id = 1;
}

message RiskScreeningMetadata {
  // deprecated: use risk data (risk svc) instead
  RiskFactor risk_blocking_reason = 1 [deprecated = true];
  // deprecated: use risk data (risk svc) instead
  RiskFactor forced_manual_review_reason = 2 [deprecated = true];
  // client request id for the risk service request
  string client_request_id = 3;
}

enum RiskFactor {
  RISK_FACTOR_UNSPECIFIED = 0;
  RISK_FACTOR_RED_LISTED_CONTACT = 1;
  RISK_FACTOR_KYC_PIN_CODE = 2; // User's KYC address pin code is in block list
  RISK_FACTOR_LOCATION_PIN_CODE = 3; // User's device location pin code is in block list
  RISK_FACTOR_LOCATION_CO_ORDINATE = 4; // User's device location co-ordinate is in block list
  RISK_FACTOR_EKYC_ONBOARDING_NUMBER_MISMATCH = 5;
  RISK_FACTOR_HIGH_VELOCITY = 6;
  RISK_FACTOR_DEVICE_BLOCKED_LIST = 7; // User's device model is in forced manual review list
  RISK_FACTOR_ONBOARDING_RISK_MODEL = 8; // DS Risk service marked the user as risky
  RISK_FACTOR_EMPTY_LOCATION_PIN_CODE = 9; // User's pin code is empty when fetched via geolocation
  RISK_FACTOR_EPFO_USER = 10; // User has passed screener through EPFO and Gmail-PAN name match failed
}

message PanManualReviewAnnotation {
  Verdict verdict = 1;
  string reviewed_by = 2;
  google.protobuf.Timestamp reviewed_on = 3;
  repeated PanNameReviewCategory pan_name_review_category = 4;
  string remarks = 5;
}

enum PanNameReviewCategory {
  PAN_NAME_REVIEW_CATEGORY_UNSPECIFIED = 0;
  // Full name mismatch ex -> Rajat and Niket Goel
  PAN_NAME_REVIEW_CATEGORY_FULL_NAME_MISMATCH = 1;
  // ex Rajat Kumar and Rahul Kumar
  PAN_NAME_REVIEW_CATEGORY_FIRST_NAME_MISMATCH = 2;
  // ex Arun Kumar and Arun singh
  PAN_NAME_REVIEW_CATEGORY_LAST_NAME_MISMATCH = 3;
  // ex Arun Kumar Choudhary and  Arun Choudhary
  PAN_NAME_REVIEW_CATEGORY_COMMON_MIDDLE_NAME_MISSING = 4;
  // ex Rajat Choudhary Vikas Singh and Rajat Choudri Vikash Singh
  PAN_NAME_REVIEW_CATEGORY_SAME_PRONUNCIATION = 5;
  // ex Lalit Mohan Singh and Lalit Mohan Singha
  PAN_NAME_REVIEW_CATEGORY_ALTERNATIVE_PRONUNCIAT_COMMON_VARIATION = 6;
  // ex SEKH MAHAMMAD and Sk Md Asif
  PAN_NAME_REVIEW_CATEGORY_PRIMARY_IDENTIFIER_MISSING = 7;
  // ex Niket T Goel and Niket A Goel
  PAN_NAME_REVIEW_CATEGORY_MIDDLE_NAME_MISMATCH = 8;
  // ex Rahul Kumar Manish Kumar Singh and Kumar Rahul Manish Singh Kumar
  PAN_NAME_REVIEW_CATEGORY_JUMBLED_NAMES = 9;
  // ex SHAHRUKH SAIFI and Sharukh
  PAN_NAME_REVIEW_CATEGORY_PRIMARY_IDENTIFIER_MATCHES = 10;
  PAN_NAME_REVIEW_CATEGORY_COMMON_ABBREVIATIONS = 11;
  PAN_NAME_REVIEW_CATEGORY_FULL_NAME_MATCH = 12;
  // Remark will contain detail.
  PAN_NAME_REVIEW_CATEGORY_OTHER_MISMATCH = 13;
}

enum Verdict {
  VERDICT_UNSPECIFIED = 0;
  VERDICT_PASS = 1;
  VERDICT_FAIL = 2;
}

message DebitCardNameCheck {
  // Inhouse DS name match API decision & score
  bool inhouse_name_match_passed = 1;
  float inhouse_name_match_score = 2;

  // Old(Already being used) name match API decision & score
  bool old_name_match_passed = 3;
  float old_name_match_score = 4;
  // Number of retries for name check.
  // User will be redirected to error screen if name check fails
  // for more than a given threshold.
  int32 name_check_retry_count = 5;
}

// Following meta data needs to be persisted in order to avoid repeated fetching
// of statuses of validation from domain services - Employment verification and credit report verification services
message AppScreeningMetaData {
  // boolean to represent whether credit report was found
  api.typesv2.common.BooleanEnum credit_report_found = 1;
  // boolean to represent whether pan based credit report presence check has been attempted
  // this essentially represents if CREDIT_REPORT_PRESENCE_CHECK_WITH_PAN has completed
  api.typesv2.common.BooleanEnum pan_credit_report_check_attempted = 23;
  // boolean to represent whether consent was given by user to download credit report
  // 1. True being positive consent
  // 2. False being negative consent
  // 3. Unspecified if consent was not yet taken
  api.typesv2.common.BooleanEnum consent_credit_report_download = 2;
  // boolean to represent if credit report verification passed for the user
  api.typesv2.common.BooleanEnum credit_report_verification_passed = 3;
  // boolean to represent if employment verification passed for the user
  api.typesv2.common.BooleanEnum employment_verification_passed = 4;
  // reason to allow/reject manual screening
  string manual_screening_reason = 5;
  // screening skip reason as enum
  ScreeningSkipReason skip_reason = 6;
  // reason to skip credit report screening stages
  CreditReportScreeningSkipReason credit_report_flow_skip_reason = 7;
  // reason why `employment_verification_passed` field is marked as TRUE
  EmploymentVerificationSuccessReason employment_verification_success_reason = 8;
  // reason to mark credit report verification stage as success
  CreditReportVerificationSuccessReason credit_report_verification_success_reason = 9;
  // denotes the user channel incase of finite code
  inappreferral.enums.FiniteCodeChannel finite_code_channel = 10;
  // denotes the different types of Finite Code
  inappreferral.enums.FiniteCodeType finite_code_type = 11;
  // When true, indicates that user is to be subjected to employment declaration ONLY(employment verification is to be skipped)
  api.typesv2.common.BooleanEnum employment_declaration_only = 12;
  // denotes if gmail verification threshold was crossed by the user or not
  api.typesv2.common.BooleanEnum gmail_verification_passed = 13;
  // boolean to represent if work email verification passed for the user
  api.typesv2.common.BooleanEnum work_email_verification_passed = 14;
  // reason to fail work email verification
  WorkEmailVerificationFailureReason work_email_verification_failure_reason = 15;
  // boolean to represent if LinkedIn verification passed
  api.typesv2.common.BooleanEnum linkedin_verification_passed = 16;
  // enum to represent how linkedin was passed for user
  LinkedInValidationType linkedin_validation_type = 17;
  // whether heuristic verification passed
  api.typesv2.common.BooleanEnum heuristic_screening_passed = 18;
  // boolean to represent if user gstin presence check stage was passed
  api.typesv2.common.BooleanEnum gstin_presence_check_passed = 19;
  // reason why `gstin_presence_check_passed` field is marked as TRUE
  GSTINPresenceCheckSuccessReason gstin_presence_check_success_reason = 20;
  // whether user succeeded screening with experimental bypass
  api.typesv2.common.BooleanEnum experimental_screening_bypass = 21;
  // metadata to denote if screening passed
  api.typesv2.common.BooleanEnum screening_passed = 22;
  // Heuristic screener pass reason
  HeuristicPassReason heuristic_screening_pass_reason = 24;
  // boolean to represent if form16 check passed or not
  api.typesv2.common.BooleanEnum form16_check_passed = 25;
  // boolean to represent if uan presence check passed or not
  api.typesv2.common.BooleanEnum uan_presence_check_passed = 26;
  // boolean to represent if affluence check of user passed
  api.typesv2.common.BooleanEnum affluence_check_passed = 27;
  // affluence score of the user with payu
  int32 affluence_score = 28;

  api.typesv2.common.BooleanEnum credit_report_pan_matched = 29;
}

// EKYCNameDOBValidationData stores info related to EKYC_NAME_DOB_VALIDATION stage
message EKYCNameDOBValidationData {
  // API retries for tracking errored vendor API responses
  int32 retries = 1;
  // Validation failure description
  string failure_desc = 2;
  // API raw response
  string raw_response = 3;
}

message PanValidation {
  // block_till denotes the time till which the user will not be allowed to do ckyc
  google.protobuf.Timestamp block_till = 3;
  PanValidationSuspendReason pan_validation_suspend_reason = 4;
}

message VKYCMetadata {
  // Since we have a stateless probabilistic way to show vkyc option to user, this can be set to true to persist that info once vkyc is shown to user
  bool perform_vkyc_check = 1;
  // vkyc option used, current flow could be due to plain onboarding or for a ckyc L, S or O user
  VKYCOption vkyc_option = 2;
  InhouseVKYC inhouse_v_k_y_c = 3;
}

message InhouseVKYC {
  // application id is for nr vkyc, used to get applicant details
  string application_id = 1;
  // application id is for nr vkyc, used to check call status
  string call_id = 2;
}

message IntentSelectionMetadata {
  // intent selected by user or the intent of the user identified through acquisition channel
  onboarding.OnboardingIntent selection = 1;
  // list of intent choices that were showed to the user,
  // this will be empty in case intent was identified by acquisition channel
  repeated onboarding.OnboardingIntent choices = 2;
  // weather intent was received through acquisition channel
  bool auto_intent = 3;
}

message SoftIntentSelectionMetadata {
  // soft intents selected by user
  repeated onboarding.OnboardingSoftIntent selection = 1;

  // request id of the credit report download process
  string credit_report_req_id = 2;
}

// reason why the in-app screening was skipped for the user during onboarding
enum ScreeningSkipReason {
  SCREENING_REASON_UNSPECIFIED = 0;
  // feature flag is turned off
  FEATURE_FLAG_OFF = 1;
  // platform and app version number check fails
  APP_VERSION_CHECK_FAILED = 2;
  // a wait-listed user has already gone through screening
  WL_USER = 3;
  // Special PRIVILEGE in the entered FINITE CODE
  FINITE_CODE_PRIVILEGE = 4;
  // Non wait-listed old user. The user started onboarding even before we introduced waitlist as a feature.
  NON_WL_OLD_USER = 5;
  // B2B Salary program user is whitelisted by manual screening
  B2B_SALARY_PROGRAM_USER = 6;
  // users in DC acquisition user group are whitelisted
  DEBIT_CARD_PARTNER_ACQUISITION_USER = 7;
  // user didn't succeed screening and opted for min balance account
  USER_OPTED_FOR_MIN_BALANCE_ACCOUNT = 8;
}

// reason why credit report flow was skipped from in-app screening for the user during onboarding
enum CreditReportScreeningSkipReason {
  SKIP_REASON_UNSPECIFIED = 0;
  // credit report flow flag is turned off
  SKIP_REASON_FLAG_OFF = 1;
  // credit report screening is skipped if presence check times out
  SKIP_REASON_PRESENCE_CHECK_API_MAX_RETRIES = 2;
  // credit report screening is skipped between defined time interval conveyed by vendor
  SKIP_REASON_VENDOR_API_DOWNTIME = 3;
}

enum EmploymentVerificationSuccessReason {
  EMPLOYMENT_VERIFICATION_SUCCESS_REASON_UNSPECIFIED = 0;
  // we were able to successfully verify epf data
  EPF_DATA_VERIFIED = 1;
  // we were able to successfully verify domain name in personal profile link
  DOMAIN_NAME_VERIFIED = 2;
  // employment verification consumer process timed out.
  VERIFICATION_PROCESS_TIMED_OUT = 3;
  // company selected by the user is present in MCA DB but is not registered with epfo
  PASS_NON_EPFO_REG_COMPANY = 4;
  // employment verification is marked successful if user is a referee
  ACCEPT_INAPP_REFEREE = 5;
  // employment verification is marked successful when user is subjected to employee declaration only - to collect employment data
  EMPLOYMENT_DECLARATION_ONLY = 6;
  // employment verification is marked successful for iOS users based on ActionOnEmploymentVerificationFailForIOS config flag
  ACCEPT_IOS_USER = 7;
  // employment verification is marked successful for Android users based on ActionOnEmploymentVerificationFailForAndroid config flag
  ACCEPT_ANDROID_PROD_MOVEMENT = 8;
}

enum WorkEmailVerificationFailureReason {
  WORK_EMAIL_VERIFICATION_FAILURE_REASON_UNSPECIFIED = 0;
  // denotes attempts to send otp to email are exhausted
  WORK_EMAIL_VERIFICATION_FAILURE_REASON_SEND_OTP_ATTEMPTS_EXHAUSTED = 1;
  // denotes attempts to verify otp are exhausted
  WORK_EMAIL_VERIFICATION_FAILURE_REASON_VERIFY_OTP_ATTEMPTS_EXHAUSTED = 2;
  // denotes invalid email domain entered by the user
  WORK_EMAIL_VERIFICATION_FAILURE_REASON_INVALID_EMAIL_DOMAIN = 3;
  // denotes record with provided CIN not found at vendor
  WORK_EMAIL_VERIFICATION_FAILURE_REASON_COMPANY_EMAIL_NOT_FOUND = 4;
  // denotes failure due to vendor api attempts exhaustion
  WORK_EMAIL_VERIFICATION_FAILURE_REASON_VENDOR_API_ATTEMPTS_EXHAUSTED = 5;
}

// reason why credit report verification was marked success
enum CreditReportVerificationSuccessReason {
  CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_UNSPECIFIED = 0;
  // cb verification stage is success if report verification retries exhausts
  CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_API_MAX_RETRIES = 1;
  // cb verification stage is success if vendor responds with report not found on attempt to download report
  CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_REPORT_NOT_FOUND = 2;
  // cb verification stage is success if report was downloaded and verified successfully
  CREDIT_REPORT_VERIFICACTION_SUCCESS_REASON_CREDIT_REPORT_VERIFIED = 3;
}

enum PanValidationSuspendReason {
  PAN_VALIDATION_SUSPEND_REASON_UNSPECIFIED = 0;
  // denotes when a user is blocked in pan validation stage due to max retries by user
  PAN_VALIDATION_SUSPEND_REASON_USER_MAX_RETRIES = 1;
  // denotes when a user is blocked in pan validation stage due to vendor rate limiting
  PAN_VALIDATION_SUSPEND_REASON_VENDOR_RATE_LIMIT_REACHED = 2;
}

// Linkedin Verification is expected to evolve over time - vendor might change, validation condition might change
// This enum represents roughly what validation condition was used to pass or reject user
enum LinkedInValidationType {
  LINKEDIN_VALIDATION_TYPE_UNSPECIFIED = 0;
  // This validation include user's name match and company name match with LinkedIn profile
  LINKEDIN_VALIDATION_SEON_USER_NAME_COMPANY_NAME_MATCH = 1;
}


enum GSTINPresenceCheckSuccessReason {
  GSTIN_PRESENCE_CHECK_SUCCESS_REASON_UNSPECIFIED = 0;
  // user gstin found at vendor
  GSTIN_PRESENCE_CHECK_SUCCESS_REASON_GSTIN_FOUND = 1;
  // api failure
  GSTIN_PRESENCE_CHECK_SUCCESS_REASON_API_FAILURE = 2;
  // retry exhausted
  GSTIN_PRESENCE_CHECK_SUCCESS_REASON_RETRIES_EXHAUSTED = 3;
}

enum VKYCOption {
  VKYC_OPTION_UNSPECIFIED = 0;
  // user subjected to vkyc during onboarding due to probability enabling
  VKYC_OPTION_ONBOARDING = 1;
  // user subjected to vkyc during onboarding due to having L, S or O as prefix in ckyc number
  VKYC_OPTION_LSO = 2;
  // user subjected to vkyc during onboarding due to having CKYC O as prefix in ckyc number
  VKYC_OPTION_CKYC_O = 3;
  // user subjected to vkyc during onboarding by force due to employment type student.
  VKYC_OPTION_STUDENT = 4;
  // Partial KYC dedupe user subjected to VKYC
  VKYC_OPTION_PARTIAL_KYC_DEDUPE = 5;
  // Users with EKYC & Onboarding Number Mismatch
  VKYC_OPTION_EKYC_NUMBER_MISMATCH = 6;
  // Users with Affluence Score 5 and from Tier 3 cities
  VKYC_OPTION_LOW_QUALITY_USERS = 7;
  // Fi lite users
  VKYC_OPTION_FI_LITE_USERS = 8;
  VKYC_OPTION_CLOSED_ACCOUNT_REOPENING = 9;
  // Fi lite users continuing CC onboarding
  VKYC_OPTION_FI_LITE_CC_USERS = 10;
  VKYC_OPTION_NON_RESIDENT_ONBOARDING = 11;
  VKYC_OPTION_FEDERAL_LOANS = 12;
}

enum HeuristicPassReason {
  HEURISTIC_PASS_REASON_UNSPECIFIED = 0;
  // User was passed screener based on their device model and manufacturer
  HEURISTIC_PASS_REASON_DEVICE_MODEL_CHECK = 1;
  // User was passed based on their age
  HEURISTIC_PASS_REASON_USER_AGE = 2;
  // User was passed screener based on their phone number billing type
  HEURISTIC_PASS_REASON_PHONE_BILLING_TYPE = 3;
}

// Feature names of various onboarding journeys
enum Feature {
  FEATURE_UNSPECIFIED = 0;
  FEATURE_SA = 1; // Savings Account
  FEATURE_FI_LITE = 2;
  FEATURE_CC = 3; // Credit Card
  FEATURE_PL = 4; // Personal Loans
  FEATURE_UPI_TPAP = 5;
  FEATURE_NON_RESIDENT_SA = 6; // Savings Account for NRI
  FEATURE_NON_RESIDENT_SA_US = 7; // Savings Account for US NRI
  FEATURE_WEALTH_ANALYSER = 8; // Wealth Analyser onboarding journey
  FEATURE_NON_RESIDENT_SA_QATAR = 9; // Savings Account for Qatar NRI
}

// Details of Fi Lite onboarding
message FiLiteDetails {
  api.typesv2.common.BooleanEnum is_enabled = 1;
  FiLiteSource fi_lite_source = 2;
  // Timestamp when a user converted to a fi lite user, this is updated only once and is never overwritten
  google.protobuf.Timestamp accessibility_enabled_at = 3;
}

message FeatureInfo {
  user.onboarding.OnboardingStage current_stage = 1;
  google.protobuf.Timestamp completed_at = 2;
  FeatureStatus feature_status = 3;
  api.typesv2.deeplink_screen_option.onboarding.FeatureOnboardingEntryPoint feature_onboarding_entry_point = 4;
}

// Details regarding the various onboarding journey per feature
message FeatureDetails {
  map<string, FeatureInfo> feature_info = 1;
}

// Source through which Fi Lite was being accessed
enum FiLiteSource {
  FI_LITE_SOURCE_UNSPECIFIED = 0;
  FI_LITE_SOURCE_PAN_DOB = 1;
  FI_LITE_SOURCE_SCREENER = 2;
  FI_LITE_SOURCE_KYC_DEDUPE = 3;
  FI_LITE_SOURCE_DEVICE_REGISTRATION = 4;
  FI_LITE_SOURCE_EKYC_NAME_DOB_VALIDATION = 5;
  FI_LITE_SOURCE_INTENT_SELECTION = 6;
  FI_LITE_SOURCE_AFFLUENCE_CLASS_7 = 7;
  FI_LITE_SOURCE_SA_CUSTOMER_CREATION_FAILURE = 8;
  FI_LITE_SOURCE_DIRECT_TO_FI_LITE = 9;
  FI_LITE_SOURCE_STUCK_USER = 10;
  FI_LITE_SOURCE_DIRECT_TO_HOME = 11;
  // This is used when a user has dropped off from onboarding and we manually move them to Wealth Analyser
  // before calling them back on the app
  FI_LITE_SOURCE_DROPPED_OFF_USER_TO_WEALTH_ANALYSER = 12;
  // This is marked when user starts their eligibility check for loans from the intent screen
  // this is done to get the user to land on home when they come back on the app
  FI_LITE_SOURCE_LOANS_ELIGIBILITY_CHECK = 13;
  // this is used when user didn't complete their SA onboarding journey within a specified number of days
  FI_LITE_SOURCE_SA_ONBOARDING_EXPIRED = 14;
}

// FeatureStatus enum is used to denote the current status of the feature
enum FeatureStatus {
  FEATURE_STATUS_UNSPECIFIED = 0;
  // Onboarding is in progress for the feature, this shall be used when no user input is needed, and it is in progress
  FEATURE_STATUS_ONBOARDING_IN_PROGRESS = 1;
  // Failure is used when onboarding has failed in between
  FEATURE_STATUS_ONBOARDING_FAILURE = 2;
  // Active is used when onboarding is completed for a feature, and it is ready to use
  FEATURE_STATUS_ACTIVE = 3;
  // Inactive is used when onboarding is successfully complete, but the feature is unavailable for use.
  // e.g. SA got closed
  FEATURE_STATUS_INACTIVE = 4;
}

message PanAadharLinkageDetails {
  // hash of the last 4 digits of aadhar received from ekyc name dob validation API
  string ekyc_name_dob_validation_aadhar_digits_hash = 1;
  // hash of the last 4 digits of aadhar received from pan aadhar linkage API
  string pan_linked_aadhar_digits_hash = 2;
  // boolean flag to store the mismatch of the hash
  api.typesv2.common.BooleanEnum is_aadhar_digits_hash_mismatch = 3;
  // boolean flag to store the pan aadhaar link status
  api.typesv2.common.BooleanEnum pan_aadhaar_linked = 4;
  // hash of the last 2 digits of aadhar received from ekyc name dob validation API
  string ekyc_name_dob_validation_aadhar_last2_digits_hash = 5;
  // hash of the last 2 digits of aadhar received from pan aadhar linkage API
  string pan_linked_aadhaar_last2_digits_hash = 6;
}

message PassportVerificationMetadata {
  // Results of Passport OCR data being matched with passport details fetched from PSK (Passport Seva Kendra) services.
  message VerificationVendorAPIResults {
    api.typesv2.Verdict passport_number_match = 1;
    api.typesv2.Verdict user_name_match = 2;
    api.typesv2.Verdict date_of_issue_match = 3;
  }

  // stores client_request_id needed to get the passport data from DocExtraction service
  string doc_extract_client_req_id = 1;
  string passport_front_url = 2;
  string passport_back_url = 3;
  PassportVerificationStatus status = 4;
  string passport_front_doc_extract_client_req_id = 5;
  string passport_back_doc_extract_client_req_id = 6;
  google.protobuf.Timestamp attempted_at = 7;
  int64 current_attempts_in_cooldown_window = 8;
  string face_image_url = 9;
  VerificationVendorAPIResults verification_vendor_api_results = 10;
  // old passport file number is used to verify old passport details from PSK
  // in case if new passport is issued outside India
  string old_passport_file_number = 11;
  PassportVerificationFailureReason failure_reason = 12;
  // ARN of the passport issued outside india
  string passport_arn = 13;
  // Manual review annotation for the passport verification
  PassportManualReviewDetails manual_review_annotation = 14;
}

message PassportManualReviewDetails {
  Verdict verdict = 1;
  string reviewed_by = 2;
  google.protobuf.Timestamp reviewed_on = 3;
  string remarks = 4;
  // Screenshots/proof image of the PSK portal
  string review_proof_image_url = 5;
}

enum PassportVerificationStatus {
  PASSPORT_VERIFICATION_STATUS_UNSPECIFIED = 0;
  PASSPORT_VERIFICATION_STATUS_FRONT_DOWNLOADED = 1;
  PASSPORT_VERIFICATION_STATUS_BACK_DOWNLOADED = 2;
  PASSPORT_VERIFICATION_STATUS_OCR_COMPLETED = 3;
  PASSPORT_VERIFICATION_STATUS_SUCCESS = 4;
  PASSPORT_VERIFICATION_STATUS_FAILED = 5;
  // data from passport is confirmed by user
  PASSPORT_VERIFICATION_STATUS_DATA_CONFIRMED = 6;
  // The passport is under manual review.
  // This applies to cases where the passport was issued outside India.
  // Manual verification is conducted using the ARN from the Global Passport Seva Kendra website.
  PASSPORT_VERIFICATION_STATUS_MANUAL_REVIEW = 7;
}

enum PassportVerificationFailureReason {
  PASSPORT_VERIFICATION_FAILURE_REASON_UNSPECIFIED = 0;
  // passport issued outside India
  PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_ISSUED_OUTSIDE_INDIA = 1;
  // passport name mismatch with passport seva kendra data
  PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_NAME_MISMATCH_WITH_PSK_DATA = 2;
  // passport name mismatch with passport seva kendra data
  PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_NUMBER_MISMATCH_WITH_PSK_DATA = 3;
  // passport data not found in passport seva kendra
  PASSPORT_VERIFICATION_FAILURE_REASON_PASSPORT_DATA_NOT_FOUND_IN_PSK = 4;
}

message CrossValidationResult {
  CrossValidationVerdict verdict = 1;
  // Below fields are populated only if verdict is failed
  // check for which verdict has failed
  CrossValidationCheck check = 2;
  CrossValidationFailureReason failure_reason = 3;
  CrossValidationDataSource data_source_1 = 4;
  CrossValidationDataSource data_source_2 = 5;
  CrossValidationManualReviewInfo cross_validation_manual_review_info = 6;

}

message CrossValidationManualReviewInfo {
  string agent_email = 1;
  string remarks = 2;
  google.protobuf.Timestamp reviewed_at = 3;
}

message CountryIdVerificationMetadata {
  // stores client_request_id needed to get the country id (emirates id in case of UAE) data from DocExtraction service
  string doc_extract_client_req_id = 1;
}

message FeatureIntentSelectionInfo {
  // whether the user has selected the feature on hard intent screen or not
  bool selected_as_hard_intent = 1;
  // list of soft intents selected by the user and fall within the feature
  // eg - for SA feature: ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI, ONBOARDING_SOFT_INTENT_ZERO_BALANCE_SAVINGS_ACCOUNT, etc
  repeated onboarding.OnboardingSoftIntent selected_soft_intents = 2;
}

message FeatureEligibility {
  Status status = 1;
  // eligibility status of the feature
  enum Status {
    STATUS_UNSPECIFIED = 0;
    // user is eligible for the feature
    STATUS_PASSED = 1;
    // user is not eligible for the feature
    STATUS_FAILED = 2;
    // additional data is required to evaluate
    STATUS_UNKNOWN = 3;
  }

  AdditionalDataRequired additional_data_required = 2;
  // additional data required to determine eligibility
  message AdditionalDataRequired {
    // deeplink to redirect to for collecting data
    frontend.deeplink.Deeplink deeplink = 1;
  }
}

message FeatureLifecycle {
  Feature feature = 1;
  // intent selection info of the feature
  FeatureIntentSelectionInfo intent_selection_info = 2;
  // eligibility status of the feature
  FeatureEligibility eligibility_status = 4;
  // activation status of the feature
  FeatureStatus activation_status = 5;
}
