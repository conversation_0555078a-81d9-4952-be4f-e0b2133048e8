syntax = "proto3";

package api.vendors.reward;

option go_package = "github.com/epifi/gamma/api/vendors/reward";
option java_package = "com.github.epifi.gamma.api.vendors.reward";

message FulfillRewardRequest {
  string token = 1;
}

message FulfillRewardRequestPayload {
  // Unique customer identifier used between fi and vendor.
  string user_id = 1;
  // Name of the vendor (M2P)
  string vendor = 2;
  // Reward offer type (Pass offer type from reward)
  string offer_type = 3;
  // Type of reward: VOUCHER, FI_COIN
  string reward_type = 4;
  // reward value/units to be given
  double reward_value = 5;
  // id of package to be given in case of Voucher reward (it will come from minion reward)
  string reward_package_id = 6;
  // Vendor reference ID (minion reward external-id), Should be unique for each fulfillment
  string vendor_ref_id = 7;
  // string for validating the request (Pass category from reward)
  string req_validator = 8;
  string event_time = 9;
}
