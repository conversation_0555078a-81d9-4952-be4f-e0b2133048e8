syntax = "proto3";

package api.vendors.reward;

option go_package = "github.com/epifi/gamma/api/vendors/reward";
option java_package = "com.github.epifi.gamma.api.vendors.reward";

message GetRewardStatusRequest {
  string token = 1;
}

message GetRewardStatusRequestPayload {
  string user_id = 1;
  // Vendor reference ID (minion reward external-id)
  string vendor_ref_id = 2;
  // Name of the vendor (M2P)
  string vendor = 3;

}

message GetRewardStatusResponse {
  // vendor_ref_id passed in the request
  string vendor_ref_id = 1;
  // reference id of the fulfillment done at Fi's end.
  string fulfillment_ref_id = 2;
  // Status of the reward, Possible Statuses: CREATED, PROCESSING_PENDING, PROCESSING_MANUAL_INTERVENTION, PROCESSING_FAILED, PROCESSED
  string status = 3;
}
