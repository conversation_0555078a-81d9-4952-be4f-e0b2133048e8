//go:generate gen_sql -types=CompanyMarketStatistics,CompanyMarketData,CompanyFundamentalParameters,CompanyFundamentalParagraph,CompanyDetails,AssetDetails
syntax = "proto3";

package vendors.catalog.bridgewise;

import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendors/catalog/bridgewise";
option java_package = "com.github.epifi.gamma.api.vendors.catalog.bridgewise";

message GetAccessTokenReq {
  string application_client_id = 1 [json_name = 'application_client_id'];
  string secret = 2 [json_name = 'secret'];
}

message GetAccessTokenRes {
  string access_token = 1 [json_name = 'access_token'];
}

message AssetDetails {
  int64 company_id = 1 [json_name = "company_id"];
  int64 security_id = 2 [json_name = "security_id"];
  bool security_primary_flag = 3 [json_name = "security_primary_flag"];
  int64 trading_item_id = 4 [json_name = "trading_item_id"];
  bool primary_flag = 5 [json_name = "primary_flag"];
  string ticker_symbol = 6 [json_name = "ticker_symbol"];
  string exchange_symbol = 7 [json_name = "exchange_symbol"];
  string exchange_name = 8 [json_name = "exchange_name"];
  int64 exchange_id = 9 [json_name = "exchange_id"];
  int64 exchange_importance_level = 10 [json_name = "exchange_importance_level"];
  string currency_iso3 = 11 [json_name = "currency_iso3"];
  string asset_type = 12 [json_name = "asset_type"];
  bool has_score = 13 [json_name = "has_score"];
}

message GetAssetIdentifierDetailsRes {
  repeated AssetDetails results = 1 [json_name = "results"];
}

message CompanyDetails {
  string language = 1 [json_name = "language"];
  string updated_at = 2 [json_name = "updated_at"];
  int64 company_id = 3 [json_name = "company_id"];
  string company_name = 4 [json_name = "company_name"];
  string company_name_short = 5 [json_name = "company_name_short"];
  string website = 6 [json_name = "website"];
  string primary_ticker_symbol = 7 [json_name = "primary_ticker_symbol"];
  string primary_exchange_symbol = 8 [json_name = "primary_exchange_symbol"];
  int64 region_id = 9 [json_name = "region_id"];
  string region_name = 10 [json_name = "region_name"];
  int64 incorporation_country_id = 11 [json_name = "incorporation_country_id"];
  string incorporation_country_name = 12 [json_name = "incorporation_country_name"];
  int64 domicile_country_id = 13 [json_name = "domicile_country_id"];
  string domicile_country_name = 14 [json_name = "domicile_country_name"];
  int64 gics_sector_id = 15 [json_name = "gics_sector_id"];
  string gics_sector_name = 16 [json_name = "gics_sector_name"];
  int64 gics_industry_group_id = 17 [json_name = "gics_industry_group_id"];
  string gics_industry_group_name = 18 [json_name = "gics_industry_group_name"];
  int64 gics_industry_id = 19 [json_name = "gics_industry_id"];
  string gics_industry_name = 20 [json_name = "gics_industry_name"];
  int64 csa_industry_id = 21 [json_name = "csa_industry_id"];
  string csa_industry_name = 22 [json_name = "csa_industry_name"];
  int64 primary_currency_market_id = 23 [json_name = "primary_currency_market_id"];
  string primary_currency_market = 24 [json_name = "primary_currency_market"];
  string primary_currency_financials_iso3 = 25 [json_name = "primary_currency_financials_iso3"];
  int64 primary_security_id = 26 [json_name = "primary_security_id"];
  string primary_security_name = 27 [json_name = "primary_security_name"];
  string ipo_date = 28 [json_name = "ipo_date"];
  string pdf_path = 29 [json_name = "pdf_path"];
  google.protobuf.Value include = 30 [json_name = "include"];
  google.protobuf.Value is_red_flagged = 31 [json_name = "is_red_flagged"];
}

message GetCompanyRes {
  repeated CompanyDetails companies = 1 [json_name = "companies"];
}

message CompaniesData {
  repeated CompanyDetails data = 1 [json_name = "data"];
  int32 total_count = 2 [json_name = "total_count"];
}

message CompanyFundamentalParameters {
  string language = 1 [json_name = "language"];
  string updated_at = 2 [json_name = "updated_at"];
  int64 calendar_year = 3 [json_name = "calendar_year"];
  int64 calendar_quarter = 4 [json_name = "calendar_quarter"];
  string analysis_type = 5 [json_name = "analysis_type"];
  string analysis_type_name = 6 [json_name = "analysis_type_name"];
  string section_type = 7 [json_name = "section_type"];
  string section_type_name = 8 [json_name = "section_type_name"];
  string filing_date = 9 [json_name = "filing_date"];
  string period_type = 10 [json_name = "period_type"];
  int64 parameter_id = 11 [json_name = "parameter_id"];
  google.protobuf.Value parameter_name = 12 [json_name = "parameter_name"];
  google.protobuf.Value parameter_description = 13 [json_name = "parameter_description"];
  double parameter_value = 14 [json_name = "parameter_value"];
  double parameter_value_change = 15 [json_name = "parameter_value_change"];
  string parameter_type = 16 [json_name = "parameter_type"];
  string parameter_currency_iso3 = 17 [json_name = "parameter_currency_iso3"];
  double parameter_exchange_rate_to_usd = 18 [json_name = "parameter_exchange_rate_to_usd"];
}

message GetCompanyFundamentalParametersRes {
  repeated CompanyFundamentalParameters parameters = 1 [json_name = "parameters"];
}

message CompanyFundamentalParagraph {
  string analysis_type = 1 [json_name = "analysis_type"];
  string section_type = 2 [json_name = "section_type"];
  string paragraph_type = 3 [json_name = "paragraph_type"];
  string paragraph = 4 [json_name = "paragraph"];
  string language = 5 [json_name = "language"];
  string updated_at = 6 [json_name = "updated_at"];
}

message GetCompanyFundamentalParagraphsRes {
  repeated CompanyFundamentalParagraph paragraphs = 1 [json_name = "paragraphs"];
}

message CompanyMarketData {
  int64 trading_item_id = 1 [json_name = "trading_item_id"];
  string updated_at = 2 [json_name = "updated_at"];
  string date = 3 [json_name = "date"];
  double open_price = 4 [json_name = "open_price"];
  double close_price = 5 [json_name = "close_price"];
  double high_price = 6 [json_name = "high_price"];
  double low_price = 7 [json_name = "low_price"];
  int64 volume = 8 [json_name = "volume"];
  double market_cap = 9 [json_name = "market_cap"];
  double implied_market_cap = 10 [json_name = "implied_market_cap"];
  string market_cap_currency_iso3 = 11 [json_name = "market_cap_currency_iso3"];
  string price_currency_iso3 = 12 [json_name = "price_currency_iso3"];
  double exchange_rate_to_usd = 13 [json_name = "exchange_rate_to_usd"];
  double daily_return = 14 [json_name = "daily_return"];
  double market_cap_exchange_rate_to_usd = 15 [json_name = "market_cap_exchange_rate_to_usd"];
  double market_cap_usd = 16 [json_name = "market_cap_usd"];
  string filing_date = 17 [json_name = "filing_date"];
  double change_since_filing = 18 [json_name = "change_since_filing"];
}

message GetCompanyMarketDataRes {
  repeated CompanyMarketData market_data = 1 [json_name = "market_data"];
}

message CompanyLogoLink {
  string size = 1 [json_name = "size"];
  string url = 2 [json_name = "url"];
  string type = 3 [json_name = "type"];
  string extension = 4 [json_name = "extension"];
  bool is_sponsor = 5 [json_name = "is_sponsor"];
}

message CompanyLogo {
  int64 company_id = 1 [json_name = "company_id"];
  google.protobuf.Value sponsor_id = 2 [json_name = "sponsor_id"];
  repeated CompanyLogoLink links = 3 [json_name = "links"];
}

message GetCompanyLogosRes {
  repeated CompanyLogo items = 1 [json_name = "items"];
  int64 total = 2 [json_name = "total"];
  int64 page = 3 [json_name = "page"];
  int64 size = 4 [json_name = "size"];
  int64 pages = 5 [json_name = "pages"];
}

message CompanyMarketStatistics {
  int64 company_id = 1 [json_name = "company_id"];
  int64 trading_item_id = 2 [json_name = "trading_item_id"];
  double one_day = 3 [json_name = "1D"];
  double five_days = 4 [json_name = "5D"];
  double one_month = 5 [json_name = "1M"];
  double six_months = 6 [json_name = "6M"];
  double one_year = 7 [json_name = "1Y"];
  double two_years = 8 [json_name = "2Y"];
  double five_years = 9 [json_name = "5Y"];
  double year_to_date = 10 [json_name = "YTD"];
}

message GetCompanyMarketStatisticsRes {
  repeated CompanyMarketStatistics market_statistics = 1 [json_name = "market_statistics"];
}
