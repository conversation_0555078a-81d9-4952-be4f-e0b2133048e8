syntax = "proto3";

package vendors.moengage;

import "api/vendors/moengage/insights/spends.proto";
import "api/vendors/moengage/stocks/stocks.proto";

option go_package = "github.com/epifi/gamma/api/vendors/moengage";
option java_package = "com.github.epifi.gamma.api.vendors.moengage";

// GetUserAttributesRequest contains all the query parameters that the content API will support.
// To keep the API extendable with minimum changes on Moengage dashboard, we'll support taking in a list of fields (string)
// that we want to fetch and a list of key-value pairs for any data that may be needed to fetch the required information.
message GetUserAttributesRequest {
  // moengage's user ID that maps 1:1 to Fi's internal actorId
  string user_id = 1;

  // comma separated list of area names for which we want to fetch PII information, e.g., REWARDS,QUEST
  string areas = 2;

  // comma separated list of fields to fetch. API's response
  string field_mask = 3;

  // comma separated list of information required by services to fetch the fields, like externalTxnId, summaryDuration, etc.
  // information needs to be present as an object of key:value pairs. e.g., txn_id:XXXXXXXX, duration:7d, etc.
  string request_metadata = 4;
}


// GetUserAttributesResponse is the response object that will be returned by the content API
message GetUserAttributesResponse {
  // contains key:value pairs where keys are areas given in requested fields
  // value is map of key:value pairs of field name to user attributes
  map<string, FieldNameToUserAttributeMap> area_to_field_name_user_attributes = 1 [json_name = "area_to_field_name_user_attributes"];
}

message FieldNameToUserAttributeMap {
  // contains key:value pairs where keys are the requested fields in  GetPiiDataRequest.fields_to_fetch
  // and values are the data that's requested.
  map<string, UserAttribute> field_name_to_user_attributes = 1 [json_name = "field_name_to_user_attributes"];
}

message UserAttribute {
  oneof ValueTypes {
    // for simple string values
    string string_value = 1 [json_name = "string_value"];
    // for summary of rewards earned by the user
    RewardsSummary rewards_summary = 2 [json_name = "rewards_summary"];
    // summary of user spends over a time period
    SpendsSummary spends_summary = 3 [json_name = "spends_summary"];
    // Summary of loan details
    LoanSummary loan_summary = 4 [json_name = "loan_summary"];
    // Summary of credit card details
    CreditCardSummary credit_card_summary = 5 [json_name = "credit_card_summary"];
    // summary of debit card details
    DebitCardSummary debit_card_summary = 6 [json_name = "debit_card_summary"];
    // summary of a stock market index
    stocks.MarketIndexSummary market_index_summary = 7 [json_name = "market_index_summary"];
    // detailed info about a stock
    stocks.Stock stock_details = 8 [json_name = "stock_details"];
    // detailed info about aasalary
    AaSalaryProgramSummary aa_salary_program_summary = 9 [json_name = "aa_salary_program_summary"];
    // detailed info about a loan offer
    LoanOfferDetails loan_offer_details = 10 [json_name = "loan_offer_details"];
    // details about daily portfolio change
    PortfolioChangeDetails portfolio_change_details = 11 [json_name = "portfolio_change_details"];
    // details about weekly portfolio change
    WeeklyPortfolioChangeDetails weekly_portfolio_change_details = 12 [json_name = "weekly_portfolio_change_details"];
  }
}

message RewardsSummary {
  string fi_coins_rewards_summary = 1 [json_name = "fi_coins_rewards_summary"];
  string cash_rewards_summary = 2 [json_name = "cash_rewards_summary"];
}

message LoanSummary {
  string emi_amount = 1 [json_name = "emi_amount"];
  // Emi date. For users who missed a EMI, this data will be in the past, for other this will be in future
  string emi_date = 2 [json_name = "emi_date"];
  // Days past due date
  int32 dpd = 3 [json_name = "dpd"];
  // Days till due date
  int32 days_till_next_due = 4 [json_name = "days_till_next_due"];
  // Bank account details
  BankDetails bank_details = 5 [json_name = "bank_details"];
}
message BankDetails {
  string bank_account_number = 1 [json_name = "bank_account_number"];
  string bank_name = 2 [json_name = "bank_name"];
}
message CreditCardSummary {
  string first_name = 1 [json_name = "first_name"];
  string bill_amount = 2 [json_name = "bill_amount"];
  string last_four_digits_of_card = 3 [json_name = "last_four_digits_of_card"];
  string due_date = 4 [json_name = "due_date"];
  string bill_generation_date = 5 [json_name = "bill_generation_date"];
  string minimum_amount_due = 6 [json_name = "minimum_amount_due"];
  string limit_utilized_percent = 7 [json_name = "limit_utilized_percent"];
  string number_of_days_overdue = 8 [json_name = "number_of_days_overdue"];
  string unpaid_bill_amount = 9 [json_name = "unpaid_bill_amount"];
  string unpaid_minimum_amount_due = 10 [json_name = "unpaid_minimum_amount_due"];
  bool is_minimum_amount_due_paid = 11 [json_name = "is_minimum_amount_due_paid"];
  bool is_bill_amount_paid = 12 [json_name = "is_bill_amount_paid"];
  string interest_charges = 13 [json_name = "interest_charges"];
}

message DebitCardSummary {
  string zero_forex_markup_saved_till_now = 1 [json_name = "zero_forex_markup_saved_till_now"];
  string zero_forex_markup_saved_for_last_seven_days = 2 [json_name = "zero_forex_markup_saved_for_last_seven_days"];
  string domestic_transactions_amount = 3 [json_name = "domestic_transactions_amount"];
  string international_transactions_amount = 4 [json_name = "international_transactions_amount"];
  string communication_address = 5 [json_name = "communication_address"];
  string permanent_address = 6 [json_name = "permanent_address"];
}

message AaSalaryProgramSummary {
  int32 last_activated_month = 1 [json_name = "last_activated_month"];
  int32 last_activated_year = 2 [json_name = "last_activated_year"];
  int32 transfer_days_due = 3 [json_name = "transfer_days_due"];
  int32 transfer_over_due_since = 4 [json_name = "transfer_over_due_since"];
  bool is_aa_salary_active = 5 [json_name = "is_aa_salary_active"];
  int32 days_since_last_transfer = 6 [json_name = "days_since_last_transfer"];
  int32 upcoming_activation_month = 7 [json_name = "upcoming_activation_month"];
  int32 upcoming_activation_year = 8 [json_name = "upcoming_activation_year"];
  string cashback_for_latest_committed_amount = 9 [json_name = "cashback_for_latest_committed_amount"];
  int64 latest_committed_amount = 10 [json_name = "latest_committed_amount"];
}

message LoanOfferDetails {
  string loan_offer_amount = 1 [json_name = "loan_offer_amount"];
  string interest_rate_yearly = 2 [json_name = "interest_rate_yearly"];
  string lender = 3 [json_name = "lender"];
  string offer_expiry_date = 4 [json_name = "offer_expiry_date"];
}

message PortfolioChangeDetails {
  string portfolio_daily_change_amount = 1 [json_name = "portfolio_daily_change_amount"];
  stocks.NumericMetric portfolio_daily_change_percentage = 2 [json_name = "portfolio_daily_change_percentage"];
  // This will convey the date for which the portfolio daily change summary was calculated
  string portfolio_summary_date = 3 [json_name = "portfolio_summary_date"];
  string daily_top_gainer_name = 4 [json_name = "daily_top_gainer_name"];
  string daily_top_gainer_change_percentage = 5 [json_name = "daily_top_gainer_change_percentage"];
  stocks.NumericMetric daily_benchmark_change_percentage = 6 [json_name = "daily_benchmark_change_percentage"];
}

message WeeklyPortfolioChangeDetails {
  string portfolio_weekly_change_amount = 1 [json_name = "portfolio_weekly_change_amount"];
  stocks.NumericMetric portfolio_weekly_change_percentage = 2 [json_name = "portfolio_weekly_change_percentage"];
  // This will convey the date for which the portfolio weekly change summary was calculated
  string portfolio_summary_date = 3 [json_name = "portfolio_summary_date"];
  string weekly_top_gainer_name = 4 [json_name = "weekly_top_gainer_name"];
  string weekly_top_gainer_change_percentage = 5 [json_name = "weekly_top_gainer_change_percentage"];
}
