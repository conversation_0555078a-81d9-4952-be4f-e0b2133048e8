syntax = "proto3";

package vendors.moengage.stocks;

import "api/vendors/moengage/stocks/stocks.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendors/moengage/stocks";
option java_package = "com.github.epifi.gamma.api.vendors.moengage.stocks";

// MarketIndexSummaryCacheMessage is a wrapper message over MarketIndexSummary
// It contains the cached value and metadata columns for cache maintenance
// ToDo: create a generic cache wrapper which can contain any message
message MarketIndexSummaryCacheMessage {
  stocks.MarketIndexSummary market_index_summary = 1;
  // time when the cache value was set. This will be used to identify staleness of cache and can be used to eagerly
  // refresh the cached value
  google.protobuf.Timestamp updated_at = 2;
}
