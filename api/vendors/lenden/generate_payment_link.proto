syntax = "proto3";

package vendors.lenden;

import "api/vendors/lenden/common.proto";

option go_package = "github.com/epifi/gamma/api/vendors/lenden";

message GeneratePaymentLinkRequestPayload {
  string origin_system = 1 [json_name = "origin_system"];
  string loan_id = 2 [json_name = "loan_id"];
  string product_id = 3 [json_name = "product_id"];
  string purpose = 4 [json_name = "purpose"];
  double amount = 5 [json_name = "amount"];
  bool is_intent_flow = 6 [json_name = "is_intent_flow"];
}

message GeneratePaymentLinkRequest {
  Params params = 1 [json_name = "params"];
  Fields fields = 2 [json_name = "fields"];
  GeneratePaymentLinkRequestPayload json = 3 [json_name = "json"];
  Attributes attributes = 4 [json_name = "attributes"];
  string api_code = 5 [json_name = "api_code"];
}

message GeneratePaymentLinkResponseData {
  string url = 1 [json_name = "url"];
  string order_id = 2 [json_name = "order_id"];
  string deep_link = 3 [json_name = "deep_link"];
}

message GeneratePaymentLinkResponse {
  string trace_id = 1 [json_name = "trace_id"];
  string message_code = 2 [json_name = "message_code"];
  string message = 3 [json_name = "message"];
  GeneratePaymentLinkResponseData response_data = 4 [json_name = "response_data"];
}

message GeneratePaymentLinkResponseWrapper {
  string message = 1 [json_name = "message"];
  GeneratePaymentLinkResponse response = 2 [json_name = "response"];
}
