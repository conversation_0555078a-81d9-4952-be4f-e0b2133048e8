syntax = "proto3";

package vendors.lenden;

import "api/vendors/lenden/common.proto";

option go_package = "github.com/epifi/gamma/api/vendors/lenden";

message AmortizationScheduleRequestPayload {
  string loan_id = 1 [json_name = "loan_id"];
  string product_id = 2 [json_name = "product_id"];
  string origin_system = 3 [json_name = "origin_system"]; // Should be "PARTNER"
}

message AmortizationScheduleRequest {
  Params params = 1 [json_name = "params"];
  Fields fields = 2 [json_name = "fields"];
  AmortizationScheduleRequestPayload json = 3 [json_name = "json"];
  Attributes attributes = 4 [json_name = "attributes"];
  string api_code = 5 [json_name = "api_code"];
}

message AmortizationAmountBreakupComponent {
  string purpose = 1 [json_name = "purpose"];
  double amount = 2 [json_name = "amount"];
}

message AmortizationScheduleItem {
  string due_date = 1 [json_name = "due_date"];
  double due_amount = 2 [json_name = "due_amount"];
  repeated AmortizationAmountBreakupComponent details = 3 [json_name = "details"];
  string status = 4 [json_name = "status"];
  double outstanding_amount = 5 [json_name = "outstanding_amount"];
}

message AmortizationScheduleResponseData {
  repeated AmortizationScheduleItem amortization_schedule = 1 [json_name = "amortization_schedule"];
}

message AmortizationScheduleResponse {
  string trace_id = 1 [json_name = "trace_id"];
  string message_code = 2 [json_name = "message_code"];
  string message = 3 [json_name = "message"];
  AmortizationScheduleResponseData response_data = 4 [json_name = "response_data"];
}

message AmortizationScheduleResponseWrapper {
  string message = 1 [json_name = "message"];
  AmortizationScheduleResponse response = 2 [json_name = "response"];
}
