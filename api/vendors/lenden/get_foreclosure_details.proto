syntax = "proto3";

package vendors.lenden;

import "api/vendors/lenden/common.proto";

option go_package = "github.com/epifi/gamma/api/vendors/lenden";

message GetForeclosureDetailsRequestPayload {
  string loan_id = 1 [json_name = "loan_id"];
  string product_id = 2 [json_name = "product_id"];
  string purpose = 3 [json_name = "purpose"];
  string origin_system = 4 [json_name = "origin_system"];
}

message GetForeclosureDetailsRequest {
  Params params = 1 [json_name = "params"];
  Fields fields = 2 [json_name = "fields"];
  GetForeclosureDetailsRequestPayload json = 3 [json_name = "json"];
  Attributes attributes = 4 [json_name = "attributes"];
  string api_code = 5 [json_name = "api_code"];
}

message ForeclosureDetails {
  double principal_outstanding = 1 [json_name = "principal_outstanding"];
  double interest_outstanding = 2 [json_name = "interest_outstanding"];
  double delay_outstanding = 3 [json_name = "delay_outstanding"];
  double late_fee_outstanding = 4 [json_name = "late_fee_outstanding"];
  double foreclosure_amount = 5 [json_name = "foreclosure_amount"];
  double foreclosure_charges = 6 [json_name = "foreclosure_charges"];
  double gap_interest_refund = 8 [json_name = "gap_interest_refund"];
  double repayment_received = 9 [json_name = "repayment_received"];
  int32 days_difference = 7 [json_name = "days_difference"];
}

message GetForeclosureDetailsResponse {
  string trace_id = 1 [json_name = "trace_id"];
  string message_code = 2 [json_name = "message_code"];
  string message = 3 [json_name = "message"];
  ForeclosureDetails response_data = 4 [json_name = "response_data"];
}

message GetForeclosureDetailsResponseWrapper {
  string message = 1 [json_name = "message"];
  GetForeclosureDetailsResponse response = 2 [json_name = "response"];
}
