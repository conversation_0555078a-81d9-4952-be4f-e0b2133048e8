syntax = "proto3";

package vendors.lenden;

import "api/vendors/lenden/common.proto";

option go_package = "github.com/epifi/gamma/api/vendors/lenden";

message GetPaymentStatusRequestPayload {
  string origin_system = 1 [json_name = "origin_system"];
  string product_id = 2 [json_name = "product_id"];
  string order_id = 3 [json_name = "order_id"];
}

message GetPaymentStatusRequest {
  Params params = 1 [json_name = "params"];
  Fields fields = 2 [json_name = "fields"];
  GetPaymentStatusRequestPayload json = 3 [json_name = "json"];
  Attributes attributes = 4 [json_name = "attributes"];
  string api_code = 5 [json_name = "api_code"];
}

message GetPaymentStatusResponseData {
  string order_id = 1 [json_name = "order_id"];
  string status = 2 [json_name = "status"];
  string failure_reason = 3 [json_name = "failure_reason"];
}

message GetPaymentStatusResponse {
  string trace_id = 1 [json_name = "trace_id"];
  string message_code = 2 [json_name = "message_code"];
  string message = 3 [json_name = "message"];
  GetPaymentStatusResponseData response_data = 4 [json_name = "response_data"];
}

message GetPaymentStatusResponseWrapper {
  string message = 1 [json_name = "message"];
  GetPaymentStatusResponse response = 2 [json_name = "response"];
}
