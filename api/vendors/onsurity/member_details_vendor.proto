syntax = "proto3";

package vendors.onsurity;


option go_package = "github.com/epifi/gamma/api/vendors/onsurity";
option java_package = "com.github.epifi.gamma.api.vendors.onsurity";

message MemberDetailsApiRequest {
  // Member’s email address
  repeated string email_ids = 1 [json_name = "emailIds"];

  // Member’s phone number
  repeated string phone_numbers = 2 [json_name = "phoneNumbers"];
}

message MemberDetailsApiResponse {
  Data data = 1 [json_name = "data"];
  Meta meta = 2 [json_name = "meta"];

  message Data {
    map<string, PrimaryMemberDataValues> membership_details = 1 [json_name = "membershipDetailsMap"];
    repeated ParentalPurchaseData parental_purchases = 2 [json_name = "parental_purchase_data"];
    repeated InvitedMemberData invited_members = 3 [json_name = "invited_member_data"];
  }

  message Meta {
    bool is_success = 1 [json_name = "isSuccess"];
    string status_code = 2 [json_name = "statusCode"];
    string response_message = 3 [json_name = "responseMessage"];
    string display_message = 4 [json_name = "displayMessage"];
    //    google.protobuf.Struct errors =5 [json_name = "errors"];

  }

  message Error {
    string action = 1 [json_name = "action"];
    string message = 2 [json_name = "message"];
    string row = 3 [json_name = "row"];
    string value = 4 [json_name = "value"];
  }

  message PrimaryMemberDataValues {
    repeated PrimaryMemberData primaryMemberValues = 1 [json_name = "primary_member_data"];
  }

  message ParentalPurchaseData {
    string name = 1 [json_name = "name"];
    string gender = 2 [json_name = "gender"];
    string date_of_birth = 3 [json_name = "date_of_birth"];
    string relation = 4 [json_name = "relation"];
    string oshs_id = 5 [json_name = "oshs_id"];
    string plan_name = 6 [json_name = "plan_name"];
    string plan_category = 7 [json_name = "plan_category"];
    string policy_type = 8 [json_name = "policy_type"];
    string plan_id = 9 [json_name = "plan_id"];
    string plan_subscription_id = 10 [json_name = "plan_subscription_id"];
    bool active = 11 [json_name = "active"];
    string source = 12 [json_name = "source"];
    int64 date_of_joining = 13 [json_name = "date_of_joining"];
    string os_id = 14 [json_name = "os_id"];
  }

  message PrimaryMemberData {
    string name = 1 [json_name = "name"];
    string gender = 2 [json_name = "gender"];
    string date_of_birth = 3 [json_name = "date_of_birth"];
    string phone_number = 4 [json_name = "phone_number"];
    string email_id = 5 [json_name = "email_id"];
    string relation = 6 [json_name = "relation"];
    string oshs_id = 7 [json_name = "oshs_id"];
    string plan_name = 8 [json_name = "plan_name"];
    string plan_category = 9 [json_name = "plan_category"];
    string policy_type = 10 [json_name = "policy_type"];
    string nominee_name = 11 [json_name = "nominee_name"];
    string nominee_relation = 12 [json_name = "nominee_relation"];
    string nominee_dob = 13 [json_name = "nominee_dob"];
    string designation = 14 [json_name = "designation"];
    int64 annual_income = 15 [json_name = "annual_income"];
    string plan_subscription_id = 16 [json_name = "plan_subscription_id"];
    bool active = 17 [json_name = "active"];
    string source = 18 [json_name = "source"];
    int64 date_of_joining = 19 [json_name = "date_of_joining"];
    string os_id = 20 [json_name = "os_id"];
    repeated Dependent dependents = 21 [json_name = "dependents"];

    message Dependent {
      string name = 1 [json_name = "name"];
      string gender = 2 [json_name = "gender"];
      string date_of_birth = 3 [json_name = "date_of_birth"];
      string relation = 4 [json_name = "relation"];
      string oshs_id = 5 [json_name = "oshs_id"];
      string plan_name = 6 [json_name = "plan_name"];
      string plan_category = 7 [json_name = "plan_category"];
      string policy_type = 8 [json_name = "policy_type"];
      string plan_id = 9 [json_name = "plan_id"];
      string plan_subscription_id = 10 [json_name = "plan_subscription_id"];
      bool active = 11 [json_name = "active"];
      int64 date_of_joining = 12 [json_name = "date_of_joining"];
      string os_id = 13 [json_name = "os_id"];
    }
  }

  message InvitedMemberData {
    repeated Recipient recipients = 1 [json_name = "recipients"];
    string message = 2 [json_name = "message"];
    string phone_number = 3 [json_name = "phone_number"];
    string email_id = 4 [json_name = "email_id"];
    string status = 5 [json_name = "status"];
    int64 created_at = 6 [json_name = "created_at"];
    string request_id = 7 [json_name = "request_id"];
    string plan_id = 8 [json_name = "plan_id"];
    int64 date_of_invitation = 9 [json_name = "date_of_invitation"];
  }


  message Recipient {
    string relation = 1 [json_name = "relation"];
    string name = 2 [json_name = "name"];
    string phone_number = 3 [json_name = "phone_number"];
    string email_id = 4 [json_name = "email_id"];
    string gender = 5 [json_name = "gender"];
    string dob = 6 [json_name = "dob"];
    string plan_name = 7 [json_name = "plan_name"];
    string policy = 8 [json_name = "policy"];
    string designation = 9 [json_name = "designation"];
    string income = 10 [json_name = "income"];
    string nominee_name = 11 [json_name = "nominee_name"];
    string nominee_relation = 12 [json_name = "nominee_relation"];
    string nominee_dob = 13 [json_name = "nominee_dob"];
  }


}







