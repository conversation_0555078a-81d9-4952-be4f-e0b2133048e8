syntax = "proto3";

package vendors.zenduty;

import "google/protobuf/struct.proto";


option go_package = "github.com/epifi/gamma/api/vendors/zenduty";
option java_package = "com.github.epifi.gamma.api.vendors.zenduty";

// doc: https://zenduty.com/docs/generic-integration/#custom-mapping-configuration-via-api
message ZendutyAlert {
  string message = 1 [json_name = "message"];
  string summary = 2 [json_name = "summary"];
  string alert_type = 3 [json_name = "alert_type"];
  google.protobuf.Struct payload = 4 [json_name = "payload"];
}

message ZendutyAlertResponse {
  string trace_id = 1 [json_name = "trace_id"];
  string message = 2 [json_name = "message"];
}
