syntax = "proto3";

package vendors.airtel;

import "google/protobuf/wrappers.proto";

option go_package = "github.com/epifi/gamma/api/vendors/airtel";
option java_package = "com.github.epifi.gamma.api.vendors.airtel";

// Integration with Airtel SMS API Doc - https://docs.google.com/document/d/1zOKxi74SL8lPNWEc0L9Z5qNGv7C20gEWT-F56CHAW5s/edit?tab=t.0
// AirtelSendSMSRequest is the request body for sending SMS using Airtel API
message AirtelSendSMSRequest {
  // unique customer id generated from airtel portal for sending SMS
  string customer_id = 1 [json_name = "customerId"];
  // list of destination phone numbers to send SMS
  repeated string destination_address = 2 [json_name = "destinationAddress"];
  // message content to be sent
  string message = 3 [json_name = "message"];
  // source address or sender id from which SMS is sent
  string source_address = 4 [json_name = "sourceAddress"];
  // optional parameter which will flow end to end i.e. starting from sendSms API to callbacks
  map<string, string> meta_data = 5 [json_name = "metaData"];
}

// AirtelSendSMSResponse is the response body for sending SMS using Airtel API
message AirtelSendSMSResponse {
  // unique request id generated by airtel for the SMS sent
  string message_request_id = 1 [json_name = "messageRequestId"];
  // error message if any
  string error_message = 2 [json_name = "errorMessage"];
  // error code if any
  string error_code = 3 [json_name = "errorCode"];
}


// AirtelSmsDLRRequest is the request body for receiving delivery status of SMS sent using Airtel API.
// Ref: https://docs.google.com/document/d/1zOKxi74SL8lPNWEc0L9Z5qNGv7C20gEWT-F56CHAW5s/edit?tab=t.0
message AirtelSmsDLRRequest {
  // unique message id generated by airtel for the SMS sent
  string message_id = 1 [json_name = "messageId"];
  // unique request id generated by airtel for the SMS sent
  string message_request_id = 2 [json_name = "messageRequestId"];
  // unique customer id generated from airtel portal for sending SMS
  string customer_id = 3 [json_name = "customerId"];
  // source address or sender id from which SMS is sent
  string source_address = 4 [json_name = "sourceAddress"];
  // destination phone number to which SMS is sent
  string destination_address = 5 [json_name = "destinationAddress"];
  // message content sent
  string message = 6 [json_name = "message"];
  // status of the message sent
  string message_status = 7 [json_name = "messageStatus"];
  // error code if any
  string error_code = 8 [json_name = "errorCode"];
  // error description if any
  string error_description = 9 [json_name = "errorDescription"];
  // type of message sent
  string message_type = 10 [json_name = "messageType"];
  // timestamp at which request was made in milliseconds
  int64 request_date = 11 [json_name = "requestDate"];
  // timestamp at which message was delivered in milliseconds
  int64 delivered_time = 12 [json_name = "deliveredTime"];
  // number of message units
  int32 units = 13 [json_name = "units"];
  // optional parameter which will flow end to end i.e. starting from sendSms API to callbacks
  AirtelSmsDLRMetaData meta_data = 14 [json_name = "metaData"];
  // status of the message
  string status = 15 [json_name = "status"];
}

message AirtelWhatsappDLRPayload {
  // Identifier of the customer associated with the message
  string customer_id = 1 [json_name = "customerId"];

  // Unique identifier of the message
  string message_id = 2 [json_name = "messageId"];

  // Identifier of the request associated with the message. Present in case of OUTBOUND request.
  string message_request_id = 3 [json_name = "messageRequestId"];

  // Identifier of the WhatsApp message. Not present in case of failed msgStatus.
  string wa_msg_id = 4 [json_name = "waMsgId"];

  // Identifier of the session document where the message belongs. Present in case of SESSION_MESSAGE.
  string session_doc_id = 5 [json_name = "sessionDocId"];

  // Source address of the message (sender's address)
  string source_address = 6 [json_name = "sourceAddress"];

  // Recipient's address for the message
  string recipient_address = 7 [json_name = "recipientAddress"];

  // Country code of the source address
  string source_country = 8 [json_name = "sourceCountry"];

  // Country code of the recipient address
  string recipient_country = 9 [json_name = "recipientCountry"];

  // Identifier of the conversation related to the message. Present only in SENT, DELIVERED, and READ msgStatus.
  string conversation_id = 10 [json_name = "ConversationId"];

  // Category of the conversation. Present only in SENT, DELIVERED, and READ msgStatus.
  string conversation_type = 11 [json_name = "conversationType"];

  // Start time of the conversation (timestamp in milliseconds). Present only in SENT, DELIVERED, and READ msgStatus.
  int64 conversation_start_time = 12 [json_name = "ConversationStartTime"];

  // Status of the message (e.g., "SENT," "DELIVERED," "READ")
  string msg_status = 13 [json_name = "msgStatus"];

  // Parameters associated with the messagePresent in case of Initiated and Received msgStatus.
  MessageParams message_parameters = 14 [json_name = "messageParameters"];

  // Sorting information for the message (e.g., "SESSION_MESSAGE," "TEMPLATE_MESSAGE")
  string msg_sort = 15 [json_name = "msgSort"];

  // Stream information for the message (e.g., "OUTBOUND," "INBOUND")
  string msg_stream = 16 [json_name = "msgStream"];

  // Client-provided correlation ID for tracking the message
  string client_correlation_id = 17 [json_name = "clientCorrelationId"];

  // Type of the message (e.g., "text," "image," "video", "interactive")
  string message_type = 18 [json_name = "messageType"];

  // Time when the session log was recorded (timestamp in milliseconds)
  int64 session_log_time = 19 [json_name = "sessionLogTime"];

  // Date when the record was created (timestamp in milliseconds)
  int64 created_date = 20 [json_name = "createdDate"];

  // Date when the record was last updated (timestamp in milliseconds)
  int64 updated_date = 21 [json_name = "updatedDate"];

  // Error information, if applicable (additional information if an error occurred). Present in case of failure.
  ErrorDetails error_details = 22 [json_name = "errorDetails"];
}

message MessageParams{
  Interactive interactive = 1 [json_name = "interactive"];
  string type = 2 [json_name = "type"];
  TextBody body = 3 [json_name = "body"];
}

message TextBody{
  string text = 1 [json_name = "text"];
}

message Interactive{
  Action action = 1 [json_name = "action"];
}

message Action{
  string buttons = 1 [json_name = "buttons"];
  repeated Section sections = 2 [json_name = "sections"];
}

message Section{
  string title = 1 [json_name = "title"];
  repeated Row rows = 2 [json_name = "rows"];
}

message Row{
  string id = 1 [json_name = "id"];
  string title = 2 [json_name = "title"];
}


message ErrorDetails {
  // Error code representing the type of issue encountered
  int32 code = 1 [json_name = "code"];

  // Short title or summary of the error
  string title = 2 [json_name = "title"];

  // Detailed description of the error, providing additional context
  string details = 3 [json_name = "details"];
}


message AirtelSmsDLRMetaData {
  // sub account id
  google.protobuf.StringValue sub_account_id = 1 [json_name = "subAccountId"];
  // created by
  // using google.protobuf.StringValue since we need a null value instead of an empty string
  // in case of an empty value
  google.protobuf.StringValue created_by = 2 [json_name = "createdBy"];
  // rbac sub account
  RbacSubAccount rbac_sub_account = 3 [json_name = "rbacSubAccount"];
  // mdr category
  google.protobuf.StringValue mdr_category = 4 [json_name = "mdrCategory"];
  // Metadata included in the Airtel SendSMS API request will be mirrored in the Airtel DLR API request.
  // This metadata will appear in the `metaData` field with identical key-value pairs, ensuring consistency and traceability.
  //
  // **NOTE:** Airtel supports only one callback URL per account. By default, callbacks are sent to the production URL for all SMS messages.
  // To distinguish callbacks for different environments, we include `"env": "qa"` in the SendSMS API request metadata.
  // This helps us identify the environment type in the callback request.
  //
  // If new metadata fields are added in the future, ensure they are included in the `AirtelSmsDLRMetaData` message.
  string env = 5 [json_name = "env"];
}

message RbacSubAccount {
  // account id
  google.protobuf.StringValue account_id = 1 [json_name = "accountId"];
  // first name
  google.protobuf.StringValue first_name = 2 [json_name = "firstName"];
  // email id
  google.protobuf.StringValue email_id = 3 [json_name = "emailId"];
  // status
  google.protobuf.StringValue status = 4 [json_name = "status"];
  // iam uuid
  google.protobuf.StringValue iam_uuid = 5 [json_name = "iamUuid"];
  // services
  Services services = 6 [json_name = "services"];
}

message Services {
  // SMS
  SmsService sms = 1 [json_name = "SMS"];
}

message SmsService {
  // credit flag
  google.protobuf.BoolValue credit_flag = 1 [json_name = "creditFlag"];
  // service status
  google.protobuf.StringValue service_status = 2 [json_name = "serviceStatus"];
  // credits allotted
  google.protobuf.Int32Value credits_allotted = 3 [json_name = "creditsAllotted"];
  // dlt enabled
  google.protobuf.BoolValue dlt_enabled = 4 [json_name = "dltenabled"];
}
