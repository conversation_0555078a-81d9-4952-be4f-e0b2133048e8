syntax = "proto3";

package api.vendors.saven.creditcard;

option go_package = "github.com/epifi/gamma/api/vendors/saven/creditcard";
option java_package = "com.github.epifi.gamma.api.vendors.saven.creditcard";

message GetCreditCardTrackingDetailsResponse {
  TrackingData tracking_data = 1 [json_name = "tracking_data"];
  Exception exception = 2 [json_name = "exception"];
}

message TrackingData {
  string pickup_date = 1 [json_name = "pickup_date"];
  // eg - DELIVERED, IN_TRANSIT, OUT_FOR_DELIVERY, SHIPPED
  string current_status = 2 [json_name = "current_status"];
  // Detailed information for each scan for the order
  repeated Scan scans = 3 [json_name = "scan"];
  // Carrier partner for delivery eg - BLUEDART, DELHIVERY, INDIAPOST
  string carrier = 4 [json_name = "carrier"];
  // Vendor who is responsible for printing the card eg - MCT, SESHAASAI
  string card_printing_vendor = 5 [json_name = "card_printing_vendor"];
  // AWB number for the shipment
  string awb_number = 6 [json_name = "awb_number"];
  ExtraFields extra_fields = 7 [json_name = "extra_fields"];
  string tracking_url = 8 [json_name = "tracking_url"];
}

message Exception {
  string status_code = 1 [json_name = "error_code"];
  string error_message = 2 [json_name = "error_message"];
}

message Scan {
  string scan_time = 1 [json_name = "time"];
  string location = 2 [json_name = "location"];
  string status_detail = 3 [json_name = "status_detail"];
}

message ExtraFields {
  string expected_delivery_date = 1 [json_name = "expected_delivery_date"];
}

message GetCreditCardTrackingDetailsRequest {
  // this is the unique identifier for the user at vendor's end i.e. there will be a one to one mapping between this and actor_id ( user identifier across epifi systems),
  // this is referred as external_user_id in across epifi system
  string internal_user_id = 1 [json_name = "internal_user_id"];
}

message UpdateCreditCardDeliveryStatusRequest {
  DeliveryDetails delivery_details = 1 [json_name = "cardTrackingDetails"];
  string user_id = 2 [json_name = "epifiUserId"];

  message DeliveryDetails {
    string delivery_status = 1 [json_name = "deliveryStatus"];
    string delivery_vendor = 2 [json_name = "deliveryVendor"];
    string tracking_url = 3 [json_name = "trackingUrl"];
  }
}
