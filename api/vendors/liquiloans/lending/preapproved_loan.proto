syntax = "proto3";

package vendors.liquiloans.lending;

import "google/protobuf/struct.proto";
import "google/protobuf/wrappers.proto";

option go_package = "github.com/epifi/gamma/api/vendors/liquiloans/lending";
option java_package = "com.github.epifi.gamma.api.vendors.liquiloans.lending";

message GetCreditLineDetailsRequest {
  string sid = 1 [json_name = "sid"];
  string applicant_id = 2 [json_name = "applicant_id"];
  string checksum = 3 [json_name = "checksum"];
}

message GetCreditLineDetailsResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    CreditLineDetails credit_line_details = 1 [json_name = "credit_line_details"];
    repeated CreditLineSchemes credit_line_schemes = 2 [json_name = "credit_line_schemes"];

    message CreditLineDetails {
      string applicant_id = 1 [json_name = "applicant_id"];
      double upper_limit = 2 [json_name = "upper_limit"];
      double available_limit = 3 [json_name = "available_limit"];
      double block_limit = 4 [json_name = "block_limit"];
    }

    message CreditLineSchemes {
      double roi = 1 [json_name = "roi"];
      string roi_type = 2 [json_name = "roi_type"];
      double max_emi_allowed = 3 [json_name = "max_emi_allowed"];
      int32 min_tenure = 4 [json_name = "min_tenure"];
      int32 max_tenure = 5 [json_name = "max_tenure"];
      double min_drawdown_amount = 6 [json_name = "min_drawdown_amount"];
      double max_drawdown_amount = 7 [json_name = "max_drawdown_amount"];
      string emi_due_date = 8 [json_name = "emi_due_date"]; //YYYY-MM-DD
      string pf_type = 9 [json_name = "pf_type"];
      double pf_fees = 10 [json_name = "pf_fees"];
      string franking_type = 11 [json_name = "franking_type"];
      string franking_charges = 12 [json_name = "franking_charges"];
      string tenure_frequency = 13 [json_name = "tenure_frequency"];
    }
  }
}

message AddPersonalDetailsRequest {
  string sid = 1 [json_name = "sid"];
  string name = 2 [json_name = "name"];
  string email = 3 [json_name = "email"];
  string contact_number = 4 [json_name = "contact_number"];
  string pan = 5 [json_name = "pan"];
  string gender = 6 [json_name = "gender"];
  string dob = 7 [json_name = "dob"];
  string checksum = 8 [json_name = "checksum"];
  string urn = 9 [json_name = "urn"];
  string udf1 = 10 [json_name = "udf1"];
  string udf8 = 11 [json_name = "UDF8"];
  string udf9 = 12 [json_name = "UDF9"];
  // represents the monthly income of the user
  string udf4 = 13 [json_name = "UDF4"];
  // represents the income data source
  // "AA" - income data source is txns from account aggregator
  string udf5 = 14 [json_name = "UDF5"];
  string udf2 = 15 [json_name = "UDF2"];
}

message AddPersonalDetailsResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    int64 applicant_id = 1 [json_name = "applicant_id"];
    string name = 2 [json_name = "name"];
    string email = 3 [json_name = "email"];
    string contact_number = 4 [json_name = "contact_number"];
  }
}

message AddBankingDetailsRequest {
  string applicant_id = 1 [json_name = "applicant_id"];
  string account_type = 2 [json_name = "account_type"];
  string bank_name = 3 [json_name = "bank_name"];
  string ifsc = 4 [json_name = "ifsc"];
  string account_number = 5 [json_name = "account_number"];
  string account_holder_name = 6 [json_name = "account_holder_name"];
  string sid = 7 [json_name = "sid"];
  string checksum = 8 [json_name = "checksum"];
}

message AddAddressDetailsRequest {
  string applicant_id = 1 [json_name = "applicant_id"];
  string address_line_1 = 2 [json_name = "address_line_1"];
  string address_line_2 = 3 [json_name = "address_line_2"];
  string area = 4 [json_name = "area"];
  string pin_code = 5 [json_name = "pin_code"];
  string city = 6 [json_name = "city"];
  string state = 7 [json_name = "state"];
  string address_type = 8 [json_name = "address_type"];
  string sid = 9 [json_name = "sid"];
  string checksum = 10 [json_name = "checksum"];
}

// This can have income_transfer_type, joining_date -- Optional Fields
message AddEmploymentDetailsRequest {
  string sid = 1 [json_name = "sid"];
  string applicant_id = 2 [json_name = "applicant_id"];
  string occupation = 3 [json_name = "occupation"];
  string organization_name = 4 [json_name = "organization_name"];
  string monthly_income = 5 [json_name = "monthly_income"];
  string checksum = 6 [json_name = "checksum"];
  string designation = 7 [json_name = "designation"];
}

// Common response used across multiple APIs (Add banking, employment and address details) as structure is same
message AddDetailsResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  google.protobuf.Struct data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];
}

message GetLimitRequest {
  string sid = 1 [json_name = "sid"];
  string applicant_id = 2 [json_name = "applicant_id"];
  string checksum = 3 [json_name = "checksum"];
}

message GetLimitResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    int32 applicant_id = 1 [json_name = "applicant_id"];
    string name = 2 [json_name = "name"];
    string email = 3 [json_name = "email"];
    string contact_number = 4 [json_name = "contact_number"];
    string pan = 5 [json_name = "pan"];
    LimitValue upper_limit = 6 [json_name = "upper_limit"];
    LimitValue available_limit = 7 [json_name = "available_limit"];
    string limit_expiry = 8 [json_name = "limit_expiry"];
    string status = 9 [json_name = "status"];

    message LimitValue {
      double value = 1 [json_name = "value"];
      string last_updated = 2 [json_name = "last_updated"];
    }
  }
}
message ApplicantLookupRequest {
  string sid = 1 [json_name = "sid"];
  string pan = 2 [json_name = "pan"];
  string checksum = 3 [json_name = "checksum"];
}

message ApplicantLookupResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    int64 applicant_id = 1 [json_name = "applicant_id"];
    string name = 2 [json_name = "name"];
    string email = 3 [json_name = "email"];
    string contact_number = 4 [json_name = "contact_number"];
    string pan = 5 [json_name = "pan"];
    Details details = 6 [json_name = "details"];
    Activation activation = 7 [json_name = "activation"];

    message Details {
      bool banking = 1 [json_name = "banking"];
      bool address = 2 [json_name = "address"];
      bool employment = 3 [json_name = "employment"];
    }

    message Activation {
      bool agreement = 1 [json_name = "agreement"];
      bool mandate = 2 [json_name = "mandate"];
    }
  }
}

message GetMandateLinkRequest {
  string sid = 1 [json_name = "sid"];
  string applicant_id = 2 [json_name = "applicant_id"];
  string checksum = 3 [json_name = "checksum"];
  string recreate_url = 4 [json_name = "recreate_url"];
}

message GetMandateLinkResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    string mandate_link = 1 [json_name = "mandate_link"];
    string mandate_id = 2 [json_name = "mandate_id"];
  }
}

message GetMandateStatusRequest {
  string sid = 1 [json_name = "sid"];
  string applicant_id = 2 [json_name = "applicant_id"];
  string checksum = 3 [json_name = "checksum"];
}

message GetMandateStatusResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    string status = 1 [json_name = "status"];
  }
}

message MakeDrawdownRequest {
  string sid = 1 [json_name = "sid"];
  string applicant_id = 2 [json_name = "applicant_id"];
  double amount = 3 [json_name = "amount"];
  string tenure_frequency = 4 [json_name = "tenure_frequency"];
  int32 tenure = 5 [json_name = "tenure"];
  string checksum = 6 [json_name = "checksum"];
  string urn = 7 [json_name = "urn"];
  string scheme_code = 8 [json_name = "scheme_code"];
}

message MakeDrawdownResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    int64 loan_id = 1 [json_name = "loan_id"];
    string applicant_id = 2 [json_name = "applicant_id"];
    string urn = 3 [json_name = "urn"];
  }
}

message GetPdfAgreementRequest {
  string sid = 1 [json_name = "sid"];
  string applicant_id = 2 [json_name = "applicant_id"];
  string application_id = 3 [json_name = "application_id"];
  string checksum = 4 [json_name = "checksum"];
}

message GetPdfAgreementResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    int32 doc_id = 1 [json_name = "doc_id"];
    string pdf = 2 [json_name = "pdf"];
  }
}

message SendBorrowerAgreementOtpRequest {
  string sid = 1 [json_name = "sid"];
  string applicant_id = 2 [json_name = "applicant_id"];
  string application_id = 3 [json_name = "application_id"];
  string checksum = 4 [json_name = "checksum"];
}

message SendBorrowerAgreementOtpResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  repeated Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
  }
}

message VerifyBorrowerAgreementOtpRequest {
  string sid = 1 [json_name = "sid"];
  string applicant_id = 2 [json_name = "applicant_id"];
  string application_id = 3 [json_name = "application_id"];
  string checksum = 4 [json_name = "checksum"];
  string doc_id = 5 [json_name = "doc_id"];
  string otp = 6 [json_name = "otp"];
}

message VerifyBorrowerAgreementOtpResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    string agreement_signed_copy = 1 [json_name = "AgreementSignedCopy"];
  }
}

message GetLoanStatusRequest {
  string sid = 1 [json_name = "SID"];
  string application_id = 2 [json_name = "application_id"];
  string timestamp = 3 [json_name = "Timestamp"]; //YYYY-MM-DD HH:MM:SS
  string checksum = 4 [json_name = "Checksum"];
  string urn = 9 [json_name = "urn"];
}

message GetLoanStatusResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    Status status = 1 [json_name = "status"];

    message Status {
      int32 loan_id = 1 [json_name = "loanId"];
      string loan_code = 2 [json_name = "loanCode"];
      string status = 3 [json_name = "status"];
      string order_id = 4 [json_name = "orderId"];
      string urn = 5 [json_name = "urn"];
      double amount = 6 [json_name = "amount"];
      double product_amount = 7 [json_name = "productAmount"];
      double disbursed_amount = 8 [json_name = "disbursedAmount"];
      string disbursed_date = 9 [json_name = "disbursementDate"];
      double emi = 10 [json_name = "emi"];
      int32 tenure = 11 [json_name = "tenure"];
      double roi = 12 [json_name = "roi"];
      string utr = 13 [json_name = "utr"];
      string last_status_timestamp = 14 [json_name = "last_status_timestamp"];
    }
  }
}

message VerifyAndDownloadCkycRequest {
  string sid = 1 [json_name = "SID"];
  string applicant_id = 2 [json_name = "applicant_id"];
  string checksum = 3 [json_name = "Checksum"];
  string timestamp = 4 [json_name = "Timestamp"]; //YYYY-MM-DD HH:MM:SS
  string id_type = 5 [json_name = "id_type"];
  string id_no = 6 [json_name = "id_no"];
  string auth_factor_type = 7 [json_name = "auth_factor_type"];
  string auth_factor = 58 [json_name = "auth_factor"];
}

message VerifyAndDownloadCkycResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    PersonalDetails personal_details = 1 [json_name = "PERSONAL_DETAILS"];

    message PersonalDetails {
      string ckyc_no = 1 [json_name = "CKYC_NO"];
      string name = 2 [json_name = "NAME"];
      string kyc_date = 3 [json_name = "KYC_DATE"];
      string gender = 4 [json_name = "GENDER"];
      string dob = 5 [json_name = "DOB"];
      string fname = 6 [json_name = "FNAME"];
      string lname = 7 [json_name = "LNAME"];
      string father_name = 8 [json_name = "FATHER_NAME"];
      string mother_name = 9 [json_name = "MOTHER_NAME"];
      string mob_num = 10 [json_name = "MOB_NUM"];
      string email = 11 [json_name = "EMAIL"];
      Address address = 12 [json_name = "ADDRESS"];
      string image_type = 13 [json_name = "IMAGE_TYPE"];
      string photo = 14 [json_name = "PHOTO"];

      message Address {
        string perm_line1 = 1 [json_name = "PERM_LINE1"];
        string perm_line2 = 2 [json_name = "PERM_LINE2"];
        string perm_line3 = 3 [json_name = "PERM_LINE3"];
        string perm_city = 4 [json_name = "PERM_CITY"];
        string perm_dist = 5 [json_name = "PERM_DIST"];
        string perm_state = 6 [json_name = "PERM_STATE"];
        string perm_country = 7 [json_name = "PERM_COUNTRY"];
        string perm_pin = 8 [json_name = "PERM_PIN"];
        string perm_poa = 9 [json_name = "PERM_POA"];
        string perm_corres_same_flag = 10 [json_name = "PERM_CORRES_SAMEFLAG"];
        string corres_line1 = 11 [json_name = "CORRES_LINE1"];
        string corres_line2 = 12 [json_name = "CORRES_LINE2"];
        string corres_line3 = 13 [json_name = "CORRES_LINE3"];
        string corres_city = 14 [json_name = "CORRES_CITY"];
        string corres_dist = 15 [json_name = "CORRES_DIST"];
        string corres_state = 16 [json_name = "CORRES_STATE"];
        string corres_country = 17 [json_name = "CORRES_COUNTRY"];
        string corres_pin = 18 [json_name = "CORRES_PIN"];
        string corres_poa = 19 [json_name = "CORRES_POA"];
      }
    }
  }
}

// used for v2 RepaymentDetails API
// api/apiintegration/v2/GetRepaymentDetails
message GetRepaymentScheduleRequest {
  string sid = 1 [json_name = "SID"];
  string loan_id = 2 [json_name = "loan_id"];
  string checksum = 3 [json_name = "Checksum"];
}

// used for v4 RepaymentDetails API
// api/apiintegration/v4/GetRepaymentDetails
message GetRepaymentScheduleRequestV4 {
  string sid = 1 [json_name = "sid"];
  string loan_id = 2 [json_name = "application_id"];
  string checksum = 3 [json_name = "checksum"];
}

// used for both v2 and v4 RepaymentDetails APIs
// 1. api/apiintegration/v2/GetRepaymentDetails
// 2. api/apiintegration/v4/GetRepaymentDetails
message GetRepaymentScheduleResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];

  message Data {
    repeated Schedule schedule = 1 [json_name = "schedule"];
  }
}


// Example JSON:
//  {
//    "application_id": "272847",
//    "installment_number": 2,
//    "due_date": "2024-01-05",
//    "due_amount": 424,
//    "principal_amount": 178,
//    "interest_amount": 246,
//    "lpi": 6.02,
//    "other_charges": 0,
//    "bounce_charges": 0,
//    "waived_charges": 0,
//    "payment_status": "Unpaid",
//    "received_date": null,
//    "received_amount": 0,
//    "paid_principal_amount": 0,
//    "paid_interest_amount": 0,
//    "paid_lpi": 0,
//    "paid_other_charges": 0,
//    "paid_bounce_charges": 0,
//    "post_payment_principal_outstanding": 0,
//    "post_payment_interest_outstanding": 0,
//    "post_payment_charges_outstanding": 6.02
//  }
message Schedule {
  int64 application_id = 1 [json_name = "application_id"];
  int32 installment_number = 2 [json_name = "installment_number"];
  string due_date = 3 [json_name = "due_date"]; //YYYY-MM-DD
  double due_amount = 4 [json_name = "due_amount"];
  double principal_amount = 5 [json_name = "principal_amount"];
  double interest_amount = 6 [json_name = "interest_amount"];
  string payment_status = 7 [json_name = "payment_status"];
  string received_date = 8 [json_name = "received_date"];
  double received_amount = 9 [json_name = "received_amount"];
  double paid_principal_amount = 10 [json_name = "paid_principal_amount"];
  double paid_interest_amount = 11 [json_name = "paid_interest_amount"];
  double lpi = 12 [json_name = "lpi"];
  double other_charges = 13 [json_name = "other_charges"];
  double bounce_charges = 14 [json_name = "bounce_charges"];
  double post_payment_principal_outstanding = 15 [json_name = "post_payment_principal_outstanding"];
  double post_payment_interest_outstanding = 16 [json_name = "post_payment_interest_outstanding"];
  // populated only in v4
  double waived_charges = 17 [json_name = "waived_charges"];
  // populated only in v4
  double paid_lpi = 18 [json_name = "paid_lpi"];
  // populated only in v4
  double paid_other_charges = 19 [json_name = "paid_other_charges"];
  // populated only in v4
  double paid_bounce_charges = 20 [json_name = "paid_bounce_charges"];
  // populated only in v4
  double post_payment_charges_outstanding = 21 [json_name = "post_payment_charges_outstanding"];
}

message GetRepaymentScheduleErrorResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  repeated Data data = 3 [json_name = "data"];

  message Data {
    repeated Schedule schedule = 1 [json_name = "schedule"];
  }
}

message ErrorResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  repeated Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {}
}

message UploadDocumentRequest {
  // epifi credentials provided by liquiloans
  string sid = 1 [json_name = "sid"];
  // Application id/ loan id we got from vendor post drawdown
  string application_id = 2 [json_name = "Applicationid"];
  // document type to be uploaded
  string document_type = 3 [json_name = "DocumentType"];
  // extra info that can be sent, Max size 255 chars
  string remarks = 4 [json_name = "Remarks"];
  // hmac hash checksum of the request
  string checksum = 5 [json_name = "Checksum"];
  // binary data of the file to be uploaded
  bytes file = 6 [json_name = "Files[]"];
}

message UploadDocumentResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    int64 application_id = 1 [json_name = "application_id"];
    int64 applicant_id = 2 [json_name = "applicant_id"];
    int64 document_type_id = 3 [json_name = "document_type_id"];
    int64 nbfc_id = 4 [json_name = "nbfc_id"];
    int64 id = 5 [json_name = "id"];
    string file_name = 6 [json_name = "file_name"];
    string file_path = 7 [json_name = "file_path"];
    string remarks = 8 [json_name = "remarks"];
    string is_password_protected = 9 [json_name = "is_password_protected"];
    string created_by = 10 [json_name = "created_by"]; // check if string
    string source = 11 [json_name = "source"]; // check if string
    string document_password = 12 [json_name = "document_password"]; // check if string
    string updated_at = 13 [json_name = "updated_at"];
    string created_at = 14 [json_name = "created_at"];
  }
}

message SaveCollectionRequest {
  string sid = 1 [json_name = "SID"];
  string loan_id = 2 [json_name = "loan_id"];
  string checksum = 3 [json_name = "Checksum"];
  repeated PaymentSchedule payment_schedule = 4 [json_name = "payment_schedule"];
  message PaymentSchedule {
    string mode_of_payment = 1 [json_name = "mode_of_payment"];
    string transaction_date = 2 [json_name = "transaction_date"];
    string payment_status = 3 [json_name = "payment_status"];
    string paid_total_amount = 4 [json_name = "paid_total_amount"];
    string voucher_no = 5 [json_name = "voucher_no"];
    string udf1 = 6 [json_name = "udf1"];
    string udf2 = 7 [json_name = "udf2"];
    string bounce_charges = 8 [json_name = "bounce_charges"];
    string collection_charges = 9 [json_name = "collection_charges"];
    string other_charges = 10 [json_name = "other_charges"];
    string due_date = 11 [json_name = "due_date"];
    // settelment_date is optional field, all the operations and decisions are taken based on transaction_date
    string settelment_date = 12 [json_name = "settelment_date"];
    string lpi_charges = 13 [json_name = "lpi_charges"];
  }
}

message SaveCollectionResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];
  message Data {
    string loan_id = 1 [json_name = "loan_id"];
  }
}

message HashGenerationForOkycRequest {
  string sid = 1 [json_name = "SID"];
  int64 application_id = 2 [json_name = "application_id"];
  string checksum = 3 [json_name = "Checksum"];
}

message HashGenerationForOkycResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];
  message Data {
    string hash = 1 [json_name = "hash"];
  }
}

message CaptchaGenerationForOkycRequest {
  string sid = 1 [json_name = "SID"];
  int64 application_id = 2 [json_name = "application_id"];
  string hash = 3 [json_name = "hash"];
  string checksum = 4 [json_name = "Checksum"];
}

message CaptchaGenerationForOkycResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];
  message Data {
    string captcha_image = 1 [json_name = "captcha_image"];
    string request_token = 2 [json_name = "request_token"];
    string captcha_txn_id = 3 [json_name = "captcha_txn_id"];
  }
}

message GenerateOtpForOkycRequest {
  string sid = 1 [json_name = "SID"];
  int64 application_id = 2 [json_name = "application_id"];
  string hash = 3 [json_name = "hash"];
  int64 uid_no = 4 [json_name = "uid_no"];
  string captcha_code = 5 [json_name = "captcha_code"];
  string request_token = 6 [json_name = "request_token"];
  string captcha_txn_id = 7 [json_name = "captcha_txn_id"];
  string checksum = 8 [json_name = "Checksum"];
}

message GenerateOtpForOkycResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];
  message Data {
    string txn_id = 1 [json_name = "txn_id"];
  }
}

message ValidateOtpForOkycRequest {
  string sid = 1 [json_name = "SID"];
  int64 application_id = 2 [json_name = "application_id"];
  int64 otp = 4 [json_name = "otp"];
  string hash = 5 [json_name = "hash"];
  string request_token = 6 [json_name = "request_token"];
  string checksum = 7 [json_name = "Checksum"];
}

message ValidateOtpForOkycResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  repeated Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];
  message Data {
  }
}

// vendor doc: https://drive.google.com/file/d/1iZTrS7lPkIuKYs-_voeoHjF6c2ghJ4qv/view?usp=drive_link
message UpdateLeadRequest {
  string sid = 1 [json_name = "SID"];
  string checksum = 2 [json_name = "Checksum"];
  string urn = 3 [json_name = "urn"];
  int64 application_id = 4 [json_name = "application_id"];
  // Loan amount
  double amount = 5 [json_name = "amount"];
  SchemeDetails scheme_details = 6 [json_name = "scheme_details"];

  message SchemeDetails {
    // possible values: Daily/Weekly/Monthly
    string installment_frequency = 1 [json_name = "installment_frequency"];
    int32 installment_tenure = 2 [json_name = "installment_tenure"];
    // amount or percentage of processing fee
    double processing_fees_value = 3 [json_name = "processing_fees_value"];
    // interest rate - mandatory
    // using wrapper type here so that it is not omitted in json marshaling in case the interest is 0
    google.protobuf.DoubleValue roi_percentage = 4 [json_name = "roi_percentage"];
    string installment_start_date = 5 [json_name = "installment_start_date"];
    // not needed, this is hard coded as per the business agreement
    double subvention_percentage = 6 [json_name = "subvention_percentage"];
    // Possible values: Standard/Percentage/FixedAmount
    string processing_fees_type = 7 [json_name = "processing_fees_type"];
    // Possible values: Declining/Flat/Balloon
    string roi_type = 8 [json_name = "roi_type"];
    // Possible values: loan_amount/product_amount
    // Product amount is applicable for zero cost EMI for consumer products like electronics
    // For our FLDG and ES cases, both of these mean the same thing
    string roi_applied_on = 9 [json_name = "roi_applied_on"];
    // Possible values: loan_amount/product_amount
    string processing_fees_customer_applied_on = 10 [json_name = "processing_fees_customer_applied_on"];
    // Possible values: Yes/No
    // implies whether to apply gst on the processing fees or not
    string processing_fees_customer_gst = 11 [json_name = "processing_fees_customer_gst"];
    // Possible values: Yes/No
    // implies whether to apply gst on the interest or not
    string roi_gst = 12 [json_name = "roi_gst"];
    // Possible values: Standard/Percentage/FixedAmount
    string gst_type = 13 [json_name = "gst_type"];
    // gst value to be applied based on gst_type
    double gst_value = 14 [json_name = "gst_value"];
  }
}

message UpdateLeadResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];

  message Data {
    string loan_id = 1 [json_name = "loan_id"];
  }
}

message CancelLeadRequest {
  string sid = 1 [json_name = "SID"];
  int64 application_id = 2 [json_name = "ApplicationId"];
  int64 applicant_id = 3 [json_name = "ApplicantId"];
  string checksum = 4 [json_name = "Checksum"];
  string timestamp = 5 [json_name = "Timestamp"];
}

message CancelLeadResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];

  message Data {
    string application_id = 1 [json_name = "ApplicationId"];
    string applicant_id = 2 [json_name = "ApplicantId"];
  }

  string code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];
}

message CancelLeadErrorResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  int64 code = 3 [json_name = "code"];
  string checksum = 4 [json_name = "checksum"];
}

message ForeClosureDetailsRequest {
  string sid = 1 [json_name = "SID"];
  int64 application_id = 2 [json_name = "application_id"];
  string checksum = 4 [json_name = "Checksum"];
  string timestamp = 5 [json_name = "Timestamp"];
}

message ForeClosureDetailsResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];

  message Data {
    string application_id = 1 [json_name = "loan_id"];
    double total_outstanding = 2 [json_name = "total_outstanding"];
    double principal_outstanding = 3 [json_name = "principal_outstanding"];
    double interest_outstanding = 4 [json_name = "interest_outstanding"];
    double penalty_charges = 5 [json_name = "penalty_charges"];
    double fees_charges = 6 [json_name = "fees_charges"];
    double other_charges = 7 [json_name = "other_charges"];
  }

  int64 code = 4 [json_name = "code"];
  string checksum = 5 [json_name = "checksum"];
}

// https://drive.google.com/file/d/1V0NVAo0hJMUvlEAQPlm-Yc0xPmNbz2uJ/view?usp=drive_link
message UpdateApplicantUdfRequest {
  string sid = 1 [json_name = "sid"];
  int64 applicant_id = 2 [json_name = "applicant_id"];
  string checksum = 3 [json_name = "checksum"];
  string udf8 = 4 [json_name = "UDF8"];
  string udf9 = 5 [json_name = "UDF9"];
  // monthly income
  string udf4 = 6 [json_name = "UDF4"];
  // income data source - AA (account aggregator)
  string udf5 = 7 [json_name = "UDF5"];
}

message UpdateApplicantUdfResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  int64 code = 3 [json_name = "code"];
  string checksum = 4 [json_name = "checksum"];
}

// https://drive.google.com/file/d/1qqavHt_DkSrdG4-VbvfnX9yUnVkq4qLU/view?usp=drive_link
message CreateRepaymentScheduleRequest {
  int64 loan_id = 1 [json_name = "loan_id"];
  string sid = 2 [json_name = "SID"];
  // possible values: Daily/Weekly/Monthly
  string repayment_frequency = 3 [json_name = "repayment_frequency"];
  int32 emi_tenure = 4 [json_name = "emi_tenure"];
  // checksum logic
  // sid||emi_start_date||emi_tenure||loan_id||EMI1 date||EMI1 interest||EMI1 other_charges||EMI1 principle||EMI1 total_amount||EMI2 date||EMI2 interest||EMI2 other_charges||EMI2 principle||EMI2 total_amount...||repayment_frequency
  string checksum = 5 [json_name = "Checksum"];
  // YYYY-MM-DD
  string emi_start_date = 6 [json_name = "emi_start_date"];
  // represents the repayment schedule with due date and due amount along with other fields
  // emi_tenure and length of payment_schedules should match
  repeated Schedule payment_schedules = 7 [json_name = "payment_schedule"];

  message Schedule {
    // YYYY-MM-DD
    string date = 1 [json_name = "date"];
    google.protobuf.DoubleValue principal = 2 [json_name = "principle"];
    google.protobuf.DoubleValue interest = 3 [json_name = "interest"];
    google.protobuf.DoubleValue other_charges = 4 [json_name = "other_charges"];
    // total sum should be equal to principal+interest+other_charges
    google.protobuf.DoubleValue total_amount = 5 [json_name = "total_amount"];
    // optional field
    google.protobuf.DoubleValue principal_outstanding = 6 [json_name = "principle_outstanding"];
  }
}

// Errors:
// If schedule is already created - http status - 400
//  {
//    "status": false,
//      "message": "Payment schedule is already available",
//      "data": {
//      "loan_id": "309392"
//       },
//      "code": 400,
//      "checksum": null
//  }
// If loan is already created - http status - 400
//  {
//    "status": false,
//      "message": "Cannot add repayment schedule for a loan that has already been disbursed",
//      "data": {},
//    "code": 400,
//    "checksum": null
//  }
message CreateRepaymentScheduleResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  int64 code = 3 [json_name = "code"];
  string checksum = 4 [json_name = "checksum"];
  Data data = 5 [json_name = "data"];
  message Data {
    string loan_id = 1 [json_name = "loan_id"];
  }
}

message GetApplicationSoaRequest {
  string sid = 1 [json_name = "sid"];
  string application_id = 2 [json_name = "application_id"];
  string checksum = 3 [json_name = "checksum"];
}

message GetApplicationSoaResponse {
  bool status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  Data data = 3 [json_name = "data"];
  int32 code = 4 [json_name = "code"];
  string timestamp = 5 [json_name = "timestamp"]; //YYYY-MM-DD HH:MM:SS
  message Data {
    string link = 1 [json_name = "link"];
    google.protobuf.Value application_id = 2 [json_name = "application_id"];
  }
}
