syntax = "proto3";

package vendors.crm.freshdesk;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendors/crm/freshdesk";
option java_package = "com.github.epifi.gamma.api.vendors.crm.freshdesk";

message Ticket {
  // Requester=User

  // Unique ID of the ticket
  uint64 id = 1 [json_name = "id"];

  // Name of the requester
  string name = 2 [json_name = "name"];

  // Phone number of the requester. If no contact exists with this phone number in Freshdesk, it will be added as a new contact.
  // If the phone number is set and the email address is not, then the name attribute is mandatory.
  string phone = 3 [json_name = "phone"];

  // Email address of the requester. If no contact exists with this email address in Freshdesk, it will be added as a new contact.
  // Mandatory for create ticket, update ticket and ...
  string email = 4 [json_name = "email"];

  // User ID of the requester. For existing contacts, the requester_id can be passed instead of the requester's email.
  uint64 requester_id = 5 [json_name = "requester_id"];

  // Subject line of the ticket
  // Optional field
  string subject = 6 [json_name = "subject"];

  // Content of the description in plain text
  // Optional field
  string description_text = 7 [json_name = "description_text"];

  // Priority of the ticket
  // By default it will be set to low if not passed while creating the ticket
  uint64 priority = 8 [json_name = "priority"];

  // Status of the ticket
  // Status will be set to CREATED by default if not passed while creating a ticket
  uint64 status = 9 [json_name = "status"];

  // agent group to which ticket is assigned to
  // default field in crm
  uint64 group_id = 10 [json_name = "group_id"];

  // crm contact/agent ID of the agent to whom the ticket has been assigned
  uint64 responder_id = 11 [json_name = "responder_id"];

  // We will be using it to add tags required for assignment like
  // Rule names, Batch names, Queue types etc
  repeated string tags = 12 [json_name = "tags"];

  // Set to true if the ticket has been deleted/trashed. Deleted tickets will not be displayed in any views except the "deleted" filter
  bool deleted = 13 [json_name = "deleted"];

  CustomFields custom_fields = 14 [json_name = "custom_fields"];

  google.protobuf.Timestamp created_at = 15 [json_name = "created_at"];
  google.protobuf.Timestamp updated_at = 16 [json_name = "updated_at"];
}

message CustomFields {

  // As part of training agents or assessing rules, sample tickets may be generated
  // This flag helps distinguish sample tickets vs live tickets
  bool cf_is_sample = 1 [json_name = "cf_is_sample"];

  string cf_review_type = 2 [json_name = "cf_review_type"];

  // Risk tickets are either manually reviewed vs auto reviewed
  // This field indicates the verdict of the ticket post the review
  string cf_verdict = 3 [json_name = "cf_verdict"];

  // Snooze expiry timestamp in seconds. Ticket will be marked on sleep/hold till expiry and will have limited visibility
  // Keeping it in seconds is important to filter tickets.
  // Ticket will be unsnoozed when either status or assigned agent changes.
  int64 cf_snoozed_till = 4 [json_name = "cf_snoozed_till"];

  // Current snooze status of the ticket. Can be snoozed or unsnoozed.
  string cf_snooze_status = 5 [json_name = "cf_snooze_status"];

  //Depricated, use model1_score and model2_score
  // Confidence score of the ticket (0- 100)
  int32 cf_confidence_score = 6 [json_name = "cf_confidence_score"];

  // it defines which analyst is worked on the ticket previously. It will help in determining who we should assign the
  // ticket once it moved back from out-call or other flow. It typically happens when case is moved around to
  // different agents in its lifecycle.
  string cf_last_assigned_analyst_email = 7 [json_name = "cf_last_assigned_analyst_email"];

  // created_at timestamp of the ticket as unix time. It can be used to filter tickets.
  // since created_at field can filter tickets only on date.
  // It might not be equal to created_at since created_at is recorded by freshdesk at ticket creation
  // and it is recorded at create ticket request.
  int64 cf_created_at_unix = 8 [json_name = "cf_created_at_unix"];

  // data for this will come from riskticket and will be reflected here, score will contain value from 0 - 100
  uint32 cf_model1_score = 9;
  string cf_model1_name = 10;
  uint32 cf_model2_score = 11;
  string cf_model2_name = 12;

  string model_selected = 13;
}

message CreateTicketRequest {

  // External ID of the requester. If no contact exists with this external ID in Freshdesk, they will be added as a new contact.
  string unique_external_id = 1 [json_name = "unique_external_id"];

  // Subject line of the ticket
  // Optional field
  string subject = 2 [json_name = "subject"];

  // HTML content of the ticket
  // Optional field
  string description = 3 [json_name = "description"];

  // Priority of the ticket
  // By default it will be set to low if not passed while creating the ticket
  uint64 priority = 4 [json_name = "priority"];

  // Status of the ticket
  // Status will be set to CREATED by default if not passed while creating a ticket
  uint64 status = 5 [json_name = "status"];

  // agent group to which ticket is assigned to
  // default field in crm
  uint64 group_id = 6 [json_name = "group_id"];

  // crm contact/agent ID of the agent to whom the ticket has been assigned
  uint64 responder_id = 7 [json_name = "responder_id"];

  // We will be using it to add tags required for assignment like
  // Rule names, Batch names, Queue types etc
  repeated string tags = 8 [json_name = "tags"];

  CustomFields custom_fields = 9 [json_name = "custom_fields"];
}

message UpdateTicketRequest {
  // Unique ID of the ticket
  uint64 id = 1 [json_name = "id"];

  // External ID of the requester. If no contact exists with this external ID in Freshdesk, they will be added as a new contact.
  string unique_external_id = 2 [json_name = "unique_external_id"];

  // Subject line of the ticket
  // Optional field
  string subject = 3 [json_name = "subject"];

  // HTML content of Ticket
  // Optional field
  string description = 4 [json_name = "description"];

  // Priority of the ticket
  // By default it will be set to low if not passed while creating the ticket
  uint64 priority = 5 [json_name = "priority"];

  // Status of the ticket
  // Status will be set to CREATED by default if not passed while creating a ticket
  uint64 status = 6 [json_name = "status"];

  // agent group to which ticket is assigned to
  // default field in crm
  uint64 group_id = 7 [json_name = "group_id"];

  // crm contact/agent ID of the agent to whom the ticket has been assigned
  uint64 responder_id = 8 [json_name = "responder_id"];

  // We will be using it to add tags required for assignment like
  // Rule names, Batch names, Queue types etc
  repeated string tags = 9 [json_name = "tags"];

  CustomFields custom_fields = 10 [json_name = "custom_fields"];
}

message GetTicketListResponse {
  // Number of tickets in response
  uint64 total = 1 [json_name = "total"];

  repeated Ticket results = 2 [json_name = "results"];
}
