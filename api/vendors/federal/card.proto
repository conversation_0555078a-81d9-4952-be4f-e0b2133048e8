// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

// CardDetails defines a set of attributes corresponding to the basic card info, status etc
message CardDetails {
  // If request type is CARD_BLOCK then CardStatus field value should be 2 and
  // if request type is CARD_ACTIVATE then CardStatus field value should be 1.
  // Note: By default, CardStatus field value should be 1.
  // TODO(anand): [Fed follow up] can there be a value other than 1 and 2?
  // TODO(anand): if the values are fixed, can this be part of request marshalling instead?
  string card_status = 1 [json_name = "CardStatus"];

  // TODO(anand): [Fed follow up] No information in api spec about this field
  string card_type = 2 [json_name = "CardType"];

  string pin_offset = 5 [json_name = "PINOffset"];
  string expiry_date = 6 [json_name = "ExpiryDate"];

  // CardSwitchType field is mandatory for below request types and values:
  // CARD_ONOFF --C
  // DOM_ONOFF --H
  // INT_ONOFF-- I
  // ECOM_ONOFF-- E
  // NFC_ONOFF --T
  // TODO(anand): if the values are fixed, can this be part of request marshalling instead?
  // TODO(anand): a char field or a string of length 1
  string card_switch_type = 3 [json_name = "CardSwitchType"];

  // CardOrInternationalOnOff field is mandatory for below request types and values
  // CARD_ONOFF --Y/N
  // DOM_ONOFF-- Y/N
  // INT_ONOFF-- Y/N
  // ECOM_ONOFF--- Y/N
  // NFC_ONOFF --Y/N
  string card_or_international_on_off = 4 [json_name = "CardOrInternationalOnOff"];
}

// Various types of limit thresholds that can be defined on a debit card
message LimitDetails {
  string ATMWithdrawalLimit = 1 [json_name = "ATMWithdrawalLimit"];
  string POSWithdrawalLimit = 2 [json_name = "POSWithdrawalLimit"];
  string POSPurchaseLimit = 3 [json_name = "POSPurchaseLimit"];
  string InternationalATMlimit = 4 [json_name = "InternationalATMlimit"];
  string InternationalPOSLimit = 5 [json_name = "InternationalPOSLimit"];
  string DepositCreditLimit = 6 [json_name = "DepositCreditLimit"];
  string TotalRefundLimit = 7 [json_name = "TotalRefundLimit"];
}

message AccountDetails {
  string CustomerId = 1 [json_name = "CustomerId"];
  string AccountNumber = 2 [json_name = "AccountNumber"];
  string MobileNumber = 3 [json_name = "MobileNumber"];
  string reserve_field_one = 4 [json_name = "ReserveField1"];
  string reserve_field_two = 5 [json_name = "ReserveField2"];
}

message CustomerDetails {
  bool PINMailerRequired = 1 [json_name = "PINMailerRequired"];
  string card_carrier_options = 2 [json_name = "CardCarrierOptions"];
  string name_line1 = 3 [json_name = "NameLine1"];
  string name_line2 = 4 [json_name = "NameLine2"];
  string address_line1 = 5 [json_name = "AddressLine1"];
  string address_lint2 = 6 [json_name = "AddressLine2"];
  string city = 7 [json_name = "City"];
  string state = 8 [json_name = "State"];
  string postal_code = 9 [json_name = "PostalCode"];
  string issue_status = 10 [json_name = "IssueStatus"];
  string service_code = 11 [json_name = "ServiceCode"];
}

message CardInformation {
  string card_unique_id = 1 [json_name = "CardUniqueId"];
  string card_status = 2 [json_name = "CardStatus"];
  string atm_withdrawal_limit = 3 [json_name = "ATMWithdrawalLimit"];
  string international_atm_limit = 4 [json_name = "InternationalATMLimit"];
  string international_pos_limit = 15 [json_name = "InternationalPOSLimit"];
  string pos_purchase_limit = 5 [json_name = "POSPurchaseLimit"];
  string pos_withdrawal_limit = 6 [json_name = "POSWithdrawalLimit"];
  string total_withdrawal_limit = 7 [json_name = "TotalWithdrawalLimit"];
  string card_on_off = 8 [json_name = "CardOnOff"];
  string international_on_off = 9 [json_name = "InternationalOnOff"];
  string ecom_on_off = 10 [json_name = "EcomOnOff"];
  string nfc_on_off = 11 [json_name = "NFCOnOff"];
  string domestic_on_off = 12 [json_name = "DomesticOnOff"];
  string customer_name = 13 [json_name = "CustomerName"];

  AccountInformation account_info = 14 [json_name = "AccountInformation"];
}

message Accounts {
  string account_type = 1 [json_name = "AccountType"];
  string link_account_number =2 [json_name ="LinkAccountNumber"];
  string account_status =3 [json_name ="AccountStatus"];
}

message AccountInformation {
  Accounts account = 1 [json_name="Accounts"];
}

message CardCreationRequest {
  // Call back URL. API will send the response back to this URL, provided by the client.
  string callback_url = 1 [json_name = "RespUrl"];

  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 2 [json_name = "SenderCode"];
  string access_id = 3 [json_name = "ServiceAccessId"];
  string access_code = 4 [json_name = "ServiceAccessCode"];

  // expected format: NEOCRCRD<DDD><yyyyMMdd><hhmmss><XXXXX (5digit sequence Number)>
  // example: NEOCRCRD1132020050814155500009
  string request_id = 5 [json_name = "RequestId"];

  // Device ID of the client that has initiated the request
  string device_id = 6 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 7 [json_name = "DeviceToken"];

  string mobile = 8 [json_name = "MobileNumber"];

  string emboss_name = 9 [json_name = "EmbossName"];
  // TODO(anand): federal to provide more details on this and allowed values
  string card_type = 10 [json_name = "CardType"];
  // TODO(anand): federal to provide more details on this and allowed values
  string card_sub_type = 11 [json_name = "CardSubType"];
  string customer_id = 12 [json_name = "CustomerId"];
  string account_number = 13 [json_name = "AccountNumber"];
}

message CardCreationResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // TODO(anand): federal documentation have a typo, "DevieToken", verify on testing
  string device_token = 2 [json_name = "DeviceToken"];
  // Request ID that is passed in the request
  string request_id = 3 [json_name = "RequestId"];

  // Response Code for the Transaction
  string response_code = 4 [json_name = "ResponseCode"];

  string response_reason = 5 [json_name = "ResponseReason"];
  string response_action = 6 [json_name = "ResponseAction"];
}

message CardCreationError {
  string code = 1 [json_name="ErrorCode"];
  string reason = 2 [json_name="Reason"];
}

// Represents the final update response on the card creation request. CardCreationResponse(above) denotes the
// ACK response for the card creation request. After the successful ACK is received, the card creation is processed
// at the Federal's end. Once Federal has processed the request, the final response is available and posted on the
// call back url. The callback url is set in the card creation request to the Federal.
// Vendor notification service processes the final response posted on the callback url.
message UpdateCardCreationRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 1 [json_name = "SenderCode"];

  // Request ID of the transaction for which the call back is initiated
  // Request ID helps uniquely identifying a transaction
  string request_id = 2 [json_name="RequestId"];

  // Device token that is issued by the federal bank at the time of client's device registration
  string device_token = 3 [json_name="DeviceToken"];

  // Unique identifier issued by Federal to identify the card. Only available if the card was created successfully.
  string card_id = 4 [json_name="CardUniqueId"];

  // Physical or the virtual card. Internally we map it to CARD_FORM
  string card_type = 5  [json_name="CardType"];

  // Denotes the category of the card. Internally we map it to CARD_CATEGORY
  // TODO(anand): the category is provided in the response i.e. by default we are provisioning a particular
  // card category. Shouldn't we get to choose this at the time of card creation?
  string card_category = 6 [json_name="CardName"];

  // Response Code for the Transaction
  string response_code = 7 [json_name="ResponseCode"];

  // Response Description for the Transaction
  string response_desc = 8 [json_name="ResponseReason"];

  // High level response codes depicting the stage of the transaction
  // PROCESSED, SUCCESS, FAILURE, SUSPECT
  string response_action = 9 [json_name="ResponseAction"];

  // Response message contains ErrorList(contains additional error information) field only if the
  // ResponseCode is ‘OBE0071’.
  repeated CardCreationError errors = 10 [json_name="ErrorList"];

  // Sensitive encrypted data for the card.
  string card_data = 11 [json_name = "CardData"];

  // Can be used for first pin set instead of otp
  string pin_set_token = 12 [json_name = "PinSetOTPTKN"];

  // Time upto which the pin set token is valid
  string token_valid_till = 13 [json_name = "TokenValidTill"];

  // name to be printed on the card
  string emboss_name = 14 [json_name = "EmbossName"];

  // card information containing the cvv of user
  CardData basic_card_info = 15;
}

message CardEnquiryRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 1 [json_name = "SenderCode"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];

  // Device ID of the client that has initiated the request
  string device_id = 4 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 5 [json_name = "DeviceToken"];

  string user_profile_id = 8 [json_name = "UserProfileId"];
  string card_unique_id = 6 [json_name = "CardUniqueId"];
  string biometric_flag = 7 [json_name = "BiometricFlag"];
  // The encrypted block consists of the secure pin/otp etc are collected by UI components of the CL (common library
  // implemented at the UI). The collected inputs are filled in in the cred block structure and encrypted.
  // The ecnrypted output is the credblock which is passed on from UI to Epifi servers to Federal servers.
  string cred_block = 9 [json_name = "CredBlock"];
  string request_id = 10 [json_name = "RequestId"];
  string request_type = 11 [json_name = "RequestType"];

  CardDetails cardDetails = 12 [json_name = "CardDetails"];
  LimitDetails limitDetails = 13 [json_name = "LimitDetails"];
  AccountDetails accountDetails = 14 [json_name = "AccountDetails"];
  CustomerDetails customerDetails = 15 [json_name = "CustomerDetails"];
}

message CardEnquiryResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];

  string request_type = 3 [json_name = "RequestType"];

  // Response Code for the Transaction
  string response_code = 5 [json_name = "ResponseCode"];

  string response_reason = 6 [json_name = "ResponseReason"];
  string response_action = 7 [json_name = "ResponseAction"];
  CardInformation card_info = 8 [json_name = "CardInformation"];
  string tran_time_stamp = 9 [json_name = "TranTimeStamp"];
  string reserve_field_one = 10 [json_name = "ReserveField1"];
  string device_token = 11 [json_name = "DeviceToken"];
}

message CardActivationRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 1 [json_name = "SenderCode"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];

  // Device ID of the client that has initiated the request
  string device_id = 4 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 5 [json_name = "DeviceToken"];
  string user_profile_id = 6 [json_name = "UserProfileId"];
  // The encrypted block consists of the secure pin/otp etc are collected by UI components of the CL (common library
  // implemented at the UI). The collected inputs are filled in in the cred block structure and encrypted.
  // The ecnrypted output is the credblock which is passed on from UI to Epifi servers to Federal servers.
  string cred_block = 7 [json_name = "CredBlock"];
  string request_id = 8 [json_name = "RequestId"];
  string request_type = 9 [json_name = "RequestType"];
  CardDetails cardDetails = 10 [json_name = "CardDetails"];
  AccountDetails accountDetails = 11 [json_name = "AccountDetails"];
  LimitDetails limitDetails = 12 [json_name = "LimitDetails"];
  CustomerDetails customerDetails = 13 [json_name = "CustomerDetails"];
}

message CardActivationResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];

  string request_type = 3 [json_name = "RequestType"];

  // Response Code for the activation
  string response_code = 4 [json_name = "ResponseCode"];

  string response_reason = 5 [json_name = "ResponseReason"];
  string response_action = 6 [json_name = "ResponseAction"];
}

// Request type defines list of various requests that collectively facilitates
// the card provisioning.
//
// These are defined by Federal bank to identify the type of request. All the card apis
// contain this as a mandatory field. Some set of apis for which the specification is
// common (for example: status check api), the request type becomes the identifier along
// with a request-id (a unique id).
enum RequestType {
  REQUEST_TYPE_UNSPECIFIED = 0;
  CARD_ENQUIRY = 1;
  CARD_BLOCK = 3;
  CARD_ONOFF = 4;
  DOM_ONOFF = 5;
  INT_ONOFF = 6;
  ECOM_ONOFF = 7;
  NFC_ONOFF = 8;
  CARD_ACTIVATE = 9;
}

// PIN MANAGEMENT SERVICE
enum PinRequestType {
  PIN_REQUEST_TYPE_UNSPECIFIED = 0;
  PIN_SET = 1;
  PIN_CHANGE = 2;
  PIN_RESET = 3;
  PIN_VALIDATE = 4;
}

message PinManagementRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 1 [json_name = "SenderCode"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];

  // Device ID of the client that has initiated the request
  string device_id = 4 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 5 [json_name = "DeviceToken"];
  string user_profile_id = 6 [json_name = "UserProfileId"];
  string card_unique_id = 7 [json_name = "CardUniqueId"];

  string token_validation = 8 [json_name = "TokenValidation"];

  string pin_set_token = 9 [json_name = "PinSetToken"];

  // The encrypted block consists of the secure pin/otp etc are collected by UI components of the CL (common library
  // implemented at the UI). The collected inputs are filled in in the cred block structure and encrypted.
  // The ecnrypted output is the credblock which is passed on from UI to Epifi servers to Federal servers.
  string cred_block = 10 [json_name = "CredBlock"];

  // Format: NEOPNMGT<DDD><yyyyMMdd><hhmmss><XXXXX (5digit sequence Number)>
  string request_id = 11 [json_name = "RequestId"];

  // One amongst the PIN Request type defined above
  string request_type = 12 [json_name = "RequestType"];

  string customer_id = 13 [json_name = "CustomerId"];
  // TODO(anand): no idea about this field. Mark optional and set to empty in sample response
  string card_status = 14 [json_name = "CardStatus"];
  string mobile = 15 [json_name = "MobileNumber"];
}

message PinManagementResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // Device token that is issued by the federal bank at the time of client's device registration
  // TODO(anand): federal documentation have a typo, "DevieToken", verify on testing
  string device_token = 2 [json_name="DeviceToken"];
  // Request ID that is passed in the request
  string request_id = 3 [json_name = "RequestId"];

  // 16 digit card number. The string is masked to only reveal first few and last few digits.
  // Available only if the the request type is PIN_CHANGE.
  string card_unique_id = 4 [json_name="CardUniqueId"];

  // Response Code for the Transaction
  string response_code = 5 [json_name = "ResponseCode"];
  string response_reason = 6 [json_name = "ResponseReason"];
  string response_action = 7 [json_name = "ResponseAction"];
  string tran_time_stamp = 8 [json_name = "TranTimeStamp"];
}

message CardControlRequest {
    // Epifi's credentials provided by Federal bank
    // Credentials will be different for UAT and PROD
    // Credentials will be provided by Federal Bank's API support team
    // Credentials include user_id, password and sender code
    string sender_code = 1 [json_name = "SenderCode"];
    string access_id = 2 [json_name = "ServiceAccessId"];
    string access_code = 3 [json_name = "ServiceAccessCode"];

    // Device ID of the client that has initiated the request
    string device_id = 4 [json_name = "DeviceId"];
    // Device token that is issued by the federal bank at the time of device registration
    string device_token = 5 [json_name = "DeviceToken"];
    string user_profile_id = 6 [json_name = "UserProfileId"];
    // The encrypted block consists of the secure pin/otp etc are collected by UI components of the CL (common library
    // implemented at the UI). The collected inputs are filled in in the cred block structure and encrypted.
    // The ecnrypted output is the credblock which is passed on from UI to Epifi servers to Federal servers.
    string cred_block = 7 [json_name = "CredBlock"];
    string card_unique_id = 8 [json_name = "CardUniqueId"];
    string biometric_flag = 9 [json_name = "BiometricFlag"];
    string token_validation = 10 [json_name = "TokenValidation"];
    string pin_set_token = 11 [json_name = "PinSetToken"];
    string request_id = 12 [json_name = "RequestId"];
    string request_type = 13 [json_name = "RequestType"];
    CardDetails cardDetails = 14 [json_name = "CardDetails"];
    LimitDetails limitDetails = 15 [json_name = "LimitDetails"];
    AccountDetails accountDetails = 16 [json_name = "AccountDetails"];
    CustomerDetails customerDetails = 17 [json_name = "CustomerDetails"];
}

message CardControlResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];

  string request_type = 3 [json_name = "RequestType"];

  // Response Code for the activation
  string response_code = 4 [json_name = "ResponseCode"];

  string response_reason = 5 [json_name = "ResponseReason"];
  string response_action = 6 [json_name = "ResponseAction"];
  string device_token = 7 [json_name = "DeviceToken"];
  string tran_time_stamp = 8 [json_name = "TranTimeStamp"];
}

// Senstive data for the card. Contains 16/4/3.
message CardData {
  string card_number = 1 [json_name = "cardNumber"];
  string expiry = 2 [json_name = "exp"];
  string cvv = 3 [json_name = "cvv"];
  // masked card number will be set before tokenization of card_number field.
  string masked_card_number = 4;
}

message CardCVVEnquiryRequest {
  string sender_code = 1 [json_name = "SenderCode"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];
  // Device ID of the client that has initiated the request
  string device_id = 4 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 5 [json_name = "DeviceToken"];
  string card_unique_id = 6 [json_name = "CardUniqueId"];
  string biometric_flag = 7 [json_name = "BiometricFlag"];
  string cred_block = 8 [json_name = "CredBlock"];
  string request_id = 9 [json_name = "RequestId"];
  string customer_id = 10 [json_name = "CustomerId"];
  string mobile_number = 11 [json_name = "MobileNumber"];
}

message CardCVVEnquiryResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string device_token = 2 [json_name = "DeviceToken"];
  string request_id = 3 [json_name = "RequestId"];
  string card_data = 4 [json_name = "CardData"];
  string response_code = 5 [json_name = "ResponseCode"];
  string response_reason = 6 [json_name = "ResponseReason"];
  string response_action = 7 [json_name = "ResponseAction"];
  string timestamp = 8 [json_name = "TranTimeStamp"];
}

message CardLimitEnquiryRequest {
  string sender_code = 1 [json_name = "SenderCode"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];
  // Device ID of the client that has initiated the request
  string device_id = 4 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 5 [json_name = "DeviceToken"];
  string cred_block = 6 [json_name = "CredBlock"];
  string card_unique_id = 7 [json_name = "CardUniqueId"];
  string biometric_flag = 8 [json_name = "BiometricFlag"];
  string request_id = 9 [json_name = "RequestId"];
  CardDetails card_details = 10 [json_name = "CardDetails"];
  AccountDetails account_details = 11 [json_name = "AccountDetails"];
}

message CardEnquiryLimitDetails {
  string max_atm_withdraw_dom_limit = 1 [json_name = "MaxAllwdATMWithDrawlDomLimit"];
  string curr_atm_withdraw_dom_limit = 2 [json_name = "CurrentAllwdATMWithDrawlDomLimit"];
  string max_atm_withdraw_intl_limit = 3 [json_name = "MaxAllwdATMWithDrawlIntlLimit"];
  string curr_atm_withdraw_intl_limit = 4 [json_name = "CurrentAllwdATMWithDrawlIntlLimit"];
  string max_pos_purchase_dom_limit = 5 [json_name = "MaxAllwdPOSPurchaseDomLimit"];
  string curr_pos_purchase_dom_limit = 6 [json_name = "CurrentAllwdPOSPurchaseDomLimit"];
  string max_pos_purchase_intl_limit = 7 [json_name = "MaxAllwdPOSPurchaseIntlLimit"];
  string curr_pos_purchase_intl_limit = 8 [json_name = "CurrentAllwdPOSPurchaseIntlLimit"];
  string max_ecom_dom_limit = 9 [json_name = "MaxAllwdECOMDomLimit"];
  string curr_ecom_dom_limit = 10 [json_name = "CurrentAllwdECOMDomLimit"];
  string max_ecom_intl_limit = 11 [json_name = "MaxAllwdECOMIntlLimit"];
  string curr_ecom_intl_limit = 12 [json_name = "CurrentAllwdECOMIntlLimit"];
  string max_nfc_dom_limit = 13 [json_name = "MaxAllwdNFCDomLimit"];
  string curr_nfc_dom_limit = 14 [json_name = "CurrentAllwdNFCDomLimit"];
  string max_nfc_intl_limit = 15 [json_name = "MaxAllwdNFCIntlLimit"];
  string curr_nfc_intl_limit = 16 [json_name = "CurrentAllwdNFCIntlLimit"];
}

message CardLimitEnquiryResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];
  string response_code = 5 [json_name = "ResponseCode"];
  string response_reason = 6 [json_name = "ResponseReason"];
  string response_action = 7 [json_name = "ResponseAction"];
  CardInformation card_information = 8 [json_name = "CardInformation"];
  CardEnquiryLimitDetails card_limit_details = 9 [json_name = "CardLimitDetails"];
}

message CardUpdateLimitDetails {
  string updated_atm_withdrawal_dom_limit = 1 [json_name = "UpdatedATMWithDrawlDomLimit"];
  string updated_atm_withdrawal_intl_limit = 2 [json_name = "UpdatedATMWithDrawlIntlLimit"];
  string updated_pos_purchase_dom_limit = 3 [json_name = "UpdatedPOSPurchaseDomLimit"];
  string updated_pos_purchase_intl_limit = 4 [json_name = "UpdatedPOSPurchaseIntlLimit"];
  string updated_ecom_dom_limit = 5 [json_name = "UpdatedECOMDomLimit"];
  string updated_ecom_intl_limit = 6 [json_name = "UpdatedECOMIntlLimit"];
  string updated_nfc_dom_limit = 7 [json_name = "UpdatedNFCDomLimit"];
  string updated_nfc_intl_limit = 8 [json_name = "UpdatedNFCIntlLimit"];
}

message CardLimitUpdateRequest {
  string sender_code = 1 [json_name = "SenderCode"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];
  // Device ID of the client that has initiated the request
  string device_id = 4 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 5 [json_name = "DeviceToken"];
  string cred_block = 6 [json_name = "CredBlock"];
  string card_unique_id = 7 [json_name = "CardUniqueId"];
  string biometric_flag = 8 [json_name = "BiometricFlag"];
  string request_id = 9 [json_name = "RequestId"];
  string card_limit_increase_or_decrease = 10 [json_name = "CardLimitIncreaseOrDecrease"];
  CardDetails card_details = 11 [json_name = "CardDetails"];
  AccountDetails account_details = 12 [json_name = "AccountDetails"];
  CardUpdateLimitDetails card_limit_details = 13 [json_name = "CardLimitDetails"];
}

message CardLimitUpdateResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];
  string response_code = 5 [json_name = "ResponseCode"];
  string response_reason = 6 [json_name = "ResponseReason"];
  string response_action = 7 [json_name = "ResponseAction"];
}

message CardDeliveryTrackingRequest {
  string sender_code = 1 [json_name = "SenderCode"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];
  string request_id = 4 [json_name = "RequestId"];
  // Device ID of the client that has initiated the request
  string device_id = 5 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 6 [json_name = "DeviceToken"];
  string mobile_number = 7 [json_name = "MobileNumber"];
  string card_unique_id = 8 [json_name = "CardUniqueId"];
  string cred_block = 9 [json_name = "CredBlock"];
}

message CardDeliveryTrackingResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];
  // Card Generated Date in DD-MM-YYYY format
  string card_gen_date = 5 [json_name = "cardGenDate"];
  // Card Dispatched Date in DD-MM-YYYY format
  string card_disp_date = 6 [json_name = "cardDispDate"];
  // Airway Bill Number
  string awb = 7 [json_name = "awb"];
  // If Card Return to Origin , Card Return Date in DD-MM-YYYY format
  string card_rtn_date = 8 [json_name = "cardRtnDate"];
  // Courier Agency Name
  string agency_name = 9 [json_name = "agencyName"];
  // Dispatch Type – BRANCH  /CUSTOMER/WELCOME KIT
  string disp_type = 10 [json_name = "dispType"];
  string remark = 11 [json_name = "remark"];
  string response = 12 [json_name = "Response"];
  string reason = 13 [json_name = "Reason"];
  string perso_vendor_name = 14 [json_name = "persoVendorName"];
}

message  PhysicalCardDispatchRequest {
  // Call back URL. API will send the response back to this URL, provided by the client.
  string callback_url = 1 [json_name = "RespUrl"];
  // Epifi's credentials provided by Federal bank
  string sender_code = 2 [json_name = "SenderCode"];
  string access_id = 3 [json_name = "ServiceAccessId"];
  string access_code = 4 [json_name = "ServiceAccessCode"];
  string request_id = 5 [json_name = "RequestId"];
  // Device ID of the client that has initiated the request
  string device_id = 6 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 7 [json_name = "DeviceToken"];
  string mobile = 8 [json_name = "MobileNumber"];
  string card_unique_id = 9 [json_name = "CardUniqueId"];
  string customer_id = 10 [json_name = "CustomerId"];
  string accountNumber = 11 [json_name = "AccountNumber"];
}

message  PhysicalCardDispatchResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];
  // Response Code for the Transaction
  string response_code = 5 [json_name = "ResponseCode"];
  string response_reason = 6 [json_name = "ResponseReason"];
  string response_action = 7 [json_name = "ResponseAction"];
}

message UpdateDispatchPhysicalCardStatusRequest {
  // Epifi's credentials provided by Federal bank
  string sender_code = 1 [json_name = "SenderCode"];
  // Device token that is issued by the federal bank at the time of client's device registration
  string device_token = 2 [json_name="DeviceToken"];
  string request_id = 3 [json_name="RequestId"];
  string card_unique_id = 4 [json_name = "CardUniqueId"];
  string response_code = 5 [json_name="ResponseCode"];
  string response_reason = 6 [json_name = "ResponseReason"];
  string response_action = 7 [json_name = "ResponseAction"];
}

message ConsolidatedCardControlRequest {
  string sender_code = 1 [json_name = "SenderCode"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];
  string device_id = 4 [json_name = "DeviceId"];
  string device_token = 5 [json_name = "DeviceToken"];
  // encrypted pin
  string cred_block = 6 [json_name = "CredBlock"];
  // bank identifier for a card
  string card_unique_id = 7 [json_name = "CardUniqueId"];
  string biometric_flag = 8 [json_name = "BiometricFlag"];
  // flag to determine if token validation is to be used for changing controls
  string token_validation = 9 [json_name = "TokenValidation"];
  string request_id = 10 [json_name = "RequestId"];
  // constant to determine request type
  // For consolidated card controls the value will always be `NEO_FHM_ONOFF_CONSL`
  string request_type = 11 [json_name = "RequestType"];
  message CardDetails {
    // constant value "1"
    // TODO(priyansh) : Check with Federal regarding what does this specify
    string card_status = 1 [json_name = "CardStatus"];
    // constant value "00"
    // TODO(priyansh) : Check with Federal regarding what does this specify
    string card_type = 2 [json_name = "CardType"];
    // Field to determine if card is to be suspended or unsuspended
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string card_on_off = 3 [json_name = "FHM_CARD_ONOFF"];
    // Field to determine if card is to enabled for domestic txns
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string domestic_on_off = 4 [json_name = "FHM_DOM_ONOFF"];
    // Field to determine if card is to enabled for international txns
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string intl_on_off = 5 [json_name = "FHM_INTL_ONOFF"];
    // Field to determine if card is to enabled for e-commerce txns
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string ecom_on_off = 6 [json_name = "FHM_ECOM_ONOFF"];
    // Field to determine if card is to enabled for contactless txns
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string nfc_on_off = 7 [json_name = "FHM_NFC_ONOFF"];
    // Field to determine if tokenized card is to enabled for qr txns
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string token_qr_on_off = 8 [json_name = "FHM_TOKEN_QR_ONOFF"];
    // Field to determine if tokenized card is to enabled for contactless txns
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string token_nfc_on_off = 9 [json_name = "FHM_TOKEN_NFC_ONOFF"];
    // Field to determine if tokenized card is to enabled for mst txns
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string token_mst_on_off = 10 [json_name = "FHM_TOKEN_MST_ONOFF"];
    // Field to determine if tokenized card is to enabled for cof txns
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string token_cof_on_off = 11 [json_name = "FHM_TOKEN_COF_ONOFF"];
    // Field to determine if card is to enabled for tokenization
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string token_app_on_off = 12 [json_name = "FHM_TOKEN_APP_ONOFF"];
    // Field to determine if card is to enabled for atm txns
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string atm_on_off = 13 [json_name = "ATM_ONOFF"];
    // Field to determine if card is to enabled for pos txns
    // Values can be "Y", "N" or " "
    // If no changes are to be made here space will be used
    string pos_on_off = 14 [json_name = "POS_ONOFF"];
  }
  CardDetails card_details = 12 [json_name = "CardDetails"];
  AccountDetails account_details = 13 [json_name = "AccountDetails"];
}

message ConsolidatedCardControlResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string tran_timestamp = 4 [json_name = "TranTimeStamp"];
  string request_type = 5 [json_name = "RequestType"];
  string response_code = 6 [json_name = "ResponseCode"];
  string response_reason = 7 [json_name = "ResponseReason"];
  string response_action = 8 [json_name = "ResponseAction"];
  message CardInformation {
    string card_unique_id = 1 [json_name = "CardUniqueId"];
  }
  CardInformation card_information = 9 [json_name = "CardInformation"];
}

// TODO(Sanskriti): Add proper documentation over req params once received from federals' end
message ProcessCardSwitchNotificationRequest {
  // Sender code value
  string sender_code = 1 [json_name = "SenderCode"];
  // Mode of Transaction if ECOM/POS or ATM
  string transaction_mode = 2 [json_name = "transaction_mode"];
  // Unique reference id
  string request_id = 3 [json_name = "RequestId"];
  // Transaction execution date timestamp
  string transaction_executed_timestamp = 4 [json_name = "Transaction_execution_datetimestamp"];
  // User account number.
  string account_number = 5 [json_name = "User_account_number"];
  // Transaction amount
  string transaction_amount = 6 [json_name = "Transaction_amount"];
  // Indicates the type of message
  string transaction_type = 7 [json_name = "Transaction_type"];
  string transaction_code = 8 [json_name = "Transaction_Code"];
  // ARN/RRN details
  string arn = 9 [json_name = "ARN_or_RRN"];
  // In case of reversal original transaction details will be present. If not available then it will be blank.
  // For refunds the same will not be available
  string original_txn_id = 10 [json_name = "Original_txn_id"];
  // Debit Card a value to be given (01 or DC etc..)
  string remitter_instrument_type = 11 [json_name = "Remitter_instrument_type"];
  // A fixed value received from federals' end
  string remitter_details = 12 [json_name = "Remitter_details"];
  // country code where transaction is made
  string country_code = 13 [json_name = "Country_Code"];
  // remarks for the transaction, this will not be available for switch notifications
  string transaction_remarks = 14 [json_name = "Transaction_remarks"];
  // name of the merchant, will only be available in case of POS/ECOMM transactions
  string merchant_name = 15 [json_name = "Merchant_name"];
  // merchant id
  string merchant_id = 16 [json_name = "Merchant_ID"];
  // this will not be available for switch notifications
  string payment_gateway = 17 [json_name = "Payment_Gateway"];
  // this will not be available for switch notifications
  string sub_merchant_id = 18 [json_name = "Sub_Merchant_ID"];
  // terminal id
  string terminal_id = 19 [json_name = "Terminal_ID"];
  // Acquiring Institution Id shall be provided.
  string acquiring_bank = 20 [json_name = "Acquiring_bank"];
  // mcc code, available for POS and ECOM transactions.
  string mcc = 21 [json_name = "MCC"];
  string merchant_location = 22 [json_name = "Merchant_Location"];
  // A code indicating the manner in which this transaction initiated
  string pos_service_condition_code = 23 [json_name = "POS_Service_Condition_Code"];
  // response code for the transaction
  string transaction_response_code = 24 [json_name = "Transaction_response_code"];
  // POS Entry mode available in the data
  string pos_type = 25 [json_name = "POS_type"];
  // AUTH ID available. Can be used for identification of transaction
  string auth_id = 26 [json_name = "Auth_ID"];
  // Card Unique ID for Fintech Team
  string card_number = 27 [json_name = "Cardnumber"];
  string transaction_time = 28 [json_name = "Transaction_Time"];
  // field for identifying ecom/pos transaction
  string service_entry_mode = 29 [json_name = "Service_Entry_Mode"];
  // code for identifying atm markup transaction
  string atm_markup_transaction = 30 [json_name = "Auth_User_Fld1"];
  // code for identifying ecom/pos markup transaction
  string ecom_pos_markup_transaction = 31 [json_name = "Pauth_Txn_Id_Filler_Fld2"];
  // in some of the notifications we are getting `Transaction_Time` as `Transation_Time`
  string transation_time = 32 [json_name = "Transation_Time"];
  // The original currency in which the transaction happened. For ex: 840(USA)
  string org_txn_currency_code = 33 [json_name = "Original_Txn_Currency_Cde"];
  // Whether customer paid by tapping card or not. Possible values: Y and N
  // Card_Tap = Y if customer paid by tapping card, otherwise N.
  string card_tap = 34 [json_name = "Card_Tap"];
  // Whether customer paid by tapping device or not. Possible values: Y and N
  // Device_Tap = NFC/ MST(VISA) if customer paid by tapping device, otherwise N.
  string device_tap = 35 [json_name = "Device_Tap"];
  // The value of transaction in the original currency(Need ISO decimal conversion)
  string org_currency_value = 36 [json_name = "Original_Crncy_Value"];
  // PinCode of the ATM, if transaction was via ATM mode.
  string geocode = 37 [json_name = "Geocode"];
}

message CollectDCIssuanceFeeRequest {
  // unique id for each request
  string request_id = 1 [json_name = "req_unique_id"];
  // Partner id
  string partner_id = 2 [json_name = "req_partner_id"];
  // Sender id
  string sender_id = 3 [json_name = "req_sender_id"];
  // Charge type
  string charge_type = 4 [json_name = "req_charge_type"];
  // account number
  int64 account_number = 5 [json_name = "req_accno"];
  // card issuance fee without gst
  int64 fee_without_gst = 6 [json_name = "req_fee_without_gst"];
  // transaction detail
  string transaction_particular = 7 [json_name = "tran_particulars"];
}

message CollectDCIssuanceFeeResponse {
  // eg. Request processed for non-KL state. IGST calculated.
  string response_description = 1 [json_name = "var_message"];
  // 200- Success
  string response_code = 2 [json_name = "var_responsecode"];
  // reference number is the same as the unique id sent in the request, used for checking status
  string reference_number = 3 [json_name = "var_reference_number"];

  // Federal is sending json_name in uppercase in some cases(failure) and lower case in some cases(success)
  // eg. Request does not meet prerequisite
  string response_description2 = 4 [json_name = "VAR_MESSAGE"];
  // eg. 777- Wrong request body.
  string response_code2 = 5 [json_name = "VAR_RESPONSECODE"];
}

message CheckDCIssuanceFeeStatusRequest {
  // reference number is the same as the unique id sent in the request of CollectDCIssuanceFeeRequest
  string reference_number = 1 [json_name = "req_unique_id"];
  // Partner id
  string partner_id = 2 [json_name = "req_partner_id"];
  // Sender id
  string sender_id = 3 [json_name = "req_sender_id"];
  // Charge type
  string charge_type = 4 [json_name = "req_charge_type"];
  // account number
  int64 account_number = 5 [json_name = "req_accno"];
  // Pass “ENQUIRY” as default value
  string api_name = 6 [json_name = "apiname"];
}

message CheckDCIssuanceFeeStatusResponse {
  // eg. SUCCESS
  // eg. NO DATA FOUND
  string status = 1 [json_name = "status"];
  // error code, if any error
  string error_code = 2 [json_name = "error_code"];
  // reference number is the same as the unique id sent in the request of CollectDCIssuanceFeeRequest
  string reference_number = 3 [json_name = "var_reference_number"];
  // tran_id
  string tran_id = 4 [json_name = "tran_id"];
}

