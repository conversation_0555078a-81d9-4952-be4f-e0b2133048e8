syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

// https://docs.google.com/document/d/1m5WAudRO-YQqNY6jIsx6tvCzilmh928w/edit :- document link
message LoanEligibilityRequestLentra {
  Metadata metadata = 2 [json_name = "metadata"];
  LoanApplication loan_application = 3 [json_name = "loanApplication"];
  Integrations integrations = 4 [json_name = "integrations"];
  string s_dedupe_id = 5 [json_name = "sDedupeId"];
}

message LoanApplication {
  string application_id = 1 [json_name = "applicationId"];
  string application_type = 2 [json_name = "applicationType"];
  repeated string loan_type = 3 [json_name = "loanType"];
  string agency_id = 4 [json_name = "agencyId"];
  string lender = 5 [json_name = "lender"];
  string source = 6 [json_name = "source"];
  string merchant_id = 7 [json_name = "merchantId"];
  string channel_id = 8 [json_name = "channelId"];
  string category_id = 9 [json_name = "categoryId"];
  string currency = 10 [json_name = "currency"];
  double loan_amount = 11 [json_name = "loanAmount"];
  Borrower borrower = 12 [json_name = "borrower"];
  string applicant_type = 13 [json_name = "applicantType"];
}

message Borrower {
  repeated CustomerIdentifiers customer_identifiers = 1 [json_name = "customerIdentifiers"];
  string name = 2 [json_name = "name"];
  string customer_id = 3 [json_name = "customerId"];
  string source_ip_v4 = 4 [json_name = "sourceIPv4"];
  double income_estimated = 5 [json_name = "incomeEstimated"];
  ContactDetailsLentra contact_details = 6 [json_name = "contactDetails"];
  string dob = 7 [json_name = "dob"];
  string gender = 8 [json_name = "gender"];
}

message EmploymentDetails {
  string employment_type = 1 [json_name = "employmentType"];
  string employer_name = 2 [json_name = "employerName"];
  string employment_category = 3 [json_name = "employmentCategory"];
  int64 net_monthly_salary = 4 [json_name = "netMonthlySalary"];
  int64 gross_monthly_salary = 5 [json_name = "grossMonthlySalary"];
}

message ContactDetailsLentra {
  repeated AddressLentra addresses = 1 [json_name = "addresses"];
  repeated EmailList email_lists = 2 [json_name = "emailList"];
  repeated PhoneList phone_lists = 3 [json_name = "phoneList"];
}
message AddressLentra {
  string co = 1 [json_name = "co"];
  string hba = 2 [json_name = "hba"];
  string srl = 3 [json_name = "srl"];
  string landmark = 4 [json_name = "landmark"];
  string als = 5 [json_name = "als"];
  string vtc = 6 [json_name = "vtc"];
  string pincode = 7 [json_name = "pincode"];
  string po = 8 [json_name = "po"];
  string district = 9 [json_name = "district"];
  string state = 10 [json_name = "state"];
  string country = 11 [json_name = "country"];
  string uri = 12 [json_name = "uri"];
  string latitude = 13 [json_name = "latitude"];
  string longitude = 14 [json_name = "longitude"];
  string type = 15 [json_name = "type"];
}

message PhoneList {
  string phone_type = 1 [json_name = "phoneType"];
  string phone_number = 2 [json_name = "phoneNumber"];
  string country_code = 3 [json_name = "countryCode"];
}

message EmailList {
  string email_type = 1 [json_name = "emailType"];
  string email_id = 2 [json_name = "emailId"];
}

message CustomerIdentifiers {
  string id_name = 1 [json_name = "idName"];
  string id_value = 2 [json_name = "idValue"];
}

message Metadata {
  string version = 1 [json_name = "version"];
  string timestamp = 2 [json_name = "timestamp"];
  string trace_id = 3 [json_name = "traceId"];
}

message Integrations {
  string multibureau = 1 [json_name = "multibureau"];
  string fraud_check = 2 [json_name = "fraudCheck"];
  string limit_check = 3 [json_name = "limitCheck"];
}

/*
Sample response:
{
    "dateTime": "Tue May 13 18:34:01 IST 2025",
    "referenceId": "FBL_PLxxxxxEPIFI_NTB",
    "responseDetails":
    {
        "response":
        {
            "approvedAmount": 500000,
            "cibilScore": "00795",
            "cibilStatus": "SUCCESS",
            "eligibilityDecision": "Approved",
            "eligibilityStatus": "SUCCESS",
            "emi": 0,
            "experianScoreCardScore": "",
            "fraudCheckDecision": "NO_MATCH",
            "fraudCheckStatus": "SUCCESS",
            "hunterScore": "0",
            "max_eligible_emi": 121920,
            "processingFee": 0,
            "roi": 12.25,
            "scoringResponseWithCoapp":
            {
                "APPLICANT-RESULT":
                [
                    {
                        "APPLICANT-ID": "",
                        "DECISION": "Approved",
                        "DERIVED_FIELDS":
                        {
                            "CALCULATED_FIELDS$ABOVE_3LACS": 203200,
                            "CALCULATED_FIELDS$CC_INCOME_ABOVE_3LACS": 203200,
                            "CALCULATED_FIELDS$CC_INCOME_ABOVE_3LACS_TEST": 203200,
                            "CALCULATED_FIELDS$CC_INCOME_BELOW_3LACS": 290285.71,
                            "CALCULATED_FIELDS$CC_INCOME_BELOW_3LACS_TEST": 290285.71,
                            "CALCULATED_FIELDS$CREDIT_VS_HIGH_CREDIT_MAX": 1546172,
                            "CALCULATED_FIELDS$EMI_INTEREST_RATE": 0.010208,
                            "CALCULATED_FIELDS$EMI_INTEREST_RATE_EPIFI": 0.010833,
                            "CALCULATED_FIELDS$FINAL_ELIG_EXPSCORE_EPIFI": 500000,
                            "CALCULATED_FIELDS$FINAL_ELIG_EXP_MULTIPLIE_EPIFI": 500000,
                            "CALCULATED_FIELDS$MAX_FOIR": 121920,
                            "CALCULATED_FIELDS$MAX_FOIR_EPIFI": 121920,
                            "CALCULATED_FIELDS$MAX_FOIR_TEST": 121920,
                            "CALCULATED_FIELDS$MAX_INCOME": 203200,
                            "CALCULATED_FIELDS$MAX_LOAN_ELIGIBLE_TEST": 4608310.02,
                            "CALCULATED_FIELDS$MAX_LOAN__ELIGIBLE": 4608310.02,
                            "CALCULATED_FIELDS$MAX_LOAN__ELIGIBLE_EPIFI": 4544624.76,
                            "CALCULATED_FIELDS$MAX_PERMISSIBLE_EMI": 121920,
                            "CALCULATED_FIELDS$MAX_PERMISSIBLE_EMI_EPIFI": 121920,
                            "CALCULATED_FIELDS$MAX_PERMISSIBLE_EMI_TEST": 121920,
                            "CALCULATED_FIELDS$MAX_TENURE": 0,
                            "CALCULATED_FIELDS$PL_AMT_AVLD_NEGATIVE": 0,
                            "CALCULATED_FIELDS$TOTAL_LIMIT_CHECK_OUTPUT": 0,
                            "CALCULATED_FIELDS$TOTAL_UNSEC_PLCC": 0,
                            "CALCULATED_FIELDS$UNSEC_LIMIT_CHECK_FINAL_ELIGI": 0,
                            "CALCULATED_FIELDS$UPTO_3LAC": 290285.71,
                            "CONSTANT_FIELDS$CURRENT_DATE": "********",
                            "CUSTOM_FIELDS$2SMA0_12": 0,
                            "CUSTOM_FIELDS$30DPD_12MONTHS": "Y",
                            "CUSTOM_FIELDS$30DPD_3MONTHS": "Y",
                            "CUSTOM_FIELDS$60DPD_24MONTHS": "Y",
                            "CUSTOM_FIELDS$60DPD_6MONTHS": "Y",
                            "CUSTOM_FIELDS$90DPD": "Y",
                            "CUSTOM_FIELDS$ACCOUNT_STATUS": "Y",
                            "CUSTOM_FIELDS$ACTIVE_NPA_CHECK": "Y",
                            "CUSTOM_FIELDS$ACTIVE_PL_10KPLUS": 0,
                            "CUSTOM_FIELDS$AC_CLOSE_DATE":
                            [
                                {
                                    "ACCT_DT_CLOSED": "********"
                                },
                                {},
                                {},
                                {},
                                {
                                    "ACCT_DT_CLOSED": "********"
                                },
                                {
                                    "ACCT_DT_CLOSED": "********"
                                },
                                {
                                    "ACCT_DT_CLOSED": "********"
                                },
                                {
                                    "ACCT_DT_CLOSED": "********"
                                },
                                {
                                    "ACCT_DT_CLOSED": "11012020"
                                },
                                {
                                    "ACCT_DT_CLOSED": "14112015"
                                },
                                {
                                    "ACCT_DT_CLOSED": "19052012"
                                },
                                {
                                    "ACCT_DT_CLOSED": "07012015"
                                },
                                {
                                    "ACCT_DT_CLOSED": "16022008"
                                },
                                {
                                    "ACCT_DT_CLOSED": "09072007"
                                },
                                {
                                    "ACCT_DT_CLOSED": "10092022"
                                },
                                {
                                    "ACCT_DT_CLOSED": "18082010"
                                },
                                {
                                    "ACCT_DT_CLOSED": "12062010"
                                },
                                {
                                    "ACCT_DT_CLOSED": "24052006"
                                }
                            ],
                            "CUSTOM_FIELDS$AC_OPEN_DATE":
                            [
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "07022025"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "10102024"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "25062024"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "27032023"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "03122022"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "30082022"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "15092021"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "07082017"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "11022017"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "03022012"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "05042011"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "30092008"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "28122007"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "17102006"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "26072006"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "10062006"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "11032009"
                                },
                                {
                                    "ACCT_DT_OPENED_DISBURSED": "06092005"
                                }
                            ],
                            "CUSTOM_FIELDS$AGE_IN_DAYS": 15340,
                            "CUSTOM_FIELDS$BUREAU_ADDRESS_OUTPUT":
                            [
                                {
                                    "ADDR_CATEGORY": "01",
                                    "BUREAU_ADDRESS": "xxxxx"
                                },
                                {
                                    "ADDR_CATEGORY": "02",
                                    "BUREAU_ADDRESS": "xxxxx"
                                },
                                {
                                    "ADDR_CATEGORY": "01",
                                    "BUREAU_ADDRESS": "xxxxx"
                                },
                                {
                                    "ADDR_CATEGORY": "03",
                                    "BUREAU_ADDRESS": "xxxxx"
                                }
                            ],
                            "CUSTOM_FIELDS$BUREAU_VINTAGE": 236,
                            "CUSTOM_FIELDS$CC": 203200,
                            "CUSTOM_FIELDS$CC_CREDIT_LIMIT": 1016000,
                            "CUSTOM_FIELDS$CC_HIGH_CREDIT": 530172,
                            "CUSTOM_FIELDS$CC_INCOME": 203200,
                            "CUSTOM_FIELDS$CC_UTILIZATION_RATIO": 3.4685,
                            "CUSTOM_FIELDS$CIBIL_SCORE": 795,
                            "CUSTOM_FIELDS$COOLING_PERIOD_12MONTHS": "Y",
                            "CUSTOM_FIELDS$CREDIT_AND_HIGH_CREDIT_MAX": 1016000,
                            "CUSTOM_FIELDS$CREDIT_AND_HIGH_CREDIT_MAX_TEST": 1016000,
                            "CUSTOM_FIELDS$DDPD30PLUS_3MONTHS_IT_CNT": 0,
                            "CUSTOM_FIELDS$DPD30PLUS_3MONTHS_IT": "Y",
                            "CUSTOM_FIELDS$DPD90_PLUS_12MON_IT": "Y",
                            "CUSTOM_FIELDS$DPD90_PLUS_12MON_IT_CNT": 0,
                            "CUSTOM_FIELDS$DPD_12MONTHS": "Y",
                            "CUSTOM_FIELDS$DPD_3MONTHS": "Y",
                            "CUSTOM_FIELDS$ELIG_PERCENT_EXPSCORECARD": 1,
                            "CUSTOM_FIELDS$EMI AMOUNT": 25000,
                            "CUSTOM_FIELDS$EMI_INCOME": 0,
                            "CUSTOM_FIELDS$EMP_TYPE": "N",
                            "CUSTOM_FIELDS$EPIFI_FOIR_FACTOR": 0.6,
                            "CUSTOM_FIELDS$EPIFI_INT_RATE": 13,
                            "CUSTOM_FIELDS$EPIFI_PF_RATE": 2,
                            "CUSTOM_FIELDS$EXPERIAN_SCORE": 681,
                            "CUSTOM_FIELDS$EXP_SCORECARD_UPPER_ELIG_LIM": 500000,
                            "CUSTOM_FIELDS$FEDERAL_COLL_CAPABILITY": 0,
                            "CUSTOM_FIELDS$FINAL_ELIGIBILITY_EPIFI": 500000,
                            "CUSTOM_FIELDS$FINAL_ELIGIBILITY_MAX_5LACS": 500000,
                            "CUSTOM_FIELDS$FINAL_ELIGIB_MAX_5LACS_TEST": 500000,
                            "CUSTOM_FIELDS$HIGH_CREDIT_AMOUNT":
                            [
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "25000"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "20104"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "93722"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "530172"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "176378"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "84313"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "67584"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "9000000"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "88255"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "167316"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "147796"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "3300000"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "544"
                                },
                                {},
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "226666"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "79722"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "11490"
                                },
                                {
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "20000"
                                }
                            ],
                            "CUSTOM_FIELDS$HUNTER_LOW_RISK_RULES": "NOO",
                            "CUSTOM_FIELDS$HUNTER_RULE_ID": "NO",
                            "CUSTOM_FIELDS$IF_ELIGIBLE_THROUGH_CC_INC_75K": "Y",
                            "CUSTOM_FIELDS$LAP_BL": "YY",
                            "CUSTOM_FIELDS$MAX_INCOME_TO_BE_CONSIDERED": 203200,
                            "CUSTOM_FIELDS$MAX_INCOME_TO_BE_CONS_TEST": 203200,
                            "CUSTOM_FIELDS$NEW_LAP_BL": "YY",
                            "CUSTOM_FIELDS$PB_FOIR_FACTOR": 0.6,
                            "CUSTOM_FIELDS$PB_INTEREST_RATE": 12.25,
                            "CUSTOM_FIELDS$PINCODE": 560103,
                            "CUSTOM_FIELDS$PL_ACC_OPEN_12MONTHS": 1,
                            "CUSTOM_FIELDS$PL_BL_ENQ": 3,
                            "CUSTOM_FIELDS$PL_BL_ENQ_LATEST": 3,
                            "CUSTOM_FIELDS$PL_OPEN_12MONTHS": 1,
                            "CUSTOM_FIELDS$PL_RISKSCORE": "Y",
                            "CUSTOM_FIELDS$TEST":
                            [
                                {
                                    "ACCT_DT_CLOSED": "********",
                                    "ACCT_DT_OF_LAST_PAYMENT": "********",
                                    "ACCT_DT_OPENED_DISBURSED": "07022025",
                                    "ACCT_EMI_AMT": "8620",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "25000",
                                    "ACCT_TYPE": "05",
                                    "EMI_PRESENCE_CHECK": "YES",
                                    "FINAL_TENURE_FOR_EMI_CALC": 6,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 15,
                                    "TRADE_TENURE_EMI_CALC": 6
                                },
                                {
                                    "ACCT_DT_OF_LAST_PAYMENT": "11022025",
                                    "ACCT_DT_OPENED_DISBURSED": "10102024",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "20104",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 7,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 7,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_OF_LAST_PAYMENT": "02032025",
                                    "ACCT_DT_OPENED_DISBURSED": "25062024",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "93722",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 11,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 11,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_OF_LAST_PAYMENT": "28042025",
                                    "ACCT_DT_OPENED_DISBURSED": "27032023",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "530172",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 26,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 26,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "********",
                                    "ACCT_DT_OF_LAST_PAYMENT": "********",
                                    "ACCT_DT_OPENED_DISBURSED": "03122022",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "176378",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "********",
                                    "ACCT_DT_OF_LAST_PAYMENT": "02122023",
                                    "ACCT_DT_OPENED_DISBURSED": "30082022",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "84313",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "********",
                                    "ACCT_DT_OF_LAST_PAYMENT": "07122022",
                                    "ACCT_DT_OPENED_DISBURSED": "15092021",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "67584",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "********",
                                    "ACCT_DT_OF_LAST_PAYMENT": "********",
                                    "ACCT_DT_OPENED_DISBURSED": "07082017",
                                    "ACCT_EMI_AMT": "87774",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "9000000",
                                    "ACCT_TYPE": "02",
                                    "EMI_PRESENCE_CHECK": "YES",
                                    "FINAL_TENURE_FOR_EMI_CALC": 240,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 9,
                                    "TRADE_TENURE_EMI_CALC": 240
                                },
                                {
                                    "ACCT_DT_CLOSED": "11012020",
                                    "ACCT_DT_OF_LAST_PAYMENT": "13112019",
                                    "ACCT_DT_OPENED_DISBURSED": "11022017",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "88255",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "14112015",
                                    "ACCT_DT_OF_LAST_PAYMENT": "02112015",
                                    "ACCT_DT_OPENED_DISBURSED": "03022012",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "167316",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "19052012",
                                    "ACCT_DT_OF_LAST_PAYMENT": "11052012",
                                    "ACCT_DT_OPENED_DISBURSED": "05042011",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "147796",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "07012015",
                                    "ACCT_DT_OF_LAST_PAYMENT": "07012015",
                                    "ACCT_DT_OPENED_DISBURSED": "30092008",
                                    "ACCT_EMI_AMT": "35178",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "3300000",
                                    "ACCT_TYPE": "02",
                                    "EMI_PRESENCE_CHECK": "YES",
                                    "FINAL_TENURE_FOR_EMI_CALC": 240,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 9,
                                    "TRADE_TENURE_EMI_CALC": 240
                                },
                                {
                                    "ACCT_DT_CLOSED": "16022008",
                                    "ACCT_DT_OPENED_DISBURSED": "28122007",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "544",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "09072007",
                                    "ACCT_DT_OPENED_DISBURSED": "17102006",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "10092022",
                                    "ACCT_DT_OF_LAST_PAYMENT": "01082022",
                                    "ACCT_DT_OPENED_DISBURSED": "26072006",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "226666",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "18082010",
                                    "ACCT_DT_OF_LAST_PAYMENT": "10082010",
                                    "ACCT_DT_OPENED_DISBURSED": "10062006",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "79722",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "12062010",
                                    "ACCT_DT_OF_LAST_PAYMENT": "12062010",
                                    "ACCT_DT_OPENED_DISBURSED": "11032009",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "11490",
                                    "ACCT_TYPE": "10",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 0,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 0,
                                    "TRADE_TENURE_EMI_CALC": 0
                                },
                                {
                                    "ACCT_DT_CLOSED": "24052006",
                                    "ACCT_DT_OF_LAST_PAYMENT": "07052006",
                                    "ACCT_DT_OPENED_DISBURSED": "06092005",
                                    "ACCT_HIGH_CREDIT_SANC_AMT": "20000",
                                    "ACCT_TYPE": "13",
                                    "EMI_PRESENCE_CHECK": "NO",
                                    "FINAL_TENURE_FOR_EMI_CALC": 48,
                                    "TL_DATE_DIFF_MONTHS_EMI_CALC": 0,
                                    "TRADE_LEVEL_CURRENT_OBLI": 0,
                                    "TRADE_ROI_EMI_CALC": 15,
                                    "TRADE_TENURE_EMI_CALC": 48
                                }
                            ],
                            "CUSTOM_FIELDS$TOTAL_CURRENT_EMI": 0,
                            "CUSTOM_FIELDS$TOTAL_PL_AMT_AVLD_CUST_FINAL": 0,
                            "CUSTOM_FIELDS$UNSEC_ACCT_OPEN_6MON_IT": 1,
                            "CUSTOM_FIELDS$UNSEC_LOAN_ENQIRY_EXP": 4,
                            "CUSTOM_FIELDS$UNSEC_LOAN_OPEN_6MNTH_EXP": 1,
                            "CUSTOM_FIELDS$WRITEOFF_STLMT_SUIT_FLD_18M": "Y",
                            "FINANCIAL_FIELDS$ABOVE_3LACS": 203200,
                            "FINANCIAL_FIELDS$CC_INCOME_ABOVE_3LACS": 203200,
                            "FINANCIAL_FIELDS$CC_INCOME_ABOVE_3LACS_TEST": 203200,
                            "FINANCIAL_FIELDS$CC_INCOME_BELOW_3LACS": 290285.71,
                            "FINANCIAL_FIELDS$CC_INCOME_BELOW_3LACS_TEST": 290285.71,
                            "FINANCIAL_FIELDS$CREDIT_VS_HIGH_CREDIT_MAX": 1546172,
                            "FINANCIAL_FIELDS$EMI_INTEREST_RATE": 0.010208,
                            "FINANCIAL_FIELDS$EMI_INTEREST_RATE_EPIFI": 0.010833,
                            "FINANCIAL_FIELDS$FINAL_ELIG_EXPSCORE_EPIFI": 500000,
                            "FINANCIAL_FIELDS$FINAL_ELIG_EXP_MULTIPLIE_EPIFI": 500000,
                            "FINANCIAL_FIELDS$MAX_FOIR": 121920,
                            "FINANCIAL_FIELDS$MAX_FOIR_EPIFI": 121920,
                            "FINANCIAL_FIELDS$MAX_FOIR_TEST": 121920,
                            "FINANCIAL_FIELDS$MAX_INCOME": 203200,
                            "FINANCIAL_FIELDS$MAX_LOAN_ELIGIBLE_TEST": 4608310.02,
                            "FINANCIAL_FIELDS$MAX_LOAN__ELIGIBLE": 4608310.02,
                            "FINANCIAL_FIELDS$MAX_LOAN__ELIGIBLE_EPIFI": 4544624.76,
                            "FINANCIAL_FIELDS$MAX_PERMISSIBLE_EMI": 121920,
                            "FINANCIAL_FIELDS$MAX_PERMISSIBLE_EMI_EPIFI": 121920,
                            "FINANCIAL_FIELDS$MAX_PERMISSIBLE_EMI_TEST": 121920,
                            "FINANCIAL_FIELDS$MAX_TENURE": 0,
                            "FINANCIAL_FIELDS$PL_AMT_AVLD_NEGATIVE": 0,
                            "FINANCIAL_FIELDS$TOTAL_LIMIT_CHECK_OUTPUT": 0,
                            "FINANCIAL_FIELDS$TOTAL_UNSEC_PLCC": 0,
                            "FINANCIAL_FIELDS$UNSEC_LIMIT_CHECK_FINAL_ELIGI": 0,
                            "FINANCIAL_FIELDS$UPTO_3LAC": 290285.71,
                            "POLICY_ID": 10,
                            "POLICY_NAME": "EPIFI_NTB_EXP"
                        },
                        "ELIGIBILITY-AMOUNT": "500000",
                        "ELIGIBILITY-APPROVED-AMOUNT": "500000",
                        "ELIGIBILITY-DECISION": "Approved",
                        "ELIGIBILITY_RESPONSE":
                        {
                            "ADDITIONAL_FIELDS":
                            {
                                "TENOR": "48"
                            },
                            "APPROVED_AMOUNT": 500000,
                            "COMPUTED_AMOUNT": 500000,
                            "COMPUTE_DISP": "FINAL_ELIG_EXPSCORE_EPIFI",
                            "COMPUTE_LOGIC": "A",
                            "DECISION": "Approved",
                            "ELIGIBILITY_AMOUNT": 500000,
                            "ELIGIBILITY_NAME": "EPIFI_NTB_EXP",
                            "ElgbltyID": 9,
                            "FINAL": true,
                            "GRID_AGGREGATION": "MAX",
                            "GRID_EXP": "( ( 100000 <= FINAL_ELIG_EXPSCORE_EPIFI ) && ( FINAL_ELIG_EXPSCORE_EPIFI < 500001 ) )   ",
                            "GridID": 1,
                            "MATCHED_ELIGIBILITY":
                            [
                                {
                                    "ADDITIONAL_FIELDS":
                                    {
                                        "TENOR": "48"
                                    },
                                    "APPROVED_AMOUNT": 500000,
                                    "COMPUTED_AMOUNT": 500000,
                                    "COMPUTE_DISP": "FINAL_ELIG_EXPSCORE_EPIFI",
                                    "COMPUTE_LOGIC": "A",
                                    "DECISION": "Approved",
                                    "ELIGIBILITY_AMOUNT": 500000,
                                    "ELIGIBILITY_NAME": "EPIFI_NTB_EXP",
                                    "ElgbltyID": 9,
                                    "FINAL": true,
                                    "GRID_EXP": "( ( 100000 <= FINAL_ELIG_EXPSCORE_EPIFI ) && ( FINAL_ELIG_EXPSCORE_EPIFI < 500001 ) )   ",
                                    "GridID": 1,
                                    "MAX_TENOR": 48,
                                    "REMARK": "APPROVED",
                                    "VALUES":
                                    {
                                        "FINAL_ELIG_EXPSCORE_EPIFI": 500000
                                    }
                                }
                            ],
                            "MAX_TENOR": 48,
                            "REMARK": "APPROVED",
                            "VALUES":
                            {
                                "FINAL_ELIG_EXPSCORE_EPIFI": 500000
                            }
                        },
                        "POLICY_ID": "10",
                        "POLICY_NAME": "EPIFI_NTB_EXP",
                        "RULES":
                        [],
                        "SCORING-REF-ID": "174714144187365",
                        "STATUS": "SUCCESS",
                        "responseDate": 0
                    }
                ],
                "SUMMARY":
                {
                    "APPLICATION-APPROVED-AMOUNT": "500000",
                    "APPLICATION-DECISION": "Approved",
                    "STATUS": "SUCCESS"
                },
                "responseDate": 1747141441881
            }
        },
        "responseCode": "200",
        "responseDesc": "Data save successfully"
    },
    "trackingId": "xxxxxxxx"
}
*/
message LoanEligibilityResponseLentra {
  string reference_id = 1 [json_name = "referenceId"];
  string tracking_id = 2 [json_name = "trackingId"];
  string dateTime = 3 [json_name = "dateTime"];
  ResponseDetails response_details = 4 [json_name = "responseDetails"];
}
message ResponseDetails {
  string response_code = 1 [json_name = "responseCode"];
  string response_desc = 2 [json_name = "responseDesc"];
  ResponseLentra response = 3 [json_name = "response"];
}
message ResponseLentra {
  string eligibility_status = 1 [json_name = "eligibilityStatus"];
  string eligibility_decision = 2 [json_name = "eligibilityDecision"];
  double approved_amount = 3 [json_name = "approvedAmount"];
  string cibil_status = 4 [json_name = "cibilStatus"];
  string cibil_score = 5 [json_name = "cibilScore"];
  string fraud_check_decision = 6 [json_name = "fraudCheckDecision"];
  string fraud_check_status = 7 [json_name = "fraudCheckStatus"];
  ScoringResponseWithCoapp scoring_response_with_coapp = 8 [json_name = "scoringResponseWithCoapp"];
  double roi = 9 [json_name = "roi"];
  double emi = 10 [json_name = "emi"];
  double max_eligible_emi = 11 [json_name = "max_eligible_emi"];
  double processing_fee = 12 [json_name = "processingFee"];
}
message ScoringResponseWithCoapp {
  int64 response_date = 1 [json_name = "responseDate"];
  Summary summary = 2 [json_name = "SUMMARY"];
  repeated ApplicantResult applicant_result = 3 [json_name = "APPLICANT-RESULT"];
}

message Summary {
  string application_decision = 1 [json_name = "APPLICATION-DECISION"];
  string application_approved_amount = 2 [json_name = "APPLICATION-APPROVED-AMOUNT"];
  string status = 3 [json_name = "STATUS"];
}
message ApplicantResult {
  int64 response_date = 1 [json_name = "responseDate"];
  string scoring_ref_id = 2 [json_name = "SCORING-REF-ID"];
  string status = 3 [json_name = "STATUS"];
  string policy_name = 4 [json_name = "POLICY_NAME"];
  string policy_id = 5 [json_name = "POLICY_ID"];
  string decision = 6 [json_name = "DECISION"];
  string eligibility_amount = 7 [json_name = "ELIGIBILITY-AMOUNT"];
  string eligibility_decision = 8 [json_name = "ELIGIBILITY-DECISION"];
  string applicant_id = 9 [json_name = "APPLICANT-ID"];
  string eligibility_approved_amount = 10 [json_name = "ELIGIBILITY-APPROVED-AMOUNT"];
  EligibilityResponse eligibility_response = 11 [json_name = "ELIGIBILITY_RESPONSE"];
  repeated RuleLentra rules = 12 [json_name = "RULES"];
  DerivedFields derived_fields = 13 [json_name = "DERIVED_FIELDS"];
}

message RuleLentra {
  int64 criteria_id = 1 [json_name = "CriteriaID"];
  string rule_name = 2 [json_name = "RuleName"];
  ValuesLentra values = 3 [json_name = "Values"];
  string outcome = 4 [json_name = "Outcome"];
  string remark = 5 [json_name = "Remark"];
  string exp = 6 [json_name = "Exp"];
  message ValuesLentra {
    int64 cibil_score = 1 [json_name = "CIBIL_SCORE"];
    int64 bureau_vintage = 2 [json_name = "BUREAU_VINTAGE"];
    double max_income_to_be_considered = 3 [json_name = "MAX_INCOME_TO_BE_CONSIDERED"];
    string experian_scorecard_score = 4 [json_name = "EXPERIAN_SCORECARD_SCORE"];
    int64 max_loan_eligible = 5 [json_name = "MAX_LOAN__ELIGIBLE"];
  }
}

message DerivedFields {
  string policy_name = 1 [json_name = "POLICY_NAME"];
  double custom_fields_epifi_interest_rate = 13 [json_name = "CUSTOM_FIELDS$EPIFI_INT_RATE"];
  double custom_fields_epifi_pf_rate = 14 [json_name = "CUSTOM_FIELDS$EPIFI_PF_RATE"];
  double custom_fields_max_emi_epifi = 15 [json_name = "CALCULATED_FIELDS$MAX_PERMISSIBLE_EMI_EPIFI"];
}

message EligibilityResponse {
  string decision = 1 [json_name = "DECISION"];
  int64 elgblty_id = 2 [json_name = "ElgbltyID"];
  string eligibility_name = 3 [json_name = "ELIGIBILITY_NAME"];
  string remark = 4 [json_name = "REMARK"];
  double approved_amount = 5 [json_name = "APPROVED_AMOUNT"];
  //repeated MatchedEligibility matched_eligibility = 6 [json_name = "MATCHED_ELIGIBILITY"];
}
//message MatchedEligibility {
//  Values values = 1 [json_name = "Values"];
//  string eligibility_id = 2 [json_name = "eligibility_id"];
//  int64 grid_id = 3 [json_name = "grid_id"];
//  string decision = 4 [json_name = "decision"];
//  string compute_disp = 5 [json_name = "compute_disp"];
//  string compute_logic = 6 [json_name = "compute_logic"];
//  double max_amount = 7 [json_name = "max_amount"];
//  double min_amount = 8 [json_name = "min_amount"];
//  int64 dp = 9 [json_name = "dp"];
//  int64 max_tenor = 10 [json_name = "max_tenor"];
//  string remark = 11 [json_name = "remark"];
//  double computed_amount = 12 [json_name = "computed_amount"];
//  double eligibility_amount = 13 [json_name = "eligibility_amount"];
//  int64 cnt = 14 [json_name = "cnt"];
//  string grid_exp = 15 [json_name = "grid_exp"];
//  int64 mul_prod = 16 [json_name = "mul_prod"];
//  string eligibility_name = 17 [json_name = "eligibility_name"];
//  AdditionalFields additional_fields = 18 [json_name = "additional_fields"];
//  int64 dt_submit = 19 [json_name = "dt_submit"];
//  message Values {
//    int64 foir_cc = 1 [json_name = "foir_cc"];
//  }
//}
//message AdditionalFields {
//  string max = 1 [json_name = "max"];
//}
