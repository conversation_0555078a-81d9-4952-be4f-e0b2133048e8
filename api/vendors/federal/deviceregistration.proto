/*
  Protos file contains definitions for device registration request and response
 */
syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

message RegisterDeviceRequest {
  string sender_code = 1 [json_name = "SenderCode"];
  string service_access_id = 2 [json_name = "ServiceAccessId"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];
  string request_id = 4 [json_name = "RequestId"];
  string device_id = 5 [json_name = "DeviceId"];
  string user_profile_id = 6 [json_name = "UserProfileId"];
  string mobile_number = 7 [json_name = "MobileNumber"];
  string email_id = 8 [json_name = "EmailId"];
  string encrypted_payload = 9 [json_name = "EncryptedPayload"];
}

message RegisterDeviceResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];
  string response_code = 5 [json_name = "Response"];
  string reason = 6 [json_name = "Reason"];
}

message DeviceRegistrationSMSUpdateCallback {
  string sms_code = 1 [json_name = "sms_code"];
  string sms_timestamp = 2 [json_name = "sms_timestamp"];
  string mobile_number = 3 [json_name = "Mobile_Number"];
  string status = 4 [json_name = "Status"];
  string reason = 5 [json_name = "Reason"];
}
