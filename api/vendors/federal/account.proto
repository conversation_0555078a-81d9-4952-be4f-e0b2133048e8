syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

message GuardianDetails {
  string guardian_name = 1 [json_name = "GuardianName"];
  string guardian_code = 2 [json_name = "GuardianCode"];
  string guardian_address = 3 [json_name = "GuardianAddress"];
  string guardian_city_code = 4 [json_name = "GuardianCitCode"];
  string guardian_state_code = 5 [json_name = "GuardianStaCode"];
  string guardian_country_code = 6 [json_name = "GuardianConCode"];
  string guardian_pin_code = 7 [json_name = "GuardianPinCode"];
}

message NomineeDetails {
  string nominee_name = 1 [json_name = "NomineeName"];
  string nominee_relation = 2 [json_name = "NomineeRelation"];
  string nominee_age = 3 [json_name = "NomineeAge"];
  string nominee_dob = 4 [json_name = "NomineeDOB"];
  string nominee_minor_flag = 5 [json_name = "NomineeMinorFlag"];
  string nominee_address = 6 [json_name = "NomineeAddress"];
  string nominee_city_code = 7 [json_name = "NomineeCitCode"];
  string nominee_state_code = 8 [json_name = "NomineeStaCode"];
  string nominee_country_code = 9 [json_name = "NomineeConCode"];
  string nominee_pin_code = 10 [json_name = "NomineepinCode"];
  GuardianDetails guardian_details = 11 [json_name = "GuardianDetails"];
}

message OpenSavingsAccountRequest {
  string resp_url = 1 [json_name = "RespUrl"];
  string sender_code = 2 [json_name = "SenderCode"];
  string service_access_id = 3 [json_name = "ServiceAccessId"];
  string service_access_code = 4 [json_name = "ServiceAccessCode"];
  string device_id = 5 [json_name = "DeviceId"];
  string user_profile_id = 6 [json_name = "UserProfileId"];
  string device_token = 7 [json_name = "DeviceToken"];
  string request_id = 8 [json_name = "RequestId"];
  string schem_code = 9 [json_name = "SchemCode"];
  string sol_id = 10 [json_name = "SolId"];
  string cust_id = 11 [json_name = "CustId"];
  string customer_name = 12 [json_name = "CustomerName"];
  string preopen_kit_flag = 13 [json_name = "PreopenKitFlag"];
  string account_no = 14 [json_name = "AccountNo"];
  string acc_open_date = 15 [json_name = "AccOpenDate"];
  string ModeOfOperation = 16 [json_name = "ModeOfOperation"];
  string mobile_no = 17 [json_name = "Mobile_Number"];
  string email_id = 18 [json_name = "EmailId"];
  string no_of_customers = 19 [json_name = "NoOfCustomers"];
  string nominee_opted_flag = 20 [json_name = "NomineeOptedFlag"];
  NomineeDetails nominee_details = 21 [json_name = "NomineeDetails"];
  string cust_ids = 22 [json_name = "CustIds"];
  string source_of_funds = 23 [json_name = "SourceOfFunds"];
  string scholarship_flag = 24 [json_name = "ScholarshipFlag"];
  string dbt_flag = 25 [json_name = "DBT_Flag"];
  string annual_txn_volume = 26 [json_name = "AnnualTxnVolume"];
  string acc_purpose = 27 [json_name = "AccPurpose"];
}

message CreateAccountRequest {
  OpenSavingsAccountRequest savings_acc_req = 1 [json_name = "SB_Acc_Opening_Request"];
}

message CreateAccountResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];
  string response = 5 [json_name = "Response"];
  string reason = 6 [json_name = "Reason"];
}

message GetBalanceRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include sender code, access code and access id
  string sender_code = 1 [json_name = "SenderCode"];
  string service_access_id = 2 [json_name = "ServiceAccessId"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];
  // Client should pass unique value to each call, to identify the request.
  string request_id = 4 [json_name = "RequestId"];
  // Device ID of the client that has initiated the request (mandatory)
  string device_id = 5 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration (mandatory)
  string device_token = 6 [json_name = "DeviceToken"];
  // Account number of which balance is to be found
  string account_number = 7 [json_name = "AccountNumber"];
  // Remitter’s mobile. Mobile number should be of the format of ten digits number with prefix 91.
  string mobile_number = 8 [json_name = "MobileNumber"];
  // Optional: Cred block contains the user credentials in encrypted form. We can pass empty string here
  string cred_block = 9 [json_name = "CredBlock"];
}

message GetBalanceResponse {
  // Sender code that is passed in the request
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];
  // Device token that is passed in the request
  string device_token = 3 [json_name = "DeviceToken"];
  // Timestamp at which request is processed
  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];
  // Account number sent in the request
  string account_number = 5 [json_name = "Account_Number"];
  // Customer name linked with the given account number
  string customer_name = 6 [json_name = "CustomerName"];
  // Type of account
  string account_type = 7 [json_name = "Account_Type"];
  // Status of the given account
  string account_status = 8 [json_name = "Account_Status"];
  // Ledger balance which is the available balance as of the beginning of the day.
  // Balance will be of the format "+4817917.35"
  string ledger_balance = 9 [json_name = "LedgerBalance"];
  // Available balance is Ledger balance plus or minus any subsequent activity in the account, balance at that point of time
  // Balance will be of the format "+4817917.35"
  string available_balance = 10 [json_name = "AvailableBalance"];
  // Currency code of the balance
  string currency_code = 11 [json_name = "BalCurrencycode"];
  // Corresponding Response code and reason
  string response_code = 12 [json_name = "Response"];
  string reason = 13 [json_name = "Reason"];
}

// Represents the attributes for the GetAccountStatement API.
// All attributes are mandatory.
message GetAccountStatementRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include sender_id, access_id and sender code
  string sender_code = 1 [json_name = "SenderCode"];
  string service_access_id = 2 [json_name = "ServiceAccessId"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];

  // Device ID of the client that has initiated the request
  string device_id = 4 [json_name = "DeviceId"];

  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 5 [json_name = "DeviceToken"];

  // Client should pass unique value to each call, to identify the request
  string request_id = 6 [json_name = "RequestId"];

  // Customer identification information
  string customer_id = 7 [json_name = "CustomerId"];

  // savings account number for which to get the statement for.
  string account_number = 8 [json_name = "AccountNumber"];

  // mobile number of customer
  string mobile_number = 9 [json_name = "MobileNumber"];

  // Date should be in yyyyMMdd format
  string from_date = 10 [json_name = "FromDate"];
  string to_date = 11 [json_name = "ToDate"];

  // Serial Number of transaction from where statement should start
  // minimum value is 1
  string page_num = 12 [json_name = "PageNum"];
}

// Represents the response attributes for the GetAccountStatement API from Federal.
message GetAccountStatementResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];
  string customer_id = 5 [json_name = "CustomerId"];
  string customer_name = 6 [json_name = "CustomerName"];
  string opening_balance = 7 [json_name = "OpeningBalance"];
  string account_no = 8 [json_name = "AccountNo"];

  // specifies the page number from which next set of transactions is to be displayed.
  string page_num = 9 [json_name = "PageNum"];
  // it'll specify more records are there or not.
  string has_more_rows = 10 [json_name = "HasMoreRows"];

  // denotes all the transactions of the deposit account within the duration specified in the request message.
  TransactionDetails transaction_details = 11 [json_name = "transactionDetails"];

  string response_code = 12 [json_name = "ResponseCode"];
  string response_reason = 13 [json_name = "ResponseReason"];
  string response_action = 14 [json_name = "ResponseAction"];

  message TransactionDetails {
    repeated Transactions transactions = 1 [json_name = "transactions"];

    message Transactions {
      string tran_date_time = 1 [json_name = "TranDateTime"];
      string tran_code = 2 [json_name = "TranCode"];
      string transaction_desc = 3 [json_name = "TransactionDesc"];
      string cheque_no = 4 [json_name = "ChequeNo"];
      string amount = 5 [json_name = "Amount"];
      string tran_type = 6 [json_name = "TranType"];
      string running_balance = 7 [json_name = "RunningBalance"];
      string tran_reference = 8 [json_name = "TranReference"];
      string comments = 9 [json_name = "Comments"];
      string value_date = 10 [json_name = "ValueDate"];
      string instrument_type = 11 [json_name = "InstrmntType"];
      string instrument_date = 12 [json_name = "InstrmntDate"];
      string instrument_num = 13 [json_name = "InstrmntNum"];
      string additional_particular = 14 [json_name = "TranParticular2"];
      string batch_serial_id = 15 [json_name = "PartTranSrlNum"];
    }
  }
}

message GetClosingBalanceRequest {
  string sender_id = 1 [json_name = "SenderId"];
  string sender_code = 2 [json_name = "SenderCode"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];
  string request_id = 4 [json_name = "RequestId"];
  string date = 5 [json_name = "Date"];
  string account_no = 6 [json_name = "AccountNo"];
  string bank_id = 7 [json_name = "BankId"];
}

message GetClosingBalanceResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string balance = 3 [json_name = "Balance"];
  string date = 4 [json_name = "Date"];
  string bkod_date = 5 [json_name = "BkodDate"];
  string response = 6 [json_name = "Response"];
  string reason = 7 [json_name = "Reason"];
}

message AccountCreationCallBackResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string created_on = 4 [json_name = "Saving_Acc_CreatedOn"];
  string customer_id = 5 [json_name = "CustomerId"];
  string saving_account_number = 6 [json_name = "Saving_Account_Number"];
  string response_code = 7 [json_name = "ResponseCode"];
  string response_reason = 8 [json_name = "ResponseReason"];
  string response_action = 9 [json_name = "ResponseAction"];
}

message GetBalanceV1Request {
  string request_id = 1 [json_name = "REQUEST_ID"];
  string sender_id = 2 [json_name = "SENDER_ID"];
  string sender_code = 3 [json_name = "SENDER_CODE"];
  string sender_access_code = 4 [json_name = "SENDER_ACCESS_CODE"];
  string account_number = 5 [json_name = "ACCOUNT_NUMBER"];
}

message GetBalanceV1Response {
  message AccountBalance {
    string request_id = 1 [json_name = "REQUEST_ID"];
    string sender_code = 2 [json_name = "SENDER_CODE"];
    string customer_name = 3 [json_name = "CUSTOMER_NAME"];
    string account_number = 4 [json_name = "ACCOUNT_NUMBER"];
    string ledger_balance = 5 [json_name = "LEDGER_BALANCE"];
    string available_balance = 6 [json_name = "AVAILABLE_BALANCE"];
    string funds_in_clg = 7 [json_name = "FUNDS_IN_CLG"];
    string lien_amt = 8 [json_name = "LIEN_AMT"];
    string balance_as_on = 9 [json_name = "BALANCE_AS_ON"];
    string freeze_status = 10 [json_name = "FREEZE_STATUS"];
    string freeze_reason = 11 [json_name = "FREEZE_REASON"];
    string response_code = 12 [json_name = "RESPONSE_CODE"];
    string reason = 13 [json_name = "REASON"];
  }

  AccountBalance account_balance = 1 [json_name = "ACCOUNT_BALANCE"];

  // fields to be used in-case of non successful response
  string request_id = 2 [json_name = "REQUEST_ID"];
  string sender_code = 3 [json_name = "SENDER_CODE"];
  string response_code = 4 [json_name = "RESPONSE_CODE"];
  string reason = 5 [json_name = "REASON"];

}

// GetMiniStatementRequest is request body of Vendor api - Mini Statement
message GetMiniStatementRequest {
  string sender_id = 1 [json_name = "SenderId"];
  string sender_code = 2 [json_name = "SenderCode"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];
  string request_id = 4 [json_name = "RequestId"];
  string account_number = 5 [json_name = "Accountnumber"];
  string from_date = 6 [json_name = "FromDate"];
  string to_date = 7 [json_name = "ToDate"];
}

// GetMiniStatementResponse is response body of Vendor api - Mini Statement
message GetMiniStatementResponse {

  Statement statement = 1 [json_name = "Neo_ministatement_response"];
  // Duplicate fields from Statement message, because federal send these fields outside of Neo_ministatement_response in case of any error
  // like No Data Found (NS0003).
  string sender_code = 2 [json_name = "SenderCode"];
  string request_id = 3 [json_name = "RequestId"];
  string response_code = 4 [json_name = "Response"];
  string response_reason = 5 [json_name = "Reason"];
  message Statement {
    string sender_code = 1 [json_name = "SenderCode"];
    string request_id = 2 [json_name = "RequestId"];
    repeated Transaction transactions = 3 [json_name = "Statement"];
    string response_code = 4 [json_name = "Response"];
    string response_reason = 5 [json_name = "Reason"];
  }
  message Transaction {
    string tran_date_time = 1 [json_name = "TRANDATETIME"];
    string tran_code = 2 [json_name = "TRANCODE"];
    string transaction_desc = 3 [json_name = "TRANSACTIONDESC"];
    string cheque_no = 4 [json_name = "CHEQUENO"];
    string amount = 5 [json_name = "AMOUNT"];
    string tran_type = 6 [json_name = "TRANTYPE"];
    string tran_reference = 7 [json_name = "TRANREFERENCE"];
    string comments = 8 [json_name = "COMMENTS"];
    string value_date = 19 [json_name = "VALUEDATE"];
    string instrument_type = 10 [json_name = "INSTRMNTTYPE"];
    string instrument_date = 11 [json_name = "INSTRMNTDATE"];
    string instrument_num = 12 [json_name = "INSTRMNTNUM"];
    string additional_particular = 13 [json_name = "TRANPARTICULAR2"];
    string batch_serial_id = 14 [json_name = "PARTTRANSRLNUM"];
    string additional_tran_type = 15 [json_name = "PARTTRANTYPE"];
  }
}

message GetAccountStatementByDrApiRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials could be different on each environment.
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include sender_id, access_id and sender code
  string sender_code = 1 [json_name = "Sender_Code"];
  string sender_id = 2 [json_name = "SenderId"];
  string sender_access_code = 3 [json_name = "SenderAccessCode"];

  // savings account number for which to get the statement for.
  string account_number = 4 [json_name = "Account_no"];

  // Date should be in yyyyMMdd format
  string from_date = 5 [json_name = "From_date"];
  string to_date = 6 [json_name = "To_date"];
}

// Represents the response attributes for the GetAccountStatement using DR API from Federal.
message GetAccountStatementByDrResponse {
  message Details {

    message Statement {
      string account_number = 1 [json_name = "AccountNumber"];
      string acid = 2 [json_name = "ACID"];
      string tran_date = 3 [json_name = "TRAN_DATE"];
      string tran_id = 4 [json_name = "TRAN_ID"];
      string pstd_date = 5 [json_name = "PSTD_DATE"];
      string part_tran_srl_number = 6 [json_name = "PART_TRAN_SRL_NUM"];
      string tran_type = 7 [json_name = "TRAN_TYPE"];
      string tran_sub_type = 8 [json_name = "TRAN_SUB_TYPE"];
      string part_tran_type = 9 [json_name = "PART_TRAN_TYPE"];
      string debit = 10 [json_name = "DEBIT"];
      string credit = 11 [json_name = "CREDIT"];
      string balance = 12 [json_name = "BALANCE"];
      string tran_particular = 14 [json_name = "TRAN_PARTICULAR"];
      string instrmnt_type = 15 [json_name = "INSTRMNT_TYPE"];
      string instrmnt_date = 16 [json_name = "INSTRMNT_DATE"];
      string instrmnt_num = 17 [json_name = "INSTRMNT_NUM"];
      string entry_date = 18 [json_name = "ENTRY_DATE"];
      string tran_crncy_code = 19 [json_name = "TRAN_CRNCY_CODE"];
      string tran_amt = 20 [json_name = "TRAN_AMT"];
      string value_date = 21 [json_name = "VALUE_DATE"];
      string ref_num = 22 [json_name = "REF_NUM"];
      string rpt_code = 23 [json_name = "RPT_CODE"];
      string tran_rmks = 24 [json_name = "TRAN_RMKS"];
      string module_id = 25 [json_name = "MODULE_ID"];
      string txn_date_time = 26 [json_name = "TXN_DATETIME"];
      string unique_tran_id = 27 [json_name = "UNIQUE_TRANID"];
      string tran_particular_2 = 28 [json_name = "TRAN_PARTICULAR_2"];
      string tran_rmks1 = 29 [json_name = "TRAN_RMKS1"];
      string crdr_flg = 30 [json_name = "CRDR_FLG"];
    }

    repeated Statement statement = 1 [json_name = "Statement"];
  }

  Details details = 1 [json_name = "Details"];
  string error_code = 2 [json_name = "ErrorCode"];
  string error = 3 [json_name = "Error"];
}

message AccountUpdateCallBackResponse {
  string account_number = 1 [json_name = "account_number"];
  string account_closed = 2 [json_name = "account_closed"];
  string account_freezed = 3 [json_name = "account_freezed"];
  string lien_marking = 4 [json_name = "lien_marking"];
  string reference_number = 5 [json_name = "reference_number"];
  string sol_id = 6 [json_name = "sol_id"];
  string scheme_code = 7 [json_name = "scheme_code"];
  string clr_balance = 8 [json_name = "clr_balance"];
  string freeze_reason_code = 9 [json_name = "freeze_reason_code"];
  string account_open_date = 10 [json_name = "account_open_date"];
  string account_close_date = 11 [json_name = "account_close_date"];
  string drawing_power = 12 [json_name = "drawing_power"];
  string sanction_limit = 13 [json_name = "sanction_limit"];
  string adhoc_limit = 14 [json_name = "adhoc_limit"];
  string cumulative_dr_amount = 15 [json_name = "cumulative_Dr_Amount"];
  string cumulative_cr_amount = 16 [json_name = "cumulative_Cr_Amount"];
  string last_trans_date = 17 [json_name = "last_trans_date"];
  string account_currency_code = 18 [json_name = "account_currency_code"];
  string scheme_type = 19 [json_name = "scheme_type"];
  string cif_id = 20 [json_name = "cif_Id"];
  string bank_id = 21 [json_name = "bank_Id"];
  string freeze_reason_code2 = 22 [json_name = "freeze_reason_code2"];
  string freeze_reason_code3 = 23 [json_name = "freeze_reason_code3"];
  string freeze_reason_code4 = 24 [json_name = "freeze_reason_code4"];
  string freeze_reason_code5 = 25 [json_name = "freeze_reason_code5"];
  string last_tran_date_cr = 26 [json_name = "last_tran_date_CR"];
  string last_tran_id_cr = 27 [json_name = "last_tran_id_CR"];
  string last_tran_date_dr = 28 [json_name = "last_tran_date_DR"];
  string last_tran_id_dr = 29 [json_name = "last_tran_id_DR"];
  string un_cleared_bal_amount = 30 [json_name = "un_cleared_bal_amount"];
  string mode_of_operation_code = 31 [json_name = "mode_of_operationCode"];
}

message AccountStatusEnquiryRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include sender_id, access_id and sender code
  string sender_code = 1 [json_name = "SenderCode"];
  string service_access_id = 2 [json_name = "ServiceAccessId"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];
  // Generated by client and should be passed unique value to each call, to identify the request
  string channel_request_id = 4 [json_name = "channelRequesetId"];
  string account_number = 5 [json_name = "acctId"];
  string mobile = 6 [json_name = "mobile"];
  // Request channel eg: EPIFI
  string channel = 7 [json_name = "channel"];
}

message AccountStatusEnquiryResponse {
  string account_number = 1 [json_name = "account_number"];
  string account_closed = 2 [json_name = "account_closed"];
  // Freeze Code C- Credit, T – Total, D- Debit
  string account_freezed = 3 [json_name = "account_freezed"];
  string lien_marking = 4 [json_name = "lien_marking"];
  string sol_id = 5 [json_name = "sol_id"];
  string scheme_code = 6 [json_name = "scheme_code"];
  // Clear balance in account, amount remaining in the account after clearing all outstanding transactions
  string clr_balance = 7 [json_name = "clr_balance"];
  string freeze_reason_code = 8 [json_name = "freeze_reason_code"];
  string freeze_reason_code2 = 9 [json_name = "freeze_reason_code2"];
  string freeze_reason_code3 = 10 [json_name = "freeze_reason_code3"];
  string freeze_reason_code4 = 11 [json_name = "freeze_reason_code4"];
  string freeze_reason_code5 = 12 [json_name = "freeze_reason_code5"];
  string account_open_date = 13 [json_name = "account_open_date"];
  string account_close_date = 14 [json_name = "account_close_date"];
  // Relevant only in case of ODCC accounts
  string drawing_power = 15 [json_name = "drawing_power"];
  // Approved Sanction Limit. Relevant only in case of ODCC / Term loan accounts.
  string sanction_limit = 16 [json_name = "sanction_limit"];
  // Approved adhoc Limit. Relevant only in case of ODCC / Term loan accounts.
  string adhoc_limit = 17 [json_name = "adhoc_limit"];
  string cumulative_dr_amount = 18 [json_name = "cumulative_Dr_Amount"];
  string cumulative_cr_amount = 19 [json_name = "cumulative_Cr_Amount"];
  // Last transaction date. Eg: 2022-09-27 00:00:00
  string last_trans_date = 20 [json_name = "last_trans_date"];
  string account_currency_code = 21 [json_name = "account_currency_code"];
  // Scheme type , SBA – Savings bank
  string scheme_type = 22 [json_name = "scheme_type"];
  string cif_id = 23 [json_name = "cif_Id"];
  string bank_id = 24 [json_name = "bank_Id"];
  string last_tran_date_cr = 25 [json_name = "last_tran_date_CR"];
  string last_tran_id_cr = 26 [json_name = "last_tran_id_CR"];
  string last_tran_date_dr = 27 [json_name = "last_tran_date_DR"];
  string last_tran_id_dr = 28 [json_name = "last_tran_id_DR"];
  string un_cleared_bal_amount = 29 [json_name = "un_cleared_bal_amount"];
  // Mode of Operation SG – Single, ES- Either or Survivor, JT - Joint
  string mode_of_operation_code = 30 [json_name = "mode_of_operationCode"];
  string cust_id = 31 [json_name = "cust_id"];
  string acct_name = 32 [json_name = "acct_name"];
  string kyc_flag = 33 [json_name = "kycflag"];
  // Account status – A – ACTIVE, D- DORMANT, I- INACTIVE
  string status = 34 [json_name = "status"];
  string last_tran_amt_dr = 35 [json_name = "last_tran_Amt_DR"];
  string last_tran_amt_cr = 36 [json_name = "last_tran_Amt_CR"];
  // Count of signature in account. If value is 1 or more than signature exists.
  string sign_count = 37 [json_name = "signcount"];
  // Free text fields for future extendability
  string free_text1 = 38 [json_name = "freetext1"];
  string free_text2 = 39 [json_name = "freetext2"];
  string free_text3 = 40 [json_name = "freetext3"];
  string free_text4 = 41 [json_name = "freetext4"];
  string free_text5 = 42 [json_name = "freetext5"];
  string free_text6 = 43 [json_name = "freetext6"];
  string free_text7 = 44 [json_name = "freetext7"];
  string free_text8 = 45 [json_name = "freetext8"];
  string free_text9 = 46 [json_name = "freetext9"];
  string free_text10 = 47 [json_name = "freetext10"];
  string free_text11 = 48 [json_name = "freetext11"];
  string free_text12 = 49 [json_name = "freetext12"];
  string free_text13 = 50 [json_name = "freetext13"];
  string free_text14 = 51 [json_name = "freetext14"];
  string free_text15 = 52 [json_name = "freetext15"];
  // UPI lite flag is exposed in free_text16. If UPI lite is enabled, it will be Y else N.
  string free_text16 = 53 [json_name = "freetext16"];
  string free_text17 = 54 [json_name = "freetext17"];
  string free_text18 = 55 [json_name = "freetext18"];
  string free_text19 = 56 [json_name = "freetext19"];
  string free_text20 = 57 [json_name = "freetext20"];
}

message ThirdPartyAccountCollectionRequest {
  string sb_account = 1 [json_name = "SBAccount"];
  string mobile_number = 2 [json_name = "MobileNumber"];
  string customer_id = 3 [json_name = "Customerid"];
  string penny_drop_id = 4 [json_name = "PennyDropid"];
  string tp_account_number = 5 [json_name = "TPAccountNumber"];
  string tp_ifsc = 6 [json_name = "TPIFSC"];
  string reserve_field_1 = 7 [json_name = "Reservefield1"];
  string reserve_field_2 = 8 [json_name = "Reservefield2"];
  string reserve_field_3 = 9 [json_name = "Reservefield3"];
  string reserve_field_4 = 10 [json_name = "Reservefield4"];
  string reserve_field_5 = 11 [json_name = "Reservefield5"];
}

message ThirdPartyAccountCollectionResponse {
  string message = 1 [json_name = "message"];
  string status = 2 [json_name = "status"];
  string err_codes = 3 [json_name = "errCode"];
}
