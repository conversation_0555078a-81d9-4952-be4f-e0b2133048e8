syntax = "proto3";

package vendors.inhouse;

import "api/auth/internal/auth_factor_update.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/device_properties.proto";
import "api/user/user.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendors/inhouse";
option java_package = "com.github.epifi.gamma.api.vendors.inhouse";

message DetectRiskRequest {
  string actor_id = 1 [json_name = "actor_id"];
  string request_id = 2 [json_name = "request_id"];
  // Actor ID of the user who referred the current user
  string referrer_actor_id = 3 [json_name = "referrer_actor_id"];
  string device_id = 4 [json_name = "device_id", deprecated = true];
  string email_id = 5 [json_name = "email"];
  bool credit_report_presence = 6 [json_name = "credit_report_presence"];
  bool credit_report_download_consent = 7 [json_name = "credit_report_download_consent"];
  string device_manufacturer = 8 [json_name = "device_manufacturer", deprecated = true];
  bool is_device_premium = 9 [json_name = "device_is_premium", deprecated = true];
  int32 age = 10 [json_name = "age"];
  // Name match score between gmail and pan name
  float gmail_pan_name_match_score = 11 [json_name = "namematch_gmail_kyc"];
  string phone_number = 12 [json_name = "phone_number"];
  int32 screener_mail_count = 13 [json_name = "screener_mail_count"];
  float latitude = 14 [json_name = "latitude"];
  float longitude = 15 [json_name = "longitude"];
  float threshold = 16 [json_name = "threshold"];
  repeated string liveness_statuses = 17 [json_name = "liveness_status"];
  repeated float liveness_scores = 18 [json_name = "liveness_score"];
  repeated string liveness_inhouse_errors = 19 [json_name = "liveness_inhouse_errors"];
  repeated float liveness_inhouse_scores = 20 [json_name = "liveness_inhouse_score"];
  repeated float facematch_scores = 21 [json_name = "facematch_score"];
  repeated string ckyc_errors = 22 [json_name = "ckyc_errors"];
  string hashed_phone_number = 23 [json_name = "hashed_number"];
  api.typesv2.common.Name father_name = 24 [json_name = "father_name"];
  api.typesv2.common.Name mother_name = 25 [json_name = "mother_name"];
  bool onboarding_ekyc_number_mismatch = 26 [json_name = "aadhar_number_mismatch"];
  api.typesv2.common.Name user_pan_name = 27 [json_name = "user_pan_name"];
  repeated float otp_scores = 28 [json_name = "otp_score"];
  repeated CreditReportAttributeInfo cb_details = 29 [json_name = "cb_details"];
  EmploymentData employment_data = 30 [json_name = "employment_data"];
  DeviceDetails device_details = 31 [json_name = "device_details"];
}

message CreditReportAttributeInfo {
  string attribute_key = 1;
  string attribute_value = 2;
}


message DetectRiskResponse {
  float score = 1 [json_name = "score"];
  float threshold = 2 [json_name = "threshold"];
  // bool flag to denote if the user is risky
  bool risky_user = 3 [json_name = "risky_user"];
  // response time of the API
  float time = 4 [json_name = "time"];
  // list of errors returned by the API
  repeated string error = 5 [json_name = "error"];
}

message DetectReOnboardingRiskRequest {
  string actor_id = 1 [json_name = "actor_id"];
  string request_id = 2 [json_name = "request_id"];
  // Actor ID of the user who referred the current user
  string referrer_actor_id = 3 [json_name = "referrer_actor_id"];
  string device_id = 4 [json_name = "device_id", deprecated = true];
  string email_id = 5 [json_name = "email"];
  bool credit_report_presence = 6 [json_name = "credit_report_presence"];
  bool credit_report_download_consent = 7 [json_name = "credit_report_download_consent"];
  string device_manufacturer = 8 [json_name = "device_manufacturer", deprecated = true];
  bool is_device_premium = 9 [json_name = "device_is_premium", deprecated = true];
  int32 age = 10 [json_name = "age"];
  // Name match score between gmail and pan name
  float gmail_pan_name_match_score = 11 [json_name = "namematch_gmail_kyc"];
  string phone_number = 12 [json_name = "phone_number"];
  int32 screener_mail_count = 13 [json_name = "screener_mail_count"];
  float latitude = 14 [json_name = "latitude"];
  float longitude = 15 [json_name = "longitude"];
  float threshold = 16 [json_name = "threshold"];
  repeated string liveness_statuses = 17 [json_name = "liveness_status"];
  repeated float liveness_scores = 18 [json_name = "liveness_score"];
  repeated string liveness_inhouse_errors = 19 [json_name = "liveness_inhouse_errors"];
  repeated float liveness_inhouse_scores = 20 [json_name = "liveness_inhouse_score"];
  repeated float facematch_scores = 21 [json_name = "facematch_score"];
  repeated string ckyc_errors = 22 [json_name = "ckyc_errors"];
  string hashed_phone_number = 23 [json_name = "hashed_number"];
  api.typesv2.common.Name father_name = 24 [json_name = "father_name"];
  api.typesv2.common.Name mother_name = 25 [json_name = "mother_name"];
  bool onboarding_ekyc_number_mismatch = 26 [json_name = "aadhar_number_mismatch"];
  api.typesv2.common.Name user_pan_name = 27 [json_name = "user_pan_name"];
  repeated float otp_scores = 28 [json_name = "otp_score"];
  string user_city = 30 [json_name = "user_city"];
  string user_postal_code = 31 [json_name = "user_postal_code"];
  string kyc_level = 32 [json_name = "kyc_level"];
  string overall_afu_status = 33 [json_name = "overall_afu_status"];
  repeated string auth_factors = 34 [json_name = "auth_factors"];
  string failure_reason = 35 [json_name = "failure_reason"];
  auth.afu.AuthFactorValues new_values = 36 [json_name = "new_values"];
  int64 afu_attempt_num = 37 [json_name = "afu_attempt_num"];
  repeated float afu_liveness_score = 38 [json_name = "afu_liveness_score"];
  repeated float afu_otp_score = 39 [json_name = "afu_otp_score"];
  repeated float afu_facematch_score = 40 [json_name = "afu_facematch_score"];
  repeated string vendor_request_statuses = 41 [json_name = "vendor_request_statuses"];
  string epifi_email_phone_num_update = 42 [json_name = "epifi_email_phone_num_update"];
  string epifi_device_update = 43 [json_name = "epifi_device_update"];
  auth.afu.AuthFactorValues current_values = 44 [json_name = "current_values"];
  api.typesv2.common.Device new_device = 45 [json_name = "new_device", deprecated = true];
  auth.afu.ActorAuthState actor_auth_state = 46 [json_name = "actor_auth_state"];
  string user_state = 47 [json_name = "user_state"];
  float afu_latitude = 48 [json_name = "afu_latitude"];
  float afu_longitude = 49 [json_name = "afu_longitude"];
  string onboarding_completed_at = 50 [json_name = "onboarding_completed_at"];
  repeated CreditReportAttributeInfo cb_details = 51 [json_name = "cb_details"];
  repeated string afu_liveness_statuses = 52 [json_name = "afu_liveness_status"];
  EmploymentData employment_data = 53 [json_name = "employment_data"];
  DeviceDetails device_details = 54 [json_name = "device_details"];
  repeated AFUAttempt afu_attempts = 55 [json_name = "afu_attempts"];
  ScreenerChecksInfo screener_checks_info = 56 [json_name = "screener_checks_info"];
  float onboarding_model_risk_score = 58 [json_name = "onboarding_model_risk_score"];
  AccountInfo account_info = 59 [json_name = "account_info"];
  DeviceDetails old_device_details = 60 [json_name = "old_device_details"];
  string is_credit_report_download_consent_given = 61 [json_name = "is_credit_report_download_consent_given"];
  // user image will be removed
  user.Profile profile = 62;
}

message DetectReOnboardingRiskResponse {
  float score = 1 [json_name = "score"];
  float threshold = 2 [json_name = "threshold"];
  // bool flag to denote if the user is risky
  bool risky_user = 3 [json_name = "risky_user"];
  // response time of the API
  float time = 4 [json_name = "time"];
  // list of errors returned by the API
  repeated string error = 5 [json_name = "error"];
}

message DetectLocationRiskRequest {
  string actor_id = 1 [json_name = "actor_id", (validate.rules).string.min_len = 1];

  string request_id = 2 [json_name = "request_id", (validate.rules).string.min_len = 1];

  string latitude = 3 [json_name = "latitude", (validate.rules).string.min_len = 1];

  string longitude = 4 [json_name = "longitude", (validate.rules).string.min_len = 1];

  string pincode = 5 [json_name = "pincode", (validate.rules).string.min_len = 1];
}

message DetectLocationRiskResponse {
  float score = 1 [json_name = "score"];

  string severity = 2 [json_name = "severity"];
  // location risk model version
  string version = 3 [json_name = "version"];
}

// EmploymentData will contain the information user has provided about their employment such as salary, employee info.
message EmploymentData {
  message Range {
    int32 min_value = 1 [json_name = "min_value"];
    int32 max_value = 2 [json_name = "max_value"];
  }

  string employment_type = 1 [json_name = "employment_type"];

  // company name selected/entered by salaried users
  string company_name = 2 [json_name = "company_name"];

  Range annual_salary_range = 3 [json_name = "annual_salary_range"];

  // if company is registered with EPFO
  bool is_company_epf_registered = 4 [json_name = "is_company_epf_registered"];
}

// DeviceDetails contains device details and device properties.
message DeviceDetails {
  string id = 1 [json_name = "id"];

  string manufacturer = 8 [json_name = "manufacturer"];

  bool is_premium = 9 [json_name = "is_premium"];

  // app version in semver format e.g. 4.2.0
  string app_version = 5 [json_name = "app_version"];

  string language = 6 [json_name = "language"];

  string sw_version = 7 [json_name = "sw_version"];

  // list of all apps installed
  repeated api.typesv2.UserDeviceInstalledAppInfo installed_apps = 10 [json_name = "installed_apps"];

  string model = 11 [json_name = "model"];
}

// AFUAttempt contains afu data for a single afu attempt such as liveness and facematch scores.
message AFUAttempt {
  string overall_status = 1 [json_name = "overall_status"];
  repeated string auth_factors = 2 [json_name = "auth_factors"];
  repeated float liveness_scores = 3 [json_name = "liveness_scores"];
  repeated float otp_scores = 4 [json_name = "otp_scores"];
  repeated float facematch_scores = 5 [json_name = "facematch_scores"];
  string created_at = 6 [json_name = "created_at"];
  repeated string liveness_statuses = 7 [json_name = "liveness_statuses"];
}

// ScreenerChecksInfo contains screener attempts and related checks.
message ScreenerChecksInfo {
  int32 credit_report_fail_count = 1 [json_name = "credit_report_fail_count"];
}

// AccountInfo contains info related to user account.
message AccountInfo {
  string tier = 1 [json_name = "tier"];
  string created_at = 2 [json_name = "created_at"];
}

message GetCasePrioritisationScoreRequest {
  message Alert {
    string case_id = 1 [json_name = "case_id"];
    string created_at = 2 [json_name = "created_at"];
  }
  message Rule {
    string name = 1 [json_name = "name"];
  }
  message AlertWithRuleDetails {
    Alert alert = 1 [json_name = "alert"];
    Rule rule = 2 [json_name = "rule"];
  }
  repeated AlertWithRuleDetails alert_with_rule_details = 1 [json_name = "alert_with_rule_details"];
  string actor_id = 2 [json_name = "actor_id"];
  string request_id = 3 [json_name = "request_id"];
  string model_version = 4 [json_name = "model_version"];
}


message ModelResponseInfo {
  string name = 1;
  float score = 2;
}

message GetCasePrioritisationScoreResponse {
  string request_id = 1 [json_name = "request_id"];
  string model_version = 2 [json_name = "model_version"];
  float score = 3 [json_name = "score"];
  repeated ModelResponseInfo model_info = 4;
}
