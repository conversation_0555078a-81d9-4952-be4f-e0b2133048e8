syntax = "proto3";

package vendors.inhouse.bre;

import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendors/inhouse/bre";
option java_package = "com.github.epifi.gamma.api.vendors.inhouse.bre";

message GetLoanDecisioningRequest {
  Values values = 1 [json_name = "values"];
  message Values {
    Input input = 1 [json_name = "input"];
    message Input {
      string actor_id = 1 [json_name = "actorId"];
      string name = 2 [json_name = "name"];
      string gender = 3 [json_name = "gender"];
      string pan = 4 [json_name = "pan"];
      string dob = 5 [json_name = "dob"];
      string address = 6 [json_name = "address"];
      int32 pincode = 7 [json_name = "pincode"];
      string employment_type = 8 [json_name = "employmentType"];
      string employer_name = 9 [json_name = "employerName"];
      string work_email = 10 [json_name = "workEmail"];
      int32 declared_income = 11 [json_name = "declaredIncome"];
      string loan_program = 12 [json_name = "loanProgram"];
      string scheme_id = 13 [json_name = "schemeId"];
      string batch_id = 14 [json_name = "batchId"];
      string kyc_level = 15 [json_name = "kycLevel"];
      PolicyParams policy_params = 16 [json_name = "policyParams"];
      string vendor = 17 [json_name = "vendor"];
      message PolicyParams {
        double pd_score = 3 [json_name = "pdScore"];
        string pricing_scheme = 4 [json_name = "pricingScheme"];
        int32 ever_vkyc_attempted = 5 [json_name = "everVkycAttempted"];
        string pd_score_version = 6 [json_name = "pdScoreVersion"];
        string scheme_id = 7 [json_name = "schemeId"];
        string pricing_scheme_bre = 8 [json_name = "pricingSchemeBRE"];
        string batch_id = 9 [json_name = "batchId"];
        //  new version of the API has pre and final params separately which is live for non fi core subvention and non fi core stpl
        //  old version of the API has flat params which is live for fi core subvention and stpl
        Pre pre = 10 [json_name = "pre"];
        message Pre {
          double pd_score = 1 [json_name = "pdScore"];
          string pd_score_version = 2 [json_name = "pdScoreVersion"];
          string scheme_id = 3 [json_name = "schemeId"];
          string batch_id = 4 [json_name = "batchId"];
          int32 ever_vkyc_attempted = 5 [json_name = "everVkycAttempted"];
          string pricing_scheme = 6 [json_name = "pricingScheme"];
        }
        Final final = 11 [json_name = "final"];
        message Final {
          string scheme_id = 1 [json_name = "schemeId"];
          string batch_id = 2 [json_name = "batchId"];
          string pricing_scheme_bre = 3 [json_name = "pricingSchemeBRE"];
        }
      }
    }
  }
}

message GetLoanDecisioningResponse {
  repeated Decision decision = 1 [json_name = "decision"];
  message Decision {
    string action = 1 [json_name = "action"];
    OfferDetails offer_details = 2 [json_name = "offerDetails"];
    string loan_program = 3 [json_name = "loanProgram"];
    string vendor = 4 [json_name = "vendor"];
    string scheme_id = 5 [json_name = "schemeId"];
    string actor_id = 6 [json_name = "actorId"];
    string batch_id = 7 [json_name = "batchId"];
    repeated string reasons = 8 [json_name = "reasons"];
    repeated string external_reasons = 9 [json_name = "externalReasons"];
    PolicyParams policy_params = 10 [json_name = "policyParams"];
    message PolicyParams {
      double pd_score = 3 [json_name = "pdScore"];
      string pricing_scheme = 4 [json_name = "pricingScheme"];
      int32 ever_vkyc_attempted = 5 [json_name = "everVkycAttempted"];
      string pd_score_version = 6 [json_name = "pdScoreVersion"];
      string scheme_id = 7 [json_name = "schemeId"];
      string pricing_scheme_bre = 8 [json_name = "pricingSchemeBRE"];
      string batch_id = 9 [json_name = "batchId"];
      //  new version of the API has pre and final params separately which is live for non fi core subvention and non fi core stpl
      //  old version of the API has flat params which is live for fi core subvention and stpl
      Pre pre = 10 [json_name = "pre"];
      message Pre {
        double pd_score = 1 [json_name = "pdScore"];
        string pd_score_version = 2 [json_name = "pdScoreVersion"];
        string scheme_id = 3 [json_name = "schemeId"];
        string batch_id = 4 [json_name = "batchId"];
        int32 ever_vkyc_attempted = 5 [json_name = "everVkycAttempted"];
        string pricing_scheme = 6 [json_name = "pricingScheme"];
      }
      Final final = 11 [json_name = "final"];
      message Final {
        string scheme_id = 1 [json_name = "schemeId"];
        string batch_id = 2 [json_name = "batchId"];
        string pricing_scheme_bre = 3 [json_name = "pricingSchemeBRE"];
      }
    }

    message OfferDetails {
      double min_amount = 1 [json_name = "minAmount"];
      double max_amount = 2 [json_name = "maxAmount"];
      double max_emi_amount = 3 [json_name = "maxEmiAmount"];
      double interest_percentage = 4 [json_name = "interestPercentage"];
      double processing_fee_percentage = 5 [json_name = "processingFeePercentage"];
      int32 min_tenure_in_months = 6 [json_name = "minTenureInMonths"];
      int32 max_tenure_in_months = 7 [json_name = "maxTenureInMonths"];
      string emi_due_date = 8 [json_name = "emiDueDate"];
      double gst_percentage = 9 [json_name = "gstPercentage"];
      string valid_till_timestamp = 10 [json_name = "validTill"];
    }
  }
}

message InhouseBRECheckForCCRequest {
  Values values = 1 [json_name = "values"];
  message Values {
    Input input = 1 [json_name = "input"];
    message Input {
      string actor_id = 1 [json_name = "actor_id"];
      string pincode = 2 [json_name = "pincode"];
      string dob = 3 [json_name = "dob"];
      string campaign_name = 4 [json_name = "campaign_name"];
    }
  }
}

message InhouseBRECheckForCCResponse {
  Entities entities = 1 [json_name = "entities"];
  Sources sources = 2 [json_name = "sources"];
  Flow flow = 3 [json_name = "flow"];

  message Flow {
    Products products = 1 [json_name = "products"];
    message Products {
      Product product = 1 [json_name = "product"];
      message Product {
        string action = 1 [json_name = "action"];
        repeated string reasons = 2 [json_name = "reasons"];
      }
    }
  }
  message Entities {
    Values values = 1 [json_name = "values"];
    message Values {
      Strategy strategy = 1 [json_name = "strategy"];
      Splitter splitter = 2 [json_name = "splitter"];
      message Splitter {
        Split split = 1 [json_name = "fed_cc_dist_fi_lite_PS1_Random_split_send_to_fed"];
        message Split {
          int32 index = 1 [json_name = "index"];
        }
      }
      message Strategy {
        CCEligibilityChecks cc_eligibility_checks = 1 [json_name = "CC_Eligibility_Checks"];
        DeclineReasonsPd decline_reasons_pd = 2 [json_name = "Decline_reason_PD"];
        message CCEligibilityChecks {
          string action = 1 [json_name = "action"];
          repeated string reasons = 2 [json_name = "reasons"];
        }
        message DeclineReasonsPd {
          repeated string reasons = 1 [json_name = "reasons"];
        }
      }
    }
  }

  message Sources {
    Values values = 1 [json_name = "values"];
    message Values {
      Conversions conversions = 1 [json_name = "conversions"];
      ConversionsV2 conversions_v2 = 2 [json_name = "conversions_v2"];
      PdModelData pd_model_data = 3 [json_name = "pd_model"];
      message Conversions {
        int32 age = 1 [json_name = "age"];
        string report_id = 2 [json_name = "report_id"];
        int32 bureau_score = 3 [json_name = "bureau_score"];
        int32 joint_ownership_only_flag = 4 [json_name = "joint_ownership_only_flag"];
        int32 gl_count_trades_opened_l6m = 5 [json_name = "gl_count_trades_opened_l6m"];
        int32 el_active_flag = 6 [json_name = "el_active_flag"];
        int32 overall_sf_wd_wo_flag_ever = 7 [json_name = "overall_sf_wd_wo_flag_ever"];
        int32 overall_sub_dbt_lss_flag_ever = 8 [json_name = "overall_sub_dbt_lss_flag_ever"];
        int32 overall_rs_flag_ever = 9 [json_name = "overall_rs_flag_ever"];
        int32 overall_count_inquiries_l6m = 10 [json_name = "overall_count_inquiries_l6m"];
        int32 overall_max_dpd_ever = 11 [json_name = "overall_max_dpd_ever"];
        int32 overall_sma_l12m_flag = 12 [json_name = "overall_sma_l12m_flag"];
        int32 overall_max_dpd_l6m = 13 [json_name = "overall_max_dpd_l6m"];
        int32 overall_max_dpd_l3m = 14 [json_name = "overall_max_dpd_l3m"];
        int32 overall_count_months_gte30_dpd_l12m = 15 [json_name = "overall_count_months_gte30_dpd_l12m"];
        int32 overall_max_dpd_l1m = 16 [json_name = "overall_max_dpd_l1m"];
        int32 active_permissible_loan_eligible_flag = 17 [json_name = "active_permissible_loan_eligible_flag"];
        int32 active_permissible_limit_eligible_flag = 18 [json_name = "active_permissible_limit_eligible_flag"];
        int32 active_variant_assigned_flag = 19 [json_name = "active_variant_assigned_flag"];
      }
      message ConversionsV2 {
        int32 age = 1 [json_name = "age"];
        string report_id = 2 [json_name = "report_id"];
        int32 bureau_score = 3 [json_name = "bureau_score"];
        int32 joint_ownership_only_flag = 4 [json_name = "joint_ownership_only_flag"];
        int32 gl_count_trades_opened_l6m = 5 [json_name = "gl_count_trades_opened_l6m"];
        int32 el_active_flag = 6 [json_name = "el_active_flag"];
        int32 overall_sf_wd_wo_flag_ever = 7 [json_name = "overall_sf_wd_wo_flag_ever"];
        int32 overall_sub_dbt_lss_flag_ever = 8 [json_name = "overall_sub_dbt_lss_flag_ever"];
        int32 overall_rs_flag_ever = 9 [json_name = "overall_rs_flag_ever"];
        int32 overall_count_inquiries_l6m = 10 [json_name = "overall_count_inquiries_l6m"];
        int32 overall_max_dpd_ever = 11 [json_name = "overall_max_dpd_ever"];
        int32 overall_sma_l12m_flag = 12 [json_name = "overall_sma_l12m_flag"];
        int32 overall_max_dpd_l6m = 13 [json_name = "overall_max_dpd_l6m"];
        int32 overall_max_dpd_l3m = 14 [json_name = "overall_max_dpd_l3m"];
        int32 overall_count_months_gte30_dpd_l12m = 15 [json_name = "overall_count_months_gte30_dpd_l12m"];
        int32 overall_max_dpd_l1m = 16 [json_name = "overall_max_dpd_l1m"];
        int32 active_permissible_loan_eligible_flag = 17 [json_name = "active_permissible_loan_eligible_flag"];
        int32 active_permissible_limit_eligible_flag = 18 [json_name = "active_permissible_limit_eligible_flag"];
        int32 active_variant_assigned_flag = 19 [json_name = "active_variant_assigned_flag"];
        int32 non_cc_vintage = 21 [json_name = "non_cc_vintage"];
        float cc_current_utilisation_perc = 22 [json_name = "cc_current_utilisation_perc"];
        int32 cc_active_flag = 23 [json_name = "cc_active_flag"];
        int32 al_max_amout_ever = 24 [json_name = "alMaxAmtEver"];
        int32 hl_max_amout_ever = 25 [json_name = "hlMaxAmtEver"];
        int32 pl_max_amout_ever = 26 [json_name = "plMaxAmtEver"];
        int32 cc_max_amout_ever = 27 [json_name = "ccMaxAmtEver"];
        int32 bureau_vintage = 28 [json_name = "bureauVintage"];
        int32 max_thick_loan_amount = 29 [json_name = "maxThickLoanAmt"];
        string max_thick_loan_amount_bucket = 30 [json_name = "maxThickLoanAmtBucket"];
        string bureau_vintage_bucket = 31 [json_name = "bureauVintageBucket"];
        string bureau_thickness = 32 [json_name = "bureauThickness"];
      }
      message PdModelData {
        string MOB6_30P_EOP = 1 [json_name = "MOB6_30P_EOP"];
        float probability_0 = 2 [json_name = "probability_0"];
        float probability_1 = 3 [json_name = "probability_1"];
      }
    }
  }
}

message GetLoanPreScreeningRequest {
  Values values = 1 [json_name = "values"];
  message Values {
    Input input = 1 [json_name = "input"];
    message Input {
      string actor_id = 1 [json_name = "actorId"];
      string name = 2 [json_name = "name"];
      string gender = 3 [json_name = "gender"];
      string pan = 4 [json_name = "pan"];
      string dob = 5 [json_name = "dob"];
      string address = 6 [json_name = "address"];
      int32 pincode = 7 [json_name = "pincode"];
      string employment_type = 8 [json_name = "employmentType"];
      string employer_name = 9 [json_name = "employerName"];
      string work_email = 10 [json_name = "workEmail"];
      int32 declared_income = 11 [json_name = "declaredIncome"];
      string loan_program = 12 [json_name = "loanProgram"];
    }
  }
}

message GetLoanPreScreeningResponse {
  repeated Decision decision = 1 [json_name = "decision"];
  message Decision {
    string action = 1 [json_name = "action"];
    string scheme_id = 2 [json_name = "schemeId"];
    string batch_id = 3 [json_name = "batchId"];
    repeated string reasons = 4 [json_name = "reasons"];
    repeated string external_reasons = 5 [json_name = "externalReasons"];
    FinalOfferBreRequirements final_offer_bre_requirements = 6 [json_name = "finalOfferBreRequirements"];
    message FinalOfferBreRequirements {
      bool epfo = 1 [json_name = "EPFO"];
      bool aa = 2 [json_name = "AA"];
      bool cibil = 3 [json_name = "CIBIL"];
    }
    PolicyParams policy_params = 7 [json_name = "policyParams"];
    message PolicyParams {
      double pd_score = 1 [json_name = "pdScore"];
      string pricing_scheme = 2 [json_name = "pricingScheme"];
      int32 ever_vkyc_attempted = 3 [json_name = "everVkycAttempted"];
      string pd_score_version = 4 [json_name = "pdScoreVersion"];
      string scheme_id = 5 [json_name = "schemeId"];
      string pricing_scheme_bre = 6 [json_name = "pricingSchemeBRE"];
      string batch_id = 7 [json_name = "batchId"];
      //  new version of the API has pre and final params separately which is live for non fi core subvention and non fi core stpl
      //  old version of the API has flat params which is live for fi core subvention and stpl
      Pre pre = 8 [json_name = "pre"];
      message Pre {
        double pd_score = 1 [json_name = "pdScore"];
        string pd_score_version = 2 [json_name = "pdScoreVersion"];
        string scheme_id = 3 [json_name = "schemeId"];
        string batch_id = 4 [json_name = "batchId"];
        int32 ever_vkyc_attempted = 5 [json_name = "everVkycAttempted"];
        string pricing_scheme = 6 [json_name = "pricingScheme"];
      }
      Final final = 9 [json_name = "final"];
      message Final {
        string scheme_id = 1 [json_name = "schemeId"];
        string batch_id = 2 [json_name = "batchId"];
        string pricing_scheme_bre = 3 [json_name = "pricingSchemeBRE"];
      }
    }
  }
}

message GetPreBreEligibilityDetailsRequest {
  Values values = 1 [json_name = "values"];

  message Values {
    Input input = 1 [json_name = "input"];

    message Input {
      string actorId = 1 [json_name = "actorId"];
      string evaluationRequestTime = 2 [json_name = "evaluationRequestTime"];
      string requestId = 3 [json_name = "requestId"];
      CustomerDetails customerDetails = 4 [json_name = "customerDetails"];
      PolicyParams policyParams = 5 [json_name = "policyParams"];
      // TODO: add a field of extra details, when requirements comes
      // ExtraDetails extraDetails = 6 [json_name = "extraDetails"];
    }
  }
}

message GetPreBreEligibilityDetailsResponse {
  PreBreDecision decision = 1 [json_name = "decision"];
  google.protobuf.Struct details = 2;
  message PreBreDecision {
    string actorId = 1 [json_name = "actorId"];
    string evaluationRequestTime = 2 [json_name = "evaluationRequestTime"];
    string requestId = 3 [json_name = "requestId"];
    string evaluationRunTime = 4 [json_name = "evaluationRunTime"];
    string validTill = 5 [json_name = "validTill"];
    PolicyParams policyParams = 6 [json_name = "policyParams"];
    repeated string validLenders = 7 [json_name = "validLenders"];
    int32 numberValidLenders = 8 [json_name = "numberValidLenders"];
    repeated RejectionReasons rejectionReasons = 9 [json_name = "rejectionReasons"];
  }
}

message GetFinalBreEligibilityDetailsRequest {
  Values values = 1 [json_name = "values"];

  message Values {
    Input input = 1 [json_name = "input"];

    message Input {
      string actorId = 1 [json_name = "actorId"];
      string evaluationRequestTime = 2 [json_name = "evaluationRequestTime"];
      string requestId = 3 [json_name = "requestId"];
      CustomerDetails customerDetails = 4 [json_name = "customerDetails"];
      string vendor = 5 [json_name = "vendor"];
      string product = 6 [json_name = "product"];
      DataAvailability dataAvailability = 7 [json_name = "dataAvailability"];
      PolicyParams policyParams = 8 [json_name = "policyParams"];
    }
  }

}

message GetFinalBreEligibilityDetailsResponse {
  string evaluationRequestTime = 1 [json_name = "evaluationRequestTime"];
  string requestId = 2 [json_name = "requestId"];
  string actorId = 3 [json_name = "actorId"];
  string vendor = 4 [json_name = "vendor"];
  string product = 5 [json_name = "product"];
  DataAvailability dataAvailability = 6 [json_name = "dataAvailability"];
  // key -> loan_program, value -> if true return send the required details in next call
  // ex. "dataRequirements":{"PL_SALARIED_P001": false,"PL_SALARIED_P002": true,"PL_SALARIED_P003": true}
  map<string, bool> dataRequirements = 7 [json_name = "dataRequirements"];
  PolicyParams policyParams = 8 [json_name = "policyParams"];
  bool subsequentCallAllowed = 9 [json_name = "subsequentCallAllowed"];
  google.protobuf.Struct details = 10 [json_name = "details"];
  FinalBreDecision prioritizedDecision = 11 [json_name = "prioritizedDecision"];
  repeated FinalBreDecision decision = 12 [json_name = "decision"];
  string evaluationRunTime = 13 [json_name = "evaluationRunTime"];

}

message GetPreBreEligibilityOfferRequest {
  Values values = 1 [json_name = "values"];

  message Values {
    Input input = 1 [json_name = "input"];

    message Input {
      string actor_id = 1 [json_name = "actorId"];
      string evaluation_request_time = 2 [json_name = "evaluationRequestTime"];
      string request_id = 3 [json_name = "requestId"];
      CustomerDetails customer_details = 4 [json_name = "customerDetails"];
      string product = 5 [json_name = "product"];
      DataAvailability data_availability = 6 [json_name = "dataAvailability"];
      PolicyParams policy_params = 7 [json_name = "policyParams"];
    }
  }

}

message GetPreBreEligibilityOfferResponse {
  string evaluation_request_time = 1 [json_name = "evaluationRequestTime"];
  string request_id = 2 [json_name = "requestId"];
  string actor_id = 3 [json_name = "actorId"];
  string product = 4 [json_name = "product"];
  DataAvailability data_availability = 5 [json_name = "dataAvailability"];
  // key -> loan_program, value -> if true return send the required details in next call
  // ex. "dataRequirements":{"PL_SALARIED_P001": false,"PL_SALARIED_P002": true,"PL_SALARIED_P003": true}
  map<string, bool> data_requirements = 6 [json_name = "dataRequirements"];
  PolicyParams policy_params = 7 [json_name = "policyParams"];
  bool subsequent_call_allowed = 8 [json_name = "subsequentCallAllowed"];
  google.protobuf.Struct details = 9 [json_name = "details"];
  repeated FinalBreDecision decisions = 10 [json_name = "decision"];
  string evaluation_run_time = 11 [json_name = "evaluationRunTime"];

}

message FinalBreDecision {
  string lendingProgram = 1 [json_name = "lendingProgram"];
  string action = 2 [json_name = "action"];
  repeated string conditions = 3 [json_name = "conditions"];
  OfferDetails offerDetails = 4 [json_name = "offerDetails"];
  repeated string rejectionReasons = 5 [json_name = "rejectionReasons"];
  repeated string externalReasons = 6 [json_name = "externalReasons"];
  string validTill = 7 [json_name = "validTill"];
  string bureau = 8 [json_name = "bureau"];
  string reportDate = 9 [json_name = "reportDate"];
  string reportId = 10 [json_name = "reportId"];
  string schemeId = 11 [json_name = "schemeId"];
  string strategy = 12 [json_name = "strategy"];
}

message OfferDetails {
  string emiDueDate = 1 [json_name = "emiDueDate"];
  double gstPercentage = 2 [json_name = "gstPercentage"];
  double interestPercentage = 3 [json_name = "interestPercentage"];
  double maxAmount = 4 [json_name = "maxAmount"];
  double maxEmiAmount = 5 [json_name = "maxEmiAmount"];
  int32 maxTenureInMonths = 6 [json_name = "maxTenureInMonths"];
  double minAmount = 7 [json_name = "minAmount"];
  int32 minTenureInMonths = 8 [json_name = "minTenureInMonths"];
  double processingFeePercentage = 9 [json_name = "processingFeePercentage"];
  string pricingScheme = 10 [json_name = "pricingScheme"];
}

// This is common to both Pre bre and final bre, only add fields which are common to both
message CustomerDetails {
  PersonalDetails personalDetails = 1 [json_name = "personalDetails"];
  EmploymentDetails employmentDetails = 2 [json_name = "employmentDetails"];
  AddressDetails addressDetails = 3 [json_name = "addressDetails"];
  RequestedLoanDetails requested_loan_details = 5 [json_name = "requestedLoanDetails"];
}

message RequestedLoanDetails {
  double desired_loan_amount = 1 [json_name = "desiredLoanAmount"];
}

message PersonalDetails {
  string dob = 1 [json_name = "dob"];
  string gender = 2 [json_name = "gender"];
  string name = 3 [json_name = "name"];
  string pan = 4 [json_name = "pan"];
  string phoneNumber = 5 [json_name = "phoneNumber"];
  string email = 6 [json_name = "email"];
}

message EmploymentDetails {
  string employerName = 1 [json_name = "employerName"];
  string employmentType = 2 [json_name = "employmentType"];
  int32 declaredMonthlyIncome = 3 [json_name = "declaredMonthlyIncome"];
  string workEmail = 4 [json_name = "workEmail"];
  string workAddress = 5 [json_name = "workAddress"];
  int32 workPincode = 6 [json_name = "workPincode"];
}

message AddressDetails {
  string address = 1 [json_name = "address"];
  int32 pincode = 2 [json_name = "pincode"];
  string addressType = 3 [json_name = "addressType"];
}

message RejectionReasons {
  string lender = 1 [json_name = "lender"];
  string decision = 2 [json_name = "decision"];
  repeated string rejection_reason = 3 [json_name = "rejectionReason"];
}

// Data Availability
message DataAvailability {
  DataAvailabilityDetails epfo = 1 [json_name = "EPFO"];
  DataAvailabilityDetails aa = 2 [json_name = "AA"];
  DataAvailabilityDetails cibil = 3 [json_name = "CIBIL"];
  DataAvailabilityDetails experian = 4 [json_name = "EXPERIAN"];
  message DataAvailabilityDetails {
    bool isAvailable = 1 [json_name = "isAvailable"];
    string collectionDate = 2 [json_name = "collectionDate"];  // YYYY-MM-DD format
  }
}

// Policy Parameters
message PolicyParams {
  ExecutionInfo executionInfo = 1 [json_name = "executionInfo"];
  DataInfo dataInfo = 2 [json_name = "dataInfo"];
}

// contains information that is generated by BRE for consumption in subsequent calls
message ExecutionInfo {
  repeated PolicyPramsDetails pre = 1 [json_name = "pre"];
  repeated PolicyPramsDetails final = 2 [json_name = "final"];
}

// common policy params for both pre-bre and final-bre
message PolicyPramsDetails {
  string batchId = 1 [json_name = "batchId"];
  string schemeId = 2 [json_name = "schemeId"];
  string lendingProgram = 3 [json_name = "lendingProgram"];
  string preBreRequestId = 4 [json_name = "preBRERequestId"];
  string finalBreRequestId = 5 [json_name = "finalBRERequestId"];
}

message DataInfo {
  AaData aa_data = 1 [json_name = "aaData"];
  bool isEtbUser = 2 [json_name = "isEtbUser"];
  message AaData {
    double median_amount_salary_last_180_days = 1 [json_name = "medianAmountSalaryLast180Days"];
  }
  bool is_b2b_salary_user = 3 [json_name = "isB2bSalaryUser"];
  double monthly_income = 4 [json_name = "monthlyIncome"];
  int32 months_since_salary_active = 5 [json_name = "monthSinceSalaryActive"];
  int32 salary_credit_day = 6 [json_name = "salaryCreditDay"];
}


