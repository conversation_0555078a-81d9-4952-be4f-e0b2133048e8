syntax = "proto3";

package api.vendors.pushnotification;

option go_package = "github.com/epifi/gamma/api/vendors/pushnotification";
option java_package = "com.github.epifi.gamma.api.vendors.pushnotification";

message SendPushNotificationRequest {
  // JWT token containing a serialized SendPushNotificationRequestPayload as the 'request_payload' claim.
  string token = 1;
}

message SendPushNotificationRequestPayload {
  // Unique user identifier at vendor's end
  string vendor_internal_user_id = 1 [json_name = "internalUserId"];

  // Name of the vendor (e.g., SAVEN, M2P, etc.)
  string vendor = 2 [json_name = "vendor"];

  // Push notification content
  PushNotificationContent content = 3 [json_name = "content"];

  // Specific notification ID for tracking/deduplication
  string notification_id = 4 [json_name = "notificationId"];

  // Timestamp when the notification was created/originated at vendor's end in RFC3339 format
  string created_at = 5 [json_name = "createdAt"];

  // Optional: Expiry time for the notification in RFC3339 format
  string expires_at = 6 [json_name = "expiresAt"];
}

message PushNotificationContent {
  // Notification title (required)
  string title = 1 [json_name = "title"];

  // Notification subtitle/body (required)
  string subtitle = 2 [json_name = "subtitle"];

  // Optional: Icon URL for the notification
  // Defaults to Fi icon url
  string icon_url = 3 [json_name = "iconUrl"];

  // Optional: Image URL for rich notifications
  // Defaults to no image
  string image_url = 4 [json_name = "imageUrl"];

  // Optional: Deep link for navigation
  // Defaults to deeplink mapped to the vendor if exists, else Home deeplink
  string deeplink = 5 [json_name = "deeplink"];
}
