//go:generate gen_sql -types=Exchange,ListingStatus,HistoricalPriceData,IntervalPriceData,IntervalDuration,PriceDataPoint,FinancialInfo,FundamentalParameters
syntax = "proto3";

package api.securities.catalog;

import "api/securities/catalog/enums.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/securities/catalog";

// SecurityListing represents a listing of a security (such as a stock) on a specific exchange.
// It contains identifiers, exchange and symbol information, vendor details, and status fields.
// This message is designed to uniquely identify and describe a security's presence on a market.
message SecurityListing {
  // Unique identifier for this security listing (e.g., "SL" + random string)
  string internal_id = 1;
  // External id is the unique identifier in the usstocks table (format: USS/INS + idGen.RandAlphaNumericString(5)).
  // This field can be used to fetch the listing by its external reference.
  string external_id = 2;
  // Foreign key referencing the Security object this listing belongs to
  string security_id = 3;
  // The exchange where this security is listed (e.g., NSE, NYSE, NASDAQ)
  Exchange exchange = 4;
  // The trading symbol/ticker for this listing on the exchange
  string symbol = 5;
  // True if this is the primary listing for the security
  bool is_primary_listing = 6;
  // The current status of this listing (active, inactive, etc.)
  ListingStatus status = 7;
  // Financial information for this listing like PE, PB, etc.
  FinancialInfo financial_info = 8;
  // International Securities Identification Number (ISIN) for this listing
  string isin = 9;
  // Vendor information (e.g., data provider or source)
  vendorgateway.Vendor vendor = 10;
  // The vendor's unique identifier for this listing
  string vendor_listing_id = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  google.protobuf.Timestamp deleted_at = 14;
}

enum SecurityListingFieldMask {
  SECURITY_LISTING_FIELD_MASK_UNSPECIFIED = 0;
  SECURITY_LISTING_FIELD_MASK_INTERNAL_ID = 1;
  SECURITY_LISTING_FIELD_MASK_EXTERNAL_ID = 2;
  SECURITY_LISTING_FIELD_MASK_SECURITY_ID = 3;
  SECURITY_LISTING_FIELD_MASK_EXCHANGE = 4;
  SECURITY_LISTING_FIELD_MASK_SYMBOL = 5;
  SECURITY_LISTING_FIELD_MASK_IS_PRIMARY_LISTING = 6;
  SECURITY_LISTING_FIELD_MASK_STATUS = 7;
  SECURITY_LISTING_FIELD_MASK_FINANCIAL_INFO = 8;
  SECURITY_LISTING_FIELD_MASK_ISIN = 9;
  SECURITY_LISTING_FIELD_MASK_VENDOR = 10;
  SECURITY_LISTING_FIELD_MASK_VENDOR_LISTING_ID = 11;
  SECURITY_LISTING_FIELD_MASK_CREATED_AT = 12;
  SECURITY_LISTING_FIELD_MASK_UPDATED_AT = 13;
  SECURITY_LISTING_FIELD_MASK_DELETED_AT = 14;
}

// FinancialInfo contains the financial information for the stock
message FinancialInfo {
  // Market Capitalization (Market Cap) is the total market value of a company's outstanding shares of stock.
  // It is calculated as share price times the number of shares outstanding and is used to measure a company's size and investment risk.
  google.type.Money market_cap = 1;
  // Fundamental parameters for the stock based on the latest trailing twelve months (TTM) data
  FundamentalParameters ttm_fundamental_parameters = 2;
}

message FundamentalParameters {
  // Book Value Per Share (BVPS) represents the equity available to common shareholders divided by the number of outstanding shares. It indicates the per-share value of a company's net assets and is used to assess whether a stock is undervalued or overvalued compared to its market price.
  google.type.Money book_value_per_share = 1;
  // Price-to-Earnings (P/E) Ratio is calculated as the market price per share divided by earnings per share. It measures how much investors are willing to pay for each dollar of earnings and is widely used to value companies and compare them across industries.
  double pe_ratio = 2;
  // Price-to-Book (P/B) Ratio compares a company's market value to its book value. It is calculated as the market price per share divided by book value per share. A lower P/B ratio may indicate an undervalued stock, while a higher ratio may suggest overvaluation.
  double pb_ratio = 3;
  // Dividend Yield is the ratio of a company's annual dividend per share to its share price. It shows the return on investment from dividends alone and is important for income-focused investors.
  double dividend_yield = 4;
  // Return on Equity (ROE) measures a company's profitability by showing how much profit it generates with the money shareholders have invested. It is calculated as net income divided by shareholder equity.
  double return_on_equity = 5;
  // Shares Outstanding is the total number of a company's shares that are currently held by all its shareholders. It is used in the calculation of metrics like earnings per share and book value per share.
  double shares_outstanding = 6;
}
