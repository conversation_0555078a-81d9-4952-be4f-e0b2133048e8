syntax = "proto3";

package api.securities.catalog;

import "api/rpc/status.proto";
import "api/securities/catalog/enums.proto";
import "api/securities/catalog/model_security.proto";
import "api/securities/catalog/model_security_listing.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/securities/catalog";


//  The SecuritiesCatalog is designed to provide access to a comprehensive catalog of securities and their
//  associated listings. This service exposes methods for retrieving detailed metadata about individual securities
//  (e.g., company information, financial data, etc.) and their exchange-specific listings (e.g., stock symbols,
//  trading item IDs, and historical price data).
//
//  This service is essential for applications that require information about traded securities, including
//  but not limited to financial platforms, trading applications, market data services, and investment analysis tools.
service SecuritiesCatalog {
  // Retrieves market information about a single security using its ID.
  // This includes company details, financials, internal status for a equity security and ETF Details for an ETF.
  //
  // Use this when you need full context on the underlying security (e.g., for display, analysis, or enrichment).
  //
  // Typical use cases:
  // - Display security page with full company profile and financial metrics
  rpc GetSecurity (GetSecurityRequest) returns (GetSecurityResponse);

  // Retrieves market information about a multiple securities using thiers IDs.
  // This includes company details, financials, internal status for a equity security and ETF Details for an ETF.
  //
  // Use this when you need full context on the underlying security (e.g., for display, analysis, or enrichment).
  //
  // Typical use cases:
  // - Display security pages with full company profile and financial metrics
  rpc GetSecurities (GetSecuritiesRequest) returns (GetSecuritiesResponse);

  // Fetches a specific security listing (i.e., how a security is traded on a specific exchange).
  // Listings include stock symbols, trading item IDs, price data, and exchange-level details.
  //
  // Use this when working with exchange-level trading data such as tickers, ISIN mappings,
  // or historical price series.
  //
  // Typical use cases:
  // - Fetch stock symbol and exchange metadata
  // - Fetch exchange-level historical pricing, mcap etc.
  rpc GetSecurityListing (GetSecurityListingRequest) returns (GetSecurityListingResponse);

  // Fetches security listings in bulk (i.e., how a security is traded on a specific exchange).
  // Listings include stock symbols, trading item IDs, price data, and exchange-level details.
  //
  // Use this when working with exchange-level trading data such as tickers, ISIN mappings,
  // or historical price series.
  //
  // Typical use cases:
  // - Fetch stock symbol and exchange metadata
  // - Fetch exchange-level historical pricing, mcap etc.
  rpc GetSecurityListings (GetSecurityListingsRequest) returns (GetSecurityListingsResponse);

  // Retrieves historical price data for securities on a specified date.
  // This includes closing prices, vendor information, and relevant timestamps.
  //
  // Use this when you need historical price information for securities on a specific date.
  //
  // Typical use cases:
  // - Display historical price charts
  // - Calculate historical returns
  // - Analyze price movements
  rpc GetPriceByDateAndSecListingIDs (GetPriceByDateAndSecListingIDsRequest) returns (GetPriceByDateAndSecListingIDsResponse);

  // Retrieves security listing IDs for given ISINs and exchanges.
  // This helps in mapping ISINs to their specific exchange listing IDs.
  //
  // Use this when you need to get the security listing IDs for specific exchanges using ISINs.
  //
  // Typical use cases:
  // - Map multiple ISINs to exchange-specific listing IDs
  // - Handle different exchange listings (NSE/BSE) for multiple securities
  rpc GetSecListingIdsByISINs (GetSecListingIdsByISINsRequest) returns (GetSecListingIdsByISINsResponse);

  // AddSecurityWithISINs attempts to ensure that each ISIN in the request is correctly mapped to a security.
  //
  // For each ISIN in the request
  // - The RPC fetches security data from the vendor.
  // - If a matching security already exists in the system, it creates a mapping between the ISIN and that security.
  // - If no matching security exists, it creates both a new security record and a new security listing record.
  //
  // Use this when you need to add mapping for a security/listing with an ISIN
  rpc AddSecurityWithISINs (AddSecurityWithISINsRequest) returns (AddSecurityWithISINsResponse);
}

message GetSecurityRequest {
  string id = 1;
  // Specifies which fields of the Security object should be populated in the response.
  // Only the requested fields will be fetched and populated with data.
  // Fields not included in this list might have their zero/default values.
  // This allows for more efficient and targeted data retrieval.
  repeated SecurityFieldMask fields = 2;
}

message GetSecurityResponse {
  rpc.Status status = 1;
  Security security = 2;
}

message GetSecuritiesRequest {
  repeated string ids = 1;
  // Specifies which fields of the Security object should be populated in the response.
  // Only the requested fields will be fetched and populated with data.
  // Fields not included in this list might have their zero/default values.
  // This allows for more efficient and targeted data retrieval.
  repeated SecurityFieldMask fields = 2;
}

message GetSecuritiesResponse {
  rpc.Status status = 1;
  // map of security id to security object
  map<string, Security> securities_map = 2;
}

message ExchangeSymbol {
  string symbol = 1;
  Exchange exchange = 2;
}

message GetSecurityListingRequest {
  oneof identifier {
    string external_id = 1;
    ExchangeSymbol exchange_symbol = 2;
  }

  // Specifies which fields of the Security object should be populated in the response.
  // Only the requested fields will be fetched and populated with data.
  // Fields not included in this list might have their zero/default values.
  // This allows for more efficient and targeted data retrieval.
  repeated SecurityFieldMask security_fields = 4;

  // Specifies which fields of the Security Listing object should be populated in the response.
  // Only the requested fields will be fetched and populated with data.
  // Fields not included in this list might have their zero/default values.
  // While requesting for both security and security listing data, we will be making 2 dao calls,
  // How?, use external_id/exchange_symbol to fetch security listing and then use security_id from the listing to fetch security data.
  // This might add latency to this RPC.
  repeated SecurityListingFieldMask security_listing_fields = 5;
}

message GetSecurityListingResponse {
  rpc.Status status = 1;

  // Only the requested fields per object will be sent in the response.
  Security security = 2;
  SecurityListing security_listing = 3;
}

message GetSecurityListingsRequest {
  message ExternalIds {
    repeated string external_ids = 1;
  }
  message ExchangeSymbols {
    repeated ExchangeSymbol exchange_symbols = 1;
  }
  oneof identifiers {
    ExternalIds external_ids = 1;
    ExchangeSymbols exchange_symbols = 2;
  }

  // Specifies which fields of the Security object should be populated in the response.
  // Only the requested fields will be fetched and populated with data.
  // Fields not included in this list might have their zero/default values.
  // This allows for more efficient and targeted data retrieval.
  repeated SecurityFieldMask security_fields = 4;

  // Specifies which fields of the Security Listing object should be populated in the response.
  // Only the requested fields will be fetched and populated with data.
  // Fields not included in this list might have their zero/default values.
  // While requesting for both security and security listing data, we will be making 2 dao calls,
  // How?, use external_id/exchange_symbol to fetch security listing and then use security_id from the listing to fetch security data.
  // This might add latency to this RPC.
  repeated SecurityListingFieldMask security_listing_fields = 5;
}

message GetSecurityListingsResponse {
  rpc.Status status = 1;
  repeated SecurityAndSecurityListing security_and_security_listings = 2;
}

message SecurityAndSecurityListing {
  // Only the requested fields per object will be sent in the response.
  Security security = 2;
  SecurityListing security_listing = 3;
}

message GetPriceByDateAndSecListingIDsRequest {
  // List of security listing identifiers to fetch historical prices for
  repeated string security_listing_ids = 1;

  // The date for which historical prices are requested
  google.type.Date price_date = 2;
}

message GetPriceByDateAndSecListingIDsResponse {
  rpc.Status status = 1;
  // Map of security_listing_id to its price data
  map<string, google.type.Money> prices = 2;
}

message GetSecListingIdsByISINsRequest {
  // List of ISIN and exchange combinations to fetch security listing IDs for
  repeated ISINExchangePair isin_exchange_pairs = 1;
}

message ISINExchangePair {
  // The ISIN identifier of the security
  string isin = 1;

  // The exchange for which to get the listing ID
  Exchange exchange = 2;
}

message SecListingIdWithISIN {
  ISINExchangePair isin_exchange_pair = 1;
  string security_listing_external_id = 2;
}

message GetSecListingIdsByISINsResponse {
  rpc.Status status = 1;

  // List of security listing IDs, each corresponding to an ISIN and exchange.
  repeated SecListingIdWithISIN sec_listing_ids_with_isins = 2;
}


message AddSecurityWithISINsRequest {
  repeated string isins = 1;
}

message AddSecurityWithISINsResponse {
  rpc.Status status = 1;
  repeated IsinSecurityListingPair isin_security_listing_pairs = 2;
}

message IsinSecurityListingPair {
  string isin = 1;
  string security_listing_id = 2;
}
