//go:generate gen_sql -types=Exchange,SecurityType,GICSSectorType,GICSIndustryGroupType,GICSIndustryType,ListingStatus
syntax = "proto3";

package api.securities.catalog;

option go_package = "github.com/epifi/gamma/api/securities/catalog";

// Exchange enum follows the naming convention of EXCHANGE_[COUNTRY]_[EXCHANGE],
// where COUNTRY is the ISO country code and EXCHANGE is the abbreviated name of the exchange.
// This provides a consistent and clear identification system for global exchanges.
enum Exchange {
  EXCHANGE_UNSPECIFIED = 0;
  // National Stock Exchange
  EXCHANGE_INDIA_NSE = 1;
  // Bombay Stock Exchange
  EXCHANGE_INDIA_BSE = 2;
  // New York Stock Exchange
  EXCHANGE_USA_NYSE = 3;
  EXCHANGE_USA_NASDAQ = 4;
  // American Stock Exchange
  EXCHANGE_USA_ASE = 5;
  // NYSE Arca was formed from the 2006 merger of the New York Stock Exchange (NYSE) and Archipelago (Arca)
  EXCHANGE_USA_ARCA = 6;
  // Bats Global Markets was previously known as Better Alternative Trading System (BATS)
  // it was initially branded as an alternative trading platform, marketing itself to investors as a company that was more innovative than established exchanges
  EXCHANGE_USA_BATS = 7;
  // Pink sheets are listings for stocks that trade over-the-counter (OTC) rather than on a major U.S. stock exchange
  EXCHANGE_USA_PINX = 8;
  // OTC exchange for trading delisted stocks. Alpaca does not support buying OTC stocks however sell orders for OTC are allowed
  // users with existing holdings in OTC stock are expected to sell them.
  EXCHANGE_USA_OTC = 9;
  // London Stock Exchange
  EXCHANGE_UK_LSE = 10;
}

// SecurityType refers to the type of security
enum SecurityType {
  SECURITY_TYPE_UNSPECIFIED = 0;
  SECURITY_TYPE_STOCK = 1;
  SECURITY_TYPE_ETF = 2;
}

// GICS sector types defined at https://www.msci.com/our-solutions/indexes/gics
enum GICSSectorType {
  GICS_SECTOR_TYPE_UNSPECIFIED = 0;
  GICS_SECTOR_TYPE_CONSUMER_DISCRETIONARY = 1;
  GICS_SECTOR_TYPE_CONSUMER_STAPLES = 2;
  GICS_SECTOR_TYPE_ENERGY = 3;
  GICS_SECTOR_TYPE_FINANCIALS = 4;
  GICS_SECTOR_TYPE_HEALTH_CARE = 5;
  GICS_SECTOR_TYPE_INDUSTRIALS = 6;
  GICS_SECTOR_TYPE_INFORMATION_TECHNOLOGY = 7;
  GICS_SECTOR_TYPE_MATERIALS = 8;
  GICS_SECTOR_TYPE_REAL_ESTATE = 9;
  GICS_SECTOR_TYPE_COMMUNICATION_SERVICES = 10;
  GICS_SECTOR_TYPE_UTILITIES = 11;
}

// GICS industry group types defined at https://www.msci.com/our-solutions/indexes/gics
enum GICSIndustryGroupType {
  GICS_INDUSTRY_GROUP_TYPE_UNSPECIFIED = 0;
  GICS_INDUSTRY_GROUP_TYPE_ENERGY = 1;
  GICS_INDUSTRY_GROUP_TYPE_MATERIALS = 2;
  GICS_INDUSTRY_GROUP_TYPE_CAPITAL_GOODS = 3;
  GICS_INDUSTRY_GROUP_TYPE_COMMERCIAL_AND_PROFESSIONAL_SERVICES = 4;
  GICS_INDUSTRY_GROUP_TYPE_TRANSPORTATION = 5;
  GICS_INDUSTRY_GROUP_TYPE_AUTOMOBILES_AND_COMPONENTS = 6;
  GICS_INDUSTRY_GROUP_TYPE_CONSUMER_DURABLES_AND_APPAREL = 7;
  GICS_INDUSTRY_GROUP_TYPE_CONSUMER_SERVICES = 8;
  GICS_INDUSTRY_GROUP_TYPE_CONSUMER_DISCRETIONARY_DISTRIBUTION_AND_RETAIL = 9;
  GICS_INDUSTRY_GROUP_TYPE_CONSUMER_STAPLES_DISTRIBUTION_AND_RETAIL = 10;
  GICS_INDUSTRY_GROUP_TYPE_FOOD_BEVERAGE_AND_TOBACCO = 11;
  GICS_INDUSTRY_GROUP_TYPE_HOUSEHOLD_AND_PERSONAL_PRODUCTS = 12;
  GICS_INDUSTRY_GROUP_TYPE_HEALTH_CARE_EQUIPMENT_AND_SERVICES = 13;
  GICS_INDUSTRY_GROUP_TYPE_PHARMACEUTICALS_BIOTECHNOLOGY_AND_LIFE_SCIENCES = 14;
  GICS_INDUSTRY_GROUP_TYPE_BANKS = 15;
  GICS_INDUSTRY_GROUP_TYPE_FINANCIAL_SERVICES = 16;
  GICS_INDUSTRY_GROUP_TYPE_INSURANCE = 17;
  GICS_INDUSTRY_GROUP_TYPE_SOFTWARE_AND_SERVICES = 18;
  GICS_INDUSTRY_GROUP_TYPE_TECHNOLOGY_HARDWARE_AND_EQUIPMENT = 19;
  GICS_INDUSTRY_GROUP_TYPE_SEMICONDUCTORS_AND_SEMICONDUCTOR_EQUIPMENT = 20;
  GICS_INDUSTRY_GROUP_TYPE_TELECOMMUNICATION_SERVICES = 21;
  GICS_INDUSTRY_GROUP_TYPE_MEDIA_AND_ENTERTAINMENT = 22;
  GICS_INDUSTRY_GROUP_TYPE_UTILITIES = 23;
  GICS_INDUSTRY_GROUP_TYPE_EQUITY_REAL_ESTATE_INVESTMENT_TRUSTS = 24;
  GICS_INDUSTRY_GROUP_TYPE_REAL_ESTATE_MANAGEMENT_AND_DEVELOPMENT = 25;
}

// GICS industry types defined at https://www.msci.com/our-solutions/indexes/gics
enum GICSIndustryType {
  GICS_INDUSTRY_TYPE_UNSPECIFIED = 0;
  GICS_INDUSTRY_TYPE_ENERGY_EQUIPMENT_AND_SERVICES = 1;
  GICS_INDUSTRY_TYPE_OIL_GAS_AND_CONSUMABLE_FUELS = 2;
  GICS_INDUSTRY_TYPE_CHEMICALS = 3;
  GICS_INDUSTRY_TYPE_CONSTRUCTION_MATERIALS = 4;
  GICS_INDUSTRY_TYPE_CONTAINERS_AND_PACKAGING = 5;
  GICS_INDUSTRY_TYPE_METALS_AND_MINING = 6;
  GICS_INDUSTRY_TYPE_PAPER_AND_FOREST_PRODUCTS = 7;
  GICS_INDUSTRY_TYPE_AEROSPACE_AND_DEFENSE = 8;
  GICS_INDUSTRY_TYPE_BUILDING_PRODUCTS = 9;
  GICS_INDUSTRY_TYPE_CONSTRUCTION_AND_ENGINEERING = 10;
  GICS_INDUSTRY_TYPE_ELECTRICAL_EQUIPMENT = 11;
  GICS_INDUSTRY_TYPE_INDUSTRIAL_CONGLOMERATES = 12;
  GICS_INDUSTRY_TYPE_MACHINERY = 13;
  GICS_INDUSTRY_TYPE_TRADING_COMPANIES_AND_DISTRIBUTORS = 14;
  GICS_INDUSTRY_TYPE_COMMERCIAL_SERVICES_AND_SUPPLIES = 15;
  GICS_INDUSTRY_TYPE_PROFESSIONAL_SERVICES = 16;
  GICS_INDUSTRY_TYPE_AIR_FREIGHT_AND_LOGISTICS = 17;
  GICS_INDUSTRY_TYPE_PASSENGER_AIRLINES = 18;
  GICS_INDUSTRY_TYPE_MARINE_TRANSPORTATION = 19;
  GICS_INDUSTRY_TYPE_GROUND_TRANSPORTATION = 20;
  GICS_INDUSTRY_TYPE_TRANSPORTATION_INFRASTRUCTURE = 21;
  GICS_INDUSTRY_TYPE_AUTOMOBILE_COMPONENTS = 22;
  GICS_INDUSTRY_TYPE_AUTOMOBILES = 23;
  GICS_INDUSTRY_TYPE_HOUSEHOLD_DURABLES = 24;
  GICS_INDUSTRY_TYPE_LEISURE_PRODUCTS = 25;
  GICS_INDUSTRY_TYPE_TEXTILES_APPAREL_AND_LUXURY_GOODS = 26;
  GICS_INDUSTRY_TYPE_HOTELS_RESTAURANTS_AND_LEISURE = 27;
  GICS_INDUSTRY_TYPE_DIVERSIFIED_CONSUMER_SERVICES = 28;
  GICS_INDUSTRY_TYPE_DISTRIBUTORS = 29;
  GICS_INDUSTRY_TYPE_BROADLINE_RETAIL = 30;
  GICS_INDUSTRY_TYPE_SPECIALTY_RETAIL = 31;
  GICS_INDUSTRY_TYPE_CONSUMER_STAPLES_DISTRIBUTION_AND_RETAIL = 32;
  GICS_INDUSTRY_TYPE_BEVERAGES = 33;
  GICS_INDUSTRY_TYPE_FOOD_PRODUCTS = 34;
  GICS_INDUSTRY_TYPE_TOBACCO = 35;
  GICS_INDUSTRY_TYPE_HOUSEHOLD_PRODUCTS = 36;
  GICS_INDUSTRY_TYPE_PERSONAL_CARE_PRODUCTS = 37;
  GICS_INDUSTRY_TYPE_HEALTH_CARE_EQUIPMENT_AND_SUPPLIES = 38;
  GICS_INDUSTRY_TYPE_HEALTH_CARE_PROVIDERS_AND_SERVICES = 39;
  GICS_INDUSTRY_TYPE_HEALTH_CARE_TECHNOLOGY = 40;
  GICS_INDUSTRY_TYPE_BIOTECHNOLOGY = 41;
  GICS_INDUSTRY_TYPE_PHARMACEUTICALS = 42;
  GICS_INDUSTRY_TYPE_LIFE_SCIENCES_TOOLS_AND_SERVICES = 43;
  GICS_INDUSTRY_TYPE_BANKS = 44;
  GICS_INDUSTRY_TYPE_FINANCIAL_SERVICES = 45;
  GICS_INDUSTRY_TYPE_CONSUMER_FINANCE = 46;
  GICS_INDUSTRY_TYPE_CAPITAL_MARKETS = 47;
  GICS_INDUSTRY_TYPE_MORTGAGE_REAL_ESTATE_INVESTMENT_TRUSTS = 48;
  GICS_INDUSTRY_TYPE_INSURANCE = 49;
  GICS_INDUSTRY_TYPE_IT_SERVICES = 50;
  GICS_INDUSTRY_TYPE_SOFTWARE = 51;
  GICS_INDUSTRY_TYPE_COMMUNICATIONS_EQUIPMENT = 52;
  GICS_INDUSTRY_TYPE_TECHNOLOGY_HARDWARE_STORAGE_AND_PERIPHERALS = 53;
  GICS_INDUSTRY_TYPE_ELECTRONIC_EQUIPMENT_INSTRUMENTS_AND_COMPONENTS = 54;
  GICS_INDUSTRY_TYPE_SEMICONDUCTORS_AND_SEMICONDUCTOR_EQUIPMENT = 55;
  GICS_INDUSTRY_TYPE_DIVERSIFIED_TELECOMMUNICATION_SERVICES = 56;
  GICS_INDUSTRY_TYPE_WIRELESS_TELECOMMUNICATION_SERVICES = 57;
  GICS_INDUSTRY_TYPE_MEDIA = 58;
  GICS_INDUSTRY_TYPE_ENTERTAINMENT = 59;
  GICS_INDUSTRY_TYPE_INTERACTIVE_MEDIA_AND_SERVICES = 60;
  GICS_INDUSTRY_TYPE_ELECTRIC_UTILITIES = 61;
  GICS_INDUSTRY_TYPE_GAS_UTILITIES = 62;
  GICS_INDUSTRY_TYPE_MULTI_UTILITIES = 63;
  GICS_INDUSTRY_TYPE_WATER_UTILITIES = 64;
  GICS_INDUSTRY_TYPE_INDEPENDENT_POWER_AND_RENEWABLE_ELECTRICITY_PRODUCERS = 65;
  GICS_INDUSTRY_TYPE_DIVERSIFIED_REITS = 66;
  GICS_INDUSTRY_TYPE_INDUSTRIAL_REITS = 67;
  GICS_INDUSTRY_TYPE_HOTEL_AND_RESORT_REITS = 68;
  GICS_INDUSTRY_TYPE_OFFICE_REITS = 69;
  GICS_INDUSTRY_TYPE_HEALTH_CARE_REITS = 70;
  GICS_INDUSTRY_TYPE_RESIDENTIAL_REITS = 71;
  GICS_INDUSTRY_TYPE_RETAIL_REITS = 72;
  GICS_INDUSTRY_TYPE_SPECIALIZED_REITS = 73;
  GICS_INDUSTRY_TYPE_REAL_ESTATE_MANAGEMENT_AND_DEVELOPMENT = 74;
}

// ListingStatus represents the current operational status of a security listing.
// This enum is used to indicate whether a listing is currently available for trading or not.
enum ListingStatus {
  // Default value. Indicates that the listing status is not specified.
  LISTING_STATUS_UNSPECIFIED = 0;
  // The listing is currently active and available for trading.
  LISTING_STATUS_ACTIVE = 1;
  // The listing is currently inactive and not available for trading.
  LISTING_STATUS_INACTIVE = 2;
}
