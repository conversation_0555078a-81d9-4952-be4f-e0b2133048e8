//go:generate gen_sql -types=SecurityDetails,StockDetails,FundDetails
syntax = "proto3";

package api.securities.catalog;

import "api/securities/catalog/enums.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/securities/catalog";

message Security {
  string id = 1;
  SecurityType security_type = 2;
  string security_name = 3;
  vendorgateway.Vendor vendor = 4;
  string vendor_security_id = 5;
  string logo_url = 6;
  SecurityDetails security_details = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;
}

message SecurityDetails {
  oneof security_type {
    // StockDetails contains the stock related information and metadata
    StockDetails stock_details = 1;
    // FundDetails contains fund related information and metadata
    FundDetails fund_details = 2;
  }
}

// StockDetails contains the stock related information and metadata
message StockDetails {
  string stock_name = 1;
  string stock_short_name = 2;
  string website_url = 3;
  string region_name = 4;
  string incorporation_country_name = 5;
  GICSSectorType gics_sector_type = 6;
  GICSIndustryGroupType gics_industry_group_type = 7;
  GICSIndustryType gics_industry_type = 8;
  string stock_description = 9;
}

// FundDetails contains fund related information and metadata
message FundDetails {
  string fund_name = 1;
  string fund_name_short = 2;
  string region_name = 3;
  string country_name = 4;
  string benchmark_name = 5;
  string benchmark_name_short = 6;
  Holdings etf_holdings = 7;
  EquitySectorHoldings equity_sector_holdings = 8;
}

// Holdings are the securities which constitute the ETF
message Holdings {
  // Key is the security name and value would be it's weight in the fund
  map<string, float> holdings = 1;
}

message EquitySectorHoldings {
  // key would be string value of GICSSectorType enum and value would be percentage of this sector
  map<string, float> equity_sectors = 1;
}

enum SecurityFieldMask {
  SECURITY_FIELD_MASK_UNSPECIFIED = 0;
  SECURITY_FIELD_MASK_ID = 1;
  SECURITY_FIELD_MASK_SECURITY_TYPE = 3;
  SECURITY_FIELD_MASK_NAME = 4;
  SECURITY_FIELD_MASK_VENDOR = 5;
  SECURITY_FIELD_MASK_VENDOR_SECURITY_ID = 6;
  SECURITY_FIELD_MASK_LOGO_URL = 7;
  SECURITY_FIELD_MASK_DETAILS = 8;
  SECURITY_FIELD_MASK_CREATED_AT = 9;
  SECURITY_FIELD_MASK_UPDATED_AT = 10;
  SECURITY_FIELD_MASK_DELETED_AT = 11;
}
