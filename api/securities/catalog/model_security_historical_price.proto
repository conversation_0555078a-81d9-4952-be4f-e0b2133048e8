syntax = "proto3";

package api.securities.catalog;

import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/securities/catalog";

// HistoricalPrice represents the price record of a security listing as per a specific date.
// It is used to store historical close prices, vendor information, and relevant timestamps.
message HistoricalPrice {
  // Unique identifier for this historical price record
  string id = 1;
  // Foreign key referencing the SecurityListing this price belongs to
  string security_listing_id = 2;
  // The date for which this price is recorded
  google.type.Date price_date = 3;
  // The closing price for the security on the given date
  google.type.Money close_price = 4;
  // Vendor information (e.g., data provider or source)
  vendorgateway.Vendor vendor = 5;
  // The date when the price was derived or calculated.
  // - For a non-trading day like weekends or holidays (e.g., Saturday 10/05/25), this will be the previous trading day (e.g., Friday 09/05/25)
  // - For a trading day (e.g., Thursday 08/05/25), this will be the same day (08/05/25)
  google.type.Date price_derived_date = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  google.protobuf.Timestamp deleted_at = 9;
}

enum HistoricalPriceFieldMask {
  HISTORICAL_PRICE_FIELD_MASK_UNSPECIFIED = 0;
  HISTORICAL_PRICE_FIELD_MASK_ID = 1;
  HISTORICAL_PRICE_FIELD_MASK_SECURITY_LISTING_ID = 2;
  HISTORICAL_PRICE_FIELD_MASK_PRICE_DATE = 3;
  HISTORICAL_PRICE_FIELD_MASK_CLOSE_PRICE = 4;
  HISTORICAL_PRICE_FIELD_MASK_VENDOR = 5;
  HISTORICAL_PRICE_FIELD_MASK_PRICE_DERIVED_DATE = 6;
  HISTORICAL_PRICE_FIELD_MASK_CREATED_AT = 7;
  HISTORICAL_PRICE_FIELD_MASK_UPDATED_AT = 8;
  HISTORICAL_PRICE_FIELD_MASK_DELETED_AT = 9;
}
