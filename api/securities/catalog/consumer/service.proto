syntax = "proto3";

package api.securities.catalog;

import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/securities/catalog/consumer";

service SecuritiesCatalogConsumer {
  // This RPC method triggers the update of securities data by fetching fresh data from an external vendors such as
  // Bridgewise or MorningStar. Once the data is fetched, it updates the corresponding entities 'security' and
  // 'security_listings' to ensure that all information related to the securities is current and accurate.
  rpc RefreshSecurityDetails (RefreshSecurityDetailsRequest) returns (RefreshSecurityDetailsResponse);

  // This RPC method fetches all securities from external vendors in batches and adds
  // any missing securities to the catalog database.
  rpc AddNewSecurities (AddNewSecuritiesRequest) returns (AddNewSecuritiesResponse);

  // This RPC method fetches the daily historical prices for a security listing in a given date range from the vendor
  // and derives prices for holidays and stores the price data in historical_price table
  rpc ProcessSecurityListingHistoricalPrices (ProcessSecurityListingHistoricalPricesRequest) returns (ProcessSecurityListingHistoricalPricesResponse);
}

message RefreshSecurityDetailsRequest {
  queue.ConsumerRequestHeader request_header = 1;
  string security_id = 2;
  string company_id = 3;
  string trading_item_id = 4;
}

message RefreshSecurityDetailsResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message AddNewSecuritiesRequest {
  queue.ConsumerRequestHeader request_header = 1;
  int32 page_num = 2;
}

message AddNewSecuritiesResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessSecurityListingHistoricalPricesRequest {
  queue.ConsumerRequestHeader request_header = 1;
  string security_listing_id = 2;
  google.protobuf.Timestamp start_date = 3 [(validate.rules).timestamp.required = true];
  google.protobuf.Timestamp end_date = 4 [(validate.rules).timestamp.required = true];
  string company_id = 5;
  string trading_item_id = 6;
}

message ProcessSecurityListingHistoricalPricesResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
