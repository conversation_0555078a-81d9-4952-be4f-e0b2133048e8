syntax = "proto3";

package api.collection;

import "google/type/date.proto";
import "google/protobuf/timestamp.proto";

import "api/collection/common.proto";

option go_package = "github.com/epifi/gamma/api/collection";
option java_package = "com.github.epifi.gamma.api.collection";

message Allocation {
  // unique identifier of the allocation of a lead in our system
  string id = 1;
  // the lead to which this allocation belongs to
  string lead_id = 2;
  // first date of the allocation to the vendor
  google.type.Date date_of_allocation = 3;
  google.type.Date default_date = 4;
  // represents whether the allocation is created at vendor or not
  VendorStatus vendor_status = 5;
  // represents whether the allocation is recovered or not
  RecoveryStatus recovery_status = 6;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  google.protobuf.Timestamp deleted_at = 11;
}

// AllocationFieldMask is an enum that represents the fields of the Allocation entity
// An example use-case is using this enum as a update/select field mask for update RPCs or DAO methods
enum AllocationFieldMask {
  ALLOCATION_FIELD_MASK_UNSPECIFIED = 0;
  ALLOCATION_FIELD_MASK_ID = 1;
  ALLOCATION_FIELD_MASK_LEAD_ID = 2;
  ALLOCATION_FIELD_MASK_DATE_OF_ALLOCATION = 3;
  ALLOCATION_FIELD_MASK_DEFAULT_DATE = 4;
  ALLOCATION_FIELD_MASK_VENDOR_STATUS = 5;
  ALLOCATION_FIELD_MASK_RECOVERY_STATUS = 6;
  ALLOCATION_FIELD_MASK_CREATED_AT = 9;
  ALLOCATION_FIELD_MASK_UPDATED_AT = 10;
  ALLOCATION_FIELD_MASK_DELETED_AT = 11;
}
