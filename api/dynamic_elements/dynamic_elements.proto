syntax = "proto3";

package dynamic_elements;

import "api/dynamic_elements/enums.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/service_name.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/components/linear_progress_bar.proto";
import "api/typesv2/ui/widget_themes.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/dynamic_elements";
option java_package = "com.github.epifi.gamma.api.dynamic_elements";


// Fetch request to be used for integration with individual BE services
message FetchDynamicElementsRequest {
  string actor_id = 1;

  ClientContext client_context = 2;
}

// Fetch response to be used for integration with individual services
message FetchDynamicElementsResponse {
  rpc.Status status = 1;

  repeated DynamicElement elements_list = 2;
}

// Callback request to be used for integration with individual services
message DynamicElementCallbackRequest {
  string actor_id = 1;

  string element_id = 2;

  CallbackPayload callback_payload = 3;
}

// Callback response to be used for integration with individual services
message DynamicElementCallbackResponse {
  rpc.Status status = 1;
}

// Contains the details of the client app required to fetch dynamic elements
// Apart from screen_name other contexts can be used by the BE services
// eg: Insights service may use location, time, etc to give relevant insights
message ClientContext {
  frontend.deeplink.Screen screen_name = 1;
  // additional info required for dynamically rendered screens
  // eg: help category screens
  oneof screen_additional_info {
    FAQCategoryScreenAdditionalInfo faq_category = 2;
    // For backward compatibility the default is promotions section(SECTION_BODY) of the screen
    // i.e. if no additional info is passed it is considered as promotions section of the screen
    HomeScreenAdditionalInfo home_info = 3;
    // Additional info for fetching different sections on Invest landing screen powered by dynamic elements
    InvestmentLandingScreenAdditionalInfo invest_landing_info = 4;
    // Additional info for analyser screen.
    AnalyserScreenAdditionalInfo analyser_screen_info = 5;
    // Additional info for DC dashboard
    DcDashboardScreenAdditionalInfo dc_dashboard_screen_additional_info = 6;
    // Additional info for post payment screen
    PostPaymentScreenAdditionalInfo post_payment_screen_additional_info = 7;
    // Additional info for AMB details screen
    AMBDetailsScreenAdditionalInfo amb_details_screen_additional_info = 8;

  }
  // app platform of the client eg: Android/Ios
  api.typesv2.common.Platform app_platform = 14;
  // app version of the client
  int32 app_version = 15;
}

message DcDashboardScreenAdditionalInfo {
  enum Section {
    SECTION_UNSPECIFIED = 0;
    // Section for showing widget in offers & promotions section
    SECTION_DC_DASHBOARD_PROMOTIONAL_WIDGET = 1;
    // Section for large banner
    SECTION_DC_DASHBOARD_LARGE_BANNER = 2;
    // Section for GTM POPUP
    SECTION_DC_DASHBOARD_GTM_POPUP = 3;
  }
  Section section = 1;
}

message PostPaymentScreenAdditionalInfo {
  enum Section {
    SECTION_UNSPECIFIED = 0;
    // Section for showing promotional banner
    SECTION_POST_PAYMENT_PROMOTIONAL_BANNER = 1;
  }
  Section section = 1;
}

message AMBDetailsScreenAdditionalInfo {
  enum Section {
    SECTION_UNSPECIFIED = 0;
    // Section for showing promotional banner
    SECTION_AMB_PROMOTIONAL_BANNER = 1;
  }
  Section section = 1;
}

message AnalyserScreenAdditionalInfo {
  string analyser_name = 1;
}

// additional info required to identify FAQ category screens
message FAQCategoryScreenAdditionalInfo {
  int64 category_id = 1;
  string category_name = 2;
}

// additional info required to identify the section on home screen
message HomeScreenAdditionalInfo {
  enum Section {
    SECTION_UNSPECIFIED = 0;
    // to identify the critical banner section on the top bar
    SECTION_TOP_BAR = 1;
    // to identify the banner section in the body of Home screen
    SECTION_BODY = 2;
    // to identify lending related promotional banner section in the body of Home screen
    SECTION_BODY2 = 3;
    // To identify whether the request is made from Home V2 screen for GTM Popup
    SECTION_GTM_POPUP = 4;
    // to identify the primary feature section in middle widgets section of home v2
    SECTION_FEATURE_PRIMARY = 5;
    // to identify the secondary feature section in middle widgets section of home v2
    // this might be a list of feature widgets coming from different services
    SECTION_FEATURE_SECONDARY = 6;
    // Display content within a card.
    // Optionally include tabs for switching between different tabs content views if available.
    // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
    SECTION_TABBED_CARD = 7;
  }
  enum Version {
    VERSION_UNSPECIFIED = 0;
    // to identify v2 version of home screen
    VERSION_V2 = 1;
  }
  Section section = 1;
  Version version = 2;
}

// additional info required to identify the section on invest landing screen
message InvestmentLandingScreenAdditionalInfo {
  enum Section {
    SECTION_UNSPECIFIED = 0;
    // Section for showing banner in Investment landing
    SECTION_INVEST_LANDING_BANNER = 1;
    // Section for GTM POPUP in Investment landing
    SECTION_INVEST_GTM_POPUP = 2;
  }
  Section section = 1;
}

message DynamicElement {
  // BE service owning this dynamic element
  api.typesv2.ServiceName owner_service = 1;
  // id of this dynamic element within the owner service
  string id = 2;
  // A dynamic element can be used for alert, marketing, insights etc.
  // Not used by the client as of now.
  ElementUtilityType utility_type = 3;
  // A dynamic element can be a banner, bottom sheet, pop up etc.
  ElementStructureType structure_type = 4;
  // its parameters are dependent on ElementStructureType
  ElementContent content = 5;
  // map of meta data required for biz analytics
  map<string, string> biz_analytics_data = 6;
  // Endtime of the element
  google.protobuf.Timestamp end_time = 7;
}

// Contains the params required to render a dynamic element.
// the content of a targeted comms element is dependent on its structure type
// e.g., banner, bottom sheets etc.
message ElementContent {
  oneof content {
    BannerElementContent banner = 1;
    BottomSheetElementContent bottom_sheet = 2;
    PopUpElementContent pop_up = 3;
    BannerElementContentV2 banner_v2 = 4;
    ScrollableBannerElementContent scrollable_banner = 5;
    GTMPopUpBanner gtm_pop_up_banner = 6;
    FeatureWidgetWithFourPoints feature_widget_with_four_points = 7;
    FeatureWidgetWithThreePoints feature_widget_with_three_points = 8;
    FeatureWidgetWithTwoPoints feature_widget_with_two_points = 9;
    TabbedCard tabbed_card = 10;
    RedirectElementContent redirect_element = 11;
    ProgressBarCardContent progress_bar_element = 12;
    BannerElementContentV3 banner_v3 = 13;
  }
}

// redirects user to a deeplink
message RedirectElementContent {
  // redirect to deeplink
  frontend.deeplink.Deeplink deeplink = 1;
}

// Display content within a card.
// Optionally include tabs for switching between different tabs content views if available.
// https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
message TabbedCard {
  // Optional Title for the whole Tabbed Card View
  api.typesv2.ui.IconTextComponent title = 1;
  // Tabs for the Widget tapping on which we show the respective card for the tab
  repeated Tab tabs = 2;
  // Background color for tab when selected e.g white
  api.typesv2.ui.BackgroundColour selected_tab_bg_color = 3;
  // Background color for tab when selected e.g gray
  api.typesv2.ui.BackgroundColour unselected_tab_bg_color = 4;

  message Tab {
    // Optional value of tab
    // If we want to show only one tab don't send this so we just show the card
    // If more than one tabs always send this tab
    api.typesv2.ui.IconTextComponent tab = 1;
    // Card content for particular tab
    Card card = 2;
  }
  message Card {
    // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-17385&mode=design&t=Q1lU7tXjuFhkQ8R7-0
    CollectiveInfoView collective_info = 1;
    // Description for the card content
    // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
    api.typesv2.ui.IconTextComponent description = 2;
    // Number of chips binding it to be max as per the requirement
    repeated Chip chips = 3 [(validate.rules).repeated.max_items = 4];
    // Footer with deeplink to land on respective screen
    // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-17385&mode=design&t=Q1lU7tXjuFhkQ8R7-0
    api.typesv2.ui.IconTextComponent footer = 4;
    // Background Color of the card i.e white in designs
    api.typesv2.ui.BackgroundColour bg_color = 5;
    // Corner Radius of the card
    int32 corner_radius = 6;

    message CollectiveInfoView {
      // Background colour for https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
      api.typesv2.ui.BackgroundColour bg_colour = 1;
      // Array of collective informations like Portfolio, Today etc in figma
      repeated CollectiveInfo collective_infos = 2;
      // Corner Radius of the card
      int32 corner_radius = 3;
    }

    message CollectiveInfo {
      // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
      // primary value e.g Portfolio $125.3 or Today
      api.typesv2.ui.IconTextComponent primary = 1;
      // secondary value e.g ^ 4.5% shows the change in the overall values
      api.typesv2.ui.IconTextComponent secondary = 2;
    }

    // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-17334&mode=design&t=Q1lU7tXjuFhkQ8R7-0
    message Chip {
      // Image of the Chip e.g Amazon image
      api.typesv2.common.VisualElement image = 1;
      // Title of the Chip e.g Name of Stocks
      api.typesv2.common.Text title = 2;
      // This is either the change in value for stocks or mutual funds else just a simple itc for other views
      api.typesv2.ui.IconTextComponent subtitle = 3;
      // Boolean that shows if some chip change in values has to be tracked realtime
      // This will make the subtitle to track realtime or not
      bool should_track_realtime = 4;
      // Stock Id for which to track real time
      // Will be used only when real time tracking is enabled
      string stock_id = 5;
      // deeplink during chips click action
      frontend.deeplink.Deeplink deeplink = 6;
    }
  }
}

// Contains data/params required to render a Banner element
message BannerElementContent {
  // title of the banner
  string title = 1;
  // body of the banner with detailed description
  string body = 2;
  // url for the icon to be shown on the banner
  // deprecated in favour of visual_element
  string icon_url = 3 [deprecated = true];
  // hex encoded background color for the banner.
  string background_color = 4;
  // list of CTAs to be rendered on the banner
  repeated DynamicElementCta cta_list = 5;
  // Deeplink for click on banner
  frontend.deeplink.Deeplink deeplink = 6;
  // text color for title
  string title_text_color = 7;
  // text color for body
  string body_text_color = 8;
  api.typesv2.common.VisualElement visual_element = 9;
}

// Contains data/params required to render a Banner element on home v2
message BannerElementContentV2 {
  // title of the banner
  api.typesv2.common.Text title = 1;
  // image to be shown on the banner
  // deprecated in favour of visual_element
  api.typesv2.common.Image image = 2 [deprecated = true];
  // background color for the banner.
  api.typesv2.ui.BackgroundColour background_color = 3;
  // list of CTAs to be rendered on the banner
  repeated DynamicElementCta cta_list = 4;
  // Deeplink for click on banner
  frontend.deeplink.Deeplink deeplink = 5;
  // shadows to be shown on the banner
  repeated api.typesv2.ui.Shadow shadows = 6;
  // body of the banner with detailed description
  // Optional field
  api.typesv2.common.Text body = 7;
  // Parameters to decide whether to show time counter or not
  TimeCounterParams time_counter_params = 8;
  api.typesv2.common.VisualElement visual_element = 9;
  // The Visual to render on the Full Banner space. If this is set, Banner will only display the Full visual and no other
  // elements will be visible, apart from timer and the Indicator points when applicable. Ref:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-27875&mode=dev
  api.typesv2.common.VisualElement visual_element_full_banner = 10;
  // The background color of the selected indicator view/dot, when this banner element is visible. Ref:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-24373&mode=dev
  api.typesv2.ui.BackgroundColour indicator_selected_color = 11;
  // The background color of the default views/dots, when this banner element is visible. Ref:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-24373&mode=dev
  api.typesv2.ui.BackgroundColour indicator_default_color = 12;
  // [Optional] Ui variant type used for rendering [BannerElementContentV2]
  // Clients should render the entire list of Promo banners in either V2 or existing Ui, depending on the
  // 1st element's banner_element_content_v2_ui_variant
  BannerElementContentV2UiVariant banner_element_content_v2_ui_variant = 13;
  // Enum for specifying the various types of UI variants to render for this Banner
  enum BannerElementContentV2UiVariant {
    // Default handling of the UI for Home Promo banner v2. Ref:
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21086-48823&mode=dev
    BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_UNSPECIFIED = 0;
    // V2 UI handling for the Home Promo banner v2. Ref:
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=21519-24366&mode=dev
    BANNER_ELEMENT_CONTENT_V2_UI_VARIANT_V2 = 1;
  }
}

message TimeCounterParams {
  // Whether to show time counter in banner or not
  bool show_time_counter = 1;
  // Text parameters to be used for time counter text
  api.typesv2.common.Text text_params = 2;
  // BG colour to be used in time counter section
  string bg_colour = 3;
}

// Contains data/params required to render a Bottom Sheet element
// TODO: [V2] Bottom sheet element is not fully scoped out. Not used as of now.
message BottomSheetElementContent {
  // title of the bottom sheet
  string title = 1;
  // body of the bottom sheet with detailed description
  string body = 2;
  // url for the icon to be shown on the bottom sheet
  string icon_url = 3;
  // list of CTAs to be rendered on the bottom sheet
  repeated DynamicElementCta cta_list = 4;
}

// Contains data/params required to render a Pop Up element
message PopUpElementContent {
  message AdditionalTextSection {
    // Background color of the box
    api.typesv2.ui.BackgroundColour background_color = 1;
    // A list of text values to be displayed vertically
    repeated api.typesv2.common.Text texts = 2;
  }
  // title of the pop up
  string title = 1;
  // body of the bottom sheet with detailed description
  string body = 2;
  // url for the icon to be shown on the pop up
  string icon_url = 3;
  // hex encoded background color for the banner.
  string background_color = 4;
  // list of CTAs to be rendered on the pop up
  repeated DynamicElementCta cta_list = 5;
  // A section with extra text content. Example, starts/ends text seen here:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=2017%3A46103&t=YI8R7iqkhCKhLutT-0
  AdditionalTextSection additional_text_section = 6;
}

message DynamicElementCta {
  enum Type {
    // No action can be taken on the event.
    TYPE_UNSPECIFIED = 0;

    // user will be shown a popup element on clicking
    TYPE_SHOW_POP_UP = 1;

    // lets the user to get notified later
    TYPE_NOTIFY_ME = 2;

    // on this Cta, User will not be shown this element later
    TYPE_DISMISSED = 3;
  }
  // Type of CTA
  Type type = 1;
  string text = 2;
  // hex encoded background color for the cta.
  string background_color = 3;
  // options required for different types of CTAs
  oneof options {
    // for TYPE_SHOW_POP_UP
    PopUpElementContent pop_up_options = 4;
  }
  // Deeplink for click on CTA
  frontend.deeplink.Deeplink deeplink = 5;
  // image url for the cta
  string cta_image_url = 6;
  // hex encoded color for the cta text.
  string text_color = 7;
}

// The callback parameters depending upon the owner service
// TODO: [V2] to define the service specific payloads
message CallbackPayload {
  oneof payload {
    InAppTargetedCommsCallbackPayload in_app_targeted_comms = 1;
    InsightsCallbackPayload insights = 2;
    VKYCNudgeCallbackPayload vkyc_nudge = 3;
  }
}

message InAppTargetedCommsCallbackPayload {
  bool is_dismissed = 1;
}

message InsightsCallbackPayload {
}

// update the last callback received
message VKYCNudgeCallbackPayload {
  google.protobuf.Timestamp last_callback_time = 1;
}

// A third type of banner that shows scrolling content on the right
message ScrollableBannerElementContent {
  // Header will be static in the banner
  BannerHeader header = 1;
  // Scrollable part of the banner
  repeated BannerSingleShapeElement scrolling_elements = 2;
  // bg colour of the full widget
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 3;
}

message BannerHeader {
  // Title to be shown in the header
  repeated api.typesv2.common.Text title = 1;
  // To redirect the user on appropriate landing screen for banner
  api.typesv2.ui.IconTextComponent cta = 2;
}

message BannerSingleShapeElement {
  // Refer this link for possible shapes
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0-to-2.1?node-id=12197%3A101109&t=KcJwVz1S5DrrCFqO-1
  enum Shape {
    SHAPE_UNSPECIFIED = 0;
    // Oval shape
    SHAPE_STAMP_1 = 1;
    // Milestone: Circular top section, rounded square bottom section
    SHAPE_STAMP_2 = 2;
    // Pentagon: Rounded triangular top section, rounded square bottom section
    SHAPE_STAMP_3 = 3;
    // Square: Rounded square shape
    SHAPE_STAMP_4 = 4;
  }
  Shape shape = 1;
  // Image to be shown inside each shaped element
  api.typesv2.common.Image image = 2;
  // Title of shaped element
  api.typesv2.common.Text title = 3;
  // Bg colour of each shape element
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 4;
  // Shadow of shape element
  repeated api.typesv2.common.ui.widget.Shadow shadow = 5;
  // Deeplink for click on banner
  frontend.deeplink.Deeplink deeplink = 6;
}

// GTMPopUpBanner defines the parameters required to show GTM pop up banner on home load
// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%9B%A0%EF%B8%8F-Home-Workfile-2?type=design&node-id=995-51039&t=Ff1iXdLHD498gai1-0
message GTMPopUpBanner {
  message BodyLayoutParagraph {
    api.typesv2.common.Text title = 1;
    api.typesv2.common.Text body_content = 2;
    // Image/lotte to be shown on pop up
    api.typesv2.common.VisualElement pop_up_visual_element = 3;
  }
  message BodyLayoutBulletPoints {
    api.typesv2.common.Text title = 1;
    message SingleBulletPoint {
      api.typesv2.common.Image image = 1;
      api.typesv2.common.Text text = 2;
    }
    repeated SingleBulletPoint bullet_points = 2;
    // Image/lotte to be shown on pop up
    api.typesv2.common.VisualElement pop_up_visual_element = 3;
  }
  message BodyLayoutFullLottie {
    api.typesv2.common.VisualElement pop_up_visual_element = 1;
  }
  // Body can have multiple layouts
  oneof Body {
    BodyLayoutParagraph body_layout_paragraph = 1;
    BodyLayoutBulletPoints body_layout_bullet_points = 2;
    BodyLayoutFullLottie body_layout_full_lottie = 3;
  }
  // Background colour of the pop up container
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 4;
  // List of ctas to be shown
  repeated DynamicElementCta ctas = 5;
  // Deeplink for click on the banner
  frontend.deeplink.Deeplink deeplink = 6;
  // background lotte
  api.typesv2.common.VisualElement bg_visual_element = 7;
  // time duration after which the lotte is to be loaded
  google.protobuf.Duration start_pop_up_after = 8;
  // parameter to decide whether to dismiss on clicking outside the banner
  bool dismiss_on_click_outside_pop_up = 9;
}

// FeatureWidgetWithFourPoints defines the parameters required to feature widget on home which highlights 4 points
// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29100&mode=design&t=BS8f7qemLTGYtAp6-1
message FeatureWidgetWithFourPoints {
  // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29100&mode=design&t=mkvx9Q4hMXzHNgwQ-1
  message TextVisualElementCard {
    message TopSection {
      // image spanning the entire top section
      api.typesv2.common.VisualElement visual_element = 1;
    }
    TopSection top_section = 1;

    message MiddleSection {
      // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29100&mode=design&t=TNslpsfJ95lnpl7E-1
      message HighlightedPoint {
        api.typesv2.common.VisualElement left_icon = 1;
        api.typesv2.common.Text pre_text = 2;
        api.typesv2.common.Text text = 3;
      }
      repeated HighlightedPoint highlighted_points = 1;
    }
    MiddleSection middle_section = 2;

    // cta below the middle section
    api.typesv2.ui.IconTextComponent cta = 3;
    api.typesv2.ui.BackgroundColour bg_colour = 4;
    api.typesv2.ui.Shadow shadow = 5;
  }

  // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7120%3A35948&mode=design&t=HYWSznO6nH0xMGNX-1
  message FullVisualElementCard {
    // visual element to be shown on the entire card
    api.typesv2.common.VisualElement visual_element = 1;
    // cta on top of the visual element
    api.typesv2.ui.IconTextComponent cta = 2;
    api.typesv2.ui.BackgroundColour bg_colour = 5;
    api.typesv2.ui.Shadow shadow = 6;
  }

  oneof Card {
    TextVisualElementCard text_visual_element_card = 1;
    FullVisualElementCard full_visual_element_card = 2;
  }

  // boolean indicator for specifying whether this card should only be used for carousel
  bool is_carousel_variant = 3;

  // title to be shown on top of the dynamic element
  api.typesv2.common.Text title = 4;
  // border color for the card
  api.typesv2.common.ui.widget.BackgroundColour border_color = 5;
}

// FeatureWidgetWithThreePoints defines the parameters required to feature widget on home which highlights 3 points
// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29002&mode=design&t=BS8f7qemLTGYtAp6-1
message FeatureWidgetWithThreePoints {
  // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29002&mode=design&t=mkvx9Q4hMXzHNgwQ-1
  message LeftVerticalFlyer {
    // visual element occupying entire flyer
    api.typesv2.common.VisualElement visual_element = 1;
    // cta on top of the visual element
    api.typesv2.ui.IconTextComponent cta = 2;
    api.typesv2.ui.BackgroundColour bg_colour = 3;
    api.typesv2.ui.Shadow shadow = 4;
  }
  LeftVerticalFlyer left_vertical_flyer = 1;

  // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29002&mode=design&t=mkvx9Q4hMXzHNgwQ-1
  message RightHorizontalFlyer {
    api.typesv2.common.Text pre_text = 1;
    api.typesv2.common.Text text = 2;
    api.typesv2.common.VisualElement right_icon = 3;
    frontend.deeplink.Deeplink deeplink = 4;
    api.typesv2.ui.BackgroundColour bg_colour = 5;
    api.typesv2.ui.Shadow shadow = 6;
  }
  repeated RightHorizontalFlyer right_horizontal_flyers = 2;

  // title to be shown on top of the dynamic element
  api.typesv2.common.Text title = 3;
  // border color for the card
  api.typesv2.common.ui.widget.BackgroundColour border_color = 4;
}

// FeatureWidgetWithTwoPoints defines the parameters required to feature widget on home which highlights 2 points
// https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29103&mode=design&t=BS8f7qemLTGYtAp6-1
message FeatureWidgetWithTwoPoints {
  // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29103&mode=design&t=mkvx9Q4hMXzHNgwQ-1
  message TopHorizontalFlyer {
    // pre heading can be nil
    api.typesv2.common.Text pre_heading = 1;
    // can either be text or image
    oneof heading {
      api.typesv2.common.Text text = 2;
      api.typesv2.common.VisualElement image = 3;
    }
    // image next to the heading
    api.typesv2.common.VisualElement visual_element = 4;
    // cta below the heading
    api.typesv2.ui.IconTextComponent cta = 5;
    api.typesv2.ui.BackgroundColour bg_colour = 6;
    api.typesv2.ui.Shadow shadow = 7;
  }
  TopHorizontalFlyer top_horizontal_flyer = 1;

  // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%8F%A0-Home-Workfile-2?type=design&node-id=7272%3A29103&mode=design&t=mkvx9Q4hMXzHNgwQ-1
  message BottomHorizontalFlyer {
    api.typesv2.common.Text pre_text = 1;
    api.typesv2.common.Text text = 2;
    api.typesv2.common.VisualElement right_icon = 3;
    frontend.deeplink.Deeplink deeplink = 4;
    api.typesv2.ui.BackgroundColour bg_colour = 5;
    api.typesv2.ui.Shadow shadow = 6;
  }
  repeated BottomHorizontalFlyer bottom_horizontal_flyers = 2;

  // title to be shown on top of the dynamic element
  api.typesv2.common.Text title = 3;
  // border color for the card
  api.typesv2.common.ui.widget.BackgroundColour border_color = 4;
}


// https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=34292-15842&t=rU7IEj5cPd2bq243-1
// For displaying progress-based features. e.g., To show user's tier benefits, Fi coins earned etc.
message ProgressBarCardContent {
  // title to be shown above the progress bar. e.g., 3% back on all transactions
  api.typesv2.common.Text title = 1;
  // optional, visual element to be shown on the left side of the banner
  api.typesv2.common.VisualElement left_visual_element = 2;
  // progress bar component
  api.typesv2.ui.sdui.components.LinearProgressBar progress_bar = 3;
  // visual element to be shown on the right side of the banner. e.g., money plant image
  api.typesv2.common.VisualElement right_visual_element = 4;

  // https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=34292-15842&t=bOkq4gEXz4iUysyg-1
  message SubtitleContent {
    // Subtitle can contain a mix of images and text, e.g., `<Fi coin icon> 3000 / <Fi coin icon> 5000`.
    // If a text element within the subtitle requires a gradient color, backend should send a single Text in the `subtitle` field,
    // and the `text_colour` that client should apply to the Text.
    api.typesv2.ui.IconTextComponent subtitle = 1;
    // optional, should be sent only if a gradient color needs to be applied to the subtitle text.
    api.typesv2.common.ui.widget.BackgroundColour text_colour = 2;
  }
  // content to be shown below the progress bar. e.g., `<Fi coin icon> 3000 / <Fi coin icon> 5000`
  repeated SubtitleContent subtitle_content = 5;
  // optional, background colour of the entire card
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 6;
}

// https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=33339-48137&t=fuPV9532WdSfWeRF-1
// Although many of these fields are also present in BannerElementContentV2, client has logic to display only certain elements in that case.
// For example, client only shows the title, body or CTA even if BE sends all of them. Hence, defined this separate message.
message BannerElementContentV3 {
  // title of the banner. e.g., `PAYING A BUSINESS?`
  api.typesv2.common.Text title = 1;
  // e.g., `Get 3% back as Fi-Coins on this payment`, `Upgrade to Prime now` cta with a deeplink
  api.typesv2.ui.IconTextComponent body = 2;
  // To be rendered on the left side of the banner
  api.typesv2.common.VisualElement left_visual_element = 3;
  // To be rendered on the right side of the banner
  api.typesv2.common.VisualElement right_visual_element = 4;
  // background colour of the banner
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 5;
  // Deeplink to redirect the user on click of the banner
  frontend.deeplink.Deeplink deeplink = 6;
  // Border color for the banners
  // For Example - pay landing screen banner, supporting both solid and gradient styles.
  api.typesv2.common.ui.widget.BackgroundColour borderColour = 7;
}
