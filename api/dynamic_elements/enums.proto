syntax = "proto3";

package dynamic_elements;

option go_package = "github.com/epifi/gamma/api/dynamic_elements";
option java_package = "com.github.epifi.gamma.api.dynamic_elements";

// The utility type of a dynamic element.
// A dynamic element can be used for Alerts, Marketing, insights etc.
enum ElementUtilityType {
  ELEMENT_UTILITY_TYPE_UNSPECIFIED = 0;
  // used for alerts e.g: maintenance alerts
  ELEMENT_UTILITY_TYPE_ALERT = 1;
  // used for marketing
  ELEMENT_UTILITY_TYPE_MARKETING = 2;
  // used for insights
  ELEMENT_UTILITY_TYPE_INSIGHT = 3;
  // default utility type
  ELEMENT_UTILITY_TYPE_DEFAULT = 4;
  // used for collecting consent from user
  ELEMENT_UTILITY_TYPE_CONSENT_COLLECTION = 5;
}

// The structure type of a dynamic element
// can be a banner, bottom sheet, pop up etc.
enum ElementStructureType {
  ELEMENT_STRUCTURE_TYPE_UNSPECIFIED = 0;
  // used for Banner element
  ELEMENT_STRUCTURE_TYPE_BANNER = 1;
  // used for bottom sheet element
  ELEMENT_STRUCTURE_TYPE_BOTTOM_SHEET = 2;
  // used for pop up element
  ELEMENT_STRUCTURE_TYPE_POP_UP = 3;
  // used for Banner element v2
  ELEMENT_STRUCTURE_TYPE_BANNER_V2 = 4;
  // used for scrollable banner element
  ELEMENT_STRUCTURE_TYPE_BANNER_SCROLLABLE = 5;
  // used for gtm pop ups
  ELEMENT_STRUCTURE_TYPE_GTM_POP_UP = 6;
  // used for feature widgets highlighting 4 points
  ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_FOUR_POINTS = 7;
  // used for feature widgets highlighting 3 points
  ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_THREE_POINTS = 8;
  // used for feature widgets highlighting 2 points
  ELEMENT_STRUCTURE_TYPE_FEATURE_WIDGET_TWO_POINTS = 9;
  // Display content within a card.
  // Optionally include tabs for switching between different tabs content views if available.
  // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
  ELEMENT_STRUCTURE_TYPE_FEATURE_TABBED_CARD = 10;
  ELEMENT_STRUCTURE_TYPE_REDIRECT_ELEMENT = 11;
  // used for BannerElementContentV3
  ELEMENT_STRUCTURE_TYPE_BANNER_V3 = 12;
  // used for ProgressBarCardContent
  ELEMENT_STRUCTURE_TYPE_PROGRESS_BAR_CARD = 13;
}

enum BizAnalyticsDataKey {
  BIZ_ANALYTICS_DATA_KEY_UNSPECIFIED = 0;
  // to specify the area surfaced in dynamic element
  BIZ_ANALYTICS_DATA_KEY_AREA = 1;
  // to specify current tier of user
  BIZ_ANALYTICS_DATA_KEY_CURRENT_USER = 2;
  // to specify pitched tier
  BIZ_ANALYTICS_DATA_KEY_PITCHED_TIER = 3;
  // to specify the balance bucket of user
  BIZ_ANALYTICS_DATA_KEY_BALANCE_BUCKET = 4;
}
