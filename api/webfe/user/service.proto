syntax = "proto3";

package webfe.user;

import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/common/name.proto";
import "api/webfe/common/flow_name.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/webfe/user";
option java_package = "com.github.epifi.gamma.api.webfe.user";

service User {
  // CollectUserDetails gathers basic and employment information from the user for loan eligibility
  rpc CollectUserDetails (CollectUserDetailsRequest) returns (CollectUserDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message CollectUserDetailsRequest {

  message BasicUserDetails {
    // Name of the user as per PAN card
    api.typesv2.common.Name name = 1;
    // Date of birth of the user in the format "YYYY-MM-DD"
    google.type.Date dob = 2;
    // PAN number of the user
    string pan = 3;
  }

  frontend.header.RequestHeader req = 1;

  // Either basic user details or employment details must be provided
  oneof details {
    BasicUserDetails basic_user_details = 2;
  }

  // Indicates the originating screen from which this RPC was invoked, for tracking purposes
  frontend.deeplink.Screen deeplink_screen = 3;
  // The name of the flow from which this RPC was invoked, for tracking purposes
  webfe.common.FlowName flow_name = 4;
}

message CollectUserDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Deeplink to navigate to the screen corresponding to the next step after collecting user details
  frontend.deeplink.Deeplink deeplink = 2;
}
