syntax = "proto3";

package webfe.travel;

option go_package = "github.com/epifi/gamma/api/webfe/travel";
option java_package = "com.github.epifi.gamma.api.webfe.travel";

// Enum for travel styles
enum TravelStyle {
  TRAVEL_STYLE_UNSPECIFIED = 0;
  // economy flight tickets, quality mid-range accommodation, practical transportation options, and a variety of culinary experiences.
  ECONOMIC = 1;
  // premium flight tickets, luxurious 5-star accommodations, exclusive transportation, and gourmet culinary encounters.
  LUXURY = 2;
  // economy flight tickets, basic shared accommodations, public transit options, and authentic local dining experiences.
  BACKPACKING = 3;
}
