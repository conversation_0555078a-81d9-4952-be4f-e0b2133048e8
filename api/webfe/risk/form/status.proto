syntax = "proto3";

package webfe.risk.form;

option go_package = "github.com/epifi/gamma/api/webfe/risk/form";
option java_package = "com.github.epifi.gamma.api.webfe.risk.form";

enum Status {
  // Success
  OK = 0;
  // Invalid response, reason can be no response to mandatory question.
  // response validation failed etc.
  INVALID_ARGUMENT = 3;
  // Form does not exist for given id.
  NOT_FOUND = 5;
  // Internal error
  INTERNAL = 13;
  // Form is already submitted and can't be resubmitted.
  ALREADY_SUBMITTED = 101;
  // Form has expired.
  EXPIRED = 102;
  // User does not have access to the form.
  // It Might happen if a user has logged in with incorrect auth.
  ACCESS_DENIED = 103;
  // Cancelled form
  CANCELLED = 104;
}
