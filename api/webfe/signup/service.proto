syntax = "proto3";
package webfe.signup;

import "api/frontend/header/request.proto";
import "api/frontend/header/metadata.proto";
import "api/rpc/status.proto";
import "api/rpc/method_options.proto";
import "api/frontend/header/response.proto";
import "api/typesv2/common/phone_number.proto";
import "api/webfe/common/flow_name.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/webfe/signup";
option java_package = "com.github.epifi.gamma.api.webfe.signup";

service Signup {
  // Generates 6-digit OTP & delivers to phone number specified in the request via SMS
  // Resends the existing OTP if the request is made with same Token.
  //  - Generated OTP is to be valid for a short amount of time - Ex: 5 mins
  //  - If request is made prior to predefined time (Ex: 30 seconds), request the user/client to wait before second request.
  //  - To maintain exponential timer to prevent brute force attacks
  rpc GenerateOtp (GenerateOtpRequest) returns (GenerateOtpResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  };

  // Verify the OTP against a GenerateOTPRequest instance & login the user on a successful verification
  // On N invalid attempts to verify Otp, user has to be forced to generate a new Otp
  // VerifyOtp will return a Web Access token
  rpc VerifyOtp (VerifyOtpRequest) returns (VerifyOtpResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  };
}

message GenerateOtpRequest {
  frontend.header.RequestHeader req = 1;
  api.typesv2.common.PhoneNumber phone_number = 2;
  // Unique identifier of GenerateOtp request
  // If token(+phone_number) is not sent, a new OTP is generated and sent to the user via SMS
  // If token(+phone_number) is sent, request is treated as ResendOtp and existing OTP is sent to the user phone via SMS
  // For FLOW_NAME_NET_WORTH_MCP_AUTH, token is mandatory; it contains signed session id
  string token = 3;
  webfe.common.FlowName flow_name = 4;
}

// Represents response of GenerateOtp method
message GenerateOtpResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // List of status codes returned
  enum Status {
    // Otp Sent
    OK = 0;
    // Expired token. Generate a new token
    OTP_TOKEN_EXPIRED = 100;
    // Input token is not active. Cannot reuse it
    OTP_INACTIVE = 101;
    // Resend failed; Reached the limit to resend OTP
    OTP_RESEND_LIMIT = 102;
    // Request too soon; Please retry after a while
    OTP_RESEND_INTERVAL = 103;
  }
  // Represents if the OTP has been generated and sent without any errors
  rpc.Status status = 1;

  // Unique identifier of GenerateOtp request
  string token = 2;

  // A timer(in seconds) for the client post which it should raise a new request for Otp
  // Any attempt prior to this timer will not be honored & will result in error
  uint32 retry_timer_seconds = 3;
}

// Represents a request of VerifyOtp method
message VerifyOtpRequest {
  frontend.header.RequestHeader req = 15;

  // Phone number that is being verified
  api.typesv2.common.PhoneNumber phone_number = 1;

  // Unique identifier of GenerateOtp request
  string token = 2;
  // 6-digit OTP that is shared to the phone number via SMS
  string otp = 3 [(validate.rules).string = {len: 6, pattern: "^[0-9]+$"}];
  // Acquisition Info for the user
  // For example - URL from where verify otp was triggered
  AcquisitionInfo acq_info = 4;
  webfe.common.FlowName flow_name = 5;
}

message AcquisitionInfo {
  string url = 1;
}

// Represents a response of VerifyOtp method
message VerifyOtpResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // contains `device_integrity_nonce` required to generate safetynet attestaion.
  frontend.header.Metadata metadata = 3;
  // List of status codes returned
  enum Status {
    // OTP verified
    OK = 0;
    // Expired token. Generate a new token
    OTP_TOKEN_EXPIRED = 100;
    // Input token is not active. Cannot reuse it
    OTP_INACTIVE = 101;
    // Incorrect Otp
    OTP_INCORRECT = 102;
    // Incorrect Otp. Last attempt remaining
    OTP_INCORRECT_LAST_WARNING = 103;
    // Too many verification attempts on the token. Generate new token
    OTP_VERIFY_LIMIT = 104;
  }
  // Status of the request
  rpc.Status status = 1;
  // Represents a User's access token.
  // This token will serve as authentication parameter in the
  // further requests until an access token is generated
  string access_token = 2;
  string actor_id = 4;
}
