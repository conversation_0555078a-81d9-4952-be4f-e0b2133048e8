syntax = "proto3";

package api.webfe.deeplink_screen_option.loans;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/employment_type.proto";
import "api/webfe/deeplink_screen_option/message.proto";

option go_package = "github.com/epifi/gamma/api/webfe/deeplink_screen_option/loans";
option java_package = "com.github.epifi.gamma.api.webfe.deeplink_screen_option.loans";

// Helps in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message WebLoansEligibilityPanFormScreenOptions {
  // common header for all screen options
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;
  // common content for all screen options
  CommonOptions content = 2;
  // Loan Request ID for the ongoing pre-eligibility process.
  // This ties the submitted data to a specific evaluation flow.
  string request_id = 3;
}

message WebLoansEligibilityEmploymentFormScreenOptions {
  // common header for all screen options
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;
  // common content for all screen options
  CommonOptions content = 2;
  // list of employment types to be shown for the user to select
  message EmploymentTypeOptions {
    // employment type to be sent to the backend
    typesv2.EmploymentType type = 1;
    // employment type title to be shown to the user
    string title = 2;
  }
  // list of employment types to be shown for the user to select
  repeated EmploymentTypeOptions employment_type_options = 3;
  // Loan Request ID for the ongoing pre-eligibility process.
  // This ties the submitted data to a specific evaluation flow.
  string request_id = 4;
}

message WebLoansEligibilityTncScreenOptions {
  // common header for all screen options
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;
  // common content for all screen options
  CommonOptions content = 2;
  // title of the tnc
  string tnc_title = 3;
  // list of tncs to be shown on the screen
  message Tnc {
    // stringified html content of the tnc
    string html_content = 1;
    // id of the tnc
    string id = 2;
    // boolean value to check if the tnc is mandatory
    bool is_mandatory = 3;
  }

  // list of tncs to be shown on the screen
  repeated Tnc tnc_list = 4;
  // Loan Request ID for the ongoing pre-eligibility process.
  // This ties the submitted data to a specific evaluation flow.
  string request_id = 5;
}

message WebLoansEligibilityPollingScreenOptions {
  // common header for all screen options
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;
  // common content for all screen options
  CommonOptions content = 2;
  // flag to show the loader which is intended to be shown for a longer duration
  bool show_longer_duration_loader = 3;
  // Loan Request ID for the ongoing pre-eligibility process.
 // This ties the submitted data to a specific evaluation flow.
  string request_id = 5;
}

message WebLoansEligibilityLoanOfferScreenOptions {
  // common header for all screen options
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;
  // name of the user
  string name = 2;
  // loan amount of the loan offer
  string loan_amount = 3;
  // text of the main button to be shown on the screen
  string button_text = 4;
  // background image url of the loan offer
  string bg_image_url = 5;
  // disclaimer text box to be shown on the screen only when its not empty
  string disclaimer_text = 6;
  // Map of all event properties and their values (for analytics purpose)
  map<string, string> event_properties = 7;
}

message WebLoansEligibilityWaitlistScreenOptions {
  // common header for all screen options
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;
  // text of the main button to be shown on the screen
  string button_text = 2;
  // image url of the waitlist screen
  string image_url = 3;
  // disclaimer text box to be shown on the screen only when its not empty
  string disclaimer_text = 4;
  // title and sub-title to be shown on the screen
  string title = 5;
  string sub_title = 6;
  // Map of all event properties and their values (for analytics purpose)
  map<string, string> event_properties = 7;
}
