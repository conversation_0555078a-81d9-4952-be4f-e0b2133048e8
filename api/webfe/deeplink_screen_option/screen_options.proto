syntax = "proto3";

package api.webfe.deeplink_screen_option;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/webfe/deeplink_screen_option/message.proto";
import "api/webfe/common/flow_name.proto";

option go_package = "github.com/epifi/gamma/api/webfe/deeplink_screen_option";
option java_package = "com.github.epifi.gamma.api.webfe.deeplink_screen_option";

// Helps in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message WebErrorScreenOptions {
  // common header for all screen options
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;
  // image url to be displayed
  string image_url = 2;
  // title to be displayed
  string title = 3;
  // subtitle to be displayed
  string subtitle = 4;
  // button text to be displayed
  bool show_refresh_button = 5;
}

message WebPollingScreenOptions {
  // common header for all screen options
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;
  // common content for all screen options
  CommonOptions content = 2;
  // flag to show the loader which is intended to be shown for a longer duration
  bool show_longer_duration_loader = 3;
  // flow name to be used for the flow for deciding the rpc to be called
  .webfe.common.FlowName flow_name = 4;
  // request properties to be sent in the request to the rpc
  map<string, string> request_properties = 5;
}

message WebPanFormScreenOptions {
  // common header for all screen options
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;
  // common content for all screen options
  CommonOptions content = 2;
  // extra request properties to be sent in the request to the rpc
  map<string, string> extra_request_properties = 3;
}
