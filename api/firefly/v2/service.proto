syntax = "proto3";

package api.firefly.v2;

import "api/firefly/v2/card_request.proto";
import "api/firefly/v2/cc_offer.proto";
import "api/firefly/v2/credit_card.proto";
import "api/rpc/status.proto";
import "api/firefly/v2/enums/enums.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/firefly.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/firefly/v2";
option java_package = "com.github.epifi.gamma.api.firefly.v2";

// Service to enable Partner related services for Epifi users.
service FireflyV2 {
  // RPC to generate an authentication token for client to authenticate with credit card sdk
  // It checks if the user is already onboarded or has started onboarding,
  // based on which makes a decision to call different vg endpoints to generate the auth token
  rpc GenerateCreditCardSdkAuthToken (GenerateCreditCardSdkAuthTokenRequest) returns (GenerateCreditCardSdkAuthTokenResponse);

  // RPC to fetch the tracking details of the credit card
  rpc GetCreditCardTrackingDetails (GetCreditCardTrackingDetailsRequest) returns (GetCreditCardTrackingDetailsResponse);

  // RPC to fetch the credit cards for an actor_id/external_user_id
  rpc GetCreditCards (GetCreditCardsRequest) returns (GetCreditCardsResponse);

  // GetLandingInfo rpc for checking the current card state of the user and redirecting user to the corresponding
  // screen based on their card states, if card is not created yet we will redirect the user to the next screen based on their onboarding journey.
  rpc GetLandingInfo (GetLandingInfoRequest) returns (GetLandingInfoResponse);

  // RPC to fetch active credit card offer
  rpc GetCreditCardOffers (GetCreditCardOffersRequest) returns (GetCreditCardOffersResponse);
}

message GetCreditCardOffersRequest {
  string actor_id = 1;
  vendorgateway.Vendor vendor = 2;
  api.typesv2.CardProgram card_program = 3;
}

message GetCreditCardOffersResponse {
  rpc.Status status = 1;
  repeated CreditCardOffer offers = 2;
}

message GetLandingInfoRequest {
  string actor_id = 1;
}

message GetLandingInfoResponse {
  rpc.Status status = 1;
  // next action for the user based on the current card state of the user
  frontend.deeplink.Deeplink next_action = 2;
}

message GetCreditCardsRequest {
  oneof identifier {
    string external_user_id = 1;
    string actor_id = 2;
  }
  // by default, it will return only `CREATED` i.e. valid active cards
  repeated enums.CardState state_filters = 3;
}

message GetCreditCardsResponse {
  rpc.Status status = 1;
  repeated CreditCard credit_cards = 2;
}

message GetCreditCardTrackingDetailsRequest {
  oneof identifier {
    string external_user_id = 1;
    string actor_id = 2;
  }
}

message GetCreditCardTrackingDetailsResponse {
  rpc.Status status = 1;
  CardDeliveryTrackingDetails tracking_details = 2;
}

message GenerateCreditCardSdkAuthTokenRequest {
  // Unique identifier for the actor requesting the token.
  string actor_id = 1;
  typesv2.common.Device device_info = 2;
}

message GenerateCreditCardSdkAuthTokenResponse {
  rpc.Status status = 1;
  // Generated authentication token.
  string auth_token = 2;
  // Token type of the auth_token
  api.typesv2.CreditCardSdkTokenType token_type = 3;
}
