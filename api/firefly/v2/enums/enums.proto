// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=CardNetworkType,CardState,CardRequestStageState,CardRequestStage,CardRequestType,CardRequestStatus,CardTrackingDeliveryState,CreditCardApplicantType
syntax = "proto3";

package api.firefly.v2.enums;

option go_package = "github.com/epifi/gamma/api/firefly/v2/enums";
option java_package = "com.github.epifi.gamma.api.firefly.v2.enums";

enum CardNetworkType {
  CARD_NETWORK_TYPE_UNSPECIFIED = 0;
  CARD_NETWORK_TYPE_VISA = 1;
  CARD_NETWORK_TYPE_RUPAY = 2;
}

enum CardState {
  CARD_STATE_UNSPECIFIED = 0;
  CARD_STATE_CREATED = 2;
  CARD_STATE_CLOSED = 3;
}

enum CardRequestStageState {
  CARD_REQUEST_STAGE_STATE_UNSPECIFIED = 0;
  // stage has been initiated
  CARD_REQUEST_STAGE_STATE_INITIATED = 1;
  // stage has been completed
  CARD_REQUEST_STAGE_STATE_SUCCESS = 2;
  // stage has been failed
  CARD_REQUEST_STAGE_STATE_FAILED = 3;
}

enum CardRequestStage {
  CARD_REQUEST_STAGE_STAGE_UNSPECIFIED = 0;
  CARD_REQUEST_STAGE_STAGE_PAN_CHECK = 1;
  CARD_REQUEST_STAGE_STAGE_PRE_ELIGIBILITY_CHECK = 2;
  CARD_REQUEST_STAGE_STAGE_EKYC = 3;
  CARD_REQUEST_STAGE_STAGE_EKYC_VERIFICATION = 4;
  CARD_REQUEST_STAGE_STAGE_BRE = 5;
  CARD_REQUEST_STAGE_STAGE_SELFIE_CHECK = 6;
  CARD_REQUEST_STAGE_STAGE_VKYC = 7;
  CARD_REQUEST_STAGE_STAGE_VKYC_POLLING = 8;
  CARD_REQUEST_STAGE_STAGE_EMBOSS_NAME = 9;
  CARD_REQUEST_STAGE_STAGE_CIF_POLLING = 10;
  CARD_REQUEST_STAGE_STAGE_CUSTOMER_REGISTRATION = 11;
  CARD_REQUEST_STAGE_STAGE_SIM_BINDING = 12;
  CARD_REQUEST_STAGE_STAGE_SETUP_MPIN = 13;
  CARD_REQUEST_STAGE_STAGE_PENNY_DROP = 14;
  CARD_REQUEST_STAGE_STAGE_ADDITIONAL_REVIEW_REQUIRED = 15;
  CARD_REQUEST_STAGE_STAGE_REJECTED = 16;
  CARD_REQUEST_STAGE_ONBOARDING_INIT_AT_VENDOR = 17;
  CARD_REQUEST_STAGE_SELECT_DELIVERY_ADDRESS = 18;
  CARD_REQUEST_STAGE_TERMS_CONSENTS = 19;
  CARD_REQUEST_STAGE_CARD_CONSENTS = 20;
  CARD_REQUEST_STAGE_VERIFY_OTP = 21;
  CARD_REQUEST_STAGE_LIMIT_CHECK = 22;
  CARD_REQUEST_STAGE_ONBOARDING_COMPLETED = 23;
  CARD_REQUEST_STAGE_CMS_READY = 24;
  CARD_REQUEST_STAGE_PAN_SUCCESS_CHECK = 25;
  CARD_REQUEST_STAGE_LIMIT_RE_CHECK = 26;
  CARD_REQUEST_STAGE_CARD_ISSUED = 27;
  CARD_REQUEST_STAGE_PENNY_DROP_POLLING = 28;
  CARD_REQUEST_STAGE_ADDITIONAL_DETAILS = 29;
  CARD_REQUEST_STAGE_PERSONAL_DETAILS = 30;
  CARD_REQUEST_STAGE_PRE_APPROVED = 31;
  CARD_REQUEST_STAGE_VERIFY_CUSTOMER = 32;
  CARD_REQUEST_STAGE_CUSTOMER_DETAILS = 33;
  CARD_REQUEST_STAGE_OFFICE_ADDRESS = 34;
  CARD_REQUEST_STAGE_RE_KYC = 35;
  // When customer registration fails at bank due to some technical error we move user to this state and retry customer registration.
  // On UI user will see a retry CTA.
  CARD_REQUEST_STAGE_CUSTOMER_REGISTRATION_RETRY = 36;
}

enum CardRequestType {
  CARD_REQUEST_TYPE_UNSPECIFIED = 0;
  CARD_REQUEST_TYPE_ONBOARDING = 1;
  CARD_REQUEST_TYPE_DELIVERY_TRACKING = 2;
}

enum CardRequestStatus {
  CARD_REQUEST_STATUS_UNSPECIFIED = 0;
  CARD_REQUEST_STATUS_IN_PROGRESS = 1;
  CARD_REQUEST_STATUS_FAILED = 2;
  CARD_REQUEST_STATUS_SUCCESS = 3;
}

enum CreditCardFieldMask {
  CREDIT_CARD_FIELD_MASK_UNSPECIFIED = 0;
  CREDIT_CARD_FIELD_MASK_ID = 1;
  CREDIT_CARD_FIELD_MASK_ACTOR_ID = 2;
  CREDIT_CARD_FIELD_MASK_NETWORK_TYPE = 3;
  CREDIT_CARD_FIELD_MASK_EXTERNAL_USER_ID = 4;
  CREDIT_CARD_FIELD_MASK_STATE = 5;
  CREDIT_CARD_FIELD_MASK_VENDOR = 6;
  CREDIT_CARD_FIELD_MASK_CARD_PROGRAM = 7;
  CREDIT_CARD_FIELD_MASK_CREATED_AT = 8;
  CREDIT_CARD_FIELD_MASK_UPDATED_AT = 9;
  CREDIT_CARD_FIELD_MASK_DELETED_AT = 10;
}

enum CardRequestFieldMask {
  CARD_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  CARD_REQUEST_FIELD_MASK_ID = 1;
  CARD_REQUEST_FIELD_MASK_ACTOR_ID = 2;
  CARD_REQUEST_FIELD_MASK_TYPE = 3;
  CARD_REQUEST_FIELD_MASK_STATUS = 4;
  CARD_REQUEST_FIELD_MASK_REQUEST_DETAILS = 5;
  CARD_REQUEST_FIELD_MASK_STAGE_DETAILS = 6;
  CARD_REQUEST_FIELD_MASK_VENDOR = 7;
  CARD_REQUEST_FIELD_MASK_EXTERNAL_USER_ID = 8;
  CARD_REQUEST_FIELD_MASK_CREATED_AT = 9;
  CARD_REQUEST_FIELD_MASK_UPDATED_AT = 10;
  CARD_REQUEST_FIELD_MASK_DELETED_AT = 11;
}

enum CreditCardOfferFieldMask {
  CREDIT_CARD_OFFER_FIELD_MASK_UNSPECIFIED = 0;
  CREDIT_CARD_OFFER_FIELD_MASK_ID = 1;
  CREDIT_CARD_OFFER_FIELD_MASK_ACTOR_ID = 2;
  CREDIT_CARD_OFFER_FIELD_MASK_VENDOR = 3;
  CREDIT_CARD_OFFER_FIELD_MASK_OFFER_CONSTRAINTS = 4;
  CREDIT_CARD_OFFER_FIELD_MASK_VALID_SINCE = 5;
  CREDIT_CARD_OFFER_FIELD_MASK_VALID_TILL = 6;
  CREDIT_CARD_OFFER_FIELD_MASK_CARD_PROGRAM = 7;
  CREDIT_CARD_OFFER_FIELD_MASK_CREATED_AT = 8;
  CREDIT_CARD_OFFER_FIELD_MASK_UPDATED_AT = 9;
  CREDIT_CARD_OFFER_FIELD_MASK_DELETED_AT = 10;
}

enum CardTrackingDeliveryState {
  CARD_TRACKING_DELIVERY_STATE_UNSPECIFIED = 0;
  IN_TRANSIT = 1;
  SHIPPED = 2;
  OUT_FOR_DELIVERY = 3;
  DELIVERED = 4;
  RETURNED_TO_ORIGIN = 5;
}

enum CreditCardApplicantType {
  CREDIT_CARD_APPLICANT_TYPE_UNSPECIFIED = 0;
  CREDIT_CARD_APPLICANT_TYPE_ETB_ONBOARDING = 1;
  CREDIT_CARD_APPLICANT_TYPE_NTB_ONBOARDING = 2;
  CREDIT_CARD_APPLICANT_TYPE_PRE_APPROVED_ONBOARDING = 3;
}
