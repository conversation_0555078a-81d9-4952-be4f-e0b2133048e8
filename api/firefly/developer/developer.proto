// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package firefly.developer;

option go_package = "github.com/epifi/gamma/api/firefly/developer";
option java_package = "com.github.epifi.gamma.api.firefly.developer";

enum FireflyEntity {
  FIREFLY_ENTITY_UNSPECIFIED = 0;
  CREDIT_ACCOUNT = 1;
  CARD_TRANSACTION = 2;
  TRANSACTION_ADDITIONAL_INFO = 3;
  CREDIT_CARD_BILL = 4;
  CREDIT_CARD_BILL_PAYMENT = 5;
  CARD_AUDIT = 6;
  CARD_REQUEST = 7;
  CARD_REQUEST_STAGE = 8;
  CREDIT_CARD_OFFER = 9;
  CREDIT_CARD_OFFER_ELIGIBILITY_CRITERIA = 10;
  CREDIT_CARD = 11;
  CREDIT_CARD_SKU = 12;
  CREDIT_CARD_SKU_OVERRIDES = 13;
  LOAN_ACCOUNT = 14;
  TRANSACTION_LOAN_OFFERS = 15;
  DISPUTED_TRANSACTIONS = 16;
  PINOT_CC_TRANSACTIONS = 17;
  CREDIT_CARD_RECOMMENDATION_INFO = 18;
  CARD_REQUEST_V2 = 19;
  CREDIT_CARD_V2 = 20;
  CREDIT_CARD_OFFER_V2 = 21;
}
