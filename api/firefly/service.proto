syntax = "proto3";

package firefly;

import "api/dynamic_elements/dynamic_elements.proto";
import "api/firefly/billing/enums/enums.proto";
import "api/firefly/enums/enums.proto";
import "api/firefly/internal/card_request.proto";
import "api/firefly/internal/card_request_stage.proto";
import "api/firefly/internal/cc_offer.proto";
import "api/firefly/internal/credit_card.proto";
import "api/firefly/method_options.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/firefly/enums/enums.proto";
import "api/pay/service.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/card_type.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/date.proto";
import "api/typesv2/deposit.proto";
import "api/typesv2/device_unlock.proto";
import "api/typesv2/firefly.proto";
import "api/typesv2/money.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "google/type/postal_address.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/firefly";
option java_package = "com.github.epifi.gamma.api.firefly";

service Firefly {
  // FreezeUnfreezeCard rpc to freeze or unfreeze a card.
  // This rpc will initiate the freeze/unfreeze workflow with celestial and return status check api as the next action
  // which client will poll for checking the status of freeze/unfreeze request.
  // We will have auth validation for unfreeze request to prevent any malicious card actions and no validations in
  // case of freeze request. Post validations we will call vendor for freezing/unfreezing of card and based on
  // vendor response show appropriate next actions for the user.
  // Freeze operation for a card is a temporary operation and user can unfreeze the same card after authentication.
  rpc FreezeUnfreezeCard (FreezeUnfreezeCardRequest) returns (FreezeUnfreezeCardResponse) {
    option (firefly.skip_card_request_id_validation) = true;
  };

  // GetRequestStatus returns the current status of the request and returns next action(deeplink) present
  // in the card request.
  rpc GetRequestStatus (GetRequestStatusRequest) returns (GetRequestStatusResponse) {
    option (firefly.skip_card_id_validation) = true;
  };

  // CollectCardDeliveryAddress rpc for collecting the address type at which card is to be delivered for the user
  // This rpc returns the next action for the client.
  // This rpc will update the address type in card onboarding request and send a signal to card onboarding workflow
  // which will proceed to next steps after address selection
  rpc CollectCardDeliveryAddress (CollectCardDeliveryAddressRequest) returns (CollectCardDeliveryAddressResponse) {
    option (firefly.skip_card_id_validation) = true;
  };

  // StartCardOnboarding rpc to initiate card onboarding for a user.
  // We will create card request for onboarding workflow and initiate onboarding workflow with celestial and return the
  // next action for the user.
  // This rpc will throw error in case if user has already started onboarding.
  rpc StartCardOnboarding (StartCardOnboardingRequest) returns (StartCardOnboardingResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // GetLandingInfo rpc for checking the current card state of the user and redirecting user to the corresponding
  // screen based on their card states, if card is not created yet we will redirect the user to the next screen
  // based on their onboarding journey.
  rpc GetLandingInfo (GetLandingInfoRequest) returns (GetLandingInfoResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // RPC to fetch card limits for transactions like ATM, POS, ECOM
  rpc FetchCardLimits (FetchCardLimitsRequest) returns (FetchCardLimitsResponse) {
    option (firefly.skip_card_request_id_validation) = true;
    option (firefly.skip_card_id_validation) = true;
  };

  // RPC to fetch card usage for transactions like ATM, POS, ECOM, Contactless, International
  rpc FetchCardUsage (FetchCardUsageRequest) returns (FetchCardUsageResponse) {
    option (firefly.skip_card_request_id_validation) = true;
    option (firefly.skip_card_id_validation) = true;
  };

  /*
  RPC for triggering signal for card creation activity during onboarding workflow, we will get the bill generation date
  in the rpc input which will be stored against the onboarding card request and then send a signal for card creation at vendor.  */
  rpc CreateCard (CreateCardRequest) returns (CreateCardResponse) {
    option (firefly.skip_card_id_validation) = true;
  };

  // GetCreditCard rpc to fetch credit card using more than one get by options
  // returns:
  //      - NotFound if credit card is not present for the given get by option
  rpc GetCreditCard (GetCreditCardRequest) returns (GetCreditCardResponse) {
    option (firefly.skip_card_request_id_validation) = true;
    option (firefly.skip_card_id_validation) = true;
  };

  // RPC to select bill gen date request and returns a list of available dates for bill generation
  // and payment due dates
  rpc GetBillingDatesInfo (GetBillingDatesInfoRequest) returns (GetBillingDatesInfoResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // RPC to set the bill dates selected from the given list of available dates
  // the dates will include the bill generation date as well as the payment due date
  rpc SetCardBillingDates (SetCardBillingDatesRequest) returns (SetCardBillingDatesResponse) {
    option (firefly.skip_card_id_validation) = true;
  };

  // RPC to create card request and initiate workflow for a given request and return the next action
  // for the user
  rpc InitiateCardReq (InitiateCardReqRequest) returns (InitiateCardReqResponse) {
    option (firefly.skip_card_request_id_validation) = true;
    option (firefly.skip_card_id_validation) = true;
  };

  // RPC for verifying qr code of user
  rpc VerifyQRCode (VerifyQRCodeRequest) returns (VerifyQRCodeResponse);

  // RPC for setting card pin
  // deprecated in favour of SetCardPinV2
  rpc SetCardPin (SetCardPinRequest) returns (SetCardPinResponse);

  // RPC for setting preferences
  rpc SetCardPreferences (SetCardPreferencesRequest) returns (SetCardPreferencesResponse);

  // RPC for collecting auth flow from the user for a given workflow/request, we will signal the workflow to proceed
  // with auth activity from this rpc
  rpc CollectAuthFlow (CollectAuthFlowRequest) returns (CollectAuthFlowResponse) {
    option (firefly.skip_card_id_validation) = true;
  };

  // rpc to populate all the details needed for the credit card dashboard
  rpc GetDashboard (GetDashboardRequest) returns (GetDashboardResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // rpc to populate the cc dashboard section where the card details are displayed as per the card status
  // i.e whether the card has enable ecom transactions or not, etc
  rpc GetDashboardCardInfo (GetDashboardCardInfoRequest) returns (GetDashboardCardInfoResponse) {
    option (firefly.skip_card_request_id_validation) = true;
  };

  // RPC to fetch the unmasked card details for a card . This will take the customer_id of the
  // user in request and returns the card details of the user
  rpc GetCardDetails (GetCardDetailsRequest) returns (GetCardDetailsResponse) {
    option (firefly.skip_card_id_validation) = true;
  };

  rpc GetCardDeliveryDetails (GetCardDeliveryDetailsRequest) returns (GetCardDeliveryDetailsResponse) {
    option (firefly.skip_card_request_id_validation) = true;
  };

  //RPC to initiate the bill generation workflow for a given customer for a given month.
  rpc StartBillGenerationWorkflow (StartBillGenerationWorkflowRequest) returns (StartBillGenerationWorkflowResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // RPC to fetch ongoing credit card in app payment status
  rpc GetCreditCardPaymentStatus (GetCreditCardPaymentStatusRequest) returns (GetCreditCardPaymentStatusResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // RPC to signal workflow based on external id stored in card request stage
  rpc SignalWorkFlowByExternalId (SignalWorkFlowByExternalIdRequest) returns (SignalWorkFlowByExternalIdResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // RPC to fetch all cards for a user
  rpc GetAllCards (GetAllCardsRequest) returns (GetAllCardsResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // RPC to fetch card requests for a actor id and a workflow
  rpc GetCardRequestByActorIdAndWorkflow (GetCardRequestByActorIdAndWorkflowRequest) returns (GetCardRequestByActorIdAndWorkflowResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // RPC to fetch payload details for pin set
  rpc GetPinSetDetails (GetPinSetDetailsRequest) returns (GetPinSetDetailsResponse) {
    option (firefly.skip_card_request_id_validation) = true;
  };

  // RPC to set pin v2
  rpc SetCardPinV2 (SetCardPinV2Request) returns (SetCardPinV2Response) {
    option (firefly.skip_card_id_validation) = true;
  };

  // RPC to fetch active credit card offer
  rpc GetCreditCardOffers (GetCreditCardOffersRequest) returns (GetCreditCardOffersResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // RPC to estimate credit utilised by credit card
  rpc EstimateCreditUtilised (EstimateCreditUtilisedRequest) returns (EstimateCreditUtilisedResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // RPC for fetching the next action based on the rewards disbursement status
  rpc GetRewardDisbursementStatus (GetRewardDisbursementStatusRequest) returns (GetRewardDisbursementStatusResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // rpc to get card request and card request stages
  rpc GetCardRequestAndCardRequestStage (GetCardRequestAndCardRequestStageRequest) returns (GetCardRequestAndCardRequestStageResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  // ActorId is a mandatory parameter in the Request
  // Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no elements found for the given user on this screen
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the list of targeted comms elements is fetched successfully
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  }

  // DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // rpc to record the submission done by the user for their rewards for future claim
  // This takes in the reward id and reward option id and stores it for a conditional claim
  rpc RecordRewardSelectionInfo (RecordRewardSelectionInfoRequest) returns (RecordRewardSelectionInfoResponse) {
    option (firefly.skip_card_id_validation) = true;
  };

  // VerifyQrAndStartActivation validates the QR data and initiates card activation workflow if it is valid QR
  rpc VerifyQrAndStartActivation (VerifyQrAndStartActivationRequest) returns (VerifyQrAndStartActivationResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // FetchCreditCardEligibility takes actor id in request and checks if the user has a credit card and if not, checks
  // the eligibility of the user to get the cc
  rpc FetchCreditCardEligibility (FetchCreditCardEligibilityRequest) returns (FetchCreditCardEligibilityResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // TriggerRealtimeCardEligibilityCheck rpc to trigger real time card eligibility check for the user and return next screen to be shwon to the iser
  rpc TriggerRealtimeCardEligibilityCheck (TriggerRealtimeCardEligibilityCheckRequest) returns (TriggerRealtimeCardEligibilityCheckResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // RefreshCardDetails takes actor id in request and refreshes card details for an actor with vendor response
  rpc RefreshCardDetails (RefreshCardDetailsRequest) returns (RefreshCardDetailsResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // RPC to return the config required for calculating interest rates, and other details
  rpc GetDepositConfig (GetDepositConfigRequest) returns (GetDepositConfigResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // rpc to update card request with details required for fd creation, update new next action and signal the workflow
  // to initiate fd creation with deposit client.
  rpc CreateDeposit (CreateDepositRequest) returns (CreateDepositResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // rpc to initiate secured card onboarding.
  rpc StartCardOnboardingV2 (StartCardOnboardingV2Request) returns (StartCardOnboardingV2Response) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // rpc to signal workflow to move on
  rpc SignalMoveOn (SignalMoveOnRequest) returns (SignalMoveOnResponse) {
    option (firefly.skip_card_id_validation) = true;
  };
  // rpc to return whether a user has performed an action (swipe/click to continue) on a create card screen
  rpc GetUserCreditCardIntent (GetUserCreditCardIntentRequest) returns (GetUserCreditCardIntentResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // rpc to get deeplink using sync proxy workflow
  rpc GetRequestStatusSync (GetRequestStatusSyncRequest) returns (GetRequestStatusSyncResponse) {
    option (firefly.skip_card_id_validation) = true;
  };
  // rpc to update credit card entry. This will be a wrapper over the update dao method to make it usable from
  // other services
  rpc UpdateCreditCard (UpdateCreditCardRequest) returns (UpdateCreditCardResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // rpc to check whether a do once activity has already been done. It will accept a task id and returns true
  // the task id has already been done
  rpc CheckDoOnceActivityStatus (CheckDoOnceActivityStatusRequest) returns (CheckDoOnceActivityStatusResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  }

  // Updates the card limit field in latest card request for onboarding with the limit passed in the UpdateSecuredCardCreditLimitRequest
  rpc UpdateSecuredCardCreditLimit (UpdateSecuredCardCreditLimitRequest) returns (UpdateSecuredCardCreditLimitResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  }
  // GetLoungePassesForUser returns a list of lounges that have been claimed/currently claimed by the user
  rpc GetLoungePassesForUser (GetLoungePassesForUserRequest) returns (GetLoungePassesForUserResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // IsCreditCardUser returns whether the user has a credit card or not
  rpc IsCreditCardUser (IsCreditCardUserRequest) returns (IsCreditCardUserResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // GetRedeemedRewardsInfo returns all the rewards redeemed by the user in last anniversary year
  rpc GetRedeemedRewardsInfo (GetRedeemedRewardsInfoRequest) returns (GetRedeemedRewardsInfoResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  rpc GetCardRequestByExternalVendorId (GetCardRequestByExternalVendorIdRequest) returns (GetCardRequestByExternalVendorIdResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  rpc CollectCommunicationAddress (CollectCommunicationAddressRequest) returns (CollectCommunicationAddressResponse) {
    option (firefly.skip_card_id_validation) = true;
  };

  // ValidateCardPresence checks for the presence of credit or debit card of the user. It takes actor_id in the request
  // alongwith the last 4 digits of the card and the type of card (CREDIT, DEBIT).
  rpc ValidateCardPresence (ValidateCardPresenceRequest) returns (ValidateCardPresenceResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };

  // RPC method for recording user consent and proceeding with a user action, signaling the workflow according to context to move forward.
  rpc RecordConsentAndProceedWithUserAction (RecordConsentAndProceedWithUserActionRequest) returns (RecordConsentAndProceedWithUserActionResponse) {
    // Option to skip validation of card ID.
    option (firefly.skip_card_id_validation) = true;
    // Option to skip validation of card request ID.
    option (firefly.skip_card_request_id_validation) = false;
  };

  // RPC for stopping the credit card onboarding process.
  rpc StopCreditCardOnboarding (StopCreditCardOnboardingRequest) returns (StopCreditCardOnboardingResponse) {
    // Option to skip validation of card ID.
    option (firefly.skip_card_id_validation) = true;
    // Option to skip validation of card request ID.
    option (firefly.skip_card_request_id_validation) = false;
  };

  // RPC to trigger reversal of renewal fee for an unsecured credit card .
  rpc TriggerUnsecuredCCRenewalFeeReversal (TriggerUnsecuredCCRenewalFeeReversalRequest) returns (TriggerUnsecuredCCRenewalFeeReversalResponse) {
    option (firefly.skip_card_id_validation) = true;
    option (firefly.skip_card_request_id_validation) = true;
  };
  // GetPaymentsDomainOrderData will be used to fetch cards related data for payment orchestration. This will be
  // used to fetch any cards domain data like redirect action, payment amount, etc. The presence of this RPC will
  // ensure the isolation of domain specific logic to the specific domain itself.
  rpc GetPaymentsDomainOrderData (pay.GetPaymentsDomainOrderDataRequest) returns (pay.GetPaymentsDomainOrderDataResponse) {
    // Option to skip validation of card ID.
    option (firefly.skip_card_id_validation) = true;
    // Option to skip validation of card request ID.
    option (firefly.skip_card_request_id_validation) = true;
  };
}

message TriggerUnsecuredCCRenewalFeeReversalRequest {
  // Identifier for which we are triggering the reversal.
  string actor_id = 1;
  // Origin of the request, specifying where it originated from.
  firefly.enums.Provenance provenance = 2;
  // reason for fee reversal
  string description = 3 [(validate.rules).string.min_len = 1];
}

message TriggerUnsecuredCCRenewalFeeReversalResponse {
  rpc.Status status = 1;
}

message GetRedeemedRewardsInfoRequest {
  string actor_id = 1;
  repeated ResponseField field_mask = 2;

  enum ResponseField {
    RESPONSE_FIELD_UNSPECIFIED = 0;
    RESPONSE_FIELD_ONBOARDING_REWARD_TYPE = 1;
    RESPONSE_FIELD_FI_COINS = 2;
    RESPONSE_FIELD_FI_COINS_CASHBACK_VALUE = 3;
    RESPONSE_FIELD_CASHBACK = 4;
    RESPONSE_FIELD_LOUNGE_PASS = 5;
    RESPONSE_FIELD_FOREX_FEES = 6;
  }
}

message GetRedeemedRewardsInfoResponse {
  rpc.Status status = 1;
  // rewards type selected at the time of onboarding
  enums.CCRewardType selected_reward_type = 2;
  // total fi-coins earned
  double fi_coins_count = 3;
  // total cashback value for total fi coins earned
  google.type.Money cashback_value_of_fi_coins = 4;
  // total cashback received
  google.type.Money cashback_received = 5;
  // lounge pass availed
  int32 lounge_pass_availed = 6;
  // total forex markup fees saved
  google.type.Money forex_fees_saved = 7;
}

// Request message for stopping credit card onboarding.
message StopCreditCardOnboardingRequest {
  string card_request_id = 1; // Identifier for the failed card request.
}

// Response message for stopping credit card onboarding.
message StopCreditCardOnboardingResponse {
  // Response status containing metadata.
  rpc.Status status = 1; // Status of the response.
  // Deeplink for the next action to be performed.
  frontend.deeplink.Deeplink next_action = 2; // Deeplink for the next action.
}


// Request message for recording consent and proceeding with a user action.
message RecordConsentAndProceedWithUserActionRequest {
  // Consent for credit card onboarding.
  frontend.firefly.enums.CreditCardOnboardingConsent credit_card_onboarding_consent = 1;
  // Unique identifier for the card request.
  string card_request_id = 2;
  // Actor ID for the user initiating the action.
  string actor_id = 3;
  // Information about the device used by the user.
  api.typesv2.common.Device device = 4;
}

// Response message for recording consent and proceeding with a user action.
message RecordConsentAndProceedWithUserActionResponse {
  // Response status containing metadata.
  rpc.Status status = 1;
  // Deeplink for the next action to be performed.
  frontend.deeplink.Deeplink next_action = 2;
}


message ValidateCardPresenceRequest {
  string actor_id = 1;
  // type of card, i.e CREDIT/DEBIT. If unspecified, it will look for
  // both Credit and Debit cards for the given number
  api.typesv2.CardType card_type = 2;
  // last 4 digits of the card for which the txn has taken place.
  string last_four_digits = 3 [(validate.rules).string.len = 4];
}

message ValidateCardPresenceResponse {
  rpc.Status status = 1;
  // status to signify whether the queried card number exists or not for the given actor
  // for the given card type
  api.typesv2.common.BooleanEnum presence_status = 2;
}

message GetCardRequestByExternalVendorIdRequest {
  string external_vendor_id = 1;
}

message GetCardRequestByExternalVendorIdResponse {
  rpc.Status status = 1;
  repeated CardRequest card_requests = 2;
}

// IsCreditCardUserRequest is the request for IsCreditCardUser RPC
message IsCreditCardUserRequest {
  string actor_id = 1;
}

// IsCreditCardUserResponse is the response for IsCreditCardUser RPC
message IsCreditCardUserResponse {
  rpc.Status status = 1;
  bool is_credit_card_user = 2;
}

message GetLoungePassesForUserRequest {
  string actor_id = 1;
}

message GetLoungePassesForUserResponse {
  rpc.Status status = 1;
  repeated LoungePass lounge_passes = 2;
  message LoungePass {
    // time in which the lounge was claimed by the user
    google.protobuf.Timestamp claim_time = 1;
    // time of expiration of the lounge pass
    google.protobuf.Timestamp expiration_time = 2;
    // external id to identify a lounge pass
    string redemption_id = 3;
    // web url for the qr code pdf file
    string qr_code_url = 4;
  }
}

message CheckDoOnceActivityStatusRequest {
  string do_once_task_id = 1;
}

message CheckDoOnceActivityStatusResponse {
  rpc.Status status = 1;
  bool is_task_done = 2;
}

message UpdateCreditCardRequest {
  CreditCard credit_card = 1;
  repeated enums.CreditCardFieldMask credit_card_field_mask = 2;
}

message UpdateCreditCardResponse {
  rpc.Status status = 1;
}

message RefreshCardDetailsRequest {
  string actor_id = 1;
}

message RefreshCardDetailsResponse {
  rpc.Status status = 1;
}

message TriggerRealtimeCardEligibilityCheckRequest {
  string actor_id = 1;
  int32 screen_identifier = 2;
  api.typesv2.CardProgram card_program = 3;
}

message TriggerRealtimeCardEligibilityCheckResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message FetchCreditCardEligibilityRequest {
  string actor_id = 1;
  firefly.enums.Vendor vendor = 2;
  bool should_call_vendor = 3;
  api.typesv2.CreditCardRequestHeader credit_card_request_header = 4;
}

message FetchCreditCardEligibilityResponse {
  rpc.Status status = 1;
  // will be true if the user already has a cc or has a
  // valid available limit for the user
  bool is_user_cc_eligible = 2;
  // value of the limit available for cc for the user
  google.type.Money available_limit = 3;
  // offer id for the
  string offer_id = 4;
  // card program type of the user
  api.typesv2.CardProgramType card_program_type = 5;
  // has the user been approved via web
  bool is_user_web_approved = 6;
  // represents the estimated available limit based on the credit report
  // of the user
  google.type.Money estimated_limit = 7;
  api.typesv2.CardProgram card_program = 8;
  bool is_user_fi_rejected = 9;
}

message RecordRewardSelectionInfoRequest {
  string reward_id = 1;
  string reward_option_id = 2;
  string card_request_id = 3;
  enums.CCRewardType reward_type = 5;
}

message RecordRewardSelectionInfoResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetCardRequestAndCardRequestStageRequest {
  string actor_id = 1;
  enums.CardRequestWorkFlow card_request_workflow = 2;
}

message GetCardRequestAndCardRequestStageResponse {
  rpc.Status status = 1;
  CardRequest card_request = 2;
  repeated CardRequestStage card_request_stages = 3;
}

message GetRewardDisbursementStatusRequest {
  string actor_id = 1;
  string client_request_id = 2;
  string card_request_id = 3;
  enums.CCRewardOfferType reward_offer_type = 4;
}

message GetRewardDisbursementStatusResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetCardRequestByActorIdAndWorkflowRequest {
  string actor_id = 1;
  enums.CardRequestWorkFlow card_request_work_flow = 2;
}

message GetCardRequestByActorIdAndWorkflowResponse {
  rpc.Status status = 1;
  CardRequest card_request = 2;
}

message GetAllCardsRequest {
  string actor_id = 1;
}

message GetAllCardsResponse {
  rpc.Status status = 1;
  repeated firefly.CreditCard credit_cards = 2;
}

message FreezeUnfreezeCardRequest {
  // id of the card which needs to be frozen/unfrozen
  string card_id = 1;
  // freeze/unfreeze request
  firefly.enums.CardRequestType request_type = 2;
  // origin of freeze/unfreeze request
  firefly.enums.Provenance provenance = 3;
  // reason for freeze/unfreeze card
  string reason = 4;

  api.typesv2.DeviceUnlockMechanism device_unlock_mechanism = 5;
  api.typesv2.DeviceUnlockMechanismStrength device_unlock_mechanism_strength = 6;
}

message FreezeUnfreezeCardResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // next action for the client
  frontend.deeplink.Deeplink next_action = 2;
}

message GetRequestStatusRequest {
  // unique identifier for the a request
  string card_request_id = 1;
}

message GetRequestStatusResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  // next action for the client
  frontend.deeplink.Deeplink next_action = 2;

  // current request status for the action
  firefly.enums.CardRequestStatus request_status = 3;

  // workflow name of the card request
  firefly.enums.CardRequestWorkFlow workflow = 4;
  // details specific to the particular card request
  firefly.CardRequestDetails card_request_details = 5;
}

message CollectCardDeliveryAddressRequest {
  // address type of the address at which card is to be delivered
  // actual address of the user will be present with the user service
  api.typesv2.AddressType address_type = 1 [(validate.rules).enum = {not_in: [0]}];

  // unique identifier for the card onboarding request
  string card_request_id = 2 [(validate.rules).string.min_len = 1];
}

message CollectCardDeliveryAddressResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // next action for the client
  frontend.deeplink.Deeplink next_action = 2;
}

message StartCardOnboardingRequest {
  string actor_id = 1;

  firefly.enums.Provenance provenance = 2;

  firefly.enums.CardNetworkType card_network_type = 18;
  api.typesv2.CardProgram card_program = 19;
}

message StartCardOnboardingResponse {
  enum Status {
    OK = 0;

    // If user has already initiated card onboarding
    ALREADY_EXIST = 5;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  // unique request id for onboarding request
  string card_request_id = 2;

  // next action for the user
  // Currently in case of secure cards this will redirect user to open fixed deposit, in case of unsecure cards we will
  // redirect user to address selection screen.
  frontend.deeplink.Deeplink next_action = 3;

  // offer id based on which request is made
  string offer_id = 4;

  api.typesv2.CardProgram card_program = 5;
}
message FetchCardLimitsRequest {
  string credit_card_id = 1;
  string actor_id = 2;
}

message FetchCardLimitsResponse {
  rpc.Status status = 1;
  repeated ControlLimits control_limits = 2;
  message ControlLimits {
    firefly.enums.CardControlType card_control_type = 1;
    google.type.Money daily_limit_value = 2;
    google.type.Money min_daily_limit_value = 3;
    google.type.Money max_daily_limit_value = 4;
    bool limit_disabled = 5;
  }
  enums.CardState card_state = 3;
  // list of card control limits. Required to extend card limits wrt
  // location type. Key will be the card control location type and value
  // will be the list of control limits
  map<string, CardLimitDetails> card_limits = 4;

  message CardLimitDetails {
    repeated ControlLimits control_limits = 1;
    enums.CardLimitDisablementType card_limit_disablement_type = 2;
  }

  string credit_card_id = 5;

}

message FetchCardUsageRequest {
  string credit_card_id = 1;
  string actor_id = 2;
}

message FetchCardUsageResponse {
  rpc.Status status = 1;
  repeated CardUsage card_usages = 2;

  message CardUsage {
    firefly.enums.CardControlType card_control_type = 1;
    bool is_enabled = 2;
  }
  enums.CardState card_state = 3;
  string credit_card_id = 4;
}

message GetLandingInfoRequest {
  string actor_id = 1;

  api.typesv2.CreditCardRequestHeader credit_card_request_header = 2;
}

message GetLandingInfoResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  // next action for the user based on the current card state of the user
  frontend.deeplink.Deeplink next_action = 2;

  // offer id based on which request is made
  string offer_id = 3;

  // program type based on which workflow will be initiated.
  api.typesv2.CardProgramType card_program_type = 4;

  api.typesv2.CardProgram card_program = 5;

  // Flag indicating that credit card v2 flow(via SDK) is enabled or not.
  // This flag will be true only when user is not onboarded on any active credit card via old flow.
  bool is_cc_sdk_flow_enabled_for_user = 6;
}


message CreateCardRequest {
  // unique request id which will stay consistent throughout a workflow .
  string card_reqeust_id = 1;

  google.type.Date bill_gen_date = 2 [deprecated = true];
  int64 selected_payment_due_date = 3;
  int64 selected_bill_gen_date = 4;
  api.typesv2.AddressType address_type = 5;
}

message CreateCardResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
  api.typesv2.CardProgram card_program = 6;
}

message GetCreditCardRequest {
  oneof GetBy {
    string vendor_identifier = 1;
    string credit_card_id = 2;
    string actor_id = 4;
  }
  // optional parameter to fetch only few params
  // we will return the complete credit card object is passed empty
  repeated firefly.enums.CreditCardFieldMask select_field_masks = 3;
}

message GetCreditCardResponse {
  enum Status {
    OK = 0;

    NOT_FOUND = 5;
  }
  rpc.Status status = 1;
  firefly.CreditCard credit_card = 2;
}

message GetBillingDatesInfoRequest {

}

message GetBillingDatesInfoResponse {
  rpc.Status status = 1;
  repeated CreditCardBillingDates dates = 2;
}

// structure to hold the set of bill generation date and payment due date for the credit card user
message CreditCardBillingDates {
  int64 bill_gen_date = 1;
  int64 payment_due_date = 2;
  bool default = 3;
}

message SetCardBillingDatesRequest {
  CreditCardBillingDates billing_dates = 1;
  int64 payment_due_date = 2;
  string card_request_id = 3;
}

message SetCardBillingDatesResponse {
  rpc.Status status = 1;
  string card_request_id = 2;
  frontend.deeplink.Deeplink next_action = 3;
}

message CollectAuthFlowRequest {
  // identifier for a card request
  string card_request_id = 1;

  // auth flow
  firefly.enums.CardAuthFlow card_auth_flow = 2;
}

message CollectAuthFlowResponse {
  rpc.Status status = 1;
  // next action for client
  frontend.deeplink.Deeplink next_action = 2;
}

message InitiateCardReqRequest {
  string card_id = 1;
  // workflow corresponding to which card request has to be created
  firefly.enums.CardRequestWorkFlow card_request_work_flow = 2;
  // entry point of the request
  firefly.enums.Provenance provenance = 3;
  // request data con contains user provided data for initiating a card request
  oneof RequestData {
    // contains user provided data for changing card limits
    firefly.LimitsChangeData limits_change_data = 4;
    // contains user provided data for changing card controls
    firefly.ControlsChangeData controls_change_data = 5;
    // contains user provided data for reissue card
    firefly.ReissueCardData reissue_card_data = 6;
    // contains user provided data for raising dispute
    firefly.DisputeData dispute_data = 7;
    // contains credit card repayment request details
    firefly.PaymentData payment_data = 8;
    // contains export statement data
    firefly.ExportStatementData export_statement_data = 9;
    // contains card activation data
    firefly.CardActivationData card_activation_data = 10;
    // contains data regarding the real time profile validation flow.
    firefly.RealTimeProfileValidationCheckData real_time_profile_validation_check_data = 11;
    // contains details regarding the auto payment of the user
    firefly.AutoRepaymentData auto_repayment_data = 12;
    // contains details to send comms to the user
    firefly.UserCommunicationData user_communication_data = 13;
  }
  api.typesv2.DeviceUnlockMechanism device_unlock_mechanism = 14;
  api.typesv2.DeviceUnlockMechanismStrength device_unlock_mechanism_strength = 15;
  // actor id of the user for whom the workflow is going to start.
  // This will be required to start workflow pre to card onboarding
  string actor_id = 16;
  // [OPTIONAL] client request id with which the request has to be created. This would be useful in flows in which
  // the flow initialisation and polling is done by an external service. This id will be stored in the orchestration_id
  // field of card request
  string client_request_id = 17;
}

message InitiateCardReqResponse {
  rpc.Status status = 1;

  frontend.deeplink.Deeplink next_action = 2;

  string card_request_id = 3;
}

message LimitsChangeData {
  // This is used to convey if the updated daily value is increased from previous value or not
  // If true, then auth will be done; otherwise not
  bool is_value_increase = 1;

  // control type (ATM, POS, ECOM, Contactless) for which new value is being set
  firefly.enums.CardControlType control_type = 2;

  google.type.Money updated_limit_value = 3;
  // location type for which limit change has happened
  firefly.enums.CardUsageLocationType card_usage_location_type = 4;
}

message ControlsChangeData {
  // Enable or disable International
  api.typesv2.common.BooleanEnum international = 1;
  // Enable or disable contactless
  api.typesv2.common.BooleanEnum contactless = 2;
  // Enable or disable ATM transactions
  api.typesv2.common.BooleanEnum atm = 3;
  // Enable or disable POS transactions
  api.typesv2.common.BooleanEnum pos = 4;
  // Enable or disable ECOM transacations
  api.typesv2.common.BooleanEnum ecom = 5;
}

message ReissueCardData {
  // reason for blocking the previous card
  string block_card_reason = 1;
}

message DisputeData {
  string txn_id = 1;
  google.type.Money amount = 2;
  string reason = 3;
  string description = 4;
  firefly.enums.DisputeType dispute_type = 5;
  string url = 6;
}

message VerifyQRCodeRequest {
  string actor_id = 1;
  // card_id of the card for which we need to verify data
  string card_id = 2;
  // card_request_id of the request to be updated
  string card_request_id = 3;
  // encrypted data present in the qr code
  string qr_data = 4;
  // vendor at which card was printed
  // we need this for identifying the keys which needs to be used for validating the qr data
  firefly.enums.CardPrintingVendor card_printing_vendor = 5;
}

message VerifyQRCodeResponse {
  enum Status {
    OK = 0;
    VENDOR_API_FAILURE = 100;
    VERIFICATION_FAILED = 101;
    // User has already activated card
    CARD_ALREADY_ACTIVATED = 102;
  }
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

// deprecated in favour of SetCardPinV2
message SetCardPinRequest {
  string card_request_id = 1;
  string card_id = 2;
  string cred_block = 3;
  google.type.Date expiry_date = 4;
}

// deprecated in favour of SetCardPinV2
message SetCardPinResponse {
  enum Status {
    OK = 0;
    // If the pin was not set, validation will fail.
    PIN_NOT_SET = 101;
    // pin setup is pending. Use the checkStatus api to check the status later.
    PENDING = 103;
    // User has entered a weak pin such as 0000 or 1234
    WEAK_PIN_ENTERED = 201;
    // Cred block validation failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
  }
  rpc.Status status = 1;
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 2;
  frontend.deeplink.Deeplink next_action = 3;
}

message SetCardPreferencesRequest {
  string card_request_id = 1;
  string card_id = 2;
  message CardPreferences {
    // Enable or disable International
    api.typesv2.common.BooleanEnum international = 1;
    // Enable or disable contactless
    api.typesv2.common.BooleanEnum contactless = 2;
    // Enable or disable ATM transactions
    api.typesv2.common.BooleanEnum atm = 3;
    // Enable or disable POS transactions
    api.typesv2.common.BooleanEnum pos = 4;
    // Enable or disable ECOM transacations
    api.typesv2.common.BooleanEnum ecom = 5;
  }
  CardPreferences card_preferences = 3;
}

message SetCardPreferencesResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetDashboardRequest {
  string actor_id = 1;
}

message GetDashboardResponse {
  message BasicInfo {
    string card_id = 1;
    api.typesv2.Money outstanding_balance = 2 [deprecated = true];
    api.typesv2.Money available_balance = 3 [deprecated = true];
    api.typesv2.Money total_balance = 4 [deprecated = true];
    enums.CardState card_state = 5;
  }
  message BillInfo {
    int32 bill_gen_date = 1;
  }
  message RewardsInfo {
    // TODO: add details once design is finalized
  }

  message FeatureFlags {
    bool enable_emi_conversion = 1;
  }

  rpc.Status status = 1;
  BasicInfo basic_info = 2;
  BillInfo bill_info = 3;
  FeatureFlags feature_flags = 4;
  frontend.deeplink.InfoItemWithCta card_additional_info = 5;
  repeated frontend.deeplink.InfoItemWithCta carousel = 6;
}


message GetDashboardCardInfoRequest {
  string card_id = 1;
}

message GetDashboardCardInfoResponse {
  rpc.Status status = 1;
  bool ecom_enabled = 2;
  google.type.Money outstanding_balance = 3 [deprecated = true];
  google.type.Money available_balance = 4 [deprecated = true];
  google.type.Money total_balance = 5 [deprecated = true];
  string masked_card_number = 6;
  bool card_frozen = 7;
  bool card_blocked = 8;
  string card_id = 9;
  google.type.Money limit_utilised = 10;
  google.type.Money limit_available = 11;
  google.type.Money total_limit = 12;
  enums.CardState card_state = 13;
}

message GetCardDetailsRequest {
  string card_request_id = 1;
}

message GetCardDetailsResponse {
  rpc.Status status = 1;
  // Due to compliance regulations we cannot have raw expiry passing through our backend layer, we will be using the
  // expiry token to pass the expiry which will be converted to actual expiry date by atlas
  api.typesv2.Date expiry_date = 2 [deprecated = true];
  string clear_card_number = 3;
  enums.CardNetworkType card_network_type = 4;
  string cvv = 5;
  api.typesv2.common.Name name = 6;
  int64 view_card_details_expiry = 7;
  int64 copy_card_details_expiry = 8;
  // card program to be used to determine the background card image to be used in fe
  api.typesv2.CardProgram card_program = 9;
  string expiry_token = 10;
}

message GetCardDeliveryDetailsRequest {
  string card_id = 2;
}

message GetCardDeliveryDetailsResponse {
  rpc.Status status = 1;
  enums.PhysicalCardStatus physical_card_status = 2;
  ShipmentDetails shipment_details = 3;
  google.protobuf.Timestamp delivered_at = 4;
  message ShipmentDetails {
    // unique identifier using which user can track their card
    string awb = 1;
    // courier partner
    string carrier = 2;
  }
  google.type.PostalAddress delivery_address = 5;
}


message StartBillGenerationWorkflowRequest {
  string entity_id = 1;
  google.type.Date statement_date = 2;
  vendorgateway.Vendor vendor = 3;
}

message StartBillGenerationWorkflowResponse {
  rpc.Status status = 1;
}

message PaymentData {
  // actor id of the user for whom the payment is being done
  string actor_id = 1;
  string card_id = 2;
  google.type.Money amount = 4;
  // amount of money  that has to be paid without user intervention.
  // this can come as a redemption of fi-coins, etc. Something like a
  // discount
  google.type.Money discount_amount = 5;
  // account id of the source account . This is to add an extension for tpap payments.
  // If empty, it will go forward with fi savings account as the source account
  string account_id = 6;
  // type of the source account to identify where the payment is being initiated from
  enums.PaymentAccountType payment_account_type = 7;
  // base64 encoded account id containing data as to whether it contains an internal or
  // tpap account id
  string derived_account_id = 8;
}

message AutoRepaymentData {
  // actor id of the user for whom the payment is being done
  string actor_id = 1;
  string card_id = 2;
  google.type.Money amount = 4;
  // entry point from where the bill payment is being done .
  // It can be user initiated or automatic
  billing.enums.PaymentProvenance payment_provenance = 3;
}

message UserCommunicationData {
  // it represents the type of communication for which card request has been created
  firefly.enums.CommunicationType communication_type = 1;
  // it is the time from which the comms are configured to be triggered after some delay, this will not be used in case of stage drop off comms as there can be separate reference time for each stages
  google.protobuf.Timestamp reference_time = 2;
  // this is required when we need to send workflow stage drop off comms as we need this to fetch latest card request and then current stage for the initiator workflow
  string initiator_wf_client_req_id = 3;
  // Flag used for enabling and disabling certain duration-based comms delivery,
  // such as between 10 am to 10 pm. Time range will be controlled from config.
  bool skip_trigger_time_window_validation = 4;
}

message ExportStatementData {
  string bill_id = 3;
}

message GetCreditCardPaymentStatusRequest {
  // unique identifier for the a request
  string payment_req_id = 1;
  // the current polling attempt number
  int32 attempt_number = 2;
}

message GetCreditCardPaymentStatusResponse {
  rpc.Status status = 1;
  // current request status for the action
  enums.PaymentStatus payment_status = 2;
  // deeplink to be shown to the user
  frontend.deeplink.Deeplink deeplink = 3;
}

message SignalWorkFlowByExternalIdRequest {
  //external id for which we need to fetch card request stage and signal workflow
  string external_id = 1;
  string signal_name = 2;
  oneof SignalWorkFlowPayload {
    firefly.VendorRepaymentSignalWorkflowData vendor_repayment_signal_workflow_data = 3;
  }
  firefly.enums.CardRequestStageName stage_name = 4;
}

message SignalWorkFlowByExternalIdResponse {
  rpc.Status status = 1;
}

message VendorRepaymentSignalWorkflowData {
  firefly.enums.PaymentStatus payment_status = 1;
}

message GetPinSetDetailsRequest {
  string card_id = 1;
}

message GetPinSetDetailsResponse {
  rpc.Status status = 1;
  // pin set payload
  message Payload {
    string entity_id = 1;
    string kit_no = 2;
    // Due to compliance regulations we cannot have raw expiry passing through our backend layer, we will be using the
    // expiry token to pass the expiry which will be converted to actual expiry date by atlas
    api.typesv2.Date expiry_date = 3 [deprecated = true];
    api.typesv2.Date dob = 4;
    string masked_card_no = 5;
    string expiry_token = 6;
  }
  Payload payload = 2;
}

message SetCardPinV2Request {
  string card_request_id = 1;
  string cred_block = 2;
  string device_id = 3;
  api.typesv2.common.Platform platform = 4;
}

message SetCardPinV2Response {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetCreditCardOffersRequest {
  string actor_id = 1;
  enums.Vendor vendor = 2;
  api.typesv2.CardProgram card_program = 3;
}

message GetCreditCardOffersResponse {
  rpc.Status status = 1;
  repeated CreditCardOffer offers = 2;
}

message EstimateCreditUtilisedRequest {
  string actor_id = 1;
  enums.Vendor vendor = 2;
}

message EstimateCreditUtilisedResponse {
  rpc.Status status = 1;
  google.type.Money credit_utilised = 2;
}

message VerifyQrAndStartActivationRequest {
  string actor_id = 1;
  // encrypted data present in the qr code
  string qr_data = 2;
  // vendor at which card was printed
  // we need this for identifying the keys which needs to be used for validating the qr data
  firefly.enums.CardPrintingVendor card_printing_vendor = 3;
}

message VerifyQrAndStartActivationResponse {
  enum Status {
    OK = 0;
    VENDOR_API_FAILURE = 100;
    VERIFICATION_FAILED = 101;
    // User has already activated card
    CARD_ALREADY_ACTIVATED = 102;
  }
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message CardActivationData {
  bool isQrCodeVerified = 1;
}

// Data structure for real-time profile validation check.
message RealTimeProfileValidationCheckData {
  // Email ID of the user for whom the validation is running.
  // Stored here since the email is unverified and cannot be stored in the user entity.
  string email_id = 1;
  // Client request ID with which the authentication has been completed.
  // If non-empty, we can assume that an authentication has already been done.
  string auth_client_req_id = 2;
  // Name of the user. This will be used in sending communications.
  api.typesv2.common.Name name = 3;
  // Type of card program for which eligibility is being checked.
  api.typesv2.CardProgramType card_program_type = 4;
  // Vendor providing the card program.
  api.typesv2.CardProgramVendor card_program_vendor = 5;
}


message GetDepositConfigRequest {
  api.typesv2.CardProgramVendor vendor = 1;
}

message GetDepositConfigResponse {
  rpc.Status status = 1;
  // Min deposit amount required for obtaining a secured card
  google.type.Money min_deposit_amount = 2;
  // Max deposit a user can make for obtaining a secured card
  google.type.Money max_deposit_amount = 3;
  // Deposit amount = deposit_multiplier * desired credit limit amount
  float deposit_multiplier = 4;
  // Default credit limit is a list of credit limit that a user can choose from
  repeated google.type.Money default_credit_limits = 5;
  // Credit limit shown to the user when the calculation screen loads for the first time
  google.type.Money selected_credit_limit = 6;
  // Term for which the deposit is created and lien marked
  api.typesv2.DepositTerm deposit_term = 7;
  // Credit limit = credit_multiplier * deposit amount
  float credit_multiplier = 8;
  api.typesv2.DepositTerm min_deposit_term = 9;
  api.typesv2.DepositTerm max_deposit_term = 10;
  google.type.Money min_credit_limit = 11;
  repeated SliderAmountDetail slider_amount_details = 12;
  SliderAmountDetail default_slider_amount_detail = 13;
}

message SliderAmountDetail {
  google.type.Money deposit_amount = 1;
  google.type.Money credit_limit = 2;
  string deposit_amount_text = 3;
  int32 amount_slider_value = 4;
}

message CreateDepositRequest {
  google.type.Money amount = 1 [(validate.rules).message.required = true];
  api.typesv2.DepositNomineeDetails nominee_details = 2;
  string actor_id = 3;
  api.typesv2.DepositTerm deposit_term = 4;
  google.type.Money deposit_amount = 5;
  google.type.Money credit_limit = 6;
}

message CreateDepositResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message StartCardOnboardingV2Request {
  string actor_id = 1;
  firefly.enums.Provenance provenance = 2;
  firefly.enums.CardNetworkType card_network_type = 18;
  api.typesv2.CardProgram card_program = 19;
}

message StartCardOnboardingV2Response {
  rpc.Status status = 1;
  // unique request id for onboarding request
  string card_request_id = 2;
  // currently in case of secure cards this will redirect user to polling screen, in case of unsecure cards we will
  // redirect user to address selection screen.
  frontend.deeplink.Deeplink next_action = 3;
  string offer_id = 4;
  api.typesv2.CardProgram card_program = 5;
}

message SignalMoveOnRequest {
  string card_request_id = 1;
  firefly.enums.SignalContext signal_context = 2;
}

message SignalMoveOnResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetUserCreditCardIntentRequest {
  string actor_id = 1;
}

message GetUserCreditCardIntentResponse {
  rpc.Status status = 1;
  // this variable will be true if the stage is in progress state, false otherwise
  bool has_user_shown_intent = 2;
}

message GetRequestStatusSyncRequest {
  string actor_id = 1;
  string card_request_id = 2;
  string workflow_id = 3;
}

message GetRequestStatusSyncResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
  firefly.enums.CardRequestStatus request_status = 3;
  firefly.enums.CardRequestWorkFlow workflow = 4;
  firefly.CardRequestDetails card_request_details = 5;
}

message UpdateSecuredCardCreditLimitRequest {
  string actor_id = 1;
  google.type.Money credit_limit = 2;
  google.type.Money deposit_amount = 3;
}

message UpdateSecuredCardCreditLimitResponse {
  rpc.Status status = 1;
}

message CollectCommunicationAddressRequest {
  string card_request_id = 1;
  // address type to be saved in card request
  api.typesv2.AddressType address_type = 3;
}

message CollectCommunicationAddressResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}
