// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package pay.payload;

import "api/typesv2/payment_gateway.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/duration.proto";

option go_package = "github.com/epifi/gamma/api/pay/payload";
option java_package = "com.github.epifi.gamma.api.pay.payload";

message CheckPgFundTransferStatusWfPayload {
  // internal order id for which the polling has to start
  string order_id = 1;
  // PG vendor via which the payment is being done
  vendorgateway.Vendor vendor = 2;

  // the program via which the pg payment is being made
  api.typesv2.PaymentGatewayProgram pg_program = 3;

  // Optional: the time duration to wait for receiving signal before polling for fund transfer status.
  // this is useful in avoiding unnecessary polling, in case we know that we will receive the signal
  // within the given duration, and then only start polling for the status. If unset, the workflow starts polling
  // for fund transfer status immediately. The polling logic is still there as a fallback in case the signal is dropped.
  google.protobuf.Duration fundTransferStatusWaitSignalTimeout = 4;
}
