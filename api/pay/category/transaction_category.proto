syntax = "proto3";

package pay.category;

option go_package = "github.com/epifi/gamma/api/pay/category";
option java_package = "com.github.epifi.gamma.api.pay.category";

enum TransactionCategory {
  // unspecified
  TRANSACTION_CATEGORY_UNSPECIFIED = 0;
  // credit interest
  CREDIT_INTEREST = 1;
  // ATM withdrawal
  ATM_WITHDRAWAL = 2;
  // POS txn
  POS = 3;
  // Ecomm txn
  ECOMM = 4;
  // Mobile banking
  MOBILE_BANKING = 5;
  // Deposit add funds debit txn
  DEPOSIT_ADD_FUNDS = 6;
  // Miscellaneous charges
  MISCELLANEOUS_CHARGES = 7;
  // Visa Money Transfer
  VMT = 8;
  // Cash transactions like cash withdrawal/deposit
  CASH = 9;
  // Adjustment for forex transaction
  FOREX_ADJUSTMENT = 10;
  // Interest credited for a deposit account
  // Note: This could represent interest earned for a deposit account but credit to both deposit and savings account.
  DEPOSIT_INTEREST_CREDIT = 11;
  // money debited due to deposit account creation
  DEPOSIT_CREATION = 12;
  // money credited due to pre-mature deposit closure
  DEPOSIT_PRE_CLOSURE = 13;
  // money credited due to deposit has reached it's maturity date
  DEPOSIT_AUTO_CLOSURE = 14;

  // Cash deposit done through ATM
  ATM_CASH_DEPOSIT = 15;
  // EMI related category
  // currently CC is using it for CC EMI related transactions
  EMI = 16;

  // Net Banking
  NET_BANKING = 17;
}
