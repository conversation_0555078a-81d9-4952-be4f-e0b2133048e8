syntax = "proto3";

package api.pay.internationalfundtransfer.file_generator;

import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator";
option java_package = "com.github.epifi.gamma.api.pay.internationalfundtransfer.file_generator";


//go:generate gen_sql -types=FileType,FileGenerationAttemptFieldMask,FileStatus,FileProcessingInfo,LRSFileProcessingInfo,SOFFileProcessingInfo,SwiftTransferFileProcessingInfo,RefundTransferFileProcessingInfo,InwardFundTransferFileProcessingInfo,LRSReportingFileProcessingInfo,TaxTTMFileProcessingInfo,GSTReportingFileProcessingInfo,TCSReportingFileProcessingInfo,FailureReason
message FileGenerationAttempt {
  string id = 1;
  // request id sent by the client for which the file is to be generated
  string client_request_id = 2;
  string file_name = 3;
  FileType file_type = 4;
  FileStatus file_status = 5;
  vendorgateway.Vendor vendor = 6;
  // file type specific processing info kept for each type of file within the system
  // for e.g: for LRS file, we keep the file url that is generated when the vendor downloads the file
  // and the file url of the corresponding file that the vendor uploads.
  // if file generation is in any of the failed state, then we keep the failure reason here.
  FileProcessingInfo file_processing_info = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;

  // Time at which the file was acknowledged by say a partner bank agent
  google.protobuf.Timestamp acknowledged_at = 11;
}

enum FileGenerationAttemptFieldMask {
  FILE_GENERATION_ATTEMPT_FIELD_MASK_UNSPECIFIED = 0;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_ID = 1;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_CLIENT_REQUEST_ID = 2;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_FILE_NAME = 3;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_FILE_TYPE = 4;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_FILE_STATUS = 5;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_VENDOR_NAME = 6;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_FILE_PROCESSING_INFO = 7;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_CREATED_AT = 8;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_UPDATED_AT = 9;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_DELETED_AT = 10;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_ACKNOWLEDGED_AT = 11;
}

enum FileStatus {
  FILE_STATUS_UNSPECIFIED = 0;
  // init state for file creation, when a request for file creation is made, we make an attempt in table corresponding to the client request id with this state
  FILE_STATUS_CREATION_INITIATED = 1;
  // when all the records to be inserted in the file are fetched from respective services and inserted into a file type of structure
  FILE_STATUS_CREATION_SUCCESSFUL = 2;
  // when there is some error while creating the file
  // errors could be:
  // 1. error while fetching data from respective services or dao api calls
  // 2. network error,
  // 3. some bug in code, etc
  FILE_STATUS_CREATION_FAILED = 3;
  // when the upload of the generated file is successful to vendors desired location (e.g: Upload to S3/SFTP was successful)
  FILE_STATUS_UPLOAD_SUCCESSFUL = 4;
  // when there is some error while uploading the file to some location (e.g AWS S3)
  FILE_STATUS_UPLOAD_FAILED = 5;
  // if the file is generated successfully but there are no entries in the file
  FILE_STATUS_UPLOAD_FAILED_EMPTY_FILE = 6;
  // once the file has been processed by the vendor, the vendor can perform various actions for that file
  // e.g: for LRS check file, the vendor will upload a new file which will contain some new entities.
  // we keep the uploaded url for that file within our relation and then mark the corresponding status as 'VENDOR_PROCESSED'
  // this is a terminal state as no further action is to be performed by vendor or the service on that particular file generation attempt.
  FILE_STATUS_VENDOR_PROCESSED = 7;

  // When a file is marked invalid after being generated,
  // which could be because of one or more entities in the file being unwanted
  FILE_STATUS_INVALID = 8;
}

enum FileType {
  FILE_TYPE_UNSPECIFIED = 0;
  FILE_TYPE_LRS_CHECK = 1;
  FILE_TYPE_SOF_CHECK = 2;
  FILE_TYPE_SWIFT_TRANSFER = 3;
  FILE_TYPE_REFUND_TRANSFER = 4;
  // this is a ttum file used for inward fund transfer
  FILE_TYPE_INWARD_FUND_TRANSFER = 5;
  FILE_TYPE_A2_FORM = 6;
  FILE_TYPE_LRS_REPORTING = 7;
  FILE_TYPE_TAX_TTM = 8;
  FILE_TYPE_GST_REPORTING = 9;
  FILE_TYPE_TCS_REPORTING = 10;
  // file to be generated for checking LRS limit of user pro-actively,
  // independent of order placed by order or not
  FILE_TYPE_PRO_ACTIVE_LRS_CHECK = 11;

  // Use MT-199 message attachment instead.
  // for each swift file their is expected to have mt 199 file
  // this file is share with vendor
  // it has 50 row in each file
  // it has file format <mobileNo><Pan><amount>
  // so this has zip url
  FILE_TYPE_MT199 = 12 [deprecated = true];

  FILE_TYPE_GST_REPORTING_INWARD = 13;
  // file to be generated for checking lrs limit of user after usstocks onboarding process completion
  FILE_TYPE_POST_ONBOARDING_LRS_CHECK = 14;

  // File type generated for GST incurred in all outward international fund transfers over a time period
  FILE_TYPE_AGGREGATED_OUTWARD_GST_REPORT = 15;

  // File type generated for TCS incurred in all outward international fund transfers over a time period
  FILE_TYPE_AGGREGATED_OUTWARD_TCS_REPORT = 16;

  // File type generated for GST incurred in all inward international fund transfers over a time period
  FILE_TYPE_AGGREGATED_INWARD_GST_REPORT = 17;

  // Attachment accompanying the MT-199 message
  // The attachment is encrypted (password-protected) and the message contains the password to open the attachment
  // It is used to distribute pooled SWIFT transfer funds among recipients
  FILE_TYPE_MT199_MESSAGE_ATTACHMENT = 18;

  // File type generated for inward fund transfer which includes only gst transactions corresponding to each inward transfer file
  FILE_TYPE_INWARD_GST_TTUM = 19;
}

message FileProcessingInfo {
  // if the file is generated an uploaded successfully, then the generated url path
  // and the corresponding response from vendor for that file entry is recorded on a per file type basis
  oneof info {
    LRSFileProcessingInfo lrs_file_processing_info = 1;
    SOFFileProcessingInfo sof_file_processing_info = 2;
    SwiftTransferFileProcessingInfo swift_transfer_file_processing_info = 3;
    RefundTransferFileProcessingInfo refund_transfer_file_processing_info = 4;
    InwardFundTransferFileProcessingInfo inward_fund_transfer_file_processing_info = 5;
    A2FormFileProcessingInfo a2_form_file_processing_info = 7;
    LRSReportingFileProcessingInfo lrs_reporting_file_processing_info = 8;
    TaxTTMFileProcessingInfo tax_ttm_file_processing_info = 9;
    GSTReportingFileProcessingInfo gst_reporting_file_processing_info = 10;
    TCSReportingFileProcessingInfo tcs_reporting_file_processing_info = 11;
    MT199FileProcessingInfo mt199_file_processing_info = 12;
    GSTReportingInwardFileProcessingInfo gst_reporting_inward_file_processing_info = 13;
    AggregatedTaxReportingFileProcessingInfo aggregated_tax_reporting_file_processing_info = 14;
    Mt199MessageAttachment mt199_message_attachment = 15;
  }
  // if there was a failure at any stage in file generation, we record the reason in FailureReason message.
  FailureReason failure_reason = 6;
}

message LRSFileProcessingInfo {
  // s3 file url generated by the service when requested by vendor
  string generated_file_url = 1;
  // corresponding processed file url uploaded by client for the generated file
  string uploaded_file_url = 2;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 3;
  // corresponding processed file url uploaded by client for the generated file
  string uploaded_file_path = 4;
  // number of pans in the generated file
  int64 number_of_pans = 5;
  google.protobuf.Timestamp lrs_check_result_processed_at = 6;
}

message MT199FileProcessingInfo {
  // s3 file url generated by the service when requested by vendor
  string generated_file_url = 1;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 2;
  // number of entries in the generated file
  int64 number_of_entries = 3;
}

message Mt199MessageAttachment {
  // Path where the generated file is stored on AWS S3
  string generated_file_path = 1;

  // URL to enable partner bank agents to access the generated file
  // Note: The file URL isn't persisted. It is dynamically generated from file-path whenever agent requests
  string generated_file_url = 2;

  // number of user transactions part of the pooled SWIFT transfer
  int32 num_of_transactions = 3;

  // Total USD amount transferred via SWIFT
  google.type.Money total_amount_in_usd = 4;

  // Password to open the attachment
  string password = 5;
}

message SOFFileProcessingInfo {
  // s3 file url generated by the service when requested by vendor
  string generated_file_url = 1;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 2;
  // number of entries in the generated file
  int64 number_of_entries = 3;
}

message SwiftTransferFileProcessingInfo {
  // s3 file url generated by the service when requested by vendor
  string generated_file_url = 1;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 2;
  // number of transactions in the generated file
  int64 number_of_transactions = 3;
  // swift transaction id after file processing by vendor is complete
  string swift_transaction_id = 4;
  // mt199 file zip url
  string zip_url_mt199_files = 5;
  // forex rate applicable for all the transactions in the swift transfer file.
  google.type.Money fx_rate = 6;
  // represent forex rate id for file
  string fx_rate_id = 7;
  // represent deal id for given forex rate
  string deal_id = 8;
  // Payment instrument id of international account
  string international_account_pi_id = 9;
  // amount to be transferred in foreign currency eg: USD
  google.type.Money transfer_amount = 10;
  // timestamp at which the swift file was acknowledged by OPs agent
  google.protobuf.Timestamp swift_acknowledged_at = 11;
}

message RefundTransferFileProcessingInfo {
  // s3 file url generated by the service when requested by vendor
  string generated_file_url = 1;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 2;
  // number of transactions in the generated file
  int64 number_of_transactions = 3;
}

message InwardFundTransferFileProcessingInfo {
  // s3 file url generated by the service when requested by vendor
  string generated_file_url = 1;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 2;
  // number of transactions in the generated file
  int64 number_of_transactions = 3;
  // swift transaction id after file processing by vendor is complete
  string swift_transaction_id = 4;
  // forex rate at which inward fund transfer was processed
  google.type.Money forex_rate = 5;
  // represent total amount being utilized during this file
  // it represent amount in USD
  google.type.Money total_amount = 6;
  // represents the date for which Remittance ops needs to be done.
  // For a date D, to generate the TTUM file for Alpaca,
  // we need to look at settlement file for D-3 (for sell orders) and D-2(for dividends).
  google.protobuf.Timestamp ops_date = 7;
  // represent process id for inward remittance which help in initiating swift transfer
  string batch_id = 8;
}

message A2FormFileProcessingInfo {
  // s3 file url generated by the service when requested by internationalfundtransfer svc
  string generated_file_url = 1;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 2;
}

message LRSReportingFileProcessingInfo {
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 1;
  // s3 file url - valid only for specific time generated using the above path
  string generated_file_url = 2;
}

message TaxTTMFileProcessingInfo {
  // s3 file url - valid only for specific time generated using the above path
  string generated_file_url = 1;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 2;
  // total tax amount sent in this file
  google.type.Money tax_amount = 3;
}

message GSTReportingFileProcessingInfo {
  // s3 file url - valid only for specific time generated using the above path
  string generated_file_url = 1;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 2;
}

message TCSReportingFileProcessingInfo {
  // s3 file url - valid only for specific time generated using the above path
  string generated_file_url = 1;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 2;
}

message GSTReportingInwardFileProcessingInfo {
  // s3 file url - valid only for specific time generated using the above path
  string generated_file_url = 1;
  // s3 path generated by the service when requested by vendor
  string generated_file_path = 2;
}

message AggregatedTaxReportingFileProcessingInfo {
  // URL encoded S3 path of the file generated with prefixes for partner bank agents
  // to be able to access it via Sherlock (Web UI)
  string generated_file_url = 1;

  // (Raw) S3 path of the file generated
  string generated_file_path = 2;
}

message FailureReason {
  string failure_message = 1;
}
