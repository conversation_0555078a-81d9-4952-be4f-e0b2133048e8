syntax = "proto3";

package api.pay.internationalfundtransfer.file_generator;

import "api/celestial/workflow/client.proto";
import "api/pay/internationalfundtransfer/file_generator/file_generation_attempt.proto";
import "api/pay/internationalfundtransfer/file_generator/lrs_limits.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator";
option java_package = "com.github.epifi.gamma.api.pay.internationalfundtransfer.file_generator";


//  File Generator Service is used for generating files and uploading them to an S3 bucket which needs to be sent to
//  respective vendors to enable international fund transfer.
service FileGenerator {

  //  GenerateLRSCheckFile rpc would take in a vendor and client request id and would generate an lrs check file with all the PAN ids
  //  whose LRS is to be fetched from vendor in the format that is expected by the input vendor
  //  and would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
  //  it will update the corresponding state for the file entry in file generation attempt relation.
  //  if the client request id is the same as that of a previous client request id and there is already a generated file
  //  for that client request id, it will send the previously generated url, or else will generate a new file
  rpc GenerateLRSCheckFile (GenerateLRSCheckFileRequest) returns (GenerateLRSCheckFileResponse) {}

  //  GenerateSOFCheckFile rpc would take in a vendor and client request id and would generate an sof check file with all the transaction details
  //  from whom SOF is to be verified by the vendor in the format that is expected by the input vendor
  //  it would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
  //  it will update the corresponding state for the file entry in file generation attempt relation.
  //  if the client request id is the same as that of a previous client request id and there is already a generated file
  //  for that client request id, it will send the previously generated url, or else will generate a new file
  rpc GenerateSOFCheckFile (GenerateSOFCheckFileRequest) returns (GenerateSOFCheckFileResponse) {}

  //  GenerateSwiftTransferFile rpc would take in a vendor and client request id and would generate a swift transfer file with all the transaction details
  //  from whom swift transaction is to be made by the vendor in the format that is expected by the input vendor
  //  it would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
  //  it will update the corresponding state for the file entry in file generation attempt relation.
  //  if the client request id is the same as that of a previous client request id and there is already a generated file
  //  for that client request id, it will send the previously generated url, or else will generate a new file
  rpc GenerateSwiftTransferFile (GenerateSwiftTransferFileRequest) returns (GenerateSwiftTransferFileResponse) {}

  //  GenerateRefundTransferFile rpc would take in a vendor name and client request id and would generate a refund transfer file for users whom a refund
  //  is to be initiated by the vendor in the format that is expected by the input vendor and would upload the file to a pre-configured
  //  s3 bucket. After the file gets uploaded to s3 bucket, it will update the corresponding state for the file entry in file generation attempt relation.
  //  if the client request id is the same as that of a previous client request id and there is already a generated file
  //  for that client request id, it will send the previously generated url, or else will generate a new file
  rpc GenerateRefundTransferFile (GenerateRefundTransferFileRequest) returns (GenerateRefundTransferFileResponse) {}

  //  GenerateInwardFundTransferFile rpc would take in a vendor name and client request id and would generate an Inward Remittance transfer file
  //  for all the transactions for whom money is to be remitted from  from vendor in the format that is expected by the input vendor
  //  it would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
  //  it will update the corresponding state for the file entry in file generation attempt relation
  //  if the client request id is the same as that of a previous client request id and there is already a generated file
  //  for that client request id, it will send the previously generated url, or else will generate a new file
  rpc GenerateInwardFundTransferFile (GenerateInwardFundTransferFileRequest) returns (GenerateInwardFundTransferFileResponse) {}

  //  GenerateA2Form rpc would take in vendor name, client request id and A2 form generation data and would generate an A2 form respective to that vendor
  //  using the doc service with proper doc template and data passed to the rpc. Doc service will generate a pdf file from the data and then upload it to their
  //  pre-configured bucket whose presigned s3 url will be made available to the caller api for certain time.
  //  this rpc would also read the file from the pre-signed url and store the file within respective bucket of ownership
  rpc GenerateA2Form (GenerateA2FormRequest) returns (GenerateA2FormResponse) {}

  //  UploadFile rpc will take in a particular file type and the file contents as input along with the vendor and upload the file to the desired location as per the file type
  //  It will return the file path url to the caller api
  rpc UploadFile (UploadFileRequest) returns (UploadFileResponse) {}

  //  GetFileGenerationAttempt fetches the file generation attempt for the identifier passed. returns error if no record is found
  rpc GetFileGenerationAttempt (GetFileGenerationAttemptRequest) returns (GetFileGenerationAttemptResponse) {}

  //  GetFileGenerationAttempts fetches the file generation attempts for the file type defined in the request
  rpc GetFileGenerationAttempts (GetFileGenerationAttemptsRequest) returns (GetFileGenerationAttemptsResponse) {}

  //  AcknowledgeFileGenerationAttempt acknowledges the file generation attempt for the given client request id and updates the
  //  corresponding file generation attempt with data relevant to the file type
  rpc AcknowledgeFileGenerationAttempt (AcknowledgeFileGenerationAttemptRequest) returns (AcknowledgeFileGenerationAttemptResponse) {}

  //  GenerateLRSReportingFile rpc will generate an overall report file which contains all the information corresponding to PAN ids for whom LRS is verified
  //  e.g: Some sample data points that we need to send in this file are: pan id, name of the remitter, date of remittance, purpose code, amount in usd, etc
  //  rpc would upload the file to a pre-configured s3 bucket. After the file gets uploaded to s3 bucket,
  //  also will update the corresponding state for the file entry in file generation attempt relation
  rpc GenerateLRSReportingFile (GenerateLRSReportingFileRequest) returns (GenerateLRSReportingFileResponse) {}

  //  GenerateTaxTTMFile rcp will generate an overall TAX TTM file which needs to be transferred from pool account to federal's tax accounts
  //  When swift transfer happens, we send the buy amount from pool account to the vendor(e.g alpaca) and the taxes are settled from the customer to the
  //  banking vendor(e.g federal) using this TAX TTM file
  rpc GenerateTaxTTMFile (GenerateTaxTTMFileRequest) returns (GenerateTaxTTMFileResponse) {}

  //  GenerateGSTReportingFile rpc will generate overall GST reporting file for the successful swift transactions within the system
  rpc GenerateGSTReportingFile (GenerateGSTReportingFileRequest) returns (GenerateGSTReportingFileResponse) {}

  //  GenerateTCSReportingFile rpc will generate overall TCS reporting file for the successful swift transactions within the system
  rpc GenerateTCSReportingFile (GenerateTCSReportingFileRequest) returns (GenerateTCSReportingFileResponse) {}

  //  GenerateGSTReportingInwardFile rpc will generate overall GST reporting inward file for the successful swift transactions within the system
  rpc GenerateGSTReportingInwardFile (GenerateGSTReportingInwardFileRequest) returns (GenerateGSTReportingInwardFileResponse) {}

  // PreCheckForSwiftFileGeneration RPC is used to check if a SWIFT transfer file is ready to be created or not.
  // A SWIFT transfer file is ready to be generated when:
  // 1. all transactions for all the actors in the incoming LRS response file are processed, and
  // the corresponding workflows have processed those signals
  // 2. all non-rejected transactions in a previously generated SWIFT transfer file have been reset to their previous
  // workflow states
  // This RPC is used to allow/prevent partner bank agents from generating SWIFT transfer file via CX/Sherlock web portal.
  rpc PreCheckForSwiftFileGeneration (PreCheckForSwiftFileGenerationRequest) returns (PreCheckForSwiftFileGenerationResponse) {}

  // RPC to generate GST & TCS reports for all international fund transfers over a time period
  rpc GenerateAggregatedTaxReport (GenerateAggregatedTaxReportRequest) returns (GenerateAggregatedTaxReportResponse);

  // UpdateFileGenerationAttempt RPC updates the file generation attempt details for given field_masks value
  // Usecase is need to update file status during dropping account from existing inward ttum file, and marking it as a invalid file
  rpc UpdateFileGenerationAttempt (UpdateFileGenerationAttemptRequest) returns (UpdateFileGenerationAttemptResponse);

  // GenerateMT199Attachment creates a file containing the list of users (along with other relevant details)
  // whose money is being remitted in the pooled SWIFT transfer
  // The file is encrypted and can be opened using the password in the MT-199 message
  // Once opened, the file can be used to distribute the pooled SWIFT transfer amount among the recipient users
  rpc GenerateMt199MessageAttachment (GenerateMt199MessageAttachmentRequest) returns (GenerateMt199MessageAttachmentResponse);

  // SoftDeleteFileEntityMapping RPC deletes the file entity mapping records for the given file generation attempt
  rpc SoftDeleteFileEntityMapping (SoftDeleteFileEntityMappingRequest) returns (SoftDeleteFileEntityMappingResponse);

  rpc GetParsedFileContents (GetParsedFileContentsRequest) returns (GetParsedFileContentsResponse);
}

message GetParsedFileContentsRequest {
  oneof identifier {
    string client_request_id = 1;
  }
  // Each file generation attempt can store multiple files. Eg: 2 LRS files are stored against the same file generation attempt
  // This option helps with selection of one of them for the same file generationa attempt
  enum FileOption {
    FILE_OPTION_UNSPECIFIED = 0;
    FILE_OPTION_UPLOADED_LRS_FILE = 1;
  }

  FileOption file_option = 2;
}

message GetParsedFileContentsResponse {
  rpc.Status status = 1;
  oneof contents {
    LRSLimitList lrs_limit_list = 2;
  }
}

message UpdateFileGenerationAttemptRequest {
  // id should be present in FileGenerationAttempt
  FileGenerationAttempt file_generation_attempt = 1;
  repeated FileGenerationAttemptFieldMask field_masks = 2;
}

message UpdateFileGenerationAttemptResponse {
  rpc.Status status = 1;
}

message SoftDeleteFileEntityMappingRequest {
  string file_gen_attempt_id = 1;
}

message SoftDeleteFileEntityMappingResponse {
  rpc.Status status = 1;
}

message PreCheckForSwiftFileGenerationRequest {
}

message PreCheckForSwiftFileGenerationResponse {
  rpc.Status status = 1;
  bool avalible_to_generate = 2;
}

message GenerateLRSCheckFileRequest {
  vendorgateway.Vendor vendor = 1;
  string client_request_id = 2;
  // the max number of pan ids to be filled within the file
  // decided by the vendor
  int32 limit = 3;
  // there can be two file types for check LRS limit
  // LRS_LIMIT is used to perform LRS check for users who have placed buy orders
  // PRO_ACTIVE_LRS_CHECK is used to perform pro-active LRS check
  // It could be periodic LRS check, user's interest in the feature etc
  FileType file_type = 4 [(validate.rules).enum = {in: [1, 11, 14]}];
}

message GenerateLRSCheckFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  // updated file generated attempt with generated url
  FileGenerationAttempt file_generation_attempt = 2;
}

message GenerateSOFCheckFileRequest {
  vendorgateway.Vendor vendor = 1;
  string client_request_id = 2;
  // the max number of sof transactions to be filled within the file
  // decided by the vendor
  int32 limit = 3;
}

message GenerateSOFCheckFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // updated file generated attempt with generated url
  FileGenerationAttempt file_generation_attempt = 2;
}

message GenerateSwiftTransferFileRequest {
  vendorgateway.Vendor vendor = 1;
  string client_request_id = 2;
}

message GenerateSwiftTransferFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // updated file generated attempt with generated url
  FileGenerationAttempt file_generation_attempt = 2;
}

message GenerateRefundTransferFileRequest {
  vendorgateway.Vendor vendor = 1;
  string client_request_id = 2;
}

message GenerateRefundTransferFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // updated file generated attempt with generated url
  FileGenerationAttempt file_generation_attempt = 2;
}

message GenerateInwardFundTransferFileRequest {
  vendorgateway.Vendor vendor = 1;
  string client_request_id = 2;
  // represent total amount being utilized during this file
  // it represent amount in USD
  google.type.Money total_amount = 3;
  // represents the date for which Remittance ops needs to be done.
  // For a date D, to generate the TTUM file for Alpaca, we need to look at settlement file for D-3 (for sell orders) and D-2(for dividends).
  google.protobuf.Timestamp ops_date = 4;
  // represent batch id (process id) for inward remittance corresponding to file generation attempt
  string batch_id = 5;
  // file type of inward fund transfer file, this can be either 'FILE_TYPE_INWARD_FUND_TRANSFER' or 'FILE_TYPE_INWARD_GST_TTUM'
  FileType file_type = 6;
}

message GenerateInwardFundTransferFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // updated file generated attempt with generated url
  FileGenerationAttempt file_generation_attempt = 2;
}

message UploadFileRequest {
  vendorgateway.Vendor vendor = 1;
  // the file generation attempt corresponding to which the file is to be uploaded
  FileGenerationAttempt file_generation_attempt = 2;
  // file content of the file to be uploaded
  bytes file_contents = 3;
  // represent total number of entries in file
  int32 total_entries = 4;

  // Entities used when generating inward fund transfer file contents
  // For sell-orders, these are IDs of the US stock sell orders
  // For dividends, these are IDs of account activities
  // These entity IDs identify the contents of a file uniquely from another file of the same type
  repeated string entity_ids = 5;
}

message UploadFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // updated file generated attempt with uploaded url
  FileGenerationAttempt file_generation_attempt = 2;
}

message GetFileGenerationAttemptsRequest {
  rpc.PageContextRequest page_context = 1;
  // optional: the type of file for which the file generation attempts are to be fetched
  // note: either the file type or client request ids, not both, must be present to fetch file generation attempts
  FileType file_type = 2;
  // optional: file request ids to be fetched
  repeated string client_request_ids = 3;
  // Files created on or after start time are returned if this is populated
  google.protobuf.Timestamp start_time = 4;
  // Files created on or before end time are returned if this is populated
  google.protobuf.Timestamp end_time = 5;
  // returns list of file in given statuses
  repeated FileStatus file_status = 6;
}

message GetFileGenerationAttemptsResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // list of file generation attempts
  repeated FileGenerationAttempt file_generation_attempts = 2;
  // page size if fixed for now just pass the page tokens for now
  rpc.PageContextResponse page_context = 3;
}

message AcknowledgeFileGenerationAttemptRequest {
  FileGenerationAttempt fga_entry = 1;
}

message AcknowledgeFileGenerationAttemptResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
}

message GenerateA2FormRequest {
  // the vendor for which A2 form is to be generated
  vendorgateway.Vendor vendor = 1;
  // client request id for which the A2 form is to be generated
  string client_request_id = 2;
}

message GenerateA2FormResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // file generation attempt for the generated file
  FileGenerationAttempt file_generation_attempt = 2;
}

message GenerateLRSReportingFileRequest {
  // the vendor for which LRS reporting file is to be generated
  vendorgateway.Vendor vendor = 1;
  // client request id for which the LRS reporting file is to be generated
  string client_request_id = 2;
  // represent the list of IFT client reqid for which file needed to be generated
  repeated celestial.workflow.ClientReqId ift_client_req_ids = 3;
}

message GenerateLRSReportingFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // file generation attempt for the generated file
  FileGenerationAttempt file_generation_attempt = 2;
  // list of IFT workflow client request IDs that were included in the file generated using v1 file generation flow
  repeated celestial.workflow.ClientReqId used_ift_wf_client_req_ids = 3;
}

message GenerateTaxTTMFileRequest {
  // the vendor for which Tax TTM File is to be generated
  vendorgateway.Vendor vendor = 1;
  // client request id for which Tax TTM File is to be generated
  string client_request_id = 2;

  // represent the list of IFT client reqid for which file needed to be generated
  repeated celestial.workflow.ClientReqId ift_client_req_ids = 3;
}

message GenerateTaxTTMFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  // file generation attempt for the generated file
  FileGenerationAttempt file_generation_attempt = 2;
  // list of IFT workflow client request IDs that were included in the file generated using v1 file generation flow
  repeated celestial.workflow.ClientReqId used_ift_wf_client_req_ids = 3;
}

message GenerateGSTReportingFileRequest {
  // the vendor for which GST Reporting File is to be generated
  vendorgateway.Vendor vendor = 1;
  // client request id for which GST Reporting File is to be generated
  string client_request_id = 2;
  // represent the list of IFT client reqid for which file needed to be generated
  repeated celestial.workflow.ClientReqId ift_client_req_ids = 3;
}

message GenerateGSTReportingFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // file generation attempt for the generated file
  FileGenerationAttempt file_generation_attempt = 2;
  // list of IFT workflow client request IDs that were included in the file generated using v1 file generation flow
  repeated celestial.workflow.ClientReqId used_ift_wf_client_req_ids = 3;
}

message GenerateTCSReportingFileRequest {
  // the vendor for which TCS Reporting File is to be generated
  vendorgateway.Vendor vendor = 1;
  // client request id for which TCS Reporting File is to be generated
  string client_request_id = 2;
  // represent the list of IFT client reqid for which file needed to be generated
  repeated celestial.workflow.ClientReqId ift_client_req_ids = 3;

}

message GenerateTCSReportingFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // file generation attempt for the generated file
  FileGenerationAttempt file_generation_attempt = 2;
  // list of IFT workflow client request IDs that were included in the file generated using v1 file generation flow
  repeated celestial.workflow.ClientReqId used_ift_wf_client_req_ids = 3;
}

message GetFileGenerationAttemptRequest {
  oneof identifier {
    // file generation request identifier
    string id = 1;
    // file generation request client request identifier
    string client_request_id = 2;
  }
  // pass this flag as true if s3 url is to be generated for the file generation attempt
  bool is_s3_url_required = 3;
}

message GetFileGenerationAttemptResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  // file generation attempt for the requested identifier
  FileGenerationAttempt file_generation_attempt = 2;
}

message GenerateGSTReportingInwardFileRequest {
  // the vendor for which GST Reporting File is to be generated
  vendorgateway.Vendor vendor = 1;
  // client request id for which GST Reporting File is to be generated
  string client_request_id = 2;
  // represent the list of IFT client reqid for which file needed to be generated
  repeated celestial.workflow.ClientReqId ift_client_req_ids = 3;
}

message GenerateGSTReportingInwardFileResponse {
  // rpc response status
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // file generation attempt for the generated file
  FileGenerationAttempt file_generation_attempt = 2;
}

message GenerateAggregatedTaxReportRequest {
  FileType file_type = 1 [(validate.rules).enum = {in: [15, 16, 17]}];

  google.protobuf.Timestamp start_ts = 2;

  google.protobuf.Timestamp end_ts = 3;
}

message GenerateAggregatedTaxReportResponse {
  rpc.Status status = 1;

  // file generation attempt for the generated file
  FileGenerationAttempt file_generation_attempt = 2;
}

message GenerateMt199MessageAttachmentRequest {
  // ID of an initiated SWIFT transaction provided by partner bank
  string swift_transaction_id = 1;

  // IDs of IFT orders to be included in the MT-199 message attachment
  repeated string order_client_req_ids = 2;
}

message GenerateMt199MessageAttachmentResponse {
  rpc.Status status = 1;

  // ID of a successful MT-199 message attachment generation attempt
  string file_generation_attempt_id = 2;
}
