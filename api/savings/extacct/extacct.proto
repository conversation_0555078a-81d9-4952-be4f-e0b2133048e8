//go:generate gen_sql -types=OverallStatus,FailureReason,BankAccountVerificationFieldMask,Source,Vendor,NameMatchData,Caller
// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package savings.extacct;

import "api/kyc/internal/kyc_attempt.proto";
import "api/typesv2/common/name.proto";
import "api/vendorgateway/vendor_status.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/savings/extacct";
option java_package = "com.github.epifi.gamma.api.savings.extacct";

enum OverallStatus {
  OVERALL_STATUS_UNSPECIFIED = 0;
  OVERALL_STATUS_IN_PROGRESS = 1;
  OVERALL_STATUS_FAILURE = 2;
  OVERALL_STATUS_SUCCESS = 3;
}

// FailureReason enum describes why the bank account verification failed.
// It's derived from vendor API response & other checks.
enum FailureReason {
  FAILURE_REASON_UNSPECIFIED = 0;
  FAILURE_REASON_API_TIMEOUT = 1;
  FAILURE_REASON_USER_GIVEN_NAME_MISMATCH = 2;
  FAILURE_REASON_NAME_AT_BANK_MISMATCH = 3;
  FAILURE_REASON_INVALID_IFSC = 4;
  FAILURE_REASON_INVALID_ACCOUNT_NUMBER = 5;
  FAILURE_REASON_ACCOUNT_CLOSED = 6;
  FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK = 7;
  // Unknown vendor response, usually Invalid ID Number or Combination of Inputs from Karza
  FAILURE_REASON_UNKNOWN = 8;
  // user entered account number is same as of fi account number
  FAILURE_REASON_SAME_ACCOUNT_NUMBER = 9;
  // used when vendor is federal. this means penny drop payment to input account Number + Ifsc failed
  FAILURE_REASON_PAYMENT_FAILED = 10;
}

// Field mask specifies which fields need to be considered for a particular operation.
// For example, when updating name match data.
enum BankAccountVerificationFieldMask {
  FIELD_MASK_UNSPECIFIED = 0;
  FIELD_MASK_ACCOUNT_NUMBER = 1;
  FIELD_MASK_IFSC = 2;
  FIELD_MASK_NAME_AT_BANK = 3;
  FIELD_MASK_OVERALL_STATUS = 4;
  FIELD_MASK_VENDOR_STATUS = 5;
  FIELD_MASK_VENDOR_REQ_ID = 6;
  FIELD_MASK_VENDOR = 7;
  FIELD_MASK_FAILURE_REASON = 8;
  FIELD_MASK_USER_GIVEN_NAME_MATCH_SCORE = 9;
  FIELD_MASK_NAME_AT_BANK_MATCH_SCORE = 10;
  FIELD_MASK_KYC_NAME = 11;
  FIELD_MASK_DELETED_AT_UNIX = 12;
}

// Source defines from where the bank account verification was initiated
enum Source {
  SOURCE_UNSPECIFIED = 0;
  // penny drop verification triggered by user in-app
  SOURCE_USER = 1;
  // penny drop verification triggered from sherlock
  SOURCE_SHERLOCK = 2;
  // manually verified accounts that we share to federal (and they send back UTR after successful transaction)
  // are stored when dataops agent uploads csv containing successful balance transfer UTRs in sherlock
  SOURCE_FEDERAL_UTR_SHEET = 3;
  // penny drop initiated by user via web form
  SOURCE_WEB = 4;
}

// Vendor used to perform account verification
enum Vendor {
  VENDOR_UNSPECIFIED = 0;
  KARZA = 9;
  FEDERAL_BANK = 10;
}

// BankAccountVerification contains all the information pertaining to
// verifying a users bank account
message BankAccountVerification {
  string id = 1;
  string actor_id = 2;

  string account_number = 3;
  string ifsc = 4;
  string name_at_bank = 5;

  OverallStatus overall_status = 6;
  // contains vendor rpc status and response description
  vendorgateway.VendorStatus vendor_status = 7;
  string vendor_req_id = 8;

  // name of vendor used to validate bank account
  Vendor vendor = 9;
  FailureReason failure_reason = 10;

  NameMatchData name_match_data = 11;
  Caller caller = 12;

  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  int64 deleted_at_unix = 15;
}

// NameMatchData contains the names and scores of
// name matches performed to verify a users authenticity
// with the kyc name of the actor at Fi
message NameMatchData {
  api.typesv2.common.Name kyc_name = 1;
  string user_given_name = 2;
  kyc.NameMatchScore user_given_name_match_score = 3;
  string name_at_bank = 4;
  kyc.NameMatchScore name_at_bank_match_score = 5;
}

// Caller contains details of the person
// initiating the bank account verification
message Caller {
  // source enum tells the person initiating the bank account validation
  Source source = 1;
  // email is needed if sherlock agent is the source
  string email = 2;
}
