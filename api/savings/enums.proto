//go:generate gen_sql -types=SAClosureRequestStatus,SAClosureRequestStatusReason,SAClosureRequestEntryPoint
syntax = "proto3";

package savings;

option go_package = "github.com/epifi/gamma/api/savings";
option java_package = "com.github.epifi.gamma.api.savings";

// AccessLevel tells about the savings account's access level
// In case it is partial, restrictions will give information on
// what features are unavailable
enum AccessLevel {
  ACCESS_LEVEL_UNSCPECIFIED = 0;
  ACCESS_LEVEL_FULL_ACCESS = 1;
  // if there are any restrictions imposed on the account, the
  // access level has to be partial and vice-versa
  ACCESS_LEVEL_PARTIAL_ACCESS = 2;
  // no access denotes that the account is closed or temporarily blocked
  ACCESS_LEVEL_NO_ACCESS = 3;
}

// Restriction is an activity that is not-allowed on the account
enum Restriction {
  RESTRICTION_UNSCPECIFIED = 0;
  RESTRICTION_CREDIT_FREEZE = 1;
  RESTRICTION_DEBIT_FREEZE = 2;
}

// ConstraintsUpdateReason tells why the account constraints were updated
enum ConstraintsUpdateReason {
  CONSTRAINTS_UPDATE_REASON_UNSPECIFIED = 0;
  // denotes user's account is deleted at fi as it is requested by user
  CONSTRAINTS_UPDATE_REASON_ACCOUNT_DELETION_REQUEST = 1;
  // denotes user's account is closed due to min kyc expiry
  CONSTRAINTS_UPDATE_REASON_MIN_KYC_ACCOUNT_EXPIRY = 2;
  // denotes user's account is deleted by fi
  // possible reason could be liveness or facematch issue
  CONSTRAINTS_UPDATE_REASON_FRAUDULENT_ACCOUNT = 3;
  // kyc issues like face match, liveness, name match
  CONSTRAINTS_UPDATE_REASON_CORE_KYC_ISSUE = 4;
  // suspicious email patterns, AA info mismatch, location based issues
  // and also if data analytics has pointed a user as risky
  CONSTRAINTS_UPDATE_REASON_PROFILE_INDICATORS = 5;
  // penny drop abuse as name suggests
  CONSTRAINTS_UPDATE_REASON_PENNY_DROP_ABUSE = 6;
  // received funds from known fraudsters
  CONSTRAINTS_UPDATE_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS = 7;
  // high number of withdrawals from atms
  CONSTRAINTS_UPDATE_REASON_HIGH_ATM_WITHDRAWAL_COUNT = 8;
  // intersection rule in transaction monitoring
  CONSTRAINTS_UPDATE_REASON_TM_ALERT = 9;
  // transactin monitoring profile mismatch
  CONSTRAINTS_UPDATE_REASON_TM_PROFILE_MISMATCH = 10;
  // unblocking after due dilligence
  CONSTRAINTS_UPDATE_REASON_DUE_DILIGENCE = 11;
  // unblocking after customer outcall
  CONSTRAINTS_UPDATE_REASON_CUSTOMER_OUTCALL = 12;
  // For other remarks will contain detailed remark
  CONSTRAINTS_UPDATE_REASON_OTHER = 13;
  // account frozen under LEA
  CONSTRAINTS_UPDATE_REASON_LEA_COMPLAINT = 14;
  // account inquired under LEA
  CONSTRAINTS_UPDATE_REASON_LEA_ENQUIRY = 15;
  // account frozen under NPCI
  CONSTRAINTS_UPDATE_REASON_NPCI_COMPLAINT = 16;
  // account frozen under FEDERAL rules
  CONSTRAINTS_UPDATE_REASON_FEDERAL_RULES = 17;
  // unfreeze reason: LEA
  CONSTRAINTS_UPDATE_REASON_LEA_UNFREEZE = 18;
  // freeze reason : lso user vkyc pending
  CONSTRAINTS_UPDATE_REASON_LSO_USER_VKYC_PENDING = 19;
  // freeze reason: block onboarding (no comms triggered)
  CONSTRAINTS_UPDATE_REASON_BLOCK_ONBOARDING = 20;
  // unblock to provide app access to use cc/pl
  CONSTRAINTS_UPDATE_REASON_CC_OR_PL_USER = 21;
  // freeze reason: users who were part of revkyc queue and did not complete re vkyc
  CONSTRAINTS_UPDATE_REASON_RE_VKYC_NOT_COMPLETED = 22;
}

enum SAFieldName {
  SA_FIELD_NAME_UNSPECIFIED = 0;
  SA_FIELD_NAME_ACCOUNT_NUMBER = 1;
  SA_FIELD_NAME_USER_NAME = 2;
  SA_FIELD_NAME_CUSTOMER_ID = 3;
  SA_FIELD_NAME_SIGNATURE = 4;
}

// SAClosureRequestStatus tells the statuses via which the SA closure request can flow through
// during its lifecycle
enum SAClosureRequestStatus {
  SA_CLOSURE_REQUEST_STATUS_UNSPECIFIED = 0;
  // NonTerminal: closure request is created with this status
  SA_CLOSURE_REQUEST_STATUS_INITIATED = 1;
  // NonTerminal: when feedback/reason for closure of the account is taken from the user
  SA_CLOSURE_REQUEST_STATUS_FEEDBACK_RECEIVED = 2;
  // NonTerminal: when the closure request is submitted successfully by the user
  SA_CLOSURE_REQUEST_STATUS_SUBMITTED = 3;
  // Terminal: when the closure request is cancelled by the user manually after submission
  SA_CLOSURE_REQUEST_STATUS_CANCELLED_MANUALLY = 4;
  // Terminal: when the closure request is cancelled automatically because of criteria failure
  // after the closure request is submitted
  SA_CLOSURE_REQUEST_STATUS_CANCELLED_AUTOMATICALLY = 5;
  // NonTerminal: when the closure request is picked by the ops for operating upon
  SA_CLOSURE_REQUEST_STATUS_ACKNOWLEDGED_BY_OPS = 6;
  // Terminal: when the closure request is successfully processed by the ops, i.e.
  // here it would mean savings account is closed
  SA_CLOSURE_REQUEST_STATUS_COMPLETED_SUCCESSFULLY = 7;
  // Terminal: when the closure request is rejected, i.e.
  // savings account can't be closed for the closure request raised
  SA_CLOSURE_REQUEST_STATUS_REJECTED = 8;
  // Terminal: closure request has expired
  SA_CLOSURE_REQUEST_STATUS_EXPIRED = 9;
  // Terminal: when the closure request is cancelled by the user manually before submission
  SA_CLOSURE_REQUEST_STATUS_CANCELLED_MANUALLY_BEFORE_SUBMISSION = 10;
  // Terminal: when user selects to resolve the issues and continue on fi
  SA_CLOSURE_REQUEST_STATUS_SUPPORT_TICKET_CREATED_AND_CLOSED = 11;
  // Terminal: when freeze/criteria check fails while validating before sending the request to ops
  SA_CLOSURE_REQUEST_STATUS_CANCELLED_ON_VALIDATION = 12;
  // when closure request is sent to ops via mail for further processing
  SA_CLOSURE_REQUEST_STATUS_SENT_TO_OPS = 13;
}

enum SAClosureRequestStatusReason {
  SA_CLOSURE_REQUEST_STATUS_REASON_UNSPECIFIED = 0;
  SA_CLOSURE_REQUEST_STATUS_REASON_OTHERS = 1;
  SA_CLOSURE_REQUEST_STATUS_REASON_HAS_FREEZE = 2;
  SA_CLOSURE_REQUEST_STATUS_REASON_FAILED_CRITERIA_CHECK = 3;
  SA_CLOSURE_REQUEST_STATUS_REASON_HAS_LIEN = 4;
  SA_CLOSURE_REQUEST_STATUS_REASON_HAS_PENDING_CHARGES = 5;
}

enum SAClosureRequestEntryPoint {
  SA_CLOSURE_REQUEST_ENTRY_POINT_UNSPECIFIED = 0;
  // entry into the SA closure flow via chatbot
  SA_CLOSURE_REQUEST_ENTRY_POINT_CHATBOT = 1;
  // entry into the SA closure flow via FAQs
  SA_CLOSURE_REQUEST_ENTRY_POINT_FAQ = 2;
  // entry into the SA closure flow from profile section
  SA_CLOSURE_REQUEST_ENTRY_POINT_PROFILE = 3;
}
