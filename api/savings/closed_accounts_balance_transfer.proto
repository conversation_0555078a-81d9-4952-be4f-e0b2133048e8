syntax = "proto3";

package savings;

import "api/savings/account.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";


option go_package = "github.com/epifi/gamma/api/savings";
option java_package = "com.github.epifi.gamma.api.savings";

// CbtTransactionStatus enum describes if a transaction was successful or failed
enum CbtTransactionStatus {
  CBT_TRANSACTION_STATUS_UNSPECIFIED = 0;
  CBT_TRANSACTION_STATUS_FAILED = 1;
  CBT_TRANSACTION_STATUS_SUCCESS = 2;
}

enum CbtFieldMask {
  CBT_FIELD_MASK_UNSPECIFIED = 0;
  // field mask for last_known_balance, last_known_balance_captured_at
  CBT_FIELD_MASK_LAST_KNOWN_BALANCE = 1;
  // field mask for bav_id
  CBT_FIELD_MASK_BAV_ID = 2;
  // field mask for utr, date_of_transfer and amount_transferred
  CBT_FIELD_MASK_TRANSACTION_DETAILS = 3;
  // field mask for transaction_status, transaction_failure_reason
  CBT_FIELD_MASK_TRANSACTION_STATUS = 4;
  // field mas for reported_closure_balance
  CBT_FIELD_MASK_REPORTED_CLOSURE_BALANCE = 5;
}

message ClosedAccountBalanceTransfer {
  string id = 1;
  // savings_account_id is the foreing key to savings_accounts table
  string savings_account_id = 2;

  BalanceFromPartener last_known_balance = 3;
  google.protobuf.Timestamp last_known_balance_captured_at = 4;
  google.type.Money reported_closure_balance = 14;
  google.type.Money balance_captured_from_statement = 15;

  // bav_id is the foreign key to bank_account_verifications table, which
  // contains information on alternate account (to which money was transferred to)
  string bav_id = 5;
  string utr = 6;
  google.type.Date date_of_transfer = 7;
  google.type.Money amount_transferred = 8;
  CbtTransactionStatus transaction_status = 9;
  string transaction_failure_reason = 10;

  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  int64 deleted_at_unix = 13;
}
