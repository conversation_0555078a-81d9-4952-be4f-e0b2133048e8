// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package savings.developer;

import "api/savings/savings_account_closure_request.proto";
import "api/savings/closed_accounts_balance_transfer.proto";
import "api/savings/extacct/extacct.proto";

option go_package = "github.com/epifi/gamma/api/savings/developer";
option java_package = "com.github.epifi.gamma.api.savings.developer";

enum SavingsEntity {
  SAVINGS_ENTITY_UNSPECIFIED = 0;

  // To fetch savings account by user Id(entity id)
  SAVINGS_ACCOUNT = 1;

  // to fetch user initiated savings account closure requests
  SAVINGS_ACCOUNT_CLOSURE_REQUESTS = 2;

  // to fetch closed account balance transfer records
  CLOSED_ACCOUNT_BALANCE_TRANSFER = 3;

  // to fetch bank account verification records
  BANK_ACCOUNT_VERIFICATIONS = 4;
}

message SavingsAccountClosureRequestsEntityResponse {
  repeated savings.SavingsAccountClosureRequest savings_account_closure_requests = 1;
}

message ClosedAccountBalanceTransferEntityResponse {
  repeated savings.ClosedAccountBalanceTransfer closed_account_balance_transfers = 1;
}

message BankAccountVerificationsEntityResponse {
  repeated savings.extacct.BankAccountVerification bank_account_verifications = 1;
}
