// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package savings;

import "api/dynamic_elements/dynamic_elements.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/savings/account.proto";
import "api/savings/closed_accounts_balance_transfer.proto";
import "api/savings/enums.proto";
import "api/savings/savings_account_closure_request.proto";
import "api/typesv2/account/enums.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";
import "api/typesv2/nominee.proto";

option go_package = "github.com/epifi/gamma/api/savings";
option java_package = "com.github.epifi.gamma.api.savings";

service Savings {
  // Create and Read Operations on savings account entity
  rpc CreateAccount (CreateAccountRequest) returns (CreateAccountResponse) {}

  // GetAccount rpc can be used to fetch the complete savings account entity.
  // Use GetAccount only in the case GetSavingsAccountEssentials does not return relevant data.
  rpc GetAccount (GetAccountRequest) returns (GetAccountResponse) {}

  // RPC to get list of accounts for a given list of identifiers like primary user id.
  // returns : NOT_FOUND status if for given list of identifiers, not a single account is not found, else INTERNAL in other error cases
  // For a given list of identifiers, if some records are not found, the list of records that are found is returned.
  rpc GetAccountsList (GetAccountsListRequest) returns (GetAccountsListResponse) {}

  // RPC to enquire balance corresponding to an account
  // Deprecated in favour of accounts.balance.GetAccountBalance
  rpc GetAccountBalance (GetAccountBalanceRequest) returns (GetAccountBalanceResponse) {
    option deprecated = true;
  }

  rpc UpdateAccount (UpdateAccountRequest) returns (UpdateAccountResponse) {}

  // RPC to enquire opening balance corresponding to account(s) over a specified time time range
  //
  // Opening balance is the balance that an account has at the beginning of an accounting time period
  rpc GetOpeningBalance (GetOpeningBalanceRequest) returns (GetOpeningBalanceResponse) {}

  // GetAccountBalanceWithSummary returns account balance with detailed summary and buckets
  rpc GetAccountBalanceWithSummary (GetAccountBalanceWithSummaryRequest) returns (GetAccountBalanceWithSummaryResponse);

  // GetListOfActiveAccounts returns a list of active savings accounts.
  // The RPC returns the result in following sequence,
  // If only created_before is specified then response are returned in descending order based on creation time.
  // If only created_after is specified then response are returned in ascending order based on creation time.
  // If both created_before and created_after is specified then response are returned in ascending order based on creation time.
  // If non of created_before and created_after is specified then all the accounts returned in descending order based on creation time.
  rpc GetListOfActiveAccounts (GetListOfActiveAccountsRequest) returns (GetListOfActiveAccountsResponse);

  rpc CloseAccount (CloseAccountRequest) returns (CloseAccountResponse);

  rpc ReopenAccount (ReopenAccountRequest) returns (ReopenAccountResponse);

  // RPC to check if a credit transaction is allowed for an account.
  // The RPC can be used to check if the credit transaction for the given amount in the RPC request will breach any of
  // min-kyc limits or not.
  rpc IsTxnAllowed (IsTxnAllowedRequest) returns (IsTxnAllowedResponse);

  // RPC to return account balance with details such as available, ledger and lien balance. It also returns freeze status
  // of the account. Balance details in this api can be stale.
  // To maintain backward compatibility, this API currently invokes both version of balance API (new and old).
  // Old API is given preference until the time we gain decent confidence in the new API.
  // Hence, client should use vendor_api_option to explicitly invoke newer version of balance api call.
  // Deprecated in favour of accounts.balance.GetAccountBalance
  rpc GetAccountBalanceV1 (GetAccountBalanceV1Request) returns (GetAccountBalanceV1Response) {
    option deprecated = true;
  }

  // RPC to update savings balance account if last updated timestamp received in input is after updatedAt
  // timestamp for the account mapped with input actorId, we will sync the balance by calling vendor api
  rpc UpdateBalance (UpdateBalanceRequest) returns (UpdateBalanceResponse);

  // StoreClosedAccountBalTransferData rpc creates entry in closed accounts balance transfer table.
  // It will not create an entry if a row already exists with the same information
  // Returns:
  // Success - data creation was successful
  // InvalidArgument - savings account id is not mentioned
  // AlreadyExists - row with same data already exists
  // Internal - unforeseen error
  rpc StoreClosedAccountBalTransferData (StoreClosedAccountBalTransferDataRequest) returns (StoreClosedAccountBalTransferDataResponse);

  // StoreClosedAccountBalTransferDataFromStatement
  // checks if account is closed, fetches statement for the account during the closure period
  // will create an entry with the balance captured from the statement
  // FailedPrecondition - if savings account mentioned is not closed already
//  Internal - unforeseen error
  rpc StoreClosedAccountBalTransferDataFromStatement (StoreClosedAccountBalTransferDataFromStatementRequest) returns (StoreClosedAccountBalTransferDataFromStatementResponse);

  // GetClosedAccountBalTransferData rpc fetches closed accounts balance transfer data from db.
  // Returns:
  // Success: record(s) found
  // InvalidArgument - savings account id is not mentioned
  // NotFound: no entries found
  // Internal: unforeseen error
  rpc GetClosedAccountBalTransferData (GetClosedAccountBalTransferDataRequest) returns (GetClosedAccountBalTransferDataResponse);

  // FetchDynamicElements rpc returns the dynamic elements response based on screen type and additional inputs
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse);

  // DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse);

  // GetEODSavBalanceHistory RPC to get eod balance history of user from a given start to end date
  // Rpc will support history upto a maximum of 30 days
  // Returns:
  // Success: dates are within expected range and records are found
  // If dates are out of range, max history (last 30 days) is returned
  // Internal: unforeseen error
  rpc GetEODSavBalanceHistory (GetEODSavBalanceHistoryRequest) returns (GetEODSavBalanceHistoryResponse);

  // UpdateClosedAccountBalTransferData rpc updates closed accounts balance transfer data in db.
  // Returns:
  // Success: record was updated
  // InvalidArgument - cbt id is not mentioned or if field mask is empty
  // Internal: unforeseen error
  rpc UpdateClosedAccountBalTransferData (UpdateClosedAccountBalTransferDataRequest) returns (UpdateClosedAccountBalTransferDataResponse);

  // FetchOrCreateSignAttempt checks if bank has signature available or not
  // if not it creates a sign attempt and return sign url
  // it also ensure only one attempt is in progress at a account level
  rpc FetchOrCreateSignAttempt (FetchOrCreateSignAttemptRequest) returns (FetchOrCreateSignAttemptResponse);

  // GetSavingsAccountEssentials rpc returns frequently accessed fields from savings account entity. The data would be
  // cached to facilitate higher QPS and performance. For fetching data not returned by this RPC, use GetAccount instead.
  rpc GetSavingsAccountEssentials (GetSavingsAccountEssentialsRequest) returns (GetSavingsAccountEssentialsResponse);

  // CreateOrGetSaClosureRequest rpc
  // - returns closure request if one is already created for the user
  // - creates new Closure Request if not created and returns the new request
  // - invalidates non-submitted stale request, creates new request and returns the new request
  // there should be only a single closure request in non-terminal state
  rpc CreateOrGetSaClosureRequest (CreateOrGetSaClosureRequestRequest) returns (CreateOrGetSaClosureRequestResponse);

  // returns the closure request for the actor
  // returns RecordNotFound if no closure request is found
  // returns Internal if user has multiple closure requests or other server errors
  rpc GetActiveSaClosureRequestForUser (GetActiveSaClosureRequestForUserRequest) returns (GetActiveSaClosureRequestForUserResponse);

  // UpdateSaClosureRequestStatus rpc updates Status of closure request on some validation
  rpc UpdateSaClosureRequestStatus (UpdateSaClosureRequestStatusRequest) returns (UpdateSaClosureRequestStatusResponse);

  // records feedback entered by user
  // overrides feedback if there is any feedback stored for the closure request already
  rpc RecordSaClosureUserFeedback (RecordSaClosureUserFeedbackRequest) returns (RecordSaClosureUserFeedbackResponse);

  // returns sa closure requests that become eligible to be sent for closure to federal between given timestamps
  // requests that are submitted T-10 days (config driven) are eligible to be processed for closure
  // data is fetched in paginated manner
  rpc GetSubmittedSaClosureRequests (GetSubmittedSaClosureRequestsRequest) returns (GetSubmittedSaClosureRequestsResponse);

  // paginated rpc to return all sa closure requests filtered by the conditions
  // empty list for a filter applies no filter on the field
  rpc GetSaClosureRequestsByFilter (GetSaClosureRequestsByFilterRequest) returns (GetSaClosureRequestsByFilterResponse);

  // VerifyPanForAccountClosure rpc checks if given pan matches with the user's pan to validate user during account closure process.
  rpc VerifyPanForAccountClosure (VerifyPanForAccountClosureRequest) returns (VerifyPanForAccountClosureResponse);
  // GetSavingsAccountNominees rpc returns nominees for an actor's savings account.
  rpc GetSavingsAccountNominees (GetSavingsAccountNomineesRequest) returns (GetSavingsAccountNomineesResponse);

  rpc UpdateSavingsAccountNominees (UpdateSavingsAccountNomineesRequest) returns (UpdateSavingsAccountNomineesResponse);
}

message UpdateSavingsAccountNomineesRequest {
  // actor id for whom nominee needs to be updated
  string actor_id = 1;

  // nominee details
  NomineeDetails nominee_details = 2;
}

message UpdateSavingsAccountNomineesResponse {
  rpc.Status status = 1;
}

message GetSavingsAccountNomineesRequest {
  // Savings account id for which nominees are to be fetched
  string actor_id = 1;
}

message GetSavingsAccountNomineesResponse {
  // rpc status
  rpc.Status status = 1;
  // nominee details
  repeated api.typesv2.Nominee nominee = 2;
}


message UpdateBalanceRequest {
  // actor corresponding to whom, saving account needs to be updated
  string actor_id = 1;

  // timestamp at which savings account was updated the last time
  google.protobuf.Timestamp prev_updated_at = 2;
}

message UpdateBalanceResponse {
  enum Status {
    OK = 0;
    // Account data corresponding to the given identifier not found
    NOT_FOUND = 5;
    // Internal server
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}
message GetAccountBalanceV1Request {
  oneof identifier {
    // Internal account id.
    string id = 1;

    // External account id, contains identifier provided by banking partner.
    BankAccountIdentifier external_id = 2;
  }
  // actor to whom the account belongs to.
  string actor_id = 3;

  enum DataFreshness {
    DATA_FRESHNESS_UNSPECIFIED = 0;
    // Data has to be real time
    // Using this option will lead to higher latency as this usually means we hit
    // partner banks to fetch real time data
    REAL_TIME = 1;
    // Data can be stale by a delay of 20 seconds
    // This option is a hybrid between `REAL_TIME` and `STALE` and offers a balance of latency and data freshness
    // Recommendation: Should work for most business use cases unless it blocks user facing application
    // e.g., Fund transfer
    NEAR_REAL_TIME = 2;
    // Data is expected to be stale by up to 60 seconds
    // Recommendation: Use for analytical purposes
    STALE = 3;
    // Data is expected to be stale by 60 seconds and more.
    // The balance data if older than 60s is attempted to be refreshed from vendor.
    // In case, the refresh fails historical data that is present in the DB cache is returned.
    HISTORICAL = 4;
    // LAST_KNOWN_BALANCE_FROM_DB will return balance which is store at DB.
    LAST_KNOWN_BALANCE_FROM_DB = 5;
  }
  // enum used to give options to client to decide on how much stale response is acceptable
  DataFreshness data_freshness = 4;

  enum VendorApiOption {
    VENDOR_API_OPTION_UNSPECIFIED = 0;

    // Using this option will lead to not comparing old and new vendor api.
    // It will give response of new vendor api.
    NEW_VENDOR_API = 1;
  }

  // enum used to give options to client to decide whether to use new vendor api response directly.
  // Client should use this option to explicitly invoke newer version of vendor's balance api call.
  VendorApiOption vendor_api_option = 5;
}

message GetAccountBalanceV1Response {
  enum Status {
    OK = 0;
    // Account data corresponding to the given identifier not found
    NOT_FOUND = 5;
    // The actor does not have permission to execute the specified operation.
    // One of the reasons could be that Actor is not owner of the account
    PERMISSION_DENIED = 7;
    // Internal server
    INTERNAL = 13;
    // account number not created (creation in progress or max retries exhausted)
    ACCOUNT_NUMBER_NOT_CREATED = 100;
    // account has no access level (cannot call vg in this case)
    // an account can have no access level in scnerios like (but not limited to):
    // 1. Min kyc account expired
    // 2. Risk Threshold breached
    // In such cases, there is no point of calling vendor since we do not get the desired response i.e. balance
    ACCOUNT_NO_LEVEL_ACCESS = 101;
  }
  rpc.Status status = 1;
  // Ledger balance of the account (Opening balance on the given day)
  google.type.Money ledger_balance = 2;
  // Contains latest balance of an account.
  google.type.Money available_balance = 3;
  // Optional: Clearance balance refers to the amount which is in clearance state. This typically happens for cheques.
  google.type.Money clearance_balance = 4;
  // Optional: Lien balance is the amount which the bank has put a hold on. Can happen for failed payments
  google.type.Money lien_balance = 6;
  // Timestamp for which the given balance was calculated
  google.protobuf.Timestamp balance_at = 8;
  // Optional:  There can be freeze conditions with respect to an account, freeze status gives a standard code about it
  string freeze_raw_code = 9;
  // Optional:  Reason for the freeze status
  string freeze_reason = 10;
}

// Request message for creating a new account in the database.
message CreateAccountRequest {
  // User Id of the user for whom account need to be created.
  string primary_account_holder_id = 1;

  // Request Id to be passed to create account at vendor's end
  string req_id = 2;

  // This enum denotes from which stage the account creation needs to be restarted. There are two
  // steps in account creation -
  // 1. Account creation
  // 2. Enquiry for account creation
  enum ForceRetryOption {
    // When retry is not needed
    UNSPECIFIED = 0;
    // Force retry needs to be triggered from account creation
    FORCE_NEW_REQUEST = 1;
    // Force retry needs to be triggered from enquiry status
    FORCE_INQUIRY = 2;
  }

  // Parameter to force retry account creation. This parameter is to be used only in special failure scenarios,
  // where we want to redo account creation post manual fixes. Parameter to be set true either through script/sherlock.
  ForceRetryOption force_retry = 3;

  // Savings account creation request made to the partner bank will be of this account type.
  // Pass only for NRE and NRO account api.typesv2. For rest of the accounts, SKU is evaluated internally.
  // should_open_amb_account flag is consumed for evaluating SKU for min AMB accounts.
  savings.SKU sku = 4;

  string actor_id = 5;

  message Options {
    // flag mentioning if user gave consent to open min AMB account
    // savings account will be created with Regular tier schemes codes (with respective KYC scheme) if this flag is set
    bool should_open_amb_account = 6;
  }

  Options options = 6;
}

// Response message for creating a new account. Contains the newly created account.
message CreateAccountResponse {
  // Account that was written to the database. Contains the `id` field populated.
  Account account = 1;
  // RPC status to return when a call is made
  rpc.Status status = 2;

  enum Status {
    OK = 0;

    // INVALID_DEDUPE_STATUS indicates that the dedupe check before account creation returned a dedupe status that is not
    // supported for account creation.
    INVALID_DEDUPE_STATUS = 100;
  }
}

// A struct to uniquely identify account using identifiers provided by banking partner.
message BankAccountIdentifier {
  // Account number provided by bank.
  string account_no = 1;
  // IFSC code belonging to the account.
  string ifsc_code = 2;
}

// Request message for reading an account.
message GetAccountRequest {
  oneof identifier {
    // Internal account id.
    string id = 1;

    // External account id, contains identifier provided by banking partner.
    // deprecated: use AccountNumberBankFilter instead
    BankAccountIdentifier external_id = 2 [deprecated = true];

    // Id of the primary account holder
    // deprecated : use ActorUniqueAccountIdentifier instead
    string primary_user_id = 3 [deprecated = true];
    // deprecated : use ActorUniqueAccountIdentifier instead
    string actor_id = 4 [deprecated = true];

    AccountNumberBankFilter account_num_bank_filter = 5;
    // Identifier to uniquely determine the account using actor id, APO and Vendor
    ActorUniqueAccountIdentifier actor_unique_account_identifier = 6;
  }
}

// Response message for reading an account. Contains the requested account.
message GetAccountResponse {
  Account account = 1;
  rpc.Status status = 2;
}

message GetAccountsListRequest {
  // identifier to be used for making RPC call
  oneof identifier {
    // list of primary_user_id to get respective list of account
    // deprecated in favour of BulkActorIdentifier
    PrimaryUserIdentifier user_ids = 1 [deprecated = true];
    // actor id list and APO list to get respective list of account
    BulkActorIdentifier bulk_actor_identifier = 2;
  }
}

message GetAccountsListResponse {
  enum Status {
    OK = 0;
    // Account data corresponding to the given identifier not found
    NOT_FOUND = 5;
    // Internal server
    INTERNAL = 13;
  }
  // RPC status to return when a call is made
  rpc.Status status = 1;
  // list of accounts for given identifier
  repeated Account accounts = 2;
}

message PrimaryUserIdentifier {
  repeated string primary_user_ids = 2;
  // Request param to control which account (Regular, NRE or NRO) types to return
  // To maintain backward compatibility, the rpc returns Regular savings account incase when Account Product Offering list is empty
  repeated api.typesv2.account.AccountProductOffering account_product_offering_list = 3;
}

message BulkActorIdentifier {
  repeated string actor_ids = 1;
  // Request param to control which account (Regular, NRE or NRO) types to return
  // Returns error if Account Product Offering list is empty or contains Unspecified type
  repeated api.typesv2.account.AccountProductOffering account_product_offerings = 2;
  // Request param to control which Partner Bank accounts to return
  // Returns error if Partner Bank Account list is empty or contains Unspecified type
  repeated vendorgateway.Vendor partner_banks = 3;
}

message ActorUniqueAccountIdentifier {
  string actor_id = 1;
  // Request param to control which account (Regular, NRE or NRO) type to return
  // Returns error if Account Product Offering is unspecified
  api.typesv2.account.AccountProductOffering account_product_offering = 2;
  // Request param to control which Partner Bank account to return
  // Returns error if Partner Bank is unspecified
  vendorgateway.Vendor partner_bank = 3;
}

// Request message for fetching account balance.
message GetAccountBalanceRequest {
  oneof identifier {
    // Internal account id.
    string id = 1;

    // External account id, contains identifier provided by banking partner.
    BankAccountIdentifier external_id = 2;
  }
  // actor to whom the account belongs to.
  string actor_id = 3;

  enum DataFreshness {
    DATA_FRESHNESS_UNSPECIFIED = 0;
    // Data has to be real time
    // Using this option will lead to higher latency as this usually means we hit
    // partner banks to fetch real time data
    REAL_TIME = 1;
    // Data can be stale by a delay of 10 seconds
    // This option is a hybrid between `REAL_TIME` and `STALE` and offers a balance of latency and data freshness
    // Recommendation: Should work for most business use cases unless it blocks user facing application
    // e.g., Fund transfer
    NEAR_REAL_TIME = 2;
    // Data is expected to be stale by up to 60 seconds
    // Recommendation: Use for analytical purposes
    STALE = 3;
  }
  // enum used to give options to client to decide on how much stale response is acceptable
  DataFreshness data_freshness = 4;
}

// Response message for fetching account balance.
message GetAccountBalanceResponse {
  enum Status {
    OK = 0;
    // Account data corresponding to the given identifier not found
    NOT_FOUND = 5;
    // The actor does not have permission to execute the specified operation.
    // One of the reasons could be that Actor is not owner of the account
    PERMISSION_DENIED = 7;
    // Internal server
    INTERNAL = 13;
    // account number not created (creation in progress or max retries exhausted)
    ACCOUNT_NUMBER_NOT_CREATED = 100;
    // account has no access level (cannot call vg in this case)
    ACCOUNT_NO_LEVEL_ACCESS = 101;
  }
  rpc.Status status = 1;
  // Contains latest balance of an account.
  google.type.Money available_balance = 2;
  // Ledger balance of the account (Opening balance on the given day)
  google.type.Money ledger_balance = 3;
  // Timestamp when this balance was last updated
  google.protobuf.Timestamp last_updated_at = 4;
}

// Request message for updating various account info.
message UpdateAccountRequest {
  oneof identifier {
    // Internal account id.
    string id = 1;

    // External account id, contains identifier provided by banking partner.
    BankAccountIdentifier external_id = 2;

    // user id corresponding to the primary account holder
    string primary_account_holder = 9;

  }
  // New balance to be updated in the account.
  google.type.Money balance = 3;
  // Set of account constraints to be changed.
  AccountConstraints constraints = 4;
  // New account status to be updated
  State state = 5;
  // New phone number to be updated for account
  api.typesv2.common.PhoneNumber phone_number = 6;
  // New email id to be updated for account
  string email_id = 7;

  // The fields that need to be updated. This is repeated because a single update
  // call can update multiple fields. The update request ignores any fields that aren't specified in the field mask,
  // leaving them with their current values.
  repeated AccountFieldMask update_mask = 8;

  //  sku to be updated
  SKU sku = 10 [deprecated = true];

  string actor_id = 11;

  //  sku and sku info to be updated
  SKUInfo sku_info = 12;

}

// Response message for updating account info.
message UpdateAccountResponse {
  enum Status {
    OK = 0;
    // Account data corresponding to the given identifier not found
    NOT_FOUND = 5;
    // Internal server
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // Contains latest balance of an account.
  // Contains updated account info.
  Account account = 2;
}

message GetOpeningBalanceRequest {
  oneof identifier {
    // internal account id
    string id = 1;

    // External account id, contains identifier provided by banking partner.
    // like account no and ifsc
    BankAccountIdentifier external_id = 2;

    // actor id corresponding to the primary account holder
    // to be passed by client if sum of opening balance corresponding
    // to all the accounts of the actor is required.
    // In case data specific to a single account is needed please use
    // `id` and `external_id`
    string primary_account_holder_actor = 3;
  }

  // date on which opening balance need to be enquired
  google.type.Date date = 4 [(validate.rules).message.required = true];

  string actor_id = 5 [(validate.rules).string.min_len = 1];
}

message GetOpeningBalanceResponse {
  enum Status {
    OK = 0;
    // Account data corresponding to the given identifier not found
    NOT_FOUND = 5;
    // The actor does not have permission to execute the specified operation.
    // One of the reasons could be that Actor is not owner of the account
    PERMISSION_DENIED = 7;
    // Internal server
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // opening balance corresponding to the specified request parameter
  google.type.Money opening_balance = 2;
}

message GetAccountBalanceWithSummaryRequest {
  oneof identifier {
    // internal account id
    string id = 1;

    // External account id, contains identifier provided by banking partner.
    // like account no and ifsc
    BankAccountIdentifier external_id = 2;

    // actor id corresponding to the primary account holder
    // to be passed by client if sum of opening balance corresponding
    // to all the accounts of the actor is required.
    // In case data specific to a single account is needed please use
    // `id` and `external_id`
    string primary_account_holder_actor = 3;
  }

  string actor_id = 4;

  enum Range {
    RANGE_UNSPECIFIED = 0;

    MONTH = 1;
  }
  // start time from which we fetch the opening balance and txn aggregates for given account (end time is time.now())
  // currently using start time as max(account opening date ,beginning of current month)
  Range time_range = 5 [(validate.rules).enum = {not_in: 0}];

  // enum sent from the client to identify if the service is requesting to update balance on his home screen.
  // Optional field: If not sent then default FORCE_BALANCE_UPDATE_UNSPECIFIED will be set
  // If FORCE_BALANCE_UPDATE_NEEDED is passed as value then we will fetch the balance from vendor GetBalance first and fallback will be GetBalanceV1 api
  // and vice versa if the FORCE_BALANCE_UPDATE_NEEDED is not provided
  enum ForceBalanceUpdate {
    FORCE_BALANCE_UPDATE_UNSPECIFIED = 0;
    FORCE_BALANCE_UPDATE_NEEDED = 1;
    FORCE_BALANCE_UPDATE_NOT_NEEDED = 2;
  }
  // Enum to identify if the user wants to get his balance refreshed.
  ForceBalanceUpdate force_balance_update = 6;
}

message GetAccountBalanceWithSummaryResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;

    DEVICE_TEMPORARILY_DEACTIVATED = 100;
  }

  rpc.Status status = 1;

  google.type.Money opening_balance = 2;

  // available balance in account which can be used
  // for transaction by the actor
  google.type.Money available_balance = 3;

  // balance in the account ledger
  // in general ledger balance is always same as available
  // balance, only in certain scenarios like e-mandates, etc.
  // ledger balance might differ from available balance until
  // the money in hold is released
  google.type.Money ledger_balance = 4;

  // total amount spent by the actor (total debit - saved)
  google.type.Money total_spent = 5 [deprecated = true];

  // total amount credited to the account
  google.type.Money total_credit = 6;

  // total amount debited from the account
  google.type.Money total_debit = 7;

  // detailed summary of each bucket of account summary
  repeated AccountSummaryBucketDetails bucket_details = 8 [deprecated = true];

  // returns true in case computed balance epiFi's end is stale
  // due to missing transactions in the ledger.
  // the aggregates in this case may not sum up to the balance returned
  bool is_computed_balance_stale = 9;

  // balance as of timestamp
  google.protobuf.Timestamp balance_at = 10;

  // returns true in case stale balance is returned from the server cache
  bool is_balance_stale = 11;
}

// Account summary comprises of
message AccountSummaryBucketDetails {
  enum Bucket {
    BUCKET_UNSPECIFIED = 0;

    CREDIT = 1;

    SPENT = 2;

    SAVED = 3;
  }

  Bucket bucket = 1;
  // money value spent in the current category
  google.type.Money value = 2;
  // percentage increase or decrease of current category w.r.t previous month
  float percent_change = 3;
  // array of sub section/ or sub categories (eg. under spent category we can have bills, food as subcategories) in account summary
  repeated AccountSummaryBucketDetails sections = 4;
}

message GetListOfActiveAccountsRequest {
  // Optional: timestamp before which active account needs to be fetched
  // in case empty all accounts created till current time will be returned
  google.protobuf.Timestamp created_before = 1;

  // Optional: timestamp after which active account needs to be fetched
  // in case empty all accounts created starting from the first account will be returned
  google.protobuf.Timestamp created_after = 2;

  // Optional: size of page returned by the RPC. Ideally, caller should specify this
  // when the time duration for the query is expected to be huge.
  // E.g. if the client want to fetch all the active accounts till current date then
  // it should specify the page size as the response can be enormous
  int32 page_size = 3;

  // Optional: offset for response to be returned
  // In case of pagination caller can use the offset value passed in the call response
  // to fetch the subsequent pages.
  int64 offset = 4;
}

// list of all active savings account for which monthly statement will be generated
message GetListOfActiveAccountsResponse {
  // savings account for which monthly statement will be generated
  message Account {
    option deprecated = true;

    // internal ID referencing the account.
    string id = 1;

    // User id of the primary account holder
    string primary_account_holder_user_id = 4;
  }

  enum Status {
    OK = 0;
    // No active accounts found
    NOT_FOUND = 5;
    // Internal server error
    INTERNAL = 13;
  }

  repeated Account account = 1 [deprecated = true];

  rpc.Status status = 2;

  // list of savings account returned based on the input argument
  repeated savings.Account account_list = 3;

  // offset to be passed in next RPC call in order to fetch subsequent accounts
  // to be used by caller in case account details are being fetched in a paginated manner
  int64 next_offset = 4;
}

message CloseAccountRequest {
  oneof identifier {
    // Internal account id.
    string id = 1;

    // External account id, contains identifier provided by banking partner.
    BankAccountIdentifier external_id = 2;

    // Id of the primary account holder
    string primary_user_id = 3;
  }
}

message CloseAccountResponse {
  rpc.Status status = 1;

  // Contains updated account info.
  Account account = 2;
}

message ReopenAccountRequest {
  oneof identifier {
    // Internal account id.
    string id = 1;

    // External account id, contains identifier provided by banking partner.
    BankAccountIdentifier external_id = 2;

    // Id of the primary account holder
    string primary_user_id = 3;
  }
}

message ReopenAccountResponse {
  rpc.Status status = 1;

  // Contains updated account info.
  Account account = 2;
}

message IsTxnAllowedRequest {
  oneof identifier {
    // internal account id
    string id = 1;

    // External account id, contains identifier provided by banking partner.
    // like account no and ifsc
    BankAccountIdentifier external_id = 2;

    // actor id corresponding to the primary account holder
    // to be passed by client if sum of opening balance corresponding
    // to all the accounts of the actor is required.
    // In case data specific to a single account is needed please use
    // `id` and `external_id`
    string primary_account_holder_actor = 3;
  }
  // Amount to be credited to the account
  google.type.Money amount = 4;

  // field to specify if the requested transaction is debit or credit one.
  TxnType txn_type = 5;

  enum TxnType {
    TXN_TYPE_UNSPECIFIED = 0;
    CREDIT = 1;
    DEBIT = 2;
  }
}

message IsTxnAllowedResponse {
  enum Status {
    // credit transaction allowed
    OK = 0;
    // No accounts found
    NOT_FOUND = 5;
    // Internal server error
    INTERNAL = 13;
    // credit transaction not allowed as one or more min-kyc checks failed
    NOT_ALLOWED_MIN_KYC_CHECK_FAILURE = 101;
  }
  rpc.Status status = 1;

  // field to represent credit amount allowed for transaction.
  // This value will be based on the min of (total credited allowed - total credited till now) and (max savings balance - current savings balance)
  google.type.Money credit_amount_allowed = 2;

  // for min-kyc accounts, date on which savings account will freeze
  // as per the current min-kyc limits, accounts will freeze one year after creation
  // In case of account being full-kyc, this field will be null
  google.protobuf.Timestamp account_freeze_date = 3;

  // Reason why credit transaction isn't allowed
  Reason not_allowed_reason = 4;

  enum Reason {
    REASON_UNSPECIFIED = 0;
    MIN_KYC_ACCOUNT_DURATION_CHECK_FAILURE = 1;
    MIN_KYC_MAX_BALANCE_CHECK_FAILURE = 2;
    MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILURE = 3;
    // new user here signifies that user's account was created after
    // MinKycLimitChangeDate (savings/config/config.go)
    MIN_KYC_MAX_BALANCE_CHECK_FAILURE_FOR_NEW_USER = 4;
    // new user here signifies that user's account was created after
    // MinKycLimitChangeDate (savings/config/config.go)
    MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILURE_FOR_NEW_USER = 5;
  }

  message SavingsTxnLimits {
    // denotes the remaining/allowed credit limit percentage rounded down
    // deprecated in favour of allowed_credit_limit_percent_float
    int32 allowed_credit_limit_percent = 1 [deprecated = true];
    // denotes the remaining/allowed credit limit amount
    google.type.Money allowed_credit_limit_amount = 2;
    // denotes the remaining/allowed savings balance limit percentage rounded down
    // deprecated in favour of allowed_savings_limit_percent_float
    int32 allowed_savings_limit_percent = 3 [deprecated = true];
    // denotes the remaining/allowed savings balance limit amount
    google.type.Money allowed_savings_limit_amount = 4;
    // denotes the max savings balance amount a user is allowed to have
    google.type.Money max_allowed_savings_limit_amount = 5;
    // denotes the total credit amount a user is allowed to have
    google.type.Money total_allowed_credit_limit_amount = 6;
    // denotes the remaining/allowed credit limit percentage, (might loose precision)
    double allowed_credit_limit_percent_float = 7;
    // denotes the remaining/allowed savings balance limit, (might loose precision)
    double allowed_savings_limit_percent_float = 8;
  }
  // denotes various limits related to txn like savings account limit, credit limit for now
  SavingsTxnLimits savings_txn_limits = 5;
}

message StoreClosedAccountBalTransferDataRequest {
  savings.ClosedAccountBalanceTransfer data = 1;
}

message StoreClosedAccountBalTransferDataResponse {
  rpc.Status status = 1;
}

message StoreClosedAccountBalTransferDataFromStatementRequest {
  // savings_account_id is the id in savings_accounts table
  string savings_account_id = 1;
}

message StoreClosedAccountBalTransferDataFromStatementResponse {
  rpc.Status status = 1;
}

message GetClosedAccountBalTransferDataRequest {
  // savings_account_id is the id in savings_accounts table
  string savings_account_id = 1;
}

message GetClosedAccountBalTransferDataResponse {
  rpc.Status status = 1;
  repeated savings.ClosedAccountBalanceTransfer entries = 2;
}

message GetEODSavBalanceHistoryRequest {
  string actor_id = 1;
  // Start time of balance history
  google.protobuf.Timestamp start_time = 2;
  // End time of balance history
  google.protobuf.Timestamp end_time = 3;
}

message GetEODSavBalanceHistoryResponse {
  rpc.Status status = 1;
  // List of balances by date
  repeated BalanceByDate balance_by_dates = 2;
}

message BalanceByDate {
  google.type.Date date = 1;
  // Balance amount
  google.type.Money eod_balance = 2;
}

message UpdateClosedAccountBalTransferDataRequest {
  // closed account id is used as primary identifier
  savings.ClosedAccountBalanceTransfer data = 1;
  repeated savings.CbtFieldMask field_masks = 2;
}

message UpdateClosedAccountBalTransferDataResponse {
  rpc.Status status = 1;
}

message FetchOrCreateSignAttemptRequest {
  string actor_id = 1;
}

message FetchOrCreateSignAttemptResponse {
  rpc.Status status = 1;
  SignStatus sign_status = 2;
  // sign_url contains url in case of sign state is created, failed, or in progress
  string sign_url = 3;
  // used by client to decide they have to close webview
  string exit_url = 4;
}

enum SignStatus {
  SIGN_STATUS_UNSPECIFIED = 0;
  // if account status api have sign count >=1
  SIGN_STATUS_ALREADY_AVAILABLE_WITH_BANK = 1;
  // if last attempt is successful but bank don't have sign
  SIGN_STATUS_SIGN_IN_REVIEW = 2;
  // first attempt created
  SIGN_STATUS_FIRST_ATTEMPT_CREATED = 3;
  // last attempt is in progress, in this case we return same url
  SIGN_STATUS_LAST_ATTEMPT_IN_PROGRESS = 4;
  // last attempt failed, we create a new attempt
  SIGN_STATUS_LAST_ATTEMPT_FAILED = 5;
  // last attempt is signed but federal sign count < 1 even after breaching time threshold
  SIGN_STATUS_LAST_ATTEMPT_FAILED_SINCE_PENDING_AT_BANK = 6;
}

message ActorIdBankFilter {
  string actor_id = 1;
  vendorgateway.Vendor partner_bank = 2;
}

message AccountNumberBankFilter {
  // External account number that's visible to the user.
  string account_number = 1;
  vendorgateway.Vendor partner_bank = 2;
}

message GetSavingsAccountEssentialsRequest {
  oneof filter {
    // deprecated in favour of ActorUniqueAccountIdentifier
    ActorIdBankFilter actor_id_bank_filter = 1 [deprecated = true];
    AccountNumberBankFilter account_num_bank_filter = 2;
    ActorUniqueAccountIdentifier actor_unique_account_identifier = 3;
  }
}

message SavingsAccountEssentials {
  // internal ID referencing the account.
  string id = 1;

  // External account number that's visible to the user.
  string account_no = 2;

  string actor_id = 3;

  // IFSC code corresponding of the account
  string ifsc_code = 5;

  // Current Account status
  State state = 6;

  // Partner bank to which the account belongs to
  vendorgateway.Vendor partner_bank = 7;

  SKUInfo sku_info = 8;

  // cache_version will be used to invalidate older MinimalAccount cached data in case we add more
  // fields in MinimalAccount. The current version of the cache will be set as a constant in code.
  int32 cache_version = 10;
}

message GetSavingsAccountEssentialsResponse {
  rpc.Status status = 1;
  SavingsAccountEssentials account = 2;
}

message CreateOrGetSaClosureRequestRequest {
  // actor id of the user requesting for account closure
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // entry point of the SA Closure flow
  // entry point is recorded for the user when the request creation is done
  // user might resume the closure request from different entry point - this will not be tracked
  SAClosureRequestEntryPoint entry_point = 2;
  // field mask to select the fields from closure request
  repeated SavingsAccountClosureRequestFieldMask field_masks = 3;
}

message CreateOrGetSaClosureRequestResponse {
  rpc.Status status = 1;
  // closure request of the user
  SavingsAccountClosureRequest closure_request = 2;
}

message GetActiveSaClosureRequestForUserRequest {
  // actor id of the user requesting for account closure
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // field mask to select the fields from closure request
  repeated SavingsAccountClosureRequestFieldMask field_masks = 2;
}

message GetActiveSaClosureRequestForUserResponse {
  rpc.Status status = 1;
  // closure request of the user
  SavingsAccountClosureRequest closure_request = 2;
}

message UpdateSaClosureRequestStatusRequest {
  // primary id of the sa closure request table to update
  string closure_request_id = 1 [(validate.rules).string.min_len = 1];
  SAClosureRequestStatus status = 2;
  SAClosureRequestStatusReason status_reason = 3;
}

message UpdateSaClosureRequestStatusResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // Internal error while processing the request
    INTERNAL = 13;
    INVALID_STATUS_UPDATE = 101;
    TERMINAL_STATUS_UPDATE_NOT_ALLOWED = 102;
    // when from status and to status is same
    DUPLICATE_STATUS_UPDATE_NOT_ALLOWED = 103;
  }
  rpc.Status status = 1;
}

message RecordSaClosureUserFeedbackRequest {
  // primary id of the sa closure request table to update
  string closure_request_id = 1 [(validate.rules).string.min_len = 1];
  SaClosureRequestUserFeedback user_feedback = 2;
}

message RecordSaClosureUserFeedbackResponse {
  rpc.Status status = 1;
}

message GetSubmittedSaClosureRequestsRequest {
  rpc.PageContextRequest page_context = 1;
  google.protobuf.Timestamp from_timestamp = 2;
  google.protobuf.Timestamp to_timestamp = 3;
}

message GetSubmittedSaClosureRequestsResponse {
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  repeated SavingsAccountClosureRequest savings_account_closure_requests = 3;
}

// check for indexes in db before creating a new filter
message GetSaClosureRequestsByFilterRequest {
  rpc.PageContextRequest page_context = 1;
  repeated SavingsAccountClosureRequestFieldMask field_masks = 2;
  repeated string actor_id_filter = 3;
  repeated string closure_request_id_filter = 4;
  repeated SAClosureRequestStatus status_filter = 5;
}

message GetSaClosureRequestsByFilterResponse {
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  repeated SavingsAccountClosureRequest savings_account_closure_requests = 3;
}

message VerifyPanForAccountClosureRequest {
  string actor_id = 1;
  string pan = 2;
}

message VerifyPanForAccountClosureResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
    // PAN is not correct
    PAN_INCORRECT = 101;
    // Verification is locked temporarily
    // There have been too many incorrect attempts
    // Wait for some time and try again
    PAN_INCORRECT_LOCKED = 102;
  }
  rpc.Status status = 1;
  // in case of not ok response, number of attempts left is sent back
  int32 attempts_left = 2;
}
