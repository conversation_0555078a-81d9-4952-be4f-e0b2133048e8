syntax = "proto3";

package savings.activity;

option go_package = "github.com/epifi/gamma/api/savings/activity";
option java_package = "com.github.epifi.gamma.api.savings.activity";

import "api/celestial/activity/header.proto";
import "api/savings/account.proto";


message UpdateNomineeAtVendorRequest{
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message UpdateNomineeAtVendorResponse{
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  string vendor_request_id = 2;
}

message EnquireNomineeAtVendorRequest{
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string vendor_request_id = 2;
}

message EnquireNomineeAtVendorResponse{
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message UpdateNomineeInDBRequest{
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // Updation status to be updated in DB
  WorkflowState previous_workflow_state = 2;
}

message UpdateNomineeInDBResponse{
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}
