syntax = "proto3";

package types.webui;

option go_package = "github.com/epifi/gamma/api/types/webui";
option java_package = "com.github.epifi.gamma.api.types.webui";

message MediaUnit {
  MediaType media_type = 1;
  string image_path = 2;
  string base64_kyc_image_data = 3;
  string actor_id = 4;
  string request_id = 5;
  string label = 6;
  string video_location = 7;
  string background_color = 8;
  string created_at = 9;
}

enum MediaType {
  MEDIA_TYPE_UNSPECIFIED = 0;
  MEDIA_TYPE_IMAGE = 1;
  MEDIA_TYPE_VIDEO = 2;
}
