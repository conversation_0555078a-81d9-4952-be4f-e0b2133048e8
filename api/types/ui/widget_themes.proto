syntax = "proto3";

// Please start using types.ui.widget package since this causes cyclic dependency issues
package types.ui;

option go_package = "github.com/epifi/gamma/api/types/ui";
option java_package = "com.github.epifi.gamma.api.types.ui";

/*
Background colour:
  Each ui component can have a background colour. Following proto message can be included in any component.
  Background colour can be of the following types:
  1. Radial gradient:
      The colour varies in the component radially. Eg https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/FFF-Home-V2.0-(WIP)?node-id=1137%3A22747.
      A gradient that might seem to be varying from left to the right, can be perceived as a radial gradient with a relatively large radius.
      Using the centre, radius and an array of colours the client can render the radial background for the component.

  2. Single colour:
    The complete component is of single background colour.

  Shadow effect:
  1. Shadow effect:
    Some component can have a shadow effect: https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/FFF-Home-V2.0-(WIP)?node-id=824%3A23705
 */

message BackgroundColour {
  // Please start using types.ui.widget package since this causes cyclic dependency issues
  option deprecated = true;
  oneof colour {
    // Radial gradient - the colour of the component changes radially
    RadialGradient radial_gradient = 1;
    // Block colour - single colour to be used for the background
    string block_colour = 2;
  }
}

/*
  Radial gradient:
      The colour varies in the component radially. Eg https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/FFF-Home-V2.0-(WIP)?node-id=1137%3A22747.
      A gradient that might seem to be varying from left to the right, can be perceived as a radial gradient with a relatively large radius.
      Using the centre, radius and an array of colours the client can render the radial background for the component.
  RadialGradient can be defined using center, radius, and an array of colours equally spaced along the radius from the center.
  We will assume that the gradients are circular.
 */
message RadialGradient {
  // Center of the radial gradient
  CenterCoordinates center = 1;
  // Radius of the gradiant
  int32 outer_radius = 2;
  // Sequence of equally spaced colours to render for the gradiant
  repeated string colours = 3;
}

// center of gradient - x and y coordinates
message CenterCoordinates {
  // x coordinate
  int32 center_x = 1;
  // y coordinate
  int32 center_y = 2;
}

/*
  Some widget components can have a shadow effect - https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/FFF-Home-V2.0-(WIP)?node-id=824%3A23705
 */
message Shadow {
  // Height of the shadow
  int32 height = 1;
  // Blur parameter
  int32 blur = 2;
  // opacity of shadow component. Value denotes percentage and ranges between 0-100
  int32 opacity = 4;
  // Background colour for the shadow
  BackgroundColour colour = 3;
}
