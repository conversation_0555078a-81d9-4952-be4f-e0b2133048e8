syntax = "proto3";

package types.ui.components;

import "api/types/text.proto";
import "api/types/ui/widget/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/types/ui/components";
option java_package = "com.github.epifi.gamma.api.types.ui.components";

// Renders individual section cards with section header followed by section info points
// ex: https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9239&node-type=frame&t=ocYBnZsxF4kdCkhM-0
message SectionDetails {
  Header header = 1;
  // List of points to render in the section
  repeated types.Text infos = 2;
  // background color of the section
  types.ui.widget.BackgroundColour bg_color = 3;
  // contains title and other info for the card, supports CTA if required. can be nil if no header is needed
  message Header {
    // Text of the header, e.g. 'How to redeem'
    types.Text title = 1;
    // Background of the header (optional)
    types.ui.widget.BackgroundColour bg_color = 2;
  }
}
