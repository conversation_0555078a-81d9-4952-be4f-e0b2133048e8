syntax = "proto3";

package types.deeplink_screen_option.p2pinvestment;

import "api/types/deeplink_screen_option/header.proto";

option go_package = "github.com/epifi/gamma/api/types/deeplink_screen_option/p2pinvestment";
option java_package = "com.github.epifi.gamma.api.types.deeplink_screen_option.p2pinvestment";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// screen: JUMP_PROMOTION_LOADING_SCREEN
// RPC to be called on loading this screen frontend.p2pinvestment.GetInvestPageFromPromotionalUseCase
message PromotionLoadingScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // string value of UseCase enum
  // using string here instead of enum in order to not require any client changes when new enum values are added
  string use_case = 2;
  // json payload corresponding to use case
  string json_payload = 3;
}

enum UseCase {
  USE_CASE_UNSPECIFIED = 0;
  // use case to promote a specific plan to land users on the plan's invest page from PN's nudges etc
  USE_CASE_PROMOTE_SPECIFIC_PLAN = 1;
}

// payload corresponding to USE_CASE_PROMOTE_SPECIFIC_PLAN
message PromoteSpecificPlanPayload {
  // string value of p2pinvestment.SchemeName
  // scheme name for the investment page where we want to land the user
  string investment_scheme_name = 1;
}
