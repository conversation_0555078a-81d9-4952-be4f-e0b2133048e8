syntax = "proto3";

package types.deeplink_screen_option.docs;

import "api/frontend/deeplink/deeplink.proto";
import "api/types/deeplink_screen_option/header.proto";
import "api/types/text.proto";
import "api/types/ui/widget/widget_themes.proto";
import "api/types/visual_element.proto";
import "api/types/ui/header_bar.proto";

option go_package = "github.com/epifi/gamma/api/types/deeplink_screen_option/docs";
option java_package = "com.github.epifi.gamma.api.types.deeplink_screen_option.docs";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=305-13142&t=S9XAH2X60XUfyYdb-0
// Deeplink: IMAGE_CAPTURE_INFO
message CaptureImageInfoScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  types.Text title = 2;
  types.Text description = 3;
  repeated frontend.deeplink.Cta ctas = 4;
  types.ui.widget.BackgroundColour bg_color = 5;
  // in favour of removed @param icon visual element
  reserved 6;
  // flow eg. NR onb
  string flow = 7;
  repeated ImageCaptureInfo image_info = 8;
  HeaderBar header_bar = 9;
}

// https://www.figma.com/design/tDL7NfNrlSmcD1x3FeT1lT/NRO%2FNRE---Onboarding?node-id=305-13142&t=S9XAH2X60XUfyYdb-0
message ImageCaptureInfo {
  types.VisualElement file_illustration = 1;
  types.Text description = 2;
  // deeplink for click
  frontend.deeplink.Deeplink deeplink = 3;
  // front/back side, can be used for logging events.
  string type = 4;
}
