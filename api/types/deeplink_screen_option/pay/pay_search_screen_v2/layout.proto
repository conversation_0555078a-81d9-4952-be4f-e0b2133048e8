syntax = "proto3";

package types.deeplink_screen_option.pay.pay_search_screen_v2;

import "api/frontend/deeplink/deeplink.proto";
import "api/types/deeplink_screen_option/pay/pay_search_screen_v2/screen_options.proto";
import "api/types/text.proto";
import "api/types/ui/icon_text_component.proto";
import "api/types/ui/widget/common.proto";
import "api/types/ui/widget/widget_themes.proto";
import "api/types/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/types/deeplink_screen_option/pay/pay_search_screen_v2";
option java_package = "com.github.epifi.gamma.types.deeplink_screen_option.pay.pay_search_screen_v2";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message PaySearchV2Layout {
    // background color for the search screen
    types.ui.widget.BackgroundColour background_colour = 1;
    // Title of the screen, eg: Pay any number, Send money to any UPI app
    types.Text title = 2;
    // banner to be displayed above the search bar
    // eg: You can now pay any mobile number on any app
    types.ui.IconTextComponent banner = 3;
    // search bar component
    SearchBar search_bar = 4;
    // contacts icon to be displayed to the right of search bar.
    // When the user taps this icon, scroll the All Contacts section to the top of the screen to bring it into focus.
    types.VisualElement contact_icon = 5;
    // list of sections to be displayed in search screen
    // client should render the sections in the order they are received
    repeated SearchSectionInfo sections = 6;
    // Show VPA handle suggestions when the user starts searching for a vpa. ie when user types "@" in the search bar
    // e.g., @okicici, @okhdfc
    repeated types.ui.IconTextComponent vpa_handles = 7;
    // Footer for the screen. eg: `powered by bhim, upi, federal bank`
    types.VisualElement footer = 8;
    // When the search query doesn't return any results, show this component,
    // suggesting to use other payment methods like upiId, qr scan etc.
    OtherPaymentOptionsNudge other_payment_options_nudge = 9;
    // When the user starts searching, the client performs a local search
    // to find matches for the query until an 8-digit number or a valid VPA is entered.
    // If no results are found, display this component to prompt the user to enter a complete query.
    types.ui.widget.VisualElementTitleSubtitleElement query_completion_nudge = 10;
    // When the user starts searching for a VPA (enters '@' in the search bar), display this Cta along with vpa handle suggestions.
    // The CTA should only be enabled if the entered VPA is valid. Client should trigger search rpc when user taps the Cta.
    frontend.deeplink.Cta verify_vpa_cta = 11;

    // Search bar to be shown at the top of the screen
    message SearchBar {
        // To apply bg color, corner radius etc on the search bar
        types.ui.IconTextComponent.ContainerProperties container_properties = 1;
        // Image shown on the left side of search bar, for eg: magnifying glass icon
        types.VisualElement left_icon = 2;
        // placeholder to be displayed until user starts typing
        types.Text placeholder = 3;
        // Apply font, text color etc from this on the text entered by user on search bar.
        types.Text text_style = 4;
    }

    // When user's query returns no results, nudge the user to try other payment methods.
    message OtherPaymentOptionsNudge {
        // To apply bg color, border color, corner radius etc on the component
        types.ui.IconTextComponent.ContainerProperties container_properties = 1;
        // eg: "Don’t see the number you are looking for?"
        types.Text title = 2;
        // ctas to take the user to other payment methods
        // eg: Upi id, scan & pay etc.
        repeated types.ui.IconTextComponent ctas = 3;
    }

    // Details for each section displayed in search screen
    message SearchSectionInfo {
        types.deeplink_screen_option.pay.pay_search_screen_v2.SearchSection section_type = 1;
        // title corresponding to each section type.
        // eg: All contacts, Recent searches
        types.ui.IconTextComponent title = 2;

        oneof option {
            RecentSearchesSection recent_searches = 3;
            AllContactsSection all_contacts = 4;
        }
    }

    // Additional info for the Recent Searches section
    message RecentSearchesSection {
        // max number of recent searches to be displayed in ui
        int32 max_display_count = 1;
        ZeroStateComponent zero_state = 2;

        // To be shown when user doesn't have any recent searches
        message ZeroStateComponent {
            types.VisualElement image = 1;
            types.Text description = 2;
            types.VisualElement footer_icons = 3;
        }
    }

    // Additional info for the All Contacts section
    message AllContactsSection {
        ContactsPermissionNudge contacts_permission_nudge = 1;

        // If the user hasn't granted permission to access contacts, display this component to prompt them to grant permission.
        // Once permission is granted, remove this component from the UI and display the user's contacts.
        message ContactsPermissionNudge {
            // To apply bg color, corner radius etc
            types.ui.IconTextComponent.ContainerProperties card_container_properties = 1;
            // eg: Your contacts aren’t synced
            types.Text title = 2;
            // eg: Allows us to sync your contacts for easier, faster payments ⚡️
            types.Text description = 3;
            // tapping on the cta prompts the user to grant contacts permission, or navigate to device settings to enable contacts permission(iOS)
            types.ui.IconTextComponent cta = 4;
            types.VisualElement image = 5;
            // eg: Don’t worry, your data is end-to-end encrypted 🔒 Know more
            types.ui.IconTextComponent footer = 6;
        }
    }
}
