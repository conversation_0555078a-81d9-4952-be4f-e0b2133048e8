syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

enum DataExtractionMode {
  // mode for data extraction
  DATA_EXTRACTION_MODE_UNSPECIFIED = 0;
  // On the basis of sql query
  DATA_EXTRACTION_MODE_QUERY_BASED = 1;
  // On basis of given csv file of PII data and require other PII info
  DATA_EXTRACTION_MODE_ATTRIBUTES_BASED = 2;
}

enum QueryEngine {
  // query engine for data extraction
  QUERY_ENGINE_UNSPECIFIED = 0;
  // query will run in snowflake
  QUERY_ENGINE_SNOWFLAKE = 1;
  // query will run in presto
  QUERY_ENGINE_PRESTO = 2;
  // query will run in bigQuery
  QUERY_ENGINE_BIGQUERY = 3;
}

message SelfServeDataExtractionRequest {
  // monorail issue id for identify user's requirement
  int64 monorail_id = 1 [deprecated = true];
  // data extraction mode i.e. query based / attributes based
  DataExtractionMode data_extraction_mode = 2;
  // title for sql query
  string query_title = 3;
  // Query engine i.e. snowflake/ presto
  QueryEngine query_engine = 4;
  // final output provided to receiver's email
  string receiver_email = 5;
}
