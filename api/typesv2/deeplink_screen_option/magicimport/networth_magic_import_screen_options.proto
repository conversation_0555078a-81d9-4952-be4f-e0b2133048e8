syntax = "proto3";

package api.typesv2.deeplink_screen_option.magicimport;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/ui/header_bar.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/properties/visual_properties.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/magicimport";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.magicimport";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Deeplink : NETWORTH_MAGIC_IMPORT_SCREEN
message NetworthMagicImportScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 7;
  // Toolbar
  typesv2.HeaderBar header_bar = 1;
  ScanStateScreen scan_screen = 2;
  PreviewStateScreen preview_screen = 3;
  LoadingStateScreen loading_screen = 4;
  ErrorStateScreen network_error_screen = 6;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15699&t=Iy3TPAyrAFPn1jPi-4
message ScanStateScreen {
  // Snap & add anything to your networth
  common.Text title = 2;
  // Scan to know the value of any asset
  common.Text sub_title = 3;
  // For best results, scan one clear object at a time.
  common.Text message = 4;
  // Not an optional field
  PickerComponent picker_component = 5;
  // no of assets supported
  int32 no_of_assets_supported = 6;
  // Supported file types
  repeated SupportedFile supported_files = 7;
  // Max file size in kb for each file and if crosses we are not allowing to add that.
  // For images client will reduce to below this size
  // Total Max allowed size will be max_file_size_kb * no_of_assets_supported
  int32 max_file_size_kb = 8;
  // this will be shown to the user when permission for camera access is denied
  common.VisualElement permission_denial_placeholder = 9;
}

message PickerComponent {
  common.ui.widget.BackgroundColour background = 1;
  common.ui.widget.BackgroundColour border = 2;
  int32 corner_radius = 3;
  ui.IconTextComponent gallery_itc = 4;
  ui.IconTextComponent document_itc = 5;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15728&t=Iy3TPAyrAFPn1jPi-4
message PreviewStateScreen {
  // Ready to analyse its value?
  common.Text title = 1;
  // Scan more
  ScanMoreComponent scan_more = 2;
  // Analyse CTA
  ui.IconTextComponent analyse_cta = 3;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12409-15732&t=Iy3TPAyrAFPn1jPi-4
message LoadingStateScreen {
  // Ready to analyse its value?
  common.Text title = 1;
  // Loader animation
  // Using string as we want to use rive which visual element doesn't have support.
  string loader_animation_url = 2;
  // Data is safe and secure
  ui.IconTextComponent confidential_message = 4;
  // Frame on the picture when loading
  // This before image uploading
  common.VisualElement frame = 5;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12546-20031&t=snNA1uq3sIlGvlwB-4
// This is for no internet use case
message ErrorStateScreen {
  common.Text title = 1;
  common.Text message = 2;
  common.VisualElement icon = 3;
  ui.IconTextComponent retry_cta = 4;
}

message SupportedFile {
  // e.f, pdf, doc, jpg, png
  string file_extension = 1;
  // PDF only icon
  common.VisualElement icon_url = 2;
  // Preview image url
  common.VisualElement preview_img = 3;
}

message ScanMoreComponent {
  ui.sdui.properties.BorderProperty border = 1;
  common.VisualElement icon = 2;
  common.Text text = 3;
}
