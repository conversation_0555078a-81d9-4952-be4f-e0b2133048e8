syntax = "proto3";

package api.typesv2.deeplink_screen_option.frontend;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/validator.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/frontend";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.frontend";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Figma : https://www.figma.com/design/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=14542-41244&t=oUlGBCkhBDKdS8mO-4
// Screen Options for ADD_NOMINEE_DETAILS_SCREEN deeplink
message NomineeScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // entrypoint to be used for the screen
  // maps to typesV2.NomineeEntryPoint
  string entrypoint = 2;

  // Supported nominee document types
  repeated NomineeDocument nominee_documents = 3;

  message NomineeDocument {
    // typesV2.NomineeDocumentType
    string document_type = 1;
    // Document name e.g, for NOMINEE_DOCUMENT_TYPE_PAN it will be Pan
    string document_name = 2;
    // Document input place holder e,g, Enter your Pan for NOMINEE_DOCUMENT_TYPE_PAN
    string document_input_place_holder = 3;
    // Max length for the document, 0 will fallback to no length restriction
    int32 max_input_length = 4;
    // Validator for the document number
    repeated TextFieldValidator validator = 5;
  }
}
