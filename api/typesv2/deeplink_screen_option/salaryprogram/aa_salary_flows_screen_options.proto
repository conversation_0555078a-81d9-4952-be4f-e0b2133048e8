syntax = "proto3";

package api.typesv2.deeplink_screen_option.salaryprogram;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/document_upload/polling/polling_option.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/connected_account/coming_soon.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/version.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.salaryprogram";

// Helps in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message AaSalaryLandingScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=20580-51478&t=D2Vo6oWO3FqPjHyB-4
  string loader_text = 2;
}

message AaSalaryProgramFlowsTerminalScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // title & desc of terminal screen for any flow in aa salary program flows
  typesv2.common.Text title = 2;
  typesv2.common.Text desc = 3;
  // icon to be displayed in the center of the bottom sheet, this icon is based on the flow type and status(success/failures)
  typesv2.common.VisualElement status_based_icon = 4;
  // proceed_cta indicates the subsequent screen that the flow should navigate to.
  frontend.deeplink.Cta proceed_cta = 6;
  // aa_salary_program_terminal_screen states terminal screen name
  // figma for a sample state: https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20580-51258&mode=design&t=LCaSFfUaiubQKZSm-0
  // https://www.figma.com/design/HgHUMNbvNtQxNY72xt0CBH/%F0%9F%90%B0-Central-Growth-FFF?node-id=5199-47531&t=6mKN7q19PSHVnJUk-4
  string aa_salary_program_terminal_screen = 7;
  // background color of the bottom sheet
  typesv2.common.ui.widget.BackgroundColour bg_color = 8;
  // show_confetti is used whether to show confetti or not
  bool show_confetti = 9;
}

message AaSalaryProgramFlowsAmountTransferSetupScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // top_cashback_icon indicates the rupee cashback icon
  typesv2.common.VisualElement top_cashback_icon = 2;
  // title states the text with eligible cashback percentage
  typesv2.common.Text title = 3;
  // transfer_amount_cta takes to payment screen
  frontend.deeplink.Cta transfer_amount_cta = 4;
  // amount_selection_card is used to select the amount to be transferred, this also tells the cashback percentage
  AmountSelectionCard amount_selection_card = 5;
  // icon actions that navigates to back screen
  typesv2.common.VisualElement back_navigation_element = 6;
  // shows the info icon
  typesv2.ui.IconTextComponent info_navigation_element = 7;
  // animation for cashback
  // If the animation is empty, from client we should directly show the content.
  string animation = 8;
  // background color of the screen
  typesv2.common.ui.widget.BackgroundColour bg_color = 9;
  // Powered by federal bank
  typesv2.ui.IconTextComponent powered_by = 10;
  // ui version of the screen
  typesv2.Version version = 11;
  // back confirmation popup
  // This popup is shown when user clicks back (1st time).
  frontend.deeplink.InformationPopupOptions info_popup = 12;

  message AmountSelectionCard {
    typesv2.common.Text title = 1;
    typesv2.common.Text rupees_symbol_text = 2;
    typesv2.common.VisualElement edit_icon = 3;
    typesv2.common.ui.widget.BackgroundColour bg_color = 4;
    typesv2.Money initial_amount = 5;
    SliderOption slider_option = 6;
    BenefitsSubCard benefits_sub_card = 7;
    AmountInfo amount_info = 8;
    // Amount ranges based on which we decide users eligibility
    // Whether eligible for 1% cashback or 2% cashback
    repeated AmountRange amount_ranges = 9;
    // Suggested amount to be displayed to the user to select from amount user wants to transfer
    repeated typesv2.common.Text amount_suggestions = 10;
    // Index of the selected amount suggestion.
    int32 selected_suggestion_index = 11;
    message SliderOption {
      repeated SliderRanges slider_ranges = 1 [deprecated = true];
      repeated uint32 split_pointers = 2;
      // Deprecated in support of (`AmountRange`) message
      message SliderRanges {
        option deprecated = true;
        typesv2.Money min_value = 1;
        typesv2.Money max_value = 2;
        typesv2.common.Text tag = 3;
      }
    }
    message AmountInfo {
      typesv2.common.Text min_amount_display_val = 3;
      typesv2.common.Text max_amount_display_val = 4;
      typesv2.Money min_amount = 1;
      typesv2.Money max_amount = 2;
      // error text when user enters amount lesser than the range
      typesv2.common.Text min_amount_error = 5;
      typesv2.common.Text max_amount_error = 6;
    }
    message BenefitsSubCard {
      typesv2.common.ui.widget.BackgroundColour bg_colour = 1;
      typesv2.ui.IconTextComponent benefit1 = 2;
      typesv2.ui.IconTextComponent benefit2 = 3;
    }
  }

  // AmountRange represents a range of monetary values.
  // It includes a minimum value (`min_value`), a maximum value (`max_value`), and a tag (`tag`) to describe the cashback user eligible for.
  message AmountRange {
    typesv2.Money min_value = 1; // The minimum monetary value in the range.
    typesv2.Money max_value = 2; // The maximum monetary value in the range.
    typesv2.common.Text tag = 3;        // A tag to describe the cashback user eligible for. Used in v1.
    // A tag to describe the cashback user eligible for. This is used in v2 version of the screen.
    typesv2.ui.IconTextComponent tag_itc = 4;
  }
}

message AddFundsViaOffAppTransferScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.VisualElement top_icon = 2;
  typesv2.common.Text title = 3;
  repeated OffAppAddFundsInfoComponent info_components = 4;
  typesv2.common.VisualElement back_navigation_icon = 5;
  typesv2.ui.IconTextComponent info_icon = 6;
  frontend.deeplink.Cta cta = 7;
  typesv2.common.ui.widget.BackgroundColour bg_color = 8;
}

message OffAppAddFundsInfoComponent {
  typesv2.common.VisualElement serial_number = 1;
  typesv2.common.Text title = 2;
  oneof content {
    IconsList icons_list = 3;
    CopyText copy_text = 4;
  }
  typesv2.common.ui.widget.BackgroundColour bg_colour = 5;

  message IconsList {
    typesv2.common.Text header = 1;
    repeated typesv2.common.VisualElement app_icons = 2;
  }

  message CopyText {
    typesv2.common.Text text = 1;
    typesv2.common.ui.widget.BackgroundColour bg_colour = 2;
    typesv2.common.VisualElement copy_icon = 3;
    // e.g UPI_ID, ACCOUNT_NUMBER, IFSC etc.
    string type = 4;
  }
}

// screen options for AA_SALARY_SOURCE_OF_FUND_SCREEN
message AaSalarySourceOfFundScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.VisualElement top_icon = 2;
  // title of the screen eg - Verify your salary status
  typesv2.common.Text title = 3;
  // subtitle of the screen eg - To calculate cashback - verify your salary status
  typesv2.common.Text subtitle = 4;
  // data security label eg - Your data is safe & confidential
  typesv2.ui.IconTextComponent security_label = 5;

  message ConnectedAccountRowComponent {
    typesv2.common.VisualElement bank_icon = 1;
    typesv2.common.Text bank_account_label = 2;
    bool is_selected = 3;
    frontend.deeplink.Deeplink deeplink = 4;
  }

  message ConnectedAccountsComponent {
    // title eg - Choose salary account to auto-fetch bank statement
    typesv2.common.Text title = 1;
    // list of connected accounts
    repeated ConnectedAccountRowComponent connected_accounts = 2;
    // cta to connect new account
    typesv2.ui.IconTextComponent connect_new_account = 3;
    typesv2.ui.IconTextComponent partner_logo = 4;
  }
  ConnectedAccountsComponent connected_accounts_component = 6;
  repeated typesv2.common.ui.widget.CheckboxItem terms_info = 7;
  // component to show ITR coming soon
  typesv2.connected_account.ComingSoonComponent bottom_view = 8;
  // consume deeplink from ConnectedAccountRowComponent
  frontend.deeplink.Cta proceed_cta = 9;
}

message AASalaryDataPullScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.ui.widget.BackgroundColour screen_bg_color = 2;

  message UiData {
    typesv2.common.VisualElement icon = 1;
    typesv2.common.Text title = 2;
    typesv2.common.Text subtitle = 3;
    typesv2.ui.IconTextComponent retry_cta = 4;
    // deeplink should be set to nil, click gesture handled by client
    typesv2.ui.IconTextComponent close_cta = 5;
  }
  // data fetch or download/upload is in progress
  UiData progressState = 3;
  // data download/upload has failed
  UiData errorState = 4;
  // if polling option is nil client do not need to poll
  frontend.document_upload.polling.DocumentPollingOption polling_options = 5;
  // do not pull bank data if false
  bool pull_bank_data = 6;
  // on upload succeed redirect to this deeplink
  frontend.deeplink.Deeplink success_deeplink = 7;

  message AccountDataPullParams {
    // redirects to terminal screen in case of network errors and no api calls can be made
    frontend.deeplink.Deeplink network_error_deeplink = 1;
    // number of retried allowed in case of network error
    int32 max_retry_count_allowed = 2;
    repeated string consent_handles = 3;
  }
  AccountDataPullParams params = 8;
}
