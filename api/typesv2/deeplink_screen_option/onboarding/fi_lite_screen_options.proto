syntax = "proto3";

package api.typesv2.deeplink_screen_option.onboarding;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.onboarding";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message FeatureBenefitsScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  repeated Section sections = 2;
  repeated frontend.deeplink.Cta ctas = 3;
  typesv2.common.ui.widget.BackgroundColour background_colour = 4;
  typesv2.common.ui.widget.BackgroundColour divider_colour = 5;
  typesv2.common.VisualElement bg_image = 6;
  string feature_onboarding_entry_point = 7;
  // flag to decide whether to fetch data from RPC or use screen options instead
  typesv2.common.BooleanEnum to_fetch_screen_options_data = 8;
  // TNC text which is getting displayed above the CTA
  typesv2.common.Text tnc_text = 9;
  // Background color for the bottom section which have CTA and tnc text
  typesv2.common.ui.widget.BackgroundColour bottom_section_bg = 10;
}

// Entry point for the benefit screen, will be used to control redirection after onboarding is complete
enum FeatureOnboardingEntryPoint {
  FEATURE_ONBOARDING_ENTRY_POINT_UNSPECIFIED = 0;
  FEATURE_ONBOARDING_ENTRY_POINT_JUMP = 1;
  FEATURE_ONBOARDING_ENTRY_POINT_MF = 2;
  FEATURE_ONBOARDING_ENTRY_POINT_SD = 3;
  FEATURE_ONBOARDING_ENTRY_POINT_FD = 4;
  FEATURE_ONBOARDING_ENTRY_POINT_USS = 5;
  FEATURE_ONBOARDING_ENTRY_POINT_CC = 6;
  FEATURE_ONBOARDING_ENTRY_POINT_DC = 7;
  FEATURE_ONBOARDING_ENTRY_POINT_AUTO_PAY = 8;
  FEATURE_ONBOARDING_ENTRY_POINT_PL = 9;
  FEATURE_ONBOARDING_ENTRY_POINT_BOTTOM_NAV_BAR = 10;
}

message Section {
  oneof type {
    SectionTypeList section_type_list = 1;
    SectionTypeIcons section_type_icons = 2;
    SectionTypeText section_type_text = 3;
    SectionTypeHeader section_type_header = 4;
    SectionTypeCarousel section_type_carousel = 5;
    SectionTypeIconTextComponent section_type_itc = 6;
  }
}

message SectionTypeIconTextComponent {
  typesv2.ui.IconTextComponent section_itc = 1;
}

// https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=1696-94670&t=bBH0T1l6cGtjcfIN-4
message SectionTypeList {
  typesv2.common.Text section_header = 1;
  repeated typesv2.ui.IconTextComponent benefit_list_items = 2;
  typesv2.common.ui.widget.BackgroundColour bg_color = 4; // This controls the bg colour of the entire screen
}

// https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=1696-95144&t=bBH0T1l6cGtjcfIN-4
message SectionTypeIcons {
  typesv2.common.Text section_title = 1;
  typesv2.common.Text section_inner_title = 2;
  repeated typesv2.ui.VerticalIconTextComponent benefit_list_items = 3;
  int32 max_elements_per_row = 4;
  typesv2.common.ui.widget.BackgroundColour bg_color = 5;
}

message SectionTypeText {
  typesv2.ui.IconTextComponent text = 1;
  typesv2.common.ui.widget.BackgroundColour bg_color = 2;
}

message SectionTypeHeader {
  typesv2.common.VisualElement title_image = 1;
  typesv2.common.Text text_over_title = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text subtitle = 4;
  typesv2.common.ui.widget.Shadow title_image_shadow = 5;
  // Powered by Federal or Alpaca
  typesv2.ui.IconTextComponent powered_by = 6;
}

message SectionTypeCarousel {
  typesv2.common.Text section_title = 1;
  repeated typesv2.ui.VerticalIconTextComponent benefit_list_items = 2;
  typesv2.common.ui.widget.BackgroundColour bg_color = 3;
}

// screen options for ONBOARDING_INTENT_SELECTION
// https://www.figma.com/file/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-%E2%80%A2%C2%A0FFF-%E2%80%A2%C2%A0v1.2-%E2%80%A2-Oct-2022?node-id=20001%3A66397&mode=dev
message OnboardingIntentSelectionScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;

  typesv2.common.Text title = 2;
  typesv2.common.Text subtitle = 3;
  typesv2.common.ui.widget.BackgroundColour bg_color = 4;

  message IntentDetails {
    // intent identifier has to be sent to the API that will fetch
    // the next screen to display for this particular selection
    string intent_identifier = 1;
    typesv2.common.Text title = 2;
    typesv2.common.VisualElement icon = 3;
    message Tag {
      typesv2.common.Text text = 1;
      typesv2.common.ui.widget.BackgroundColour bg_color = 2;
    }
    repeated Tag tags = 4;
    reserved 5;
    // If a particular intent is not applicable for the user then this flag will disable the selection for them
    bool is_intent_disabled = 7;
    // If a particular intent is disabled then the following message will be shown to the user
    typesv2.ui.IconTextComponent failure_message = 6;
  }
  repeated IntentDetails intents = 5;

  // additional_info is the text displayed right above the "Next" CTA
  typesv2.common.Text additional_info = 7;

  repeated frontend.deeplink.Cta ctas = 6;

  // Any additional intent not already present in "intents" field, which can be selected by the user
  // Ex - Fi Lite intent details
  AdditionalIntentDetails additional_intent_details = 8;

  // Entry point from where user entered the ONBOARDING_INTENT_SELECTION screen
  // The entry point from intent screen deeplink should be passed via client to the SetOnboardingIntent call
  // string version of user.onboarding.IntentSelectionEntryPoint
  string entry_point = 9;

  // If default intent identifier is matching with any of the intent identifiers, it will be selected by default
  string default_intent_identifier = 10;
}

message AdditionalIntentDetails {
  // Intent identifier has to be sent to the API that will fetch
  // The next screen to display for this particular selection
  string intent_identifier = 1;
  // Details to be shown
  // Example - "I’m only looking to make UPI payments" text
  // NOTE - If there is a deeplink inside details call that on click of this component
  // Otherwise call SetOnboardingIntent API to set intent
  ui.IconTextComponent details = 2;
}

// screen options for SEND_SMS_DATA screen
message SendSmsDataScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // type of sms data to be sent
  repeated SmsDataType sms_data_types = 2;
  api.typesv2.common.Text loading_text = 3;
  message Footer {
    api.typesv2.common.Text text = 1;
    api.typesv2.common.VisualElement visual_element = 2;
    common.ui.widget.BackgroundColour bg_color = 3;
  }
  Footer footer = 4;
  frontend.deeplink.Deeplink deeplink = 5;
}

enum SmsDataType {
  SMS_DATA_TYPE_UNSPECIFIED = 0;
  SMS_DATA_TYPE_PAN = 1;
}

