syntax = "proto3";

package api.typesv2.deeplink_screen_option.onboarding;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/user.proto";
import "api/typesv2/money.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";

import "api/typesv2/address.proto";

import "api/frontend/deeplink/deeplink.proto";


option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/onboarding";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.onboarding";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// screen options for CONFIRM_CARD_MAILING_ADDRESS
// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=3007-12881&t=YVQoKyxLjIDwNxCR-0
message ConfirmCardMailingAddressOptions {
  typesv2.KYCLevel kyc_level = 1;
  // Deprecated: in favor of screen_title
  string title = 2 [deprecated = true];
  // Deprecated: in favor of screen_subtitle
  string subtitle = 3 [deprecated = true];
  string place_holder_for_name = 4;
  string place_holder_for_address = 5;
  // Corresponding to the address type selected by the user,
  // check box text should be updated accordingly.
  // If no check box text is present for the address type,
  // check box should not be shown.
  message CheckBoxText {
    api.typesv2.AddressType type = 1;
    string text = 2;
  }
  repeated CheckBoxText check_box_texts = 6;
  Flow flow = 7;
  enum Flow {
    FLOW_UNSPECIFIED = 0;
    FLOW_ONBOARDING = 1;
    // Figma link corresponding to debit card flow: https://www.figma.com/file/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=0%3A1&t=9qKFpwq2IcmVuFs0-0
    FLOW_DEBIT_CARD = 2;
  }
  // total amount to be paid by the user (inclusive of gst charges if any)
  api.typesv2.Money amount = 8;
  // unique identifier for card
  string card_id = 9;
  // Image corresponding to the CONFIRM_CARD_MAILING_ADDRESS_SCREEN
  api.typesv2.common.Image image = 10;
  // color associated with checkbox texts
  string checkbox_text_color = 11;
  // placeholder to be used to display the amount to be paid by user
  string place_holder_for_amount = 12;
  // Corresponding to the amount placeholder, a hint text is associated which can be stored as part of this field.
  string hint_text_amount = 13;
  // color associated with place holders
  string place_holder_color = 14;
  // color associated with the placeholder content
  string content_color = 15;
  // color associated with hint
  string hint_color = 16;
  // color associated with divider line
  string divider_color = 17;
  // color associated with edit icon
  string edit_icon_color = 18;
  // color associated with the card in the background encapsulating placeholders & related content
  string card_color = 19;
  // background color for CONFIRM_CARD_MAILING_ADDRESS_SCREEN
  string background_color = 20;
  // this encapsulates the title related details for CONFIRM_CARD_MAILING_ADDRESS_SCREEN
  api.typesv2.common.Text screen_title = 21;
  // this encapsulates the sub title related details for CONFIRM_CARD_MAILING_ADDRESS_SCREEN
  api.typesv2.common.Text screen_subtitle = 22;
  // the amount to be displayed to user as a break up of the card fee & gst value. This is set from backend.
  string display_amount = 23;
  // cta associated with CONFIRM_CARD_MAILING_ADDRESS_SCREEN
  frontend.deeplink.Cta cta = 24;
  // name to be shown on card mailing address screen
  // this will be used in case of debit card charges flow to show the name to be printed on the card
  string name = 25;
  // the message to be shown for address confirmation
  string address_confirmation_message = 26;
  // flag decides whether to hide the address field or not. eg, for NR onb address field won't be shown.
  bool hide_address_field = 27;
  // common header for all screen options
  api.typesv2.deeplink_screen_option.ScreenOptionHeader header = 28;

  frontend.deeplink.HeaderBar header_bar = 29;

  message PlaceHolder {
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
    api.typesv2.common.ui.widget.BackgroundColour border_color = 2;
    api.typesv2.common.Text label = 3;
    api.typesv2.common.Text value = 4;
    api.typesv2.common.Text explanatory_text = 5;
  }
  api.typesv2.common.Text debit_card_name_description = 30;
  PlaceHolder gender = 31;

  message PurposeOfSavingsAccount {
    api.typesv2.common.Text title = 1;
    repeated RadioOption radio_options = 2;
  }
  message RadioOption {
    // Option text (e.g., "Yes", "No")
    api.typesv2.common.Text options = 1;
    string purpose_of_savings_account_value = 2;
  }
  PurposeOfSavingsAccount purpose_of_savings_account = 32;
  api.typesv2.ui.IconTextComponent confirm_bottom_sheet_header = 33;
}
