syntax = "proto3";

package api.typesv2.deeplink_screen_option.pkg;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/ui/sdui/sections/section.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.pkg";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;


// Screen options for 'SDUI_BOTTOM_SHEET'
message SduiBottomSheetOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;

  // SDUI Content
  typesv2.ui.sdui.sections.Section section = 2;

  // If true, the bottom sheet should be rendered as a full-screen modal.
  bool is_full_screen = 3;
}
