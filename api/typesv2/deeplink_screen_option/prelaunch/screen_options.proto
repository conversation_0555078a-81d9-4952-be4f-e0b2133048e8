syntax = "proto3";

package api.typesv2.deeplink_screen_option.prelaunch;

import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/deeplink_screen_option/header.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/prelaunch";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.prelaunch";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

enum PreLaunchFlowType {
  PRE_LAUNCH_FLOW_TYPE_UNSPECIFIED = 0;
  PRE_LAUNCH_FLOW_TYPE_CREDIT_CARD = 1;
}

message PreLaunchScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  PreLaunchPage pre_launch_page = 2;
  // this will be the enum PreLaunchFlowType string value for each enum type
  string prelaunch_flow_type = 3;
}

message PreLaunchPage {
  // bg image of the pre-launch page
  api.typesv2.common.Image bg_image = 1;
  // bg color of the pre-launch page
  string bg_color = 2;
  api.typesv2.common.Text title = 3;
  api.typesv2.common.Text sub_title = 4;
  // list of tags to pitch benefits of US Stocks as a feature
  // eg: Invest in top global companies, Buy instantly; no brokerage, Industry-best forex rates
  repeated api.typesv2.ui.IconTextComponent benefits = 5;
  // eg: Be the first to invest in US stocks with us
  api.typesv2.ui.IconTextComponent bonus = 6;

  // CTA to record interest of user to use US stocks as a feature
  Button record_interest = 7;
  // Disabled CTA to signify that request has been submitted
  Button success = 8;
  // CTA to retry record interest action
  Button try_again = 9;

  // disclaimer_msg, success_msg, try_again_msg is shown on same UI position
  // If user has not already submitted interest, disclaimer msg is shown, after submitting interest if request was successful
  // success_msg is shown and in case of failure try_again_msg is shown
  // eg: By proceeding, I consent to Fi checking my LRS limit & eligibility (per RBI) to purchase international stocks.
  api.typesv2.common.Text disclaimer_msg = 10;
  // eg: We'll be in touch. We're super excited for you to try this feature.
  api.typesv2.common.Text success_msg = 11;
  // eg: Our servers are busy sorting submissions! Do retry.
  api.typesv2.common.Text try_again_msg = 12;
}

message Button {
  string bg_color = 1;
  api.typesv2.common.Text text = 2;
  // used to add status on button like whether button is enabled or disabled
  enum ButtonStatus {
    // keeping default behaviour enabled
    BUTTON_STATUS_ENABLED = 0;
    BUTTON_STATUS_DISABLED = 1;
  }
  ButtonStatus status = 3;
  // url for the image to be shown on the button
  string img_url = 4;
}
