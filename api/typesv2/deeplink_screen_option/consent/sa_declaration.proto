syntax = "proto3";

package api.typesv2.deeplink_screen_option.consent;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/form/consent.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/consent";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.consent";

// Helps in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// https://www.figma.com/design/0bX37LFjIEZ5TNTa5CA7UU/New-Onboarding-Workfile?node-id=3107-12167&t=Z7Ea1FAM7u6v5HyW-0
message SaDeclarationScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.deeplink.HeaderBar header_bar = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text sub_title = 4;
  repeated SAConsentCard cards = 5;
  repeated frontend.deeplink.Cta ctas = 7;
  typesv2.ui.IconTextComponent cta_header = 8;
}

message SAConsentCard {
  // https://www.figma.com/design/wcXh7A8xXrUIOyzsuoFXy4/%F0%9F%9B%A0%EF%B8%8F-Onboarding-%2F-Workfile?node-id=27821-27722&t=66nWfx4QivkHoB2b-0

  message InfoButton {
    // Visual element for the info icon (typically an "i" icon)
    typesv2.common.VisualElement icon = 1;
    // Deeplink to navigate to help/info screen
    frontend.deeplink.Deeplink deeplink = 2;

    // Whether the info button should be visible (some declarations may not need info)
    bool is_visible = 5;
  }

  message CheckBoxCard {
    typesv2.form.Consent consents = 1;
    typesv2.common.ui.widget.BackgroundColour bg_color = 2;
    typesv2.common.ui.widget.BackgroundColour border_color = 3;
    typesv2.common.Text title = 4;
    typesv2.common.Text description = 5;
    // Optional explanatory text shown below the checkbox (e.g., for PEP declaration)
    typesv2.common.Text explanatory_text = 6;
    InfoButton info_button = 7;
  }
  // Radio button options for declarations requiring Yes/No choice (e.g., accessibility declaration)
  message RadioButtonCard {
    // The question or statement text (e.g., "I am a differently abled person")
    typesv2.common.Text title = 1;
    typesv2.common.Text description = 2;
    RadioOption radio_option_yes = 3;
    RadioOption radio_option_no = 4;
    typesv2.common.ui.widget.BackgroundColour bg_color = 5;
    typesv2.common.ui.widget.BackgroundColour border_color = 6;
    // List of consent ids that need to be sent back to backend
    repeated string consent_ids = 7;
    // Whether this is a mandatory field
    bool is_mandatory = 8;
    // Info button for providing additional context about the radio button question
    InfoButton info_button = 9;

    message RadioOption {
      // Option text (e.g., "Yes", "No")
      typesv2.common.Text text = 1;
      // Deeplink will be passed for yes with a bottom screen
      frontend.deeplink.Deeplink radio_option_deeplink = 2;
      typesv2.ui.IconTextComponent cta_header = 3;
      bool enable_cta = 4;
    }
  }
  oneof card {
    CheckBoxCard check_box_card = 1;
    RadioButtonCard radio_button_card = 2;
  }
}

