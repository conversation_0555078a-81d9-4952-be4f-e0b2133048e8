syntax = "proto3";

package api.typesv2.deeplink_screen_option.auth;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/ui/header_bar.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/auth";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.auth";


// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message AuthTotpScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Purpose to pass in generate totp rpc
  // auth.totp.enums.Purpose.String()
  string purpose = 2;
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 3;
  // eg: Fi MCP
  HeaderBar title_bar = 4;
  // eg: Key logo for net worth mcp
  common.VisualElement main_logo = 5;
  // eg: Your Fi MCP code
  common.Text code_title_text = 6;
  // eg: 123456
  // Text will initially be dummy and client will overwrite when rpc call is successful
  common.Text code_text = 7;
  // eg: Expires in 24 sec
  // Text will initially be dummy and client will overwrite when rpc call is successful based on expiry timestamp
  common.Text expiry_text = 8;
}
