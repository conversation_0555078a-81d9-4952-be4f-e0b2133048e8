syntax = "proto3";

package api.typesv2.deeplink_screen_option.assetandanalysis;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/investment/ui/landing_page.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/assetandanalysis";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.assetandanalysis";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// screen options for ASSET_LANDING_PAGE
message AssetLandingPageScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // request params for asset landing page
  frontend.investment.ui.LandingPageRequestParams params = 2;
}

// Screen options for Asset_IMPORT_STATUS_POLLING_SCREEN
// https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=121-9682&t=TMDdzjxiprl4u9IS-4
message AssetImportStatusPolingScreenOptions {
  enum FlowType {
    FLOW_TYPE_UNSPECIFIED = 0;
    FLOW_TYPE_WB_ONBOARDING = 1;
    FLOW_TYPE_MF_REFRESH = 2;
    FLOW_TYPE_MAGIC_IMPORT = 3;
  }
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  string asset_type = 2;
  string flow_id = 3;
  string background_color = 4;
  string background_lottie_url = 5;
  // this defines the initial state of the loading animation
  AssetImportStatusDetails import_status_details = 6;
  // in case polling times out without a terminal state, then client will use this to show the in progess state
  AssetImportTerminalInProgressDetails import_in_progress_details = 7;
  // in case of any failures, then client will use this to show the in error state
  AssetImportTerminalFailureDetails import_failure_details = 8;
  PollingData polling_details = 9;
  // current networth of user before importing mf
  api.typesv2.Money current_networth = 10;
  // Maps to FlowType enum
  string flow_type = 11;
  // Flow specific payload which is required to check asset import status or construct deeplink
  bytes payload = 12;
  message PollingData {
    // polling should be done in this intervals
    int32 polling_interval = 1;
    // max duration for which the client should poll
    int32 max_polling_duration = 2;
  }
}

message AssetImportStatusDetails {
  // Title stating the current status of data fetch
  api.typesv2.common.Text title = 1;
  // Subtitle stating details of data fetch status
  api.typesv2.common.Text subtitle = 2;
  DataFetchLottieDetails lottie_details = 3;
  FooterDetails footer_details = 4;
  message FooterDetails {
    api.typesv2.common.Text content = 1;
    api.typesv2.common.VisualElement right_image = 2;
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  }
}

message DataFetchLottieDetails {
  int32 start_frame = 2;
  int32 end_frame = 3;
  bool should_loop = 4;
}

// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=158-7623&t=66Wt92ulqSVdD9D2-4
message AssetImportTerminalInProgressDetails {
  // text to be shown above the animation eg Net Worth
  api.typesv2.common.Text title = 1;
  // used to update how many funds are connected
  ui.VerticalKeyValuePair assets_update = 2;
  frontend.deeplink.Cta exit_cta = 3;
  // based on the status this will pass frames to be loaded on the lottie
  DataFetchLottieDetails lottie_details = 4;
  // client will use this to show share cta in nav bar top right corner
  api.typesv2.ui.IconTextComponent share_cta = 5;
}

// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=137-7836&t=66Wt92ulqSVdD9D2-4
message AssetImportTerminalFailureDetails {
  api.typesv2.common.Text error_title = 1;
  api.typesv2.ui.IconTextComponent error_message = 2;
  api.typesv2.common.VisualElement error_image = 3;
  repeated frontend.deeplink.Cta ctas = 4;
  DataFetchLottieDetails lottie_details = 5;
}

// Figma : https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=13604-13207&t=Gyk1mAcTx6b3G9sr-4
// Deeplink : WEALTH_SDUI_BOTTOM_SHEET
message WealthSduiBottomSheetScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 2;
  api.typesv2.common.VisualElement close_icon = 3;
  api.typesv2.ui.sdui.sections.Section content = 4;
}

// Deeplink Screen: EXPORT_NETWORTH_DATA
message ExportNetworthDataScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  repeated string allowed_android_apps = 2;
  repeated string allowed_ios_apps = 3;
}
