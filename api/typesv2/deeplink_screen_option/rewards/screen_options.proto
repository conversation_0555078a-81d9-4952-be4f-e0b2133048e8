syntax = "proto3";

package api.typesv2.deeplink_screen_option.rewards;

import "api/frontend/rewards/pkg/display_components.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/components/countdown_timer.proto";
import "api/typesv2/common/ui/components/info_sections.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/rewards";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.rewards";

// Helps in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message RewardOfferDetailsScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // id of rewardOffer whose details need to be shown.
  string reward_offer_id = 2;
}

// Screen options for the Claimed Reward details screen:
// https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?type=design&node-id=12244-79466&mode=design&t=CmtuBRklVbY3rWEx-0
message ClaimedRewardDetailsScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 2;
  // rewardId/exchangerOfferOrderId can be passed for fetching reward or exchanger reward respectively
  string reward_id = 1;
}


// generic bottom sheet used for rewards catalog redemption use cases
// figma -> https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11134-5784&node-type=section&t=RyOPmHuEw3RYqWZy-0
// deeplink : CATALOG_OFFER_REDEMPTION_BOTTOM_SHEET in api/frontend/deeplink/deeplink.proto
message CatalogOfferRedemptionBottomSheetScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  oneof bottom_sheet_data {
    // bottom sheet to show the generic redemption/inoperable info etc
    OfferRedemptionBottomSheetData offer_redemption_bottom_sheet_data = 2;
    // custom action data to fetch a specific bottom sheet like fi coin conversion,address input
    // Deeplink called -> Custom action performed while being on this bottom sheet deeplink which is currently an rpc call which fetches a new deeplink -> new deeplink is opened
    OfferRedemptionCustomActionBottomSheetData offer_redemption_custom_action_bottom_sheet_data = 3;
  }
  // analytics details required for the bottom sheet
  AnalyticsDetails analytics_details = 4;
}

// generic bottom sheet data for initiating offer redemption
// figma -> https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-8262&node-type=frame&t=3mz9KqdYwv0sjXia-0
message OfferRedemptionBottomSheetData {
  // title of the generic bottom sheet Eg: Not enough Fi-Coins, making it ITC for supporting icons in future
  ui.IconTextComponent title = 1;
  // icon text component to show the fi coins count in bottom sheet Eg : 1,00,000 Fi-Coins
  ui.IconTextComponent banner = 2;
  // description of the offer bottom sheet dialog
  // EG:The Fi-Federal salary account comes with exclusive offers and epic benefits
  repeated ui.IconTextComponent descriptions = 3;
  // generic bottom cta for any primary action
  // located at bottom right of the bottom sheet
  // Eg : Earn Money Plants, Next, Confirm, View Fi Salary benefits, Okay, Pay Emi( Loan intermediary screen use case) etc
  frontend.rewards.pkg.Cta next_action_cta = 5;
  // back button cta [Optional]
  frontend.rewards.pkg.Cta back_cta = 6;
  // cross icon Cta [Optional]
  frontend.rewards.pkg.Cta close_cta = 7;
  // offer id
  string entity_id = 8;
  // generic bottom cta for any secondary action
  // located at bottom right of the bottom sheet
  // Eg : Spend Fi-Coins etc
  frontend.rewards.pkg.Cta secondary_action_cta = 9;
}

// AnalyticsDetails stores the details required for analytic events for the bottom sheet
message AnalyticsDetails {
  // generic map of key value pairs for event properties for the banner
  // e.g. { "offerId": "offer-id-1", "category_tag": "CATEGORY_TAG_VOUCHERS" }
  map<string, string> event_properties = 1;
}

message OfferRedemptionCustomActionBottomSheetData {
  frontend.rewards.pkg.CustomAction custom_action = 1;
}

// Addrress Selection Bottom sheet
// figma -> https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9073&node-type=frame&t=3mz9KqdYwv0sjXia-0
// deeplink : REWARDS_ADDRESS_SELECTION_BOTTOM_SHEET in api/frontend/deeplink/deeplink.proto
message RewardsAddressSelectionBottomSheetScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 2;
  AddressBottomSheetData address_bottom_sheet_data = 1;
}

// this data consists of list of addresses and rewards offer id we are going to redeem
message AddressBottomSheetData {
  ConfirmAddressSection confirm_address_section = 1;
  // bottom sheet initiate offer redemption post address selection
  OfferRedemptionBottomSheetData confirm_order_section = 3;
  // offer id
  string entity_id = 4;
  // Eg: REWARDS_PHYSICAL_MERCHANDISE_SHIPPING
  // will be passed back to BE while updating address
  AddressType address_type = 5;

  // figma -> https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9225&t=RyOPmHuEw3RYqWZy-4
  message ConfirmAddressSection {
    // title of the dialog. Eg: First, confirm your delivery address
    api.typesv2.common.Text title = 1;
    // sub title of the dialog. Eg: Your selected merchandise will be delivered here
    api.typesv2.common.Text sub_title = 2;
    // address background color
    common.ui.widget.BackgroundColour bg_color = 3;
    // selected address title Eg: Delivery Address
    api.typesv2.common.Text delivery_address_title = 4;
    // edit icon
    common.VisualElement edit_icon = 5;
    // next cta
    frontend.rewards.pkg.Cta next_action_cta = 6;
    // bottom sheet with list of user addresses, user can select any address from the list
    ChangeAddressSection change_address_section = 7;

    // change address section contains lost of addresses where user can select or change the address
    // will be shown on clicking edit button
    message ChangeAddressSection {
      // title of the dialog. Eg: First, confirm your delivery address
      api.typesv2.common.Text title = 1;
      // list of addresses from the BE for the selected user
      repeated PostalAddress address = 3;
      // add address cta, cta action is client hardcoded
      frontend.rewards.pkg.Cta add_address_cta = 4;
    }
  }
}



// View Details screen options for particular offer.
// figma ->  https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9239&node-type=frame&t=ocYBnZsxF4kdCkhM-0
// deeplink : OFFER_DETAILS_BOTTOM_SHEET in api/frontend/deeplink/deeplink.proto
message OfferDetailsBottomSheetScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // title of the bottom sheet ex: 'Offer Details'
  api.typesv2.common.Text title = 2;
  // section displayed on top of the Offer details bottom sheet
  TopSection top_section = 3;
  // list of all the sections
  repeated api.typesv2.common.ui.components.SectionDetails offer_details_section = 4;
  // cta to call the next action based on deeplink or custom action
  frontend.rewards.pkg.Cta cta = 5;
  // optional countdown timer that we may want to show
  // figma - https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&mode=design&t=L8BZNVzqWgPOKUtu-0
  api.typesv2.common.ui.components.CountdownTimer countdown_timer = 6;
  // unique id of the offer
  string offer_id = 7;
  // https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9239&node-type=frame&t=ocYBnZsxF4kdCkhM-0
  message TopSection {
    // contains title of the offer e.g. 'Swiggy ₹500 gift voucher'
    api.typesv2.common.Text offer_title = 1;
    // contains logo/brand-image of the offer
    api.typesv2.common.VisualElement offer_logo = 2;
    // background color of this section
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 3;
    // contains reference product image of the offer
    // https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11485-9239&node-type=frame&t=y1JankHlPRClnFBN-0
    api.typesv2.common.VisualElement offer_product_image = 4;
  }
}

// Reward Claim screen options for a specific reward
// figma -> https://www.figma.com/design/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=5535-47031&t=Cm0hCOjgfAkh5aCK-4
// deeplink : REWARD_CLAIM_SCREEN in api/frontend/deeplink/deeplink.proto
message RewardClaimScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // unique id of the reward
  string reward_id = 2;
}

// Reward Claim Success screen options for a specific reward
// Note : Currently only for fi-coins
// figma -> https://www.figma.com/design/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=8073-19980&t=MY1jWYlSz8xJsL1Z-4
// deeplink : REWARD_CLAIM_SUCCESS_SCREEN
message RewardClaimSuccessScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;

  PageUiDetails page_ui_details = 2;

  RewardDetails reward_details = 3;

  // Sections to display SDUI offer catalog or can be other promotions
  api.typesv2.ui.sdui.sections.Section promo_section = 4;

  // Primary CTA for the screen
  BottomSection bottom_section = 5;

  // Contains the UI details for the screen like background color, bg image etc
  message PageUiDetails {
    // Background color of the screen
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;

    // The background image to be rendered on the screen
    api.typesv2.common.VisualElement top_bg_image = 2;
  }

  // Reward details to be displayed on the screen like title, reward icon, reward value etc
  message RewardDetails {
    // Title to render on the claim success screen
    // e.g. Total Fi Coins
    api.typesv2.common.Text title = 1;

    // The claimed reward icon/visual to be rendered on the screen
    // e.g. Fi coins icon
    api.typesv2.common.VisualElement reward_icon = 2;

    // Reward value text to be shown on the claim reward screen
    // e.g. For fi-coin reward, this will be updated fi coins balance
    api.typesv2.common.Text reward_value_text = 4;

    // The subtitle for the claimed reward
    // e.g. +150 Fi-Coins earned on making UPI payments last month
    api.typesv2.common.Text reward_subtitle = 7;
  }

  // BottomSection contains the CTA for the screen
  message BottomSection {
    // Primary CTA for the screen
    // e.g. Go to Gift Card Store
    frontend.rewards.pkg.Cta cta = 1;
  }
}

message EarnedRewardsHistoryScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Unique identifier for the flow in which screen is to be rendered on UI
  // EX: CREDIT_CARD_EARNED_BENEFITS
  // api.typesv2.rewards.EarnedRewardsHistoryDomainId.String()
  string earned_rewards_history_domain_id = 2;
}

