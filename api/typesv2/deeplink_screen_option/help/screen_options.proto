syntax = "proto3";

package api.typesv2.deeplink_screen_option.help;

import "api/frontend/inapphelp/contact_us/contact_us.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/common/text.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/help";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.help";
// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Screen options for CX Support Ticket Detail Screen.
message CXTicketDetailScreenOptions {
  // Common header all screens
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Mandatory field to RPC, which is required for RPC.
  int64 ticket_id = 2;
}

// Screen options for CX Recent Activity Details screen
// Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5727-12519&t=EI08GYM4GCjSSz8P-0
message CXRecentActivityDetailsScreenOptions {
  // Common header all screens
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Field required for RPC to get recent activity Details
  string activity_id = 2;
}

// Screen options for all recent activities screen
// Figma : https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=5727-12199&t=KDtEn9DuZ10X9QxN-0
message CXHelpRecentActivitiesScreenOptions {
  // Common header all screens
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // denotes if search bar is to be shown to the user
  bool is_search_bar_shown = 2;
}

// Screen options for query screen config
message CXHelpQueryScreenOptions {
  // Common header all screens
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // title of the screen which will be displayed on top of the search bar on the search screen
  api.typesv2.common.Text screen_title = 2;
  // criteria on which user query should be validated
  frontend.inapphelp.contact_us.QueryValidationConfig query_validation_config = 3;
}
