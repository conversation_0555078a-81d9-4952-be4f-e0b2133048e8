syntax = "proto3";

package api.typesv2.deeplink_screen_option.usstocks.screen_options_v2;

import "api/typesv2/deeplink_screen_option/header.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/usstocks/screen_options_v2";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.usstocks.screen_options_v2";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Screen options for USSTOCKS_LANDING_SCREEN
message LandingPageScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Maps to frontend.usstocks.LandingScreenEntryPoint
  string entry_point = 2;
}
