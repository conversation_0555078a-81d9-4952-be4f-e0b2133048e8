syntax = "proto3";

package api.typesv2.deeplink_screen_option.firefly;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/firefly/cards.proto";
import "api/frontend/firefly/drawable_properties.proto";
import "api/frontend/firefly/enums/enums.proto";
import "api/frontend/firefly/service.proto";
import "api/frontend/pay/transaction/service.proto";
import "api/typesv2/address.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/deposit.proto";
import "api/typesv2/firefly.proto";
import "api/typesv2/money.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/text_with_hyperlinks.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/user.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/firefly";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.firefly";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message CreditCardDetailsAndBenefitsScreenOptions {
  // mandatory field needed for screenOptionV2 compatibility
  deeplink_screen_option.ScreenOptionHeader header = 1;

  string screen_title = 2;
  string card_image_url = 3;
  string credit_limit_str = 4;
  frontend.deeplink.InfoItemWithCta brands = 5;
  frontend.deeplink.InfoItemWithCta valueback_cheatsheet = 6;
  frontend.deeplink.InfoItemWithCta welcome_vouchers = 7;
  repeated frontend.deeplink.InfoItem static_images = 8;

  repeated frontend.deeplink.InfoItemWithCtaV2 bottom_info_items = 9;

  frontend.deeplink.Cta share = 10;
  // text to be used while sharing
  string text_for_share = 11;
  typesv2.common.Text share_credit_limit_text = 12;
}

message CreditCardAddressSelectionV2ScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.deeplink.StepInfo step_info = 4;
  frontend.deeplink.Cta continue_cta = 6;
  string card_request_id = 7;
  typesv2.KYCLevel kyc_level = 9;
  frontend.deeplink.InfoItemV3 header_details = 10;
  frontend.deeplink.InfoItemWithCtaV3 add_address_cta_v2 = 11;
  // deprecated in favour of header_details
  string text = 2 [deprecated = true];
  // deprecated in favour of header_details
  string sub_text = 3 [deprecated = true];
  // deprecated in favour of header_details
  string delivery_address_icon_url = 5 [deprecated = true];
  // deprecated in favour of add_address_cta_v2
  frontend.deeplink.InfoItemWithCtaV2 add_address_cta = 8 [deprecated = true];
  typesv2.ui.IconTextComponent header_icon_text_component = 12;
}

message CreditCardRealTimeEligibilityCheckIntroScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.VisualElement image_top = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text sub_title = 4;
  frontend.deeplink.Cta check_credit_score_cta = 5;
  typesv2.common.VisualElement image_bottom = 6;
}

message SecuredCreditCardDepositScreenOptions {
  // mandatory field needed for screenOptionV2 compatibility
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.deeplink.StepInfo step_info = 2;
  frontend.deeplink.InfoItemWithCtaV2 top_section_details = 3;
  typesv2.Money min_deposit_amount = 4;
  frontend.deeplink.InfoItemV2 bottom_text = 5;
  frontend.deeplink.Cta insufficient_balance_cta = 6;
  frontend.deeplink.Cta open_deposit_cta = 7;
  typesv2.common.Text ineligible_action_text = 8;
  frontend.deeplink.Cta add_nominee_cta = 9;
  typesv2.common.VisualElement add_nominee_visual_element = 10;
  typesv2.common.Text terms_and_conditions_text = 11;
  typesv2.Money default_credit_limit = 12;
  repeated typesv2.Money suggested_credit_limits = 13;
  typesv2.common.Text credit_limit_heading = 14;
  typesv2.common.Text deposit_limit_heading = 15;
  typesv2.Money max_deposit_amount = 16;
  typesv2.common.Text min_deposit_text = 17;
  typesv2.common.Text max_deposit_text = 18;
  frontend.deeplink.InfoToolTipV2 info_tooltip_credit_limit = 19;
  frontend.deeplink.InfoToolTipV2 info_tooltip_fixed_deposit = 20;
  frontend.deeplink.VisualElementCta benefits_visual_element_cta = 21;
}

message SecuredCreditCardFdDetailsScreenOptions {
  // mandatory field needed for screenOptionV2 compatibility
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.deeplink.StepInfo step_info = 2;
  frontend.deeplink.InfoItemV3 header_details = 3;
  repeated frontend.deeplink.InfoItemV3 fd_details = 4;
  frontend.deeplink.Cta next_cta = 5;
  typesv2.DepositStatus fd_creation_status = 6;
  string card_request_id = 7;
  typesv2.common.VisualElement ve_bank_logo = 8;
}

message SecuredCcDepositTenureSelectionBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.Text heading_text = 2;
  typesv2.common.Text tenure_days_text = 3;
  typesv2.common.Text tenure_month_text = 4;
  repeated DepositInterestDetails slider_term_details = 5 [deprecated = true];
  DepositTerm selected_deposit_term = 6;
  frontend.deeplink.Cta confirmation_cta = 7;
  typesv2.common.Text min_deposit_term_error_text = 8;
  typesv2.common.Text max_deposit_term_error_text = 9;
  // Needed for ios component reuse, android will also start using this
  repeated typesv2.DurationSliderValue duration_slider_values = 10;
}

message SecuredCcDetailsBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.deeplink.InfoItemV3 header_details = 2;
  repeated typesv2.common.TextWithIcon feature_details = 3;
  string feature_bg_color = 4;
}

// Figma link : https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-19690&mode=dev,
// Figma link: https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-19763&mode=dev
message CcIntroBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.Text title = 2;
  typesv2.common.Text sub_title = 3;
  typesv2.common.ui.widget.BackgroundColour background_colour = 4;
  frontend.firefly.PromotionInfo promotion = 5;
  frontend.firefly.WrappedButtonInfo button = 6;
  typesv2.common.VisualElement visual_element = 7;
}

// Figma link : https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=1399-20302&mode=dev
message CcIntroScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  int32 screen_identifier = 2;
  // Deprecated: in favour of typesv2.CreditCardRequestHeader
  frontend.firefly.enums.CardProgramType card_program_type = 3;
  // Deprecated: in favour of typesv2.CreditCardRequestHeader
  typesv2.CardProgram card_program = 4;
  typesv2.CreditCardRequestHeader credit_card_request_header = 5;
}

message FireflySyncPollStatusScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  string card_request_id = 2;
  // deprecated in favour of display_message_object
  string display_message = 3 [deprecated = true];
  // delay for the next attempt in milliseconds
  int32 retry_delay = 4;
  string workflow_id = 5;
  // url of the image that is displayed at the top half of the screen .
  // If this is passed as non-empty, the rest of the screen contents are pushed
  // down a bit
  typesv2.common.VisualElement screen_image = 6;
  // text to be displayed representing the process happening in the backend .
  // eg. Hold on! Your Credit Card is getting created
  typesv2.common.Text display_message_object = 7;
  // text to be sent conditionally that is displayed below the display message.
  // condition can be anything, eg. unexpected delay while processing
  typesv2.common.Text sub_text = 8;
  // This will be passed in the deeplink so that the client is able to provide that
  // in the next API call . This is being done to keep a track of the number of
  // polling attempts that have been done
  int32 attempt_number = 9;
  // background color for the entire screen
  string bg_color = 10;
}

message CreditCardSyncPollStatusScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  string card_request_id = 2;
  string workflow_id = 3;
  // url of the image that is displayed at the top half of the screen .
  // If this is passed as non empty, the rest of the screen contents are pushed
  // down a bit
  typesv2.common.VisualElement screen_image = 4;
  // text to be displayed representing the process happening in the backend .
  // eg. Hold on! Your Credit Card is getting created
  typesv2.common.Text display_message = 5;
  // text to be sent conditionally that is displayed below the display message.
  // condition can be anything, eg. unexpected delay while processing
  typesv2.common.Text sub_text = 6;
  // This will be passed in the deeplink so that the client is able to provide that
  // in the next API call . This is being done to keep a track of the number of
  // polling attempts that have been done
  int32 attempt_number = 7;
  // delay for the next attempt in milliseconds
  int64 retry_delay = 8;
}

// Figma: https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite?type=design&node-id=9701-42332&mode=design&t=2EHzNmCp0NDQRrgc-4
message CcAmpliFiScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.firefly.WrappedIconTextToolBar tool_bar = 2;
  frontend.firefly.WrappedTextInfo title = 3;
  frontend.firefly.WrappedTextInfo sub_title = 4;
  frontend.firefly.WrappedAmpliFiCard ampli_fi_card = 5;
  typesv2.common.ui.widget.BackgroundColour divider_colour = 6;
  frontend.firefly.AmpliFiBenefit ampli_fi_benefit = 7;
  frontend.firefly.WrappedHyperLinksWidget text_with_hyperlinks = 8;
  frontend.firefly.SliderProperties slider_properties = 9;
  frontend.firefly.WrappedVisualElement footer_icon = 10;
  frontend.firefly.DrawableProperties screen_background_colour = 11;
  typesv2.AddressType address_type = 12;
  typesv2.KYCLevel kyc_level = 13;
  string card_request_id = 14;
}

// figma: https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=2539-20531&mode=design&t=G1MP0tw5tHn8d1n0-0
message CcIneligibleUserScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.deeplink.InfoItemV3 top_section_details = 2;
  typesv2.common.VisualElement separator_visual_element = 3;
  typesv2.common.Text bottom_section_heading = 4;
  repeated IneligibleUserDetails ineligible_user_details = 5;
  typesv2.common.Text footer_text = 6;
  frontend.deeplink.Cta main_cta = 7;
}

message IneligibleUserDetails {
  frontend.deeplink.InfoItemWithCtaV3 bottom_section_details = 1;
  bool is_collapsed = 2;
  typesv2.common.VisualElement chevron = 3;
}


// screen options for CREDIT_CARD_BILLING_DETAILS_BOTTOM_VIEW_SCREEN
message CreditCardBillingDetailsBottomViewScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.Text screen_heading = 2;
  repeated frontend.deeplink.InfoItemV2 billing_infos = 3;
  string bg_color = 4;
}

// screen options for CC_BILL_GENERATION_DATE_SELECTION_SCREEN
message CcBillGenerationDateSelectionScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // [OPTIONAL] Card id will be required for cases where the billing info has to be
  // displayed for update after card creation
  string card_id = 2;
  // bool to specify if the billing dates selection is for a newly onboarding user or
  // is it an update for an already onboarded user
  bool is_update = 3;
}

// screen options for CREDIT_CARD_TPAP_PAYMENT_INIT_SCREEN
message CreditCardTpapPaymentInitScreenOption {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // attributes containing parameters to initiate a third party application payment flow
  frontend.pay.transaction.TransactionAttribute transaction_attributes = 2;
  // order id for the order which has been created to initiate payment
  string order_id = 3;
  // type of pin authorization that needs to be done for the payment
  frontend.pay.transaction.PinRequiredType pin_required_type = 4;
}

// screen options for CC_NETWORK_SELECTION_SCREEN
// figma - https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-Credit-Card-%E2%80%A2-FFF?type=design&node-id=4751-30530&mode=design&t=J4NxEIKvEI7Qj1v3-4
message CcNetworkSelectionScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.Text header_text = 2;
  typesv2.common.Text description = 3;
  repeated NetworkSelectionComponent network_selection_component = 4;
  message NetworkSelectionComponent {
    typesv2.ui.VerticalIconTextComponent vertical_icon_text_component = 1;
    frontend.firefly.enums.CardNetworkType card_network_type = 2;
  }
  frontend.deeplink.Cta primary_cta = 5;
}

message CardTabScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;

  enum SelectedTab {
    CREDIT_CARD = 0;
    DEBIT_CARD = 1;
  }

  // The selected tab
  SelectedTab selected_tab = 2;

  // Create map of SelectedTab and Deeplink
  map<string, frontend.deeplink.Deeplink> tab_to_deeplink = 3;
}
// screen options for CC_CONSENT_BOTTOM_SHEET_SCREEN
// figma : https://www.figma.com/file/yaHMO53FKTiBzJWHAKnbOf/%F0%9F%92%B3-AmpliFi-Credit-Card-%E2%80%A2-FFF?type=design&node-id=6226-23210&mode=design&t=qRSwyCrbsNCWRuU4-4
message CcConsentBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // title for the bottom sheet
  typesv2.common.Text title = 2;
  // list of consent items to be shown in the Consent Bottom Sheet
  repeated ConsentItem consent_items = 3;
  // visual element for the card program at the bottom of the consent items
  common.VisualElement program_visual_element = 4;
  // cta for the consent
  frontend.deeplink.Cta consent_cta = 5;
  // container properties for the consent list items
  frontend.firefly.DrawableProperties consent_container_properties = 6;
  // Deprecated in favour of hyper text field : consent_text
  // consent checkbox
  typesv2.common.ui.widget.CheckboxItem consent_checkbox_text = 7 [deprecated = true];
  // Deprecated in favour of consent_check_box having CreditCardOnboardingConsent enum
  // consent for credit card onboarding given by user
  frontend.firefly.enums.CreditCardOnboardingConsent credit_card_onboarding_consent = 8 [deprecated = true];
  // checkable consent with consent type
  frontend.firefly.ConsentBox consent_check_box = 9;
  // hyper text consent text
  typesv2.ui.TextWithHyperlinks consent_text = 10;
}

// message to contain the data for the consent items being rendered on the consent bottom sheet
message ConsentItem {
  // title for the consent item
  typesv2.common.Text title = 1;
  // sub title text for the bottom sheet, hyperlinks to support showing links in the sub title
  typesv2.ui.TextWithHyperlinks sub_title = 2;
  // left icon for the consent item
  common.VisualElement left_visual_element = 3;
  // bottom icon to show the welcome offer banner for the items
  common.VisualElement bottom_visual_element = 4;
}

message CcCreditReportAddressScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  frontend.firefly.WrappedIconTextToolBar tool_bar = 2;
  frontend.deeplink.InfoItemV3 top_section = 3;
  frontend.firefly.DrawableProperties address_section_properties = 4;
  frontend.deeplink.Cta continue_cta = 5;
  typesv2.common.ui.widget.BackgroundColour background_color = 6;
  typesv2.ui.IconTextComponent add_address_cta = 7;
  string card_request_id = 8;
  bool is_location_permission_required = 9;
}

// screen options for CC_USER_INELIGIBLE_TRANSITION_SCREEN
// figma - https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4810-54748&mode=design&t=s16D6IUkCTgMlDIc-4
message CcUserIneligibleTransitionScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // top visual element for the screen
  common.VisualElement screen_header_visual_element = 2;
  // screen title
  typesv2.common.Text title = 3;
  // screen subtitle
  typesv2.ui.TextWithHyperlinks sub_title = 4;
  // info text to be shown as title before card image
  typesv2.common.Text info_text = 5;
  // visual element to show the eligible card image
  common.VisualElement info_visual_element = 6;
  // cta to trigger share feedback Cta
  frontend.deeplink.Cta share_feedback_cta = 7;
  // cta to navigate to the eligible card intro screen
  frontend.deeplink.Cta get_card_cta = 8;
  // toolbar to be rendered on the top of the screen
  frontend.firefly.WrappedIconTextToolBar tool_bar = 9;
}

// screen options for CC_CREDIT_LIMIT_UPDATE_SCREEN
// figma - https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=41507-40335&mode=design&t=8IAddUkbmT8aV2k8-4
message CCCreditLimitUpdateScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // background colour of the screen
  typesv2.common.ui.widget.BackgroundColour background_colour = 2;
  // visual element of top section
  common.VisualElement top_visual_element = 3;
  // screen title
  typesv2.common.Text title = 4;
  // screen subtitle
  typesv2.common.Text subtitle = 5;
  // credit limit related info
  typesv2.ui.VerticalIconTextComponent credit_limit_info = 6;
  // primary cta to continue onboarding flow
  frontend.deeplink.Cta primary_cta = 7;
  // secondary cta
  typesv2.ui.IconTextComponent secondary_cta = 8;
  // card request id for corresponding cc onboarding workflow for which this screen being used
  string card_request_id = 9;
}

// screen options for CC_CARD_TNC_CONSENT_SCREEN
// figma - https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?type=design&node-id=40754-7114&mode=design&t=8IAddUkbmT8aV2k8-4
message CCCardTnCConsentScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // background colour of the screen
  typesv2.common.ui.widget.BackgroundColour background_colour = 2;
  // screen title
  typesv2.common.Text title = 3;
  // screen subtitle
  typesv2.common.Text subtitle = 4;
  // property like background colour and corner radius
  frontend.firefly.DrawableProperties card_benefits_container_property = 5;
  // array containing card benefits
  repeated frontend.deeplink.InfoItemV2 card_benefits = 6;
  // cta to continue onboarding flow
  frontend.deeplink.Cta cta = 7;
  // consent check box status
  frontend.firefly.ConsentBox consent_check_box = 8;
  // consent text with tnc links
  typesv2.ui.TextWithHyperlinks consent_text = 9;
  // partner icon to be displayed in the footer area
  common.VisualElement bottom_visual_element = 10;
  // card request id for corresponding cc onboarding workflow for which this screen being used
  string card_request_id = 11;
}

// Provide the deeplink pay load for generic screen
message GenericScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;

  // Unique screen identifier, mapped with deeplink screen ordinal
  int32 screen_id = 2;

  // Use this field to pass any additional data required for the screen
  // For example, we can pass the offer id to display the offer details
  // In client side will send this back to server in "Frontend_Firefly_GetGenericScreenRequest"
  bytes metadata = 4;
}

message CcIntroScreenV2ScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  common.VisualElement loader_animation = 2;
  // marshalled string of api/typesv2/deeplink_screen_options/firefly/screen_options.proto -> CreditCardMetadata
  string metadata = 3;
  common.VisualElement bg_image = 4;
  CreditCardRequestHeader credit_card_request_header = 5;
}

message CreditCardMetadata {
  oneof metadata {
    IntroScreenV2Metadata intro_screen_v2_metadata = 1;
  }
}

message IntroScreenV2Metadata {
  // card state if card is already issued, will be unspecified if card is not issued
  // firefly.v2.enums.CardState.String()
  string card_state = 1;
  // card onboarding status
  // firefly.v2.enums.CardRequestStatus.String()
  string onboarding_request_status = 2;
  // result from CC eligibility check,
  // this will be true when the user is not already onboarded and is eligible for it.
  bool is_user_cc_eligible = 3;
}
