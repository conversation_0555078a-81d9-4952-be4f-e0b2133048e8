syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// possible features available to the epiFi users
enum Feature {
  FEATURE_UNSPECIFIED = 0;
  // INVESTMENT_MF_UI feature controls visibility of UI elements to invest in mutual fund
  INVESTMENT_MF_UI = 1;
  // PAY_VIA_PHONE_NUMBER feature controls if user is allowed to search VPAs by phone number
  PAY_VIA_PHONE_NUMBER = 2;
  // MF_ADVANCE_FILTER feature controls if user can use Advance filter for exploring Mutual funds or not.
  MF_ADVANCE_FILTER = 3;
  // MF_TEXT_SEARCH feature controls if user can use text search for exploring Mutual funds or not.
  MF_TEXT_SEARCH = 4;
  // DISPUTE_FOR_OFF_APP_TXN feature controls if users/support agents raise dispute for off app txns
  DISPUTE_FOR_OFF_APP_TXN = 5;
  // MF_SIP feature controls if we would be allowing amc specific SIPs for certain funds
  MF_SIP = 6;
  // MF_NEW_OTI_PAYMENT_FLOW controls which payment flow to use for one time payments
  // We currently call Pay's frontend rpc for executing payments and need to migrate to backend rpc for payment
  // This flag would be governing this switch
  MF_NEW_OTI_PAYMENT_FLOW = 7;
  // SHOW_SUPPORT_TICKETS_IN_APP controls if users can see the support tickets in the app
  SHOW_SUPPORT_TICKETS_IN_APP = 8;
  // HOME_PAGE_LAYOUT_V2 feature indicates that the new home page layout api needs to be used for the actor
  HOME_PAGE_LAYOUT_V2 = 9;
  // MERCHANT_ANALYSER controls if user can see merchant analyser banner and screen
  MERCHANT_ANALYSER = 10;
  // TIME_ANALYSER controls if user can see time analyser, banner and time filter v2 implementation
  TIME_ANALYSER = 11;
  // SENSEFORTH_CHATBOT feature controls if senseforth chabot webview has to be loaded for the users on a mobile client
  SENSEFORTH_CHATBOT = 12;
  // AA_FINVU_TOKEN_AUTHENTICATION feature controls if token authentication is to be used in Finvu flows
  AA_FINVU_TOKEN_AUTHENTICATION = 13;
  // Controls whether to show JumpV2 to user or not
  JUMP_V2 = 14;
  // CATEGORY_ANALYSER_ADD_FUNDS_BANNER flag controls if add funds banner can be shown to the user
  CATEGORY_ANALYSER_ADD_FUNDS_BANNER = 15;
  // Controls if upi tpap if enabled for user or not
  UPI_TPAP = 16;
  // Controls entry to credit score analyser
  CREDIT_SCORE_ANALYSER = 17;
  // controls which users are eligible for feedback
  ANALYSER_FEEDBACK = 18;
  // controls the users eligible for fi minute hub banner
  FI_MINUTE_HUB_BANNER = 19;
  // US_STOCK_UI feature controls visibility of UI elements to invest in us stocks
  US_STOCK_UI = 20;
  // US_STOCK_LANDING_PAGE_UI feature controls visibility of landing page to the user to invest in us stocks
  // Pre-launch page is shown instead of landing for users not allowed for this feature
  US_STOCK_LANDING_PAGE_UI = 21;
  // controls which users are eligible to see/claim the salaryprogram health insurance benefit.
  SALARY_PROGRAM_HEALTH_INSURANCE_BENEFIT = 22;

  ASK_FI_HOME_SEARCH_BAR = 23;
  // Referrals V1 landing page flag
  REFERRALS_V1_LANDING_PAGE = 24;
  // controls whether to enable jump renewal flow
  JUMP_RENEWAL_FLOW = 25;
  // Controls if upi mapper if enabled for user or not
  UPI_MAPPER = 26;
  CREDIT_SCORE_ANALYSER_V2 = 27;
  // Controls if the new QR SDK is enable for user or not
  ML_KIT_QR = 28;
  // control flag on client side to enable vkyc next action approch
  ENABLE_GET_VKYC_NEXT_ACTION = 29;

  // Controls flag on client side to enable or disable 2FA for mutual fund one time purchase flow
  ENABLE_2FA_MF_ONE_TIME_BUY = 30;

  // Controls flag on client side to enable or disable 2FA for sip registration flow
  ENABLE_2FA_MF_REGISTER_SIP = 31;

  // Controls flag to convert autoInvest orders to SIP orders.
  ENABLE_MF_FIT_SIP_CONVERSION = 32;

  // TIME_ANALYSER_INSIGHTS flag controls if we should generate new insights or continue showing the older insights.
  TIME_ANALYSER_INSIGHTS = 33;

  // CATEGORY_ANALYSER_ACCOUNT_FILTER flags controls release of account filter in category analyser.
  // old bank account filter will get disabled for users who are eligible for the account filter.
  CATEGORY_ANALYSER_ACCOUNT_FILTER_V2 = 34;

  // Feature used to perform AB testing on the ordering of investment landing components
  INVESTMENT_LANDING_COMPONENTS_ORDERING = 35;

  // feature used to perform controlled rollout and AB for referral link generation at client side
  REFERRAL_LINK_GENERATION_AT_CLIENT_SIDE = 36;

  // feature for controlled rollout of connected accounts as a screener check in onboarding
  FEATURE_CONNECTED_ACCOUNTS_IN_SCREENER = 37;

  // feature to enable jump dashboard with zero values to non invested users
  JUMP_DASHBOARD_FOR_NON_INVESTED_USERS = 38;

  // feature to perform A/B testing on interest rate for fixed deposit (all templates)
  FIXED_DEPOSIT_INTEREST_RATES = 39;

  // feature to perform A/B testing on default term for fixed deposit custom template
  FIXED_DEPOSIT_CUSTOM_TEMPLATE_DEFAULT_TERM = 40;

  //feature to show new txn receipt screen v1 to the user group
  TXN_RECEIPT_SCREEN_V1 = 41;

  // feature to perform A/B testing on home search bar chips
  HOME_SEARCH_BAR_CHIPS = 42;

  // feature to check if qr should be validated for
  // the user by client at their end or they should
  // use ResolveQrData FE rpc
  VERIFY_QR_V1 = 43;

  // feature used to perform controlled rollout and AB for new referral page during onboarding
  REFERRAL_SCREEN_DURING_ONBOARDING_V1 = 44;

  // feature used to perform controlled rollout and AB for new affluent user bonus transition screen during onboarding
  // this screen is currently being shown only for users who belong to particular affluence classes and have onboarded via regular referral
  AFFLUENT_USER_BONUS_TRANSITION_SCREEN = 45;

  // feature to enable health engine for payments
  HEALTH_ENGINE_FOR_PAYMENTS = 46;

  // feature to perform A/B testing for different eligibility of user and scheme in JUMP
  JUMP_USER_SCHEME_ELIGIBILITY = 47;

  // feature used to control the view of upcoming transactions line item in time analyser
  TIME_ANALYSER_UPCOMING_TRANSACTIONS = 48;

  // feature used to perform controlled rollout and testing of aadhaar number flow for upi pin set
  UPI_PIN_SET_USING_AADHAAR = 49;

  // feature perform vkyc then add funds
  PRIORITIES_VKYC_OVER_ADD_FUNDS = 50;

  // feature controls configurable zero states to be shown in analysers
  ANALYSER_ZERO_STATES = 51;

  // Feature used to perform AB testing on the deeplink for Mutual Funds in investment instruments section of investment landing
  INVESTMENT_LANDING_MUTUAL_FUNDS_DEEPLINK = 52;

  // feature used to perform controlled rollout and testing of international payments for upi accounts
  UPI_INTERNATIONAL_PAYMENT = 53;

  // whether to show Fi credit card txn data in askfi or not
  CC_TXN_DATA_IN_ASKFI = 54;

  // Feature used to perform AB testing on the home banner for investment products eg. MF, usstocks
  INVESTMENT_HOME_COMPONENT = 55;

  // Feature for Connected Account AA Consent Renewal
  AA_CONSENT_RENEWAL = 56;

  // Feature used to perform AB testing on the Invest landing banner products eg. MF, usstocks, P2P, deposits etc.
  INVEST_LANDING_BANNER = 57;

  // Feature for Jump Maturity Consent flow
  JUMP_MATURITY_CONSENT = 58;

  // Feature used to perform controlled rollout and testing of UPI Pin Flow Error Ticket Creation
  PAY_INCIDENT_MANAGER_UPI_PIN_FLOW_ERROR = 59;

  // Feature for fund transfer via Celestial rollout
  FUND_TRANSFER_V1 = 60;

  // US_STOCK_ETF_SUPPORT feature controls visibility of ETFs to the user to invest in ETFs
  US_STOCK_ETF_SUPPORT = 61;

  // ONBOARDING_ADD_FUNDS_V2 feature to perform AB testing and enabling/disabling of ONBOARDING_ADD_FUNDS_V2 screen
  ONBOARDING_ADD_FUNDS_V2 = 62;

  // FRESHDESK_MONORAIL_INTEGRATION feature to control creation of Monorail tickets when a user's issue is escalated from Freshdesk
  FRESHDESK_MONORAIL_INTEGRATION = 63;

  // feature used to perform a controlled rollout and testing of credit card linking for upi payments
  CC_UPI_LINKING = 64;

  // ConnectedAccount: Connect Fi To Fi feature for connecting Fi Federal saving bank account
  CA_CONNECT_FI_TO_FI = 65;

  // Feature used to perform controlled rollout and testing of P2P deemed transaction Ticket Creation/Resolution
  PAY_INCIDENT_MANAGER_P2P_DEEMED_TRANSACTION = 66;

  // Feature used to perform controlled rollout and testing of P2M deemed transaction Ticket Creation/Resolution
  PAY_INCIDENT_MANAGER_P2M_DEEMED_TRANSACTION = 67;

  // Jump all activity deeplink support
  JUMP_ALL_ACTIVITY_DEEPLINK = 68;

  // NEW_VPA_HANDLE controls feature to assign new vpa handles to users i.e @fifederal
  NEW_VPA_HANDLE = 69;

  // Feature used to perform controlled rollout and testing of updated benefits on adding funds for affluent users
  UPDATED_BENEFITS_FOR_AFFLUENT_USER_ONB_ADD_FUNDS = 70;

  // Feature used to perform controlled rollout and testing for realtime credit card eligibility for a user
  FEATURE_CC_REALTIME_ELIGIBILITY = 71;

  // show new profile details bottom sheet in credit score analyser
  CREDIT_SCORE_ANALYSER_PROFILE_DETAILS_V2 = 72;

  // Feature used to show backend configurable feedback surveys to users, as part of inapphelp service
  INAPPHELP_FEEDBACK_ENGINE = 73;

  // Feature to fetch order with transactions using in memory join instead of db-join (db-optimisation)
  FETCH_ORDER_WITH_TRANSACTIONS_USING_IN_MEMORY_JOIN = 74;

  // feature used to perform controlled rollout and AB for new affluent user bonus transition screen for non referees during onboarding
  // this screen is currently being shown only for users who belong to particular affluence classes and are non refrees
  AFFLUENT_USER_BONUS_TRANSITION_SCREEN_NON_REFEREE = 75;

  // feature to perform A/B testing for stacked referral rewards in referrals landing v1 page
  REFERRALS_V1_LANDING_PAGE_STACKED_REWARDS_COMPONENT = 76;

  // Feature used to perform controlled rollout and testing of CC EMI flows on AmpliFi
  FEATURE_CC_EMI = 77;

  // feature used to perform controlled rollout of new onboarding API for credit cards
  CC_REGISTER_CUSTOMER_V2 = 78;

  // Feature used to perform controlled rollout and testing of Jump invest v2 page
  FEATURE_JUMP_INVEST_PAGE_V2 = 79;

  // Feature used to perform AB for component on usstocks landing page
  USS_LANDING_PAGE_AB_SUPPORT = 80;

  // Feature used to perform AB for collection on usstocks collection screen
  USS_COLLECTION_SCREEN_AB_SUPPORT = 81;

  // feature to display phone number screen in mf holdings import flow
  MF_HOLDINGS_IMPORT_PHONE_NUMBER_SCREEN = 82;

  // CREDIT_SCORE_ANALYSER_AUTO_REFRESH is used to control auto refresh of credit report while loading credit score analyser
  CREDIT_SCORE_ANALYSER_AUTO_REFRESH = 83;

  // ATT permission prompt for iOS
  ATT_IOS_PERMISSION_PROMPT = 84;

  // CONSENT_RENEWAL_WIDGET_IN_ASKFI feature is used to display a warning widget in askfi transactions search
  CONSENT_RENEWAL_WIDGET_IN_ASKFI = 85;

  // Feature used to perform controlled rollout for new landing page
  FEATURE_JUMP_NEW_LANDING_PAGE = 86;

  // AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET feature is used to display a warning widget in all transactions page when consent is expired
  AA_CONSENT_RENEWAL_ALL_TXNS_PAGE_WIDGET = 87;

  // feature on order receipt page for quick re-categorization
  QUICK_RECAT = 88;

  // POST_PAYMENT_SCREEN is used to display post payment screen.
  POST_PAYMENT_SCREEN = 89;

  // app-update hard nudge on referrals screen
  APP_UPDATE_HARD_NUDGE_REFERRALS = 90;

  // app-update soft nudge on referrals screen
  APP_UPDATE_SOFT_NUDGE_REFERRALS = 91;

  // REFERRALS_V1_REFERRAL_HISTORY_EARNING_SUMMARY_INFO is used to display earning summary info in referral history of referrals v1 page
  // Figma - https://www.figma.com/file/5TZH6I0awKw8DMtj3aMzrC/Referrals-%E2%80%A2-FFF?type=design&node-id=9465-13384&mode=dev
  REFERRALS_V1_REFERRAL_HISTORY_EARNING_SUMMARY_INFO = 92;

  // CC feature to update card details at bank
  FEATURE_UPDATE_CARD_DETAILS_AT_BANK = 93;

  // feature flag for upi lite
  UPI_LITE = 94;

  // CC_ONBOARDING_WITHOUT_BILL_GEN_DATE_CAPTURE_STAGE is for showing bill gen date selection on card activation screen instead of separate stage screen
  FEATURE_CC_ONBOARDING_WITHOUT_BILL_GEN_DATE_CAPTURE_STAGE = 95;

  // feature flag for investment retention
  INVESTMENT_RETENTION_UI = 96;

  // feature flag for Early Salary, used to control the user groups and app version release evaluator
  LOANS_EARLY_SALARY = 97;

  // feature flag for Loans Fi-lite, used to control the user groups and app version release evaluator
  LOANS_FI_LITE = 98;

  // NETWORTH_DASHBOARD is for enabling and disabling the networth dashboard entry points on home
  NETWORTH_DASHBOARD = 99;

  // feature for biometric revalidation
  FEATURE_BIOMETRIC_REVALIDATION = 100;

  // Feature flag for releasing the deposits widget in networth
  NETWORTH_DEPOSITS_WIDGET = 101;

  // feature for remitter info backfill workflow
  FEATURE_REMITTER_INFO_BACKFILL = 102;

  // feature for new endpoint on inbound notification
  FEATURE_NEW_ENDPOINT_INBOUND_NOTIFICATION = 103;

  // feature flag for IMPS deemed txns handling
  IMPS_DEEMED_HANDLING = 104;

  // Feature used to perform controlled rollout and testing of
  // debited-transaction-threshold-breach ticket Creation/Resolution
  PAY_INCIDENT_MANAGER_DEBITED_TRANSACTION_THRESHOLD_BREACH = 105;

  // feature to enable health engine for INTRA payments
  HEALTH_ENGINE_FOR_INTRA_PAYMENTS = 106;

  // feature to enable health engine for NEFT payments
  HEALTH_ENGINE_FOR_NEFT_PAYMENTS = 107;

  // feature to enable health engine for IMPS payments
  HEALTH_ENGINE_FOR_IMPS_PAYMENTS = 108;

  // feature to enable health engine for RTGS payments
  HEALTH_ENGINE_FOR_RTGS_PAYMENTS = 109;

  // Feature used to perform controlled rollout and testing of
  // transaction detailed status update ticket Creation/Resolution
  PAY_INCIDENT_MANAGER_TRANSACTION_DETAILED_STATUS_UPDATE = 110;

  // deposit create v2 flow which is BE driven UI flow
  IS_CREATE_DEPOSIT_V2_ENABLED = 111;

  // deposit close payout v2 flow which is BE  driven UI flow
  IS_CLOSE_DEPOSIT_V2_ENABLED = 112;

  // Feature used to display new cc intro horizontal layout screen
  FEATURE_CC_INTRO_SCREEN_HORIZONTAL_LAYOUT = 113;

  // Feature used to perform controlled rollout and testing of new add funds vpa
  NEW_ADD_FUNDS_VPA = 114;

  // Feature used to perform controlled rollout of CX feedback engine integrated
  // screen to take user feedback if they are ineligible to onboard.
  FEATURE_CC_USER_INELIGIBLE_FEEDBACK = 115;

  // first time payment prompt
  FIRST_TIME_PAYMENT_PROMPT = 116;

  // flag to control if disabled cards are to be shown in analyser landing page
  ANALYSER_LANDING_PAGE_DISABLED_CARD = 117;

  // Feature used to perform controlled rollout of phone number as referral code
  PHONE_NUMBER_AS_REFERRAL_CODE = 118;

  // feature used to control rollout for CardSwitchNotifications
  CARD_SWITCH_NOTIFICATION = 119;

  // Feature to enable recent_activities be sorted on created_at as index
  SORT_RECENT_ACTIVITIES_ON_CREATED_AT = 120;

  // Feature used to trigger a notification through which in app csat survey is served
  IN_APP_CSAT_SURVEY = 121;

  // mf investment calculator feature flag
  MF_INVESTMENT_CALCULATOR_UI = 122;

  // mf investment nav chart feature flag
  MF_NAV_GRAPH_UI = 123;

  // Feature is used to control rollout for analyser error view v2
  ANALYSER_ERROR_VIEW_V2 = 124;

  // Feature is used to control rollout for Self transfer feature
  SELF_TRANSFER = 125;

  // AA data fetch feature to wait until we receive all FI notification and then proceed with data fetch
  AA_DATA_FETCH_WAIT_FOR_ALL_NOTIFICATION = 126;

  // feature for non-pre-approved unsecured card onboarding api
  FEATURE_CC_REGISTER_CUSTOMER_V3 = 127;

  // Feature is used to control rollout for New liveness screen for credit card onboarding
  FEATURE_CC_BE_DRIVEN_LIVENESS_SCREEN = 128;

  // Salary lite program feature
  SALARY_LITE_PROGRAM = 129;

  // flag to use upcoming transaction service to show user's upcoming txns (on home) instead of Fit service
  UPCOMING_TRANSACTIONS_V2 = 130;

  // flag to enable connected accounts benefits screen v2
  CONNECTED_ACCOUNT_BENEFITS_SCREEN_V2 = 131;

  // flag to enable Ind securities feature on net worth
  NETWORTH_IND_SECURITIES_WIDGET = 132;

  // flag to allow display of Equity Accounts on home summmary
  AA_EQUITY_ACC_HOME_SUMMARY = 133;

  // Feature is used to control rollout of secured credit card
  FEATURE_CC_SECURED_CARDS = 134;

  // Feature is used to control rollout of vpa migration intro screen
  VPA_MIGRATION_INTRO_SCREEN = 135;
  // control if mf holdings import v2 flow is enabled
  MF_HOLDINGS_IMPORT_V2_FLOW = 136;
  // feature for in-house bre checks in RTBRE flows
  FEATURE_CC_INHOUSE_BRE = 137;
  // feature for forced balance refresh
  FEATURE_FORCED_BALANCE_REFRESH = 138 [deprecated = true];
  // Feature is used to enable secured loans for users
  FEATURE_SECURED_LOANS = 139;
  // feature for user activity to show the list of activities done by the user in cx chatbot (senseforth)
  CX_CHATBOT_USER_ACTIVITY = 140;

  // feature flag for beneficiary cool down period
  BENEFICIARY_COOL_DOWN_RULE = 141;

  // flag to allow and control AA txn to pipe to client for tpap
  ALLOW_AND_CONTROL_AA_TXN_FOR_TPAP = 142;

  // Feature flag for getting credit report consent on PAN DOB stage for savings account
  CREDIT_REPORT_CONSENT_FOR_SA_ON_PAN_DOB_STAGE = 143;

  // Feature enum to enable mass unsecured card
  MASS_UNSECURED_CARD = 144;

  // feature flags to control manual assets form release
  MANUAL_ASSET_FORM_AIF = 145;
  MANUAL_ASSET_FORM_ART_ARTEFACTS = 146;
  MANUAL_ASSET_FORM_BONDS = 147;
  MANUAL_ASSET_FORM_CASH = 148;
  MANUAL_ASSET_FORM_DIGITAL_GOLD = 149;
  MANUAL_ASSET_FORM_DIGITAL_SILVER = 150;
  MANUAL_ASSET_FORM_PRIVATE_EQUITY = 151;
  MANUAL_ASSET_FORM_REAL_ESTATE = 152;

  ACTIVATE_BENEFICIARY_VIA_LIVENESS = 153;

  // Feature enum to check if AutoPay / RecurringPayment
  // is enabled for the user.
  AUTOPAY_HUB = 154;

  // Feature for options page in screener
  SCREENER_CHOICE_PAGE = 155;

  // Feature flags used to rollout contextual insights on spend analysers
  SPEND_ANALYSER_INVEST_MORE_INSIGHT = 156;
  SPEND_ANALYSER_INVEST_SURPLUS_BALANCE_INSIGHT = 157;
  SPEND_ANALYSER_EARLY_SALARY_INSIGHT = 158;
  SPEND_ANALYSER_SET_REMINDER_INSIGHT = 159;
  PAY_ASK_FI_SEARCH_SCREEN = 160;

  // Feature flag to control beneficiary cool down for pay via phone number
  SUPPORT_FOR_BENEFICIARY_COOL_DOWN_IN_PAY_BY_PHONE_NUMBER = 161;

  // Feature flag for onb add funds screen and to enable opt in upgrades
  ONB_ADD_FUNDS_TIERING_SUCCESS = 162;

  // feature flag for savings account closure flow - profile settings entry point
  SAVINGS_ACCOUNT_CLOSURE_PROFILE_SETTINGS_ENTRY_POINT = 163;

  // feature flag for enabling tpap for credit card payments
  FEATURE_CREDIT_CARD_TPAP_PAYMENTS = 164;

  // Feature flag for getting permitted FIP config V2
  AA_PERMITTED_FIP_CONFIG_V2 = 165;

  // feature flag for enabling credit card recommendation framework
  FEATURE_CREDIT_CARD_RECOMMENDATION_FRAMEWORK = 166;

  // Feature flag for add funds v3
  // Collect flow/Intent flow switch based on add funds amount
  // figma: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1586-62110&mode=design&t=x4uoemlNigT4wMXl-0
  ADD_FUNDS_V3 = 167;
  // Feature flag for onboarding add funds v2.2
  // Collect flow/Intent flow switch based on add funds amount
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1586-87925&mode=design&t=WxERMQgI6gAmfn9N-0
  ONBOARDING_ADD_FUNDS_V2_2 = 168;

  // Feature flag for getting Auto Renew CTA for Deposit Accounts
  DEPOSIT_AUTO_RENEW_CTA = 169;

  // Feature flag for enabling simplifi atm withdrawals
  FEATURE_ENABLE_SIMPLIFI_CREDIT_CARD_ATM = 170;

  LOANS_FEDERAL_V2 = 171;

  // Feature flag for add funds v4
  // Flow with payment options integration
  // figma: https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=2174%3A128107&mode=dev
  ADD_FUNDS_V4 = 172;

  // Feature flag to enable savings account sign update flow
  ALFRED_SAVINGS_ACC_SIGN_UPDATE = 173;

  // feature for EMI on SimpliFi
  FEATURE_CC_EMI_SIMPLIFI = 174;

  // feature for EMI on MagniFi
  FEATURE_CC_EMI_MAGNIFI = 175;

  // Feature flag to enable new vkyc review screens
  VKYC_NEW_REVIEW_SCREEN = 176;

  // Feature flag for Credit fetch stage (CIBIL)
  LOANS_CIBIL_REPORT_FETCH = 177;

  // Feature flag to enable/disable etf in indian stocks
  INDIAN_SECURITIES_ETF = 178;

  // feature flag for one click tpap enablement flow
  ONE_CLICK_TPAP_ENABLEMENT_FLOW = 179;

  // feature flag for credit report address selection
  CREDIT_REPORT_ADDRESS_SELECTION = 180;

  // feature flag for New VKYC PAN CAPTURE and parents name update flow
  VKYC_PAN_IMAGE_CAPTURE = 181;

  // Feature flag for manual uan flow
  MANUAL_UAN_EPF_FLOW = 182;

  // feature flag for CIBIL integration in CC Fi Lite flow
  FEATURE_CC_FILITE_CIBIL_INTEGRATION = 183;

  // feature flag for tiering earned benefits screen - profile notch entry point
  TIERING_EARNED_BENEFITS_FROM_PROFILE_NOTCH = 184;

  // feature for debit card offer widget on home screen
  DEBIT_CARD_OFFER_WIDGET_HOME = 187;
  // feature flag to enable loan pre-payment via tpap for fi-core users
  LOANS_TPAP_PRE_PAYMENT = 188;

  // feature flag to enable new nsdl pan api
  NSDL_PAN_API_V2_FOR_CA = 189;

  // feature for CC preapproved unsecured card program
  FEATURE_CC_PREAPPROVED_AMPLIFI = 185;
  // feature for CC BRE unsecured card program
  FEATURE_CC_BRE_AMPLIFI = 186;

  // feature for CC preapproved secured card program
  FEATURE_CC_PREAPPROVED_SIMPLIFI = 190;
  // feature for CC BRE secured card program
  FEATURE_CC_BRE_SIMPLIFI = 191;
  // feature for CC preapproved mass_unsecured card program
  FEATURE_CC_PREAPPROVED_MAGNIFI = 192;
  // feature for CC BRE mass_unsecured card program
  FEATURE_CC_BRE_MAGNIFI = 193;

  // feature flag to enable/disable report fraud in order receipt page
  REPORT_FRAUD_ORDER_RECEIPT = 194;

  // feature flag to enable/disable reward details in order receipt page
  FEATURE_REWARD_DETAILS_IN_ORDER_RECEIPT = 195;

  // feature flag to enable phase 3.1 of lamf
  FEATURE_LAMF_PHASE3_1 = 196;
  // feature flag for loans estimate income via itr flow
  FEATURE_LOANS_INCOME_ESTIMATE_VIA_ITR = 197;

  // feature flag to enable networth refresh v2
  NETWORTH_REFRESH_V2 = 198;
  // feature flag to control calling new pan validation api in aadhaar link status check
  FEATURE_PAN_VALIDATE_V2_AADHAAR_LINK_STATUS = 199;
  // Feature flag to control rollout of MS Clarity SDk on clients
  FEATURE_MS_CLARITY_SDK_ENABLED = 200;
  // feature flag for new post disbursal screens
  FEATURE_POST_LOAN_DISBURSAL_SCREENS = 201;

  // feature flag to enable/disable reward details in all transactions page
  FEATURE_REWARD_DETAILS_IN_ALL_TRANSACTIONS_PAGE = 202;

  // feature flag to control pms manual asset form release
  MANUAL_ASSET_FORM_PORTFOLIO_MANAGEMENT_SERVICE = 203;

  // feature flag for adding alternate phone number
  FEATURE_LOAN_ADD_ALTERNATE_PHONE_NUMBER = 204;

  // feature flag for IDFC vkyc workapps integration
  LOANS_IDFC_VKYC_V2 = 205;

  // feature flag to perform controlled rollout for US Stocks RTSP (real time stock prices)
  FEATURE_USS_TABBED_CARD_RTSP = 206;

  // feature flag for nsdl pan flow v2 enabled for mf analyser or not
  NSDL_PAN_FLOW_V2_MF_ANALYSER = 207;

  // feature flag for nsdl pan flow v2 enabled for credit report analyser or not
  NSDL_PAN_FLOW_V2_CREDIT_REPORT_ANALYSER = 208;

  // feature flag to control rollout for off app eNACH cancellation.
  // figma for off app eNACH cancellation workflow: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=23674-47816&t=UVECot1zQoI1wswn-0
  FEATURE_OFF_APP_ENACH_CANCELLATION = 209;

  // feature flag to control ppf manual asset form release
  MANUAL_ASSET_FORM_PUBLIC_PROVIDENT_FUND = 210;

  // feature flag to control esop asset form release
  MANUAL_ASSET_FORM_EMPLOYEE_STOCK_OPTION = 211;

  // feature flag for Liquiloans Early Salary Program version V2
  LOANS_LIQUILOANS_EARLY_SALARY_V2 = 212;

  // feature flag for call IVR use case in CX
  FEATURE_CX_CALL_IVR = 213;
  // feature flag for rollout of automatic fee waiver
  FEATURE_CC_AUTOMATIC_FEE_WAIVER = 214;
  // feature flag for USS taxation document request option via alfred
  FEATURE_ALFRED_USS_TAX_DOCUMENT_REQUEST = 215;
  // loans feature flag for repeat loan rollout for LL stpl
  REPEAT_LOANS_LIQUILOANS_LOAN_PROGRAM_STPL = 216;
  // feature flag for rollout of call quality screen
  VKYC_CALL_QUALITY_SCREEN = 217;

  // feature flag to control whether to redirect user to all payment options screen for physical debit card charges payments
  PHYSICAL_DEBIT_CARD_CHARGES_PAYMENT_OPTIONS_SCREEN = 218;

  // feature flag to enable revamped debit card dashboard
  FEATURE_DC_DASHBOARD_V2_SCREEN = 219;

  // asset landing page for manual asset feature
  ASSET_LANDING_PAGE_FOR_MANUAL_ASSET = 220;
  // feature flag for Amplifi CC minutes
  FEATURE_AMPLIFI_CC_MINUTES = 221;
  // feature flag to determine if analyser hub widget overlay config is enabled for fi-to-fi integration
  ANALYSER_HUB_FI_TO_FI_INTEGRATION = 222;

  // Feature flag to enable the credit card reward dashboard for unsecured cards
  FEATURE_CC_REWARD_DASHBOARD_UNSECURED_CARD = 223;

  // Feature flag to enable the credit card reward dashboard for mass unsecured cards
  FEATURE_CC_REWARD_DASHBOARD_MASS_UNSECURED_CARD = 224;

  // Feature flag to enable the credit card reward dashboard for secured cards
  FEATURE_CC_REWARD_DASHBOARD_SECURED_CARD = 225;

  // feature for off-app-upi txns managed via temporal workflow
  FEATURE_OFF_APP_UPI_VIA_TEMPORAL = 226;
  // feature flag for enabling REIT
  FEATURE_INDIAN_SECURITIES_REIT = 227;
  // feature flag for enabling INVIT
  FEATURE_INDIAN_SECURITIES_INVIT = 228;
  // feature flag to enable double pin flow in uss add fund
  FEATURE_ADD_FUNDS_USS_DOUBLE_PIN_FLOW = 229;
  // feature flag for enabling NPS on net worth
  FEATURE_NET_WORTH_NPS = 230;
  // figma: https://www.figma.com/design/7gQRgxlw1QbNMf3ooZOXMu/%F0%9F%92%B3--AmpliFi-CC-%E2%80%A2-Workfile?node-id=43310-50168&t=Kr2bD7QECk0A5v6B-4
  // feature flag for enabling new cc dashboard offers widget
  FEATURE_CC_OFFERS_WIDGET = 231;
  // feature flag for sending amount screen in collect PNs
  FEATURE_AMOUNT_SCREEN_IN_COLLECT_PN = 232;
  // feature flag for money secret on home screen
  FEATURE_MONEY_SECRET_HOME_SCREEN = 233;
  // feature flag for enabling and disabling dashboard sections for milestone and benefits widget
  FEATURE_CC_DASHBOARD_BENEFITS_WIDGET_UNSECURED_CARD = 234;
  // feature flag for exposing issue reporting flow on App
  FEATURE_IN_APP_ISSUE_REPORTING_FLOW = 235;
  // feature flag for enabling and disabling loans pre-payment via payment gateway
  FEATURE_LOAN_PRE_PAY_VIA_PG = 236;
  // Feature flag for enabling bank selection screen in connected account flow
  FEATURE_CA_BANK_SELECTION = 237;
  // Feature flag for enabling Single RPC driven Home-screen on the app
  FEATURE_HOME_SINGLE_RPC = 238;
  // feature flag for disabling dc delivery address update
  FEATURE_DISABLE_DC_DELIVERY_ADDRESS_UPDATE = 239;
  // feature flag for enabling consent screen V2 in onboarding
  FEATURE_ENABLE_CONSENT_SCREEN_V2 = 240;
  // feature flag for help recent activities flow
  FEATURE_CX_HELP_RECENT_ACTIVITY = 241;
  // feature flag to show new pay receipt error UI
  //  figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=26564-28903&t=KbLwg48sh5O5tqmh-4
  FEATURE_PAY_RECEIPT_NEW_ERROR_VIEW = 242;
  // For enabling searching of PMS providers and AIFs in manual asset forms
  FEATURE_PMS_PROVIDER_AND_AIF_SEARCH = 243;
  // feature flag to enable multi lender and program eligibility evaluation for non fi core users
  FEATURE_LOANS_NON_FI_CORE_ELIGIBILITY_V2 = 244;
  // feature flag for DC new international ATM withdrawal layout
  // figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=22261-47439&t=MCh9gXKttXBPPOUr-4
  FEATURE_DC_INTERNATIONAL_ATM_WITHDRAWAL_LAYOUT = 245;

  FEATURE_US_STOCKS_SIP = 246;
  // feature flag for restricting users to initiate payments in case of Account Frozen or Not Active.
  FEATURE_ENABLE_ACCOUNT_OPERATIONAL_STATUS_CHECK = 247;
  // flag to control access to payment options v1 implementation
  FEATURE_ENABLE_PAYMENT_OPTIONS_V1 = 248;
  FEATURE_IN_APP_ISSUE_REPORTING_NON_FCR_FLOW = 249;

  // flag to enable/disable `ORDER_PHYSICAL_CARD` stage in SA onboarding flow
  // figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=9250-19535&t=L2zcBY9YvAOmYqDs-4
  FEATURE_ENABLE_ORDER_PHYSICAL_CARD_DURING_ONBOARDING = 250;
  // feature flag to show DC Travel mode
  FEATURE_DC_TRAVEL_MODE = 251;
  // feature flag to control secret summaries in networth dashboard
  FEATURE_NETWORTH_DASHBOARD_SECRET_SUMMARIES = 252;
  // feature flag to for associated transaction component in order receipt
  // Deprecated: In favour of FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT
  FEATURE_DC_FOREX_ORDER_RECEIPT = 253;
  // feature flag to control release of networth section in home bottom nav bar
  FEATURE_HOME_BOTTOM_NAV_BAR_NETWORTH_SECTION = 254;
  // feature flag to control release of pay search v2
  FEATURE_PAY_SEARCH_V2 = 255;
  // feature flag for call blocking functionality
  FEATURE_CX_CALL_BLOCKER = 256;
  // feature flag to control release of regular tier
  FEATURE_REGULAR_TIER = 257;
  // feature flag to control generic error screen in EPF
  FEATURE_EPF_GENERIC_ERROR_SCREEN = 258;
  // feature flag for USS document request option via alfred
  FEATURE_ALFRED_USS_DOCUMENT_REQUEST = 259;
  // feature flag to control release of DC toggle travel mode and related changes
  FEATURE_DC_TOGGLE_TRAVEL_MODE = 260;
  // feature flag to for associated transaction component in order receipt
  // https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=23330-40867&t=y4PxrDvsgrciSv6t-4
  FEATURE_ASSOCIATED_TRANSACTION_ORDER_RECEIPT = 263;
  // feature flag to avoid user's with older app directly get connected to agents using chatbot
  FEATURE_REQUEST_APP_UPGRADE_FOR_CHATBOT_LOADING = 261;
  // feature flag to enable uss limit order
  FEATURE_US_STOCKS_LIMIT_ORDER = 262;
  // feature flag to enable sms parser client sdk
  FEATURE_SMS_PARSER_PARTNER_SDK = 264;
  // feature flag to roll out upi mandates
  FEATURE_UPI_MANDATES = 265;
  FEATURE_MONEY_SECRET_V2 = 266;
  // FEATURE_REPORT_FRAUD_IN_DISPUTE_BOTTOM_SHEET feature controls if users should see "report fraud" option in dispute bottom sheet
  FEATURE_REPORT_FRAUD_IN_DISPUTE_BOTTOM_SHEET = 267;
  // FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME feature controls if users should see the wealth analyser widget instead of the money secrets widget
  FEATURE_SHOW_WEALTH_ANALYSER_WIDGET_HOME = 268;
  // feature flag to show ask fi widget in issue reporting flow
  FEATURE_ISSUE_REPORTING_ASK_FI_INTEGRATION = 269;
  // feature flag to roll out alternate app icon
  FEATURE_ALTERNATE_APP_ICON = 270;
  // feature flag to control if pan dob check should be skipped before initialising connected accounts sdk
  FEATURE_CONNECTED_ACCOUNTS_SKIP_PAN_DOB = 271;
  // feature flag to enable risk ivr flow for user
  FEATURE_CX_CALL_RISK_IVR_FLOW = 272;
  // feature flag for SMS parser flow in prime
  PRIME_SMS_PARSER = 273;
  // feature flag to enable wealth builder pan collection form
  FEATURE_WEALTH_BUILDER_PAN_COLLECTION_FORM = 274;
  // feature flag to enable tpap option in payment options screen for lending pre pay flow
  FEATURE_LENDING_TPAP_IN_PAYMENT_OPTIONS_SCREEN = 275;
  // feature flag to control release of share post payment screen
  FEATURE_SHARE_POST_PAYMENT_SCREEN = 276;
  // Connected Accounts CASdkV3FlowParams consist of params that decides version Flow V3 should be enabled or not depending on either absolute flag or app version
  FEATURE_CONNECTED_ACCOUNT_UNIFIED_FLOW = 277;
  // feature flag to control release of self transfer screen
  FEATURE_SELF_TRANSFER_SCREEN = 278;
  // feature flag for enabling peer comparsion in secrets
  FEATURE_MONEY_SECRET_PEER_COMPARISON = 279;
  // feature flag to control release of vpa preference for list account, either User's VPA or default VPA.
  FEATURE_VPA_PREFERENCE_FOR_LIST_ACCOUNT = 280;
  // feature flag to enable auto id for debited and failed transactions catch all for in-app transaction
  FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_FAILED_TRANSACTION_CATCH_ALL_ON_APP = 281;
  // feature flag to enable auto id for debited and in progress transactions catch all for in-app transaction
  FEATURE_PAY_INCIDENT_MANAGER_DEBITED_AND_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP = 282;
  // feature flag to enable auto id for pay incident manager in progress transactions catch all for in-app transaction
  FEATURE_PAY_INCIDENT_MANAGER_IN_PROGRESS_TRANSACTION_CATCH_ALL_ON_APP = 283;
  // Feature used to perform controlled rollout and testing of new add funds prefunding flow
  FEATURE_PAY_ADD_FUNDS_PREFUNDING = 284;
  // feature flag to enable salary report money secret
  FEATURE_SALARY_REPORT_MONEY_SECRET = 285;
  // feature flag to pre account creation add funds (aka prefunding flow)
  FEATURE_ONBOARDING_PRE_ACCOUNT_CREATION_ADD_FUNDS = 286;
  // feature flag to enable lottie animation in DC travel mode
  FEATURE_DC_TRAVEL_MODE_LOTTIE = 287;
  // feature flag to control ListAccount Vendor Call.
  // Note - We are using this flag currently to restrict Vendor Call in GetPinStatus flow (Temporary solution)
  FEATURE_RESTRICT_LIST_ACCOUNT = 288;
  // Feature to enable/disable order and txns data enrichment from debit card switch notification.
  FEATURE_ENRICH_ORDER_AND_TXN_FROM_DC_SWITCH = 289;
  // Feature to enable multiple ways to enter tiering
  FEATURE_TIERING_MULTIPLE_WAYS = 290;
  // Feature to enable/disable order and dc mandates view on autoPay screen.
  FEATURE_DC_MANDATES = 291;
  // Feature flag to control release of OFF_APP_UPI preempt flow (i.e. via non-temporal)
  FEATURE_OFF_APP_UPI_PREEMPT = 292;
  // Feature flag to enable money secret footer v2
  FEATURE_MONEY_SECRET_FOOTER_V2 = 293;
  // feature flag to enable auto id for pay incident manager cheque credit transaction
  FEATURE_PAY_INCIDENT_MANAGER_CHEQUE_CREDIT_TRANSACTION = 294;
  // feature flag to enable generic description tile in order receipt
  FEATURE_ORDER_RECEIPT_GENERIC_DESCRIPTION_DETAILS_TILE = 295;
  // Feature flag-to-control ticket resolution CSAT comms
  FEATURE_CX_TICKET_RESOLUTION_CSAT_COMMS = 296;
  // Feature to enable/disable address update flow from debit card order screen.
  FEATURE_DC_ORDER_SCREEN_WA_ADDRESS_UPDATE_FLOW = 297;
  // Feature to enable revamped vkyc v2 flow
  // https://monorail.pointz.in/p/fi-app/issues/detail?id=90797
  FEATURE_VKYC_FLOW_V2 = 298;
  // Feature to enable VKYC benefit screen with comparison module
  // https://monorail.pointz.in/p/fi-app/issues/detail?id=90797
  FEATURE_VKYC_BENEFIT_SCREEN_V2 = 299;
  // Feature flag to enable displaying of Stale Computed balance icon text component on balance card in home
  FEATURE_SHOW_STALE_COMPUTED_BALANCE_WARNING = 300;
  // Feature to enable asset import animation flow post completion of asset import
  FEATURE_ASSET_IMPORT_FLOW = 301;
  // Feature to enable PAN+MF import flow for the wealth builder 2.0 flow
  FEATURE_MF_HOLDINGS_IMPORT_PAN_CONSENT_FLOW = 302;
  // Feature to enable mf import during wealth builder onboarding
  FEATURE_MF_IMPORT_WEALTH_BUILDER_ONBOARDING = 303;
  // feature flag to control release of wealth builder section in home nav bar
  FEATURE_HOME_NAV_BAR_WEALTH_BUILDER_SECTION = 304;
  // feature flag to control asset dashboard deeplink
  FEATURE_ASSET_DASHBOARD_FLOW = 305;
  // feature flag to control roll out of beneficiary name lookup integration
  FEATURE_BENEFICIARY_NAME_LOOKUP = 306;
  // Feature to enable/disable charges API for posting debit card charges in user's savings account
  FEATURE_DC_CHARGES_API = 307;
  // feature flag to enable savings account nominee update
  FEATURE_UPDATE_SAVINGS_ACCOUNT_NOMINEE = 308;
  // feature flag to enable new designs of min balance account screen (With SDUI section)
  // figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=34528-4662&t=DTx18ljrDCqwzsCF-0
  FEATURE_MIN_BALANCE_ACCOUNT_SCREEN_SDUI = 309;
  // Feature to enable themes in mf holdings import
  FEATURE_MF_HOLDINGS_IMPORT_DARK_THEME = 310;
  // feature flag for showing delta balance required in profile and plans v2
  FEATURE_SHOW_REQUIRED_BALANCE_FOR_TIER_UPGRADE = 311;
  // Feature for Chatheads optimised query
  FEATURE_CHATHEADS_OPTIMISED_QUERY = 312;
  // Feature to enable Money Plants in Earned Benefits Screen
  FEATURE_MONEY_PLANT_EARNED_BENEFITS = 313;
  // feature flag to enable auto id for add funds transactions with second leg failure
  FEATURE_PAY_INCIDENT_MANAGER_ONBOARD_ADD_FUNDS_SECOND_LEG_FAILURE = 314;
  // Feature flag to enable UI refinements and enhancements for the new home design.
  // Figma: https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28047-37963&t=6vx7apY5tjgUMWZh-0
  FEATURE_HOME_DESIGN_ENHANCEMENTS = 315;
  // Feature flag for the wealth builder landing page
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=2546-19484&t=357SbDCR0jr1aCXT-4
  FEATURE_WEALTH_BUILDER_NETWORTH_PAGE = 316;
  // Feature flag to enable QR scan enhancements
  FEATURE_QR_SCAN_ENHANCEMENTS = 317;
  // Feature to enable fetch sms data during wealth builder onboarding
  FEATURE_SEND_SMS_DATA_WEALTH_BUILDER = 318;
  // https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-63839&t=D0LXOO8JlqA5WNII-0
  // Feature flag to enable new data collection screens in loans flow
  FEATURE_LOANS_NEW_DATA_COLLECTION_SCREENS = 319;
  // Feature to enable quick link banner in UPI pay landing screen
  FEATURE_UPI_QUICK_LINK_BANNER_PAY_LANDING_SCREEN = 320;
  // https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1569&t=dbNwbHXlR83a2GfC-4
  // Feature flag to enable daily portfolio tracker
  FEATURE_PORTFOLIO_TRACKER_LANDING_SCREEN = 321;
  // Feature flag to enable new design for the second look flow
  // Figma :https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=63454-23835&t=J79RcvDgRU2Y5zUf-0
  FEATURE_LOANS_SECOND_LOOK_V1 = 322;
  // feature flag to enable prequal offer flows in Loans
  FEATURE_LOANS_PREQUAL_OFFER_FLOW = 323;
  // feature flag to decide if pin screen v2 should be shown while onboarding.
  FEATURE_SHOULD_SHOW_PIN_SCREEN_V2 = 324;
  // Feature flag to enable UI refinements and enhancements for the new pay landing screen design.
  // Figma: https://www.figma.com/design/FWW6omNbhzvzwBqlMT03am/D-fixit---workfile?node-id=816-100466&t=XUiu62Z60Gog7svF-0
  FEATURE_PAY_DESIGN_ENHANCEMENTS = 325;
  // feature flag to control roll out of failed ENACH transaction creation flow
  FEATURE_FAILED_ENACH_TRANSACTIONS = 326;
  // Feature flag to control whether AMB Entrypoint Banner should be displayed to user
  // This banner will appear on the user's account summary page when enabled
  // Figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay---Workfile?node-id=36832-49342&t=CS7H6Hd6TMoC6gwJ-0
  FEATURE_AMB_ENTRYPOINT_BANNER = 327;
  // https://monorail.pointz.in/p/fi-app/issues/detail?id=96205
  FEATURE_CA_FLOW_WEALTH_BUILDER_ONBOARDING = 328;
  // Feature to enable daily total transaction limit on app
  FEATURE_DAILY_TOTAL_TRANSACTION_AMOUNT_LIMIT_ON_APP = 329;
  // Feature to enable freshchat chatbot SDK experiment
  FEATURE_FRESHCHAT_CHATBOT_SDK_ENABLED = 330;
  // Feature flag to enable/disable new cx landing page
  // Figma: https://www.figma.com/design/G8WIaAnPtwHBRLURYrS6jX/Help-%E2%80%A2-FFF-%E2%80%A2-v2.0?node-id=10999-34926&t=WFUh2hRcaHUvoUg6-4
  FEATURE_CX_NEW_LANDING_PAGE = 331;
  // Feature flag to enable/disable new rewards catalog merged page
  // Figma: https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=30402-16385&t=L5cs9CRphCT9WXJS-4
  FEATURE_REWARDS_CATALOG_MERGED_PAGE = 332;
  // feature flag to control roll out of indian stocks in net worth daily report page
  FEATURE_NET_WORTH_INDIAN_STOCKS_DISTRIBUTION = 333;
  // Feature flag to enable/disable liabilities section on WB dashboard
  FEATURE_WB_DASHBOARD_LIABILITIES = 334;

  FEATURE_WB_MAGIC_IMPORT = 335;
  // Feature flag to enable/disable Weekly Portfolio Tracker
  // Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=11417-3443&t=KVw50P7F6VMVXEEq-4
  FEATURE_WEEKLY_PORTFOLIO_TRACKER = 336;
  // Feature flag to enable/disable US Funds in Invest Landing Page
  FEATURE_US_FUNDS_IN_INVEST_LANDING_PAGE = 337;
  // Feature flag to enable redis based counter on Upi payments vendor Enquiry (ReqCheckTxnStatus)
  FEATURE_UPI_ENQUIRY_REDIS_BASED_COUNTER = 338;
  // Feature flag to enable/disable ruPay CC GTM banner on Pay Landing Screen.
  // Currently we are doing this from BE only as We need some client fixes to make it Sherlock driven
  FEATURE_PAY_LANDING_BANNER_RUPAY_CC_BE_DRIVEN = 339;
  // Feature flag to enable fetching pay landing screen banner via Dynamic Elements RPC.
  FEATURE_PAY_LANDING_SCREEN_BANNER_DYNAMIC_ELEMENTS = 340;
  // Feature flag to enable FI mcp TOTP code
  FEATURE_FI_MCP_TOTP_CODE = 341;
  // feature flag to control roll-out of credit card v2 (sdk based) flow
  FEATURE_CREDIT_CARD_V2_FLOW = 342;
  // Feature flag to enable credit report money secret v2
  FEATURE_CREDIT_REPORT_MONEY_SECRET = 343;
  // feature flag to enable networth mcp
  FEATURE_NETWORTH_MCP = 344;
  // feature flag to enable upgrade plan icon in explore
  FEATURE_TIERING_UPGRADE_PLAN_IN_EXPLORE = 345;
  // feature flag to enable pitching in profile page
  FEATURE_TIERING_PITCH_IN_PROFILE = 355;
  // feature flag to enable pitching in profile page
  FEATURE_TIERING_PITCH_THROUGH_PROMO_WIDGET = 356;
  // feature flag to test Indian Stocks data pull for 20 years
  FEATURE_INDIAN_STOCKS_EXTENDED_RANGE = 357;
  // feature flag to enable nugget chatbot
  FEATURE_NUGGET_CHATBOT = 358;
}
