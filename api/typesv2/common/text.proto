syntax = "proto3";

package api.typesv2.common;

import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/be-common/api/typesv2/common";
option java_package = "com.github.epifi.gamma.api.typesv2.common";

message Text {
  // use 'display_text' instead
  string text = 1 [deprecated = true];
  // font color of the text
  string font_color = 2;
  // background color where the text is rendered
  string bg_color = 3;
  // text to be shown
  oneof display_value {
    // regular string text, ie not rendered as html
    string plain_string = 4;
    // text that would be rendered as html
    // basic formatting should be present as part of html only
    // however, if font color and bg color is provided separately that would override the formatting in HTML
    string html = 5;
    // string with an embedded URL with the below format:
    // "this is custom text with links to ^^www.google.com^^Google^^ and ^^www.facebook.com^^Facebook^^ embedded"
    string embedded_links = 10;
    // text with images annotated at arbitrary positions within the text
    // Example UI: https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11166-8184&node-type=frame&t=GDndkA18B9RIUJcJ-0
    AnnotatedText annotated_text = 13;
  }
  oneof font_style {
    FontStyle standard_font_style = 6;
    FontStyleInfo custom_font_style = 7;
  }
  // opacity of font color of the text. Value denotes percentage and ranges between 0-100
  int32 font_color_opacity = 8;

  // identifiers for strings that can be fetched by the client from local db
  // strings that need to be replaced are to be sent in the format {#string#} in plain_string field
  // client will have a mapping of StringFormatter enum strings to the string that should be replaced by that enum string
  // if this list is empty client will not format the string and look for strings to replace
  repeated StringFormatter string_formatters = 9;

  // [Optional] Field to specify how to align the text. Can be unspecified, which will mean clients use platform default alignment
  TextAlignment alignment = 11;

  // Enum to decide the text alignment for the text
  enum TextAlignment {
    // need not align if the arrangement is unspecified. Clients will default to platform specific default alignment in such cases
    ALIGNMENT_UNSPECIFIED = 0;
    // aligns the text to the left
    ALIGNMENT_LEFT = 1;
    // aligns the text to the center
    ALIGNMENT_CENTER = 2;
    // aligns the text to the right
    ALIGNMENT_RIGHT = 3;
  }

  // max number of lines text can take
  // if 0 it will take the required number of lines it takes (no constraint)
  // else it will take the maximum number of lines passed e.g 1,2 etc
  int32 max_lines = 12;

  // A Text rendering which supports inline icons
  message AnnotatedText {
    repeated Span spans = 1;
    // Defines the different kinds of spans in an annotated text, either string text or a visual element icon
    message Span {
      oneof span_content {
        // The text in this span. The styling and font properties are rendered from the containing Text proto
        string text = 1;
        // An icon rendered inline.
        VisualElement icon = 2;
      }
    }
  }
}

/*
  Standard font style to be used. These font styles are supposed to be standardized across the design, ui and be teams.
  UI team should have all the details needed to render a font style on their end.
  Please check with UI team if they have the necessary details for rendering the font before adding it here
  Standardization Figma: https://www.figma.com/file/Rz4mvhX4kKOcxh7yviNlZ3/%F0%9F%92%88-Design-System-2.0?node-id=2825%3A11234
 */
enum FontStyle {
  FONT_STYLE_UNSPECIFIED = 0;
  SUBTITLE_3 = 1;
  BODY_3_PARA = 2;
  SUBTITLE_0 = 3;
  SUBTITLE_M = 4;
  HEADLINE_M = 5;
  SUBTITLE_XS = 6;
  SUBTITLE_2 = 7;
  SUBTITLE_1 = 8;
  SUBTITLE_S = 9;
  HEADLINE_L = 10;
  HEADLINE_XL = 11;
  NUMBER_M = 12;
  NUMBER_3XL = 13;
  OVERLINE_2XS_CAPS = 14;
  BUTTON_S = 15;
  NUMBER_S = 16;
  BODY_3 = 17;
  BODY_XS = 18;
  NUMBER_XL = 19;
  BODY_S = 20;
  HEADLINE_1 = 21;
  HEADLINE_2 = 22;
  HEADLINE_3 = 23;
  HEADLINE_4 = 24;
  HEADLINE_1_TRACKED = 25;
  HEADLINE_5 = 26;
  BODY_1 = 27;
  BODY_2 = 28;
  BODY_4 = 29;
  BODY_4_PARA = 30;
  BODY_5 = 31;
  BODY_1_TRACKED = 32;
  BUTTON_1 = 33;
  BUTTON_2 = 34;
  BUTTON_3 = 35;
  BUTTON_4 = 36;
  BUTTON_5 = 37;
  OVERLINE_1 = 38;
  OVERLINE_2 = 39;
  OVERLINE_3 = 40;
  NUMBERS_1 = 41;
  NUMBERS_2 = 42;
  NUMBERS_3 = 43;
  NUMBERS_4 = 44;
  NUMBERS_5 = 45;
  NUMBERS_6 = 46;
  NUMBERS_7 = 47;
  NUMBERS_8 = 48;
  MICRO_1 = 49;
  CAPTION_1 = 50;
  NUMBER_2XL = 51;
  NUMBER_L = 52;
  NUMBER_XS = 53;
  NUMBER_2XS = 54;
  DISPLAY_XL = 55;
  OVERLINE_3XS_CAPS = 56;
  RUPEE_XL = 57;
  HEADLINE_S = 58;
  BUTTON_XS = 59;
  CAPTION_2 = 60;
  OVERLINE_XS_CAPS = 61;
  HEADLINE_2XL = 62;
  SUBTITLE_2XL = 63;
  BUTTON_M = 64;
  HEADLINE_XS = 65;
  RUPEE_M = 66;
  SUBTITLE_L = 67;
  SUBTITLE_2XS = 68;
  CURRENCY_M = 69;
  OVERLINE_S_CAPS = 70;
  DISPLAY_L = 71;
  CURRENCY_XL = 72;
  DISPLAY_2XL = 73;
  DISPLAY_3XL = 74;
  SUBTITLE_XL = 75;
  DISPLAY_M = 76;
  HEADLINE_4XL = 77;
  DISPLAY_4XL = 78;
}

/*
  Info for rendering the font.
  We should ideally standardize fonts across the app and use FontStyle enum instead.
  Please evaluate the same before using this and only use in cases where standardization is not needed/present
 */
message FontStyleInfo {
  // family name. Eg: Gilroy, Inter
  // Note: We cannot send any family name here, please check with client before sending values here
  // At the time of writing this, only Gilroy and Inter are supported.
  // Please check with client if passing anything other than 'Gilroy' and 'Inter'
  string font_family = 1;
  // font style. Eg: normal, italic
  // default to 'normal' if not present
  string font_style = 2;
  // font size. Eg: 14px, 16px
  string font_size = 3;
}

message TextWithIcon {
  Text text = 1;
  // deprecating icon_url in favor of visual element for better control from backend
  string icon_url = 2 [deprecated = true];
  VisualElement visual_element = 3;
}
message TitleDescriptionComponent {
  Text title = 1;
  Text description = 2;
}

// this enum is used by client to identify strings that can be fetched from the local db
enum StringFormatter {
  STRING_FORMATTER_UNSPECIFIED = 0;
  STRING_FORMATTER_SAVINGS_ACCOUNT_NUMBER = 1;
}

