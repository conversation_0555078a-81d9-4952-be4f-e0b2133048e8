syntax = "proto3";

package api.typesv2.common;

option go_package = "github.com/epifi/be-common/api/typesv2/common";
option java_package = "com.github.epifi.gamma.api.typesv2.common";

// Duration represents a time duration specified with a unit.
message Duration {
  // The numeric value of the duration.
  uint32 duration = 1;
  // The unit of the duration.
  DurationUnit duration_unit = 2;
}

// DurationUnit represents the unit of a duration.
enum DurationUnit {
  DURATION_UNIT_UNSPECIFIED = 0;
  DURATION_UNIT_DAYS = 1;
  DURATION_UNIT_MONTHS = 2;
  DURATION_UNIT_YEARS = 3;
}
