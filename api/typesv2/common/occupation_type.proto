syntax = "proto3";

package api.typesv2.common;

option go_package = "github.com/epifi/be-common/api/typesv2/common";
option java_package = "com.github.epifi.gamma.api.typesv2.common";

// possible occupation types of a user
enum OccupationType {
  OCCUPATION_TYPE_UNSPECIFIED = 0;
  OCCUPATION_TYPE_LEGAL_AND_JUDICIARY = 1 [deprecated = true];
  OCCUPATION_TYPE_SOFTWARE_AND_IT = 2 [deprecated = true];
  OCCUPATION_TYPE_ENGINEERING = 3;
  OCCUPATION_TYPE_HEALTHCARE = 4;
  OCCUPATION_TYPE_ACADEMIA = 5;
  OCCUPATION_TYPE_BANKING = 6;
  OCCUPATION_TYPE_CHARTERED_ACCOUNTANT = 7;
  OCCUPATION_TYPE_PUBLIC_SERVICES = 8;
  OCCUPATION_TYPE_MERCHANT_AND_TRADE = 9;
  OCCUPATION_TYPE_NEWS_AND_MEDIA = 10;
  OCCUPATION_TYPE_BUSINESS = 11;
  OCCUPATION_TYPE_AVIATION = 12 [deprecated = true];
  OCCUPATION_TYPE_REAL_ESTATE_AND_INFRASTRUCTURE = 13;
  OCCUPATION_TYPE_DEFENCE_AND_LAW_ENFORCEMENT = 14;
  OCCUPATION_TYPE_MARKETING_AND_SALES = 15;
  OCCUPATION_TYPE_OTHERS = 16 [deprecated = true];
  OCCUPATION_TYPE_ENTERTAINMENT = 17;
  OCCUPATION_TYPE_CRYPTO_TRADING = 18;
  OCCUPATION_TYPE_LUXURY_CAR_DEALER = 19;
  OCCUPATION_TYPE_SCRAP_DEALER = 20;
  OCCUPATION_TYPE_STUDENT = 21;
  OCCUPATION_TYPE_HOMEMAKER = 22;
  OCCUPATION_TYPE_RETIRED = 23;
  OCCUPATION_TYPE_JUDGE = 24;
  OCCUPATION_TYPE_ADVOCATE = 25;
  OCCUPATION_TYPE_FUND_MANAGEMENT = 26;
  OCCUPATION_TYPE_SELF_EMPLOYED = 27;
  OCCUPATION_TYPE_DIPLOMAT = 28;
  OCCUPATION_TYPE_AGRICULTURE = 29;
  OCCUPATION_TYPE_VIRTUAL_CURRENCY_DEALER = 30;
  OCCUPATION_TYPE_ART_ANTIQUES_DEALER = 31;
  OCCUPATION_TYPE_ARM_ARMAMENTS_DEALER = 32;
  OCCUPATION_TYPE_GOLD_PRECIOUS_STONE_DEALER = 33;
  OCCUPATION_TYPE_PAWN_BROKER = 34;
}
