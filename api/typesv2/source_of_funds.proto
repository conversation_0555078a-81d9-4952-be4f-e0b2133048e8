syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// Source of funds for savings account
enum SourceOfFunds {
  SOURCE_OF_FUNDS_UNSPECIFIED = 0;
  BUSINESS_INCOME = 1;
  PARENT_OR_SPOUSE_OR_SIBLINGS = 2;
  AGRICULTURE = 3;
  RETIREMENT_BENEFITS = 4;
  OTHER_SOURCE = 5;
  PERSONAL_SAVINGS = 6;
  PROCEEDS_OF_SHARES_OR_INVESTMENT = 7;
  RENTAL_INTEREST_DIVIDEND_INCOME = 8;
  SALARY = 9;
}
