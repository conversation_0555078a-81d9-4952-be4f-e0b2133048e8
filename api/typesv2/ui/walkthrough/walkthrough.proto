syntax = "proto3";

package api.typesv2.ui.walkthrough;

import "api/typesv2/common/text.proto";
import "api/typesv2/ui/walkthrough/common.proto";
import "api/typesv2/ui/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui/walkthrough";
option java_package = "com.github.epifi.gamma.api.typesv2.ui.walkthrough";

// Walkthrough will contain all the information to introduce any changes on a specific ui screen
// this includes addition, removal, or ordering changes of ui elements on the screen
message Walkthrough {
  string id = 1;

  // describes entry point of the walkthrough journey
  WalkthroughEntryPoint entry_point = 2;

  // describes a step in a sequence of walkthrough steps
  repeated WalkthroughStep steps = 3;

  // describes how the walkthrough will be triggered
  WalkthroughTrigger trigger = 4;

  // flag to indicate if walkthrough is enabled or disabled from backend
  bool is_enabled = 5;
}

// WalkthroughEntryPoint describes entry points of the walkthrough journey
message WalkthroughEntryPoint {
  oneof entry_point {
    DashboardIntroCardDetails dashboard_intro_card_details = 1;
  }
}

// DashboardIntroCardDetails describes intro card as an entry point to walkthrough steps
message DashboardIntroCardDetails {
  // cta for the user to get started with walk through
  CTA start_cta = 1;
  // Max number of impressions for which the intro card will be shown
  int32 show_card_max_impressions = 2;
  // Title to be shown on intro card
  typesv2.common.Text title = 3;
  // Image to be shown on intro card
  typesv2.common.VisualElement visual_element = 4;
  // Shadow for home intro card
  repeated typesv2.ui.Shadow shadow = 5;
}

// WalkthroughStep describes a step in a sequence of walkthrough steps
message WalkthroughStep {
  oneof step {
    // FullScreenAnnouncementStep is a type of walkthrough step in which changes are announced in a full screen format
    FullScreenAnnouncementStep full_screen_announcement_step = 1;

    // DescribeScreenElementStep is a type of walkthrough step in which ui element is highlighted and described
    DescribeScreenElementStep describe_screen_element_step = 2;

    // FocusScreenElementStep is a type of walkthrough step in which ui element is focussed and a message is shown
    FocusScreenElementStep focus_screen_element_step = 3;
  }
}

// FullScreenAnnouncementStep is a type of walkthrough step in which changes are announced in a full screen format
// https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=17336%3A86844&mode=design&t=QQXaZo0rqpNZzcWU-1
message FullScreenAnnouncementStep {
  // visual element to be shown in above the message
  typesv2.common.VisualElement visual_element = 1;

  // message to be shown in the announcement step
  typesv2.common.Text message = 2;

  // cta to be shown below the message
  // will take user to next step or a deeplink or finish walkthrough
  CTA primary_cta = 3;

  // cta at the corner of the screen
  // will typically be a skip button to skip the walkthrough flow
  CTA secondary_cta = 4;
}

// DescribeScreenElementStep is a type of walkthrough step in which ui element is highlighted and described
// https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=17336%3A80648&mode=design&t=QQXaZo0rqpNZzcWU-1
message DescribeScreenElementStep {
  // id of the element which should be highlighted for the step
  // this can be a container, an icon, or any other ui element
  string element_id = 1;

  // message to be shown in walkthrough step popup
  // this describes element highlighted for the popup
  typesv2.common.Text message = 2;

  // cta near the popup message
  // will take user to next step or a deeplink or finish walkthrough
  CTA primary_cta = 3;

  // cta at the corner of the screen
  // will typically be a skip button to skip the walkthrough flow
  CTA secondary_cta = 4;
}

// FocusScreenElementStep is a type of walkthrough step in which a particular ui element is focussed some message is displayed
// https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=17336%3A80648&mode=design&t=QQXaZo0rqpNZzcWU-1
message FocusScreenElementStep {
  // denotes the element type to show focus on, in walkthrough step
  enum FocusElementType {
    FOCUS_ELEMENT_TYPE_UNSPECIFIED = 0;
    DASHBOARD_PRIVACY = 1;
    ADD_AND_MANAGE_SHORTCUTS = 2;
  }
  // type of the element which should be focussed for the step
  // this can be a container, an icon, or any other ui element
  FocusElementType focus_element_type = 1;

  // message to be shown in walkthrough step popup
  // this describes element focussed for the popup
  typesv2.common.Text message = 2;

  // cta near the popup message
  // will take user to next step or a deeplink or finish walkthrough
  CTA primary_cta = 3;

  // cta at the corner of the screen
  // will typically be a skip button to skip the walkthrough flow
  CTA secondary_cta = 4;

  // id of the parent element which contains the element to be focused
  // this will be used by client to locate the parent ui element
  string parent_element_id = 5;
}

// WalkthroughTrigger specifies how the walkthrough will be triggered
enum WalkthroughTrigger {
  WALKTHROUGH_TRIGGER_UNSPECIFIED = 0;
  // for auto triggered walkthrough, client will show walkthrough messages automatically
  WALKTHROUGH_TRIGGER_AUTO = 1;
  // for entry point driven walkthrough, client will show walkthrough messages when user clicks on entry point
  // it will not be dismissed until the user finishes all the steps in the walkthrough or visits app for x sessions
  WALKTHROUGH_TRIGGER_ENTRY_POINT = 2;
  // for auto and entry point driven walkthrough, client will populate the entry point as well as trigger walkthrough automatically
  // entry point will not be dismissed until the user finishes all the steps in the walkthrough or visits app for x sessions
  WALKTHROUGH_TRIGGER_AUTO_AND_ENTRY_POINT = 3;
}
