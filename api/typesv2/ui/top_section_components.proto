syntax = "proto3";

package api.typesv2.ui;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui";
option java_package = "com.github.epifi.gamma.api.typesv2.ui";

// UI element that contains a visual Element, title, and subtitle fields in vertical order
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-64946&t=IGmJANLtILqYrUaN-4
message TopSection {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text subtitle = 2;
  api.typesv2.common.VisualElement image = 3;
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 4;
  repeated ProgressVisualisation progress_bar_visualisation = 5;
}

enum ProgressVisualisationType {
  PROGRESS_VISUALISATION_TYPE_UNSPECIFIED = 0;
  PROGRESS_VISUALISATION_TYPE_SEMI_CIRCLE = 1;
  PROGRESS_VISUALISATION_TYPE_CIRCLE = 2;
  PROGRESS_VISUALISATION_TYPE_LINE = 3;
}

message ProgressVisualisation {
  int64 current_progress = 1;
  int64 max_progress = 2;
  int64 track_width = 3;
  int64 progress_bar_width = 4;
  typesv2.common.ui.widget.BackgroundColour track_color = 5;
  typesv2.common.ui.widget.BackgroundColour progress_bar_color = 6;
  ProgressVisualisationType visualisation_type = 7;
  oneof visualisation {
    SemiCircleProgressVisualisation semi_circle_progress_visualisation = 8;
    CircleProgressVisualisation circle_progress_visualisation = 9;
    LineProgressVisualisation line_progress_visualisation = 10;
  }
}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=447-10393&mode=design&t=hUQ1W9x7CV6BJTYF-0
message SemiCircleProgressVisualisation {
}

// https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16392&mode=design&t=hUQ1W9x7CV6BJTYF-0
message CircleProgressVisualisation {
}

// LineProgressVisualisation is a visualisation which can be used to show progress on a line. The line has a gradient color.
// There is also label which is shown below the progress.
// figma: https://www.figma.com/file/vfHzUbIFS5cGwDfEempkRB/%F0%9F%92%B0-LAMF-%E2%80%A2-FFF?type=design&node-id=477-16458&mode=design&t=7vaja8dLnmbHqoOM-0
message LineProgressVisualisation {
  typesv2.ui.IconTextComponent progress_value = 1;
  int64 progress_bar_height = 2;
}


// UI element that contains a ITC and scrolls vertically
// https://www.figma.com/design/LNihkuiVc7sQ0tOI41w7Jx/%F0%9F%92%B0-Lending-%E2%80%A2-FFF?node-id=68443-64954&t=IGmJANLtILqYrUaN-4
message USPCarouselComponent {
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
  repeated api.typesv2.ui.IconTextComponent usp_messages = 2;
  bool hide_indicators = 3;
}
