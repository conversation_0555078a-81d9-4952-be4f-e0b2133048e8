syntax = "proto3";

package api.typesv2.ui.sdui.sections;

import "api/typesv2/ui/sdui/behaviors/lifecycle_behaviors.proto";
import "api/typesv2/ui/sdui/behaviors/interaction_behaviors.proto";
import "api/typesv2/ui/sdui/components/component.proto";
import "api/typesv2/ui/sdui/properties/visual_properties.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui/sdui/sections";
option java_package = "com.github.epifi.gamma.api.typesv2.ui.sdui.sections";

// Figma screen link: https://www.figma.com/design/4lsyjL4eL8JMw7KN4pDklI/MagniFi-Credit-Card-%E2%80%A2-FFF?node-id=2391-516&t=LkluBpzFGH2480QH-4
// Drive link to section: https://drive.google.com/file/d/10mNGYl7mjn5TICzs9gbv0ny2TdhpO8YL/view?usp=drive_link
// A section that can be expanded or collapsed to show or hide its content
message ExpandableSection {
  // The header of the expandable section
  message Header {
    // Text to be shown
    api.typesv2.common.Text text = 1;
    // Icon to be shown to the left of the text
    api.typesv2.common.VisualElement left_icon = 2;
    // Right Icon when card in expanded
    api.typesv2.common.VisualElement right_icon_expanded = 3;
    // Right Icon when card in collapsed
    api.typesv2.common.VisualElement right_icon_collapsed = 4;
  }
  Header header = 1;
  // The components to be rendered in the expanded area
  components.Component expandable_content = 2;
  // The visual properties of the section, like background color, etc.
  repeated properties.VisualProperty visual_properties = 3;
  // Default state of the card, when the card will be displayed.
  bool is_expanded = 4;
  // (Optional) Behaviour (and optional Analytics metadata) to be used by clients when this section loads on the screen
  // (may or may not be visible)
  behaviors.LifecycleBehavior load_behavior = 5;
  // (Optional) Behaviours (and optional Analytics metadata) to be used by clients when this section is visible on the
  // client device's screen
  behaviors.LifecycleBehavior visible_behavior = 6;
  // (Optional) Behaviours (and optional Analytics metadata) to be used by clients when the expandable section is expanded/collapsed
  repeated behaviors.InteractionBehavior interaction_behaviors = 7;
}
