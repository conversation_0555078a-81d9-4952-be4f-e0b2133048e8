syntax = "proto3";

package api.typesv2.ui.sdui.sections;

import "api/typesv2/ui/sdui/behaviors/lifecycle_behaviors.proto";
import "api/typesv2/ui/sdui/sections/depth_wise_list_section.proto";
import "api/typesv2/ui/sdui/sections/vertical_list_section.proto";
import "api/typesv2/ui/sdui/sections/horizontal_list_section.proto";
import "api/typesv2/ui/sdui/sections/grid_list_section.proto";
import "api/typesv2/ui/sdui/sections/expandable_section.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui/sdui/sections";
option java_package = "com.github.epifi.gamma.api.typesv2.ui.sdui.sections";

// Section is a container for various typesv2 of sections like DepthWiseListSection, HorizontalListSection, VerticalListSection, GridListSection.
message Section {
  // A content field inside Section, which is a one-of of various typesv2 of Section message definitions
  oneof content {
    DepthWiseListSection depth_wise_list_section = 1;
    HorizontalListSection horizontal_list_section = 2;
    VerticalListSection vertical_list_section = 3;
    GridListSection grid_list_section = 4;
    ExpandableSection expandable_section = 5;
  }
  // Optional Behaviors and visual properties for the section. These are deprecated because we want to define behaviours
  // in individual section oneofs instead, due to the way we parse the section content from Any:
  // https://github.com/epiFi/android/blob/master/core/core/src/main/kotlin/com/epifi/paisa/core/network/sdui/components/SduiComponentConverters.kt#L196
  behaviors.LifecycleBehavior load_behavior = 6 [deprecated = true];
  behaviors.LifecycleBehavior visible_behavior = 7 [deprecated = true];
}
