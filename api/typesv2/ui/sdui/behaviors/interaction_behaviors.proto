syntax = "proto3";

package api.typesv2.ui.sdui.behaviors;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/ui/sdui/analytics/analytics_event.proto";
import "api/typesv2/ui/sdui/properties/visual_properties.proto";
import "google/protobuf/any.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors";
option java_package = "com.github.epifi.gamma.api.typesv2.ui.sdui.behaviors";

/**
Message defining the common lifecycle related behaviors that may be associated with a Screen, Section or component
Lifecycle: load, hide, view
 */
message InteractionBehavior {
  oneof behavior {
    OnClickBehavior on_click_behavior = 1;
    OnLongPressBehavior on_long_press_behavior = 2;
    TooltipBehavior tooltip_behavior = 4;
    OnExpandBehaviour expand_behavior = 5;
    OnCollapseBehaviour collapse_behavior = 6;
  }
  // (Optional) Analytics event to be associated with this interaction. This is logged by clients whenever client apps
  // execute this interaction
  analytics.AnalyticsEvent analytics_event = 3;
}

message OnClickBehavior {
  google.protobuf.Any action = 1;
}

message OnLongPressBehavior {
  google.protobuf.Any action = 1;
}

message OnExpandBehaviour {
  google.protobuf.Any action = 1;
}

message OnCollapseBehaviour {
  google.protobuf.Any action = 1;
}
// A behavior that associates a Tooltip with a SDUI section.
// Example UI of a tooltip: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=15732-56637&t=w2JmBEKFDwyID134-4
message TooltipBehavior {
  // The text to be displayed as a tooltip
  common.Text tooltip_text = 1 [(validate.rules).any.required = true];
  // (Optional) The position of the tooltip relative to the component, If not specified, the default position is top
  TooltipPosition position = 2;
  // (Optional) The background colour of the tooltip. By default, this will be #313234 in clients, if not specified
  common.ui.widget.BackgroundColour background_colour = 3;
  // (Optional) The arrow position offset percent (w.r.t the width of the Tooltip). Specifies the offset percent of the
  // tooltip popup from the anchor point. This is 0 by default
  int32 tooltipMidOffsetPercent = 4 [(validate.rules).int32 = {gte: 0, lte: 100}];
  // (Optional) The Size of the tooltip. If not specified, the default width is Width: Wrap, Height: Wrap
  properties.Size size = 5;

  // Specifies the position of the tooltip relative to the section
  enum TooltipPosition {
    POSITION_UNSPECIFIED = 0;
    POSITION_TOP = 1;
    POSITION_BOTTOM = 2;
    POSITION_START = 3;
    POSITION_END = 4;
    POSITION_CENTER = 5;
  }
}
