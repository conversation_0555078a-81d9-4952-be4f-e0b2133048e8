syntax = "proto3";

package api.typesv2.ui.sdui.properties;

import "api/typesv2/common/ui/widget/widget_themes.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui/sdui/properties";
option java_package = "com.github.epifi.gamma.api.typesv2.ui.sdui.properties";

option java_multiple_files = true;

// VisualProperty is a generic message that can be used to set any visual property of a widget.
message VisualProperty {
  oneof properties {
    BorderProperty border = 1;
    PaddingProperty padding = 2;
    CornerProperty corner = 3;
    typesv2.common.ui.widget.Shadow shadow = 4;
    typesv2.common.ui.widget.BackgroundColour bg_color = 5;
    Size size = 6;
    Position position = 7;
    // Message with all possible container attributes at one place
    ContainerProperty container_property = 8;
  }
}

// BorderProperty is used to set the border of a widget.
message BorderProperty {
  int32 border_thickness = 1;
  string border_color = 2;
  // Corner all sides corner radius for the border
  int32 corner_radius = 3;
  // Whether the border should be drawn in dash (or dotted) style
  bool is_dash = 4;
  // Length of each dash, if the is_dash is set to true. Other-wise this value has no effect
  int32 dash_length = 5;
  // Length of each gap between the dashes, if the is_dash is set to true. Other-wise this value has no effect
  int32 dash_gap_length = 6;
  // [Optional] background color of the border - supports gradient border as well
  // dashed border client support pending for border_bg_color
  typesv2.common.ui.widget.BackgroundColour border_bg_color = 7;
}

// CornerProperty is used to set the corner radius of a widget.
message CornerProperty {
  int32 top_left_corner_radius = 1;
  int32 top_right_corner_radius = 2;
  int32 bottom_left_corner = 3;
  int32 bottom_right_corner = 4;
}

// PaddingProperty is used to set the padding of a widget.
message PaddingProperty {
  int32 left = 1;
  int32 top = 2;
  int32 right = 3;
  int32 bottom = 4;
}

// Size is used to set the size of a widget.
message Size {
  Dimension width = 1;
  Dimension height = 2;

  message Dimension {
    enum Type {
      DIMENSION_TYPE_UNSPECIFIED = 0;
      DIMENSION_TYPE_FILL = 1;
      DIMENSION_TYPE_WRAP = 2;
      DIMENSION_TYPE_EXACT = 3;
      DIMENSION_TYPE_WEIGHT = 4;
    }
    Type type = 1;
    // [Optional] Field set when the Type is DIMENSION_TYPE_EXACT, to determine the exact dimensions for rendering on the
    // clients (Is usually Points in iOS, Dp in Android)
    int32 exact_value = 2;

    // [Optional] When this field is set, it represents the percentage of the available space allocated to the parent component.
    // This is useful when we want to allocate space based on the percentage of the widget. For example, if we have 3 widgets
    // with weights 20, 30, 50, the space will be allocated in the ratio 20%:30%:50%. If the total space available is 100,
    // the first widget will get 20, the second 30, and the third 50.
    Weight weight = 3;
  }
}

// [Optional] When this field is set, it represents the percentage of the available space allocated to the parent component.
// This is useful when we want to allocate space based on the percentage of the widget. For example, if we have 3 widgets
// with weights 20, 30, 50, the space will be allocated in the ratio 20%:30%:50%. If the total space available is 100,
// the first widget will get 20, the second 30, and the third 50.
message Weight {
  float value = 1 [(validate.rules).float.gte = 0, (validate.rules).float.lte = 100];
}

// Position is used to set the position of a widget.
message Position {
  int32 xAxis = 1;
  int32 yAxis = 2;
}

// A single property to Specify a bunch of background, corner and other container properties together. On clients
// (especially Android), it is essential, to maintain the right order of application of background and corner. Hence,
// services should use this to add all possible values in one object. This will allow clients to code the right order of
// application of container properties
message ContainerProperty {
  // Size specifications of the container
  Size size = 1;
  // [Optional] background color of the container
  typesv2.common.ui.widget.BackgroundColour bg_color = 2;
  // [Optional] corner properties of the container
  CornerProperty corner = 3;
  // [Optional] border to apply to the container if applicable
  BorderProperty border = 4;
  // [Optional] Padding to apply inside the container, if applicable
  PaddingProperty padding = 5;
  // [Optional] Margin values to leave outside the container's space
  PaddingProperty margin = 6;
  // [Optional] Shadow values to drop below the container
  typesv2.common.ui.widget.Shadow shadow = 7;
  // [Optional] Position of this container w.r.t it's parent
  Position position = 8;
}

// A message to specify for Vertical or Horizontal lists, about the amount of overlap among the elements. For example,
// This helps create a visual like the stacked Images here:
// https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=4264-12507&mode=design&t=nTQvmcw6cSRbF4Bc-0
message ListElementOverlapProps {
  // Amount of overlap in Dps (Android) or Points (iOS) between the elements in the list
  int32 overlap_device_pixels = 1;
  // Amount of padding (in Dps (Android) or Points (iOS)) to specify the gap between the overlaps
  int32 overlap_padding_pixels = 2;
  // (Optional) The color of the padding gap. By default, clients will render transparent color
  typesv2.common.ui.widget.BackgroundColour padding_bg_color = 3;
  // (Optional) Corner radius of the overlap gap area.
  // This should be similar to the overlapping children's corner radius.
  // Since the List parent does not know what kind children it will render, it is beneficial to define the property at List parent level
  int32 overlap_corner_radius_pixels = 4;
}
