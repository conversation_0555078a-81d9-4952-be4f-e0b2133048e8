syntax = "proto3";

package api.typesv2.ui.insights.wealthanalyser;

import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "api/frontend/deeplink/deeplink.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui/insights/wealthanalyser";
option java_package = "com.github.epifi.gamma.api.typesv2.ui.insights.wealthanalyser";

// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-40289&node-type=frame&t=fYZhJQpPl0m956gT-0
message AssetsAnalysisCard {
  oneof data {
    PrimaryCard primary_card = 1;
    SecondaryCard secondary_card = 2;
  }

  // Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-40289&node-type=frame&t=fYZhJQpPl0m956gT-0
  message PrimaryCard {
    // Example: title - "Mutual Funds", subtitle - "Get expert curated analysis of your portfolio!"
    VerticalKeyValuePair header = 1;

    // Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-40289&node-type=frame&t=fYZhJQpPl0m956gT-0
    typesv2.common.VisualElement logo = 2;

    // Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-40289&node-type=frame&t=fYZhJQpPl0m956gT-0
    typesv2.common.VisualElement content_image = 3;

    // Example: "Connect & see full report"
    IconTextComponent cta = 4;

    // Example: "#D4E6E2"
    string border_color = 5;

    // Example: "#FFFFFF"
    string bg_color = 6;

    // Example: "NEW SCORE AVAILABLE"
    IconTextComponent indicator_label = 7;

    // Example: "#00000014"
    // Client should apply shadow effect only if there is a valid hex string value.
    string shadow_color = 8;
  }

  // Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-40289&node-type=frame&t=fYZhJQpPl0m956gT-0
  message SecondaryCard {
    // Example: title - "All assets", subtitle - "See your future portfolio and check its diversification!"
    VerticalKeyValuePair header = 1;

    // Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-40289&node-type=frame&t=fYZhJQpPl0m956gT-0
    typesv2.common.VisualElement content_image = 2;

    // Example: "Connect"
    IconTextComponent cta = 3;

    // Example: "#FFFFFF"
    string bg_color = 4;

    // Example: "#00000014"
    // Client should apply shadow effect only if there is a valid hex string value.
    string shadow_color = 5;

    // Example: "NEW SCORE AVAILABLE"
    IconTextComponent indicator_label = 6;

    // Flag determines whether the asset is connected or not
    // This will help to show/hide asset value if the asset is connected otherwise display the message which is getting displayed when asset is not connected
    bool asset_connected = 7;
  }

  // Map of event properties to be sent on client eventProperties
  // events sheet https://docs.google.com/spreadsheets/d/1UJWX6L5njKdzz-MvL6o18X0GoPDOSitBTAt7B53lNnI/edit?gid=248983965#gid=248983965
  map<string, string> event_properties = 3;
  // Deeplink to be used for redirections on card tap
  frontend.deeplink.Deeplink deeplink = 4;
}
