syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";


// enum to represent Product Category (L1 details)
enum ProductCategory {
  PRODUCT_CATEGORY_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_TRANSACTIONS = 1;
  PRODUCT_CATEGORY_ACCOUNTS = 2;
  PRODUCT_CATEGORY_ONBOARDING = 3;
  PRODUCT_CATEGORY_SAVE = 4;
  PRODUCT_CATEGORY_WAITLIST = 5;
  PRODUCT_CATEGORY_RE_ONBOARDING = 6;
  PRODUCT_CATEGORY_REWARDS = 7;
  PRODUCT_CATEGORY_FIT = 8;
  PRODUCT_CATEGORY_DEBIT_CARD = 9;
  PRODUCT_CATEGORY_REFERRALS = 10;
  PRODUCT_CATEGORY_CONNECTED_ACCOUNTS = 11;
  PRODUCT_CATEGORY_FRAUD_AND_RISK = 12;
  PRODUCT_CATEGORY_JUMP_P2P = 13;
  PRODUCT_CATEGORY_PROFILE = 14;
  PRODUCT_CATEGORY_SALARY_ACCOUNT = 15;
  PRODUCT_CATEGORY_SEARCH = 16;
  PRODUCT_CATEGORY_WEALTH_ONBOARDING = 17;
  PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS = 18;
  PRODUCT_CATEGORY_APP_CRASH = 19;
  PRODUCT_CATEGORY_DATA_DELETION = 20;
  PRODUCT_CATEGORY_SCREENER = 21;
  PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED = 22;
  PRODUCT_CATEGORY_LANGUAGE_CALLBACK = 23;
  PRODUCT_CATEGORY_CATEGORY_NOT_FOUND = 24;
  PRODUCT_CATEGORY_KYC_OUTCALL = 25;
  PRODUCT_CATEGORY_TRANSACTION_ISSUES = 26;
  PRODUCT_CATEGORY_REWARDS_NEW = 27;
  PRODUCT_CATEGORY_REFERRALS_NEW = 28;
  PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI = 29;
  PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT = 30;
  PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED = 31;
  PRODUCT_CATEGORY_INSTANT_LOANS = 32;
  PRODUCT_CATEGORY_TIERING_PLANS = 33;
  PRODUCT_CATEGORY_CREDIT_CARD = 34;
  PRODUCT_CATEGORY_US_STOCKS = 35;
  PRODUCT_CATEGORY_DEVICE = 36;
  PRODUCT_CATEGORY_RISK = 37;
  PRODUCT_CATEGORY_ON_APP_TRANSACTIONS = 38;
  PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS = 39;
  PRODUCT_CATEGORY_INSTANT_SALARY = 40;
  PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD = 41;
  PRODUCT_CATEGORY_LAMF = 42;
  PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD = 43;
  PRODUCT_CATEGORY_SALARY_LITE = 44;
  PRODUCT_CATEGORY_FI_STORE = 45;
  PRODUCT_CATEGORY_GENERAL_ENQUIRY = 46;
  PRODUCT_CATEGORY_APP_RELATED = 47;
  PRODUCT_CATEGORY_DEPOSITS_AND_INVESTMENTS = 48;
  PRODUCT_CATEGORY_INCOMPLETE_CONVERSATION = 49;
  PRODUCT_CATEGORY_LOANS = 50;
  PRODUCT_CATEGORY_NET_WORTH = 51;
  PRODUCT_CATEGORY_SERVICE_REQUESTS = 52;
}

// enum to represent Product category details (L2 details)
enum ProductCategoryDetails {
  PRODUCT_CATEGORY_DETAILS_UNSPECIFIED = 0;

  // Product category details (L2) for Transactions product category
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP = 1;
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP = 2;

  // Product category details (L2) for Accounts product category
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN = 3;

  // Product category details (L2) for Onboarding product category
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_APP_DOWNLOAD_ISSUE = 4;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_CHECK_FAILURE = 5;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_PHONE_NUMBER_OTP = 6;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_EMAIL_SELECTION_FAILURE = 7;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_MOTHER_FATHER_NAME = 8;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_PAN_NAME_VALIDATION_FAILURE = 9;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_EXISTING_FEDERAL_ACCOUNT = 10;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_KYC = 11;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_LIVENESS = 12;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_FACEMATCH_FAIL = 13;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_UN_NAME_CHECK = 14;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_CONFIRM_CARD_MAILING_ADDRESS = 15;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_UPI_CONSENT_FAILURE = 16;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_REGISTRATION_FAILURE = 17;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_CUSTOMER_CREATION_FAILURE = 18;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED = 19;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_CARD_CREATION_FAILURE = 20;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_CARD_PIN_SET_FAILURE = 21;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_UPI_SETUP_FAILURE = 22;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_VKYC = 23;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_REONBOARDING = 24;

  // Product category details (L2) for DebitCard product category
  PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION = 25;
  PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY = 26;

  // Product category details (L2) for Accounts product category
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY = 27;
  // Product category details (L2) for Pay integration of Watson
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT = 28;
  // Product category details (L2) for Save product category
  PRODUCT_CATEGORY_DETAILS_SAVE_FIXED_DEPOSIT = 29;
  // Product category details (L2) for Save product category
  PRODUCT_CATEGORY_DETAILS_SAVE_SMART_DEPOSIT = 30;

  // Product category details (L2) for wealth mutual funds product category
  PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL = 31;

  // Account related L2 categories
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CLOSURE_REQUEST = 32;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_OPENING_ISSUES = 33;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_UPGRADE_DOWNGRADE = 34;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_BALANCE_TRANSFER = 35;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CATEGORY_NOT_FOUND = 36;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CHEQUEBOOK_RELATED = 37;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_DORMANT = 38;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_FEES_AND_CHARGES = 39;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_KYC_RELATED = 40;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_LIEN_ON_ACCOUNT = 41;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_RE_LOGIN_ISSUES = 42;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_SALARY_PROGRAM = 43;

  // App related L2 categories
  PRODUCT_CATEGORY_DETAILS_APP_RELATED_ISSUES = 44;
  PRODUCT_CATEGORY_DETAILS_APP_BANK_INCOMING = 45;
  PRODUCT_CATEGORY_DETAILS_APP_CX_INCOMING = 46;
  PRODUCT_CATEGORY_DETAILS_APP_BLOCK_PERMANENTLY = 47;

  // Card related L2 categories
  PRODUCT_CATEGORY_DETAILS_CARD_REQUEST = 48;
  PRODUCT_CATEGORY_DETAILS_CARD_SETTINGS = 49;

  // FIT related L2 categories
  PRODUCT_CATEGORY_DETAILS_FIT_RULES = 50;

  // Jump related L2 categories
  PRODUCT_CATEGORY_DETAILS_JUMP = 51;

  // Mutual Funds related L2 categories
  PRODUCT_CATEGORY_DETAILS_MF_INVESTMENTS = 52;
  PRODUCT_CATEGORY_DETAILS_MF_ONBOARDING = 53;
  PRODUCT_CATEGORY_DETAILS_MF_WITHDRAWALS = 54;

  // US Stocks related L2 categories
  PRODUCT_CATEGORY_DETAILS_US_STOCKS_GENERAL = 55;
  PRODUCT_CATEGORY_DETAILS_US_STOCKS_WALLET_ISSUES = 56;

  // Product related L2 categories
  PRODUCT_CATEGORY_DETAILS_DEPRECATED_PRODUCT = 57;

  // Communication related L2 categories
  PRODUCT_CATEGORY_DETAILS_BLANK_CHAT = 58;
  PRODUCT_CATEGORY_DETAILS_CALL_DROP_DISCONNECTED = 59;
  PRODUCT_CATEGORY_DETAILS_INCOMPLETE_EMAIL = 60;

  // Loan related L2 categories
  PRODUCT_CATEGORY_DETAILS_LOAN_APPLICATION_DISBURSAL = 61;
  PRODUCT_CATEGORY_DETAILS_LOAN_BUREAU_CIBIL = 62;
  PRODUCT_CATEGORY_DETAILS_LOAN_COLLECTIONS = 63;
  PRODUCT_CATEGORY_DETAILS_LOAN_EMI_RELATED = 64;
  PRODUCT_CATEGORY_DETAILS_LOAN_LAMF_SHORTFALL = 65;
  PRODUCT_CATEGORY_DETAILS_LOAN_CLOSURE_REQUEST = 66;
  PRODUCT_CATEGORY_DETAILS_LOAN_OUTCALLING = 67;
  PRODUCT_CATEGORY_DETAILS_LOAN_PERSONAL_DETAILS = 68;
  PRODUCT_CATEGORY_DETAILS_LOAN_REFUND_WAIVER = 69;

  // Connection related L2 categories
  PRODUCT_CATEGORY_DETAILS_UNABLE_TO_CONNECT = 70;
  PRODUCT_CATEGORY_DETAILS_UNABLE_TO_DISCONNECT = 71;

  // Rewards related L2 categories
  PRODUCT_CATEGORY_DETAILS_REWARDS_FI_POINTS_NOT_REFUNDED = 72;
  PRODUCT_CATEGORY_DETAILS_REWARDS_GIFT_CARDS = 73;
  PRODUCT_CATEGORY_DETAILS_REWARDS_INCORRECT_REWARD = 74;
  PRODUCT_CATEGORY_DETAILS_REWARDS_NOT_RECEIVED = 75;

  // Security related L2 categories
  PRODUCT_CATEGORY_DETAILS_BANK_INITIATED_FREEZE = 76;
  PRODUCT_CATEGORY_DETAILS_INVESTMENT_WITHDRAWALS = 77;
  PRODUCT_CATEGORY_DETAILS_LEA_NPCI_COMPLAINT = 78;

  // Service request L2 categories
  PRODUCT_CATEGORY_DETAILS_CALLBACK_REQUEST = 79;
  PRODUCT_CATEGORY_DETAILS_DATA_DELETION = 80;
  PRODUCT_CATEGORY_DETAILS_NACH_AND_MANDATES = 81;
  PRODUCT_CATEGORY_DETAILS_REVOKE_APP_ACCESS = 82;
  PRODUCT_CATEGORY_DETAILS_STOP_SERVICES = 83;

  // Transaction related L2 categories
  PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED = 84;
  PRODUCT_CATEGORY_DETAILS_AMOUNT_DEBITED_NOT_CREDITED = 85;
  PRODUCT_CATEGORY_DETAILS_AUTOMATED_PAYMENTS = 86;
  PRODUCT_CATEGORY_DETAILS_UNAUTHORISED_FRAUD_TRANSACTIONS = 87;
  PRODUCT_CATEGORY_DETAILS_CHEQUE_TRANSACTION = 88;
  PRODUCT_CATEGORY_DETAILS_DATA_NOT_REFRESHED = 89;

  // US Stocks specific L2 categories
  PRODUCT_CATEGORY_DETAILS_BUYING_US_STOCKS = 90;

  // Business related L2 categories
  PRODUCT_CATEGORY_DETAILS_BUSINESS_COLLABORATION = 91;

  // Net banking related L2 categories
  PRODUCT_CATEGORY_DETAILS_NET_BANKING = 92;

  // User related L2 categories
  PRODUCT_CATEGORY_DETAILS_UNREGISTERED_USER = 93;
  PRODUCT_CATEGORY_DETAILS_UNABLE_TO_PAY = 94;

  // Credit related L2 categories
  PRODUCT_CATEGORY_DETAILS_CREDIT_PENDING_TO_FI = 95;

  // Document related L2 categories
  PRODUCT_CATEGORY_DETAILS_DOCUMENT_REQUEST = 96;
}

// enums to represent Subcategory (L3 details)
enum SubCategory {
  // enums for L1-Transaction and L2-Debited_Via_Fi_App
  SUB_CATEGORY_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT = 1;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY = 2;

  // enums for L1-Transaction and L2-Debited_Via_Other_App
  SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT = 3;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY = 4;

  // enums for L1-Accounts and L2-Pin
  SUB_CATEGORY_ACCOUNTS_PIN_UPI_PIN = 5;
  SUB_CATEGORY_ACCOUNTS_PIN_DEVICE_PIN = 6;
  SUB_CATEGORY_ACCOUNTS_PIN_APP_PIN = 7;

  // enums for L1-Debit_Card and L2-Activation
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_QR_CODE_NOT_WORKING = 8;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNABLE_TO_SET_PIN = 9;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_POS = 10;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_CONTACTLESS = 11;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_ATM_WITHDRAWAL = 12;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_OTP_NOT_RECEIVED = 13;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_HOW_TO_ACTIVATE = 14;

  // enums for L1-Debit_Card and L2-Delivery
  SUB_CATEGORY_DEBIT_CARD_DELIVERY_TRACKING = 15;
  SUB_CATEGORY_DEBIT_CARD_DELIVERY_DID_NOT_RECEIVED_CARD = 16;

  // enums for L1-ACCOUNTS and L2-MIN_KYC_EXPIRY
  SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND = 17;
  // Sub-Category (L3) for Pin tries exceeded use-case
  SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED = 18;

  // enums for L1-Save and L2-FIXED_DEPOSIT
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_PRE_CLOSURE = 19;
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_MATURITY = 20;
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_INTEREST_PAYOUT = 21;

  // enums for L1-Save and L2-SMART_DEPOSIT
  SUB_CATEGORY_SAVE_SMART_DEPOSIT_PRE_CLOSURE = 22;
  SUB_CATEGORY_SAVE_SMART_DEPOSIT_MATURITY = 23;
  SUB_CATEGORY_SAVE_SMART_DEPOSIT_INTEREST_PAYOUT = 24;

  // Sub-Category (L3) for mutual funds units not allotted in eta
  SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED = 25;

  // enums for L1-ONBOARDING and L2-VKYC
  SUB_CATEGORY_ONBOARDING_VKYC_VKYC_REVIEW_STATUS = 26;

  // L3 categories for account closure requests
  SUB_CATEGORY_ACCOUNTS_CLOSURE_FREEZE_ON_ACCOUNT = 27;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_IN_APP_REQUEST_RECEIVED = 28;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_MANUAL_REQUEST = 29;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_NOT_CLOSABLE_DUE_TO_PENDING_CHARGES = 30;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_REDIRECTED_TO_APP = 31;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_FULL_KYC_ACCOUNT_CLOSED = 32;

  // L3 categories for account opening issues
  SUB_CATEGORY_ACCOUNTS_OPENING_ADD_FUNDS_ON_APP = 33;
  SUB_CATEGORY_ACCOUNTS_OPENING_APP_DOWNLOAD = 34;
  SUB_CATEGORY_ACCOUNTS_OPENING_DEVICE_REGISTRATION_FAILURE = 35;
  SUB_CATEGORY_ACCOUNTS_OPENING_CARD_CREATION_PIN_SETUP_FAILURE = 36;
  SUB_CATEGORY_ACCOUNTS_OPENING_CONSENT_RELATED = 37;
  SUB_CATEGORY_ACCOUNTS_OPENING_CUSTOMER_CREATION = 38;
  SUB_CATEGORY_ACCOUNTS_OPENING_EXISTING_FEDERAL_ACCOUNT = 39;
  SUB_CATEGORY_ACCOUNTS_OPENING_KYC_RELATED = 40;
  SUB_CATEGORY_ACCOUNTS_OPENING_LIVENESS_FACEMATCH_ISSUE = 41;
  SUB_CATEGORY_ACCOUNTS_OPENING_APP_SCREENING = 42;
  SUB_CATEGORY_ACCOUNTS_OPENING_REFUND_FOR_ADD_FUNDS = 43;
  SUB_CATEGORY_ACCOUNTS_OPENING_REOPEN_CLOSED_ACCOUNT = 44;
  SUB_CATEGORY_ACCOUNTS_OPENING_STUCK_AT_EMAIL_VERIFICATION = 45;
  SUB_CATEGORY_ACCOUNTS_OPENING_VKYC_ISSUES = 46;

  // L3 categories for account upgrade/downgrade
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_FUNDS_ADDED_BUT_ACCOUNT_NOT_UPGRADED = 47;
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_TIER_DOWNGRADE = 48;
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_WITHIN_COOL_OFF_PERIOD = 49;

  // L3 categories for account KYC related issues
  SUB_CATEGORY_ACCOUNTS_KYC_MIN_KYC_EXPIRED = 50;
  SUB_CATEGORY_ACCOUNTS_KYC_UNABLE_TO_SUBMIT_FORM = 51;
  SUB_CATEGORY_ACCOUNTS_KYC_NOT_UPDATED = 52;
  SUB_CATEGORY_ACCOUNTS_KYC_UPDATED_BUT_ACCOUNT_NOT_ACTIVATED = 53;
  SUB_CATEGORY_ACCOUNTS_KYC_SIGNATURE_NOT_UPDATED = 54;

  // L3 categories for chequebook related issues
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_CHARGES_WAIVER = 55;
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_NOT_RECEIVED = 56;
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_UNABLE_TO_UPLOAD_SIGNATURE = 57;

  // L3 categories for account fees and charges
  SUB_CATEGORY_ACCOUNTS_FEES_CHARGES_AMB = 58;

  // L3 categories for account information
  SUB_CATEGORY_ACCOUNTS_INFO_ACCOUNT_INFO = 59;
  SUB_CATEGORY_ACCOUNTS_INFO_ACCOUNT_TIER_DETAILS_BENEFITS = 60;
  SUB_CATEGORY_ACCOUNTS_INFO_APP_SETTINGS = 61;
  SUB_CATEGORY_ACCOUNTS_INFO_NR_ACCOUNT = 62;
  SUB_CATEGORY_ACCOUNTS_INFO_RE_KYC_ISSUES = 63;
  SUB_CATEGORY_ACCOUNTS_INFO_INFORMATION_REGARDING_LIEN = 64;

  // L3 categories for app login issues
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_ISSUES = 65;
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_DEVICE_PASSWORD_NOT_ACCEPTED = 66;
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_RE_LOGIN_BEFORE_SIGNUP = 67;

  // L3 categories for app-related issues
  SUB_CATEGORY_APP_RELATED_GENERAL_QUERIES = 68;
  SUB_CATEGORY_APP_RELATED_INSURANCE_RELATED = 69;
  SUB_CATEGORY_APP_RELATED_REGISTRATION = 70;
  SUB_CATEGORY_APP_RELATED_UPGRADE_DOWNGRADE_ISSUE = 71;
  SUB_CATEGORY_APP_RELATED_APP_CRASH = 72;
  SUB_CATEGORY_APP_RELATED_FEATURE_NOT_LOADING = 73;
  SUB_CATEGORY_APP_RELATED_USER_FEEDBACK = 74;
  SUB_CATEGORY_APP_RELATED_FULFILLMENT_RELATED = 75;
  SUB_CATEGORY_APP_RELATED_REWARDS_RELATED = 76;
  SUB_CATEGORY_APP_RELATED_REDIRECTED_TO_BANK = 77;

  // L3 categories for card-related issues
  SUB_CATEGORY_CARD_REQUEST_CARD_DAMAGED = 78;
  SUB_CATEGORY_CARD_REQUEST_CARD_NOT_REQUIRED = 79;
  SUB_CATEGORY_CARD_REQUEST_LOST_STOLEN = 80;
  SUB_CATEGORY_CARD_REQUEST_DIGITAL_CARD = 81;

  // L3 categories for card settings
  SUB_CATEGORY_CARD_SETTINGS_ACTIVATE_CARD = 82;
  SUB_CATEGORY_CARD_SETTINGS_CHANGE_USAGE_SETTINGS = 83;
  SUB_CATEGORY_CARD_SETTINGS_PIN_FAILING = 84;
  SUB_CATEGORY_CARD_SETTINGS_TEMPORARY_FREEZE = 85;
  SUB_CATEGORY_CARD_SETTINGS_UNABLE_TO_CHANGE_USAGE_SETTINGS = 86;

  // L3 categories for card delivery
  SUB_CATEGORY_CARD_DELIVERY_RTO_REDISPATCH = 87;
  SUB_CATEGORY_CARD_DELIVERY_RTO_REFUND = 88;

  // L3 categories for card charges
  SUB_CATEGORY_CARD_CHARGES_AMC = 89;
  SUB_CATEGORY_CARD_CHARGES_ECOM_POS_DECLINE_FEES = 90;
  SUB_CATEGORY_CARD_CHARGES_OTHER_CHARGES = 91;

  // L3 categories for card info
  SUB_CATEGORY_CARD_INFO_CARD_DETAILS = 92;
  SUB_CATEGORY_CARD_INFO_DELIVERY_RELATED = 93;
  SUB_CATEGORY_CARD_INFO_MIN_KYC_USER = 94;

  // L3 categories for ATM transactions
  SUB_CATEGORY_ATM_TRANSACTIONS_CDM_CASH_DEPOSIT_NOT_CREDITED = 95;
  SUB_CATEGORY_ATM_TRANSACTIONS_DEBITED_BUT_NOT_DISPENSED = 96;
  SUB_CATEGORY_ATM_TRANSACTIONS_DEBITED_BUT_PARTIALLY_DISPENSED = 97;
  SUB_CATEGORY_ATM_TRANSACTIONS_UNABLE_TO_WITHDRAW_DOMESTIC = 98;
  SUB_CATEGORY_ATM_TRANSACTIONS_UNABLE_TO_WITHDRAW_INTERNATIONAL = 99;

  // L3 categories for transaction issues
  SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_DEBITED_BUT_NOT_CREDITED = 100;
  SUB_CATEGORY_TRANSACTION_ISSUES_TXN_FAILED_DOMESTIC = 101;
  SUB_CATEGORY_TRANSACTION_ISSUES_TXN_FAILED_INTERNATIONAL = 102;
  SUB_CATEGORY_TRANSACTION_ISSUES_ORDER_FAILED_REFUND_NOT_RECEIVED = 103;
  SUB_CATEGORY_TRANSACTION_ISSUES_ORDER_PENDING = 104;

  // L3 categories for fixed deposit and smart deposit
  SUB_CATEGORY_FD_SD_UNABLE_TO_CREATE = 105;
  SUB_CATEGORY_FD_SD_UNABLE_TO_MODIFY = 106;
  SUB_CATEGORY_FD_SD_UNABLE_TO_PAUSE = 107;
  SUB_CATEGORY_FD_SD_CANCEL_AUTO_RENEWAL = 108;
  SUB_CATEGORY_FD_SD_FD_CLOSED_BUT_AMOUNT_NOT_RECEIVED = 109;
  SUB_CATEGORY_FD_SD_INCORRECT_MATURITY_AMOUNT = 110;
  SUB_CATEGORY_FD_SD_UNABLE_TO_CLOSE_FD = 111;
  SUB_CATEGORY_FD_SD_SD_CLOSED_BUT_AMOUNT_NOT_RECEIVED = 112;
  SUB_CATEGORY_FD_SD_UNABLE_TO_CLOSE_SD = 113;

  // L3 categories for FIT rules
  SUB_CATEGORY_FIT_RULES_FIT_RULE_NOT_EXECUTED = 114;
  SUB_CATEGORY_FIT_RULES_INCORRECT_AMOUNT_DEPOSITED = 115;
  SUB_CATEGORY_FIT_RULES_FIT_RULE_INFORMATION = 116;

  // L3 categories for Jump
  SUB_CATEGORY_JUMP_PORTFOLIO_MISMATCH = 117;
  SUB_CATEGORY_JUMP_WITHDRAWAL_ISSUES = 118;

  // L3 categories for Mutual Funds
  SUB_CATEGORY_MUTUAL_FUNDS_SIP_NOT_DEDUCTED = 119;
  SUB_CATEGORY_MUTUAL_FUNDS_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED = 120;
  SUB_CATEGORY_MUTUAL_FUNDS_STUCK_IN_SCREENING = 121;
  SUB_CATEGORY_MUTUAL_FUNDS_WITHDRAWAL_FROM_OTHER_PLATFORM = 122;
  SUB_CATEGORY_MUTUAL_FUNDS_INCORRECT_AMOUNT_CREDITED = 123;
  SUB_CATEGORY_MUTUAL_FUNDS_PAUSE_AUTO_INVESTMENT = 124;

  // L3 categories for US Stocks
  SUB_CATEGORY_US_STOCKS_AMOUNT_DEBITED_NOT_CREDITED_TO_WALLET = 125;

  // L3 categories for Fi Store
  SUB_CATEGORY_FI_STORE_DIRECT_TO_HOME = 126;
  SUB_CATEGORY_FI_STORE_PHYSICAL_MERCHANDISE = 127;

  // L3 categories for Salary Programs
  SUB_CATEGORY_SALARY_PROGRAMS_INSTANT_SALARY = 128;
  SUB_CATEGORY_SALARY_PROGRAMS_SALARY_LITE = 129;
  SUB_CATEGORY_SALARY_PROGRAMS_INFORMATION_REGARDING_CHARGES = 130;

  // L3 categories for Communication
  SUB_CATEGORY_COMMUNICATION_SPAM = 131;
  SUB_CATEGORY_COMMUNICATION_CALLBACK = 132;
  SUB_CATEGORY_COMMUNICATION_REQUEST_FOR_MORE_INFO = 133;

  // L3 categories for Loans
  SUB_CATEGORY_LOANS_APPLICATION_FAILED = 134;
  SUB_CATEGORY_LOANS_DISBURSAL_PENDING = 135;
  SUB_CATEGORY_LOANS_ISSUE_WITH_LOAN_APPLICATION = 136;
  SUB_CATEGORY_LOANS_LOAN_DISBURSED_BUT_ACCOUNT_NOT_CREATED = 137;
  SUB_CATEGORY_LOANS_CONSENT_WITHDRAWAL_FOR_CIBIL_ENQUIRY = 138;
  SUB_CATEGORY_LOANS_REQUEST_FOR_BUREAU_CORRECTION = 139;
  SUB_CATEGORY_LOANS_BORROWERS_DEMISE = 140;
  SUB_CATEGORY_LOANS_HARASSMENT_COMPLAINT = 141;
  SUB_CATEGORY_LOANS_PAYMENT_LINK_TO_BE_SENT = 142;
  SUB_CATEGORY_LOANS_REQUEST_FOR_SETTLEMENT = 143;
  SUB_CATEGORY_LOANS_REQUESTING_EMI_EXTENSION = 144;
  SUB_CATEGORY_LOANS_REPAYMENT_SCHEDULE = 145;
  SUB_CATEGORY_LOANS_EMI_NOT_DEDUCTED = 146;
  SUB_CATEGORY_LOANS_NACH_RE_REGISTRATION = 147;
  SUB_CATEGORY_LOANS_PAYMENT_STATUS_NOT_UPDATED = 148;
  SUB_CATEGORY_LOANS_LOAN_DETAILS_AND_STATUS = 149;
  SUB_CATEGORY_LOANS_LOAN_PRE_CLOSURE = 150;
  SUB_CATEGORY_LOANS_LOAN_REPAYMENT = 151;
  SUB_CATEGORY_LOANS_PAY_MARGIN_AMOUNT = 152;
  SUB_CATEGORY_LOANS_PLEDGE_MORE_FUNDS = 153;
  SUB_CATEGORY_LOANS_PLEDGED_MUTUAL_FUNDS_SOLD = 154;
  SUB_CATEGORY_LOANS_DELAY_IN_CLOSURE = 155;
  SUB_CATEGORY_LOANS_PAID_BUT_MF_NOT_UNPLEDGED = 156;
  SUB_CATEGORY_LOANS_PRE_CLOSURE = 157;
  SUB_CATEGORY_LOANS_PRE_DISBURSEMENT = 158;
  SUB_CATEGORY_LOANS_SALES = 159;
  SUB_CATEGORY_LOANS_SERVICE = 160;
  SUB_CATEGORY_LOANS_EMI_PAID_BUT_ECS_NACH_RETURN_CHARGED = 161;
  SUB_CATEGORY_LOANS_LATE_PAYMENT_FEES = 162;
  SUB_CATEGORY_LOANS_UNABLE_TO_PAY_VIA_APP = 163;
  SUB_CATEGORY_LOANS_UNABLE_TO_PAY_VIA_COLLECTIONS_LINK = 164;

  // L3 categories for Assets
  SUB_CATEGORY_ASSETS_ASSETS_VIA_MANUAL_FORMS = 165;
  SUB_CATEGORY_ASSETS_EPFO = 166;
  SUB_CATEGORY_ASSETS_INDIAN_STOCKS = 167;
  SUB_CATEGORY_ASSETS_LOANS = 168;
  SUB_CATEGORY_ASSETS_NPS = 169;
  SUB_CATEGORY_ASSETS_OTHER_BANK_ACCOUNTS = 170;
  SUB_CATEGORY_ASSETS_ABOUT_NEGATIVE_BALANCE = 171;
  SUB_CATEGORY_ASSETS_ABOUT_NETWORTH = 172;
  SUB_CATEGORY_ASSETS_CONNECTED_ACCOUNTS = 173;
  SUB_CATEGORY_ASSETS_UNABLE_TO_ADD_ASSETS_LIABILITIES = 174;

  // L3 categories for Rewards
  SUB_CATEGORY_REWARDS_CONVERT_TO_CASH = 175;
  SUB_CATEGORY_REWARDS_PLAY_AND_WIN = 176;
  SUB_CATEGORY_REWARDS_POWER_UP = 177;
  SUB_CATEGORY_REWARDS_TRAVEL_MILES = 178;
  SUB_CATEGORY_REWARDS_HOW_TO_GET_REWARDS = 179;
  SUB_CATEGORY_REWARDS_HOW_TO_REFER = 180;
  SUB_CATEGORY_REWARDS_REWARDS_STATEMENT = 181;
  SUB_CATEGORY_REWARDS_AMOUNT_NOT_REFUNDED = 182;
  SUB_CATEGORY_REWARDS_EXPIRED_VOUCHER_RECEIVED = 183;
  SUB_CATEGORY_REWARDS_FI_POINTS_NOT_REFUNDED = 184;
  SUB_CATEGORY_REWARDS_CAMPAIGN_SPECIFIC = 185;
  SUB_CATEGORY_REWARDS_DEBIT_CARD_OFFERS = 186;
  SUB_CATEGORY_REWARDS_REFERRAL = 187;
  SUB_CATEGORY_REWARDS_TIERING_REWARDS = 188;

  // L3 categories for KYC
  SUB_CATEGORY_KYC_NON_KYC_RELATED = 189;
  SUB_CATEGORY_KYC_VKYC_RELATED = 190;

  // L3 categories for Account Security
  SUB_CATEGORY_ACCOUNT_SECURITY_REQUEST_TO_UNFREEZE = 191;
  SUB_CATEGORY_ACCOUNT_SECURITY_FREEZE_RELATED = 192;
  SUB_CATEGORY_ACCOUNT_SECURITY_ADDITIONAL_INFORMATION = 193;
  SUB_CATEGORY_ACCOUNT_SECURITY_NOC_RELATED = 194;

  // L3 categories for Language Support
  SUB_CATEGORY_LANGUAGE_ASSAMESE = 195;
  SUB_CATEGORY_LANGUAGE_BENGALI = 196;
  SUB_CATEGORY_LANGUAGE_HINDI = 197;
  SUB_CATEGORY_LANGUAGE_KANNADA = 198;
  SUB_CATEGORY_LANGUAGE_MALAYALAM = 199;
  SUB_CATEGORY_LANGUAGE_ORIYA = 200;
  SUB_CATEGORY_LANGUAGE_TAMIL = 201;
  SUB_CATEGORY_LANGUAGE_TELUGU = 202;

  // L3 categories for Data and Statements
  SUB_CATEGORY_DATA_STATEMENTS_REQUEST_DATA_DELETION = 203;
  SUB_CATEGORY_DATA_STATEMENTS_BANK_STATEMENT = 204;
  SUB_CATEGORY_DATA_STATEMENTS_MUTUAL_FUNDS_STATEMENT = 205;
  SUB_CATEGORY_DATA_STATEMENTS_SIGNED_BANK_STATEMENT = 206;
  SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_JUMP = 207;
  SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_MUTUAL_FUNDS = 208;
  SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_US_STOCKS = 209;
  SUB_CATEGORY_DATA_STATEMENTS_US_STOCKS_STATEMENT = 210;

  // L3 categories for Mandates
  SUB_CATEGORY_MANDATES_ACTIVE_MANDATES_DETAILS = 211;
  SUB_CATEGORY_MANDATES_CANCEL_SI_NACH_MANDATES = 212;

  // L3 categories for Profile Updates
  SUB_CATEGORY_PROFILE_UPDATES_CHANGE_EMPLOYMENT_DETAILS = 213;
  SUB_CATEGORY_PROFILE_UPDATES_CONTACT_DETAILS_UPDATE = 214;
  SUB_CATEGORY_PROFILE_UPDATES_DOB_CHANGE = 215;
  SUB_CATEGORY_PROFILE_UPDATES_NAME_CHANGE_FATHER_MOTHER = 216;
  SUB_CATEGORY_PROFILE_UPDATES_NAME_CHANGE_USER = 217;

  // L3 categories for Device Issues
  SUB_CATEGORY_DEVICE_ISSUES_CARDS = 218;
  SUB_CATEGORY_DEVICE_ISSUES_DEVICE_LOST = 219;
  SUB_CATEGORY_DEVICE_ISSUES_PROMOTIONAL_COMMS = 220;

  // L3 categories for Transaction Issues
  SUB_CATEGORY_TRANSACTION_TYPES_GOODS_SERVICES_NOT_DELIVERED = 221;
  SUB_CATEGORY_TRANSACTION_TYPES_INCORRECT_AMOUNT = 222;
  SUB_CATEGORY_TRANSACTION_TYPES_NOT_VISIBLE_ON_APP = 223;
  SUB_CATEGORY_TRANSACTION_TYPES_IN_APP_UPI_P2M = 224;
  SUB_CATEGORY_TRANSACTION_TYPES_IN_APP_UPI_P2P = 225;
  SUB_CATEGORY_TRANSACTION_TYPES_OFF_APP_UPI_P2M = 226;
  SUB_CATEGORY_TRANSACTION_TYPES_OFF_APP_UPI_P2P = 227;
  SUB_CATEGORY_TRANSACTION_TYPES_INTRA_BANK = 228;
  SUB_CATEGORY_TRANSACTION_TYPES_NACH_ECS_CHARGES = 229;
  SUB_CATEGORY_TRANSACTION_TYPES_RECURRING_PAYMENT_CANCELLED_BUT_AMOUNT_DEBITED = 230;
  SUB_CATEGORY_TRANSACTION_TYPES_CHEQUE_DEPOSIT = 231;
  SUB_CATEGORY_TRANSACTION_TYPES_INTERNATIONAL_REMITTANCE = 232;
  SUB_CATEGORY_TRANSACTION_TYPES_MERCHANT_REFUND = 233;
  SUB_CATEGORY_TRANSACTION_TYPES_OTHER_DOMESTIC_TRANSACTIONS = 234;
  SUB_CATEGORY_TRANSACTION_TYPES_DEPOSITING_CASH = 235;
  SUB_CATEGORY_TRANSACTION_TYPES_IPO = 236;
  SUB_CATEGORY_TRANSACTION_TYPES_TRANSACTION_RELATED_ENQUIRY = 237;

  // L3 categories for UPI Issues
  SUB_CATEGORY_UPI_ISSUES_UNABLE_TO_LINK_FEDERAL_ACCOUNT_TO_OTHER_APPS = 238;
  SUB_CATEGORY_UPI_ISSUES_BANK_TRANSFER = 239;
  SUB_CATEGORY_UPI_ISSUES_INTERNATIONAL_TRANSACTIONS = 240;
  SUB_CATEGORY_UPI_ISSUES_LIMIT_EXCEEDED = 241;
  SUB_CATEGORY_UPI_ISSUES_PIN_TRIES_EXCEEDED = 242;
  SUB_CATEGORY_UPI_ISSUES_UPI_ISSUE = 243;

  // L3 categories for International Transactions
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_INTERNATIONAL = 244;
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_STOP_CHEQUE_PAYMENT = 245;
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_FEES_AND_CHARGES = 246;

  // L3 categories for Certificates
  SUB_CATEGORY_CERTIFICATES_BALANCE_CERTIFICATE = 247;
  SUB_CATEGORY_CERTIFICATES_INTEREST_CERTIFICATE = 248;

  // L3 categories for Eligibility Issues
  SUB_CATEGORY_ELIGIBILITY_NOT_ELIGIBLE = 249;
  SUB_CATEGORY_ELIGIBILITY_OTP_NOT_RECEIVED = 250;

  // L3 categories for Profile Changes
  SUB_CATEGORY_PROFILE_CHANGES_ADDRESS_CHANGE = 251;
  SUB_CATEGORY_PROFILE_CHANGES_UNAUTHORISED_TRANSACTION = 252;

  // L3 categories for Card Usage Issues
  SUB_CATEGORY_CARD_USAGE_CARD_NOT_ACCEPTED = 253;
  SUB_CATEGORY_CARD_USAGE_CONTACTLESS_NOT_WORKING = 254;
  SUB_CATEGORY_CARD_USAGE_ATM_DECLINE_FEES = 255;
  SUB_CATEGORY_CARD_USAGE_FUEL_CHARGES = 256;
  SUB_CATEGORY_CARD_USAGE_TCS_DEDUCTIONS = 257;

  // L3 categories for Balance Issues
  SUB_CATEGORY_BALANCE_ISSUES_BALANCE_NOT_UPDATED = 258;
  SUB_CATEGORY_BALANCE_ISSUES_DOUBLE_DEBIT = 259;
  SUB_CATEGORY_BALANCE_ISSUES_INCORRECT_AMOUNT_DEBITED = 260;
  SUB_CATEGORY_BALANCE_ISSUES_EXCESS_AMOUNT_PAID = 261;

  // L3 categories for App Access Issues
  SUB_CATEGORY_APP_ACCESS_NO_APP_ACCESS = 262;
  SUB_CATEGORY_APP_ACCESS_UNABLE_TO_INVEST = 263;
  SUB_CATEGORY_APP_ACCESS_UNABLE_TO_WITHDRAW = 264;
  SUB_CATEGORY_APP_ACCESS_UNABLE_TO_ADD_FUNDS = 265;

  // L3 categories for Payment Issues
  SUB_CATEGORY_PAYMENT_ISSUES_SENT_TO_WRONG_USER = 266;
  SUB_CATEGORY_PAYMENT_ISSUES_CASH_DEPOSIT_AT_BRANCH = 267;

  // L3 categories for Fixed/Smart Deposit Issues
  SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_CREATE_FD = 268;
  SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_CREATE_SD = 269;
  SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_REDEEM = 270;

  // L3 categories for Account Status Issues
  SUB_CATEGORY_ACCOUNT_STATUS_ACCOUNT_FROZEN_CLOSED = 271;
  SUB_CATEGORY_ACCOUNT_STATUS_EMAIL_ADDRESS = 272;
  SUB_CATEGORY_ACCOUNT_STATUS_PHONE_NUMBER = 273;
  SUB_CATEGORY_ACCOUNT_STATUS_BOUNCE_CHARGE = 274;

  // L3 categories for Reward Issues
  SUB_CATEGORY_REWARD_ISSUES_VOUCHER_NOT_RECEIVED = 275;
  SUB_CATEGORY_REWARD_ISSUES_FOREX_RATE_ISSUE = 276;

  // L3 categories for Stock Trading Issues
  SUB_CATEGORY_STOCK_TRADING_UNABLE_TO_BUY = 277;
  SUB_CATEGORY_STOCK_TRADING_MONEY_NOT_CREDITED = 278;
  SUB_CATEGORY_STOCK_TRADING_UNABLE_TO_SELL = 279;
}
