syntax = "proto3";

package api.typesv2.home;

option go_package = "github.com/epifi/gamma/api/typesv2/home";
option java_package = "com.github.epifi.gamma.api.typesv2.home";

enum IconType {
  ICON_TYPE_UNSPECIFIED = 0;

  // Nav bar icons
  HOME = 1;
  PAY = 2;
  INVEST = 3;
  BORROW = 4;
  DISCOVER = 5;

  // Dashboard Icons
  DASHBOARD_SETTING = 6;
  DASHBOARD_ACTION = 7;

  // Appbar icon for notifications
  NOTIFICATION = 8;
  // Appbar icon for Analyser
  ANALYSER = 9;
  // Appbar icon for rewards
  REWARDS = 10;
  // Appbar icon for profile
  PROFILE = 11;
  // App bar icon for Segmented card entry for both DC & CC
  CARD = 14;
  // App bar icon for Credit card (standalone)
  CREDIT_CARD = 58;
  // App bar icon for Debit card (standalone)
  DEBIT_CARD = 59;
  // Appbar icon for referral
  REFER = 16;

  // Sticky Icon typesv2
  // Sticky Icon on home page - QR code icon
  QR_CODE = 12;
  // Sticky icon on primary savings summary page
  SAVINGS_SUMMARY = 13;
  // Connect bank account(for connected accounts use case)
  CONNECT_ACCOUNT = 15;

  // shortcut icon typesv2
  SHORTCUT_SALARY_PROGRAM = 17;
  SHORTCUT_SALARY_BASIC_PLAN = 87;
  SHORTCUT_JUMP = 18;
  SHORTCUT_PRE_APPROVED_LOAN = 19;
  SHORTCUT_PRE_PAY_LOAN = 73;
  SHORTCUT_MF = 20;
  SHORTCUT_FD = 21;
  SHORTCUT_SD = 22;
  SHORTCUT_FITTT = 23;
  SHORTCUT_PAY_TO_CONTACT = 24;
  SHORTCUT_PAY_TO_UPI = 25;
  SHORTCUT_PAY_TO_PHONE = 26;
  SHORTCUT_BANK_TRANSFER = 27;
  SHORTCUT_DEBIT_CARD = 28;
  SHORTCUT_CREDIT_CARD = 29;
  SHORTCUT_CARD_OFFERS = 30;
  SHORTCUT_WAYS_TO_EARN = 31;
  SHORTCUT_OFFERS_CATALOG = 32;
  SHORTCUT_OFFERS_ORDERS = 33;
  SHORTCUT_REFERRALS = 34;
  SHORTCUT_SPEND_ANALYSER = 35;
  SHORTCUT_CREDIT_SCORE_ANALYSER = 36;
  SHORTCUT_MF_ANALYSER = 37;
  SHORTCUT_FI_MINUTES = 38;
  SHORTCUT_KYC_INFO = 39;
  SHORTCUT_AUTO_INVEST = 40;
  SHORTCUT_AUTO_PAY = 41;
  SHORTCUT_AUTO_SAVE = 42;
  SHORTCUT_MANAGE_RULES = 43;
  SHORTCUT_SET_REMINDER = 44;
  SHORTCUT_EARLY_SALARY = 45;
  SHORTCUT_CONNECTED_ACCOUNT = 46;
  SHORTCUT_ADD_MONEY = 47;
  SHORTCUT_SCAN_AND_PAY = 48;
  SHORTCUT_PLUS_PLAN = 49;
  SHORTCUT_INFINITE_PLAN = 50;
  SHORTCUT_NETWORTH = 51;
  SHORTCUT_CHEQUEBOOK_REQUEST = 52;
  SHORTCUT_INTEREST_CERTIFICATE = 53;
  SHORTCUT_ALL_REQUESTS = 54;
  SHORTCUT_SECURED_LOANS = 55;
  SHORTCUT_US_STOCKS = 56;
  // Shortcuts for fi store icons
  SHORTCUT_FI_STORE = 57;
  SHORTCUT_FI_STORE_GIFT_CARDS = 61;
  SHORTCUT_FI_STORE_MILES_EXCHANGE = 72;

  SHORTCUT_DEBIT_CARD_CONTROLS = 60;
  SHORTCUT_EXPLORE_FI = 62;
  SHORTCUT_BANK_STATEMENT = 63;
  // Savings account benefits tab which will get displayed to fi lite users
  SAVINGS_ACCOUNT_BENEFITS = 64;
  SHORTCUT_CREDIT_CARD_CONTROLS = 65;
  SHORTCUT_CREDIT_CARD_OFFERS = 66;
  SHORTCUT_DEBIT_CARD_OFFERS = 67;
  SHORTCUT_SAVINGS_ACCOUNT = 68;
  SHORTCUT_ZERO_STATE_REWARD = 69;
  SHORTCUT_AA_SALARY_PLAN = 70;
  // Networth icon in home bottom nav bar
  BOTTOM_NAV_BAR_NETWORTH = 71;
  SHORTCUT_TOP_SPEND_MERCHANTS_ANALYSER = 74;
  SHORTCUT_SPENDS_BY_TIME_ANALYSER = 75;
  SHORTCUT_SELF_TRANSFER = 76;
  // Travel section icons
  SHORTCUT_TRAVEL_BUDGETING = 77;
  SHORTCUT_TRAVEL_MODE_SWITCH = 78;
  SHORTCUT_ATM_LOCATOR = 79;
  SHORTCUT_MODIFY_DC_INTERNATIONAL_TRANSACTION_LIMIT = 80;
  SHORTCUT_CURRENCY_EXCHANGE_CALCULATOR = 81;
  // wealth builder landing page icon in home bottom nav bar
  BOTTOM_NAV_WEALTH_BUILDER = 82;

  SHORTCUT_ORDER_PHYSICAL_DEBIT_CARD = 83;
  SHORTCUT_DEBIT_CARD_SETTINGS = 84;
  // wealth builder landing page icon in home top nav bar
  TOP_NAV_WEALTH_BUILDER = 85;
  // US Stocks icon in home bottom nav bar
  NAV_BAR_US_STOCKS = 86;
  // WEALTH BUILDER SHORCUT FOR Explore
  // deprecated: use SHORTCUT_NETWORTH
  SHORTCUT_WEALTH_BUILDER = 88 [deprecated = true];
  // Redirects to AMB details screen
  SHORTCUT_AMB = 89;
  // Redirects user to fi calculator web page
  SHORTCUT_CALCULATOR = 90;
  // Redirects base tier users to suitable plan based on the balance (pitching)
  SHORTCUT_UPGRADE_PLANS = 91;
}

enum ExploreSectionType {
  EXPLORE_SECTION_TYPE_UNSPECIFIED = 0;
  EXPLORE_SECTION_TYPE_MUST_TRY = 1;
  EXPLORE_SECTION_TYPE_CARDS = 2;
  EXPLORE_SECTION_TYPE_LOANS = 3;
  EXPLORE_SECTION_TYPE_ACCOUNT_PLANS = 4;
  EXPLORE_SECTION_TYPE_YOUR_FINANCES = 5;
  EXPLORE_SECTION_TYPE_INVEST_MONEY = 6;
  EXPLORE_SECTION_TYPE_AUTOMATE_FINANCES = 7;
  EXPLORE_SECTION_TYPE_REWARDS_AND_REFERRALS = 8;
  EXPLORE_SECTION_TYPE_DOCUMENT_REQUESTS = 9;
  EXPLORE_SECTION_TYPE_PAYMENTS = 10;
  EXPLORE_SECTION_TYPE_CREDIT_CARDS = 11;
  EXPLORE_SECTION_TYPE_TRAVEL = 12;
  EXPLORE_SECTION_TYPE_INSIGHTS = 13;
}
