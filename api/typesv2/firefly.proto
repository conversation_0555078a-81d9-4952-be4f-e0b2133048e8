syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";


// Card program will be a string of ARN format having the following attributes to begin with:
// VENDOR:SOURCE:TYPE:COLLATERAL(If any)
// Example: FEDERAL:PREAPPROVED:UNSECURED which means the card issued is a
// federal credit card to a preapproved user with a credit limit and no collateral.
// FEDERAL:REALTIME_BRE:SECURED:FD means the card issued is a federal
// credit card to a user who was deemed eligible through a real time bre check for
// a secured card against a fixed deposit as the collateral.

// enums associated with card program
enum CardProgramSource {
  CARD_PROGRAM_SOURCE_UNSPECIFIED = 0;
  CARD_PROGRAM_SOURCE_PREAPPROVED = 1;
  CARD_PROGRAM_SOURCE_REALTIME_BRE = 2;
  CARD_PROGRAM_SOURCE_FI_BRE_APPROVED = 3;
}

enum CardProgramType {
  CARD_PROGRAM_TYPE_UNSPECIFIED = 0;
  CARD_PROGRAM_TYPE_SECURED = 1;
  CARD_PROGRAM_TYPE_UNSECURED = 2;
  CARD_PROGRAM_TYPE_MASS_UNSECURED = 3;
}

enum DepositStatus {
  DEPOSIT_STATUS_UNSPECIFIED = 0;
  DEPOSIT_STATUS_SUCCESSFUL = 1;
  DEPOSIT_STATUS_FAILURE = 2;
}

enum CardProgramCollateral {
  CARD_PROGRAM_COLLATERAL_UNSPECIFIED = 0;
  CARD_PROGRAM_COLLATERAL_FD = 1;
}

enum CardProgramVendor {
  CARD_PROGRAM_VENDOR_UNSPECIFIED = 0;
  CARD_PROGRAM_VENDOR_FEDERAL = 1;
}

enum CardProgramOrigin {
  CARD_PROGRAM_ORIGIN_UNSPECIFIED = 0;
  CARD_PROGRAM_ORIGIN_FI = 1;
  CARD_PROGRAM_ORIGIN_FI_LITE = 2;
}

message CardProgram {
  CardProgramVendor card_program_vendor = 1;
  CardProgramSource card_program_source = 2;
  CardProgramType card_program_type = 3;
  CardProgramCollateral card_program_collateral = 4;
  CardProgramOrigin card_program_origin = 5;
}

// Holds the nominee information for a deposit account along with its percentage
message DepositNomineeDetails {
  repeated DepositNomineeInfo nominee_info_list = 1;

  message DepositNomineeInfo {
    string nominee_id = 1;

    // percentage share of deposit allocated to this nominee
    string percentage_share = 2;
  }
}

enum CollateralLienStatus {
  COLLATERAL_LIEN_STATUS_UNSPECIFIED = 0;
  // if a credit line is active against a collateral, the lien status is marked.
  COLLATERAL_LIEN_STATUS_MARKED = 1;
  // if a credit line is closed against a collateral, the lien status is unmarked.
  COLLATERAL_LIEN_STATUS_UNMARKED = 2;
}

enum VendorTenantType {
  VENDOR_TENANT_TYPE_UNSPECIFIED = 0;
  VENDOR_TENANT_TYPE_SECURED = 1;
  VENDOR_TENANT_TYPE_UNSECURED = 2;
  VENDOR_TENANT_TYPE_MASS_UNSECURED = 3;
}

// CreditCardRequestHeader is a generic header which will flow in all credit card related flow and will hold additional details related to user's cc journey such as campaign details etc
message CreditCardRequestHeader {
  // card program arn string
  string card_program = 1;
  // attributes needed in various cc flows
  map<string, string> attributes = 2;
}

// Describes the type of auth token being returned for Credit Card Sdk. This helps clients
// invoke the appropriate root route for either Onboarding or CMS sdk
enum CreditCardSdkTokenType {
  TOKEN_TYPE_UNSPECIFIED = 0;
  // Denotes that the aut_token returned is for Onboarding flow
  TOKEN_TYPE_ONBOARDING = 1;
  // Denotes that the auth token returned is for CMS flow
  TOKEN_TYPE_CMS = 2;
}
