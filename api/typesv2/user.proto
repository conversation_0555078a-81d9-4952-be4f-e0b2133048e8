//go:generate gen_sql -types=Language
syntax = "proto3";

package api.typesv2;

import "api/typesv2/common/device.proto";
import "api/typesv2/device_properties.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// Property of a user created with a vendor bank.
// It controls various legal/regulation requirements
// from onboarding to payments to deposits.
enum KYCLevel {
  KYC_LEVEL_UNSPECIFIED = 0;

  // A user with limited access to the banking features.
  // e.g. limit on amount that can be added to the account is 1L for such users.
  MIN_KYC = 1;

  // A user with full access to the banking features.
  FULL_KYC = 2;
}

// Purpose of opening savings account
enum PurposeOfSavingsAccount {
  PURPOSE_OF_SAVINGS_ACCOUNT_UNSPECIFIED = 0;
  PURPOSE_OF_SAVINGS_ACCOUNT_SAVINGS = 1;
  PURPOSE_OF_SAVINGS_ACCOUNT_LOAN = 2;
  PURPOSE_OF_SAVINGS_ACCOUNT_INVESTMENT = 3;
}

// PaymentParty enum is to define type of party involve in a payment.
enum PaymentParty {
  PAYMENT_PARTY_UNSPECIFIED = 0;
  // a person who pays
  PAYER = 1;
  // a person who get paid
  PAYEE = 2;
}

// enum for defining device language in UserDeviceProperty message, can be used in other places as well in the future
enum Language {
  LANGUAGE_UNSPECIFIED = 0;
  LANGUAGE_ENGLISH = 1;
  LANGUAGE_HINDI = 2;
  LANGUAGE_TELUGU = 3;
  LANGUAGE_TAMIL = 4;
  LANGUAGE_KANNADA = 5;
  LANGUAGE_MALAYALAM = 6;
  LANGUAGE_BENGALI = 7;
  LANGUAGE_MARATHI = 8;
  LANGUAGE_PUNJABI = 9;
  LANGUAGE_ASSAMESE = 10;
  LANGUAGE_GUJARATI = 11;
  LANGUAGE_ODIA = 12;
  LANGUAGE_URDU = 13;
  LANGUAGE_NEPALI = 14;
}

// Language info message for device language etc.
message LanguageInfo {
  typesv2.Language language = 1;
  // the raw language string; can be used when we can't derive enum type from string.
  string raw = 2;
}


// this stores the value of property that we are storing in user_device_property table, messages for storing
// other properties like location token, device model etc. can be added later in oneof value.
message PropertyValue {
  oneof prop_value {
    // value for DEVICE_PROP_DEVICE_LANGUAGE
    typesv2.LanguageInfo device_language = 1;
    // value for DEVICE_PROP_DEVICE_LOCATION_TOKEN
    string location_token = 2;
    // value for DEVICE_PROP_MALICIOUS_APP_INFO
    UserDeviceMaliciousAppsInfo malicious_app_info = 3;
    // value for DEVICE_PROP_DEVICE_MODEL_INFO
    common.DeviceModelInfo device_model_info = 4;
    // value for DEVICE_PROP_APP_VERSION_INFO
    typesv2.AppVersionInfo app_version_info = 5;
    // value for DEVICE_PROP_GOOGLE_ADVERTISING_ID
    string google_advertising_id = 6;
    // value for DEVICE_PROP_ANDROID_ID
    string android_id = 7;
    // value for DEVICE_PROP_IP_ADDRESS_TOKEN
    string ip_address_token = 8;
    // value for DEVICE_PROP_ALL_APPS_INFO
    UserDeviceAppsInfo all_apps_info = 9;
    // value for DEVICE_PROP_APP_INSTALL_ID
    string app_install_id = 10;
    // value for DEVICE_PROP_MEMORY_INFO
    DeviceMemoryInfo device_memory_info = 12;
    // value for DEVICE_PROP_DEVICE_ID
    string device_id = 13;
    // value for DEVICE_PROP_APP_NAME
    common.AppName app_name = 14;
  }
}

// enum used for defining the type of property to be stored in UserDeviceProperty message
enum DeviceProperty {
  DEVICE_PROP_UNSPECIFIED = 0;
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_LANGUAGE).GetLanguage()
  DEVICE_PROP_DEVICE_LANGUAGE = 1;
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_LOCATION_TOKEN).GetLocationToken()
  DEVICE_PROP_DEVICE_LOCATION_TOKEN = 2;
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_DEVICE_MODEL_INFO).GetDeviceModelInfo()
  DEVICE_PROP_DEVICE_MODEL_INFO = 4;
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_MALICIOUS_APP_INFO).GetMaliciousAppInfo()
  DEVICE_PROP_MALICIOUS_APP_INFO = 5;
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_APP_VERSION_INFO).GetAppVersionInfo()
  DEVICE_PROP_APP_VERSION_INFO = 6;
  // String. Device identifier property for Android device. More details:
  // The advertising ID is a unique, user-resettable ID for advertising, provided by Google Play services.
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_GOOGLE_ADVERTISING_ID).GetGoogleAdvertisingId()
  DEVICE_PROP_GOOGLE_ADVERTISING_ID = 7;
  // String. Device identifier property for Android device. More details:
  // On Android 8.0 (API level 26) and higher versions of the platform, a 64-bit number (expressed as a hexadecimal
  // string), unique to each combination of app-signing key, user, and device. Values of ANDROID_ID are scoped by
  // signing key and user. The value may change if a factory reset is performed on the device or if an APK signing key changes
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_ANDROID_ID).GetAndroidId()
  DEVICE_PROP_ANDROID_ID = 8;
  // IP Address Token represents an IP Address in obfuscator service.
  // The IP is the originating IP address or X-Forwarded-For of the user's device.
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_IP_ADDRESS_TOKEN).GetIpAddressToken()
  DEVICE_PROP_IP_ADDRESS_TOKEN = 9;
  // Info related to all the apps installed in a user's device
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_ALL_APPS_INFO).GetAllAppsInfo()
  DEVICE_PROP_ALL_APPS_INFO = 10;
  // Identifier property for app install
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_APP_INSTALL_ID).GetAppInstallId()
  DEVICE_PROP_APP_INSTALL_ID = 11;
  // Device's fixed RAM and internal storage
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_MEMORY_INFO).GetDeviceMemoryInfo()
  DEVICE_PROP_MEMORY_INFO = 12;
  // Unique identifier of the mobile device. For Android it's Android_ID and for iOS it's Advertising ID.
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_MEMORY_INFO).GetDeviceId()
  DEVICE_PROP_DEVICE_ID = 13;
  // Maps to typesv2.AppName
  // Gamma getter: GetUserDevicePropertiesResponse.GetPropValue(typesv2.DeviceProperty_DEVICE_PROP_APP_NAME).GetAppName()
  DEVICE_PROP_APP_NAME = 14;
}

// list of this message is sent in UpsertUserDevicePropertyRequest for information regarding which properties to update
message DevicePropertyKeyValuePair {
  // enum for type of property being stored
  typesv2.DeviceProperty device_property = 1;
  // value for the property being stored
  typesv2.PropertyValue property_value = 2;
}
