syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// Validator for text fields
message TextFieldValidator {
  oneof validator {
    RegexValidator regex_validator = 2;
  }
}

// Regex validator validates given input with the regex, if doesn't match display error
message RegexValidator {
  string regex = 1;
  string validation_message = 2;
}

