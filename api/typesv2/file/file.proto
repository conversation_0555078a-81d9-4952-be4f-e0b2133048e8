syntax = "proto3";

package api.typesv2.common.file;

option go_package = "github.com/epifi/be-common/api/typesv2/common/file";
option java_package = "com.github.epifi.gamma.api.typesv2.common.file";

message File {
  FileType type = 1;
  string base64_data = 2;
  string url = 3;
  string file_name = 4;
}

enum FileType {
  FILE_TYPE_UNSPECIFIED = 0;
  FILE_TYPE_PDF = 1;
  FILE_TYPE_BINARY = 2;
  FILE_TYPE_JPEG = 3;
  FILE_TYPE_JPG = 4;
  FILE_TYPE_TIFF = 5;
  FILE_TYPE_PNG = 6;
  FILE_TYPE_TXT = 7;
}
