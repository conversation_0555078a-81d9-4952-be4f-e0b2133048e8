syntax = "proto3";

package api.typesv2;

import "api/typesv2/address.proto";
import "api/typesv2/common/phone_number.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// Nominee holds information related to a nominee needed while creating accounts.
// In case the nominee is a minor, guardian info is also included.
message Nominee {
  // Id of the nominee
  string id = 1;

  // Id of the actor the nominee has been added by
  string actor_id = 2;

  // This field indicates the type of relationship between the nominee and account holder.
  RelationType relationship = 3 [(validate.rules).enum = {not_in: [0, 27, 28, 29]}];

  // Name of the nominee
  string name = 4;

  // date of birth of the nominee
  google.protobuf.Timestamp dob = 5;

  // Contact details of the nominee like address, email, phone number etc.
  ContactInfo contact_info = 6;

  // if nominee is minor then guardian of nominee.
  typesv2.GuardianInfo guardian_info = 7;

  // pan associated with the nominee
  string pan = 8 [deprecated = true];

  // Nominee document
  NomineeDocument nominee_document = 9;
}

message GuardianInfo {
  // Guardian relationship list spec here: https://docs.google.com/document/d/1GEuZw0jPKGuVhBxqR8EhRI_pTlmD12F3GCBqpkjn8nk/edit#
  // `GuardianRelationType` list is just for the client to consume in order to display the list to the end user.
  // Backend systems deal only with `RelationType` enum.
  enum GuardianRelationType {
    RELATION_TYPE_UNSPECIFIED = 0;

    COURT_APPOINTED = 1;
    DE_FACTO_GUARDIAN = 2;
    FATHER = 3;
    MOTHER = 4;
    OTHERS = 5;
  }

  // Based on the relation type with nominee guardian code will be send to vendor
  RelationType relationship = 1 [(validate.rules).enum = {in: [7, 14, 17, 27, 28, 29]}];

  // Full name of guardian for nominee
  string name = 2;

  // Contact info of the guardian for nominee
  ContactInfo contact_info = 3;
}

message ContactInfo {
  typesv2.common.PhoneNumber phoneNumber = 1 [(validate.rules).message.skip = true];
  string email_id = 2;
  typesv2.PostalAddress address = 3;
  typesv2.AddressType address_type = 4;
}


// Denotes the type of relationship between the nominee and account holder.
// Also includes the relationship type between guardian and nominee in case the nominee is minor.
//
// Note:
// Among the following relationships, a nominee can have all relations except: COURT_APPOINTED and DE_FACTO_GUARDIAN
// And a guardian can have only the following relationships: COURT_APPOINTED, DE_FACTO_GUARDIAN, FATHER, MOTHER, OTHERS
// API specs here: https://docs.google.com/document/d/1GEuZw0jPKGuVhBxqR8EhRI_pTlmD12F3GCBqpkjn8nk/edit#
enum RelationType {
  RELATION_TYPE_UNSPECIFIED = 0;

  BENEFICIAL_OWNER = 1;
  BROTHER_IN_LAW = 2;
  BROTHER = 3;
  DAUGHTER_IN_LAW = 4;
  DAUGHTER = 5;
  FATHER_IN_LAW = 6;
  FATHER = 7;
  GRAND_DAUGHTER = 8;
  GRAND_FATHER = 9;
  GRAND_MOTHER = 10;
  GRAND_SON = 11;
  HUSBAND = 12;
  MOTHER_IN_LAW = 13;
  MOTHER = 14;
  NEPHEW = 15;
  NIECE = 16;
  OTHERS = 17;
  STEP_FATHER = 18;
  SISTER_IN_LAW = 19;
  SISTER = 20;
  STEP_MOTHER = 21;
  SON_IN_LAW = 22;
  SON = 23;
  STEP_DAUGHTER = 24;
  STEP_SON = 25;
  WIFE = 26;

  COURT_APPOINTED = 27;
  DEFACTO_GUARDIAN = 28 [deprecated = true];
  DE_FACTO_GUARDIAN = 29;
}

message NomineeDocument {
  // Document type user is giving
  NomineeDocumentType document_type = 9;

  // document number associated with the nominee
  // For document type PAN, it need to be PAN
  // document type AADHAAR, last 4 digits
  // document type driving license, full DL number
  string document_number = 10;
}

enum NomineeDocumentType {
  NOMINEE_DOCUMENT_TYPE_UNSPECIFIED = 0;
  NOMINEE_DOCUMENT_TYPE_PAN = 1;
  NOMINEE_DOCUMENT_TYPE_AADHAAR = 2; // Note: for aadhaar, we are asking user only last 4 digits of the nominee
  NOMINEE_DOCUMENT_TYPE_DRIVING_LICENSE = 3;
}

enum NomineeEntryPoint {
  NOMINEE_ENTRY_POINT_UNSPECIFIED = 0;
  NOMINEE_ENTRY_POINT_USER_PROFILE_DETAILS = 1;
  NOMINEE_ENTRY_POINT_WEALTH_ONBOARDING = 2;
}
