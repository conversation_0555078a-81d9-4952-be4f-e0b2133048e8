syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// enum to identify type of flow
enum FeedbackFlowIdentifierType {
  FEEDBACK_FLOW_IDENTIFIER_TYPE_UNSPECIFIED = 0;
  FEEDBACK_FLOW_IDENTIFIER_TYPE_APP_FLOW = 1;
  FEEDBACK_FLOW_IDENTIFIER_TYPE_DROP_OFF = 2;
  FEEDBACK_FLOW_IDENTIFIER_TYPE_CTA = 3;
}

// enum to identify the feedback flow for feedback platform
enum FeedbackAppFlowIdentifier {
  FEEDBACK_APP_FLOW_IDENTIFIER_UNSPECIFIED = 0;
  // to ask feedback flow after funds are withdrawn from jump
  // to be used in response of GetP2POrderStatus frontend server RPC
  FEEDBACK_APP_FLOW_IDENTIFIER_JUMP_POST_WITHDRAWAL = 1;
  // to trigger in app csat survey
  // to be used in response of GetSupportTicketByIdForApp frontend server RPC
  FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT = 2;
  // to trigger language preference survey
  // to be used in  GetProfileSettingPageSection
  FEEDBACK_APP_FLOW_IDENTIFIER_LANGUAGE_PREFERENCE_SURVEY = 3;
  // to trigger in app feedback flow when user dismisses a nudge
  FEEDBACK_APP_FLOW_IDENTIFIER_NUDGE_DISMISSAL = 4;
  // trigger in app during usstocks withdraw fund screen
  FEEDBACK_APP_FLOW_IDENTIFIER_USS_WITHDRAW_FUND = 24;
  // referrals feedback app flow identifier for happy flow
  FEEDBACK_APP_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS = 25;
  // Re vkyc nudge feedback flow identifier
  FEEDBACK_APP_FLOW_IDENTIFIER_RE_VKYC_NUDGE = 26;
  // to trigger in app csat survey for auto ids
  FEEDBACK_APP_FLOW_IDENTIFIER_IN_APP_CSAT_AUTO_IDS = 27;
}

enum FeedbackDropOffFlowIdentifier {
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_UNSPECIFIED = 0;
  // to ask feedback flow if user drops off during us stocks onboarding flow
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_ONBOARDING = 1;

  // To trigger feedback flow after user exits Networth dashboard,
  // to be used in response of GetNetWorthDashboard FE RPC
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_NETWORTH_DASHBOARD = 2;

  // to trigger the feedback flow when a user drops off from loan origination (onboarding of a loan account) journey.
  // will be used as a flow identifier from rpcs which are needed in the Loan Origination Flow.
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOAN_ORIGINATION = 3;

  // Feedback flow that will be triggered when a user drops off from tier all plans page which is GetTierAllPlans FE RPC,
  // when user have been downgraded or when user is in grace (and belongs to Plus and infinite tier)
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_PLUS_USER_IN_GRACE_PERIOD = 4;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_INFINITE_USER_IN_GRACE_PERIOD = 5;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_PLUS_USER_DOWNGRADED = 6;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_INFINITE_USER_DOWNGRADED = 7;

  // to trigger chat survey once the back button is hit
  // to be used in GetChatInitInformationForActor
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CHATBOT_DROPOFF_SURVEY = 8;

  // to ask feedback flow if user drops off during us stocks landing page
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_US_STOCKS_LANDING_PAGE = 9 [deprecated = true];

  // to ask feedback flow if user drops off during us wallet page has added fund atleast once in  wallet
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WALLET_PAGE_FOR_EXISTING_WALLET_USER = 10;

  // to ask feedback flow if user drops off during us wallet page never added fund in  wallet
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WALLET_PAGE_FOR_NEW_WALLET_USER = 11;

  // to ask feedback flow if user has added fund atleast once in  wallet and drops off during us landing page
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_EXISTING_WALLET_USER = 12;

  // to ask feedback flow if user never added fund in wallet and drops off during us landing page
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_NEW_WALLET_USER = 13;

  // to ask feedback flow if not onboarded usstocks user and drops off during us landing page
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_LANDING_PAGE_FOR_NEW_USER = 14;

  // Example Use Case: These identifiers (15, 16, 17) represent the feedback flows for drop-offs during the
  // introduction screens of different Credit Card programs (Amplifi, Magnifi, Simplifi).
  // Users dropping off during these screens will trigger the respective feedback flow for analysis.
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_AMPLIFI = 15;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_MAGNIFI = 16;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_SIMPLIFI = 17;

  // Example Use Case: These identifiers (18, 19, 20) represent the feedback flows for drop-offs during the
  // address selection screens of different Credit Card programs (Amplifi, Magnifi, Simplifi).
  // Users dropping off during these screens will trigger the respective feedback flow for analysis.
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_ADDRESS_SELECTION_SCREEN_AMPLIFI = 18;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_ADDRESS_SELECTION_SCREEN_MAGNIFI = 19;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FD_CREATION_SCREEN_SIMPLIFI = 20;

  // Example Use Case: These identifiers (21, 22, 23) represent the feedback flows for drop-offs during the
  // final consent screens of different Credit Card programs (Amplifi, Magnifi, Simplifi).
  // Users dropping off during these screens will trigger the respective feedback flow for analysis.
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_AMPLIFI = 21;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_MAGNIFI = 22;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_FINAL_CONSENT_SCREEN_SIMPLIFI = 23;
  // user drop off during withdraw fund screen
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_USS_WITHDRAW_FUND = 24;

  // user drop off while exploring home explore screen
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_HOME_EXPLORE = 25;

  // user drop of from salary lite intro screen
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ENACH_SALARYLITE_INTRO_SCREEN = 26;

  // user drop of from salary lite mandate setup screen
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_SALARYLITE_MANDATE_SETUP = 27;

  // user drop of from DC order screen
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_DC_ORDER_SCREEN = 28;

  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_EKYC_SCREEN = 29;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_PAN_SCREEN = 30;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_LIVENESS_SCREEN = 31;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_PASSPORT_SCREEN = 32;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDDING_EMIRATES_ID_SCREEN = 33;

  // referral happy flow identifier on user drop off
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_HAPPY_FLOW_REFERRALS = 34;

  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_ONBOARDING_VKYC_SCREEN = 35;

  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_PAN_DETAILS = 36;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OTHER_DETAILS = 37;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_ADDRESS_DETAILS = 38;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_LOAN_REQUIREMENT_DETAILS = 39;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_EMPLOYMENT_DETAILS = 40;

  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CREDIT_REPORT_CONSENT = 41;

  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_LANDING_INFO_V2_LENDER_OFFER_SCREEN = 42;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_OFFER_INTRO_SCREEN = 43;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_PWA_REDIRECTION_SCREEN = 44;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_WEBVIEW_WITH_STATUS_POLL_SCREEN = 45;
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LOANS_CONSENT_V2_SCREEN = 46;

  // user drop off from cc intro screen feedback survey
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_CC_INTRO_SCREEN_V2 = 47;
  // user drop off from LLM Query search terminal screen
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_LLM_IN_APP_CSAT = 48;
  // user drop off from pay search v2 screen
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_PAY_SEARCH_V2 = 49;
  // user drop off from all plans v2 screen
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIER_ALL_PLANS_SCREEN_V2 = 50;
  // user drop off from plans home page
  FEEDBACK_DROP_OFF_FLOW_IDENTIFIER_TIERING_EARNED_BENEFIT_SCREEN = 51;
}

enum FeedbackCTAFlowIdentifier {
  FEEDBACK_CTA_FLOW_IDENTIFIER_UNSPECIFIED = 0;
  FEEDBACK_CTA_FLOW_IDENTIFIER_CREDIT_CARD_USER_INELIGIBLE = 1;
  FEEDBACK_CTA_FLOW_IDENTIFIER_REWARDS_POLL = 2;
  FEEDBACK_CTA_FLOW_IDENTIFIER_HOME_EXPLORE = 3;
  // feedback taken after responding to user's query
  FEEDBACK_CTA_FLOW_IDENTIFIER_USERS_CX_QUERY_RESPONSE = 4;
  FEEDBACK_CTA_FLOW_IDENTIFIER_MONEY_SECRETS = 5;
  FEEDBACK_CTA_FLOW_IDENTIFIER_SHARE_AND_UNLOCK_MONEY_SECRETS = 6;
  FEEDBACK_CTA_FLOW_IDENTIFIER_PORTFOLIO_TRACKER = 7;
}
