//go:generate gen_sql -typesv2=InvestmentInstrumentType
syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// An investment instrument is an asset/tool wherein money can be put in to achieve the financial goal.
enum InvestmentInstrumentType {
  INVESTMENT_INSTRUMENT_TYPE_UNSPECIFIED = 0;
  MUTUAL_FUNDS = 1;
  P2P_JUMP = 2;
  SMART_DEPOSIT = 3;
  FIXED_DEPOSIT = 4;
  US_STOCKS = 5;
  // Recurring Deposit (RD) is a term deposit that allows to make regular deposits & earn returns on the investment.
  RECURRING_DEPOSIT = 6;
  INDIAN_STOCKS = 7;
  REAL_ESTATE = 8;
  // alternate investment fund
  AIF = 9;
  PRIVATE_EQUITY = 10;
  DIGITAL_GOLD = 11;
  CASH = 12;
  DIGITAL_SILVER = 13;
  BOND = 14;
  ART_AND_ARTEFACTS = 15;
  PORTFOLIO_MANAGEMENT_SERVICE = 16;
  PUBLIC_PROVIDENT_FUND = 17;
  EMPLOYEE_STOCK_OPTION = 18;
  // National Pension Scheme
  NPS = 19;
  GADGETS = 20;
  VEHICLES = 21;
  CRYPTO = 22;
  FURNITURE = 23;
  COLLECTIBLES = 24;
  JEWELLERY = 25;
  INVESTMENT_INSTRUMENT_TYPE_OTHERS = 26;
}
