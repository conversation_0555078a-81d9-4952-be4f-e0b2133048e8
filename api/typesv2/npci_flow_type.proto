syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// An enum defining the various typesv2 of Upi pin set flow that happens in the app
// TODO(Aniruddha): There's a Pinflow type already defined in upi/service.proto GetPinFlowParametersRequest. We should
// ideally use a common one
enum NpciFlowType {
  SET_PIN = 0;
  CHANGE_PIN = 1;
  FORGOT_PIN = 2;
}
