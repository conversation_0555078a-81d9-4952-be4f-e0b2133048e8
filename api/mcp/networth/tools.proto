syntax = "proto3";

package mcp.networth;

import "api/analyser/variables/mutualfund/investment_consistency.proto";
import "api/connected_account/service.proto";
import "api/creditreportv2/service.proto";
import "api/insights/epf/service.proto";
import "api/insights/networth/service.proto";
import "api/investment/mutualfund/external/mutual_fund_external_order.proto";

option go_package = "github.com/epifi/gamma/api/mcp/networth";
option java_package = "com.github.epifi.gamma.api.mcp.networth";

message NetworthDetails {
  insights.networth.GetNetWorthValueResponse net_worth_response = 1;
  api.analyser.variables.mutualfund.MfSchemeAnalytics mf_scheme_analytics = 2;
  connected_account.GetAccountDetailsBulkResponse account_details_bulk_response = 3;
}

message TransactionDetails {
  repeated api.investment.mutualfund.external.MutualFundExternalOrder mutual_fund_external_orders = 1;
  insights.epf.GetUANAccountsResponse uan_accounts_details = 2;
}

// NetWorthDataCollection represents a comprehensive collection of financial data items
// for AI/LLM tools to analyze and export user's complete financial profile.
message NetWorthDataCollection {
  repeated NetWorthDataItem data_items = 1;
}

// NetWorthDataItem represents a single component of user's financial data with contextual description.
// This message is designed to support AI/LLM financial analysis tools by providing structured access
// to different types of financial information with human-readable descriptions.
//
// Each item contains:
// - A descriptive label explaining what the data represents
// - One of possible financial data types in a type-safe union
message NetWorthDataItem {
  string component_description = 1;
  oneof data_object {
    insights.networth.GetNetWorthValueResponse net_worth_summary = 2;
    api.analyser.variables.mutualfund.MfSchemeAnalytics mutual_fund_analytics = 3;
    connected_account.GetAccountDetailsBulkResponse connected_account_details = 4;
    MutualFundTransactionList mutual_fund_transaction_list = 5;
    insights.epf.GetUANAccountsResponse epf_account_data = 6;
    creditreportv2.GetCreditReportsResponse credit_report_data = 7;
  }
}

message MutualFundTransactionList {
  repeated api.investment.mutualfund.external.MutualFundExternalOrder transactions = 1;
}
