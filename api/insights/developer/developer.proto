// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package insights.developer;

option go_package = "github.com/epifi/gamma/api/insights/developer";
option java_package = "com.github.epifi.gamma.api.insights.developer";

enum InsightsEntity {
  INSIGHTS_ENTITY_UNSPECIFIED = 0;

  MESSAGE_PROCESSING_STATE = 1;

  MAIL_SYNC_REQUEST = 2;

  MERCHANT_QUERY = 3;

  MERCHANT = 4;

  INSIGHT_FRAMEWORK = 5;

  INSIGHT_SEGMENT = 6;

  INSIGHT_CONTEXT_MAPPING = 7;

  GENERATION_SCRIPT_RUN = 8;

  CONTENT_TEMPLATE = 9;

  INSIGHT_ENGAGEMENT = 10;

  EPF_PASSBOOK_REQUEST = 11;

  ASSET_HISTORY = 12;
}
