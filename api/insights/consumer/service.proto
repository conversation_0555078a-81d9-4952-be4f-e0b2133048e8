syntax = "proto3";
package insights.consumer;

import "api/insights/model/enums.proto";
import "api/insights/model/insight_variable_value_pair.proto";
import "api/queue/consumer_headers.proto";
import "api/connected_account/external/external.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/insights/consumer";
option java_package = "com.github.epifi.gamma.api.insights.consumer";

service Consumer {
  // rpc to add and update the run_status of generation script run entry i.e started / completed / finished
  rpc CreateOrUpdateGenerationStatus(CreateOrUpdateGenerationStatusRequest) returns (CreateOrUpdateGenerationStatusResponse);
  // rpc to store generated actor insights data in actor_insight table
  rpc StoreGeneratedActorInsights(StoreGeneratedActorInsightsRequest) returns (StoreGeneratedActorInsightsResponse);
  // consumer rpc to generate actor insights and push/send to different destination (e.g. notification center)
  rpc DeliverInsights(DeliverInsightsRequest) returns (DeliverInsightsResponse);
  // consumer rpc to process new data fetch event for connected accounts
  rpc ProcessCaNewDataFetchEvent (connected_account.external.AccountDataSyncEvent) returns (ProcessCaNewDataFetchEventResponse);
}

message CreateOrUpdateGenerationStatusRequest {
  queue.ConsumerRequestHeader consumer_request_header = 1;
  // run_id
  string id = 2 [(validate.rules).string.min_len = 1];
  string framework_id = 3;
  // airflow run id
  string platform_run_id = 4;
  // Whether spark or apache beam
  model.GenerationPlatform generation_platform = 5;
  // defines the current status of generation i.e. started, completed or failed
  model.RunStatus run_status = 6 [(validate.rules).enum = {not_in: [0]}];
  string script_name = 7;
  uint32 number_of_insights_generated = 8;
  google.protobuf.Timestamp timestamp = 9 [(validate.rules).timestamp.required = true];
}

message CreateOrUpdateGenerationStatusResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ActorInsightData {
  string target_entity_id = 1 [(validate.rules).string.min_len = 1];
  uint32 relevance_score = 2;
  repeated model.InsightVariableValuePair values = 3;
}

message StoreGeneratedActorInsightsRequest {
  queue.ConsumerRequestHeader consumer_request_header = 1;
  // run_id (generated random uuid)
  string run_id = 2 [(validate.rules).string.min_len = 1];
  string framework_id = 3 [(validate.rules).string.min_len = 1];
  // consumer gets actor_insight_data in batches of size 1000 from generation platform
  repeated ActorInsightData actor_insight_data = 4 [(validate.rules).repeated.min_items = 1];
  // Defines the validity for the insights (valid_from and valid_till are both exclusive in validity)
  google.protobuf.Timestamp valid_from = 5;
  google.protobuf.Timestamp valid_till = 6;
}

message StoreGeneratedActorInsightsResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message DeliverInsightsRequest {
  queue.ConsumerRequestHeader consumer_request_header = 1;
  repeated BatchActorDetails batch_actor_details = 2;
  model.InsightDestination destination = 3;
}

message BatchActorDetails {
  string actor_id = 1;
  string user_id = 2;
}

message DeliverInsightsResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCaNewDataFetchEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
