syntax = "proto3";

package insights.networth.magicimport;

import "api/insights/networth/model/investmentdeclaration.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/insights/networth/magicimport";
option java_package = "com.github.epifi.gamma.api.insights.networth.magicimport";


// MagicImportDetails contains asset details via magic import
// It also contains abstracted derived details of each asset
message MagicImportDetails {
    repeated MagicImportAssetDetails asset_details_list = 1;
}

message MagicImportAssetDetails {
    MagicImportAssetType asset_type = 1;
    oneof details {
        // Asset which can be added to networth e.g. stocks, mutual funds, bonds
        ConventionalAssetDetails conventional_asset_details = 2;
        // Asset which cannot be added to networth
        UnconventionalAssetDetails unconventional_asset_details = 3;
    }
}

message ConventionalAssetDetails {
    model.InvestmentDeclaration investment_declaration = 1;
    google.type.Money estimated_value = 2;
    string investment_name = 3;
    // File name from which the asset was imported
    string file_name = 4;
}

message UnconventionalAssetDetails {
    string asset_name = 1;
    string asset_type = 2;
}

enum MagicImportAssetType {
    MAGIC_IMPORT_ASSET_TYPE_UNSPECIFIED = 0;
    // Asset which can be added to networth e.g. fixed deposits, bonds, gadgets, etc.
    MAGIC_IMPORT_ASSET_TYPE_CONVENTIONAL = 1;
    // Asset which cannot be added to networth
    MAGIC_IMPORT_ASSET_TYPE_UNCONVENTIONAL = 2;
}
