syntax = "proto3";

package insights.networth.frontend;

import "api/insights/user_declaration/form/form.proto";
import "api/typesv2/investment_instrument.proto";

option go_package = "github.com/epifi/gamma/api/insights/networth/frontend";
option java_package = "com.github.epifi.gamma.api.insights.networth.frontend";

// ManualFormIdentifier id used to identify the form being filed
// This field is supposed to be sent as a marshalled json string to client for ease of forward compatability
message ManualFormIdentifier {
  oneof identifier {
    api.insights.user_declaration.form.UserDeclarationFormIdentifier insights_user_declaration_form_identifier = 1;
    ProfileDataFormIdentifier profile_data_form_identifier = 2;
    MagicImportFormIdentifier magic_import_form_identifier = 3;
  }
}

message MagicImportFormIdentifier {
  api.typesv2.InvestmentInstrumentType instrument_type = 1;
}

message ProfileDataFormIdentifier {
  ProfileDataFormIdentifierType type = 1;
}

enum ProfileDataFormIdentifierType {
  PROFILE_DATA_FORM_IDENTIFIER_TYPE_UNSPECIFIED = 0;
  PROFILE_DATA_FORM_IDENTIFIER_TYPE_PAN = 1;
  PROFILE_DATA_FORM_IDENTIFIER_TYPE_DOB = 2;
}
