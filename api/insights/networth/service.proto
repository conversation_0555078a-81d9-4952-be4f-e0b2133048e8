syntax = "proto3";

package insights.networth;

import "api/dynamic_elements/dynamic_elements.proto";
import "api/employment/service.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/insights/networth/enums/enums.proto";
import "api/insights/networth/frontend/enums.proto";
import "api/insights/networth/magicimport/magic_import_details.proto";
import "api/insights/networth/model/intermediaries.proto";
import "api/insights/networth/model/investmentdeclaration.proto";
import "api/insights/networth/model/networth_refresh_session.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/file/file.proto";
import "api/typesv2/investment_instrument.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/insights/networth";
option java_package = "com.github.epifi.gamma.api.insights.networth";

service NetWorth {
  // GetNetWorthValue computes current value for given assets and liabilityes
  // It returns detailed reason in case the computation fails
  // Response values may not follow request order
  rpc GetNetWorthValue (GetNetWorthValueRequest) returns (GetNetWorthValueResponse) {};
  // DeclareInvestment collects declaration data from the user saves the data in DB
  rpc DeclareInvestment (DeclareInvestmentRequest) returns (DeclareInvestmentResponse) {};
  // UpdateInvestmentDeclaration updates investment declared by the user
  rpc UpdateInvestmentDeclaration (UpdateInvestmentDeclarationRequest) returns (UpdateInvestmentDeclarationResponse) {};
  // GetInvestmentDeclaration returns investment declaration for a given id
  rpc GetInvestmentDeclaration (GetInvestmentDeclarationRequest) returns (GetInvestmentDeclarationResponse) {};
  // GetInvestmentDeclarations returns all investment(paginated) declarations for a given actor
  rpc GetInvestmentDeclarations (GetInvestmentDeclarationsRequest) returns (GetInvestmentDeclarationsResponse) {};
  // DeleteInvestmentDeclaration soft deletes the investment declaration
  rpc DeleteInvestmentDeclaration (DeleteInvestmentDeclarationRequest) returns (DeclareInvestmentResponse) {};
  // UpdateBulkManualAssetsCurrentValue updates current value in declaration details of manual assets
  rpc UpdateBulkManualAssetsCurrentValue (UpdateBulkManualAssetsCurrentValueRequest) returns (UpdateBulkManualAssetsCurrentValueResponse) {};
  // CreateNetWorthRefreshSession collects netWorth refresh session data from the user saves the data in DB
  rpc CreateNetWorthRefreshSession (CreateNetWorthRefreshSessionRequest) returns (CreateNetWorthRefreshSessionResponse) {};
  // UpdateNetWorthRefreshSession updates netWorth refresh session by the user
  rpc UpdateNetWorthRefreshSession (UpdateNetWorthRefreshSessionRequest) returns (UpdateNetWorthRefreshSessionResponse) {};
  // GetNetWorthRefreshSession returns netWorth refresh session for a given id
  rpc GetNetWorthRefreshSession (GetNetWorthRefreshSessionRequest) returns (GetNetWorthRefreshSessionResponse) {};
  // GetNetWorthInstrumentsRefreshDetails returns all the instrument refresh details for net worth refresh v2
  rpc GetNetWorthInstrumentsRefreshDetails (GetNetWorthInstrumentsRefreshDetailsRequest) returns (GetNetWorthInstrumentsRefreshDetailsResponse);

  // SearchAssetFormFieldOptions returns a list of options for a form field based on a search text
  // E.g., a list of PMS provider names to be used for declaring a PMS asset
  rpc SearchAssetFormFieldOptions (SearchAssetFormFieldOptionsRequest) returns (SearchAssetFormFieldOptionsResponse);

  // StoreSnapshot stores the snapshot of the net worth for the given actor
  // This doesn't currently support selective asset types to store snapshot and considers all asset types(excluding savings account)
  rpc StoreSnapshot (StoreSnapshotRequest) returns (StoreSnapshotResponse);

  // RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  // ActorId is a mandatory parameter in the Request
  // Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no elements found for the given user on this screen
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the list of targeted comms elements is fetched successfully
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse) {}

  // RPC used by the Dynamic Elements service to callback on user action on a dynamic element
  // ActorId and ElementId are mandatory parameters in the Request
  // Response contains status code
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no element exists with the given ElementId
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the callback is registered successfully
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse) {}

  // GetPortfolioChangeSummary summarizes the net worth change based on the duration provided in absolute value and percentage.
  // This rpc will respond based on the assets provided. If assets are not provided, it will respond with all assets.
  rpc GetPortfolioChangeSummary (GetPortfolioChangeSummaryRequest) returns (GetPortfolioChangeSummaryResponse) {}
  // DeleteAllInvestmentDeclaration rpc will soft delete all the investment declarations for the given actor
  rpc DeleteAllInvestmentDeclaration (DeleteAllInvestmentDeclarationRequest) returns (DeleteAllInvestmentDeclarationResponse);
  // GetAssetsDayChange returns portfolio value at given two dates and assetTypes
  // Also returns instrument distribution for each asset
  // If value for any assetType is not present, value for that asset will not be in present response map
  rpc GetAssetsDayChange (GetAssetsDayChangeRequest) returns (GetAssetsDayChangeResponse);

  rpc MagicImportFiles (MagicImportFilesRequest) returns (MagicImportFilesResponse);
}

message MagicImportFilesRequest {
  string actor_id = 1;
  repeated api.typesv2.common.file.File files = 2;
}

message MagicImportFilesResponse {
  rpc.Status status = 1;
  .frontend.deeplink.Deeplink next_action = 2;
  magicimport.MagicImportDetails magic_import_details = 3;
}

message GetAssetsDayChangeRequest {
  // actor_id is mandatory
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Initial/Starting date
  // (not naming them as start and end date because these are not range queries but point in time queries)
  google.protobuf.Timestamp initial_date = 2;
  // Final/Ending date
  google.protobuf.Timestamp final_date = 3;
  // AssetTypes to query changes for
  repeated enums.AssetType asset_types = 4;
}

message GetAssetsDayChangeResponse {
  rpc.Status status = 1;
  // Asset response map with insights.networth.enums.AssetType.String() as key
  map<string, AssetTypeDayChangeResponse> asset_type_to_day_change_response_map = 2;
}

message AssetTypeDayChangeResponse {
  // Total value of asset at initial date
  google.type.Money initial_date_total_value = 1;
  // Total value of asset at final date
  google.type.Money final_date_total_value = 2;
  // total change , combination of all instruments diff
  double total_change = 3;
  // For each asset there can be multiple instruments and this field represent distributions for each instrument
  // For example, Multiple schemes for MF data, Multiple stocks for Stocks data etc
  repeated AssetValueChange assets_value_change = 4;
}

message AssetValueChange {
  // asset_id represents unique identifier for each instrument
  // For example, security_id for Stocks, mf_id for MF etc
  string asset_id = 1;
  // Value at initial_date
  google.type.Money initial_date_value = 2;
  // Value at final_date
  google.type.Money final_date_value = 3;
  // difference is always final_date - initial_date
  // can be negative if value at initial_date is higher than value at final_date
  double change = 4;
}

message DeleteAllInvestmentDeclarationRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message DeleteAllInvestmentDeclarationResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message GetInvestmentDeclarationsRequest {
  rpc.PageContextRequest page_context = 1;
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // declarations will be filtered based on instrument types
  repeated api.typesv2.InvestmentInstrumentType instrument_types = 3;
}

message GetInvestmentDeclarationsResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  repeated model.InvestmentDeclaration investment_declaration = 3 [deprecated = true];
  repeated InvestmentDetails investment_details = 4;
}

message StoreSnapshotRequest {
  // Actor id to store snapshot for
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message StoreSnapshotResponse {
  rpc.Status status = 1;
}

message DeclareInvestmentRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // instrument in which money was invested
  api.typesv2.InvestmentInstrumentType instrument_type = 2;
  // amount invested in instrument
  google.type.Money invested_amount = 3;
  // time at which money was invested in the instrument
  google.protobuf.Timestamp invested_at = 4;
  // time at which invested amount will mature
  google.protobuf.Timestamp maturity_time = 5;
  // the rate at which invested amount is growing
  double interest_rate = 6;
  // metadata for instrument specific details
  model.OtherDeclarationDetails declaration_details = 7;
  // reference to consent recorded when user submitted this form
  string consent_id = 12;
}

message DeclareInvestmentResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  model.InvestmentDeclaration investment_declaration = 2;
}

message UpdateInvestmentDeclarationRequest {
  model.InvestmentDeclaration updated_declaration = 1;
  // fields to update
  repeated model.InvestmentDeclarationFieldMask field_masks = 2;
}

message UpdateInvestmentDeclarationResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  model.InvestmentDeclaration investment_declaration = 2;
}

message GetInvestmentDeclarationRequest {
  string id = 1 [deprecated = true];
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  string external_id = 3 [(validate.rules).string.min_len = 1];
}

message GetInvestmentDeclarationResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  model.InvestmentDeclaration investment_declaration = 2 [deprecated = true];
  InvestmentDetails investment_details = 3;
}

message DeleteInvestmentDeclarationRequest {
  string actorId = 1 [(validate.rules).string.min_len = 1];
  string external_id = 2 [(validate.rules).string.min_len = 1];
}

message DeleteInvestmentDeclarationResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

// InvestmentDetails contains user declared investment attributes and enriched investment details
message InvestmentDetails {
  model.InvestmentDeclaration investment_declaration = 1;
  google.type.Money current_value = 2;
  ComputedInvestmentDetails computed_investment_details = 3;
}

message ComputedInvestmentDetails {
  oneof computed_details {
    EsopDetails esop_details = 1;
  }
}

message EsopDetails {
  double total_vested_esops = 1;
}

message GetNetWorthValueRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  repeated AssetType asset_types = 2;
  repeated LiabilityType liability_types = 3;
}

message GetNetWorthValueResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  repeated AssetValue asset_values = 2;
  repeated LiabilityValue liability_values = 3;
}

message AssetValue {
  // Deprecated: use asset_type instead
  AssetType net_worth_attribute = 1 [deprecated = true];
  networth.enums.AssetType asset_type = 5;
  // total balance under all instruments classified as the asset type
  google.type.Money value = 2;
  // status of balance computation. non success status indicate the balance is not available
  ComputationStatus computation_status = 3;
  // error message in case the balance computation failed
  string error_message = 4;
}

message LiabilityValue {
  LiabilityType net_worth_attribute = 1;
  // total balance under all instruments classified as the liability type
  google.type.Money value = 2;
  // status of balance computation. non success status indicate the balance is not available
  ComputationStatus computation_status = 3;
  // error message in case the balance computation failed
  string error_message = 4;
}

// AssetType defines broad assets contributing to net worth
enum AssetType {
  ASSET_TYPE_UNSPECIFIED = 0;
  // saving account managed by Fi and connected via Account Aggregator
  ASSET_TYPE_SAVINGS_ACCOUNTS = 1;
  // fixed/smart/recurring deposits managed by Fi and connected via Account Aggregator
  ASSET_TYPE_FIXED_DEPOSITS = 2;
  // EPF accounts connected by user
  ASSET_TYPE_EPF = 3;
  // Indian mutual fund investments
  ASSET_TYPE_MUTUAL_FUND = 4;
  // India listed securities. Stocks, Bonds, REITs, InvITs, Options, Futures etc
  ASSET_TYPE_INDIAN_SECURITIES = 5;
  // US listed securities
  ASSET_TYPE_US_SECURITIES = 6;
  // investments in p2p lending products
  ASSET_TYPE_P2P_LENDING = 7;
  // Alternate Investment Fund
  ASSET_TYPE_AIF = 8;
  // Private equity
  ASSET_TYPE_PRIVATE_EQUITY = 9;
  // Real Estate
  ASSET_TYPE_REAL_ESTATE = 10;
  // Art & Artefacts
  ASSET_TYPE_ART_ARTEFACTS = 11;
  // bonds
  ASSET_TYPE_BONDS = 12;
  // cash
  ASSET_TYPE_CASH = 13;
  // digital gold
  ASSET_TYPE_DIGITAL_GOLD = 14;
  // digital silver
  ASSET_TYPE_DIGITAL_SILVER = 15;
  // Portfolio Management Service (PMS)
  ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE = 16;
  // Public Provident Fund (PPF)
  ASSET_TYPE_PUBLIC_PROVIDENT_FUND = 17;
  // Employee Stock Options (ESOP)
  ASSET_TYPE_EMPLOYEE_STOCK_OPTION = 18;
  // National Pension Scheme
  ASSET_TYPE_NPS = 19;
  // Gadgets
  ASSET_TYPE_GADGETS = 20;
  // Vehicles
  ASSET_TYPE_VEHICLES = 21;
  // Crypto
  ASSET_TYPE_CRYPTO = 22;
  // Furniture
  ASSET_TYPE_FURNITURE = 23;
  // Collectibles
  ASSET_TYPE_COLLECTIBLES = 24;
  // Jewellery
  ASSET_TYPE_JEWELLERY = 25;
  // Others
  ASSET_TYPE_OTHERS = 26;
}

// LiabilityType defines broad liabilities contributing to net worth
enum LiabilityType {
  LIABILITY_TYPE_UNSPECIFIED = 0;
  // outstanding balance across all credit cards
  LIABILITY_TYPE_CREDIT_CARD_OUTSTANDING = 1;
  // outstanding home loan amount
  LIABILITY_TYPE_HOME_LOAN = 2;
  // outstanding personal loan amount
  LIABILITY_TYPE_PERSONAL_LOAN = 3;
  // outstanding vehicle loan amount
  LIABILITY_TYPE_VEHICLE_LOAN = 4;
  // outstanding education loan amount
  LIABILITY_TYPE_EDUCATION_LOAN = 5;
  // other loan types not covered as any specific liability type
  LIABILITY_TYPE_OTHER_LOAN = 6;
}

enum ComputationStatus {
  COMPUTATION_STATUS_UNSPECIFIED = 0;
  COMPUTATION_STATUS_SUCCESS = 1;
  // no data found for net worth attribute
  COMPUTATION_STATUS_NOT_FOUND = 2;
  // failed to compute net worth attribute balance
  COMPUTATION_STATUS_FAILED = 3;
}

message UpdateBulkManualAssetsCurrentValueRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Map of 'external_id -> current_value'
  // external_id would be unique id corresponding to manual asset
  // current_value would be of type money whatever user inputs
  map<string, google.type.Money> updated_asset_current_values = 2;
}

message UpdateBulkManualAssetsCurrentValueResponse {
  enum Status {
    // Request has been processed successfully
    OK = 0;

    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;

    // internal server error
    INTERNAL = 13;

    // not all the investment declarations updated successfully, some of them failed
    PARTIAL_UPDATED = 101;
  }
  rpc.Status status = 1;
  // list of updated external id
  repeated string updated_external_id = 2;
  // list of failed external id
  repeated string failed_external_id = 3;
}

message CreateNetWorthRefreshSessionRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // ordered list of assets to be refreshed with their refresh info
  repeated model.AssetRefreshDetail asset_refresh_details = 2;
}

message CreateNetWorthRefreshSessionResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  model.NetWorthRefreshSession net_worth_refresh_session = 2;
}

message UpdateNetWorthRefreshSessionRequest {
  model.NetWorthRefreshSession updated_net_worth_refresh_session = 1;
  // fields to update
  repeated model.NetWorthRefreshSessionFieldMask field_masks = 2;
}

message UpdateNetWorthRefreshSessionResponse {
  enum Status {
    OK = 0;
    // netWorth refresh session not found for given id
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  model.NetWorthRefreshSession net_worth_refresh_session = 2;
}

message GetNetWorthRefreshSessionRequest {
  string net_worth_refresh_id = 1;
}

message GetNetWorthRefreshSessionResponse {
  enum Status {
    OK = 0;
    // netWorth refresh session not found for given id
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  model.NetWorthRefreshSession net_worth_refresh_session = 2;
}

message GetNetWorthInstrumentsRefreshDetailsRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // optional: only provide in case of refresh details required for particular asset
  repeated model.NetWorthRefreshAsset networth_refresh_assets = 2;
}

message GetNetWorthInstrumentsRefreshDetailsResponse {
  enum Status {
    // Request has been processed successfully
    OK = 0;

    // internal server error
    INTERNAL = 13;

    // unable to fetch InstrumentRefreshDetails for all the instruments, some of them failed
    PARTIAL = 101;
  }
  rpc.Status status = 1;

  // list of InstrumentRefreshDetails
  // contains refresh details for each supported instrument for Net Worth Refresh.
  repeated InstrumentRefreshDetails instrument_refresh_summary = 2;
}

message InstrumentRefreshDetails {
  // asset name corresponding to particular net worth refresh asset
  model.NetWorthRefreshAsset asset_name = 1;
  // last refreshed time for the instrument
  // not applicable for manual assets
  google.protobuf.Timestamp last_refreshed_time = 2;
  // last refreshed duration for the instrument
  // not applicable for manual assets
  google.protobuf.Duration last_refreshed_duration = 3;
  // instrument refresh is required or not
  bool is_refresh_required = 4 [deprecated = true];
  // return true if there is an error while getting refresh details
  // not applicable for manual assets
  bool has_error = 5;
  // applicable only for epf
  // list of uan ids to refresh
  repeated string uan = 6;
  // applicable only for manual assets
  // InvestmentInstrumentType -> ManualAssetRefreshDetails map
  // only manual assets which needs refresh would be present
  map<string, ManualAssetRefreshDetails> manual_asset_refresh_details = 7;
  // instrument refresh status e.g. required, in process, not required
  RefreshStatus refresh_status = 8;
}

message ManualAssetRefreshDetails {
  // latest refreshed time among all the declarations for the manual asset
  google.protobuf.Timestamp last_refreshed_time = 1;
  // latest refreshed duration among all the declarations for the manual asset
  google.protobuf.Duration last_refreshed_duration = 2;
  // instrument refresh is required or not
  bool is_refresh_required = 3 [deprecated = true];
  // list of investment declared by the user for the manual asset which needs to be refreshed
  repeated model.InvestmentDeclaration investment_declarations = 4;
  // return true if there is an error while getting refresh details
  bool has_error = 5;
  // manual asset refresh status e.g. required, not required
  RefreshStatus refresh_status = 8;
}

enum RefreshStatus {
  REFRESH_STATUS_UNSPECIFIED = 0;
  // last successful refresh is before instrument refresh threshold duration, so refresh is required
  REFRESH_STATUS_REQUIRED = 1;
  // last successful refresh is not before instrument refresh threshold duration, so refresh is not required
  REFRESH_STATUS_NOT_REQUIRED = 2;
  // last refresh request is in process for instrument process refresh threshold duration, so refresh is not required for now
  // if instrument refresh do not get successfully processed within process refresh threshold then refresh status would change to REFRESH_STATUS_REQUIRED
  // else as instrument is refreshed successfully refresh status would be REFRESH_STATUS_NOT_REQUIRED
  REFRESH_STATUS_IN_PROCESS = 3;
}

message SearchAssetFormFieldOptionsRequest {
  frontend.AssetFormFieldSearchIdentifier search_identifier = 1;

  string search_text = 2;
}

message SearchAssetFormFieldOptionsResponse {
  rpc.Status status = 1;

  oneof search_results {
    PmsProviderList pms_provider_list = 2;
    AifList aif_list = 3;
    CompanyList company_list = 4;
  }
}

// For use inside one-ofs only
message PmsProviderList {
  repeated model.PmsProvider pms_providers = 1;
}

// For use inside one-ofs only
message AifList {
  repeated model.AlternativeInvestmentFund alternative_investment_funds = 1;
}

message CompanyList {
  repeated employment.EmployerInfo employer_info = 1;
}

message GetPortfolioChangeSummaryRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  google.protobuf.Timestamp history_date = 2;
}

message GetPortfolioChangeSummaryResponse {
  rpc.Status status = 1;

  google.type.Money aggregated_networth_at_date = 2;
  google.type.Money current_networth_value = 3;
  google.type.Money amount_change = 4;
  float percentage_change = 5;
}
