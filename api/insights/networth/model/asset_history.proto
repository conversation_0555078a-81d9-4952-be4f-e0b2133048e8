//go:generate gen_sql -types=AssetType,AssetData
syntax = "proto3";

package insights.networth.networth_history;

import "api/insights/networth/enums/enums.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/insights/networth/model";
option java_package = "com.github.epifi.gamma.api.insights.networth.model";

message AssetHistory {
  string id = 1;
  enums.AssetType asset_type = 2;
  // Data collected in snapshot
  AssetData data = 3;
  // Date of the snapshot
  google.protobuf.Timestamp history_date = 4;
  string actor_id = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp deleted_at = 8;
}

message AssetData {
  // Deprecated, going forward this table won't store values directly
  // Instead it will have asset specific details, units for example for dynamic assets like indian_stocks and nps etc.
  google.type.Money current_value = 1 [deprecated = true];
  oneof asset_data {
    MfData mf_data = 2;
    IndianStocksData indian_stocks_data = 3;
    NpsData nps_data = 4;
  }
}

// Wrapper messages for the repeated fields
message MfData {
  repeated MfScheme mf_schemes = 1;
}

message IndianStocksData {
  repeated IndianStock indian_stocks = 1;
}

message NpsData {
  repeated NpsScheme nps_schemes = 1;
}

message MfScheme {
  // primary key(internal id) of mutual_funds table
  string mf_id = 1;
  double units = 2;
}

message IndianStock {
  // primary key(internal id) of security_listings table
  string security_listing_id = 1;
  double units = 2;
}

message NpsScheme {
  // primary key(internal id) of nps schemes table
  string scheme_id = 1;
  double units = 2;
}
