//go:generate gen_sql -types=AssetType
syntax = "proto3";

package insights.networth.enums;

option go_package = "github.com/epifi/gamma/api/insights/networth/enums";
option java_package = "com.github.epifi.gamma.api.insights.networth.enums";

// NetWorthDashBoardType is an enum used to identify the type of dashboard to render.
enum NetWorthDashBoardType {
  NET_WORTH_DASHBOARD_TYPE_UNSPECIFIED = 0;
  // ASSETS type will load only the assets page.
  ASSETS = 1;
  // NET_WORTH type will load the net worth page.
  NET_WORTH = 2;
}

enum AssetType {
  ASSET_TYPE_UNSPECIFIED = 0;
  // saving account managed by Fi and connected via Account Aggregator
  ASSET_TYPE_SAVINGS_ACCOUNTS = 1;
  // fixed/smart/recurring deposits managed by Fi and connected via Account Aggregator
  ASSET_TYPE_FIXED_DEPOSITS = 2;
  // EPF accounts connected by user
  ASSET_TYPE_EPF = 3;
  // Indian mutual fund investments
  ASSET_TYPE_MUTUAL_FUND = 4;
  // India listed securities. Stocks, Bonds, REITs, InvITs, Options, Futures etc
  ASSET_TYPE_INDIAN_SECURITIES = 5;
  // US listed securities
  ASSET_TYPE_US_SECURITIES = 6;
  // investments in p2p lending products
  ASSET_TYPE_P2P_LENDING = 7;
  // Alternate Investment Fund
  ASSET_TYPE_AIF = 8;
  // Private equity
  ASSET_TYPE_PRIVATE_EQUITY = 9;
  // Real Estate
  ASSET_TYPE_REAL_ESTATE = 10;
  // Art & Artefacts
  ASSET_TYPE_ART_ARTEFACTS = 11;
  // bonds
  ASSET_TYPE_BONDS = 12;
  // cash
  ASSET_TYPE_CASH = 13;
  // digital gold
  ASSET_TYPE_DIGITAL_GOLD = 14;
  // digital silver
  ASSET_TYPE_DIGITAL_SILVER = 15;
  // Portfolio Management Service (PMS)
  ASSET_TYPE_PORTFOLIO_MANAGEMENT_SERVICE = 16;
  // Public Provident Fund (PPF)
  ASSET_TYPE_PUBLIC_PROVIDENT_FUND = 17;
  // Employee Stock Options (ESOP)
  ASSET_TYPE_EMPLOYEE_STOCK_OPTION = 18;
  // National Pension Scheme
  ASSET_TYPE_NPS = 19;

  ASSET_TYPE_USSTOCKS = 20;

  ASSET_TYPE_INDIAN_STOCKS = 21;
  // outstanding balance across all credit cards
  ASSET_TYPE_CREDIT_CARD_OUTSTANDING = 22;
  // outstanding home loan amount
  ASSET_TYPE_HOME_LOAN = 23;
  // outstanding personal loan amount
  ASSET_TYPE_PERSONAL_LOAN = 24;
  // outstanding vehicle loan amount
  ASSET_TYPE_VEHICLE_LOAN = 25;
  // outstanding education loan amount
  ASSET_TYPE_EDUCATION_LOAN = 26;
  // other loan types not covered as any specific liability type
  ASSET_TYPE_OTHER_LOAN = 27;
  // Gadgets
  ASSET_TYPE_GADGETS = 28;
  // Vehicles
  ASSET_TYPE_VEHICLES = 29;
  // Crypto
  ASSET_TYPE_CRYPTO = 30;
  // Furniture
  ASSET_TYPE_FURNITURE = 31;
  // Collectibles
  ASSET_TYPE_COLLECTIBLES = 32;
  // Jewellery
  ASSET_TYPE_JEWELLERY = 33;

  ASSET_TYPE_OTHERS = 34;
}
