syntax = "proto3";

package api.analyser.variables;

import "api/analyser/variables/analysis_variables.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/analyser/variables";
option java_package = "com.github.epifi.gamma.api.analyser.variables";

// VariableGenerator service provides functionality to retrieve analysis variables
// This acts like a analyser data collector layer for other services
// Ideally we should be calling it on the lines of variables collector, but we are naming it generator to keep it inline with existing implementation
service VariableGenerator {
  // GetAnalysisVariables retrieves a set of requested analysis variables for a specific actor.
  // Returns a map of variable name to variable value for each requested variable.
  rpc GetAnalysisVariables (GetAnalysisVariablesRequest) returns (GetAnalysisVariablesResponse) {}
}

// Request message for retrieving analysis variables.
message GetAnalysisVariablesRequest {
  // ID of the actor for whom the analysis variables are requested.
  string actor_id = 1;

  // List of analysis variable names to retrieve.
  repeated api.analyser.variables.AnalysisVariableName analysis_variable_names = 2;
}

// Response message containing the requested analysis variables.
message GetAnalysisVariablesResponse {
  // Status of the response. Contains error details if the request failed.
  rpc.Status status = 1;

  // Map of analysis variable names to their corresponding values.
  // The key is the string representation of api.analyser.variables.AnalysisVariableName.
  map<string, api.analyser.variables.AnalysisVariable> analysis_variable_map = 2;
}
