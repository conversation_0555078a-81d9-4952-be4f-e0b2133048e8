syntax = "proto3";

package api.analyser.variables;

import "api/analyser/investment/service.proto";
import "api/analyser/variables/mutualfund/investment_consistency.proto";
import "api/analyser/variables/networth/portfolio_summary.proto";
import "api/analyser/variables/userdeclarations/user_declared_data.proto";
import "api/insights/networth/service.proto";
import "api/insights/user_declaration/model/user_declaration.proto";
import "api/investment/mutualfund/mutual_fund.proto";

option go_package = "github.com/epifi/gamma/api/analyser/variables";
option java_package = "com.github.epifi.gamma.api.analyser.variables";

enum RawDetailsName {
  RAW_DETAILS_NAME_UNSPECIFIED = 0;
  RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME = 1;
  RAW_DETAILS_NAME_MF_PORTFOLIO_HISTORY = 2;
  RAW_DETAILS_NAME_USER_DECLARED_COMPANY_DETAILS = 3;
  RAW_DETAILS_NAME_USER_DOB = 4;
  RAW_DETAILS_NAME_MF_PORTFOLIO_ANALYTICS = 5;
  RAW_DETAILS_NAME_PORTFOLIO_SUMMARY = 6;
  RAW_DETAILS_NAME_MF_LOW_TRACKING_ERROR_INDEX_FUND = 7;
  RAW_DETAILS_NAME_NETWORTH_DETAILS = 8;
  RAW_DETAILS_NAME_MF_ASSET_CATEGORY_DETAILS = 9;
  RAW_DETAILS_NAME_MF_INVESTMENT_ACTIVITIES = 10;
  RAW_DETAILS_NAME_ASSETS_DAY_CHANGE = 11;
  RAW_DETAILS_NAME_MF_WEEKLY_CHANGE = 12;
}

// RawDetails contains any of the available raw details which help in building analysis variables
message RawDetails {
  RawDetailsName raw_details_name = 1;
  oneof variable {
    // RAW_DETAILS_NAME_USER_DECLARED_MONTHLY_INCOME this will contain the raw details of the user declared monthly salary
    UserDeclaredMonthlyIncome user_declared_monthly_income = 2;
    // RAW_DETAILS_NAME_MF_PORTFOLIO_HISTORY this will contain the raw details of the mutual fund portfolio history
    MfPortfolioHistory mf_portfolio_history = 4;
    // RAW_DETAILS_NAME_USER_DECLARED_COMPANY_DETAILS
    UserDeclaredCompanyDetails user_declared_company_details = 5;
    // RAW_DETAILS_NAME_USER_DOB
    api.analyser.variables.userdeclarations.UserDob user_dob = 6;
    // RAW_DETAILS_MF_PORTFOLIO_ANALYTICS will contain over all mf portfolio and scheme level analytics
    MfPortfolioAnalytics mf_portfolio_analytics = 7;
    // RAW_DETAILS_NAME_PORTFOLIO_SUMMARY will contain daily portfolio change summary
    api.analyser.variables.networth.PortfolioSummary portfolio_summary = 8;
    // RAW_DETAILS_NAME_MF_LOW_TRACKING_ERROR_INDEX_FUND
    MfLowTrackingErrorFundDetails mf_low_tracking_error_fund_details = 9;
    // RAW_DETAILS_NAME_USER_CONNECTED_ASSETS_DETAILS
    NetworthDetails networth_details = 10;
    // RAW_DETAILS_NAME_MF_ASSET_CATEGORY_DETAILS
    mutualfund.MfAssetCategoryDetails mf_asset_category_details = 11;
    // RAW_DETAILS_NAME_MF_INVESTMENT_ACTIVITIES
    mutualfund.MfInvestmentActivities mf_investment_activities = 12;
    // RAW_DETAILS_NAME_ASSETS_DAY_CHANGE
    AssetsDayChangeWrapper assets_day_change = 13;
  }
  RawDetailsDataStatus raw_details_data_status = 3;
}

message AssetsDayChangeWrapper {
  // Asset response map with insights.networth.enums.AssetType.String() as key
  map<string, .insights.networth.AssetTypeDayChangeResponse> asset_response_map = 1;
  // security metadata map for securities/assets in the day changes response
  // key is security_id(internal to us)
  map<string, SecurityMetadata> security_metadata_map = 2;
}

message SecurityMetadata {
  string logo_url = 1;
  string security_name = 2;
}

enum RawDetailsDataStatus {
  RAW_DETAILS_DATA_STATUS_UNSPECIFIED = 0;
  RAW_DETAILS_DATA_STATUS_AVAILABLE = 1;
  RAW_DETAILS_DATA_STATUS_NOT_AVAILABLE = 2;
}

message UserDeclaredMonthlyIncome {
  api.insights.user_declaration.model.UserDeclaration declaration = 1;
}

message MfPortfolioHistory {
  repeated .analyser.investment.MFPortfolioDetails details = 1;
  .analyser.investment.PortfolioChanges enriched_portfolio_details = 2;
}

message UserDeclaredCompanyDetails {
  api.insights.user_declaration.model.UserDeclaration company_details_declaration = 1;
}

message MfSchemeAnalytics {
  api.investment.mutualfund.MutualFund mutual_fund_catalog_details = 1;
  .analyser.investment.EnrichedMfSchemeAnalytics enriched_mf_scheme_analytics = 2;
  api.investment.mutualfund.MutualFundCategoryAverage category_avg_details = 3;
}

message MfPortfolioAnalytics {
  repeated MfSchemeAnalytics mf_scheme_analytics = 1;
  repeated MfSchemeAnalytics unknown_mf_scheme_analytics = 2;
  .analyser.investment.EnrichedMFPortfolioAnalytics enriched_portfolio_analytics = 3;
}

message MfLowTrackingErrorFundDetails {
  api.investment.mutualfund.MutualFund low_tracking_error_index_fund = 1;
}

message NetworthDetails {
  repeated .insights.networth.AssetValue asset_values = 1;
}
