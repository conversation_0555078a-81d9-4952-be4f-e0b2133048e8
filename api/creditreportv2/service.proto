syntax = "proto3";

package creditreportv2;

import "api/consent/consent_type.proto";
import "api/creditreportv2/analytics.proto";
import "api/creditreportv2/internal/credit_report.proto";
import "api/creditreportv2/internal/credit_report_download.proto";
import "api/creditreportv2/internal/credit_report_raw.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/name.proto";
import "api/vendorgateway/credit_report/credit_report.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/creditreportv2";
option java_package = "com.github.epifi.gamma.api.creditreportv2";

service CreditReportManager {
  // This rpp returns report presence status and report verification status wth credit report if present
  // fe polling APIs will call this rpc to check status in both cases i.e. report presence check and report verification status
  rpc GetCreditReport (GetCreditReportRequest) returns (GetCreditReportResponse);

  rpc GetCreditReports (GetCreditReportsRequest) returns (GetCreditReportsResponse);

  // Fetch and validate credit report
  // Publish packet to retry queue if required
  rpc InitiateCreditReportVerification (InitiateCreditReportVerificationRequest) returns (InitiateCreditReportVerificationResponse);

  // publishes packet in queue to check for report presence
  // basic sanity check is performed before publishing packet
  rpc InitiateCreditReportPresenceCheck (InitiateCreditReportPresenceCheckRequest) returns (InitiateCreditReportPresenceCheckResponse);

  // publishes packet in queue to check for report presence
  // basic sanity check is performed before publishing packet
  rpc RecordReportDownloadConsent (RecordReportDownloadConsentRequest) returns (RecordReportDownloadConsentResponse);

  // clears experian raw experian data older than 6 months
  rpc PurgeExperianDataForOldUsers (PurgeExperianDataForOldUsersRequest) returns (PurgeExperianDataForOldUsersResponse);

  // StartDownloadProcess initiates the credit report download process for the given actor.
  rpc StartDownloadProcess (StartDownloadProcessRequest) returns (StartDownloadProcessResponse);

  rpc RecordUserConsent (RecordUserConsentRequest) returns (RecordUserConsentResponse);

  // returns the next action that client needs to to fetch credit report for the user.
  rpc GetNextActionForCreditReportDownload (GetNextActionForCreditReportDownloadRequest) returns (GetNextActionForCreditReportDownloadResponse);

  // returns details of the most recent process initiated by a client to download credit report from a vendor for an actor.
  rpc GetLatestDownloadProcessDetails (GetLatestDownloadProcessDetailsRequest) returns (GetLatestDownloadProcessDetailsResponse);

  // updates next action of the report download process based on the status of the auth flow process.
  rpc RecordAuthFlowCompletion (RecordAuthFlowCompletionRequest) returns (RecordAuthFlowCompletionResponse);

  // returns the state of credit report consent for an actor
  rpc GetCreditReportConsentStatus (GetCreditReportConsentStatusRequest) returns (GetCreditReportConsentStatusResponse);

  // this rpc submits authentication answers needed for customer verification.
  rpc SubmitAuthenticationAnswers (SubmitAuthenticationAnswersRequest) returns (SubmitAuthenticationAnswersResponse);

  // this RPC fetches web url that a user can access to view their cibil report
  rpc GetCibilReportWebUrl (GetCibilReportWebUrlRequest) returns (GetCibilReportWebUrlResponse);

  // this RPC fetches the features from the latest downloaded CIBIL report for an actor
  // only actor_id and list of feature names needs to be passed in this rpc.
  // if no cibil report is found, this will return the record not found status
  rpc GetCibilReportFeatures (GetCibilReportFeaturesRequest) returns (GetCibilReportFeaturesResponse);

  // this rpc is used to fetch experian data from presto
  // we need to pass actor id as parameter and the field masks we want to query on
  // in response we get query response from all requested tables
  rpc GetExperianAnalyticsDetails (GetExperianAnalyticsDetailsRequest) returns (GetExperianAnalyticsDetailsResponse);

  // returns details of the download process initiated by a client to download credit report for a given request id.
  rpc GetDownloadProcessDetails (GetDownloadProcessDetailsRequest) returns (GetDownloadProcessDetailsResponse);

  // this rpc is used to get the latest one credit report for a user and for a particular vendor at a time
  rpc GetLatestCreditReportV2 (GetLatestCreditReportV2Request) returns (GetLatestCreditReportV2Response);
}

// This enum is used to denote the status of the credit report data availability
enum DataAvailabilityStatus {
  DATA_AVAILABILITY_STATUS_UNSPECIFIED = 0;
  // Credit report is available in the DB with the complete history
  DATA_AVAILABILITY_STATUS_FOUND = 1;
  // Credit report is not found for the user
  DATA_AVAILABILITY_STATUS_NOT_FOUND = 2;
  // Credit report is available in the DB without complete history
  DATA_AVAILABILITY_STATUS_FOUND_WITHOUT_HISTORY = 3;
}

message GetCreditReportRequest {
  // mandatory
  string actor_id = 1;
}

message GetCreditReportResponse {
  // rpc status
  rpc.Status status = 1;
  // enum to denote if report is present or not
  creditreportv2.PresenceStatus presence_status = 2;
  // enum to denote verification status
  creditreportv2.VerificationStatus verification_status = 3;
  // credit report of the user
  creditreportv2.CreditReportData credit_report_data = 4;
  // download consent status granted by the user
  creditreportv2.DownloadConsent download_consent = 5;
  // complete credit report for the user fetched from vendor
  // this contains all account details of the user
  creditreportv2.CreditReport credit_report = 6;
}

message GetCreditReportsRequest {
  // mandatory
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // credit report from listed vendors will be fetched. If list is empty, it will be fetch all credit reports
  repeated vendorgateway.Vendor vendor = 2;
  // The number of credit reports to fetch. Default value will be 1
  uint64 limit = 3 [(validate.rules).uint64.lte = 10];
}

message GetCreditReportsResponse {
  // rpc status
  rpc.Status status = 1;
  // list of credit reports
  repeated CreditReportDownloadDetails credit_reports = 2;
}

message CreditReportDownloadDetails {
  // credit_report_data will be populated in case EXPERIAN
  vendorgateway.credit_report.CreditReportData credit_report_data = 1;
  google.protobuf.Timestamp created_at = 2;
  // credit_report_raw will be populated only in case of CIBIL
  creditreportv2.CreditReportRaw credit_report_raw = 3;
  vendorgateway.Vendor vendor = 4;
  DataAvailabilityStatus credit_report_data_availability_status = 5;
}

message InitiateCreditReportVerificationRequest {
  // mandatory
  string actor_id = 1;
}

message InitiateCreditReportVerificationResponse {
  enum Status {
    OK = 0;
    // consent was not given by user
    CONSENT_NOT_FOUND = 101;
    // credit report record was not found via vendor call
    CREDIT_REPORT_NOT_PRESENT = 102;
  }
  // rpc status
  rpc.Status status = 1;
}


message InitiateCreditReportPresenceCheckRequest {
  // mandatory
  string actor_id = 1;
}

message InitiateCreditReportPresenceCheckResponse {
  // rpc status
  rpc.Status status = 1;
}


message RecordReportDownloadConsentRequest {
  // mandatory
  string actor_id = 1;
  // true denotes consent is given
  // false denotes consent is not given
  bool consent = 2;
}

message RecordReportDownloadConsentResponse {
  // rpc status
  rpc.Status status = 1;
}

message PurgeExperianDataForOldUsersRequest {}

message PurgeExperianDataForOldUsersResponse {
  rpc.Status status = 1;
  // Actor for whom the data was deleted
  repeated string actor_ids = 2;
}

message StartDownloadProcessRequest {
  // Credit report will be downloaded for this actor
  string actor_id = 1;
  // Request id can be used to track the download process
  string request_id = 2;
  // Provenance field tells which service invoked the download credit report process for the actor
  creditreportv2.Provenance provenance = 3;
  // Credit report will be downloaded using this vendor's apis.
  vendorgateway.Vendor vendor = 4;
  // This deeplink will where the user will be redirected after the download process completes
  frontend.deeplink.Deeplink redirect_deeplink = 5;
  // email id of the user for which credit report needs to be fetched
  // optional field, if not sent we will use the email present in user profile
  string email_id = 6;
  // auth info sent in case of auth already verified
  AuthInfo auth_info = 7;
  // pan id that can be sent by service which can own pan fetching stage
  string pan = 8;
  // consent info in case of consent already fetched
  ConsentDetails consent_details = 9;
  // name that can be sent by service which can own name fetching stage
  // not mandatory field, if not present fetch best name from user
  api.typesv2.common.Name name = 13;
  // allow credit report fetch without PAN number.
  // if not set as true, we'll ask user to enter PAN number if not found
  // currently only supported for experian.
  bool allow_without_pan = 11;
}

message AuthInfo {
  // auth request id in case of auth already verified for credit report download
  string auth_request_id = 1;
}

message ConsentDetails {
  consent.ConsentType consent_type = 1;
}

message StartDownloadProcessResponse {
  rpc.Status status = 1;
  creditreportv2.CreditReportDownloadStatus process_status = 2;
  creditreportv2.CreditReportDownloadSubStatus process_sub_status = 3;
  frontend.deeplink.Deeplink next_action = 4;
}

message RecordUserConsentRequest {
  string actor_id = 1;
  string request_id = 2;
  ConsentAction consent_action = 3;
  api.typesv2.common.Device device = 4;
}

enum ConsentAction {
  CONSENT_ACTION_UNSPECIFIED = 0;
  CONSENT_ACTION_ACCEPTED = 1;
  CONSENT_ACTION_REJECTED = 2;
}

message RecordUserConsentResponse {
  rpc.Status status = 1;
}

message GetNextActionForCreditReportDownloadRequest {
  string client_request_id = 1 [(validate.rules).string.min_len = 1];
}

message GetNextActionForCreditReportDownloadResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetLatestDownloadProcessDetailsRequest {
  // actor for whom credit report was downloaded
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // client service which requested a download.
  creditreportv2.Provenance provenance = 2;
  // vendor from whom report was fetched.
  vendorgateway.Vendor vendor = 3;
}

message GetLatestDownloadProcessDetailsResponse {
  rpc.Status status = 1;
  // client req id for the download
  string request_id = 2;
  // current status of the process
  creditreportv2.CreditReportDownloadStatus process_status = 3;
  // current sub-status of the process
  creditreportv2.CreditReportDownloadSubStatus process_sub_status = 4;
  // timestamp when request was registered
  google.protobuf.Timestamp created_at = 5;
  frontend.deeplink.Deeplink next_action = 6;
}

message RecordAuthFlowCompletionRequest {
  string client_req_id = 1 [(validate.rules).string.min_len = 1];
}

message RecordAuthFlowCompletionResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetCreditReportConsentStatusRequest {
  string actor_id = 1;
  // consent status for the credit report vendor
  vendorgateway.Vendor vendor = 2;
}

// CreditReportConsentStatus represents the state of a credit report fetch consent
enum CreditReportConsentStatus {
  CREDIT_REPORT_CONSENT_STATUS_UNSPECIFIED = 0;
  // no consent is available
  CREDIT_REPORT_CONSENT_STATUS_UNAVAILABLE = 1;
  // consent is expired
  CREDIT_REPORT_CONSENT_STATUS_EXPIRED = 2;
  // consent needs to be extended by user
  CREDIT_REPORT_CONSENT_STATUS_EXTENSION_REQUIRED = 3;
  // consent is active
  CREDIT_REPORT_CONSENT_STATUS_ACTIVE = 4;
}

message GetCreditReportConsentStatusResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // Internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  CreditReportConsentStatus credit_report_consent_status = 2;
}

enum CreditReportFetchStatus {
  CREDIT_REPORT_FETCH_STATUS_UNSPECIFIED = 0;
  // Credit report is fetched and is available
  CREDIT_REPORT_FETCH_STATUS_AVAILABLE = 1;
  // Credit report does not exist for the user.
  CREDIT_REPORT_FETCH_STATUS_NOT_FOUND = 2;
  // Credit report was not fetched for the user
  CREDIT_REPORT_FETCH_STATUS_NOT_FETCHED = 3;
}

message SubmitAuthenticationAnswersRequest {
  // client request id identifies the workflow for which the authentication is taking place
  string client_request_id = 1;
  // challenge id uniquely identifies the question for which the answer is submitted
  string challenge_id = 2;
  oneof answer_specific_input {
    bool resend_otp = 3;
    bool skip_question = 4;
    string user_input = 5;
  }
}

message SubmitAuthenticationAnswersResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetCibilReportWebUrlRequest {
  string actor_id = 1;
}

message GetCibilReportWebUrlResponse {
  rpc.Status status = 1;
  string url = 2;
}

message GetCibilReportFeaturesRequest {
  string actor_id = 1;
  repeated string features_names = 2;
}

message GetCibilReportFeaturesResponse {
  rpc.Status status = 1;
  // list of features data for identifiers sent in the request
  map<string, google.protobuf.Value> feature_value_map = 2;
  DataAvailabilityStatus credit_report_data_availability_status = 3;
}

message GetExperianAnalyticsDetailsRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // table names you have to make query on in experian schema
  repeated ExperianAnalyticsDetailsFieldMask field_mask_list = 2;
}
message GetExperianAnalyticsDetailsResponse {
  rpc.Status status = 1;
  repeated ReportAddress report_addresses = 2;
  repeated ReportEmail report_emails = 3;
  repeated ReportPhone report_phones = 4;
}

message GetDownloadProcessDetailsRequest {
  // client service which requested a download.
  string request_id = 1;
}

message GetDownloadProcessDetailsResponse {
  rpc.Status status = 1;
  // client req id for the download
  string request_id = 2;
  // current status of the process
  creditreportv2.CreditReportDownloadStatus process_status = 3;
  // current sub-status of the process
  creditreportv2.CreditReportDownloadSubStatus process_sub_status = 4;
  // timestamp when request was registered
  google.protobuf.Timestamp created_at = 5;
  frontend.deeplink.Deeplink next_action = 6;
}

message GetLatestCreditReportV2Request {
  // mandatory field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // credit report vendor from which report is fetched
  vendorgateway.Vendor vendor = 2;
}

message GetLatestCreditReportV2Response {
  // rpc status
  rpc.Status status = 1;
  // list of credit report
  CreditReportDownloadDetails credit_report = 2;
}
