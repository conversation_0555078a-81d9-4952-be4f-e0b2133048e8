syntax = "proto3";

package cx.chat.bot.workflow;

import "api/cx/ticket/ticket.proto";

option go_package = "github.com/epifi/gamma/api/cx/chat/bot/workflow";
option java_package = "com.github.epifi.gamma.api.cx.chat.bot.workflow";


// CreateTicketResult: action result for create ticket
message CreateTicketResult {
  cx.ticket.Ticket ticket = 1;
}

// CreateTicketParams is the input params required for creating a ticket
message CreateTicketParams {
  // ticket object with details required to create a ticket
  cx.ticket.Ticket ticket = 1;
}
