// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package cx.chat;

import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/chat.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/device.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/chat";
option java_package = "com.github.epifi.gamma.api.cx.chat";


// SenseforthChatInitInformation provides information
// to initialize senseforth webview on client side
message SenseforthChatInitInformation {
  // url which client has to load for chatbot webview
  string web_view_url = 1;
  // short token which client need to pass to senseforth
  string short_token = 2;
  // If this flag is set to TRUE , the client should reuse the short token & url stored in the cache
  api.typesv2.common.BooleanEnum reuse_cache_data = 3;
  // The context to invoke the bot. Ref: https://docs.google.com/document/d/15A9XIZRPD0omXeBTIm36WXU3GuQU46zt32zagqB2wrs/edit
  // This context will be used by client only if the deeplink(which triggered this RPC) doesn't contain context_code
  string bot_context_code = 4;
}

message GetReferenceIdForActorRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message GetReferenceIdForActorResponse {
  rpc.Status status = 1;

  string actor_id = 2;

  string reference_id = 3;

  // App id of the freshchat mobile sdk
  string app_id = 4;

  // App key of the freshchat mobile sdk
  string app_key = 5;

  // Domain name of freshchat mobile sdk
  string domain = 6;
}

// failure object to specify failure info from client side in chat view
message ClientSideChatFailureInfo {
  // chat view which client was trying to load but failed
  api.typesv2.InAppChatViewType last_tried_chat_view = 1;
  // count of client side failures like failed to load web view
  int64 failure_count = 2;
  // error reason at client eg: sent by the senseforth webview to the app client
  string failure_reason = 3;
}

message GetChatInitInformationForActorRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // chat view which client loaded in last session
  api.typesv2.InAppChatViewType last_successfully_loaded_chat_view = 2;
  // time at which user last interacted with fi chat option
  google.protobuf.Timestamp last_successful_session_time = 3;
  // client side failure information
  ClientSideChatFailureInfo client_side_chat_failure_info = 4;
  // client will use this if the user explicitly wants to start a new session
  // if this flag is set a new short token would be generated for the Senseforth webview
  // This flag is ignored in case the chat view to be loaded is freshchat
  api.typesv2.common.BooleanEnum force_new_session = 5;
  // device details of the user
  api.typesv2.common.Device device = 6;
  // client can pass a context code to load a particular starting menu on senseforth chatbot
  // the context code setup needs to be done on the a.ware platform
  string senseforth_bot_context_code = 7;
}

message GetChatInitInformationForActorResponse {
  // will return Status
  // OK for success
  // Internal for other server errors
  rpc.Status status = 1;

  // mapped freshdesk_id received from vendor-mapping service for current actor
  string reference_id = 2;

  // App id of the freshchat mobile sdk
  string app_id = 3;

  // App key of the freshchat mobile sdk
  string app_key = 4;

  // Domain name of freshchat mobile sdk
  string domain = 5;

  // Custom user properties to be passed to freschat sdk via android/ios client
  map<string, string> custom_user_properties = 6;

  // backend generated email to be passed by mobile client to Freshchat sdk
  // it is not part of custom property because client sdk explicitly expects
  // email to passed separately as an identifier
  string email = 7;

  // enum specifying which chat view has to be loaded on client side
  api.typesv2.InAppChatViewType chat_view_to_be_loaded = 8;

  // info require to use senseforth webview on client side
  SenseforthChatInitInformation senseforth_chat_init_information = 9;

  // client should auto retry in case of failures if this flag is set to true
  api.typesv2.common.BooleanEnum should_auto_retry = 10;

  // To filter and display only Topics tagged with a specific term, use the filterByTags API in ConversationOptions instance passed to showConversations() API as below.
  // Eg: To link and display only specific Topics from say orders page in your app, those specific Topics can be tagged with the term "order_queries".
  repeated string topic_tags = 11;

  // All chatbot init information specific to SDK is to be populated in this field
  // TODO(sayan): once migration is done, deprecate all senseforth and freshchat specific fields
  api.typesv2.ChatbotInitInformation chatbot_init_information = 12;
}

message UpdateTicketForChatRequest {
  int64 ticket_id = 1;

  string reference_id = 2;
}

message UpdateTicketForChatResponse {
  rpc.Status status = 1;
}

message GetActorIdFromReferenceIdRequest {
  // Mandatory
  string reference_id = 1 [(validate.rules).string.min_len = 1];
}

message GetActorIdFromReferenceIdResponse {
  rpc.Status status = 1;

  string actor_id = 2;
}

message UpdateTicketForSourceRequest {
  // mandatory
  int64 ticket_id = 1;

  // one of the phone_numeber or email parameter is mandatory
  string phone_number = 2;

  string email = 3;

  string source = 4;
}

message UpdateTicketForSourceResponse {
  rpc.Status status = 1;
}

service Chats {
  // service to fetch all the necessary properties needed to initialize chat sdk
  // it will return reference_id, freshchat app keys and custom user properties to be passed to vendor via sdk by android/ios
  rpc GetChatInitInformationForActor (GetChatInitInformationForActorRequest) returns (GetChatInitInformationForActorResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // service to fetch mapping between actor and internally generated reference id specifically for freshchat
  // This will create a new entry in DB if the mapping does not exist else return the existent reference id
  // the reference id will be generated by id gen service and is very specific to CX chat flow
  rpc GetReferenceIdForActor (GetReferenceIdForActorRequest) returns (GetReferenceIdForActorResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // service which is called by web server whenever a webhook gets triggered on ticket being created via chat
  // The business logic is to update the ticket with the actual requester details to enable linking of
  // past ticket for an actor. This will also require us to delete the stale contact in freshdesk which was created
  // The service will receive ticket id and reference id from freshchat
  // To have robust updates the service does async processing by pushing the event to queue
  // INTERNAL SERVER ERROR : if message was not pushed to queue due to some error, in such case we will rely on cron
  // job to do the update for us for consistency
  // INVALID ARGUMENT : if request does not have mandatory parameter ticket id and reference id
  rpc UpdateTicketForChat (UpdateTicketForChatRequest) returns (UpdateTicketForChatResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // service to fetch mapping between reference id and actor
  // NOT FOUND if mapping does not exist
  // INTERNAL SERVER ERROR if any other error
  rpc GetActorIdFromReferenceId (GetActorIdFromReferenceIdRequest) returns (GetActorIdFromReferenceIdResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // service which is called by web server whenever a webhook gets triggered on ticket being created via call or portal
  // The business logic is to update the ticket with the email and phone number details to enable linking of
  // past ticket for an actor.
  // To have robust updates the service does async processing by pushing the event to queue
  // INTERNAL SERVER ERROR : if message was not pushed to queue due to some error, in such case we will rely on cron
  // job to do the update for us for consistency
  // INVALID ARGUMENT : if request does not have mandatory parameter
  rpc UpdateTicketForSource (UpdateTicketForSourceRequest) returns (UpdateTicketForSourceResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}
