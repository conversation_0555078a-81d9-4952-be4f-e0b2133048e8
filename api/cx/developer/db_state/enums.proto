syntax = "proto3";

package cx.developer.db_state;

option go_package = "github.com/epifi/gamma/api/cx/developer/db_state";
option java_package = "com.github.epifi.gamma.api.cx.developer.db_state";

// list of services which are exposing db states
// each service will have it's own implementation for exposing data
// whenever a new service is added you need to write integration for the service on CX backend
// service owner will need to define and implement a service(which implements collector service)
// cx will use the client of that service to integrate
// error will be thrown by db states rpc for any service which is not integrated in CX
// this service is not equivalent to the servers in gamma
// you can have multiple services from the same server as well
// deprecated in the favor of sherlock.dev.dbstate.Service as this is needed in gringott repo as well
// until we have not fully moved to sherlock.dev.dbstate.Service we need to add new services here as well
enum Service {
  SERVICE_UNSPECIFIED = 0;

  CX = 1;

  COMMS = 2;

  ACTOR = 3;

  DEPOSIT = 4;

  USER = 5;

  ORDER = 6;

  KYC = 7;

  PAYMENT_INSTRUMENT = 8;

  CARD = 9;

  LIVENESS = 10;

  SAVINGS = 11;

  UPI = 12;

  INAPPHELP = 13;

  CASBIN = 14;

  INSIGHTS = 15;

  WAITLIST = 16;

  REWARDS = 17;

  AUTH = 18;

  VENDOR_MAPPING = 19;

  TIMELINE = 20;

  CASPER = 21;

  MERCHANT = 22;

  RMS = 23;

  FITTT = 24;

  CONNECTED_ACCOUNT = 25;

  IN_APP_REFERRAL = 26;

  WEALTH_ONBOARDING = 27;

  INVESTMENT = 28;

  RECURRING_PAYMENT = 29;

  P2P_INVESTMENT = 30;

  SEGMENT = 31;

  SALARY_PROGRAM = 32;

  ANALYSER = 33;

  CATEGORIZER = 34;

  PRE_APPROVED_LOAN = 35;

  CELESTIAL = 36;

  RISK = 37;

  NUDGE = 38;

  SCREENER = 39;

  FIREFLY = 40;

  AUTH_ORCHESTRATOR = 41;

  PAY = 42;

  USSTOCKS = 43;

  TIERING = 44;

  AML = 45;

  ALFRED = 46;

  PAN = 47;

  QUEST = 48;

  UPCOMING_TRANSACTIONS = 49;

  HEALTH_ENGINE = 50;

  CMS = 51;

  ENACH = 52;

  BANK_CUSTOMER = 53;

  EMPLOYMENT = 54;

  COLLECTION = 55;

  OMEGLE = 56;

  VKYC_CALL = 57;

  STOCKGUARDIAN_VKYC_CALL = 58;

  STOCKGUARDIAN_OMEGLE = 59;

  STOCKGUARDIAN_CREDIT_REPORT = 60;

  STOCKGUARDIAN_CREDIT_RISK = 61;

  STOCKGUARDIAN_E_SIGN = 62;

  STOCKGUARDIAN_IN_HOUSE_BRE = 63;

  STOCKGUARDIAN_LMS = 64;

  STOCKGUARDIAN_APPLICATION = 65;

  STOCKGUARDIAN_BRE = 66;

  STOCKGUARDIAN_DOCS = 67;

  STOCKGUARDIAN_MATRIX = 68;

  STOCKGUARDIAN_CUSTOMER = 69;

  STOCKGUARDIAN_APPLICANT = 70;

  STOCKGUARDIAN_KYC_VENDOR_DATA = 71;

  TSP_USER = 72;

  LEADS = 73;

  CREDIT_REPORT = 74;

  ACCOUNTS = 75;
}

// specifies the data type to be used for the given parameter
enum ParameterDataType {
  PARAMETER_DATA_TYPE_UNSPECIFIED = 0;

  // value for the parameter will be a string
  // text input box will be shown on UI
  // proto data type string will be used for value
  STRING = 1;

  // value of parameter will be a integer
  // number input will be shown on UI
  // proto data type int64 will be used for value
  INTEGER = 2;

  // value of parameter will be a double
  // number input will be shown on UI
  // proto data type double will be used for value
  DOUBLE = 3;

  // value of parameter will be a timestamp
  // date picker will be shown on UI
  // proto data type google.protobuf.Timestamp will be used for value
  TIMESTAMP = 4;

  // value of parameter will be a string(one of the option given in ParameterMeta)
  // a dropdown will be shown on UI with options given in the ParameterMeta
  // user will be able to select only one of the dropdown values
  // proto data type string will be used for value
  DROPDOWN = 5;

  // value of parameter will be of type Money(amount and currency)
  // a custom money input box will be shown on UI
  // proto data type google.type.Money  will be used for value
  MONEY = 6;

  // value of parameter will be of type Name(honorific, first name,middle name ,last name)
  // a custom money input box will be shown on UI
  // proto data type api.typesv2.common.Name will be used for value
  NAME = 7;

  // value of parameter will be of type phone number(country code and national number)
  // a custom money input box will be shown on UI
  // proto data type api.typesv2.common.PhoneNumber will be used for value
  PHONE_NUMBER = 8;

  // value of parameter will be a list of strings(one or more options given in ParameterMeta)
  // a dropdown will be shown on UI with options given in the ParameterMeta
  // user will be able to select multiple dropdown values
  // proto data type repeated string will be used for value
  MULTI_SELECT_DROPDOWN = 9;

  // value of parameter will be a pagination context object
  // pagination options will be added on UI if this parameter type is used
  // user will be able to select limit and navigate to prev and next pages
  // proto data type PageContextRequest in cx header.proto will be used for value
  PAGE_CONTEXT = 10;

  // value of parameter will be a file
  // a file upload option will be shown on UI
  // uploaded file will be passed to backend in bytes format
  FILE = 11;

  // value of parameter will be a list of files
  // a file upload option will be shown on UI
  // uploaded files will be passed to backend in bytes format
  FILE_LIST = 12;

  // value of parameter will be multiline text
  // a text area will shown on UI for parameters with this type
  // values will be passed as string value to backend
  MULTILINE_TEXT = 13;
}

// indicates whether the given parameter is optional or mandatory
enum ParameterOption {
  PARAMETER_OPTION_UNSEPCIFIED = 0;

  // given parameter is optional and not needed to be passed in every data request
  OPTIONAL = 1;

  // given parameter is mandatory and will be passed in every data request
  // validation will be added on UI
  // submit will be disabled till all the mandatory fields are filled
  MANDATORY = 2;
}

enum FileContentType {
  FILE_CONTENT_TYPE_UNSPECIFIED = 0;

  FILE_CONTENT_TYPE_CSV = 1;

  FILE_CONTENT_TYPE_TXT = 2;

  FILE_CONTENT_TYPE_JPEG = 3;

  FILE_CONTENT_TYPE_XLS = 4;

  FILE_CONTENT_TYPE_XLSX = 5;

  FILE_CONTENT_TYPE_ZIP = 7;

  FILE_CONTENT_TYPE_PDF = 8;

  FILE_CONTENT_TYPE_DOCX = 10;

  FILE_CONTENT_TYPE_JPG = 11;

  FILE_CONTENT_TYPE_PNG = 12;

  FILE_CONTENT_TYPE_WEBP = 13;
}
