syntax = "proto3";

package cx.risk_ops;

import "api/auth/liveness/internal/face_match_annotation.proto";
import "api/auth/liveness/internal/liveness_attempt.proto";
import "api/connected_account/external/external.proto";
import "api/cx/developer/actions/action_response.proto";
import "api/cx/developer/db_state/db_state.proto";
import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/cx/risk_ops/actor_activity.proto";
import "api/cx/risk_ops/queue_element.proto";
import "api/cx/risk_ops/transaction_review.proto";
import "api/cx/risk_ops/user_risk_review.proto";
import "api/persistentqueue/queue_element.proto";
import "api/risk/case_management/combined_annotation.proto";
import "api/risk/case_management/form/form.proto";
import "api/risk/case_management/review/allowed_annotation.proto";
import "api/risk/case_management/review/annotation.proto";
import "api/risk/case_management/review/case.proto";
import "api/risk/case_management/review/comment.proto";
import "api/risk/case_management/review/enums.proto";
import "api/risk/case_management/review/ui_element.proto";
import "api/risk/case_management/rule.proto";
import "api/risk/internal/data_source.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/file/file.proto";
import "api/typesv2/webui/table.proto";
import "api/user/change_feed.proto";
import "api/user/user.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/risk_ops";
option java_package = "com.github.epifi.gamma.api.cx.risk_ops";

service RiskOps {
  // RPC to fetch all records from onboarding side related face match and liveness
  // Request accepts payload type (face match or liveness) and record limit
  // Response contains status message and record list
  // Record contains attribute like actor id, request id, video location, kyc image etc
  // If RPC execution is successful the Status will be OK with records populated
  // If Payload type is not passed then Invalid Argument
  // Status will be Internal Server Error if execution fails due to some issues
  // Status from error if call to equivalent onboarding RPC fails
  rpc GetLivenessAndFacematchQueue (GetLivenessAndFacematchQueueRequest) returns (GetLivenessAndFacematchQueueResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // RPC to delete RiskOps data (face match/liveness) whose action/review has been completed
  // Request accepts record id, actor id and payload type
  // Response contains status message
  // Status will be invalid arg if any of the request parameters are missing
  // Status will be ISE if RPC execution fails
  // Status from error if call to equivalent onboarding RPC fails
  rpc DeleteLivenessAndFacematchQueueElement (DeleteLivenessAndFacematchQueueRequest) returns (DeleteLivenessAndFacematchQueueResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // RPC to fetch count of pending review cases
  // Request accepts payload type and cx header, both are mandatory parameters
  // Response contains status message and required count
  // If RPC execution is successful the Status will be OK with count field populated
  // If Payload type is not passed then Invalid Argument
  // Status will be Internal Server Error if execution fails due to some issues
  rpc GetPendingReviewCount (GetPendingReviewCountRequest) returns (GetPendingReviewCountResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // RPC to log any media download requests(images and videos)
  // Request accepts cx header and parameters to log
  // Response contains status message
  rpc AuditLogMediaDownload (AuditLogMediaDownloadRequest) returns (AuditLogMediaDownloadResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // Rpc to fetch most of user detail
  // Request accept cx header and parameters
  // Response contain status message and needed details
  rpc GetUserInfo (GetUserInfoRequest) returns (GetUserInfoResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // Rpc to fetch Annotataion details for QA QueueLiveness
  // Request accept cx header and parameters
  // Response contain status message and needed details
  rpc GetL2Details (GetL2DetailsRequest) returns (GetL2DetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to fetch review elements like facematch, liveness, etc at actor id level
  // request accepts payload type and actor id
  // response contains list of review elements and rpc status
  // OK if found
  // NotFound if data is not found
  // InvalidArg if mandatory params are incomplete
  // Internal for any other errors
  rpc GetReviewElementsByActorId (GetReviewElementsByActorIdRequest) returns (GetReviewElementsByActorIdResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Rpc to re review facematch and liveness
  // Request accept cx header and parameters
  // Response contain status message
  rpc ReReview (ReReviewRequest) returns (ReReviewResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc GetUserRiskData (GetUserRiskDataRequest) returns (GetUserRiskDataResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetTransactionBlocks returns transaction blocks for a given actor
  rpc GetTransactionBlocks (GetTransactionBlocksRequest) returns (GetTransactionBlocksResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Rpc to get the transactions associated with the given identifier in table format.
  // First response returns the latest transactions (sorted by updated_at field in descending order).
  // If sort_by enum is passed, the entries are sorted in ascending order of the field associated with the enum.
  rpc GetTransactionsTable (GetTransactionsTableRequest) returns (GetTransactionsTableResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetTransactionFilters returns the list of parameters available for applying filters for GetTransactionsTable rpc
  // This rpc is added to make the filters on UI dynamic
  rpc GetTransactionFilters (GetTransactionFiltersRequest) returns (GetTransactionFiltersResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // ListAssignedCases returns the list of cases assigned to the currently logged in analyst
  // Bases on the filters passed in request it will return the case list in webui table format
  // this rpc supports filters for priority and status fields (for ref: risk/case_management/review/case.proto)
  rpc ListAssignedCases (ListAssignedCasesRequest) returns (ListAssignedCasesResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetListAssignedCasesFilters returns the list of parameters available for applying filters for ListAssignedCases rpc
  // This rpc is added to make the filters on UI dynamic
  rpc GetCaseFilters (GetCaseFiltersRequest) returns (GetCaseFiltersResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc GetCaseDetails (GetCaseDetailsRequest) returns (GetCaseDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc CreateAnnotation (CreateAnnotationRequest) returns (CreateAnnotationResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  // rpc to get configured allowed annotations
  // maximum of 100 comments will return from this rpc
  // Deprecated in favour of ListAllowedAnnotations
  rpc GetAllowedAnnotations (GetAllowedAnnotationsRequest) returns (GetAllowedAnnotationsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
    option deprecated = true;
  };

  // rpc to create allowed annotation entry in db
  rpc CreateAllowedAnnotation (CreateAllowedAnnotationRequest) returns (CreateAllowedAnnotationResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  // rpc to annotations against the request
  // maximum of 100 comments will return from this rpc
  rpc ListAnnotations (ListAnnotationsRequest) returns (ListAnnotationsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  // rpc to comments against the request
  // maximum of 100 comments will return from this rpc
  rpc ListComments (ListCommentsRequest) returns (ListCommentsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  // DownloadFile is used to download any content from sherlock,here we are mapping the path given by the user to our
  //  internal system and sending response in a way that sherlock returns a file from browser instead of html
  rpc DownloadFile (DownloadFileRequest) returns (DownloadFileResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";

  }

  // rpc to create comments entry in db
  rpc CreateComment (CreateCommentRequest) returns (CreateCommentResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  // GetAvailableReviewActions returns permitted actions for given input entity (case id)
  rpc GetAvailableReviewActions (GetAvailableReviewActionsRequest) returns (GetAvailableReviewActionsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetReviewActionFormElements returns form elements of input review action.
  // It is used to fetch action parameters. eg. for freeze action - freeze level, remarks,
  // request reason etc. are form elements
  rpc GetReviewActionFormElements (GetReviewActionFormElementsRequest) returns (GetReviewActionFormElementsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // PerformReviewAction performs input action. Throws error if
  // all mandatory action parameters are not present
  rpc PerformReviewAction (PerformReviewActionRequest) returns (PerformReviewActionResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc UpdateRule (UpdateRuleRequest) returns (UpdateRuleResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to create the rule if not present for external id
  // it only creates the rule if external id is present
  rpc CreateRule (CreateRuleRequest) returns (CreateRuleResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetAFUData returns user's afu attempts history and additional details.
  // List of afu data sections in result:
  //  * Last 10 afu attempts of the user.
  //  * Onboarding details e.g. onboarding location, auth factors.
  rpc GetAFUData (GetAFUDataRequest) returns (GetAFUDataResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // ListRules returns list of rules present in the db
  // Filter is applied based on the filter_by field mask
  // if no field mask is passed, all the rules are returned
  // Corresponding filter_by_value needs to be passed with filter_by field else invalid argument will be returned
  rpc ListRules (ListRulesRequest) returns (ListRulesResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetPrioritizedCase returns the highest priority case of input review type.
  // Returns record not found if no cases are found.
  // Returns Invalid argument if analyst is not part of the group that reviews the input review type.
  rpc GetPrioritizedCase (GetPrioritizedCaseRequest) returns (GetPrioritizedCaseResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetRelatedCases returns all cases against the actor. It accepts actor id or case id as input.
  // Returns Record not found if cases are not found.
  rpc GetRelatedCases (GetRelatedCasesRequest) returns (GetRelatedCasesResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetTransactionReviewDetails returns the transaction information of the actor for risk ops review.
  // Returns Record not found if cases are not found.
  // we are using the server side streaming in this rpc, as there are many transactions a actor can have a sending
  // all the transactions in one go can tests the limits. hence sending it in the small batches of data.
  // this will also use in sending the insights where from all the transactions data we can send some aggregates etc
  // in the different objects in the response object.
  //
  // Client is expected to read the stream on a greedy basis i.e.,
  // i. few streams may have only transactions
  // ii. few may have only insights and so on
  // iii. few may have mix of both
  rpc GetTransactionReviewDetails (GetTransactionReviewDetailsRequest) returns
  (stream GetTransactionReviewDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Rpc to be used for getting screener check details for a particular screener check
  // details returned can vary across different checks, EX: for vpa name match check might return all vpa names vs for location check failure might return pin code etc.
  // this is needed to analyse why a particular check has failed and can be useful during manual review
  rpc GetScreenerCheckDetails (GetScreenerCheckDetailsRequest) returns (GetScreenerCheckDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetUserProducts returns all products offered by Fi that the user has registered for.
  // Returns empty list of none found.
  rpc GetUserProducts (GetUserProductsRequest) returns (GetUserProductsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetProductUserInfo returns user info for a product.
  // Fails with record not found if user has not registered for the product.
  rpc GetProductUserInfo (GetProductUserInfoRequest) returns (GetProductUserInfoResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // ListForms returns forms filtered for given filters.
  // Response also includes not submitted forms as well.
  rpc ListForms (ListFormsRequest) returns (ListFormsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  // GetAllTags will fetch all available rule tags
  rpc GetAllTags (GetAllTagsRequest) returns (GetAllTagsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  // GetActorActivityAreas message defines the options to fetch
  // available areas for actor activities.
  // This will return all available areas.
  // depending on the requirement area can be "ALL"
  rpc GetActorActivityAreas (risk_ops.GetActorActivityAreasRequest) returns (risk_ops.GetActorActivityAreasResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetActivities returns list of activities in descending order, latest entry first
  // pre-defined areas are provided as request here
  // in case the areas is empty, will return invalid argument
  // will return RNF if no activities found
  rpc GetActorActivities (risk_ops.GetActorActivitiesRequest) returns (risk_ops.GetActorActivitiesResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // CreateAnnotations creates bulk annotations.
  rpc CreateAnnotations (CreateAnnotationsRequest) returns (CreateAnnotationsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  // Rpc to fetch allowed annotations with given filters.
  rpc ListAllowedAnnotations (ListAllowedAnnotationsRequest) returns (ListAllowedAnnotationsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  // RPC to upload manually upload redacted documents by ops
  rpc UploadManuallyReviewedDocuments (UploadManuallyReviewedDocumentsRequest) returns (UploadManuallyReviewedDocumentsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  rpc GetDataForCrossValidationManualReview (GetDataForCrossValidationManualReviewRequest) returns (GetDataForCrossValidationManualReviewResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message GetDataForCrossValidationManualReviewRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  string actor_id = 2;
}

message GetDataForCrossValidationManualReviewResponse {
  rpc.Status status = 1;
  // All data except images will be shared in a JSON string for now
  string cross_validation_data = 2;
  // key maps to source of image, eg: PASSPORT, EMIRATES_ID, LIVENESS, etc.
  map<string, string > base64_images = 3;
}

message ListAllowedAnnotationsRequest {
  // Header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  risk.case_management.review.AllowedAnnotationFilters filters = 2 [(validate.rules).message.required = true];
}

message ListAllowedAnnotationsResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // No annotations found with given filters
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  oneof response {
    // For filter: UI Element. List of annotation types mapped against ui element with annotation values.
    risk.case_management.review.UIElementAllowedAnnotations ui_element_allowed_annotations = 2;
  }
}

message GetAllTagsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetAllTagsResponse {
  enum Status {
    Ok = 0;
    // Tags not found
    NOT_FOUND = 5;
    // ISE resposne due to some internal error in the rpc
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  repeated string tags = 2;
}

message GetUserProductsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  string case_id = 2 [(validate.rules).string.min_len = 1];
}

message GetUserProductsResponse {
  enum Status {
    OK = 0;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }

  rpc.Status status = 1;

  repeated string products = 2;
}

message GetProductUserInfoRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  string case_id = 2 [(validate.rules).string.min_len = 1];

  string product = 3 [(validate.rules).string.min_len = 1];
}

message GetProductUserInfoResponse {
  enum Status {
    OK = 0;
    // If user has not signed up for the product
    NOT_FOUND = 5;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }

  rpc.Status status = 1;

  repeated ReviewSection review_sections = 2;
}

message GetRelatedCasesRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  oneof identifier {
    string case_id = 2 [(validate.rules).string.min_len = 1];

    string actor_id = 3 [(validate.rules).string.min_len = 1];
  }

  rpc.PageContextRequest page_context_request = 4 [(validate.rules).message.required = true];
}

message GetRelatedCasesResponse {
  enum Status {
    OK = 0;
    // Case not found for the analyst
    NOT_FOUND = 5;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;

  // data for this rpc is sent in a table format which can be rendered on the client
  api.typesv2.webui.Table data = 2;

  rpc.PageContextResponse page_context_response = 3;
}

message GetPrioritizedCaseRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  // deprecated, use review_types going forward
  risk.case_management.review.ReviewType review_type = 2 [deprecated = true];

  repeated risk.case_management.review.ReviewType review_types = 3;
}

message GetPrioritizedCaseResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // Case not found for the analyst
    NOT_FOUND = 5;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;

  risk.case_management.review.Case case = 2;
}

message ListRulesRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  oneof filter_by_value {
    string external_id = 2;
    string rule_name = 3;
    risk.case_management.RuleGroup rule_group = 4;
  }

  // accepted rule field masks:
  // RULE_FIELD_MASK_EXTERNAL_ID
  // RULE_FIELD_MASK_NAME
  // RULE_FIELD_MASK_RULE_GROUP
  risk.case_management.RuleFieldMask filter_by = 5;
}

message ListRulesResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 2;
    // ISE response due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 3;
  }
  rpc.Status status = 1;

  repeated risk.case_management.Rule rules = 2;
}

message CreateRuleRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  // rule entity to be created in the db
  risk.case_management.Rule rule = 2;
}

message CreateRuleResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 2;
    // ISE response due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 3;
  }
  rpc.Status status = 1;
}

message GetAvailableReviewActionsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // case id against which permitted actions need to be fetched
  string case_id = 2 [(validate.rules).string.min_len = 1];
}

message GetAvailableReviewActionsResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // Actions not found for given input entity
    NOT_FOUND = 5;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;
  // list of permitted actions
  repeated string actions = 2;
}

message GetReviewActionFormElementsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // action against which form elements need to be fetched
  string review_action = 2 [(validate.rules).string.min_len = 1];
  // case id against which form elements needs to be fetched
  string case_id = 3 [(validate.rules).string.min_len = 1];
}

message GetReviewActionFormElementsResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // Form elements not found for given input entity
    NOT_FOUND = 5;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;
  // form elements of input action
  repeated cx.developer.db_state.ParameterMeta form_elements = 2 [deprecated = true];

  // Contains conditional form fields for multi choice type fields.
  repeated FormField form_fields = 3;
}

message PerformReviewActionRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // case id against which action needs to be executed
  string case_id = 2 [(validate.rules).string.min_len = 1];
  // action that needs to be executed
  string review_action = 3 [(validate.rules).string.min_len = 1];
  // parameters to execute input action
  repeated cx.developer.db_state.Filter action_parameters = 4;
}

message PerformReviewActionResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }

  rpc.Status status = 1;

  repeated cx.developer.actions.ActionResponse action_responses = 2;
}

message DownloadFileRequest {
  cx.Header header = 1;
  // file path which is used to identify the s3 file path which gets downloaded
  string path = 2;
}

message DownloadFileResponse {
  rpc.Status status = 1;
  // file content encoded in base64 format which will be downloaded
  string file_data = 2;
  // content disposition decide how browser handles the http response, whether to render on ui or to download the file
  string content_disposition = 3;
  // content type of the doucment which we are sending
  string content_type = 4;
  // name of the file post download in users download location
  string downloaded_file_name = 5;
}

message CreateCommentRequest {
  // Header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
  // comment that needs to be stored
  risk.case_management.review.Comment comment = 2 [(validate.rules).message.required = true];
}

message CreateCommentResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;
}

message CreateAllowedAnnotationRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  // annotation that needs to be stored
  risk.case_management.review.AllowedAnnotation allowed_annotation = 2;
}

message CreateAllowedAnnotationResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;
}

message GetAllowedAnnotationsRequest {
  // Header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  risk.case_management.review.ReviewEntityType entity_type = 2;

  risk.case_management.review.AnnotationType annotation_type = 3;
}

message GetAllowedAnnotationsResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // No cases found with given filters
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  repeated risk.case_management.review.AllowedAnnotation allowed_annotations = 2;
}

message ListAnnotationsRequest {
  // Header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  risk.case_management.AnnotationQuery query = 2 [deprecated = true];

  risk.case_management.AnnotationFilters filters = 3;
}

message ListAnnotationsResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // No cases found with given filters
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  repeated risk.case_management.CombinedAnnotation annotations = 2;
}

message ListCommentsRequest {
  // Header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  risk.case_management.review.CommentQuery query = 2 [deprecated = true];

  risk.case_management.review.CommentFilters filters = 3;
}

message ListCommentsResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // No cases found with given filters
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  repeated risk.case_management.review.Comment comments = 2;
}

message CreateAnnotationRequest {
  // Header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // case id against which the annotation has been added
  string case_id = 2;

  // against which entity the input should get created
  risk.case_management.review.ReviewEntityType entity_type = 3 [(validate.rules).enum = {not_in: [0]}];

  // entity id against which the annotation is created
  string entity_id = 4 [(validate.rules).string.min_len = 1];

  // allowed annotation id for the value and type
  string allowed_annotation_id = 5 [(validate.rules).string.min_len = 1];

  // added_by_email for the audit purpose
  string added_by_email = 6 [(validate.rules).string.min_len = 1];
}

message CreateAnnotationResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message CreateAnnotationsRequest {
  // Header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  repeated risk.case_management.review.Annotation annotations = 2 [(validate.rules).repeated.min_items = 1];
}

message CreateAnnotationsResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message GetCaseDetailsRequest {
  cx.Header header = 1;
  string case_id = 2;
}

message GetCaseDetailsResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // if request validation fails
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;

  // info related to all the alerts corresponding the current case in table format
  api.typesv2.webui.Table alerts = 2;
  // info related to all the cases related to the current case in table format
  api.typesv2.webui.Table related_cases = 3;
  // info related to all the actions related to the current case in table format
  api.typesv2.webui.Table past_actions = 4;

  risk.case_management.review.Case case = 5;

  // firehose id of the actor corresponding to the case
  // will be only populated for transaction review type
  string firehose_id = 6;
  // info related to escalations, current and past tickets with hyperlinks
  api.typesv2.webui.Table escalation_tickets = 7;
}

message GetCaseFiltersRequest {
  cx.Header header = 1;
}

message GetCaseFiltersResponse {
  rpc.Status status = 1;

  // list of parameters available for filtering
  repeated developer.db_state.ParameterMeta filters = 2;
}

message ListAssignedCasesRequest {
  cx.Header header = 1;
  // case status and priority filters are supported
  repeated cx.developer.db_state.Filter filters = 2;
  // max supported page size is 100
  rpc.PageContextRequest page_context = 3;
}

message ListAssignedCasesResponse {
  enum Status {
    OK = 0;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 1;
    // Record not found for the given identifier
    RECORD_NOT_FOUND = 2;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;

  // data for this rpc is sent in a table format which can be rendered on the client
  api.typesv2.webui.Table data = 2;

  rpc.PageContextResponse page_context = 5;
}

message GetTransactionFiltersRequest {
  cx.Header header = 1;

  // case id for which parameter list is being fetched
  string case_id = 2;
}

message GetTransactionFiltersResponse {
  rpc.Status status = 1;

  // list of parameters available for filtering
  repeated developer.db_state.ParameterMeta filters = 2;
}

message GetTransactionsTableRequest {
  cx.Header header = 1;

  // Identifier field is mandatory, will return InvalidArgument if none of the identifiers are passed
  oneof identifier {
    string actor_id = 2;
    string case_id = 3;
  }

  // field by which transactions needs to be sorted
  // by default the transactions are sorted by updated_at field and in descending order.
  // supported fields:
  // TRANSACTION_TABLE_FIELD_MASK_ACTOR_FROM
  // TRANSACTION_TABLE_FIELD_MASK_ACTOR_TO
  // TRANSACTION_TABLE_FIELD_MASK_AMOUNT
  // TRANSACTION_TABLE_FIELD_MASK_ORDER_WORKFLOW
  // TRANSACTION_TABLE_FIELD_MASK_STATUS
  // TRANSACTION_TABLE_FIELD_MASK_CREDIT_DEBIT
  // TRANSACTION_TABLE_FIELD_MASK_TIMESTAMP
  // TRANSACTION_TABLE_FIELD_MASK_PROVENANCE
  TransactionTableFieldMask sort_by = 4 [(validate.rules).enum = {in: [0, 2, 3, 6, 8, 10, 17, 18, 19]}];

  // set of filters to be applied in addition to actor_id while fetching the transactions.
  repeated cx.developer.db_state.Filter filters = 5;

  rpc.PageContextRequest page_context = 6;
}

message GetTransactionsTableResponse {
  enum Status {
    OK = 0;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 1;
    // Record not found for the given identifier
    RECORD_NOT_FOUND = 2;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;

  // data for this rpc is sent in a table format which can be rendered on the client
  api.typesv2.webui.Table data = 2;

  rpc.PageContextResponse page_context = 5;
}

message QueueElement {
  // element id
  string id = 1;

  // payload information
  oneof payload {
    LivenessReview liveness_review = 2;
    FacematchReview facematch_review = 3;
    LivenessSampleReview liveness_sample_review = 4;
    AFULivenessReview afu_liveness_review = 5;
    AFUFacematchReview afu_facematch_review = 6;
    NamematchReview namematch_review = 7;
    FacematchSampleReview facematch_sample_review = 8;
    PanNameSampleReview pan_name_sample_review = 9;
    LivenessAndFacematchReview liveness_and_facematch_review = 10;
    AFULivenessAndFacematchReview afu_liveness_facematch_review = 11;
    UserReview user_review = 12;
    persistentqueue.StockGuardianCkycDocuments stock_guardian_ckyc_documents = 13;
  }
}

message GetLivenessAndFacematchQueueRequest {
  // header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // type of payload required from the queue
  PayloadType payload_type = 2;

  // Number of elements required from the queue, max supported is 100
  int32 limit = 3;

  // pagination bits to reflect onboarding request proto
  int32 page_num = 4;

  // From time for date filter
  google.protobuf.Timestamp from_time = 5;

  // To time for date filter
  google.protobuf.Timestamp to_time = 6;

  // By default elements in response are in LIFO order
  // is_fifo=true will return elements in FIFO order
  bool is_fifo = 7;
}

message GetLivenessAndFacematchQueueResponse {
  // status code
  rpc.Status status = 1;

  // list of risk ops record
  repeated QueueElement elements = 2;
}

message DeleteLivenessAndFacematchQueueRequest {
  // header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // record id
  string id = 2 [(validate.rules).string.min_len = 1];

  // actor id
  string actor_id = 3 [(validate.rules).string.min_len = 1];

  // payload type (face match / liveness)
  PayloadType payload_type = 4;
}

message DeleteLivenessAndFacematchQueueResponse {
  // status code
  rpc.Status status = 1;
}

message GetPendingReviewCountRequest {
  // header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // type of payload required from the queue
  PayloadType payload_type = 2;
}

message GetPendingReviewCountResponse {
  // status code
  rpc.Status status = 1;

  // count of pending records of payload type in request
  int64 count = 2;
}

message AuditLogMediaDownloadRequest {
  // header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // id of persistent queue element
  string element_id = 2 [(validate.rules).string.min_len = 1];

  // actor id
  string actor_id = 3 [(validate.rules).string.min_len = 1];

  // agent name
  string agent_name = 4 [(validate.rules).string.min_len = 1];

  // video location for liveness
  string video_location = 5;

  // video frame for facematch
  string video_frame_location = 6;

  // kyc image for facematch
  string kyc_image_location = 7;

  // payload type
  PayloadType payload_type = 8;
}

message AuditLogMediaDownloadResponse {
  // status code
  rpc.Status status = 1;
}

message GetUserInfoRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  string actor_id = 2 [(validate.rules).string.min_len = 1];
}

message GetUserInfoResponse {
  rpc.Status status = 1;
  BasicDetails basic_details = 2;
  UserEnteredDetails user_entered_details = 3;
  EmploymentDetail employment_detail = 4;
  NameMatchDetail name_match_detail = 5;
  LivenessFacematchDetail liveness_facematch_detail = 6;
  RiskSummary risk_summary = 7;
  DeviceDetails device_details = 8;
  AccountStatus account_status = 9;
  ScreenerCheckDetails screener_check_details = 10;
}

message UpdateRuleRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  risk.case_management.Rule rule = 2;
  repeated risk.case_management.RuleFieldMask update_masks = 3;
}

message UpdateRuleResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message ScreenerCheckDetails {
  repeated string passing_stages = 1;
  string screener_attempt_result = 2;
  repeated string individual_check_results = 3;
}

message BasicDetails {
  string review_reason = 1;
  double days_since_account_creation = 2;
  string account_creation_date = 3;
  string current_onboarding_stage = 4;
  string signup_email = 5;
  string signup_email_name = 6;
  BankDetails bank_details = 7;
}

message BankDetails {
  bool is_etb_customer = 1;
  string kyc_path = 2;
  string kyc_level = 3;
  string kyc_dob = 4;
  repeated string kyc_permanent_address = 5;
}

message UserEnteredDetails {
  int32 retries_of_dob = 1;
  string mother_name = 2;
  string father_name = 3;
  string shipping_address = 4;
  string communication_address = 5;
}

message EmploymentDetail {
  string company_name = 1;
  string employment_type = 2;
  string income_range = 3;
  float absolute_income = 4;
  string gmail_insight_criteria_for_passing = 5;
}

message NameMatchDetail {
  string pan_name = 1;
  string kyc_name = 2;
  float email_name_pan_name_match_score = 3;
  float kyc_name_pan_name_match_score = 4;
}

message LivenessFacematchDetail {
  string onboarding_liveness_image = 1;
  string kyc_image = 2;
}
message RiskSummary {
  float risk_score = 1 [deprecated = true];
  bool aadhar_phone_onb_matches = 2;
  repeated RiskData risk_data = 3;
}

message RiskData {
  risk.RiskParam risk_param = 1;
  risk.Result result = 2;
  float score = 3;
}

message DeviceDetails {
  string device_model_and_manufacturer = 1;
  string device_language = 2;
  string latLng = 3;
  string location_pincode = 4;
  string location_city = 5;
  string location_state = 6;
  string malicious_app_list = 7;
}

message AccountStatus {
  InAppStatus in_app_status = 1;
  repeated user.AccessRevokeDetailsChange access_revoke_details_history = 2;
}

message InAppStatus {
  string access_revoke_status = 1;
  string reason = 2;
  string remarks = 3;
}

message GetL2DetailsRequest {
  // header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // id of persistent queue element
  string request_id = 2 [deprecated = true];

  // actor id
  string actor_id = 3 [(validate.rules).string.min_len = 1];

  // payload type
  PayloadType payload_type = 4;

  // liveness request id
  string liveness_request_id = 5;

  // facematch request id
  string facematch_request_id = 6;
}

message GetL2DetailsResponse {
  rpc.Status status = 1;

  // AccessRevokeDetails
  user.AccessRevokeDetails access_revoke_details = 2;

  // liveness annotation
  auth.liveness.Annotation liveness_annotation = 3;

  // facematch annotation
  auth.liveness.FaceMatchAnnotation facematch_annotation = 4;
}

message ConnectedAccountInfo {
  connected_account.external.AccountDetails account_details = 1;
  connected_account.external.ProfileDetails profile_details = 2;
}

message ReviewElement {
  // payload information
  oneof payload {
    LivenessReview liveness_review = 1;
  }
}

message GetReviewElementsByActorIdRequest {
  // header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // type of payload required from the queue
  PayloadType payload_type = 2;

  // Number of elements to be fetched
  int32 limit = 3;

  // actor_id for which data has to be fetched
  string actor_id = 4;
}

message GetReviewElementsByActorIdResponse {
  // status code
  rpc.Status status = 1;

  // list of risk ops record
  repeated ReviewElement review_elements = 2;
}

message ReReviewRequest {
  // header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // id of persistent queue element
  string element_id = 2 [(validate.rules).string.min_len = 1];

  // actor id
  string actor_id = 3 [(validate.rules).string.min_len = 1];

  // liveness request id
  string liveness_request_id = 4;

  // facematch request id
  string facematch_request_id = 5;

  // rereview liveness details
  auth.liveness.Verdict re_review_liveness_verdict = 6;

  // rereview facematch details
  auth.liveness.FaceMatchVerdict re_review_facematch_verdict = 7;

  // annotation is re-reviewed by
  string re_reviewed_by = 8;

  // comments by re-reviewer
  string re_review_remarks = 9;

  // payload type is required delete L2 element from queue
  PayloadType payload_type = 10;

  // type of error made by analyst
  auth.liveness.ReReviewErrorType re_review_error_type = 11 [deprecated = true];

  repeated auth.liveness.ReReviewErrorType re_review_error_types = 12;

  repeated auth.liveness.ReReviewErrorSubcategory re_review_error_subcategories = 13;
}

message ReReviewResponse {
  rpc.Status status = 1;
}

message GetUserRiskDataRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // actor_id field will be completely deprecated once user review is migrated to case management
  string actor_id = 2 [deprecated = true];
  repeated persistentqueue.ReviewReason review_reasons = 3;
  string case_id = 4;
}

message GetUserRiskDataResponse {
  rpc.Status status = 1;
  ReviewSection basic_details = 2;
  ReviewSection risk_summary = 3 [deprecated = true];
  ReviewSection KYC_details = 4;
  ReviewSection liveness_details = 5;
  ReviewSection facematch_details = 6;
  ReviewSection review_reason = 7 [deprecated = true];
  ReviewSection device_details = 8;
  ReviewSection employment_details = 9;
  ReviewSection address_details = 10;
  ReviewSection risk_action_history = 11;
  ReviewSection liveness_attempts_history = 12 [deprecated = true];
  ReviewSection facematch_attempts_history = 13 [deprecated = true];
  ReviewSection outcall_info = 14;
  ReviewSection existing_product = 15;
  ReviewSection risk_screener_checks = 16;
  ReviewSection social_media_details = 17;
  ReviewSection contact_associations = 18;
  ReviewSection referral_associations = 19;
  ReviewSection installed_apps_details = 20;
  ReviewSection watchlist_details = 21;
  ReviewSection lea_history = 22;
}

message GetAFUDataRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  oneof identifier {
    string case_id = 2 [(validate.rules).string.min_len = 1];

    string actor_id = 3 [(validate.rules).string.min_len = 1];
  }
}

message GetAFUDataResponse {
  enum Status {
    OK = 0;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 1;
    // Record not found for afu attempts not found
    RECORD_NOT_FOUND = 2;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
  }

  rpc.Status status = 1;

  repeated ReviewSection review_sections = 2;
}

message GetTransactionReviewDetailsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  string actor_id = 2 [(validate.rules).string.min_len = 1];

  TransactionFilters filters = 3 [(validate.rules).message.required = true];

  // Case id for which transaction review details are requested
  string case_id = 4;
}

message GetTransactionReviewDetailsResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // Record not found for afu attempts not found
    NOT_FOUND = 5;
    // ISE resposne due to some internal error in the rpc
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  repeated RiskReviewTransaction transactions = 2;
}

enum UserRiskDataFieldMask {
  USER_RISK_DATA_FIELD_MASK_UNSPECIFIED = 0;
  USER_RISK_DATA_FIELD_MASK_BASIC_DETAILS = 1;
  USER_RISK_DATA_FIELD_MASK_RISK_SUMMARY = 2;
  USER_RISK_DATA_FIELD_MASK_KYC_DETAILS = 3;
  USER_RISK_DATA_FIELD_MASK_LIVENESS_DETAILS = 4;
  USER_RISK_DATA_FIELD_MASK_FACEMATCH_DETAILS = 5;
  USER_RISK_DATA_FIELD_MASK_DEVICE_DETAILS = 6;
  USER_RISK_DATA_FIELD_MASK_EMPLOYMENT_DETAILS = 7;
  USER_RISK_DATA_FIELD_MASK_ADDRESS_DETAILS = 8;
  USER_RISK_DATA_FIELD_MASK_RISK_ACTION_HISTORY = 9;
  USER_RISK_DATA_FIELD_MASK_LIVENESS_ATTEMPTS_HISTORY = 10;
  USER_RISK_DATA_FIELD_MASK_FACEMATCH_ATTEMPTS_HISTORY = 11;
  USER_RISK_DATA_FIELD_MASK_OUTCALL_INFO = 12;
  USER_RISK_DATA_FIELD_MASK_EXISTING_PRODUCT = 13;
  USER_RISK_DATA_FIELD_MASK_RISK_SCREENER_CHECKS = 14;
  USER_RISK_DATA_FIELD_MASK_SOCIAL_MEDIA_DETAILS = 15;
  USER_RISK_DATA_FIELD_MASK_CONTACT_ASSOCIATIONS = 16;
  USER_RISK_DATA_FIELD_MASK_REFERRAL_ASSOCIATIONS = 17;
  USER_RISK_DATA_FIELD_MASK_INSTALLED_APPS_DETAILS = 18;
  USER_RISK_DATA_FIELD_MASK_WATCHLIST_DETAILS = 19;
  USER_RISK_DATA_FIELD_MASK_LEA_HISTORY = 20;
}

message GetScreenerCheckDetailsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  // Id of the risk data row which stores a check result
  string result_id = 2;
}

message GetScreenerCheckDetailsResponse {
  enum Status {
    OK = 0;
    // request validation failed
    INVALID_ARGUMENT = 3;
    // no check result found
    RECORD_NOT_FOUND = 5;
    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // Additional details related to the checks for analysis
  // this can contain check specific information like certain model response fields
  // or raw details used for evaluating the given check etc.
  ReviewSection additional_check_details = 2;
}

message ListFormsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  risk.case_management.form.FormFilters form_filters = 2 [(validate.rules).message.required = true];
}

message ListFormsResponse {
  enum Status {
    OK = 0;
    // request validation failed
    INVALID_ARGUMENT = 3;
    // form not found with given filters
    RECORD_NOT_FOUND = 5;
    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  repeated FormElement forms = 2;
}

message UploadManuallyReviewedDocumentsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  string application_id = 2;
  string ckyc_reference_id = 3;
  message FileData {
    api.typesv2.common.file.File file = 1;
    api.typesv2.common.BooleanEnum is_redacted = 2;
  }
  repeated FileData files = 4;
}

message UploadManuallyReviewedDocumentsResponse {
  rpc.Status status = 1;
}

enum TransactionBlockType {
  // Default value
  TRANSACTION_BLOCK_TYPE_UNSPECIFIED = 0;

  // FIFO transaction block type
  TRANSACTION_BLOCK_TYPE_FIFO = 1;
}

// TransactionFIFOBlock represents a group of transactions that are related in time order
message TransactionBlock {
  // Unique identifier for the transaction block
  string id = 1;

  // Actor ID associated with this transaction block
  string actor_id = 2;

  // Alert ID that this transaction block is associated with (optional)
  string alert_id = 3;

  // Total credit amount for all transactions in this block
  double aggregated_credit = 4;

  // Total debit amount for all transactions in this block
  double aggregated_debit = 5;

  // List of transaction IDs included in this block
  repeated string transaction_ids = 6;

  // Time duration for this transaction block
  google.protobuf.Duration duration = 7;

  // Type of block (e.g., "FIFO")
  TransactionBlockType block_type = 8;

  // Creation timestamp
  google.protobuf.Timestamp created_at = 9;
}

// GetTransactionBlocksRequest is used to request FIFO transaction blocks for a given actor
message GetTransactionBlocksRequest {
  cx.Header header = 1;

  //  identifier for the request, actor id or alert id
  oneof identifier {
    // Case ID to fetch transaction blocks for (optional)
    string alert_id = 2 [(validate.rules).string.min_len = 1];

    // Actor ID to fetch transaction blocks for (required)
    string actor_id = 3 [(validate.rules).string.min_len = 1];
  }

  int32 limit = 4 [(validate.rules).int32.gte = 0];

  // Block type
  TransactionBlockType block_type = 5;
}

// GetTransactionBlocksResponse contains the FIFO transaction blocks matching the request criteria
message GetTransactionBlocksResponse {
  enum Status {
    // Success
    OK = 0;

    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;

    // No transaction blocks found with given filters
    RECORD_NOT_FOUND = 5;

    // System faced internal errors while processing the request
    INTERNAL = 13;
  }

  // Response status
  rpc.Status status = 1;

  // List of transaction FIFO blocks
  repeated TransactionBlock transaction_blocks = 2;
}
