syntax = "proto3";

package cx.ticket;

import "api/cx/ticket/enums.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/device.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/ticket";
option java_package = "com.github.epifi.gamma.api.cx.ticket";

// Ticket object to be used internally by cx and other services
// We are adding this to decouple from vendor tickets proto object
message Ticket {
  // custom ticket fields.
  CustomFields custom_fields = 1;

  // Set to true if the ticket has been deleted/trashed. Deleted tickets will not be displayed in any views except the "deleted" filter
  bool deleted = 2;

  // HTML content of the ticket
  string description = 3;

  // Content of the ticket in plain text
  string description_text = 4;

  // Timestamp that denotes when the ticket is due to be resolved
  google.protobuf.Timestamp due_by = 5;

  // Email address of the requester
  string email = 6;

  // Timestamp that denotes when the first response is due
  google.protobuf.Timestamp fr_due_by = 7;

  // Set to true if the ticket has been escalated as the result of first response time being breached
  bool fr_escalated = 8;

  // ID of the group to which the ticket has been assigned
  int64 group_id = 9;

  // Unique ID of the ticket
  int64 id = 10;

  // Set to true if the ticket has been escalated for any reason
  bool is_escalated = 11;

  // Priority of the ticket
  Priority priority = 12;

  // User ID of the requester. For existing contacts, the requester_id can be passed instead of the requester's email.
  int64 requester_id = 13;

  // ID of the agent to whom the ticket has been assigned
  int64 responder_id = 14;

  // The channel through which the ticket was created
  Source source = 15;

  // Set to true if the ticket has been marked as spam
  bool spam = 16;

  // Status of the ticket
  Status status = 17;

  // Subject of the ticket
  string subject = 18;

  // Tags that have been associated with the ticket
  repeated string tags = 19;

  Requester requester = 20;

  // Helps categorize the ticket according to the different kinds of issues your support team deals with.
  string type = 21;

  // Ticket creation timestamp
  google.protobuf.Timestamp created_at = 22;

  // Ticket updated timestamp
  google.protobuf.Timestamp updated_at = 23;

  Group group = 24;

  // ticket url
  string url = 25;

  // this field should be used if you just need to access custom field values for display purpose
  CustomFieldsWithValue custom_field_with_value = 26;

  // actor id mapped to that ticket
  string actor_id = 27;

  // id of record in issue_categories table which represents the category of the issue (i.e. L1, L2, and L3)
  string issue_category_id = 28;
  // list of attachments
  repeated Attachment attachments = 29;
  // contact summary details
  ContactSummaryDetails contact_summary_details = 30;
}

// ContactSummaryDetails contains a brief overview of the contact's interaction.
message ContactSummaryDetails {
  // A brief text summarizing the content of the call or interaction for the ticket.
  string call_summary = 1;

  // The timestamp indicating the last time the call summary was updated.
  google.protobuf.Timestamp last_updated_at = 2;
}

// Ticket details required to be shown to the user
message TicketDetailsForUser {
  // Unique ID of the ticket
  int64 id = 1;

  // title or heading of the ticket shown to the user
  string title = 2;

  // a short description of the issue shown to the user
  string description = 3;

  // Status of the ticket
  TicketStatusForUser status = 4;

  // The time at which the ticket was created
  google.protobuf.Timestamp created_time = 5;

  // Expected time by which the ticket would be resolved
  google.protobuf.Timestamp expected_resolution_by = 6;

  // time at which the user had last interacted with Fi care
  google.protobuf.Timestamp last_interaction_time = 7;

  // time at which ticket was closed -- applicable only if the status is closed
  google.protobuf.Timestamp closed_time = 8;

  // Other details as key value map
  // 1. CREATED BY : ticket creation can be initiated by customer, system etc.
  //                  As of now(Aug 2022) all the tickets are created by customer action (call, email, chat etc.).
  // 2. CREATION MODE : Whether created through call, chat, email etc.
  // 3. Auto ID : Whether ticket has 'Auto ID' tag or not
  message MapValue {
    oneof val {
      string str_val = 1;
      api.typesv2.common.BooleanEnum bool_val = 2;
    }
  }
  map<string, MapValue> other_ticket_details = 9;

  // boolean value indicating if the in app CSAT survey will be triggered for the ticket
  // this can be populated based on some particular ticket attribute
  // for example if the ticket has resolved or closed status
  bool is_in_app_csat_survey_required = 10;
}

// this msg contains raw unparsed values of custom fields
// this should be used for purposed where some value has to be displayed
message CustomFieldsWithValue {
  string actual_user_id = 1;
  bool callback_customer = 2;
  string product_category = 3;
  string transaction_type = 4;
  string dispute_status = 5;
  string entity_id = 6;
  string product_category_details = 7;
  string sub_category = 8;
  string os_type = 9;
  int64 app_version = 10;
  google.protobuf.Timestamp transaction_date = 11;
  string transaction_provenance = 12;
  bool validation_status = 13;
  bool reopen_status = 14;
  string response_from_federal_cc = 15;
  string resolution_mode = 16;
  string product_category_meta = 17;
  bool is_all_issue_resolution_feedback_attempt_exhausted = 18;
  bool is_user_clicked_yes_for_dispute_feedback = 19;
  bool is_user_clicked_no_for_dispute_feedback = 20;
  int64 sprinklr_case_number = 21;
  string call_recording_link = 22;
  string expected_resolution_date = 23;
  string ticket_visibility = 24;
  string dispute_id = 25;
  // UTR(unique transaction reference) associated, if any, with the ticket. Eg: For fund transfer in case of Min KYC account closure
  string utr = 26;
  // To indicate whether the UTR filed is updated in the context of Min KYC account closure
  // will be used by ops to send automation mails with UTR
  bool is_utr_updated_for_min_kyc_account_closure = 27;
  // To categorize account based on the balance present
  string savings_account_balance = 28;
  // Federal customer Id of the user
  string customer_id = 29;
  // Whether there is any Monorail ticket ID associated with this Freshdesk ticket
  string monorail_raised = 30;
  // Monorail ticket ID associated with this Freshdesk ticket
  int64 monorail_ticket_id = 31;
  // To indicate whether a call is ongoing for this ticket. This is a Yes/No dropdown field
  // This will be used to skip agent validation on Sherlock during the call
  string is_call_ongoing = 32;
  // boolean to denote whether call summary provided by DS model is attached to ticket
  string is_call_summary_added = 33;
}

message CustomFields {
  // reference id from vendor mapping table to identify the user
  string actual_user_id = 1;
  // this flag will be true if ticket is marked for callback
  bool callback_customer = 2;
  // product category fields to indicate ticket belongs to which particular product
  ProductCategory product_category = 3;
  // transaction type if the ticket is for txn product category
  TransactionType transaction_type = 4;
  // dispute status to indicate whether dispute was accepted or rejected
  DisputeStatus dispute_status = 5;
  // entity id custom field to attach certain entity id based on product category
  string entity_id = 6;
  // field to indicated additional details related to ticket based on product category
  oneof product_category_details {
    ProductCategoryDetailsOnboarding product_category_details_onboarding = 7;
    ProductCategoryDetailsAccounts product_category_details_accounts = 8;
    ProductCategoryDetailsDebitCard product_category_details_debit_card = 9;
    ProductCategoryDetailsTransactions product_category_details_transactions = 10;
    ProductCategoryDetailsWealthMutualFunds product_category_details_wealth_mutual_funds = 11;
    ProductCategoryDetailsSave product_category_details_save = 12;
  }
  // reserving field number from
  // field to indicate os type of user associated to the ticket
  api.typesv2.common.Platform os_type = 20;
  // denotes app version eg: 97, 98, etc
  int64 app_version = 21;
  // date of the transaction
  // this field will be present if the ticket is due to a txn issue
  google.protobuf.Timestamp transaction_date = 22;
  // provenance of the txn associated with the ticket
  string transaction_provenance = 23;
  // identifies if ticket validation has to be skipped for given ticket or not
  bool validation_status = 24;
  // identifies if ticket is being re-opened or not
  bool reopen_status = 25;
  // custom field to indicate using what mode ticket was resolved
  // for example auto-resolution, bulk resolution, manual resolution etc
  ResolutionMode resolution_mode = 26;
  // stores product category meta identifying record from Sherlock which is mark against that ticket
  ProductCategoryMeta product_category_meta = 27;

  oneof sub_category {
    SubCategoryPin sub_category_pin = 28;
    SubCategoryDebitCardActivation sub_category_debit_card_activation = 29;
    SubCategoryDebitCardDelivery sub_category_debit_card_delivery = 30;
    SubCategoryTransactionsDebitedViaFiApp sub_category_transactions_debited_via_fi_app = 31;
    SubCategoryTransactionsDebitedFromFiAccountViaOtherApp sub_category_transactions_debited_from_fi_account_via_other_app = 32;
    SubCategoryTransactionsCardsATM sub_category_transactions_cards_atm = 33;
    SubCategoryTransactionsUPIUnableToTransact sub_category_transactions_upi_unable_to_transact = 34;
    SubCategoryWealthMutualFundsInvestmentTransactionSuccessful sub_category_wealth_mutual_funds_investment_transaction_successful = 35;
    SubCategorySaveFixedDeposit sub_category_save_fixed_deposit = 51;
    SubCategorySaveSmartDeposit sub_category_save_smart_deposit = 52;
  }
  // identifies if all attempts to capture user's feedback on issue resolution was exhausted
  bool is_all_issue_resolution_feedback_attempt_exhausted = 37;
  // this field should be set if user has clicked yes on issue resolution feedback email sent out for dispute case
  bool is_user_clicked_yes_for_dispute_feedback = 38;
  // this field should be set if user has clicked no on issue resolution feedback email sent out for dispute case
  bool is_user_clicked_no_for_dispute_feedback = 39;
  // sprinklr case number as received in webhook event from sprinklr
  int64 sprinklr_case_number = 40;
  // ozonetel call recording link
  string call_recording_link = 41;
  // expected resolution date is determined by using sla config
  google.protobuf.Timestamp expected_resolution_date = 42;
  // this field will determine who will be able to see the ticket: agent, customer or both
  TicketVisibility ticket_visibility = 43;
  // dispute id is internal Fi id to track the dispute raised by client.
  string dispute_id = 44;
  // UTR(unique transaction reference) associated, if any, with the ticket. Eg: For fund transfer in case of Min KYC account closure
  string utr = 45;
  // To indicate whether the UTR filed is updated in the context of Min KYC account closure
  // will be used by ops to send automation mails with UTR
  bool is_utr_updated_for_min_kyc_account_closure = 46;
  // SavingsAccountBalance categorizes savings account based on the balance present
  SavingsAccountBalance savings_account_balance = 47;
  // Federal customer Id of the user
  string customer_id = 48;
  // Whether there is any Monorail ticket ID associated with this Freshdesk ticket
  MonorailRaised monorail_raised = 49;
  // Monorail ticket ID associated with this Freshdesk ticket
  int64 monorail_ticket_id = 50;
  // To indicate whether a call is ongoing for this ticket. This is a Yes/No dropdown field
  // This will be used to skip agent validation on Sherlock during the call
  api.typesv2.common.BooleanEnum is_call_ongoing = 53;
  // boolean to denote whether call summary provided by DS model is attached to ticket
  api.typesv2.common.BooleanEnum is_call_summary_added = 54;
  // stores metadata corresponding to a loan outcall ticket
  LoanOutcallMetadata loan_outcall_metadata = 55;
}

message LoanOutcallMetadata {
  string loan_vendor = 1;
  string loan_program = 2;
  string loan_request_id = 3;
  string disposition1 = 4;
  string disposition2 = 5;
  string disposition3 = 6;
  string disposition4 = 7;
  string drop_off_stage = 8;
  string acquisition_channel = 9;
}

message Requester {
  string email = 1;

  int64 id = 2;

  string name = 3;

  string phone = 4;
}

// message object to be used in ticket dao layer
message TicketDetails {
  // id of ticket
  int64 id = 1;
  // status of ticket
  Status status = 2;
  // source of ticket
  Source source = 3;
  ProductCategory product_category = 4;
  string product_category_details = 5;
  // user identifier type for ticket
  UserIdentifierType identifier_type = 6;
  // identifier value
  string identifier_value = 7;
  // internal cx ticket object
  Ticket ticket = 8;
  // internal requester object
  Requester requester = 9;
  google.protobuf.Timestamp ticket_created_at = 10;
  google.protobuf.Timestamp ticket_updated_at = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  // object to store additional internal details for the ticket
  TicketMeta ticket_meta = 14;
  // support vendor for ticket
  vendorgateway.Vendor vendor = 15;
  // raw ticket details to be stored as JSON, to be used only for read-only purpose
  string raw_ticket = 16;
  // agent group: federal escalation, riskops, etc
  Group agent_group = 17;
  // actor id mapped to that ticket
  string actor_id = 18;
  // expected_resolution_time calculated using sla
  google.protobuf.Timestamp expected_resolution_time = 19;
  // id of record in issue_categories table which represents the category of the issue (i.e. L1, L2, and L3)
  string issue_category_id = 20;
  //contact summary details for the ticket
  ContactSummaryDetails contact_summary_details = 21;
  // id of the ticket responder
  int64 responder_id = 22;
}

message TicketMeta {
  api.typesv2.common.BooleanEnum is_processed_for_onboarding = 1;
  // SurveyDetails contains the details of the CSAT survey triggered for the ticket
  repeated SurveyDetail survey_details = 2;
}

message SurveyDetail {
    // event_id is a unique identifier for the survey event (source : freshdesk event)
  string event_id = 1;
  // Timestamp when the ticket was resolved/ survey got triggered
  google.protobuf.Timestamp created_at = 2;
  // Timestamp when the survey was completed
  google.protobuf.Timestamp completed_at = 3;
  // Status of the survey
  SurveyStatus status = 4;
}

enum SurveyStatus{
  SURVEY_STATUS_UNSPECIFIED = 0;
  // user hasn't started attempting csat survey
  SURVEY_STATUS_WAITING_ON_USER = 1;
  // user has started attempt to fill csat survey
  SURVEY_STATUS_PARTIALLY_SUBMITTED = 2;
  // csat survey is submitted
  SURVEY_STATUS_SUBMITTED = 3;
}

message TicketFilters {
  // list of ticket statuses that needs to be fetched
  // this will be honored if fetch_only_active_tickets is not set to true
  // optional
  repeated Status status_list = 1;
  // list of ticket source
  // optional
  repeated Source source_list = 2;
  // optional
  repeated ProductCategory product_category_list = 3;
  // optional
  // list of product category details that needs to be fetched
  repeated string product_category_details_list = 4;
  // user identifier type for ticket
  // optional
  // deprecated field: use identifier value list instead
  UserIdentifierType identifier_type = 5 [deprecated = true];
  // identifier value
  // optional
  // deprecated field: use identifier value list instead
  string identifier_value = 6 [deprecated = true];
  // from and to time filter on ticket created at field
  // at max 90 days data is allowed fetched
  // so if the diff between from and to time is greater than 90 days will return InvalidArgument error
  // will return only ticket created after from time
  // from time is mandatory
  google.protobuf.Timestamp from_time = 7;
  // will return only ticket created before to time
  // optional, will take current time as default if not passed
  google.protobuf.Timestamp to_time = 8;

  // identifier value list
  // optional
  // identifier values are currently limited to email, phone number and actual user id which can be passed to fetch the tickets
  // associated with them from db
  // identifier type and identifier value filters will not be honoured if this field is populated
  repeated string identifier_value_list = 9;
  // filter on agent groups
  // group: federal escalation, riskops, etc
  repeated Group agent_group_list = 10;
  // will return only tickets which are active after active_after_time
  // it includes all tickets created after this cut off time and also tickets active(i.e. not closed) by this time
  google.protobuf.Timestamp active_after_time = 11;
  // list of actor Ids for which tickets must be fetched
  repeated string actor_id_list = 12;
  // fetch tickets by custom field filter, this is tightly coupled with actor_id_list filter
  // [Important] This filter will only be applied if actor_id_list contains exactly one element
  // (i.e. only when fetching tickets for single actor)
  // all the other filters will be taken into consideration
  // Ref doc: https://docs.google.com/document/d/1DkRU4g6FBnS4-Y05XegbtrPP3Fco-t0mIAYRQ9WULIc/edit
  CustomFieldFilter custom_field_filter = 13;
  // fetch tickets based on IssueCategoryId (id which represents L1, L2, L3 combination)
  // to filter tickets by issue category id either [actor id] or [from and to date] is required
  string issue_category_id = 14;
  // fetch tickets based on responder id i.e agent id
  int64 responder_id = 15;
  // these filters should be used carefully since the column is not indexed. It should only be used when other filters have already reduced the size of the result set from the db
  repeated CustomFieldFilter custom_field_db_filters = 16;
}

// CustomFieldFilter represents a message by which we can filter tickets by custom field
message CustomFieldFilter {
  // FilterKey represents a custom field of ticket
  enum FilterKey {
    FILTER_KEY_UNSPECIFIED = 0;
    // filter to fetch all tickets created for given txn id
    FILTER_KEY_TXN_ID = 1;
    // to fetch tickets related to a specific mandate's issue based on its id
    FILTER_KEY_MANDATE_ID = 2;
    FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_LOAN_VENDOR = 3;
    FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_LOAN_PROGRAM = 4;
    FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION1 = 5;
    FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION2 = 6;
    FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DISPOSITION3 = 7;
    FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_DROP_OFF_STAGE = 8;
    FILTER_KEY_TICKET_FILTERS_CUSTOM_FIELD_FILTER_ACQUISITION_CHANNEL = 9;
  }
  // field on which filter needs to be applied
  FilterKey filter_key = 1;
  oneof filter_value {
    // fetch ticket for specific txn_id
    string txn_id = 2;
    string mandate_id = 3;
    string ticket_filters_custom_field_filter_loan_vendor = 4;
    string ticket_filters_custom_field_filter_loan_program = 5;
    string ticket_filters_custom_field_filter_disposition1 = 6;
    string ticket_filters_custom_field_filter_disposition2 = 7;
    string ticket_filters_custom_field_filter_disposition3 = 8;
    string ticket_filters_custom_field_filter_dropoff_stage = 9;
    string ticket_filters_custom_field_filter_acquisition_channel = 10;
  }
}

// ticket filters which can be applied from app/frontend
message TicketFiltersForUser {
  repeated TicketStatusForUser status_list = 1;
}

message BulkTicketJobDetails {
  int64 job_id = 1;
  // current status of the job
  JobStatus job_status = 2;
  // count of total tickets in the input csv for the job
  int64 input_ticket_count = 3;
  // count of tickets processed so far
  int64 processed_ticket_count = 4;
  // count of ticket for which action was successful
  int64 successful_ticket_count = 5;
  // count of tickets for which action failed
  int64 failed_ticket_count = 6;

  google.protobuf.Timestamp created_at = 7;

  google.protobuf.Timestamp updated_at = 8;
  // will be set to true if job is force killed
  api.typesv2.common.BooleanEnum is_killed = 9;
  // email of user who triggered the job
  string started_by_email = 10;
  // email of user who verified the update file
  string checker_email = 11;
  // description of the job
  string description = 12;
}

message TicketFailureLog {
  int64 job_id = 1;

  int64 ticket_id = 2;

  string failure_reason = 3;

  google.protobuf.Timestamp created_at = 4;

  google.protobuf.Timestamp updated_at = 5;
}

message BulkTicketJobFilters {
  google.protobuf.Timestamp from_date = 1;

  google.protobuf.Timestamp to_date = 2;
}

message ProductCategoryMeta {
  oneof meta {
    ProductCategoryTransactionMeta product_category_transaction_meta = 1;
    ProductCategorySavingsMeta product_category_savings_meta = 2;
    ProductCategoryRewardsMeta product_category_rewards_meta = 3;
  }
}

message ProductCategoryTransactionMeta {
  string transaction_id = 1;
  string transaction_provenance = 2;
  google.protobuf.Timestamp transaction_date = 3;
  TransactionType transaction_type = 4;
}

message ProductCategorySavingsMeta {
  string account_number = 1;
  string deposit_id = 2;
}

message ProductCategoryRewardsMeta {
  string reward_offer_id = 1;
  string offer_id = 2;
  string exchanger_offer_id = 3;
  string reward_id = 4;
  string redeemed_offer_id = 5;
  string exchanger_order_id = 6;
  string offer_name = 7;
  string offer_description = 8;
  string reward_title = 9;
  string reward_description = 10;
  string exchanger_offer_title = 11;
  string exchanger_offer_description = 12;
  string vendor_redemption_id = 13;
}

// transformation of ticket details to params in TicketDetailsForUser
// eg: from (product_category, product_category_details, subcategory) to
// (title, description) to be shown to the user
message TicketDetailsTransformation {
  string id = 1;
  // product category of the ticket is L1 tagging done by agents
  ProductCategory product_category = 2;
  // product_category_details is the L2 tagging done by agents
  string product_category_details = 3;
  // subcategory is the L3 tagging done by agents
  string subcategory = 4;
  // the type of transformation represented by this row in the table
  TicketTransformationType transformation_type = 5;
  // transformation value for the keys
  message TransformationValue {
    // title to be shown to the user
    string title = 1;
    // description or single generic line to be shown to the user
    string description = 2;
    // sla for the combination of product_category, subcategory and product_category details
    // this is kept as string, as it stores the duration like "2h" which is then parsed and used appropriately
    string sla_duration = 3;
    // link to guru article
    string guru_link = 4;
    // notes
    string note = 5;
    // escalation teams
    repeated EscalationTeam escalation_teams = 6;
    // is_fcr (first contact resolution)
    api.typesv2.common.BooleanEnum is_fcr = 7;
    // Monorail components to be added for creating an issue in the monorail
    repeated string monorail_components = 8;
  }
  TransformationValue transformation_value = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

message InAppCsatResponse {
  string id = 1;
  // actor id of the user who responded to the CSAT question
  string actor_id = 2;
  // id of the ticket for which the CSAT response was given
  int64 ticket_id = 3;
  // id of the question which was responded from feedback_questions table in inapphelp db
  string question_id = 4;
  // id of the CSAT feedabck attempt of the user, which is also stored in feedback_question_responses table in inapphelp db
  string attempt_id = 5;
  // integer denoting the CSAT score responded by the user
  int64 csat_score = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  // feedback comments given by the user
  string feedback_comments = 9;
}

// Attachment will contain info on where the attached file has been stored
// this object can be obtained by end clients by using common utility exposed in pkg to upload attached files
message Attachment {
  // this token will be generated by pkg utility
  string file_token = 1;
}

// this object has to be passed to pkg utility method to obtain Attachment object for a given file
message AttachmentFileInfo {
  // specify file name with extension
  string file_name_with_extension = 1 [(validate.rules).string.min_len = 1];
  // file content in bytes array
  bytes file_content = 2 [(validate.rules).bytes.min_len = 1];
}

// Message to be published to an SNS topic whenever there is an update in primary fields (status, group, priority) of the ticket
message TicketUpdateEvent {
  queue.ConsumerRequestHeader request_header = 1;
  Ticket ticket = 2;
}

message CreateTicketEvent {
  queue.ConsumerRequestHeader request_header = 1;
  Ticket ticket = 2;
  ClientRequestInfo client_request_info = 3;
}

message ClientRequestInfo {
  string id = 1;
  Client client = 2;
}

// the content of this message will create the token which will help identify each request for CSAT
// encoding (while sending comms) and decoding (while collecting feedback) will be done by BE
message CsatTokenPayload {
  int64 ticket_id = 1;
  string actor_id = 2;
  // the type of communication channel through which the CSAT request was sent.
  CsatComms csat_comm_type = 3;
}
