syntax = "proto3";

package cx.ticket;

option go_package = "github.com/epifi/gamma/api/cx/ticket";
option java_package = "com.github.epifi.gamma.api.cx.ticket";

enum EntityType {
  ENTITY_TYPE_UNSPECIFIED = 0;
  TRANSACTION = 1;
  SAVINGS = 2;
  REWARDS = 3;
  ACTOR_ACTIVITY = 4;
}

// enum to represent ticket source
enum Source {
  SOURCE_UNSPECIFIED = 0;
  SOURCE_EMAIL = 1;
  SOURCE_PORTAL = 2;
  SOURCE_PHONE = 3;
  SOURCE_CHAT = 7;
  SOURCE_MOBIHELP = 8;
  SOURCE_FEEDBACK_WIDGET = 9;
  SOURCE_OUTBOUND_EMAIL = 10;
}

// enum to represent product category custom field
enum ProductCategory {
  PRODUCT_CATEGORY_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_TRANSACTION = 1;
  PRODUCT_CATEGORY_ACCOUNTS = 2;
  PRODUCT_CATEGORY_ONBOARDING = 3;
  PRODUCT_CATEGORY_SAVE = 4;
  PRODUCT_CATEGORY_WAITLIST = 5;
  PRODUCT_CATEGORY_RE_ONBOARDING = 6;
  PRODUCT_CATEGORY_REWARDS = 7;
  PRODUCT_CATEGORY_FIT = 8;
  PRODUCT_CATEGORY_DEBIT_CARD = 9;
  PRODUCT_CATEGORY_REFERRALS = 10;
  PRODUCT_CATEGORY_CONNECTED_ACCOUNTS = 11;
  PRODUCT_CATEGORY_FRAUD_AND_RISK = 12;
  PRODUCT_CATEGORY_JUMP_P2P = 13;
  PRODUCT_CATEGORY_PROFILE = 14;
  PRODUCT_CATEGORY_SALARY_ACCOUNT = 15;
  PRODUCT_CATEGORY_SEARCH = 16;
  PRODUCT_CATEGORY_WEALTH_ONBOARDING = 17;
  PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS = 18;
  PRODUCT_CATEGORY_APP_CRASH = 19;
  PRODUCT_CATEGORY_DATA_DELETION = 20;
  PRODUCT_CATEGORY_SCREENER = 21;
  PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED = 22;
  PRODUCT_CATEGORY_LANGUAGE_CALLBACK = 23;
  PRODUCT_CATEGORY_CATEGORY_NOT_FOUND = 24;
  PRODUCT_CATEGORY_KYC_OUTCALL = 25;
  PRODUCT_CATEGORY_TRANSACTION_ISSUES = 26;
  PRODUCT_CATEGORY_REWARDS_NEW = 27;
  PRODUCT_CATEGORY_REFERRALS_NEW = 28;
  PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI = 29;
  PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT = 30;
  PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED = 31;
  PRODUCT_CATEGORY_INSTANT_LOANS = 32;
  PRODUCT_CATEGORY_TIERING_PLANS = 33;
  PRODUCT_CATEGORY_CREDIT_CARD = 34;
  PRODUCT_CATEGORY_US_STOCKS = 35;
  PRODUCT_CATEGORY_DEVICE = 36;
  PRODUCT_CATEGORY_RISK = 37;
  PRODUCT_CATEGORY_ON_APP_TRANSACTIONS = 38;
  PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS = 39;
  PRODUCT_CATEGORY_INSTANT_SALARY = 40;
  PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD = 41;
  PRODUCT_CATEGORY_LAMF = 42;
  PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD = 43;
  PRODUCT_CATEGORY_SALARY_LITE = 44;
  PRODUCT_CATEGORY_FI_STORE = 45;
  PRODUCT_CATEGORY_GENERAL_ENQUIRY = 46;
  PRODUCT_CATEGORY_APP_RELATED = 47;
  PRODUCT_CATEGORY_DEPOSITS_AND_INVESTMENTS = 48;
  PRODUCT_CATEGORY_INCOMPLETE_CONVERSATION = 49;
  PRODUCT_CATEGORY_LOANS = 50;
  PRODUCT_CATEGORY_NET_WORTH = 51;
  PRODUCT_CATEGORY_SERVICE_REQUESTS = 52;
}

// enum to represent posible values product category detail custom field in case of onboarding category
enum ProductCategoryDetailsOnboarding {
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_APP_DOWNLOAD_ISSUE = 1;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_CHECK_FAILURE = 2;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_PHONE_NUMBER_OTP = 3;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_EMAIL_SELECTION_FAILURE = 4;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_MOTHER_FATHER_NAME = 5;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_PAN_NAME_VALIDATION_FAILURE = 6;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_EXISTING_FEDERAL_ACCOUNT = 7;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_KYC = 8;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_LIVENESS = 9;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_FACEMATCH_FAIL = 10;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_UN_NAME_CHECK = 11;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_CONFIRM_CARD_MAILING_ADDRESS = 12;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_UPI_CONSENT_FAILURE = 13;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_DEVICE_REGISTRATION_FAILURE = 14;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_CUSTOMER_CREATION_FAILURE = 15;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_ACCOUNT_OPENING_DELAYED = 16;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_CARD_CREATION_FAILURE = 17;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_CARD_PIN_SET_FAILURE = 18;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_UPI_SETUP_FAILURE = 19;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_VKYC = 20;
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_REONBOARDING = 21;
}

enum ProductCategoryDetailsSave {
  PRODUCT_CATEGORY_DETAILS_SAVE_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_SAVE_FIXED_DEPOSIT = 1;
  PRODUCT_CATEGORY_DETAILS_SAVE_SMART_DEPOSIT = 2;
}

enum ProductCategoryDetailsAccounts {
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN = 1;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY = 2;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CLOSURE_REQUEST = 3;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_OPENING_ISSUES = 4;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_UPGRADE_DOWNGRADE = 5;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_BALANCE_TRANSFER = 6;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CATEGORY_NOT_FOUND = 7;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CHEQUEBOOK_RELATED = 8;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_DORMANT = 9;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_FEES_AND_CHARGES = 10;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_KYC_RELATED = 11;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_LIEN_ON_ACCOUNT = 12;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_RE_LOGIN_ISSUES = 13;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_SALARY_PROGRAM = 14;
}

enum SubCategoryPin {
  SUB_CATEGORY_PIN_UNSPECIFIED = 0;
  SUB_CATEGORY_PIN_UPI_PIN = 1;
  SUB_CATEGORY_PIN_DEVICE_PIN = 2;
  SUB_CATEGORY_PIN_APP_PIN = 3;
}

enum ProductCategoryDetailsDebitCard {
  PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION = 1;
  PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY = 2;
}

enum SubCategoryDebitCardActivation {
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNSPECIFIED = 0;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_QR_CODE_NOT_WORKING = 1;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNABLE_TO_SET_PIN = 2;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_POS = 3;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_CONTACTLESS = 4;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_ATM_WITHDRAWAL = 5;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_OTP_NOT_RECEIVED = 6;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_HOW_TO_ACTIVATE = 7;
}

enum SubCategoryDebitCardDelivery {
  SUB_CATEGORY_DEBIT_CARD_DELIVERY_UNSPECIFIED = 0;
  SUB_CATEGORY_DEBIT_CARD_DELIVERY_TRACKING = 1;
  SUB_CATEGORY_DEBIT_CARD_DELIVERY_DID_NOT_RECEIVED_CARD = 2;
}

enum ProductCategoryDetailsTransactions {
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP = 1;
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP = 2;
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_CARDS_ATM = 3;
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT = 4;
}

enum ProductCategoryDetailsWealthMutualFunds {
  PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL = 1;
}

enum SubCategoryTransactionsDebitedViaFiApp {
  SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT = 1;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY = 2;
}

enum SubCategoryTransactionsDebitedFromFiAccountViaOtherApp {
  SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT = 1;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY = 2;
}

enum SubCategoryTransactionsCardsATM {
  SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_DEBITED_BUT_NOT_DISPENSED_AT_MACHINE = 1;
}

enum SubCategoryTransactionsUPIUnableToTransact {
  SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED = 1;
}

enum SubCategoryWealthMutualFundsInvestmentTransactionSuccessful {
  SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNSPECIFIED = 0;
  SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED = 1;
}

enum SubCategorySaveFixedDeposit {
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_UNSPECIFIED = 0;
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_PRE_CLOSURE = 1;
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_MATURITY = 2;
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_INTEREST_PAYOUT = 3;
}

enum SubCategorySaveSmartDeposit {
  SUB_CATEGORY_SAVE_SMART_DEPOSITS_UNSPECIFIED = 0;
  SUB_CATEGORY_SAVE_SMART_DEPOSIT_PRE_CLOSURE = 1;
  SUB_CATEGORY_SAVE_SMART_DEPOSIT_MATURITY = 2;
  SUB_CATEGORY_SAVE_SMART_DEPOSIT_INTEREST_PAYOUT = 3;
}

// enum to represent transaction type custom field
enum TransactionType {
  TRANSACTION_TYPE_UNSPECIFIED = 0;
  TRANSACTION_TYPE_UPI = 1;
  TRANSACTION_TYPE_RTGS = 2;
  TRANSACTION_TYPE_NEFT = 3;
  TRANSACTION_TYPE_DEBIT_CARD = 4;
  TRANSACTION_TYPE_IMPS = 5;
  TRANSACTION_TYPE_ECOM = 6;
  TRANSACTION_TYPE_POS_ATM = 7;
  TRANSACTION_TYPE_INTRA_BANK = 8;
  // Adding an enum to explicitly differentiate cases of unset versus set to Unknown
  TRANSACTION_TYPE_UNKNOWN = 9;
}

// enum to represent ticket status
enum Status {
  STATUS_UNSPECIFIED = 0;
  STATUS_OPEN = 2;
  STATUS_PENDING = 3;
  STATUS_RESOLVED = 4;
  STATUS_CLOSED = 5;
  STATUS_WAITING_ON_THIRD_PARTY = 7;
  STATUS_ESCALATED_TO_L2 = 8;
  STATUS_ESCALATED_TO_FI_ENG = 9;
  STATUS_ESCALATED_TO_FI_OM = 10;
  STATUS_ESCALATED_TO_FEDERAL = 11;
  // The freshdesk ticket has been sent to Product team for resolution
  STATUS_SEND_TO_PRODUCT = 12;
  // The product team has accepted and is working on the ticket
  STATUS_WAITING_ON_PRODUCT = 13;
  // The issue resolved by product has been reopened
  STATUS_REOPEN = 14;
  // The product team asked for more clarification from the customer
  STATUS_NEEDS_CLARIFICATION_FROM_CX = 15;
  // Status corresponding to "Waiting on Customer" on freshdesk.
  STATUS_WAITING_ON_CUSTOMER = 16;
}

// enum to indicate possible groups for ticket
enum Group {
  GROUP_UNSPECIFIED = 0;

  GROUP_CALLBACK = 1;

  GROUP_EPIFI_ESCALATION = 2;

  GROUP_ESCALATED_CASES_CLOSURE = 3;

  GROUP_FEDERAL_ESCALATIONS = 4;

  GROUP_L1_SUPPORT = 5;

  GROUP_L2_SUPPORT = 6;

  GROUP_NON_SFTP_ESCALATIONS = 7;

  GROUP_SFTP_ESCALATIONS = 8;

  GROUP_SFTP_PENDING_GROUP = 9;

  GROUP_FEDERAL_UPDATES = 10;

  GROUP_L1_SUPPORT_WAITLIST = 11;

  GROUP_RISK_OPS = 12;

  GROUP_ACCOUNT_CLOSURE_RISK_BLOCK = 13;

  GROUP_L1_SUPPORT_CALL = 14;

  GROUP_L1_SUPPORT_CHAT = 15;

  GROUP_L1_SUPPORT_EMAIL = 16;

  GROUP_L1_SUPPORT_SOCIAL_MEDIA = 17;

  GROUP_OUTBOUND_CALL_BACK = 18;

  GROUP_LOAN_OUTBOUND_CALL = 19;
}

// enum to indicate priority of ticket
enum Priority {
  PRIORITY_UNSPECIFIED = 0;
  PRIORITY_LOW = 1;
  PRIORITY_MEDIUM = 2;
  PRIORITY_HIGH = 3;
  PRIORITY_URGENT = 4;
}

// enum for association type ticket field
enum AssociationType {
  ASSOCIATION_TYPE_UNSPECIFIED = 0;
  ASSOCIATION_TYPE_PARENT = 1;
  ASSOCIATION_TYPE_CHILD = 2;
  ASSOCIATION_TYPE_TRACKER = 3;
  ASSOCIATION_TYPE_RELATED = 4;
}

// enum for possible values of dispute status custom fields
enum DisputeStatus {
  DISPUTE_STATUS_UNSPECIFIED = 0;
  DISPUTE_STATUS_ACCEPTED = 1;
  DISPUTE_STATUS_REJECTED = 2;
}

// enum to represent different user identifier types in ticket
enum UserIdentifierType {
  USER_IDENTIFIER_TYPE_UNSPECIFIED = 0;
  // indetifier type in ticket is user reference id from vendor mapping table
  USER_INDETIFIER_TYPE_REFERENCE_ID = 1;
  // indentifier type is email
  USER_IDENTIFIER_TYPE_EMAIL = 2;
  // indentifier type is Phone number
  USER_IDENTIFIER_TYPE_PHONE_NUMBER = 3;
}

enum SupportTicketFieldMask {
  SUPPORT_TICKET_FIELD_MASK_UNSPECIFIED = 0;
  SUPPORT_TICKET_FIELD_MASK_STATUS = 1;
  SUPPORT_TICKET_FIELD_MASK_SOURCE = 2;
  SUPPORT_TICKET_FIELD_MASK_PRODUCT_CATEGORY = 3;
  SUPPORT_TICKET_FIELD_MASK_PRODUCT_CATEGORY_DETAILS = 4;
  SUPPORT_TICKET_FIELD_MASK_USER_IDENTIFIER_TYPE = 5;
  SUPPORT_TICKET_FIELD_MASK_USER_IDENTIFIER_VALUE = 6;
  SUPPORT_TICKET_FIELD_MASK_TICKET = 7;
  SUPPORT_TICKET_FIELD_MASK_REQUESTER = 8;
  SUPPORT_TICKET_FIELD_MASK_TICKET_CREATED_AT = 9;
  SUPPORT_TICKET_FIELD_MASK_TICKET_UPDATED_AT = 10;
  SUPPORT_TICKET_FIELD_MASK_TICKET_META = 11;
  SUPPORT_TICKET_FIELD_MASK_RAW_TICKET = 12;
  SUPPORT_TICKET_FIELD_MASK_AGENT_GROUP = 13;
  SUPPORT_TICKET_FIELD_MASK_ACTOR_ID = 14;
  SUPPORT_TICKET_FIELD_MASK_EXPECTED_RESOLUTION_TIME = 15;
  SUPPORT_TICKET_FIELD_MASK_ISSUE_CATEGORY_ID = 16;
  SUPPORT_TICKET_FIELD_MASK_CONTACT_SUMMARY_DETAILS = 17;
  SUPPORT_TICKET_FIELD_MASK_RESPONDER_ID = 18;
}

// enum for fields mask to be used for ticket updates
enum TicketFieldUpdateMask {
  TICKET_FIELD_UPDATE_MASK_UNSPECIFIED = 0;

  TICKET_FIELD_UPDATE_MASK_CUSTOM_FIELDS = 1;

  TICKET_FIELD_UPDATE_MASK_IS_ESCALATED = 2;

  TICKET_FIELD_UPDATE_MASK_PRIORITY = 3;

  TICKET_FIELD_UPDATE_MASK_STATUS = 4;

  TICKET_FIELD_UPDATE_MASK_SUBJECT = 5;

  TICKET_FIELD_UPDATE_MASK_TAGS = 6;

  TICKET_FIELD_UPDATE_MASK_TYPE = 7;

  TICKET_FIELD_UPDATE_MASK_GROUP = 8;
}

enum JobStatus {
  JOB_STATUS_UNSPECIFIED = 0;

  JOB_STATUS_IN_PROGRESS = 1;

  JOB_STATUS_FAILED = 2;

  JOB_STATUS_SUCCESS = 3;

  JOB_STATUS_PARTIAL_SUCCESS = 4;
}

enum ResolutionMode {
  RESOLUTION_MODE_UNSPECIFIED = 0;
  // the ticket was resolved using some automation job
  RESOLUTION_MODE_AUTO_RESOLUTION = 1;
  // the ticket was resolved in bulk using sherlock
  RESOLUTION_MODE_BULK_RESOLUTION = 2;
  // ticket was resolved manually by support agent
  RESOLUTION_MODE_MANUAL_RESOLUTION = 3;
  // Resolved by Watson service
  RESOLUTION_MODE_WATSON_RESOLUTION = 4;
}

// enum to represent ticket status as required by app client
enum TicketStatusForUser {
  TICKET_STATUS_FOR_USER_UNSPECIFIED = 0;
  // open tickets
  TICKET_STATUS_FOR_USER_ACTIVE = 1;
  // closed tickets
  TICKET_STATUS_FOR_USER_CLOSED = 2;
  // for tickets which are waiting on customer to perform some action
  TICKET_STATUS_FOR_USER_WAITING_ON_CUSTOMER = 3;
  // resolution sla has been breached still ticket is not resolved
  TICKET_STATUS_FOR_USER_DELAY = 4;
  TICKET_STATUS_FOR_USER_RE_OPEN = 5;
}

// Different types of transformations for TicketDetailsTransformation table
enum TicketTransformationType {
  TICKET_TRANSFORMATION_TYPE_UNSPECIFIED = 0;
  // transformation to convert (product category, category details, subcategory) to (title, description)
  TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_TITLE_DESCRIPTION = 1;
  // transformation to convert (product category, category details, subcategory) to sla mapping
  TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_SLA_DURATION_MAPPING = 2;
  // transformation to convert (product category, category details, subcategory) to guru link
  TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_GURU_LINK = 3;
  // transformation to convert (product category, category details, subcategory) to note
  TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_NOTE = 4;
  // transformation to convert (product category, category details, subcategory) to escalation team
  TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_ESCALATION_TEAM = 5;
  // transformation to convert (product category, category details, subcategory) to is_fcr (first contact resolution)
  TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_IS_FCR = 6;
  // transformation to convert (product category, category details, subcategory) to Monorail components for creating an issue in the monorail
  TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_MONORAIL_COMPONENTS = 7;
  // transformation to convert (product category, category details, subcategory) to (title, description) for auto IDs
  TICKET_TRANSFORMATION_TYPE_CATEGORY_TAGS_TO_TITLE_DESCRIPTION_AUTO_IDS = 8;
}

enum TicketDetailsTransformationFieldMask {
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_UNSPECIFIED = 0;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_PRODUCT_CATEGORY = 1;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_PRODUCT_CATEGORY_DETAILS = 2;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_SUBCATEGORY = 3;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_TITLE = 4;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_DESCRIPTION = 5;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_SLA_DURATION = 6;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_IS_FCR = 7;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_ESCALATION_TEAM = 8;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_GURU_LINK = 9;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_NOTE = 10;
  TICKET_DETAILS_TRANSFORMATION_FIELD_MASK_MONORAIL_COMPONENTS = 11;
}

enum EscalationTeam {
  ESCALATION_TEAM_UNSPECIFIED = 0;
  // Onboarding Tech
  ESCALATION_TEAM_ONBOARDING_TECH = 1;
  // Cards Tech
  ESCALATION_TEAM_CARDS_TECH = 2;
  // Risk Ops
  ESCALATION_TEAM_RISK_OPS = 3;
  // CNX
  ESCALATION_TEAM_CNX = 4;
  // Debit Card
  ESCALATION_TEAM_DEBIT_CARD = 5;
  // Debit Card Ops
  ESCALATION_TEAM_DEBIT_CARD_OPS = 6;
  // Debit Card Tech
  ESCALATION_TEAM_DEBIT_CARD_TECH = 7;
  // Federal
  ESCALATION_TEAM_FEDERAL = 8;
  // Federal Ops
  ESCALATION_TEAM_FEDERAL_OPS = 9;
  // FIT Tech Team
  ESCALATION_TEAM_FIT_TECH = 10;
  // Jump Tech Team
  ESCALATION_TEAM_JUMP_TECH = 11;
  // L2
  ESCALATION_TEAM_L2 = 12;
  // MF Ops
  ESCALATION_TEAM_MF_OPS = 13;
  // MF tech team
  ESCALATION_TEAM_MF_TECH = 14;
  //Onboarding Tech, Cards tech
  ESCALATION_TEAM_ONBOARDING_TECH_ROUTED_TO_CARDS = 15;
  // Pay Tech
  ESCALATION_TEAM_PAY_TECH = 16;
  // Referrals
  ESCALATION_TEAM_REFERRALS = 17;
  // Rewards
  ESCALATION_TEAM_REWARDS = 18;
  // VKYC Ops
  ESCALATION_TEAM_VKYC_OPS = 19;
  // Karza
  ESCALATION_TEAM_KARZA = 20;
  // CNX/Federal
  ESCALATION_TEAM_CNX_AND_FEDERAL = 21;
  // Onboarding tech (escalated to Federal)
  ESCALATION_TEAM_ONBOARDING_TECH_ROUTED_TO_FEDERAL = 22;
  // NA
  ESCALATION_TEAM_NOT_APPLICABLE = 23;
}

// enum to represent ticket visibility, to determine whom to show the ticket
enum TicketVisibility {
  TICKET_VISIBILITY_UNSPECIFIED = 0;
  // only show ticket to agent
  TICKET_VISIBILITY_ONLY_AGENT = 1;
  // only show ticket to customer
  TICKET_VISIBILITY_ONLY_CUSTOMER = 2;
  // show ticket to both agent and customer
  TICKET_VISIBILITY_ALL = 3;
}

// enum to categorize savings account based on the balance present
enum SavingsAccountBalance {
  SAVINGS_ACCOUNT_BALANCE_UNSPECIFIED = 0;
  SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1 = 1;
  SAVINGS_ACCOUNT_BALANCE_OTHER = 2;
}

// Whether there is any Monorail ticket ID associated with this Freshdesk ticket
// This is a dropdown field with yes / no options
enum MonorailRaised {
  MONORAIL_RAISED_UNSPECIFIED = 0;
  MONORAIL_RAISED_YES = 1;
  MONORAIL_RAISED_NO = 2;
}

// Ticket created by, this field is shown to user in show my tickets section
enum CreatedBy {
  CREATED_BY_UNSPECIFIED = 0;
  // If user themselves create a ticket
  CREATED_BY_CUSTOMER = 1;
  // Automatic ticket creation by Watson
  CREATED_BY_SYSTEM = 2;
  // Agent manually creates a ticket
  CREATED_BY_AGENT = 3;
}

// By which mode the ticket was created
enum CreationMode {
  CREATION_MODE_UNSPECIFIED = 0;
  CREATION_MODE_EMAIL = 1;
  CREATION_MODE_CHAT = 2;
  CREATION_MODE_CALL = 3;
  // Ticket created by Watson
  CREATION_MODE_AUTOMATIC = 4;
  CREATION_MODE_DEFAULT = 5;
  CREATION_MODE_APP = 6;
}

enum InAppCsatResponseFieldMask {
  IN_APP_CSAT_RESPONSE_FIELD_MASK_UNSPECIFIED = 0;
  IN_APP_CSAT_RESPONSE_FIELD_MASK_ACTOR_ID = 1;
  IN_APP_CSAT_RESPONSE_FIELD_MASK_TICKET_ID = 2;
  IN_APP_CSAT_RESPONSE_FIELD_MASK_QUESTION_ID = 3;
  IN_APP_CSAT_RESPONSE_FIELD_MASK_ATTEMPT_ID = 4;
  IN_APP_CSAT_RESPONSE_FIELD_MASK_CSAT_SCORE = 5;
}

// enum to represent the type of instruction given to the agent for a specific ticket
enum AgentInstructionType {
  AGENT_INSTRUCTION_TYPE_UNSPECIFIED = 0;
  // Figma: https://www.figma.com/proto/mPO3o3O1axCViWDGWLOn6o/%E2%98%8E%EF%B8%8F-Sherlock-%E2%80%A2%C2%A0Workfile?node-id=5037-20024&t=Bo7OUidKT17n98DM-1
  // instruction to agent to share information with the user
  AGENT_INSTRUCTION_TYPE_SHARE_INFO_TO_USER = 1;
  // Figma: https://www.figma.com/proto/mPO3o3O1axCViWDGWLOn6o/%E2%98%8E%EF%B8%8F-Sherlock-%E2%80%A2%C2%A0Workfile?node-id=5038-20867&t=Bo7OUidKT17n98DM-1
  // instruction to agent to raise an escalation
  AGENT_INSTRUCTION_TYPE_RAISE_ESCALATION = 2;
  // Figma: https://www.figma.com/proto/mPO3o3O1axCViWDGWLOn6o/%E2%98%8E%EF%B8%8F-Sherlock-%E2%80%A2%C2%A0Workfile?node-id=5038-22588&t=Bo7OUidKT17n98DM-1
  // instruction to agent to take final step and complete the call
  AGENT_INSTRUCTION_TYPE_FINAL_INSTRUCTION = 3;
}

enum Client {
  CLIENT_UNSPECIFIED = 0;
  CLIENT_IN_APP_ISSUE_REPORTING = 1;
  CLIENT_WATSON = 2;
}

enum CsatComms {
  CSAT_COMMS_UNSPECIFIED = 0;
  CSAT_COMMS_EMAIL = 1;
  CSAT_COMMS_IN_APP_NOTIFICATION = 2;
  CSAT_COMMS_SYSTEM_TRAY_NOTIFICATION = 3;
  CSAT_COMMS_IN_APP_BANNER = 4;
  CSAT_COMMS_SMS = 5;
  CSAT_COMMS_WHATSAPP = 6;
}

// App related L2 categories
enum ProductCategoryDetailsApp {
  PRODUCT_CATEGORY_DETAILS_APP_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_APP_RELATED_ISSUES = 1;
  PRODUCT_CATEGORY_DETAILS_APP_RE_LOGIN_ISSUES = 2;
}

// Card related L2 categories
enum ProductCategoryDetailsCard {
  PRODUCT_CATEGORY_DETAILS_CARD_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_CARD_REQUEST = 1;
  PRODUCT_CATEGORY_DETAILS_CARD_SETTINGS = 2;
}

// FIT related L2 categories
enum ProductCategoryDetailsFit {
  PRODUCT_CATEGORY_DETAILS_FIT_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_FIT_RULES = 1;
}

// Jump related L2 categories
enum ProductCategoryDetailsJump {
  PRODUCT_CATEGORY_DETAILS_JUMP_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_JUMP_GENERAL = 1;
}

// Mutual Funds related L2 categories
enum ProductCategoryDetailsMutualFunds {
  PRODUCT_CATEGORY_DETAILS_MUTUAL_FUNDS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_MUTUAL_FUNDS_INVESTMENTS = 1;
  PRODUCT_CATEGORY_DETAILS_MUTUAL_FUNDS_ONBOARDING = 2;
  PRODUCT_CATEGORY_DETAILS_MUTUAL_FUNDS_WITHDRAWALS = 3;
}

// US Stocks related L2 categories
enum ProductCategoryDetailsUSStocks {
  PRODUCT_CATEGORY_DETAILS_US_STOCKS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_US_STOCKS_GENERAL = 1;
  PRODUCT_CATEGORY_DETAILS_US_STOCKS_WALLET_ISSUES = 2;
  PRODUCT_CATEGORY_DETAILS_US_STOCKS_BUYING = 3;
}

// Product related L2 categories
enum ProductCategoryDetailsProduct {
  PRODUCT_CATEGORY_DETAILS_PRODUCT_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_PRODUCT_DEPRECATED = 1;
}

// Communication related L2 categories
enum ProductCategoryDetailsCommunication {
  PRODUCT_CATEGORY_DETAILS_COMMUNICATION_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_COMMUNICATION_BLANK_CHAT = 1;
  PRODUCT_CATEGORY_DETAILS_COMMUNICATION_CALL_DROP = 2;
  PRODUCT_CATEGORY_DETAILS_COMMUNICATION_INCOMPLETE_EMAIL = 3;
}

// Loan related L2 categories
enum ProductCategoryDetailsLoan {
  PRODUCT_CATEGORY_DETAILS_LOAN_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_LOAN_APPLICATION_DISBURSAL = 1;
  PRODUCT_CATEGORY_DETAILS_LOAN_BUREAU_CIBIL = 2;
  PRODUCT_CATEGORY_DETAILS_LOAN_COLLECTIONS = 3;
  PRODUCT_CATEGORY_DETAILS_LOAN_EMI_RELATED = 4;
  PRODUCT_CATEGORY_DETAILS_LOAN_LAMF_SHORTFALL = 5;
  PRODUCT_CATEGORY_DETAILS_LOAN_CLOSURE = 6;
  PRODUCT_CATEGORY_DETAILS_LOAN_OUTCALLING = 7;
}

// Connection related L2 categories
enum ProductCategoryDetailsConnection {
  PRODUCT_CATEGORY_DETAILS_CONNECTION_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_CONNECTION_UNABLE_TO_CONNECT = 1;
  PRODUCT_CATEGORY_DETAILS_CONNECTION_UNABLE_TO_DISCONNECT = 2;
}

// Rewards related L2 categories
enum ProductCategoryDetailsRewards {
  PRODUCT_CATEGORY_DETAILS_REWARDS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_REWARDS_FI_POINTS_NOT_REFUNDED = 1;
  PRODUCT_CATEGORY_DETAILS_REWARDS_GIFT_CARDS = 2;
  PRODUCT_CATEGORY_DETAILS_REWARDS_INCORRECT_REWARD = 3;
  PRODUCT_CATEGORY_DETAILS_REWARDS_NOT_RECEIVED = 4;
}

// Security related L2 categories
enum ProductCategoryDetailsSecurity {
  PRODUCT_CATEGORY_DETAILS_SECURITY_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_SECURITY_BANK_INITIATED_FREEZE = 1;
  PRODUCT_CATEGORY_DETAILS_SECURITY_LEA_NPCI_COMPLAINT = 2;
}

// Service request L2 categories
enum ProductCategoryDetailsServiceRequest {
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_CALLBACK = 1;
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_DATA_DELETION = 2;
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_NACH_MANDATES = 3;
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_REVOKE_APP_ACCESS = 4;
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_STOP_SERVICES = 5;
}

// Transaction related L2 categories
enum ProductCategoryDetailsTransaction {
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_AMOUNT_DEBITED = 1;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_AMOUNT_DEBITED_NOT_CREDITED = 2;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_AUTOMATED_PAYMENTS = 3;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_UNAUTHORIZED = 4;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_CHEQUE = 5;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_DATA_NOT_REFRESHED = 6;
}

// Business related L2 categories
enum ProductCategoryDetailsBusiness {
  PRODUCT_CATEGORY_DETAILS_BUSINESS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_BUSINESS_COLLABORATION = 1;
}

// Net banking related L2 categories
enum ProductCategoryDetailsNetBanking {
  PRODUCT_CATEGORY_DETAILS_NET_BANKING_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_NET_BANKING_GENERAL = 1;
}

// User related L2 categories
enum ProductCategoryDetailsUser {
  PRODUCT_CATEGORY_DETAILS_USER_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_USER_UNREGISTERED = 1;
  PRODUCT_CATEGORY_DETAILS_USER_UNABLE_TO_PAY = 2;
}

// Credit related L2 categories
enum ProductCategoryDetailsCredit {
  PRODUCT_CATEGORY_DETAILS_CREDIT_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_CREDIT_PENDING_TO_FI = 1;
}

// Document related L2 categories
enum ProductCategoryDetailsDocument {
  PRODUCT_CATEGORY_DETAILS_DOCUMENT_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_DOCUMENT_REQUEST = 1;
}

// L3 categories for account closure requests
enum SubCategoryAccountsClosure {
  SUB_CATEGORY_ACCOUNTS_CLOSURE_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_FREEZE_ON_ACCOUNT = 1;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_IN_APP_REQUEST_RECEIVED = 2;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_MANUAL_REQUEST = 3;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_NOT_CLOSABLE_DUE_TO_PENDING_CHARGES = 4;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_REDIRECTED_TO_APP = 5;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_FULL_KYC_ACCOUNT_CLOSED = 6;
}

// L3 categories for account opening issues
enum SubCategoryAccountsOpening {
  SUB_CATEGORY_ACCOUNTS_OPENING_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_OPENING_ADD_FUNDS_ON_APP = 1;
  SUB_CATEGORY_ACCOUNTS_OPENING_APP_DOWNLOAD = 2;
  SUB_CATEGORY_ACCOUNTS_OPENING_DEVICE_REGISTRATION_FAILURE = 3;
  SUB_CATEGORY_ACCOUNTS_OPENING_CARD_CREATION_PIN_SETUP_FAILURE = 4;
  SUB_CATEGORY_ACCOUNTS_OPENING_CONSENT_RELATED = 5;
  SUB_CATEGORY_ACCOUNTS_OPENING_CUSTOMER_CREATION = 6;
  SUB_CATEGORY_ACCOUNTS_OPENING_EXISTING_FEDERAL_ACCOUNT = 7;
  SUB_CATEGORY_ACCOUNTS_OPENING_KYC_RELATED = 8;
  SUB_CATEGORY_ACCOUNTS_OPENING_LIVENESS_FACEMATCH_ISSUE = 9;
  SUB_CATEGORY_ACCOUNTS_OPENING_APP_SCREENING = 10;
  SUB_CATEGORY_ACCOUNTS_OPENING_REFUND_FOR_ADD_FUNDS = 11;
  SUB_CATEGORY_ACCOUNTS_OPENING_REOPEN_CLOSED_ACCOUNT = 12;
  SUB_CATEGORY_ACCOUNTS_OPENING_STUCK_AT_EMAIL_VERIFICATION = 13;
  SUB_CATEGORY_ACCOUNTS_OPENING_VKYC_ISSUES = 14;
}

// L3 categories for account upgrade/downgrade
enum SubCategoryAccountsUpgradeDowngrade {
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_FUNDS_ADDED_BUT_ACCOUNT_NOT_UPGRADED = 1;
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_TIER_DOWNGRADE = 2;
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_WITHIN_COOL_OFF_PERIOD = 3;
}

// L3 categories for account KYC related issues
enum SubCategoryAccountsKYC {
  SUB_CATEGORY_ACCOUNTS_KYC_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_KYC_MIN_KYC_EXPIRED = 1;
  SUB_CATEGORY_ACCOUNTS_KYC_UNABLE_TO_SUBMIT_FORM = 2;
  SUB_CATEGORY_ACCOUNTS_KYC_NOT_UPDATED = 3;
  SUB_CATEGORY_ACCOUNTS_KYC_UPDATED_BUT_ACCOUNT_NOT_ACTIVATED = 4;
  SUB_CATEGORY_ACCOUNTS_KYC_SIGNATURE_NOT_UPDATED = 5;
}

// L3 categories for chequebook related issues
enum SubCategoryAccountsChequebook {
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_CHARGES_WAIVER = 1;
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_NOT_RECEIVED = 2;
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_UNABLE_TO_UPLOAD_SIGNATURE = 3;
}

// L3 categories for account fees and charges
enum SubCategoryAccountsFeesCharges {
  SUB_CATEGORY_ACCOUNTS_FEES_CHARGES_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_FEES_CHARGES_AMB = 1;
}

// L3 categories for account information
enum SubCategoryAccountsInfo {
  SUB_CATEGORY_ACCOUNTS_INFO_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_INFO_ACCOUNT_INFO = 1;
  SUB_CATEGORY_ACCOUNTS_INFO_ACCOUNT_TIER_DETAILS_BENEFITS = 2;
  SUB_CATEGORY_ACCOUNTS_INFO_APP_SETTINGS = 3;
  SUB_CATEGORY_ACCOUNTS_INFO_NR_ACCOUNT = 4;
  SUB_CATEGORY_ACCOUNTS_INFO_RE_KYC_ISSUES = 5;
  SUB_CATEGORY_ACCOUNTS_INFO_INFORMATION_REGARDING_LIEN = 6;
}

// L3 categories for app login issues
enum SubCategoryAccountsAppLogin {
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_ISSUES = 1;
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_DEVICE_PASSWORD_NOT_ACCEPTED = 2;
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_RE_LOGIN_BEFORE_SIGNUP = 3;
}

// L3 categories for app-related issues
enum SubCategoryAppRelated {
  SUB_CATEGORY_APP_RELATED_UNSPECIFIED = 0;
  SUB_CATEGORY_APP_RELATED_GENERAL_QUERIES = 1;
  SUB_CATEGORY_APP_RELATED_INSURANCE_RELATED = 2;
  SUB_CATEGORY_APP_RELATED_REGISTRATION = 3;
  SUB_CATEGORY_APP_RELATED_UPGRADE_DOWNGRADE_ISSUE = 4;
  SUB_CATEGORY_APP_RELATED_APP_CRASH = 5;
  SUB_CATEGORY_APP_RELATED_FEATURE_NOT_LOADING = 6;
  SUB_CATEGORY_APP_RELATED_USER_FEEDBACK = 7;
  SUB_CATEGORY_APP_RELATED_FULFILLMENT_RELATED = 8;
  SUB_CATEGORY_APP_RELATED_REWARDS_RELATED = 9;
  SUB_CATEGORY_APP_RELATED_REDIRECTED_TO_BANK = 10;
}

// L3 categories for card-related issues
enum SubCategoryCardRequest {
  SUB_CATEGORY_CARD_REQUEST_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_REQUEST_CARD_DAMAGED = 1;
  SUB_CATEGORY_CARD_REQUEST_CARD_NOT_REQUIRED = 2;
  SUB_CATEGORY_CARD_REQUEST_LOST_STOLEN = 3;
  SUB_CATEGORY_CARD_REQUEST_DIGITAL_CARD = 4;
}

enum SubCategoryCardSettings {
  SUB_CATEGORY_CARD_SETTINGS_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_SETTINGS_ACTIVATE_CARD = 1;
  SUB_CATEGORY_CARD_SETTINGS_CHANGE_USAGE_SETTINGS = 2;
  SUB_CATEGORY_CARD_SETTINGS_PIN_FAILING = 3;
  SUB_CATEGORY_CARD_SETTINGS_TEMPORARY_FREEZE = 4;
  SUB_CATEGORY_CARD_SETTINGS_UNABLE_TO_CHANGE_USAGE_SETTINGS = 5;
}

enum SubCategoryCardDelivery {
  SUB_CATEGORY_CARD_DELIVERY_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_DELIVERY_RTO_REDISPATCH = 1;
  SUB_CATEGORY_CARD_DELIVERY_RTO_REFUND = 2;
}

enum SubCategoryCardCharges {
  SUB_CATEGORY_CARD_CHARGES_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_CHARGES_AMC = 1;
  SUB_CATEGORY_CARD_CHARGES_ECOM_POS_DECLINE_FEES = 2;
  SUB_CATEGORY_CARD_CHARGES_OTHER_CHARGES = 3;
}

enum SubCategoryCardInfo {
  SUB_CATEGORY_CARD_INFO_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_INFO_CARD_DETAILS = 1;
  SUB_CATEGORY_CARD_INFO_DELIVERY_RELATED = 2;
  SUB_CATEGORY_CARD_INFO_MIN_KYC_USER = 3;
}

// L3 categories for ATM transactions
enum SubCategoryATMTransactions {
  SUB_CATEGORY_ATM_TRANSACTIONS_UNSPECIFIED = 0;
  SUB_CATEGORY_ATM_TRANSACTIONS_CDM_CASH_DEPOSIT_NOT_CREDITED = 1;
  SUB_CATEGORY_ATM_TRANSACTIONS_DEBITED_BUT_NOT_DISPENSED = 2;
  SUB_CATEGORY_ATM_TRANSACTIONS_DEBITED_BUT_PARTIALLY_DISPENSED = 3;
  SUB_CATEGORY_ATM_TRANSACTIONS_UNABLE_TO_WITHDRAW_DOMESTIC = 4;
  SUB_CATEGORY_ATM_TRANSACTIONS_UNABLE_TO_WITHDRAW_INTERNATIONAL = 5;
}

// L3 categories for transaction issues
enum SubCategoryTransactionIssues {
  SUB_CATEGORY_TRANSACTION_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_DEBITED_BUT_NOT_CREDITED = 1;
  SUB_CATEGORY_TRANSACTION_ISSUES_TXN_FAILED_DOMESTIC = 2;
  SUB_CATEGORY_TRANSACTION_ISSUES_TXN_FAILED_INTERNATIONAL = 3;
  SUB_CATEGORY_TRANSACTION_ISSUES_ORDER_FAILED_REFUND_NOT_RECEIVED = 4;
  SUB_CATEGORY_TRANSACTION_ISSUES_ORDER_PENDING = 5;
}

// L3 categories for fixed deposit and smart deposit
enum SubCategoryFDSD {
  SUB_CATEGORY_FD_SD_UNSPECIFIED = 0;
  SUB_CATEGORY_FD_SD_UNABLE_TO_CREATE = 1;
  SUB_CATEGORY_FD_SD_UNABLE_TO_MODIFY = 2;
  SUB_CATEGORY_FD_SD_UNABLE_TO_PAUSE = 3;
  SUB_CATEGORY_FD_SD_CANCEL_AUTO_RENEWAL = 4;
  SUB_CATEGORY_FD_SD_FD_CLOSED_BUT_AMOUNT_NOT_RECEIVED = 5;
  SUB_CATEGORY_FD_SD_INCORRECT_MATURITY_AMOUNT = 6;
  SUB_CATEGORY_FD_SD_UNABLE_TO_CLOSE_FD = 7;
  SUB_CATEGORY_FD_SD_SD_CLOSED_BUT_AMOUNT_NOT_RECEIVED = 8;
  SUB_CATEGORY_FD_SD_UNABLE_TO_CLOSE_SD = 9;
}

// L3 categories for FIT rules
enum SubCategoryFITRules {
  SUB_CATEGORY_FIT_RULES_UNSPECIFIED = 0;
  SUB_CATEGORY_FIT_RULES_FIT_RULE_NOT_EXECUTED = 1;
  SUB_CATEGORY_FIT_RULES_INCORRECT_AMOUNT_DEPOSITED = 2;
  SUB_CATEGORY_FIT_RULES_FIT_RULE_INFORMATION = 3;
}

// L3 categories for Jump
enum SubCategoryJump {
  SUB_CATEGORY_JUMP_UNSPECIFIED = 0;
  SUB_CATEGORY_JUMP_PORTFOLIO_MISMATCH = 1;
  SUB_CATEGORY_JUMP_WITHDRAWAL_ISSUES = 2;
}

// L3 categories for Mutual Funds
enum SubCategoryMutualFunds {
  SUB_CATEGORY_MUTUAL_FUNDS_UNSPECIFIED = 0;
  SUB_CATEGORY_MUTUAL_FUNDS_SIP_NOT_DEDUCTED = 1;
  SUB_CATEGORY_MUTUAL_FUNDS_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED = 2;
  SUB_CATEGORY_MUTUAL_FUNDS_STUCK_IN_SCREENING = 3;
  SUB_CATEGORY_MUTUAL_FUNDS_WITHDRAWAL_FROM_OTHER_PLATFORM = 4;
  SUB_CATEGORY_MUTUAL_FUNDS_INCORRECT_AMOUNT_CREDITED = 5;
  SUB_CATEGORY_MUTUAL_FUNDS_PAUSE_AUTO_INVESTMENT = 6;
}

// L3 categories for US Stocks
enum SubCategoryUSStocks {
  SUB_CATEGORY_US_STOCKS_UNSPECIFIED = 0;
  SUB_CATEGORY_US_STOCKS_AMOUNT_DEBITED_NOT_CREDITED_TO_WALLET = 1;
}

// L3 categories for Fi Store
enum SubCategoryFiStore {
  SUB_CATEGORY_FI_STORE_UNSPECIFIED = 0;
  SUB_CATEGORY_FI_STORE_DIRECT_TO_HOME = 1;
  SUB_CATEGORY_FI_STORE_PHYSICAL_MERCHANDISE = 2;
}

// L3 categories for Salary Programs
enum SubCategorySalaryPrograms {
  SUB_CATEGORY_SALARY_PROGRAMS_UNSPECIFIED = 0;
  SUB_CATEGORY_SALARY_PROGRAMS_INSTANT_SALARY = 1;
  SUB_CATEGORY_SALARY_PROGRAMS_SALARY_LITE = 2;
  SUB_CATEGORY_SALARY_PROGRAMS_INFORMATION_REGARDING_CHARGES = 3;
}

// L3 categories for Communication
enum SubCategoryCommunication {
  SUB_CATEGORY_COMMUNICATION_UNSPECIFIED = 0;
  SUB_CATEGORY_COMMUNICATION_SPAM = 1;
  SUB_CATEGORY_COMMUNICATION_CALLBACK = 2;
  SUB_CATEGORY_COMMUNICATION_REQUEST_FOR_MORE_INFO = 3;
}

// L3 categories for Loans
enum SubCategoryLoans {
  SUB_CATEGORY_LOANS_UNSPECIFIED = 0;
  SUB_CATEGORY_LOANS_APPLICATION_FAILED = 1;
  SUB_CATEGORY_LOANS_DISBURSAL_PENDING = 2;
  SUB_CATEGORY_LOANS_ISSUE_WITH_LOAN_APPLICATION = 3;
  SUB_CATEGORY_LOANS_LOAN_DISBURSED_BUT_ACCOUNT_NOT_CREATED = 4;
  SUB_CATEGORY_LOANS_CONSENT_WITHDRAWAL_FOR_CIBIL_ENQUIRY = 5;
  SUB_CATEGORY_LOANS_REQUEST_FOR_BUREAU_CORRECTION = 6;
  SUB_CATEGORY_LOANS_BORROWERS_DEMISE = 7;
  SUB_CATEGORY_LOANS_HARASSMENT_COMPLAINT = 8;
  SUB_CATEGORY_LOANS_PAYMENT_LINK_TO_BE_SENT = 9;
  SUB_CATEGORY_LOANS_REQUEST_FOR_SETTLEMENT = 10;
  SUB_CATEGORY_LOANS_REQUESTING_EMI_EXTENSION = 11;
  SUB_CATEGORY_LOANS_REPAYMENT_SCHEDULE = 12;
  SUB_CATEGORY_LOANS_EMI_NOT_DEDUCTED = 13;
  SUB_CATEGORY_LOANS_NACH_RE_REGISTRATION = 14;
  SUB_CATEGORY_LOANS_PAYMENT_STATUS_NOT_UPDATED = 15;
  SUB_CATEGORY_LOANS_LOAN_DETAILS_AND_STATUS = 16;
  SUB_CATEGORY_LOANS_LOAN_PRE_CLOSURE = 17;
  SUB_CATEGORY_LOANS_LOAN_REPAYMENT = 18;
  SUB_CATEGORY_LOANS_PAY_MARGIN_AMOUNT = 19;
  SUB_CATEGORY_LOANS_PLEDGE_MORE_FUNDS = 20;
  SUB_CATEGORY_LOANS_PLEDGED_MUTUAL_FUNDS_SOLD = 21;
  SUB_CATEGORY_LOANS_DELAY_IN_CLOSURE = 22;
  SUB_CATEGORY_LOANS_PAID_BUT_MF_NOT_UNPLEDGED = 23;
  SUB_CATEGORY_LOANS_PRE_CLOSURE = 24;
  SUB_CATEGORY_LOANS_PRE_DISBURSEMENT = 25;
  SUB_CATEGORY_LOANS_SALES = 26;
  SUB_CATEGORY_LOANS_SERVICE = 27;
  SUB_CATEGORY_LOANS_EMI_PAID_BUT_ECS_NACH_RETURN_CHARGED = 28;
  SUB_CATEGORY_LOANS_LATE_PAYMENT_FEES = 29;
  SUB_CATEGORY_LOANS_UNABLE_TO_PAY_VIA_APP = 30;
  SUB_CATEGORY_LOANS_UNABLE_TO_PAY_VIA_COLLECTIONS_LINK = 31;
}

// L3 categories for Assets
enum SubCategoryAssets {
  SUB_CATEGORY_ASSETS_UNSPECIFIED = 0;
  SUB_CATEGORY_ASSETS_ASSETS_VIA_MANUAL_FORMS = 1;
  SUB_CATEGORY_ASSETS_EPFO = 2;
  SUB_CATEGORY_ASSETS_INDIAN_STOCKS = 3;
  SUB_CATEGORY_ASSETS_LOANS = 4;
  SUB_CATEGORY_ASSETS_NPS = 5;
  SUB_CATEGORY_ASSETS_OTHER_BANK_ACCOUNTS = 6;
  SUB_CATEGORY_ASSETS_ABOUT_NEGATIVE_BALANCE = 7;
  SUB_CATEGORY_ASSETS_ABOUT_NETWORTH = 8;
  SUB_CATEGORY_ASSETS_CONNECTED_ACCOUNTS = 9;
  SUB_CATEGORY_ASSETS_UNABLE_TO_ADD_ASSETS_LIABILITIES = 10;
}

// L3 categories for Rewards
enum SubCategoryRewards {
  SUB_CATEGORY_REWARDS_UNSPECIFIED = 0;
  SUB_CATEGORY_REWARDS_CONVERT_TO_CASH = 1;
  SUB_CATEGORY_REWARDS_PLAY_AND_WIN = 2;
  SUB_CATEGORY_REWARDS_POWER_UP = 3;
  SUB_CATEGORY_REWARDS_TRAVEL_MILES = 4;
  SUB_CATEGORY_REWARDS_HOW_TO_GET_REWARDS = 5;
  SUB_CATEGORY_REWARDS_HOW_TO_REFER = 6;
  SUB_CATEGORY_REWARDS_REWARDS_STATEMENT = 7;
  SUB_CATEGORY_REWARDS_AMOUNT_NOT_REFUNDED = 8;
  SUB_CATEGORY_REWARDS_EXPIRED_VOUCHER_RECEIVED = 9;
  SUB_CATEGORY_REWARDS_FI_POINTS_NOT_REFUNDED = 10;
  SUB_CATEGORY_REWARDS_CAMPAIGN_SPECIFIC = 11;
  SUB_CATEGORY_REWARDS_DEBIT_CARD_OFFERS = 12;
  SUB_CATEGORY_REWARDS_REFERRAL = 13;
  SUB_CATEGORY_REWARDS_TIERING_REWARDS = 14;
}

// L3 categories for KYC
enum SubCategoryKYC {
  SUB_CATEGORY_KYC_UNSPECIFIED = 0;
  SUB_CATEGORY_KYC_NON_KYC_RELATED = 1;
  SUB_CATEGORY_KYC_VKYC_RELATED = 2;
}

// L3 categories for Account Security
enum SubCategoryAccountSecurity {
  SUB_CATEGORY_ACCOUNT_SECURITY_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNT_SECURITY_REQUEST_TO_UNFREEZE = 1;
  SUB_CATEGORY_ACCOUNT_SECURITY_FREEZE_RELATED = 2;
  SUB_CATEGORY_ACCOUNT_SECURITY_ADDITIONAL_INFORMATION = 3;
  SUB_CATEGORY_ACCOUNT_SECURITY_NOC_RELATED = 4;
}

// L3 categories for Language Support
enum SubCategoryLanguage {
  SUB_CATEGORY_LANGUAGE_UNSPECIFIED = 0;
  SUB_CATEGORY_LANGUAGE_ASSAMESE = 1;
  SUB_CATEGORY_LANGUAGE_BENGALI = 2;
  SUB_CATEGORY_LANGUAGE_HINDI = 3;
  SUB_CATEGORY_LANGUAGE_KANNADA = 4;
  SUB_CATEGORY_LANGUAGE_MALAYALAM = 5;
  SUB_CATEGORY_LANGUAGE_ORIYA = 6;
  SUB_CATEGORY_LANGUAGE_TAMIL = 7;
  SUB_CATEGORY_LANGUAGE_TELUGU = 8;
}

// L3 categories for Data and Statements
enum SubCategoryDataStatements {
  SUB_CATEGORY_DATA_STATEMENTS_UNSPECIFIED = 0;
  SUB_CATEGORY_DATA_STATEMENTS_REQUEST_DATA_DELETION = 1;
  SUB_CATEGORY_DATA_STATEMENTS_BANK_STATEMENT = 2;
  SUB_CATEGORY_DATA_STATEMENTS_MUTUAL_FUNDS_STATEMENT = 3;
  SUB_CATEGORY_DATA_STATEMENTS_SIGNED_BANK_STATEMENT = 4;
  SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_JUMP = 5;
  SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_MUTUAL_FUNDS = 6;
  SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_US_STOCKS = 7;
  SUB_CATEGORY_DATA_STATEMENTS_US_STOCKS_STATEMENT = 8;
}

// L3 categories for Mandates
enum SubCategoryMandates {
  SUB_CATEGORY_MANDATES_UNSPECIFIED = 0;
  SUB_CATEGORY_MANDATES_ACTIVE_MANDATES_DETAILS = 1;
  SUB_CATEGORY_MANDATES_CANCEL_SI_NACH_MANDATES = 2;
}

// L3 categories for Profile Updates
enum SubCategoryProfileUpdates {
  SUB_CATEGORY_PROFILE_UPDATES_UNSPECIFIED = 0;
  SUB_CATEGORY_PROFILE_UPDATES_CHANGE_EMPLOYMENT_DETAILS = 1;
  SUB_CATEGORY_PROFILE_UPDATES_CONTACT_DETAILS_UPDATE = 2;
  SUB_CATEGORY_PROFILE_UPDATES_DOB_CHANGE = 3;
  SUB_CATEGORY_PROFILE_UPDATES_NAME_CHANGE_FATHER_MOTHER = 4;
  SUB_CATEGORY_PROFILE_UPDATES_NAME_CHANGE_USER = 5;
}

// L3 categories for Device Issues
enum SubCategoryDeviceIssues {
  SUB_CATEGORY_DEVICE_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_DEVICE_ISSUES_CARDS = 1;
  SUB_CATEGORY_DEVICE_ISSUES_DEVICE_LOST = 2;
  SUB_CATEGORY_DEVICE_ISSUES_PROMOTIONAL_COMMS = 3;
}

// L3 categories for Transaction Issues
enum SubCategoryTransactionTypes {
  SUB_CATEGORY_TRANSACTION_TYPES_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTION_TYPES_GOODS_SERVICES_NOT_DELIVERED = 1;
  SUB_CATEGORY_TRANSACTION_TYPES_INCORRECT_AMOUNT = 2;
  SUB_CATEGORY_TRANSACTION_TYPES_NOT_VISIBLE_ON_APP = 3;
  SUB_CATEGORY_TRANSACTION_TYPES_IN_APP_UPI_P2M = 4;
  SUB_CATEGORY_TRANSACTION_TYPES_IN_APP_UPI_P2P = 5;
  SUB_CATEGORY_TRANSACTION_TYPES_OFF_APP_UPI_P2M = 6;
  SUB_CATEGORY_TRANSACTION_TYPES_OFF_APP_UPI_P2P = 7;
  SUB_CATEGORY_TRANSACTION_TYPES_INTRA_BANK = 8;
  SUB_CATEGORY_TRANSACTION_TYPES_NACH_ECS_CHARGES = 9;
  SUB_CATEGORY_TRANSACTION_TYPES_RECURRING_PAYMENT_CANCELLED_BUT_AMOUNT_DEBITED = 10;
  SUB_CATEGORY_TRANSACTION_TYPES_CHEQUE_DEPOSIT = 11;
  SUB_CATEGORY_TRANSACTION_TYPES_INTERNATIONAL_REMITTANCE = 12;
  SUB_CATEGORY_TRANSACTION_TYPES_MERCHANT_REFUND = 13;
  SUB_CATEGORY_TRANSACTION_TYPES_OTHER_DOMESTIC_TRANSACTIONS = 14;
  SUB_CATEGORY_TRANSACTION_TYPES_DEPOSITING_CASH = 15;
  SUB_CATEGORY_TRANSACTION_TYPES_IPO = 16;
  SUB_CATEGORY_TRANSACTION_TYPES_TRANSACTION_RELATED_ENQUIRY = 17;
}

// L3 categories for UPI Issues
enum SubCategoryUPIIssues {
  SUB_CATEGORY_UPI_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_UPI_ISSUES_UNABLE_TO_LINK_FEDERAL_ACCOUNT_TO_OTHER_APPS = 1;
  SUB_CATEGORY_UPI_ISSUES_BANK_TRANSFER = 2;
  SUB_CATEGORY_UPI_ISSUES_INTERNATIONAL_TRANSACTIONS = 3;
  SUB_CATEGORY_UPI_ISSUES_LIMIT_EXCEEDED = 4;
  SUB_CATEGORY_UPI_ISSUES_PIN_TRIES_EXCEEDED = 5;
  SUB_CATEGORY_UPI_ISSUES_UPI_ISSUE = 6;
}

// L3 categories for International Transactions
enum SubCategoryInternationalTransactions {
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_UNSPECIFIED = 0;
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_INTERNATIONAL = 1;
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_STOP_CHEQUE_PAYMENT = 2;
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_FEES_AND_CHARGES = 3;
}

// L3 categories for Certificates
enum SubCategoryCertificates {
  SUB_CATEGORY_CERTIFICATES_UNSPECIFIED = 0;
  SUB_CATEGORY_CERTIFICATES_BALANCE_CERTIFICATE = 1;
  SUB_CATEGORY_CERTIFICATES_INTEREST_CERTIFICATE = 2;
}

// L3 categories for Eligibility Issues
enum SubCategoryEligibility {
  SUB_CATEGORY_ELIGIBILITY_UNSPECIFIED = 0;
  SUB_CATEGORY_ELIGIBILITY_NOT_ELIGIBLE = 1;
  SUB_CATEGORY_ELIGIBILITY_OTP_NOT_RECEIVED = 2;
}

// L3 categories for Profile Changes
enum SubCategoryProfileChanges {
  SUB_CATEGORY_PROFILE_CHANGES_UNSPECIFIED = 0;
  SUB_CATEGORY_PROFILE_CHANGES_ADDRESS_CHANGE = 1;
  SUB_CATEGORY_PROFILE_CHANGES_UNAUTHORISED_TRANSACTION = 2;
}

// L3 categories for Card Usage Issues
enum SubCategoryCardUsage {
  SUB_CATEGORY_CARD_USAGE_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_USAGE_CARD_NOT_ACCEPTED = 1;
  SUB_CATEGORY_CARD_USAGE_CONTACTLESS_NOT_WORKING = 2;
  SUB_CATEGORY_CARD_USAGE_ATM_DECLINE_FEES = 3;
  SUB_CATEGORY_CARD_USAGE_FUEL_CHARGES = 4;
  SUB_CATEGORY_CARD_USAGE_TCS_DEDUCTIONS = 5;
}

// L3 categories for Balance Issues
enum SubCategoryBalanceIssues {
  SUB_CATEGORY_BALANCE_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_BALANCE_ISSUES_BALANCE_NOT_UPDATED = 1;
  SUB_CATEGORY_BALANCE_ISSUES_DOUBLE_DEBIT = 2;
  SUB_CATEGORY_BALANCE_ISSUES_INCORRECT_AMOUNT_DEBITED = 3;
  SUB_CATEGORY_BALANCE_ISSUES_EXCESS_AMOUNT_PAID = 4;
}

// L3 categories for App Access Issues
enum SubCategoryAppAccess {
  SUB_CATEGORY_APP_ACCESS_UNSPECIFIED = 0;
  SUB_CATEGORY_APP_ACCESS_NO_APP_ACCESS = 1;
  SUB_CATEGORY_APP_ACCESS_UNABLE_TO_INVEST = 2;
  SUB_CATEGORY_APP_ACCESS_UNABLE_TO_WITHDRAW = 3;
  SUB_CATEGORY_APP_ACCESS_UNABLE_TO_ADD_FUNDS = 4;
}

// L3 categories for Payment Issues
enum SubCategoryPaymentIssues {
  SUB_CATEGORY_PAYMENT_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_PAYMENT_ISSUES_SENT_TO_WRONG_USER = 1;
  SUB_CATEGORY_PAYMENT_ISSUES_CASH_DEPOSIT_AT_BRANCH = 2;
}

// L3 categories for Fixed/Smart Deposit Issues
enum SubCategoryDepositIssues {
  SUB_CATEGORY_DEPOSIT_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_CREATE_FD = 1;
  SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_CREATE_SD = 2;
  SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_REDEEM = 3;
}

// L3 categories for Account Status Issues
enum SubCategoryAccountStatus {
  SUB_CATEGORY_ACCOUNT_STATUS_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNT_STATUS_ACCOUNT_FROZEN_CLOSED = 1;
  SUB_CATEGORY_ACCOUNT_STATUS_EMAIL_ADDRESS = 2;
  SUB_CATEGORY_ACCOUNT_STATUS_PHONE_NUMBER = 3;
  SUB_CATEGORY_ACCOUNT_STATUS_BOUNCE_CHARGE = 4;
}

// L3 categories for Reward Issues
enum SubCategoryRewardIssues {
  SUB_CATEGORY_REWARD_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_REWARD_ISSUES_VOUCHER_NOT_RECEIVED = 1;
  SUB_CATEGORY_REWARD_ISSUES_FOREX_RATE_ISSUE = 2;
}

// L3 categories for Stock Trading Issues
enum SubCategoryStockTrading {
  SUB_CATEGORY_STOCK_TRADING_UNSPECIFIED = 0;
  SUB_CATEGORY_STOCK_TRADING_UNABLE_TO_BUY = 1;
  SUB_CATEGORY_STOCK_TRADING_MONEY_NOT_CREDITED = 2;
  SUB_CATEGORY_STOCK_TRADING_UNABLE_TO_SELL = 3;
}
