syntax = "proto3";
package cx.call_ivr;

option go_package = "github.com/epifi/gamma/api/cx/call_ivr";
option java_package = "com.github.epifi.gamma.api.cx.call_ivr";

enum IvrType {
  IVR_TYPE_UNSPECIFIED = 0;
  // Auth IVR will be used to complete customer authentication
  // before connecting the call to an agent
  IVR_TYPE_AUTH = 1;
  // Credit Freeze IVR will be used for user whose credit is frozen
  IVR_TYPE_RISK_BLOCK = 2;
  // Debit Card Block IVR will be used to block a user's debit card upon their request.
  IVR_TYPE_DC_BLOCK = 3;
  // Credit Card Presence check IVR checks for the availability of CC for the user
  IVR_TYPE_CC_PRESENCE_CHECK = 4;
}

enum IvrState {
  IVR_STATE_UNSPECIFIED = 0;
  // connect to an agent by reusing the routing channel + priority value that was recieved in response
  // of GetRoutingChannel RPC
  IVR_STATE_CONNECT_TO_AGENT = 1;
  // IVR is in completed state, drop the call
  IVR_STATE_COMPLETED = 2;
  // IVR state is waiting on response which can be dependent on external user actions
  // For example : clicking on a notification/email for Auth IVR
  // Expectation is Ozonetel will call GetIvrState after receiving this state after waiting for some time
  // to poll further for IVR status
  IVR_STATE_WAITING_ON_RESPONSE = 3;
  // IVR state is ongoing, expectation from vendor is to get the response of the question mapped to current IVR type and question id
  // and pass it to ProcessIvrUserInput to get the next question and IVR state
  IVR_STATE_ONGOING = 4;
}
enum IvrQuestion {
  IVR_QUESTION_UNSPECIFIED = 0;
  // Question : "Thank you for calling Fi Care. Please enter your registered mobile number"
  IVR_QUESTION_ENTER_REGISTERED_MOBILE_NUMBER = 1;
  // Question : "Sorry, the mobile number you entered is not registered with us.
  // We request you to kindly write to <NAME_EMAIL> from your registered Email id."
  IVR_QUESTION_MOBILE_NUMBER_NOT_REGISTERED = 2;
  // Question : "We have sent you a notification on your Fi app and your registered email,
  // kindly authenticate your identity by clicking on "Yes" to connect to an agent."
  IVR_QUESTION_AUTHENTICATE = 3;
  // Question : "Hey, sorry we couldn't verify your identity.
  // Kindly enter the last 6 digits of your savings account number to connect to an agent."
  IVR_QUESTION_ENTER_SAVINGS_ACCOUNT_DIGITS = 4;
  // Question : "Hey, sorry we couldn't verify your identity. We request you to kindly write to <NAME_EMAIL> from your registered
  // email id, and we will be able to assist you further."
  IVR_QUESTION_AUTHENTICATION_FAILED = 5;
  // Question : “Please select your preferred language”
  IVR_QUESTION_SELECT_LANGUAGE = 6;
  // Question: “Sorry, you have selected wrong option. Please try again."
  IVR_QUESTION_WRONG_OPTION_SELECTED = 7;
  // Question: “We have already sent an <NAME_EMAIL>. For more assistance, please listen to the options and choose one. "
  // 1. If you have not received the email from us.
  // 2. If you need any help in understanding the email?
  // 3. If you are facing any issue with the incoming credit
  // 4. If you are facing any issue with your active investment such as, Mutual fund,Fixed Deposit,etc.
  IVR_QUESTION_RISK_USER_CALL_REASON = 8;
  // Question: “If you want to arrange a call back from concerned team”
  IVR_QUESTION_REQUEST_CALLBACK = 9;
  // Question: “Do not worry, our internal team will help you with this.
  // We have arranged a call with the concerned team to address your issue within the next 24 hours.”
  IVR_QUESTION_CALLBACK_REQUEST_ACCEPTED = 10;
  // Question: "If you wish to report loss, theft, or unauthorized usage of your debit card, please press 3."
  // 1. Card Not Found: "We don’t have any active card for you." (End Flow)
  // 2. Card Found: "If you wish to block your card, please press 1."
  // Question sent upon pressing 3
  IVR_QUESTION_CHECK_AVAILABILITY_OF_VALID_DEBIT_CARD = 48;
  // Question: "This is an irreversible action, and your card will be blocked immediately. To confirm, please press 1"
  // Question sent upon the final confirmation
  IVR_QUESTION_DEBIT_CARD_BLOCK_REQUEST = 50;
  // For credit card related queries, press 1
  IVR_QUESTION_CHECK_PRESENCE_OF_CC = 52;
}

enum CallIvrDetailFieldMask {
  CALL_IVR_DETAIL_FIELD_MASK_UNSPECIFIED = 0;
  CALL_IVR_DETAIL_FIELD_MASK_PREVIOUS_QUESTION_RESPONSE = 1;
  CALL_IVR_DETAIL_FIELD_MASK_CURRENT_IVR_TYPE = 2;
  CALL_IVR_DETAIL_FIELD_MASK_PREVIOUS_IVR_TYPE = 3;
  CALL_IVR_DETAIL_FIELD_MASK_CURRENT_QUESTION_ID = 4;
  CALL_IVR_DETAIL_FIELD_MASK_PREVIOUS_QUESTION_ID = 5;
  CALL_IVR_DETAIL_FIELD_MASK_WAIT_DURATION = 6;
  CALL_IVR_DETAIL_FIELD_MASK_CURRENT_IVR_STATE = 7;
  CALL_IVR_DETAIL_FIELD_MASK_PREVIOUS_IVR_STATE = 8;
  CALL_IVR_DETAIL_FIELD_MASK_PREFERRED_LANGUAGE = 9;
}
