syntax = "proto3";

package cx.landing_page;

import "api/cx/customer_auth/customer_auth.proto";
import "api/cx/header.proto";
import "api/cx/landing_page/message.proto";
import "api/cx/landing_page/otp.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/webui/detail_grid.proto";
import "api/typesv2/webui/error_view.proto";
import "api/vendorgateway/cx/freshdesk/ticket.proto";
import "api/waitlist/internal/waitlist_user.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/landing_page";
option java_package = "com.github.epifi.gamma.api.cx.landing_page";

message GetTicketAndUserDetailsRequest {
  // ticket id will be part of cx header
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetTicketAndUserDetailsResponse {
  rpc.Status status = 1;

  // source for the given ticket
  cx.freshdesk.Source ticket_source = 2;

  // this will be set to true if a user exists in epifi for the given ticket
  bool user_exists_in_epifi = 3;

  // email of user
  string email = 4;

  // name of user
  api.typesv2.common.Name name = 5;

  // phone number of user
  api.typesv2.common.PhoneNumber phone_number = 6;

  // this is set to true if callback user flag is set in ticket otherwise false
  bool callback_user = 7;

  // this field shows the actual phone number(number which the call came from) in the ticket
  api.typesv2.common.PhoneNumber ticket_phone_number = 8;

  // This will return true if a user is found in waitlist
  bool user_exists_in_waitlist = 9;

  // this field will contain freshdesk ticket id if ticket id is found given monitorUCID record
  int64 existing_ticket_id_for_call = 10;

  // this field will contain the expected resolution time for the ticket which is sla duration added to ticket's created at time
  google.protobuf.Timestamp expected_resolution_time = 11;
}

message GetUserDetailsRequest {
  // user identifier will be part of cx header
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetUserDetailsResponse {
  rpc.Status status = 1;

  // email of user
  string email = 2;

  // name of user
  api.typesv2.common.Name name = 3;

  // phone number of user
  api.typesv2.common.PhoneNumber phone_number = 4;
}

message CallbackUserRequest {
  // ticket id, agentEmail, access token are mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

}

message CallbackUserResponse {
  // status enum to pass custom status code for this rpc
  enum Status {
    OK = 0;
    // agent is not available on ozonetel app
    AGENT_UNAVAILABLE = 102;
  }

  rpc.Status status = 1;

  // status that is being returned from ozonetel api
  // possible values:
  // queued​ ​successfully (success)
  // please​ ​provide​ ​proper​ ​user​ ​credentials (invalid username or api key)
  // Campaign​ ​is​ ​invalid (invalid campaign name)
  // Agent​ ​is​ ​not​ ​available (Invalid​ ​agentID/​ ​Agent not​ ​logged​ ​in/NotReady)
  // All​ ​parameters​ ​are​ ​mandatory (Missing​ ​parameters)
  string ozonetel_status = 3;
}

message CallbackUserAdminRequest {
  // ticket id, agentEmail, access token are mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // this field should be populated only if admin dials a number manually on UI
  // phone number from ticket will be picked by default if this is empty
  api.typesv2.common.PhoneNumber phone_number = 2;
}

message CallbackUserAdminResponse {
  // status enum to pass custom status code for this rpc
  enum Status {
    OK = 0;
    // agent is not available on ozonetel app
    AGENT_UNAVAILABLE = 102;
  }

  rpc.Status status = 1;

  customer_auth.SherlockDeepLink sherlock_deep_link = 2;

  // status that is being returned from ozonetel api
  // possible values:
  // queued​ ​successfully (success)
  // please​ ​provide​ ​proper​ ​user​ ​credentials (invalid username or api key)
  // Campaign​ ​is​ ​invalid (invalid campaign name)
  // Agent​ ​is​ ​not​ ​available (Invalid​ ​agentID/​ ​Agent not​ ​logged​ ​in/NotReady)
  // All​ ​parameters​ ​are​ ​mandatory (Missing​ ​parameters)
  string ozonetel_status = 3;
}

message GetWaitlistUserDetailsRequest {
  // ticket id, agentEmail, access token are mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetWaitlistUserDetailsResponse {
  rpc.Status status = 1;
  waitlist.Profile profile = 2;
  string access_status = 3;
  string failure_reason = 4;
}

// User's phone number is received in cx header user identifier field
message GetRecentOTPAttemptsRequest {
  // ticket id, agentEmail, access token are mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetRecentOTPAttemptsResponse {
  rpc.Status status = 1;
  repeated cx.landing_page.OTPAttempt otp_attempt_list = 2;
}


message GetWaitlistUserDetailsWithoutTicketRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // phone number for waitlist user
  // mandatory
  api.typesv2.common.PhoneNumber phone_number = 2;
}

message GetWaitlistUserDetailsWithoutTicketResponse {
  // will return
  // OK for success
  // NOT_FOUND if waitlist user not found with given number
  // INTERNAL for server errors
  // INVALID_ARGUMENT if phone number is not passed in request
  rpc.Status status = 1;
  // will contain waitlist users profile deails like name, email, phone etc
  waitlist.Profile profile = 2;
  // current status of wailist user
  string access_status = 3;
  string failure_reason = 4;
}

message GetRecentUserQueriesRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetRecentUserQueriesResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // user haven't performed any query on app
    RECORD_NOT_FOUND = 5;
    // user is not authorized to access the data
    PERMISSION_DENIED = 7;
    // the system faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  customer_auth.SherlockDeepLink sherlock_deep_link = 2;
  RecentQueries recent_queries = 3;
  // error state in case there was a failure in fetching recent queries
  api.typesv2.webui.ErrorView error_view = 4;
}

message GetAvailableTabsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetAvailableTabsResponse {
  rpc.Status status = 1;
  // tab header selected on sherlock home to fetch user details under that tab
  repeated string available_tabs = 2;
}

message GetTabDetailsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // tab selected by agent should also be passed so that details wrt to only that tab can be fetched
  string selected_tab = 2;
}

message GetTabDetailsResponse {
  rpc.Status status = 1;
  // user details that are part of the tab, these details are fetched from different rpc and shown
  api.typesv2.webui.DetailView tab_details = 2;
}

// this service will be used by sherlock web server to fetch ticket details
service LandingPage {

  // this method will take ticket_id as input and return the details for the ticket and corresponding user
  // this will throw error if ticket id is invalid
  rpc GetTicketAndUserDetails (GetTicketAndUserDetailsRequest) returns (GetTicketAndUserDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // this method will take user identifier(email or phone number) as input and return the details of user
  // this will throw error if no user exists for given identifier
  rpc GetUserDetails (GetUserDetailsRequest) returns (GetUserDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // this method will scheudle a callback on ozonetel for the given agent(in header) and customer (cutomer in the ticket)
  rpc CallbackUser (CallbackUserRequest) returns (CallbackUserResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc GetWaitlistUserDetails (GetWaitlistUserDetailsRequest) returns (GetWaitlistUserDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to callback user on ozonetel
  // this will be available only for admins
  rpc CallbackUserAdmin (CallbackUserAdminRequest) returns (CallbackUserAdminResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.information_level_for_rpc) = "LESS_SENSITIVE";
  }

  // RPC to fetch latest otp attempts for a user, given the phone number
  // This is not a paginated RPC and returns last 'x' attempts where x is fixed at backend
  // In case no attempts are found for the phone number, RECORD NOT FOUND error is returned
  // In case of any server error, INTERNAL is returned
  // In case any otp attempt is found for the user, we return the list
  // user's phone number is to be passed in cx header in user identifier
  rpc GetRecentOTPAttempts (GetRecentOTPAttemptsRequest) returns (GetRecentOTPAttemptsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc will return waitlist user details for given phone number in request
  // accessible to only to admin role
  // ticket validation, enrichment options are set to false and information level is set to insensitive
  // since this is not part of user information flow and will be called from separate ui without mandatory ticket id
  rpc GetWaitlistUserDetailsWithoutTicket (GetWaitlistUserDetailsWithoutTicketRequest) returns (GetWaitlistUserDetailsWithoutTicketResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetRecentUserQueries returns queries user has tried on app before reaching out to customer care
  // as this RPC is intended to only surface recent queries we don't have support for pagination
  // product has provided max cap of 10 queries to be shown
  rpc GetRecentUserQueries (GetRecentUserQueriesRequest) returns (GetRecentUserQueriesResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // GetAvailableTabs returns the list of all tabs that are available on home for agents to view
  // request accepts cx header
  // Error for any failure
  rpc GetAvailableTabs (GetAvailableTabsRequest) returns (GetAvailableTabsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetTabDetails returns the list key-value pairs of different user attributes, its value and cell header it belongs
  // request accepts cx header and the selected tab to fetch user details under it
  // Invalid Argument if mandatory parameter is missing
  // NotFound if record is not found for the selected tab
  // Error for any failure
  rpc GetTabDetails (GetTabDetailsRequest) returns (GetTabDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }
}
