syntax = "proto3";

package cx.data_collector.preapprovedloan;

import "api/cx/customer_auth/customer_auth.proto";
import "api/cx/data_collector/preapprovedloan/preapprovedloan.proto";
import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/loan_header.proto";
import "api/typesv2/webui/table.proto";
import "api/typesv2/webui/text.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.preapprovedloan";

service PreApprovedLoan {
  // RPC to be used to get basic loan details for an actor
  rpc GetLoanUserDetails (GetLoanUserDetailsRequest) returns (GetLoanUserDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }
  // RPC to be used to get details of loan availed by an actor
  rpc GetLoanDetails (GetLoanDetailsRequest) returns (GetLoanDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }
  // RPC to be user to fetch foreclosure details of user's loan account
  rpc GetForeClosureDetails (GetForeclosureRequest) returns (GetForeclosureResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  };

  // this rpc is used for getting additional details that needs to be shown when clicked on "view details" of a cx loans table
  rpc GetLoanAdditionalDetails(GetLoanAdditionalDetailsRequest) returns (GetLoanAdditionalDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  };

  // RPC to get all loans out calling ticket assigned to the agent
  // Agent can select a ticket and view the details relevant to the ticket
  // i.e User Profile Info, Ticket History, Loan Offer Details, and Dev actions
  rpc GetLoanOutCallAgentAssignedTickets(GetLoanOutCallAgentAssignedTicketsRequest) returns (GetLoanOutCallAgentAssignedTicketsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  };

  // RPC to get loans out calling ticket history of the user
  // Agent can use this history to understand previous user interactions
  rpc GetLoanOutCallTicketHistory(GetLoanOutCallTicketHistoryRequest) returns (GetLoanOutCallTicketHistoryResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  };

  // RPC to get the user profile info in unmasked form
  // Agent can use this info to initiate an out call to the user
  rpc GetUserProfileInfo(GetUserProfileInfoRequest) returns (GetUserProfileInfoResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "HIGHLY_SENSITIVE";
  };
}

message GetLoanUserDetailsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // accepting loan header in request since we want to segregate views for different loan programs in Sherlock
  api.typesv2.LoanHeader loan_header = 2;
}

message GetLoanUserDetailsResponse {
  rpc.Status status = 1;
  customer_auth.SherlockDeepLink sherlock_deep_link = 2;

  LoanUserDetails loan_user_details = 3;
}

message GetLoanDetailsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // accepting loan header in request since we want to segregate views for different loan programs in Sherlock
  api.typesv2.LoanHeader loan_header = 2;
}

message GetLoanDetailsResponse {
  rpc.Status status = 1;
  customer_auth.SherlockDeepLink sherlock_deep_link = 2;

  LoanDetails loan_details = 3 [deprecated = true];
  repeated LoanDetailsV2 loan_details_v2 = 4;
  // We can populate various components for loans related to loan_accounts such as loan accounts, mf pledged holdings (for lamf), soa etc.
  // Currently implemented in web for lamf only.
  repeated WebComponent components = 5;
}

message GetForeclosureRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // accepting loan header in request since we want to segregate views for different loan programs in Sherlock
  api.typesv2.LoanHeader loan_header = 2;
  string loan_account_number = 3;
}

message GetForeclosureResponse {
  rpc.Status status = 1;
  customer_auth.SherlockDeepLink sherlock_deep_link = 2;
  api.typesv2.webui.LabelValue key_value = 3;
  ForeclosureDetails fore_closure_details = 4;
}

message GetLoanAdditionalDetailsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // Each row of webui.Table has a metadata field which is used to uniquely identify it. Same will be used for getting additional details that needs to be shown on clicking view details.
  string metadata = 2;
}

message GetLoanAdditionalDetailsResponse {
  rpc.Status status = 1;
  repeated WebComponent components = 2;
}

message GetLoanOutCallAgentAssignedTicketsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  rpc.PageContextRequest page_context = 2 [(validate.rules).message.required = true];
  repeated TicketFilter ticket_filters = 3;
}

message GetLoanOutCallAgentAssignedTicketsResponse {
  rpc.Status status = 1;
  api.typesv2.webui.Table tickets = 2;
  rpc.PageContextResponse page_context = 3;
}

message GetLoanOutCallTicketHistoryRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetLoanOutCallTicketHistoryResponse {
  rpc.Status status = 1;
  repeated WebComponent tickets = 2;
}

message GetUserProfileInfoRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetUserProfileInfoResponse {
  rpc.Status status = 1;
  repeated WebComponent components = 2;
}

message TicketFilter {
  string ticket_filter_key = 1;
  string ticket_filter_value = 2;
}
