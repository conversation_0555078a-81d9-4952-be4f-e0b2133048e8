// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package cx.data_collector.tiering;

import "api/cx/customer_auth/customer_auth.proto";
import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "google/type/date.proto";
import "api/typesv2/common/boolean.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/tiering";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.tiering";

// Tieirng service is responsible for handling and providing tiering related tasks at sherlock side
service Tiering {
  // GetTieringDetails fetches all tiering related details for a user
  // ActorId is mandatory in request header
  // Response contains status message and users tiering info like grace, cool off, history etc.
  // If RPC execution is successful the Status will be OK with records populated
  // If actorId is not passed then Invalid Argument
  // Status will be Internal Server Error if execution fails due to some issues
  rpc GetTieringDetails (GetTieringDetailsRequest) returns (GetTieringDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // OverrideGracePeriod overrides grace period for a user
  // ActorId is mandatory in request header
  // Response contains status message
  // If RPC execution is successful the Status will be OK with records populated
  // If actorId is not passed then Invalid Argument
  // Status will be Internal Server Error if execution fails due to some issues
  rpc OverrideGracePeriod (OverrideGracePeriodRequest) returns (OverrideGracePeriodResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "LESS_SENSITIVE";
  }
  // OverrideCoolOffPeriod overrides cool off period for a user
  // ActorId is mandatory in request header
  // Response contains status message
  // If RPC execution is successful the Status will be OK with records populated
  // If actorId is not passed then Invalid Argument
  // Status will be Internal Server Error if execution fails due to some issues
  rpc OverrideCoolOffPeriod (OverrideCoolOffPeriodRequest) returns (OverrideCoolOffPeriodResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "LESS_SENSITIVE";
  }
  // GetTieringSensitiveDetails fetches sensitive tiering related details for a user
  // ActorId is mandatory in request header
  // Response contains status message and users tiering info like grace, cool off, history etc.
  // If RPC execution is successful the Status will be OK with records populated
  // If actorId is not passed then Invalid Argument
  // Status will be Internal Server Error if execution fails due to some issues
  rpc GetTieringSensitiveDetails (GetTieringSensitiveDetailsRequest) returns (GetTieringSensitiveDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "LESS_SENSITIVE";
  }

  // IsUserIneligibleForRewards checks if a user is ineligible for rewards
  // based on abuser segments on a given date
  rpc IsUserEligibleForRewards (IsUserEligibleForRewardsRequest) returns (IsUserEligibleForRewardsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "LESS_SENSITIVE";
  }
}

message IsUserEligibleForRewardsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // Date for which the eligibility needs to be checked
  google.type.Date date = 2;
}

message IsUserEligibleForRewardsResponse {
  rpc.Status status = 1;
  // Mandatory sherlock deeplink needed by client to do customer auth
  customer_auth.SherlockDeepLink sherlock_deep_link = 2;
  // List of tier to eligibility status mapping
  repeated TierToEligibilityStatus tier_to_eligibility_status = 3;
  message TierToEligibilityStatus {
    // Display string for tier plan
    string tier_name = 1;
    // Boolean to represent if a user is a Rewards Abuser.
    api.typesv2.common.BooleanEnum is_eligible = 2;
  }
}

message GetTieringDetailsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetTieringDetailsResponse {
  rpc.Status status = 1;
  // Display string for users current tier plan
  string tier_plan_name = 2 [deprecated = true];
  // Boolean to signify if user is currently in grace
  api.typesv2.common.BooleanEnum is_user_in_grace = 3;
  // Timestamp if user is in grace period
  // It will be present only if user is in grace period, nil Otherwise
  google.protobuf.Timestamp grace_period_till = 4;
  // Boolean to signify if user is currently in cool off
  api.typesv2.common.BooleanEnum is_user_in_cool_off = 5;
  // Timestamp if user is in cool off period
  // It will be present only if user is in cool off period, nil Otherwise
  google.protobuf.Timestamp cool_off_period_till = 6;
  // Latest 'N' movements for the user
  repeated TierMovementHistory movement_histories = 7 [deprecated = true];
  // Mandatory sherlock deeplink needed by client to do customer auth
  customer_auth.SherlockDeepLink sherlock_deep_link = 8;
  // Boolean to represent if a user is a Rewards Abuser.
  api.typesv2.common.BooleanEnum is_a_rewards_abuser_user = 9;
  // Tier movement criteria stores the criteria through which user entered current tier, and criterias user is satisfying to be on current tier
  TierMovementCriterias tier_movement_criterias = 10;
  // Display string for current AMB
  string current_amb = 11;
  // Display string for required AMB
  string required_amb = 12;
  // Display string for shortfall
  string shortfall = 13;
  // To display list of AMB history in a table format
  repeated AmbDetails amb_history = 14;
}

message TierMovementHistory {
  // Display string for user's toTier i.e user's tier after this movement
  string to_tier_plan_name = 1;
  // Display string of user's tier start timestamp
  string plan_start = 2;
  // Display string of user's tier end timestamp
  // Can be "Current Plan" in case of last movement
  string plan_end = 3;
  // Id of this movement history
  // This is a reference id to TierMovementHistory in tiering/internal/tier_movement_history.proto
  // This can be used to have actions on each movement in history table
  string id = 4;
  // Display string for user's fromTier i.e user's tier before this movement
  string from_tier_plan_name = 5;
  // Movement type(UPGRADE, DOWNGRADE etc.)
  string movement_type = 6;
}

message TierMovementCriterias {
  // criterias user is satisfying to be on current tier
  repeated string current_criterias = 1;
  // criteria through which user entered current tier
  string entry_criteria = 2;
}

message OverrideGracePeriodRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // Timestamp to override timestamp to(future timestamp or current timestamp)
  google.protobuf.Timestamp override_timestamp = 2 [(validate.rules).timestamp.required = true];
}

message OverrideGracePeriodResponse {
  rpc.Status status = 1;
  // Mandatory sherlock deeplink needed by client to do customer auth
  customer_auth.SherlockDeepLink sherlock_deep_link = 2;
}

message OverrideCoolOffPeriodRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message OverrideCoolOffPeriodResponse {
  rpc.Status status = 1;
  // Mandatory sherlock deeplink needed by client to do customer auth
  customer_auth.SherlockDeepLink sherlock_deep_link = 2;
}

message GetTieringSensitiveDetailsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetTieringSensitiveDetailsResponse {
  rpc.Status status = 1;
  // Display string for users current tier plan
  string tier_plan_name = 2;
  // Latest 'N' movements for the user
  repeated TierMovementHistory movement_histories = 3;
  // Mandatory sherlock deeplink needed by client to do customer auth
  customer_auth.SherlockDeepLink sherlock_deep_link = 4;
}

message AmbDetails {
  string dates = 1;
  string plan = 2;
  string amb_maintained = 3;
}
