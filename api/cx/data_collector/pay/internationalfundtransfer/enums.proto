// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package cx.data_collector.pay.internationalfundtransfer;

option go_package = "github.com/epifi/gamma/api/cx/data_collector/pay/internationalfundtransfer";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.pay.internationalfundtransfer";

// FileProcessingStatus tells the status of each file type that has been generated or acknowledged in the system
enum FileProcessingStatus {
  FILE_PROCESSING_STATUS_UNSPECIFIED = 0;
  FILE_PROCESSING_STATUS_INITIATED = 1;
  FILE_PROCESSING_STATUS_FAILED = 2;
  FILE_PROCESSING_STATUS_SUCCESS = 3;
}

// FileType defines the different types of files that can be generated or fetched from the system
enum FileType {
  FILE_TYPE_UNSPECIFIED = 0;
  FILE_TYPE_LRS_CHECK = 1;
  FILE_TYPE_SOF_CHECK = 2;
  FILE_TYPE_SWIFT_TRANSFER = 3;
  FILE_TYPE_REFUND_TRANSFER = 4;
  FILE_TYPE_INWARD_FUND_TRANSFER = 5;
  FILE_TYPE_LRS_REPORTING = 6;
  FILE_TYPE_TAX_TTM = 7;
  FILE_TYPE_TCS_REPORTING = 8;
  FILE_TYPE_GST_REPORTING = 9;
  // file to be generated for checking LRS limit of user pro-actively,
  // independent of order placed by order or not
  FILE_TYPE_PRO_ACTIVE_LRS_CHECK = 10;
  FILE_TYPE_GST_REPORTING_INWARD = 11;
  // file to be generated for checking lrs limit of user after usstocks onboarding process completion
  FILE_TYPE_POST_ONBOARDING_LRS_CHECK = 12;

  // Attachment to be used along with MT-199 message to distribute pooled SWIFT transfer funds among recipients
  FILE_TYPE_MT199_MESSAGE_ATTACHMENT = 13;
  // File type generated for inward fund transfer which includes only gst transactions corresponding to each inward transfer
  FILE_TYPE_INWARD_GST_TTUM = 14;
}

// ScreenType defines the additional actions that can be performed on each type of file
enum ScreenType {
  SCREEN_TYPE_UNSPECIFIED = 0;
  // screen action type that takes user to a dev action on UI to perform some input
  SCREEN_TYPE_INPUT_SCREEN = 1;
  // screen type that takes user to a screen where a file entry along with the S3 URL is shown
  SCREEN_TYPE_FILE_ENTRY = 2;
  // screen type that takes user to a new page to acknowledge a file entry
  SCREEN_TYPE_ACKNOWLEDGE = 3;
  // screen type performs LRS check for the entry
  // Client computes LRS check for the entry and refreshes LRS file entries tab
  // In case of error in computing LRS check, shows error prescribing user to retry again
  SCREEN_TYPE_LRS_CHECK = 4;
}
