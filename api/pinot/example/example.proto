syntax = "proto3";

package example;

option go_package = "github.com/epifi/gamma/api/pinot/example";
option java_package = "com.github.epifi.gamma.api.pinot.example";

// Example is a message published for persistence to a realtime pinot table
// This proto is used for hands on pinot ramp up https://docs.google.com/document/d/1b63-vd4I94nw-PnVGA_-Yt2jaArQhdBA8sJYPbZX7oA/edit#heading=h.a5x4rtx0w832
message Example {
  string id = 1 [json_name = "id"];

  string user_id = 2 [json_name = "user_id"];

  repeated string tags = 3 [json_name = "tags"];

  int64 amount = 4 [json_name = "amount"];

  int64 created_at = 5 [json_name = "created_at"];
}
