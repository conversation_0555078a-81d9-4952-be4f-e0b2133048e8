// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package redlist;

import "api/risk/internal/redlist.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/risk/redlist";
option java_package = "com.github.epifi.gamma.api.risk/redlist";

service RedList {
  // GetRedListRisk RPC is used to fetch the risk members based on categories and values
  rpc CheckRedList (CheckRedListRequest) returns (CheckRedListResponse);
  rpc UpsertRedLister (UpsertRedListerRequest) returns (UpsertRedListerResponse);
  rpc RemoveRedLister (RemoveRedListerRequest) returns (RemoveRedListerResponse);
  rpc ProcessRedListMember (ProcessRedListMemberRequest) returns (ProcessRedListMemberResponse);
}

message CheckRedListRequest {
  repeated risk.RedListPair keys = 1;
}

message CheckRedListResponse {
  rpc.Status status = 1;
  repeated risk.RedLister members = 2;
}

message UpsertRedListerRequest {
  repeated risk.RedLister members = 1;
}

message UpsertRedListerResponse {
  rpc.Status status = 1;
  repeated risk.RedLister members = 2;
}

message RemoveRedListerRequest {
  repeated risk.RedLister members = 1 [deprecated = true];
  repeated risk.RedListPair pairs = 2;
}

message RemoveRedListerResponse {
  rpc.Status status = 1;
}

message ProcessRedListMemberRequest {
  risk.RedListCategory member = 1;
  bytes csv_data = 2;
  string updated_by_email = 3;
}

message ProcessRedListMemberResponse {
  rpc.Status status = 1;
}
