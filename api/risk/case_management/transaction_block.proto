//go:generate gen_sql -types=TransactionBlock
syntax = "proto3";

package risk.case_management;

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management";
option java_package = "com.github.epifi.gamma.api.risk.case_management";

enum TransactionBlockType {
  // Default value
  TRANSACTION_BLOCK_TYPE_UNSPECIFIED = 0;

  // FIFO transaction block type
  TRANSACTION_BLOCK_TYPE_FIFO = 1;
}

// TransactionBlock represents a group of transactions that are related in some way
message TransactionBlock {
  // Unique identifier for the transaction block
  string id = 1;

  // Actor ID associated with this transaction block
  string actor_id = 2 [(validate.rules).string.min_len = 1];

  // Alert ID that this transaction block is associated with (optional)
  string alert_id = 3;

  // Total credit amount for all transactions in this block
  double aggregated_credit = 4;

  // Total debit amount for all transactions in this block
  double aggregated_debit = 5;

  // Time duration for this transaction block
  google.protobuf.Duration duration = 6;

  // Type of transaction block (e.g., "FIFO" etc.)
  TransactionBlockType block_type = 7;

  // List of transaction IDs included in this block
  repeated string transaction_ids = 8;

  // Creation timestamp
  google.protobuf.Timestamp created_at = 9;

  // Last update timestamp
  google.protobuf.Timestamp updated_at = 10;

  // Soft deletion timestamp (Unix timestamp)
  int64 deleted_at_unix = 11;
}
