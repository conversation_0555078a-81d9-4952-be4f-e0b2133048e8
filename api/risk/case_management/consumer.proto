//go:generate gen_queue_pb
syntax = "proto3";

package risk.case_management;

import "api/aws/s3/s3.proto";
import "api/cx/call_routing/event/event.proto";
import "api/cx/ticket/ticket.proto";
import "api/queue/consumer_headers.proto";
import "api/risk/case_management/alert.proto";
import "api/risk/case_management/enums/enums.proto";
import "api/risk/case_management/form/response.proto";
import "api/risk/case_management/risk_case.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management";
option java_package = "com.github.epifi.gamma.api.risk/case_management";

service RiskCaseManagementConsumer {
  rpc AddCases (RiskCasesIngestEvent) returns (AddCasesResponse);
  // RPC to ingest alerts into case-management from different systems like DS
  // Accepts batched requests and handles errors internally
  rpc AddAlerts (FrmIngestAlertsEvent) returns (AddAlertsResponse);
  // RPC to process risk form response from user.
  // Processing involves storing response in db and triggering form submission event.
  rpc ProcessFormSubmission (FormSubmissionEvent) returns (ProcessFormSubmissionResponse);
  // RPC will consume event published by cx call routing service and process risk specific routing events
  // This will take care of risk specific handling needed for a particular routing event
  // Ex: resending comms if user is reaching out via call and we are routing them to a call recording
  rpc ProcessCallRoutingEvent (cx.call_routing.event.CallRoutingEvent) returns (ProcessCallRoutingEventResponse);
  // ProcessBatchRuleEngineEvent will consume rule engine event for rule hits.
  // Rule engine periodically stores rule hits in s3 bucket as csv files.
  // New file creation triggers event and event is published to a queue.
  // This will create alerts in case management for all such rule hits.
  rpc ProcessBatchRuleEngineEvent (BatchRuleEngineEvent) returns (ProcessBatchRuleEngineEventResponse);

  // ProcessCXTicketUpdateEvent will consume cx ticket update event
  rpc ProcessCXTicketUpdateEvent (CXTicketUpdateEvent) returns (ProcessCXTicketUpdateEventResponse);

  // ProcessTransactionBlocks will consume transaction blocks from the queue and store them in DB
  rpc ProcessRiskSignalEvent (RiskSignalIngestEvent) returns (RiskSignalIngestResponse);
}

message CXTicketUpdateEvent {
  queue.ConsumerRequestHeader request_header = 1;
  // cx ticket with updated fields
  cx.ticket.Ticket ticket = 2;
}

message ProcessCXTicketUpdateEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message RiskCasesIngestEvent {
  queue.ConsumerRequestHeader request_header = 1;
  // we will receive a batch of risk cases to be ingested with each event
  repeated RiskCase risk_cases = 2 [(validate.rules).repeated = {min_items: 1, max_items: 10}];
}

message AddCasesResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message FrmIngestAlertsEvent {
  queue.ConsumerRequestHeader request_header = 1;
  message AlertPayload {
    oneof payload {
      // Payload schema for data-science alerts
      DSAlertEvent ds_alert_event = 1;
    }
  }
  // we will receive a batch of alerts to be ingested with each event
  repeated AlertPayload alert_payload = 2 [(validate.rules).repeated = {min_items: 1, max_items: 10}];
  // originating point for the alert
  enums.Provenance provenance = 3;
}

message DSAlertEvent {
  risk.case_management.RawAlert raw_alert = 1;
}

message AddAlertsResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message FormSubmissionEvent {
  queue.ConsumerRequestHeader request_header = 1;

  string form_id = 2 [(validate.rules).string.min_len = 1];

  // list of responses submitted by user for set of questions provided
  repeated risk.case_management.form.QuestionResponse question_responses = 3;
}

message ProcessFormSubmissionResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCallRoutingEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

// BatchRuleEngineEvent will consist of rule hits for multiple rules.
message BatchRuleEngineEvent {
  queue.ConsumerRequestHeader request_header = 1;

  // Single record will be all hits from a rule.
  repeated aws.s3.Record records = 2 [json_name = "Records"];
}

message ProcessBatchRuleEngineEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

// Event for ingesting transaction blocks
message RiskSignalIngestEvent {
  queue.ConsumerRequestHeader request_header = 1;
  // List of transaction blocks to ingest
  message Alert {
    RawAlert raw_alert = 2;
    case_management.enums.Provenance provenance = 3;
  }
  Alert alert = 4;
}

// Response for transaction block ingestion
message RiskSignalIngestResponse {
  // Response header with status of the message consumption
  queue.ConsumerResponseHeader response_header = 1;
}
