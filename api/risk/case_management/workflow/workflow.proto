syntax = "proto3";

package risk.case_management.workflow;

import "api/celestial/workflow/header.proto";
import "api/risk/case_management/alert.proto";
import "api/risk/case_management/enums/enums.proto";
import "api/risk/case_management/escalation/escalation.proto";
import "api/risk/case_management/form/form.proto";
import "api/risk/case_management/review/action.proto";
import "api/risk/case_management/review/case.proto";
import "api/risk/case_management/review/enums.proto";
import "api/typesv2/common/boolean.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management/workflow";
option java_package = "com.github.epifi.gamma.api.risk.case_management.workflow";

/*
PerformReviewActionRequest defines the workflow to orchestrate an action against a case.
Workflow will invoke add action activity which will create an action in actions table, and
then it will invoke the workflow specific to the action
*/
message PerformReviewActionRequest {
  // case for which the action was taken
  case_management.review.Case case = 1;
  // Different actions can be performed against a case like pass_account, freeze_account, request_more_info etc
  // This field represents the type of action that was taken on the case
  risk.case_management.review.ActionType action_type = 2;
  // For each action type we will have some action specific parameters
  risk.case_management.review.ActionParameters parameters = 3;
  // This is to identify from where the action was taken,
  risk.case_management.review.ActionSource source = 4;
  // email of analyst taking the action
  string analyst_email = 5;
  // time at which action was initiated from the source system
  google.protobuf.Timestamp initiated_at = 6;
}

message FullFreezeRequest {
  // case for which the action was taken, logging purposes
  string case_id = 1;
  // additional parameters for action request
  risk.case_management.review.AccountFreezeParameters parameters = 2;
  // email of analyst taking the action
  string analyst_email = 3;
  string actor_id = 4;
}

message UnfreezeRequest {
  // case for which the action was taken, logging purposes
  string case_id = 1;
  // additional parameters for action request
  risk.case_management.review.AccountFreezeParameters parameters = 2;
  // email of analyst taking the action
  string analyst_email = 3;
  string actor_id = 4;
}


message CreditFreezeRequest {
  // case for which the action was taken, logging purposes
  // deprecated in favor of case
  string case_id = 1;
  // additional parameters for action request
  risk.case_management.review.AccountFreezeParameters parameters = 2;
  // email of analyst taking the action
  string analyst_email = 3;
  string actor_id = 4;
  // case for which action was taken
  case_management.review.Case case = 5;
}

message DebitFreezeRequest {
  // case for which the action was taken, logging purposes
  string case_id = 1;
  // additional parameters for action request
  risk.case_management.review.AccountFreezeParameters parameters = 2;
  // email of analyst taking the action
  string analyst_email = 3;
  string actor_id = 4;
}

message PassUserRequest {
  // case for which the action was taken, logging purposes
  string case_id = 1;
  // email of analyst taking the action
  string analyst_email = 2;
  string actor_id = 3;
}

// ProcessAlertRequest will process a given raw alert and create an alert entry in db post validating the alert details
// Validation will include basic checks on alert fields as well validation for rule which triggered the alert
// Post creation of alert in database this workflow will optionally trigger case creation based on severity of the alert
message ProcessAlertRequest {
  // details of alert to be added
  RawAlert alert = 1;
}

message ProcessAlertResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

// UpsertCaseRequest workflow will create a case in CRM against an given entity
// If a case already exists for similar entity it will append the details to the same case
message UpsertCaseRequest {
  // details of entity for which case needs to be upserted
  oneof request_entity {
    AlertWithRuleDetails alert_with_rule = 1;
  }

  // field to indicate whether auto action is required on the case post upsert
  api.typesv2.common.BooleanEnum is_auto_action_required = 2;
}

message UpsertCaseResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

message CreditFreezeResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

message UnfreezeResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

message FullFreezeResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

message DebitFreezeResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

message RequestUserInfoRequest {
  // case for which the action was taken
  string case_id = 1;
  // email of analyst taking the action
  string analyst_email = 2;
  string actor_id = 3;
  review.Action action = 4;
}

message RequestUserInfoResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

message MoveToReviewRequest {
  // case for which the action was taken
  string case_id = 1;
  // email of analyst taking the action
  string analyst_email = 2;
  string actor_id = 3;
}

message MoveToReviewResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

message AddLivenessRetriesRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message AddLivenessRetriesResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

message ProcessOnboardingReviewVerdictRequest {
  string case_id = 1 [(validate.rules).string.min_len = 1];
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // final verdict for action i.e. pass or fail user onboarding
  enums.Verdict verdict = 3 [(validate.rules).enum = {not_in: [0]}];
}

message ProcessOnboardingReviewVerdictResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

// ExecuteAutoActionsRequest workflow will execute the auto action against the case.
// It will dedupe the auto action against the case and get the appropriate action after it.
// It will also add the comments against the case with the auto action details
// It will execute the auto action/actions against the case.
message ExecuteAutoActionsRequest {
  string case_id = 1 [(validate.rules).string.min_len = 1];
}

// ExecuteAutoActionsResponse returns the status of the execute auto action response and decides the next activity
// based on it.
message ExecuteAutoActionsResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

// Snoozes the case. Overwrites the snooze if already present.
// Removes snooze if snooze is not extended by another snooze request.
message SnoozeRequest {
  // case for which the action was taken
  string case_id = 1;
  // email of analyst taking the action
  string analyst_email = 2;
  string actor_id = 3;
  google.protobuf.Timestamp snooze_till = 4 [deprecated = true];

  risk.case_management.review.SnoozeParameters snooze_parameters = 5;
}

message SnoozeResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

// ProcessAFUReviewVerdictRequest applies pass or fail verdict to latest afu attempt attached to the case
// Does nothing if latest afu attempt attached to a case is not user's latest attempt.
message ProcessAFUReviewVerdictRequest {
  string case_id = 1 [(validate.rules).string.min_len = 1];
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // email of analyst taking the action
  string analyst_email = 3 [(validate.rules).string.min_len = 1];
  // verdict against latest afu attempt
  enums.Verdict verdict = 4 [(validate.rules).enum = {not_in: [0]}];
}

message ProcessAFUReviewVerdictResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

// OrchestrateFormRequest manages entire lifecycle of a form from recording in db, sending to user
// and waiting for submission signal or expiry.
// Workflow execution ends on failure, form submission or expiry.
message OrchestrateFormRequest {
  form.FormOrchestrationParams params = 1;
}

message OrchestrateFormResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;

  // Updated form after workflow completion, reflects latest status of form.
  risk.case_management.form.Form form = 2;
}

// ProcessEscalationEventRequest is request for ProcessEscalationEvent workflow
// which manages lifecycle for an escalation event.
// Workflow execution ends on failure or creation/ update of a risk case.
message ProcessEscalationEventRequest {
  risk.case_management.escalation.EscalationEvent event = 1;
}

message ProcessEscalationEventResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

message RejectEscalationRequest {
  string case_id = 1 [(validate.rules).string.min_len = 1];

  review.RejectEscalationParameters parameters = 2 [(validate.rules).message.required = true];
}

message RejectEscalationResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;
}

// GenerateFormRequest generates a form for given FormCreationParams
// Post form creation, moving the status to terminal STATUS_SENT etc. states should be handled by caller
// OrchestrateForm can be used to manage whole lifecycle
message GenerateFormRequest {
  // using form orchestration params to generate form
  // as a compatible way for general form activities
  form.FormOrchestrationParams params = 1;
}

message GenerateFormResponse {
  enum Status {
    // workflow execution was successful
    // no tech or business failure within workflow
    OK = 0;

    // business failure occurred within workflow
    // non-retryable error
    INTERNAL = 13;

    // tech failure occurred within workflow
    // retryable error
    MANUAL_INTERVENTION = 100;
  }
  // common workflow response header
  celestial.workflow.ResponseHeader response_header = 1;

  // Updated form after workflow completion, reflects latest status of form with id
  risk.case_management.form.Form form = 2;
}

// ApplyLienRequest is the request for the ApplyLien workflow
message ApplyLienRequest {
  // Request header with authentication information
  celestial.workflow.RequestHeader request_header = 1;

  // additional parameters for action request
  risk.case_management.review.AccountLienParameters parameters = 2;

  // email of analyst taking the action
  string analyst_email = 3;

  string actor_id = 4;

  // case for which action was taken
  case_management.review.Case case = 5;
}

// ApplyLienResponse is the response from the ApplyLien workflow
message ApplyLienResponse {
  // Response header with status information
  celestial.workflow.ResponseHeader response_header = 1;
}
