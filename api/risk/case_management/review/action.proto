syntax = "proto3";

package risk.case_management.review;

import "api/risk/case_management/form/question.proto";
import "api/risk/case_management/review/enums.proto";
import "api/risk/enums/enums.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management/review";
option java_package = "com.github.epifi.gamma.api.risk.case_management.review";

// When a case is registered, analyst is expected to take actions on the top of a risk case
// Actions can vary from adding annotation, adding user attributes to a negative database,
// applying verdict on a case which may result in state transition for a case.
// Action represents all such actions along with details for a case and is intended to
// act as an audit trail for a case as well.
message Action {
  // primary row id
  string id = 1;
  // case for which the action was taken
  string case_id = 2 [(validate.rules).string.min_len = 1];
  // type of review for which action was taken
  // storing this explicitly since review type can change during the lifecycle of a case
  ReviewType review_type = 3 [(validate.rules).enum = {not_in: [0]}];
  // Different actions can be performed against a case like pass_account, freeze_account, request_more_info etc
  // This field represents the type of action that was taken on the case
  ActionType action_type = 4 [(validate.rules).enum = {not_in: [0]}];
  // For each action type we will have some action specific parameters
  // Ex: For action_type FreezeAccount, we can have different freeze levels like credit_freeze, debit_freeze or total_freeze etc.
  // This action specific info will be captured as part of parameters
  ActionParameters parameters = 5;
  // This is to identify from where the action was taken,
  // it can be an analyst taking action from sherlock via review flow or bulk action by ops admin or automated system action via backend etc.
  ActionSource source = 6 [(validate.rules).enum = {not_in: [0]}];
  // email of analyst taking the action
  // this will not be present for auto actions taken by system like auto-freeze based on alert score threshold
  string analyst_email = 7;
  // Time at which action was initiated from the source system
  // Capturing initiated at field explicitly since we will be processing these actions async in a workflow and there can be delays
  // so the actual initiated_at and created_at(auto populated column in db) can be different
  google.protobuf.Timestamp initiated_at = 8;
  google.protobuf.Timestamp created_at = 9;

  google.protobuf.Timestamp updated_at = 10;

  // Actor associated with case for which action is taken.
  string actor_id = 11;

  // Current status of the action. Status update is the last step in action processing.
  ActionStatus status = 12;

  // ActionProcessingType indicates how an action on a case was processed.
  ActionProcessingType processing_type = 13;
}

message ActionParameters {
  oneof parameter {
    // parameters for ACTION_TYPE_FREEZE_ACCOUNT
    AccountFreezeParameters account_freeze_parameters = 1;
    // parameters for ACTION_TYPE_SNOOZE
    SnoozeParameters snooze_parameters = 2;
    // parameters for ACTION_TYPE_REQUEST_USER_INFO
    RequestUserInfoParameters request_user_info_parameters = 3;
    // parameters for ACTION_TYPE_REJECT_ESCALATION
    RejectEscalationParameters reject_escalation_parameters = 4;
    // parameters for ACTION_TYPE_ACCOUNT_LIEN
    AccountLienParameters account_lien_parameters = 5;
  }
}

message AccountFreezeParameters {
  // level of freeze(credit, debit or total etc) we need to do for user account
  FreezeLevel freeze_level = 1;
  // reason to change account state
  RequestReason request_reason = 2;
  // comms template to be triggered
  repeated enums.CommsTemplate comms_template = 3;
}

message AccountLienParameters {
  // reason to change account state
  RequestReason request_reason = 1;

  // the amount to be marked as lien
  google.type.Money lien_amount = 2;

  // time duration in hours for which lien is to be applied
  int32 lien_duration_in_hours = 3;

  // comms template to be triggered
  repeated enums.CommsTemplate comms_template = 4;

  string account_number = 5;
}

message SnoozeParameters {
  google.protobuf.Timestamp snooze_till = 1;

  oneof options {
    OutcallOptions outcall_options = 2;
  }

  message OutcallOptions {
    // If case is snoozed by outcall agent for failure to reach out to user,
    // a communication can be triggered reminding them of current and further outcall attempts.
    bool send_outcall_reminder_comms = 2;
  }
}

message RequestUserInfoParameters {
  OutcallMode mode = 1;

  oneof options {
    FormOutcallModeOptions form_mode_options = 2;
  }
}

message FormOutcallModeOptions {
  PostOutcallDefaultAction post_outcall_default_action = 1;

  form.QuestionIdentifiers identifiers = 2;

  google.protobuf.Timestamp expires_at = 3;
}

message PostOutcallDefaultAction {
  // default action to be performed on the case if outcall is unsuccessful
  review.ActionType action_type = 1 [(validate.rules).enum = {in: [1, 2, 5]}];
  // additional action parameters if required
  review.ActionParameters action_parameters = 2;
}

// Reason for which request was entered onto our system
message RequestReason {
  // Specifies reason for which action is being taken
  enums.RequestReason reason = 1;
  // remarks should be non empty if reason is other
  string remarks = 2;
}

message ActionProcessingParams {
  Action action = 1 [(validate.rules).message.required = true];
  ActionProcessingType processing_type = 2 [(validate.rules).enum = {not_in: [0]}];
}

message RejectEscalationParameters {
  // Rejection reason can be used to take decision on future escalations.
  string reason = 1;
}
