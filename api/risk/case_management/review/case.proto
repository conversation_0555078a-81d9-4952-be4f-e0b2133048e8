syntax = "proto3";

package risk.case_management.review;

import "api/risk/case_management/enums/enums.proto";
import "api/risk/case_management/review/enums.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/sort.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management/review";
option java_package = "com.github.epifi.gamma.api.risk.case_management.review";

/*
Rule: Rules allow you to define alert conditions based on signals a.k.a user behavioral data and trigger an alert.

Alert: For any Rule trip an alert will be generated, Alert typically have a score(1-100) or priorities(Low/medium/high) attached to them.

Case:
- A case represents an entity that requires a risk assessment.
- Based on thresholds on alert scores or priorities, a case may or may not be created.
- Although the risk profile of any actor can be viewed, a formal risk assessment should be tied with a case for tracking and audit purposes.

For more details on how an alert is converted to a case, refer case creation workflow section in the design doc:
https://docs.google.com/document/d/1J58p4C3QEFZCw6fBXJ1_c-vmsium6lotg7BVX2YNODs/edit#bookmark=id.1pfihdcv71el
*/
message Case {
  // Unique ID of the case, this will be generated in CRM tool
  string id = 1;

  Priority priority = 2;

  Status status = 3;

  // email of the analyst to whom the case has been assigned
  string assigned_to = 4;

  // will be set to true if the case was created as part of sampling in rules
  api.typesv2.common.BooleanEnum is_sample = 5;

  string actor_id = 6;

  ReviewType review_type = 7;

  google.protobuf.Timestamp created_at = 8;

  google.protobuf.Timestamp updated_at = 9;

  // field to indicate what was the final resolution for the case
  risk.case_management.enums.Verdict verdict = 10;
  // We will be using it to add tags required for assignment like
  // Rule names, Batch names, Queue types etc
  repeated string tags = 11;

  // Ticket will be marked on sleep/hold till expiry and will have limited visibility
  // Snooze will also be removed if case status or assigned agent changes
  google.protobuf.Timestamp snoozed_till = 12;

  // Confidence score is a mix of score calculated from rules triggered and user risk score.
  // It can be used to prioritize the case.
  float confidence_score = 13;

  // it defines which user is worked on the ticket previously. It will help in determining who we should assign the
  // ticket once it moved back from out-call or other flow. It typically happens when case is moved around to
  // different agents in its lifecycle.
  string last_assigned_analyst_email = 14;

  // Analyst group to which case is assigned.
  // Group can be used for access control of the case.
  // Case can be assigned to a group where all analysts in the group will have access.
  AnalystGroup analyst_group = 15;

  float model1_score = 16;
  string model1_name = 17;
  float model2_score = 18;
  string model2_name = 19;

  string model_selected = 20;
}

// CaseFilter will wrap all the filters available while fetching the list of cases
// For repeated field we will apply "OR" conditions between values of same field
// Between different fileds "AND" condition will be applied
// EX: if statuses value is [ASSIGNED, IN_REVIEW] and assigned_to_email is set to "<EMAIL>" the filter condition will be
// (status == ASSIGNED || status == IN_REVIEW) && assigned_to_email = <EMAIL>
message CaseFilters {
  // filter to be used for fetching cases assigned to a particular analyst
  string assigned_to_email = 1;

  // filter to be used to only fetch cases with given list of statuses
  repeated Status statuses = 2;

  // filter to be used to only fetch cases with given list of priorities
  repeated Priority priorities = 3;

  // filter to be used to only fetch cases with the given list of review types
  repeated ReviewType review_types = 4;

  // True - if response should have only snoozed tickets
  // False - If response should not have snoozed tickets
  // Unspecified - If response should include both
  api.typesv2.common.BooleanEnum snoozed = 5;

  // filter to be used to only fetch cases with given list of tags
  repeated string tags = 7;
}

// sort by can be used to sort cases by a field with specific order.
message CaseSortBy {
  CaseFieldMask field_mask = 1 [(validate.rules).enum = {not_in: [0]}];

  SortOrder order = 2 [deprecated = true];

  api.typesv2.common.SortOrder sort_order = 3 [(validate.rules).enum = {not_in: [0]}];
}

// We can have dedupe logic on different fields for case and alert based on the strategy used
// EX: We can choose to dedupe all alert at an actor level(or account level) and create a single case for all the alerts against a user
// Or we can group the alerts by user and type of review required etc
// DedupeParams message will wrap different possible group of dedupe fields to be used in different fields
message DedupeParams {
  message ActorIdAccountIdReviewType {
    string actor_id = 1;

    string account_id = 2;

    ReviewType review_type = 3;
  }

  message ActorIdReviewType {
    string actor_id = 1;

    ReviewType review_type = 2;
  }

  oneof dedupe_identifier {
    // Dedupe will be done at actor, account and review type level
    ActorIdAccountIdReviewType actor_id_account_id_review_type = 1;

    // Dedupe will be done at actor and review type level
    ActorIdReviewType actor_id_review_type = 2;
  }
}

message SortableCaseFilters {
  string actor_id = 1;
}

message CaseReviewLevel {
  bool is_active = 1;
  // indicates what review type should be taken against a case
  risk.case_management.enums.CaseReviewLevel case_review_level = 2;
}

// Optional fields to create case
message CaseBuilderOptions {
  // will be set to true if the case was created as part of sampling in rules
  bool is_sample = 1;
  // field indicating whether auto action will be done for the given case
  bool is_auto_action = 2;
  // review level for case
  CaseReviewLevel case_review_level = 3;
}

// optional fields to get case
message GetCaseOption {
  oneof option {
    // acceptable freshness of the case in response
    risk.case_management.enums.DataFreshness data_freshness = 1;
  }
}
