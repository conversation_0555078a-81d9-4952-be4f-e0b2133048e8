//go:generate gen_sql -types=AlertHandlingReason,AlertHandlingType
syntax = "proto3";

package risk.case_management.enums;

/*
This package will be used for all the common enums which are being used by multiple sub-packages under case_management to avoid cyclic dependencies
*/
option go_package = "github.com/epifi/gamma/api/risk/case_management/enums";
option java_package = "com.github.epifi.gamma.api.risk.case_management.enums";

// Each review or investigation of a case needs to result in one of the given verdict values
enum Verdict {
  VERDICT_UNSPECIFIED = 0;
  // Entity is deemed to be non-fraudulent and will chose to have similar access
  VERDICT_PASS = 1;
  // Entity is deemed to be fraudulent and will have restricted access
  VERDICT_FAIL = 2;
}

// In context of case management we can store and access different information at different levels
// Ex: We can have information like alerts, annotation, actions etc which we might want to fetch against different levels like
// case, entity, account or actor level etc
// Adding a generic information level enum to be used in different rpcs which requires this filter
enum InformationLevel {
  INFORMATION_LEVEL_UNSPECIFIED = 0;
  // to be used for fetching information only against case directly
  INFORMATION_LEVEL_CASE = 1;
  // to be used for fetching information against flagged entity
  INFORMATION_LEVEL_ENTITY = 2;
  // to be used for fetching information against flagged account
  INFORMATION_LEVEL_ACCOUNT = 3;
  // to be used for fetching information against flagged actor
  INFORMATION_LEVEL_ACTOR = 4;
}

// EntityType will indicate against which entity a alert is raised
// we can have different rules assessing different properties of user in the system
// Ex: we can have rule which are just assessing each transaction happening in the system vs a rule(model) which is assessing liveness video of a user
// based on what is being assessed an alert will be raised against a particular entity type
// Whatever has 1:n mapping with the user/account can be a separate entity. Others are just properties and the user entity for example can be tagged here.
enum EntityType {
  ENTITY_TYPE_UNSPECIFIED = 0;

  ENTITY_TYPE_USER = 1;

  ENTITY_TYPE_TRANSACTION = 2;

  ENTITY_TYPE_LIVENESS = 3;

  ENTITY_TYPE_AFU = 4;

  ENTITY_TYPE_SCREENER = 5;

  ENTITY_TYPE_ESCALATION = 6;

  ENTITY_TYPE_TRANSACTION_BLOCK = 7;
}

enum AccountType {
  ACCOUNT_TYPE_UNSPECIFIED = 0;

  ACCOUNT_TYPE_SAVING_BANK = 1;
}

// In what system the rule is running and alert was generated from
enum Provenance {
  PROVENANCE_UNSPECIFIED = 0;
  // dronapay is a rule engine we are using for real time transaction monitoring
  PROVENANCE_DRONAPAY = 1;
  // DS model running on live onboarding/reonboarding events as well as back runs against set of users
  PROVENANCE_DS_RISK_MODEL = 3;
  // manual and automated query runs on snowflake
  PROVENANCE_SNOWFLAKE_RULES = 4;
  // When a crime is committed, victims approach the law enforcement agencies(Cyber police) who then forward the details to the banking partner
  // e.g., Federal bank. Banking partners will then forward either partial details or full data to us.
  PROVENANCE_LEA = 5;
  // Internal risk screener e.g. onboarding internal risk checks.
  PROVENANCE_RISK_SCREENER = 6;
  // Escalation including CX incoming
  PROVENANCE_ESCALATION = 7;
  // Data Analytics rules running in big query
  PROVENANCE_DATA_ANALYTICS = 8;
}

// enum to indicate what type of action needs to be performed for a given alert
// action level can be factor of different attributes like system/rule which generated the alert, rule status or score etc
enum AlertActionLevel {
  ALERT_ACTION_LEVEL_UNSPECIFIED = 0;
  // No action is needed for the alert
  ALERT_ACTION_LEVEL_NONE = 1;
  // A case needs to be created and manual review is required
  ALERT_ACTION_LEVEL_MANUAL_REVIEW = 2;
  // A case needs to be created and user can be autoblocked
  ALERT_ACTION_LEVEL_AUTO_BLOCK = 3 [deprecated = true];
  // A case needs to be created and an auto-action needs to be performed on the case
  // action to be peformed can depend on multiple factors like rule, score stc and will be derived separately
  ALERT_ACTION_LEVEL_AUTO_ACTION = 4;
}

// enum to indicate what review type should be taken against a case
// case creation could be rejected, passed on certain params depending on this enum
enum CaseReviewLevel {
  CASE_ACTION_LEVEL_UNSPECIFIED = 0;
  // should create a case with won't review status
  CASE_ACTION_LEVEL_CREATE_WONT_REVIEW_CASE = 1;
  // should ignore case
  CASE_ACTION_LEVEL_IGNORE_CASE = 2;
}

// common enum to be used in cases where data freshness needs to be decided by clients for certain data
enum DataFreshness {
  DATA_FRESHNESS_UNSPECIFIED = 0;
  // makes a fresh vendor call
  DATA_FRESHNESS_REAL_TIME = 1;
  // data is fetched from db if it not older than x mins, otherwise makes a vendor call
  // worst case staleness could be upto 10 mins
  DATA_FRESHNESS_NEAR_REAL_TIME = 3;
  // data is fetched from db if it is not older than x days, otherwise makes a vendor call
  // worst case staleness could be upto a week
  DATA_FRESHNESS_RECENT = 2;
  // data will be fetched from db only, return record not exist if data unavailable
  DATA_FRESHNESS_LAST_KNOWN = 4;
}

// AlertHandlingReason specifies how alert can be handled/processed after creation.
enum AlertHandlingType {
  // No action is needed for the alert
  ALERT_HANDLING_TYPE_UNSPECIFIED = 0;
  // No handling is required for alert and alert processing will terminate after alert creation.
  ALERT_HANDLING_TYPE_NONE = 1;
  // A case needs to be created and manual review is required
  ALERT_HANDLING_TYPE_SEND_FOR_REVIEW = 2;
  // A case needs to be created and an auto-action needs to be performed on the case
  // action to be peformed can depend on multiple factors like rule, score stc and will be derived separately
  ALERT_HANDLING_TYPE_AUTO_ACTION = 3;
  // Alert needs be appended to existing case since manual review is not required.
  // Review can be skipped for reasons such as account frozen or freeze action is in progress.
  ALERT_HANDLING_TYPE_SKIP_REVIEW = 4;
}

// AlertHandlingReason specifies reasons for alert handling type.
enum AlertHandlingReason {
  ALERT_HANDLING_REASON_UNSPECIFIED = 0;
  // Rule is marked inactive.
  // If a rule is inactive, no alerts will be generated for it.
  ALERT_HANDLING_REASON_RULE_INACTIVE = 1 [deprecated = true];
  // If rule confidence is below a predefined threshold.
  ALERT_HANDLING_REASON_LOW_CONFIDENCE_RULE = 2;
  // If alert needs to be sent for manual review
  ALERT_HANDLING_REASON_ELIGIBLE_FOR_REVIEW = 3;
  // A rule can be in cool off for a duration after initial trigger.
  ALERT_HANDLING_REASON_RULE_IN_COOL_OFF = 4;
  // If user account is already frozen
  ALERT_HANDLING_REASON_ACCOUNT_FROZEN = 5;
  // If a freeze action is taken and account freeze is pending.
  ALERT_HANDLING_REASON_FREEZE_ACTION_IN_PROGRESS = 6;
  // If auto action is enabled for the rule.
  ALERT_HANDLING_REASON_AUTO_ACTION_ENABLED = 7;
  // if user present in whitelist
  ALERT_HANDLING_REASON_USER_IS_IN_WHITELIST = 8;
  // if rule state marked as shadow
  ALERT_HANDLING_REASON_RULE_IN_SHADOW = 9;
  // if actor id is external
  ALERT_HANDLING_REASON_EXTERNAL_ACTOR = 10;
}
