//go:generate gen_sql -types=Status,FormOrigin,EntityType,QuestionType,FileContentType

syntax = "proto3";

package risk.case_management.form;

option go_package = "github.com/epifi/gamma/api/risk/case_management/form";
option java_package = "com.github.epifi.gamma.api.risk.case_management.form";

// Current status of the form sent/to be sent to the user.
enum Status {
  STATUS_UNSPECIFIED = 0;

  // Form is recorded in db.
  STATUS_CREATED = 1;

  // Form is sent to the user.
  STATUS_SENT = 2;

  // Response is submitted by user and submission is in process at backend.
  STATUS_PROCESSING_SUBMISSION = 4;

  // Response to all mandatory questions is submitted by user.
  STATUS_SUBMITTED = 5;

  // The Form has timed out without a user response and user can no longer respond.
  STATUS_EXPIRED = 6;

  // The form has been cancelled due to some reason.
  STATUS_CANCELLED = 7;
}

// Form generation origin point.
enum FormOrigin {
  FORM_ORIGIN_UNSPECIFIED = 0;

  // If triggered by agent during manual review for outcall.
  FORM_ORIGIN_MANUAL_REVIEW_OUTCALL = 1;

  // If triggered by freeze/ unfreeze flows affecting operational status
  FORM_ORIGIN_ACCOUNT_OPERATIONAL_STATUS_UPDATE = 2;
}

enum EntityType {
  ENTITY_TYPE_UNSPECIFIED = 0;

  ENTITY_TYPE_QUESTIONNAIRE_TEMPLATE = 1;
}

enum QuestionType {
  QUESTION_TYPE_UNSPECIFIED = 0;
  QUESTION_TYPE_TEXT = 1;
  QUESTION_TYPE_FILE = 2;
  QUESTION_TYPE_MULTI_CHOICE = 3;
}

enum FileContentType {
  FILE_CONTENT_TYPE_UNSPECIFIED = 0;

  FILE_CONTENT_TYPE_JPEG = 1;

  FILE_CONTENT_TYPE_PDF = 2;

  FILE_CONTENT_TYPE_PNG = 3;
}

enum QuestionResponseFieldMask {
  QUESTION_RESPONSE_FIELD_MASK_UNSPECIFIED = 0;
  QUESTION_RESPONSE_FIELD_MASK_ALL = 1;
  QUESTION_RESPONSE_FIELD_MASK_ID = 2;
  QUESTION_RESPONSE_FIELD_MASK_FORM_ID = 3;
  QUESTION_RESPONSE_FIELD_MASK_QUESTION_ID = 4;
  QUESTION_RESPONSE_FIELD_MASK_RESPONSE = 5;
  QUESTION_RESPONSE_FIELD_MASK_CREATED_AT = 6;
  QUESTION_RESPONSE_FIELD_MASK_UPDATED_AT = 7;
  QUESTION_RESPONSE_FIELD_MASK_DELETED_AT = 8;
}

enum FormFieldMask {
  FORM_FIELD_MASK_UNSPECIFIED = 0;
  FORM_FIELD_MASK_ALL = 1;
  FORM_FIELD_MASK_ID = 2;
  FORM_FIELD_MASK_CASE_ID = 3;
  FORM_FIELD_MASK_ACTOR_ID = 4;
  FORM_FIELD_MASK_STATUS = 5;
  FORM_FIELD_MASK_ADDED_BY = 6;
  FORM_FIELD_MASK_CLIENT_REQ_ID = 7;
  FORM_FIELD_MASK_ORIGIN = 8;
  FORM_FIELD_MASK_WORKFLOW_REQ_ID = 9;
  FORM_FIELD_MASK_CREATED_AT = 10;
  FORM_FIELD_MASK_UPDATED_AT = 11;
  FORM_FIELD_MASK_DELETED_AT = 12;
  FORM_FIELD_MASK_EXPIRE_AT = 13;
}

enum QuestionFieldMask {
  QUESTION_FIELD_MASK_UNSPECIFIED = 0;
  QUESTION_FIELD_MASK_ALL = 1;
  QUESTION_FIELD_MASK_ID = 2;
  QUESTION_FIELD_MASK_CODE = 3;
  QUESTION_FIELD_MASK_TEXT = 4;
  QUESTION_FIELD_MASK_TIP = 5;
  QUESTION_FIELD_MASK_DESCRIPTION = 6;
  QUESTION_FIELD_MASK_TYPE = 7;
  QUESTION_FIELD_MASK_OPTIONS = 8;
  QUESTION_FIELD_MASK_VERSION = 9;
  QUESTION_FIELD_MASK_CREATED_AT = 10;
  QUESTION_FIELD_MASK_UPDATED_AT = 11;
  QUESTION_FIELD_MASK_DELETED_AT = 12;
  QUESTION_FIELD_MASK_PLACEHOLDER = 13;
}

enum FormQuestionMappingFieldMask {
  FORM_QUESTION_MAPPING_FIELD_MASK_UNSPECIFIED = 0;
  FORM_QUESTION_MAPPING_FIELD_MASK_ALL = 1;
  FORM_QUESTION_MAPPING_FIELD_MASK_ID = 2;
  FORM_QUESTION_MAPPING_FIELD_MASK_FORM_ID = 3;
  FORM_QUESTION_MAPPING_FIELD_MASK_QUESTION_CODE = 4;
  FORM_QUESTION_MAPPING_FIELD_MASK_IS_MANDATORY = 5;
  FORM_QUESTION_MAPPING_FIELD_MASK_CREATED_AT = 6;
  FORM_QUESTION_MAPPING_FIELD_MASK_UPDATED_AT = 7;
  FORM_QUESTION_MAPPING_FIELD_MASK_DELETED_AT = 8;
}

enum ExtendedFormFieldMask {
  EXTENDED_FORM_FIELD_MASK_UNSPECIFIED = 0;
  // Included form, questions and responses(if submitted).
  EXTENDED_FORM_FIELD_MASK_ALL = 1;
  EXTENDED_FORM_FIELD_MASK_FORM = 2;
  // If only questions are needed.
  EXTENDED_FORM_FIELD_MASK_QUESTIONS = 3;
  // If only responses are needed.
  EXTENDED_FORM_FIELD_MASK_RESPONSES = 4;
}

// Prebuild questionnaire templates.
enum QuestionnaireTemplate {
  QUESTIONNAIRE_TEMPLATE_UNSPECIFIED = 0;
  QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_AMOUNT = 1;
  QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT = 2;
  QUESTIONNAIRE_TEMPLATE_HIGH_CREDIT_COUNT_AND_AMOUNT = 3;
  QUESTIONNAIRE_TEMPLATE_STOP_BUSINESS_ACTIVITY_ACK = 4;
  QUESTIONNAIRE_TEMPLATE_INCOME_DISCREPANCY = 5;
  QUESTIONNAIRE_TEMPLATE_CFR_BLOCKED_USER_REVIEW = 6;
  QUESTIONNAIRE_TEMPLATE_CFR_BLOCKED_TRANSACTION_REVIEW = 7;
}
