//go:generate gen_sql -types=AlertHandling
syntax = "proto3";

package risk.case_management;

import "api/risk/case_management/enums/enums.proto";
import "api/risk/case_management/review/action.proto";
import "api/risk/case_management/rule.proto";
import "api/risk/case_management/transaction_block.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management";
option java_package = "com.github.epifi.gamma.api.risk.case_management";

/*
For any Rule trip an alert will be generated
we will have an alert for each rule trip in different system
*/
message Alert {
  // UUID, primary id in alerts table
  string id = 1;

  // id of the linked case in CRM tool
  string case_id = 2;

  // rule which triggered the alert
  string rule_id = 3 [(validate.rules).string.min_len = 4];
  // optional field to tag an alert to a particular batch
  // When running a rule on historical data, we can run it on a smaller set/range of data in batches.
  // To differentiate between different runs of the same rule, we will make use of a batch name which should help in linking an alert/case to a particular batch.
  string batch_name = 4;

  // identifier for actor and entity which was flagged by the rule
  string actor_id = 5;
  string account_id = 6;
  enums.EntityType entity_type = 7 [(validate.rules).enum = {not_in: [0]}];
  string entity_id = 8 [(validate.rules).string.min_len = 4];


  // field to indicate what was the final resolution for the case
  enums.Verdict verdict = 9;

  google.protobuf.Timestamp created_at = 10;

  google.protobuf.Timestamp updated_at = 11;

  enums.AccountType account_type = 12;

  // Initiation timestamp of the alert. Alert initiation point can differ for different case creation pipelines.
  // e.g. for live queues initiation timestamp is triggering of a rule and raw alert creation.
  // for bulk upload, it is file upload timestamp.
  google.protobuf.Timestamp initiated_at = 13;

  // Indicates how alert was handled after creation. e.g. for low confidence rules only alert will be created
  // and no further action is required.
  enums.AlertHandlingType handling_type = 14;

  // Specifies reasons for handling method of alert. e.g., Low confidence rule is one of the reasons
  // for not doing any handling of alert after creation.
  repeated enums.AlertHandlingReason handling_reasons = 15;

  // field to capture precision score of the rule which triggered the alert,
  // this is needed to keep a historical track of precision for analytics and debugging since rule precision can change over time.
  // If 'force use seed precision' is true, the rule's seed precision is captured; otherwise, the calculated precision is used.
  float rule_precision = 16 [(validate.rules).float = {gte: 0.0, lte: 1.0}];

  // meta details for alert like transaction blocks details and others
  AlertMetaDetails meta_details = 17;
}

message AlertMetaDetails {
  repeated TransactionBlock transaction_blocks = 1;
}

// Object to be used in alert ingestion flow
// this contains fields which needs to be passed during ingestion of alerts which is different from Alert object,
// hence creating a separate object
// Raw object will mostly contain details available in different risk evaluator systems.
// After receiving the raw alert we can normalize the details, add more details etc which would result in Alert object above
message RawAlert {
  RuleIdentifier identifier = 1;
  // OPTIONAL field to tag an alert to a particular batch
  // When running a rule on historical data, we can run it on a smaller set/range of data in batches.
  // To differentiate between different runs of the same rule, we will make use of a batch name which should help in linking an alert/case to a particular batch.
  string batch_name = 2;

  // identifier for actor and entity which was flagged by the rule
  // entity_type and entity id are mandatory, actor_id and account_id can be null based on use case
  string actor_id = 3;
  string account_id = 4;
  enums.EntityType entity_type = 5;
  string entity_id = 6;

  // Initiation point can differ for different case creation pipelines.
  // e.g. for live queues initiation timestamp is triggering of a rule and raw alert creation.
  // for bulk upload, it is file upload timestamp.
  google.protobuf.Timestamp initiated_at = 7;

  // meta details for alert like transaction blocks details and others
  AlertMetaDetails meta_details = 8;
}

// Wrapper message to be used where alerts details are needed
// along with details of Rule which triggered the rule
message AlertWithRuleDetails {
  Alert alert = 1;
  // details of the rule which triggered the alert
  // Rule is deprecated in favour of extended rule
  Rule rule = 2 [deprecated = true];
  // details of the rule which triggered the alert and additional fields related to the rule
  ExtendedRule extended_rule = 3;
}

// AlertFieldMask is the enum representation of all the Rule fields.
// Meant to be used as field mask to help with database updates
enum AlertFieldMask {
  ALERT_FIELD_MASK_UNSPECIFIED = 0;
  ALERT_FIELD_MASK_ALL = 1;
  ALERT_FIELD_MASK_ID = 2;
  ALERT_FIELD_MASK_CASE_ID = 3;
  ALERT_FIELD_MASK_ACTOR_ID = 4;
  ALERT_FIELD_MASK_ACCOUNT_TYPE = 5;
  ALERT_FIELD_MASK_ACCOUNT_ID = 6;
  ALERT_FIELD_MASK_ENTITY_TYPE = 7;
  ALERT_FIELD_MASK_ENTITY_ID = 8;
  ALERT_FIELD_MASK_RULE_ID = 9;
  ALERT_FIELD_MASK_BATCH_NAME = 10;
  ALERT_FIELD_MASK_VERDICT = 11;
  ALERT_FIELD_MASK_CREATED_AT = 12;
  ALERT_FIELD_MASK_UPDATED_AT = 13;
  ALERT_FIELD_MASK_HANDLING_TYPE = 14;
  ALERT_FIELD_MASK_HANDLING_REASON = 15;
  ALERT_FIELD_MASK_INITIATED_AT = 16;
  ALERT_FIELD_MASK_RULE_PRECISION = 17;
}

message AlertHandlingParams {
  enums.AlertHandlingType handling_type = 1;

  repeated enums.AlertHandlingReason handling_reasons = 2;

  oneof options {
    // Additional info for skipping review of alert.
    SkipReviewOptions skip_review_options = 3;
  }

  message SkipReviewOptions {
    // The latest fail action on the actor because of which review is being skipped for the alert.
    risk.case_management.review.Action action = 1;
  }
}

// AlertAggregateForActor is aggregates of alerts for an actor.
message AlertAggregateForActor {
  string case_id = 1;
  string rule_id = 2;
  // Alert count for aggregate conditions.
  int64 count = 3;
  // Alert creation date on which alerts are aggregated.
  google.type.Date created_at = 4;
}

message AlertAggregateForActorWithRule {
  AlertAggregateForActor aggregate = 1;
  ExtendedRule extended_rule = 2;
}
