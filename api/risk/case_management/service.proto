syntax = "proto3";

package risk.case_management;

import "api/order/order.proto";
import "api/order/payment/accounting_entry_type.proto";
import "api/risk/case_management/alert.proto";
import "api/risk/case_management/combined_annotation.proto";
import "api/risk/case_management/enums/enums.proto";
import "api/risk/case_management/form/enums.proto";
import "api/risk/case_management/form/form.proto";
import "api/risk/case_management/form/question.proto";
import "api/risk/case_management/form/response.proto";
import "api/risk/case_management/review/action.proto";
import "api/risk/case_management/review/allowed_annotation.proto";
import "api/risk/case_management/review/annotation.proto";
import "api/risk/case_management/review/case.proto";
import "api/risk/case_management/review/comment.proto";
import "api/risk/case_management/review/enums.proto";
import "api/risk/case_management/review/ui_element.proto";
import "api/risk/case_management/rule.proto";
import "api/risk/case_management/suggested_action.proto";
import "api/risk/case_management/transaction_block.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management";
option java_package = "com.github.epifi.gamma.api.risk.case_management";

// Case management service provide all the apis related to case store and case review
// It will contain Case Store related rpcs like
// - Fetching list of cases and other case related entities like alerts, rules etc
// - Other supported operations like adding, updating etc for same entities
// It will also contain RPC needed for case review like
// - Fetching Generic case related information required
// - Review type specific details like user transactions, liveness details etc depending on the type of review
// - Case action related apis for getting available actions or performing action or past actions etc
// - Annotations(fetch and add)
service CaseManagement {
  // rpc to fetch list of cases with given set of filters in paginated manner
  rpc ListCases (ListCasesRequest) returns (ListCasesResponse);

  // rpc to ingest alert from different systems into case management for processing
  rpc CreateAlerts (CreateAlertsRequest) returns (CreateAlertsResponse);

  // rpc to get fetch transaction details of the user associated with a given case_id for review in paginated format
  rpc GetTransactionDetailsForReview (GetTransactionDetailsForReviewRequest) returns (GetTransactionDetailsForReviewResponse);

  // rpc to return basic information required during review which is common to all review types
  // EX: Case details with current state, Action history so far against the case, Related cases etc
  // Case will be present in response irrespective of field mask, all other field are optional.
  // Based on the review type of a case, client can fetch additional details using other RPCs of this service
  rpc GetReviewDetails (GetReviewDetailsRequest) returns (GetReviewDetailsResponse);

  // rpc to get alerts(along with rule details) linked to a case at different levels(case, entity, account or actor etc levels)
  rpc GetLinkedAlerts (GetLinkedAlertsRequest) returns (GetLinkedAlertsResponse);

  // rpc to be used for taking an action against a case
  rpc PerformAction (PerformActionRequest) returns (PerformActionResponse);

  // rpc to take the unstructured input from the analyst against the particular entity
  // for ex. L1 analyst may want to add their free text against the particular case, user or transaction
  rpc CreateComment (CreateCommentRequest) returns (CreateCommentResponse);

  // rpc to list the comments for a particular entity
  // maximum of 100 comments will return from this rpc
  rpc ListComments (ListCommentsRequest) returns (ListCommentsResponse);

  // rpc to get configured allowed annotations
  // maximum of 100 comments will return from this rpc
  // Deprecated in favour of ListAllowedAnnotations
  rpc GetAllowedAnnotations (GetAllowedAnnotationsRequest) returns (GetAllowedAnnotationsResponse) {
    option deprecated = true;
  };

  // rpc to create the annotations
  rpc CreateAnnotation (CreateAnnotationRequest) returns (CreateAnnotationResponse);

  // rpc to get the annotations
  rpc ListAnnotations (ListAnnotationsRequest) returns (ListAnnotationsResponse);

  // rpc to create the rule if not present for external id
  // it only creates the rule if external id is present
  rpc CreateRule (CreateRuleRequest) returns (CreateRuleResponse);

  // rpc to get the rules by External Ids
  rpc ListRules (ListRulesRequest) returns (ListRulesResponse);

  // rpc to update the rule
  rpc UpdateRule (UpdateRuleRequest) returns (UpdateRuleResponse);

  // rpc to create allowed annotation entry in db
  rpc CreateAllowedAnnotation (CreateAllowedAnnotationRequest) returns (CreateAllowedAnnotationResponse);

  // rpc to update a case, case id is mandatory to update a case.
  rpc UpdateCase (UpdateCaseRequest) returns (UpdateCaseResponse);

  // GetPrioritizedCase returns the highest priority case of input review type.
  // Returns record not found if no cases are found.
  // Returns Invalid argument if analyst is not part of the group that reviews the input review type.
  rpc GetPrioritizedCase (GetPrioritizedCaseRequest) returns (GetPrioritizedCaseResponse);

  // Rpc to fetch list of cases for input filters with input sort order.
  // Returns Record Not Found status if cases are not found
  rpc ListSortedCases (ListSortedCasesRequest) returns (ListSortedCasesResponse);

  // GetFiUserRelationship is the rpc to get the user's relationship with different fi's product.
  rpc GetFiUserRelationship (GetFiUserRelationshipRequest) returns (GetFiUserRelationshipResponse);

  // GetForm returns the form with questionnaire attached.
  // It does not perform any validation such as expired or already submitted.
  // Caller can validate on form status.
  rpc GetForm (GetFormRequest) returns (GetFormResponse);

  // SubmitForm will record user responses to the form.
  // It fails if the form is already submitted, expired or does not exist.
  rpc SubmitForm (SubmitFormRequest) returns (SubmitFormResponse);

  // ListForms returns list of forms for a given query.
  // Additional data can be requested with field masks.
  rpc ListForms (ListFormsRequest) returns (ListFormsResponse);

  // GetAlerts returns list of all alerts against given actor
  rpc GetAlerts (GetAlertsRequest) returns (GetAlertsResponse);

  // GetAllTags will fetch all available rule tags
  rpc GetAllTags (GetAllTagsRequest) returns (GetAllTagsResponse);

  // CreateAnnotations creates bulk annotations
  rpc CreateAnnotations (CreateAnnotationsRequest) returns (CreateAnnotationsResponse);

  // ListAllowedAnnotations lists annotations for given filters.
  rpc ListAllowedAnnotations (ListAllowedAnnotationsRequest) returns (ListAllowedAnnotationsResponse);
  // GetFormsForActor returns the forms for an actor.
  // Performs general validation like actor id is nil or not.
  // Caller can validate on form status.
  rpc GetFormsForActor (GetFormsForActorRequest) returns (GetFormsForActorResponse);

  // CreateSuggestedActionForRule creates suggested action for rule
  rpc CreateSuggestedActionForRule (CreateSuggestedActionForRuleRequest) returns (CreateSuggestedActionForRuleResponse);

  // CreateRuleReviewTypeMapping creates mapping between rule and review type
  rpc CreateRuleReviewTypeMapping (CreateRuleReviewTypeMappingRequest) returns (CreateRuleReviewTypeMappingResponse);

  // GetTransactionBlocks returns transaction blocks for a given actor or alert ID
  rpc GetTransactionBlocks (GetTransactionBlocksRequest) returns (GetTransactionBlocksResponse);

  // CreateTransactionBlock creates a new transaction block
  rpc CreateTransactionBlock (CreateTransactionBlockRequest) returns (CreateTransactionBlockResponse);
}

message ListAllowedAnnotationsRequest {
  review.AllowedAnnotationFilters filters = 1 [(validate.rules).message.required = true];
}

message ListAllowedAnnotationsResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // No annotations found with given filters
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  oneof response {
    // For filter: UI Element. List of annotation types mapped against ui element with annotation values.
    review.UIElementAllowedAnnotations ui_element_allowed_annotations = 2;
  }
}

message CreateAnnotationsRequest {
  repeated review.Annotation annotations = 1;
}

message CreateAnnotationsResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message GetAllTagsRequest {}

message GetAllTagsResponse {
  enum Status {
    Ok = 0;
    // Tags not found
    NOT_FOUND = 5;
    // ISE response due to some internal error in the rpc
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  repeated string tags = 2;
}

message GetPrioritizedCaseRequest {
  // Analyst email is required to fetch analyst groups and infer queue from group.
  string analyst_email = 1 [(validate.rules).string.min_len = 1];

  // deprecated, use review_types going forward
  review.ReviewType review_type = 2 [deprecated = true];

  repeated review.ReviewType review_types = 3;
}

message GetPrioritizedCaseResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // Case not found for the analyst
    NOT_FOUND = 5;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;

  review.Case case = 2;
}

message UpdateCaseRequest {
  // case details which needs to be updated. Case id is mandatory.
  review.Case case = 1 [(validate.rules).message.required = true];
  // If empty field mask is passed all fields will be updated
  repeated review.CaseFieldMask update_masks = 2;
}

message UpdateCaseResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 13;
  }
  rpc.Status status = 1;

  review.Case case = 2;
}

message CreateAllowedAnnotationRequest {
  // annotation that needs to be created in the db
  review.AllowedAnnotation allowed_annotation = 1;
}

message CreateAllowedAnnotationResponse {
  enum Status {
    OK = 0;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 1;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 2;
  }
  rpc.Status status = 1;
}

message GetTransactionDetailsForReviewRequest {
  string case_id = 1 [(validate.rules).string.min_len = 1];
  // field by which transactions needs to be sorted
  order.OrderFieldMask sort_by = 2;
  // set of filters to be applied in addition to actor_id while fetching the transactions.
  TransactionFilters filters = 3;
  rpc.PageContextRequest page_context = 6;
}

message GetTransactionDetailsForReviewResponse {
  enum Status {
    OK = 0;
    // ISE resposne due to some internal error in the rpc
    INTERNAL_SERVER_ERROR = 1;
    // Record not found for the given identifier
    RECORD_NOT_FOUND = 2;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;

  repeated TransactionDetail transaction_details = 2;

  rpc.PageContextResponse page_context = 5;
}

// TransactionDetail stores the details required for reviewing a user's transactions. It can have order, transaction and other details in addition like category, tripped rules and alert details etc.
message TransactionDetail {
  order.OrderWithTransactions order_with_transactions = 1;
  // merchant_details are available only for p2m transactions
  MerchantDetails merchant_details = 2;
  // type of receiver (transaction is p2p or p2m)
  string receiver_type = 4;
  // type of transaction (credit/debit)
  string accounting_entry = 5;
}

message MerchantDetails {
  string mcc_code = 1;
  string merchant_name = 2;
}

message ListCasesRequest {
  // list of supported filters
  review.CaseFilters filters = 1;
  // max supported page size is 100
  rpc.PageContextRequest page_context = 2;
}

message ListCasesResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // No cases found with given filters
    RECORD_NOT_FOUND = 5;
  }
  rpc.Status status = 1;

  repeated review.Case cases = 2;

  rpc.PageContextResponse page_context = 3;
}

message CreateAlertsRequest {
  // list of alerts to be ingested
  // at max 1000 cases can be passed in request
  repeated RawAlert alerts = 1 [(validate.rules).repeated = {min_items: 1, max_items: 1000}];
}

message CreateAlertsResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // if request validation fails
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;

  // number of alerts for which creation failed
  uint32 failure_count = 2;

  message Failure {
    // details of failed alert
    RawAlert alert = 1;
    // reason for failure
    // a string will be returned describing the failure
    string reason = 2;
  }

  repeated Failure failures = 3;
}

message TransactionFilters {
  google.protobuf.Timestamp from_time = 1;
  google.protobuf.Timestamp to_time = 2;
  repeated review.TransactionReviewOrderStatusFilter statuses = 3;
  order.payment.AccountingEntryType accounting_entry = 4;
}

message GetReviewDetailsRequest {
  // case for which review details are being fetched, Mandatory for fetching the details
  string case_id = 1 [(validate.rules).string.min_len = 1];
  // Mandatory to pass at least one field mask, if field mask list is empty will return INVALID_ARGUMENT status code
  repeated review.ReviewDetailsFieldMask field_masks = 2 [(validate.rules).repeated = {min_items: 1}];
}

message GetReviewDetailsResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // if request validation fails
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;
  // case details for case_id passed in request
  review.Case case = 2;
  // list of all the actions done against the case
  // not adding pagination support since number of actions shouldn't be very high
  // in worst case if there are many actions against the case, we will return latest 100 actions
  // If pagination is needed in future, support for new RPC with pagination will be added to fetch other actions
  repeated review.Action actions = 3;

  // list of all the present/past cases against actor related to current case
  // pagination is not supported currently since number of cases against an actor should be very less
  // in worst case if there are many cases against an actor we will return latest 100 cases
  repeated review.Case related_cases = 4;

  // list of all the alerts(along with rule details) linked to the given case
  // pagination is not supported currently since number of alerts linked to a case should be fairly low
  // in worst case if there are many alerts linked to a case we will return latest 100 cases
  repeated AlertWithRuleDetails alerts = 5;

  // firehose id of the actor corresponding to the case
  // will be only populated for transaction review type
  string firehose_id = 6;

  // Aggregated alerts for an actor in the past 180 days.
  // A Maximum of 100 alert aggregates will be sent in response.
  repeated AlertAggregateForActorWithRule alert_aggregates_for_actor_with_rule = 7;
}

message GetLinkedAlertsRequest {
  // Identifier for which alerts are being fetched
  // we are only supporting case_id identifier here for now since we want to restrict access to this information without a case present
  // rest of the identifiers for different levels will be derived from case and underlying alert details
  oneof identifier {
    string case_id = 1;
  }

  // level at which alerts are required, ex: all alerts against a case vs against an actor
  enums.InformationLevel information_level = 2;

  // max supported page size is 50
  rpc.PageContextRequest page_context = 3;
}

message GetLinkedAlertsResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // No alerts are found with given filters
    RECORD_NOT_FOUND = 5;
  }
  rpc.Status status = 1;

  repeated AlertWithRuleDetails alerts = 2;

  rpc.PageContextResponse page_context = 3;
}

message PerformActionRequest {
  // case against which action is being taken
  string case_id = 1 [(validate.rules).string.min_len = 1];
  // type of action to be performed on the case
  review.ActionType action_type = 2 [(validate.rules).enum = {not_in: [0]}];
  // addtion action parameters if required
  review.ActionParameters action_parameters = 3;
  // source for the action
  review.ActionSource source = 4 [(validate.rules).enum = {not_in: [0]}];
  // email of analyst, mandatory for actions from source as review_flow
  string analyst_email = 5 [(validate.rules).string.min_len = 1];
}

message PerformActionResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // if request validation fails
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;
}

message CreateCommentRequest {
  review.Comment comment = 1;
}

message CreateCommentResponse {

  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message ListCommentsRequest {

  review.CommentQuery query = 1 [deprecated = true];

  review.CommentFilters comment_filters = 2;
}

message ListCommentsResponse {

  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // Record not found for the given identifier
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  repeated review.Comment comments = 2;
}

message GetAllowedAnnotationsRequest {
  review.AllowedAnnotationQuery query = 1;
}

message GetAllowedAnnotationsResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // No cases found with given filters
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  repeated review.AllowedAnnotation allowed_annotations = 2;
}

message CreateAnnotationRequest {
  review.Annotation annotation = 1;
}

message CreateAnnotationResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // if request validation fails
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;
}

message ListAnnotationsRequest {
  AnnotationQuery query = 1 [deprecated = true];

  AnnotationFilters annotation_filters = 2;
}

message ListAnnotationsResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // No annotations are found with given filters
    RECORD_NOT_FOUND = 5;
  }
  rpc.Status status = 1;

  repeated CombinedAnnotation annotations = 2;
}

message CreateRuleRequest {
  Rule rule = 1;
}

message CreateRuleResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message ListRulesRequest {
  repeated string externalIds = 1 [deprecated = true];

  oneof filter_by_value {
    string external_id = 2;
    string rule_name = 3;
    RuleGroup rule_group = 4;
  }

  // accepted rule field masks:
  // RULE_FIELD_MASK_EXTERNAL_ID
  // RULE_FIELD_MASK_NAME
  // RULE_FIELD_MASK_RULE_GROUP
  risk.case_management.RuleFieldMask filter_by = 5;
}

message ListRulesResponse {
  enum Status {
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // No cases found with given filters
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  repeated Rule rules = 2;
}

message UpdateRuleRequest {
  Rule rule = 1;

  repeated RuleFieldMask update_masks = 2;
}

message UpdateRuleResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message ListSortedCasesRequest {
  // supported filters
  review.SortableCaseFilters filters = 1;
  // field to sort cases on and sorting order
  review.CaseSortBy sort_by = 2 [(validate.rules).message.required = true];
  // max supported page size is 100
  rpc.PageContextRequest page_context_request = 3 [(validate.rules).message.required = true];
}

message ListSortedCasesResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // No cases found with given filters
    RECORD_NOT_FOUND = 5;
  }
  rpc.Status status = 1;

  repeated review.Case cases = 2;

  rpc.PageContextResponse page_context_response = 3;
}

message GetFiUserRelationshipRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message GetFiUserRelationshipResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // No information found for the actor
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // key could be a product type like salary tier, is CC user etc.
  // value could be like tier type like infinite, true etc.
  map<string, string> fi_user_relationship = 2;
}

message GetFormRequest {
  string form_id = 1 [(validate.rules).string.min_len = 1];
}

message GetFormResponse {
  enum Status {
    // Success
    OK = 0;
    // Form does not exist for given id.
    NOT_FOUND = 5;
    // Internal error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // list of questions to be shown to user
  repeated form.ExtendedQuestion questions = 4;

  risk.case_management.form.Form form = 5;
}

message SubmitFormRequest {
  string form_id = 1 [(validate.rules).string.min_len = 1];

  // list of responses submitted by user for questionnaire in form.
  repeated risk.case_management.form.QuestionResponse responses = 2;
}

message SubmitFormResponse {
  enum Status {
    // Success
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // Form does not exist for given id.
    NOT_FOUND = 5;
    // Internal error
    INTERNAL = 13;
    // Form has expired and can no longer be submitted.
    EXPIRED = 100;
    // Form is already submitted and can't be resubmitted.
    ALREADY_SUBMITTED = 101;
  }

  rpc.Status status = 1;
}


message ListFormsRequest {
  form.FormFilters filters = 1 [(validate.rules).message.required = true];

  repeated form.ExtendedFormFieldMask field_masks = 3;
}

message ListFormsResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // No matching form found
    NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  repeated form.ExtendedForm forms = 2;
}

message GetAlertsRequest {
  string actor_id = 1;
  // Max supported limit is 200
  int32 limit = 2;
}

message GetAlertsResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // No alerts found
    NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // list of all the alerts(along with rule details) linked to the given actor
  // pagination is not supported currently since number of alerts linked to a case should be fairly low
  // alerts will be sorted by creation timestamp in desc order
  // max number of alerts returned will be equal to the limit passed in the request
  repeated AlertWithRuleDetails alerts = 2;
}

message GetFormsForActorRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Response limit, send 0 for no limit
  int32 limit = 2;
}

message GetFormsForActorResponse {
  enum Status {
    // Success
    OK = 0;
    // No forms exist for given actor
    NOT_FOUND = 5;
    // Internal error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  repeated risk.case_management.form.Form form = 2;
}

message CreateSuggestedActionForRuleRequest {
  // rule_id, against which the suggested action is to be created
  string rule_id = 1 [(validate.rules).string.min_len = 1];

  SuggestedActionType suggested_action_type = 2 [(validate.rules).enum = {not_in: [0]}];

  review.ActionParameters action_parameters = 3;
}

message CreateSuggestedActionForRuleResponse {
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  SuggestedAction suggested_action = 2;
}

message CreateRuleReviewTypeMappingRequest {
  // rule_id, against which the suggested action is to be created
  string rule_id = 1 [(validate.rules).string.min_len = 1];

  review.ReviewType review_type = 2 [(validate.rules).enum = {not_in: [0]}];
}

message CreateRuleReviewTypeMappingResponse {
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

// GetTransactionBlocksRequest is used to request transaction blocks for a given actor or alert ID
message GetTransactionBlocksRequest {
  // Identifier for which transaction blocks are being fetched
  oneof identifier {
    // Actor ID to fetch transaction blocks for
    string actor_id = 1 [(validate.rules).string.min_len = 1];

    // Alert ID to fetch transaction blocks for
    string alert_id = 2 [(validate.rules).string.min_len = 1];
  }

  // Optional block type filter
  TransactionBlockType block_type = 3;

  // Maximum number of transaction blocks to return
  int32 limit = 4;
}

// GetTransactionBlocksResponse contains the transaction blocks matching the request criteria
message GetTransactionBlocksResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // No transaction blocks found with given filters
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }

  // Response status
  rpc.Status status = 1;

  // List of transaction blocks
  repeated TransactionBlock transaction_blocks = 2;
}

// CreateTransactionBlockRequest is used to create a new transaction block
message CreateTransactionBlockRequest {
  // Transaction blocks to create
  repeated TransactionBlock transaction_blocks = 1 [(validate.rules).repeated.min_items = 1];
}

// CreateTransactionBlockResponse contains the created transaction block
message CreateTransactionBlockResponse {
  enum Status {
    OK = 0;
    // Invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }

  // Response status
  rpc.Status status = 1;

  // Created transaction blocks
  repeated TransactionBlock transaction_blocks = 2;
}

