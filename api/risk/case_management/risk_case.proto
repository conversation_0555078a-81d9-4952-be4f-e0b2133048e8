syntax = "proto3";

package risk.case_management;

import "api/risk/case_management/enums/enums.proto";
import "api/risk/case_management/rule.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management";
option java_package = "com.github.epifi.gamma.api.risk/case_management";

message RiskCase {
  // we will only support selected payload types
  PayloadType payload_type = 1 [(validate.rules).enum = {not_in: [0]}];
  // not using persistentqueue.Payload directly to avoid direct dependency and have flexibility
  // also only added payload for review types supported currently and relevant to risk cases for now, will expand as needed
  oneof payload {
    // not using persistentqueue.LivenessReview since we only want to support actor id in request for liveness review cases
    // we wll enrich rest of the fields in consumer before pusing to persistent queue
    LivenessReview liveness_review = 2;
    UserReview user_review = 3;
    TransacionReview transaction_review = 4;
    LivenessSampleReview liveness_sample_review = 5;
    ScreenerReview screener_review = 9;
  }
  // custom identifier string entered while uploading the risk cases via dev action
  // this will be uised for tracking and monitoring the progress of case ingestion at different steps
  string batch_identifier = 6;

  // RuleIdentifier defines the identifier which will be used to query the rules from db
  RuleIdentifier rule_identifier = 7;

  // File upload timestamp. It is the initiation point for alert created from risk case.
  google.protobuf.Timestamp initiated_at = 8;
}

// Adding separate message for liveness review payload since we want to only allow actor id in request
// we will enrich other details in consumer before pushing to persistent queue
message LivenessReview {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message LivenessSampleReview {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // request id for the attempt
  string request_id = 2;
  // Sub sample will act like tags to identify what sort of sampling we are doing (eg. rule based, pre-emptive etc)
  string sub_sample = 3;
}

message UserReview {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message TransacionReview {
  string actor_id = 1 [(validate.rules).string.min_len = 1];

  string txn_id = 2 [deprecated = true];

  string account_id = 3 [(validate.rules).string.min_len = 1];

  // type of flagged entity
  // EX: Transaction review might be needed if a transaction is flagged(entity type will be transaction in that case and entity id will be internal txn id)
  // It can also be done even if a particular transaction is not flagged but user is flagged as a whole(ex for DS models),
  // in this case transaction review case can be created with entity type as user(entity id will be internal user id).
  enums.EntityType entity_type = 4;

  string entity_id = 5;
}

message ScreenerReview {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // screener attempt id for which the case is being created
  string screener_attempt_id = 2 [(validate.rules).string.min_len = 1];
}

enum PayloadType {
  PAYLOAD_TYPE_UNSPECIFIED = 0;
  // Payload received will be for liveness review
  PAYLOAD_TYPE_LIVENESS = 1;
  // Payload received will be for full user review
  PAYLOAD_TYPE_USER_REVIEW = 2;
  // for sample cases generated by ds layer
  PAYLOAD_TYPE_LIVENESS_SAMPLE = 3;
  // Payload for flagged user transactions
  PAYLOAD_TYPE_TRANSACTION_REVIEW = 19;
  // Payload for screener review cases
  PAYLOAD_TYPE_SCREENER = 20;
}
