syntax = "proto3";

package risk.activity;

import "api/celestial/activity/header.proto";
import "api/celestial/activity/notification/notification.proto";
import "api/celestial/workflow/type.proto";
import "api/comms/message.proto";
import "api/risk/bankactions/risk_bank_actions.proto";
import "api/risk/case_management/alert.proto";
import "api/risk/enums/enums.proto";
import "api/risk/lea/unified_lea_comms.proto";
import "api/risk/lea/unified_lea_complaint.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/activity";
option java_package = "com.github.epifi.gamma.api.risk.activity";

// AddToBankAction adds new entry to risk_bank_actions table
// returns permanent error if no case management action maps
// to bank action, ignores duplicate row error
message AddToBankActionRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // workflow id under consideration
  string workflow_id = 2 [(validate.rules).string.min_len = 1];
  // action is the flow current activity leads to
  enums.Action action = 3 [(validate.rules).enum = {not_in: [0]}];
  // reason to change account state
  RequestReason request_reason = 4;
  // comms template to be triggered
  repeated enums.CommsTemplate comms_template = 5;
  string actor_id = 6 [(validate.rules).string.min_len = 1];
}

message AddToBankActionResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // id of the bank action entry created
  string bank_action_id = 2;
}

message GetReminderPointRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // name of workflow we need to fetch reminders for
  celestial.workflow.TypeEnum workflow_type = 2;
  // start time of the reminder, post this reminders times will be fetched
  // if not populated, reminders will be fetched from current time
  google.protobuf.Timestamp reminder_start_time = 3;
  string actor_id = 4 [(validate.rules).string.min_len = 1];
  // optional field to identify request
  // like for bank actions will be client request id to identify exact request
  oneof request_identifier {
    // client request id for bank actions
    string client_request_id = 5;
  }
  // form id for which reminders need to be fetched
  string form_id = 6;
}

message GetReminderPointResponse {
  celestial.activity.ResponseHeader response_header = 1;
  // time point at which reminders need to be triggered
  // if no reminder is available, reminders will be stopped
  // the workflow will wait till the the below reminder point
  google.protobuf.Timestamp next_reminder_point = 2;
}

// ValidateAndGetLeaHandler validates the lea complaint and fetches the handling params for the same
message ValidateAndGetLEAHandlerRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // workflow id under consideration
  string workflow_id = 2;
  // unified lea complaint to be validated
  UnifiedLeaComplaint unified_lea_complaint = 3;
}

message ValidateAndGetLEAHandlerResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // Intended handling for a unified LEA complaint
  HandlingParams handling_params = 2;
}

// Request payload for the GetNotificationTemplateForBankActions Activity
message GetNotificationTemplateForBankActionsRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // Used to create and send form urls in the notification template
  string form_id = 2;
  // client request id for bank actions
  string bank_action_client_req_id = 3;
  // if the notification is a reminder
  bool is_reminder = 4;
}

// Response payload for the GetNotificationTemplateForBankActions Activity
message GetNotificationTemplateForBankActionsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // Contain notifications to be sent to the end users
  celestial.activity.notification.Notification notifications = 2;
}

// AppAccessRevokeRequest will revoke/restore the app access depending on Action param
message AppAccessUpdateRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // actor id for which we have to revoke app access
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // action taken against the actor id
  enums.Action action = 3 [(validate.rules).enum = {not_in: [0]}];
  // reason for which we want to apply action
  RequestReason request_reason = 4;
  // the flow via which this activity has been triggered
  string initiated_by = 5;
}

message AppAccessUpdateResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// CreateAlerts will create alerts
message CreateAsyncAlertsRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // list of alerts we want to create
  repeated case_management.RawAlert raw_alerts = 2;
}

message CreateAsyncAlertsResponse {
  message Failure {
    case_management.RawAlert alert = 1;
    string reason = 2;
  }

  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // count of alerts that failed to be created
  uint32 failure_count = 2;

  // list of alerts that failed to create
  repeated Failure failures = 3;
}

// GetUnifiedLEAComms accepts handling_params and returns comms to be triggered for end user
message GetUnifiedLEACommsRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // handling params of the unified LEA complaint
  HandlingParams handling_params = 2;
  // if the comms we want to send is a reminder comms
  bool is_reminder = 3;
}

message GetUnifiedLEACommsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  repeated comms.Communication communications = 2;
}


// ApplyLienRequest is the request for the ApplyLien workflow
message ApplyLienRequest {
  // Request header with authentication information
  celestial.activity.RequestHeader request_header = 1;

  // Account number on which the lien is to be applied
  string account_number = 2;

  // Amount for the lien
  float amount = 3;

  // Currency code for the lien amount
  string currency_code = 4;

  // Reason code for the lien
  string reason_code = 5;

  // Remarks for the lien
  string remarks = 6;

  // Start date for the lien
  google.protobuf.Timestamp start_date = 7;

  // End date for the lien
  google.protobuf.Timestamp end_date = 8;
}

// ApplyLienResponse is the response from the ApplyLien workflow
message ApplyLienResponse {
  // Response header with status information
  celestial.activity.ResponseHeader response_header = 1;
}

// Activity messages

// AddLienRequest is the request for the AddLien activity
message AddLienRequest {
  // Request header with authentication information
  celestial.activity.RequestHeader request_header = 1;

  // Account number on which the lien is to be applied
  string account_number = 2;

  // Amount for the lien
  float amount = 3;

  // Currency code for the lien amount
  string currency_code = 4;

  // Reason code for the lien
  string reason_code = 5;

  // Remarks for the lien
  string remarks = 6;

  // Duration for which the lien is to be applied in hours
  int32 lien_duration_in_hours = 7;

  // Channel request ID for the lien request
  string channel_request_id = 8;
  
  // id of the bank action entry created
  string bank_action_id = 9;
}

// AddLienResponse is the response from the AddLien activity
message AddLienResponse {
  // Response header with status information
  celestial.activity.ResponseHeader response_header = 1;
}

// EnquireLienRequest is the request for the EnquireLien activity
message VerifyLienRequest {
  // Request header with authentication information
  celestial.activity.RequestHeader request_header = 1;

  // Account number for which to enquire the lien
  string account_number = 2;

  string request_id = 3;
}

// EnquireLienResponse is the response from the EnquireLien activity
message VerifyLienResponse {
  // Response header with status information
  celestial.activity.ResponseHeader response_header = 1;
}

// EnquireLienRequest is the request for the EnquireLien activity
message CheckForExistingLienRequest {
  // Request header with authentication information
  celestial.activity.RequestHeader request_header = 1;

  // Account number for which to enquire the lien
  string account_number = 2;
}

// EnquireLienResponse is the response from the EnquireLien activity
message CheckForExistingLienResponse {
  // Response header with status information
  celestial.activity.ResponseHeader response_header = 1;
  // This indicates if there is an existing lien on the account
  bool has_existing_lien = 2;
}

message GetCommsRequest {
  // Request header with authentication information
  celestial.activity.RequestHeader request_header = 1;

  // Account number for which to get communications
  string account_number = 2;

  // time from which the lien will be applied
  google.protobuf.Timestamp start_date = 3;

  // time until which the lien will be applied
  google.protobuf.Timestamp end_date = 4;

  // amount that is marked as lien for the account
  google.type.Money amount = 5;
}

message GetCommsResponse {
  // Response header with status information
  celestial.activity.ResponseHeader response_header = 1;

  // List of communications to be sent
  repeated comms.Communication communications = 2;

  string entity_id = 3;
}

message UpdateBankActionRequest {
  // Request header with authentication information
  celestial.activity.RequestHeader request_header = 1;

  string client_req_id = 2;

  enums.State state = 3;
}

message UpdateBankActionResponse {
  // Response header with status information
  celestial.activity.ResponseHeader response_header = 1;
}
