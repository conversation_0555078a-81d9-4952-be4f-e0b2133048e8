//go:generate gen_sql -types=LienRequestType
syntax = "proto3";

package risk.lien_requests;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/lien";
option java_package = "com.github.epifi.gamma.api.risk.lien";

// LienRequestType defines the type of lien request
enum LienRequestType {
  LIEN_REQUEST_TYPE_UNSPECIFIED = 0;
  // Add a new lien
  LIEN_REQUEST_TYPE_ADD = 1;
  // Enquire about existing liens
  LIEN_REQUEST_TYPE_ENQUIRE = 2;
  // Modify an existing lien
  LIEN_REQUEST_TYPE_MODIFY = 3;
  // Remove an existing lien
  LIEN_REQUEST_TYPE_REMOVE = 4;
}

// LienRequest represents a lien request on a savings account
message LienRequest {
  // Unique identifier for the lien request
  string id = 1 [(validate.rules).string.uuid = true];

  // The request type in the API call (ADD/ENQUIRE/MODIFY/REMOVE)
  LienRequestType req_type = 2 [(validate.rules).enum.defined_only = true];

  // The id of the lien which is created in the bank system
  string lien_id = 3;

  // The status of the request
  string status = 4;

  // Type of lien
  string lien_type = 5;

  // Saving account number for which the lien action is being taken
  string savings_account_number = 6 [(validate.rules).string.min_len = 1];

  // The amount to mark as lien
  float amount = 7;

  // Currency code (e.g., INR/USD)
  string currency_code = 8;

  // The reason for placing the lien
  string reason_code = 9;

  // Additional remarks
  string remarks = 10;

  // Time from which the lien should start
  google.protobuf.Timestamp start_date = 11;

  // Time until when the lien should last
  google.protobuf.Timestamp end_date = 12;

  // Channel's request identifier
  string channel_request_id = 13;

  // Channel of the request
  string channel = 14 [(validate.rules).string.min_len = 1];

  // CBS status returned by bank API
  string cbs_status = 15;

  // CBS response returned by bank API
  string cbs_response = 16;

  // Message returned by the API
  string api_message = 17;

  // New amount value (used in case of modify request)
  float new_amount_value = 18;

  // Old amount value (used in case of modify request)
  float old_amount_value = 19;

  google.protobuf.Timestamp created_at = 20;

  google.protobuf.Timestamp updated_at = 21;

  google.protobuf.Timestamp deleted_at_unix = 22;
}