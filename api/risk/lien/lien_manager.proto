syntax = "proto3";

package risk.lien;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "google/type/money.proto";
import "api/comms/message.proto";

option go_package = "github.com/epifi/gamma/api/risk/lien";
option java_package = "com.github.epifi.gamma.api.risk.lien";

// AddLienRequest contains parameters required for adding a lien
message AddLienRequest {
  // Account number on which to add lien
  string account_number = 1 [(validate.rules).string.min_len = 1];

  // Amount to be marked as lien
  double amount = 2;

  // Currency code (e.g., INR)
  string currency_code = 3;

  // Reason code for the lien
  string reason_code = 4;

  // Additional remarks
  string remarks = 5;

  // Time from which the lien should start
  google.protobuf.Timestamp start_date = 6;

  // Time until when the lien should last
  google.protobuf.Timestamp end_date = 7;

  // Channel's request identifier
  string channel_request_id = 8;
  
  // id of the bank action entry created
  string bank_action_id = 9;
}

// AddLienResponse contains the response from the add lien operation
message AddLienResponse {
  string id = 1;

  // CBS status returned by bank API
  string cbs_status = 3;

  // CBS response returned by bank API
  string cbs_response = 4;

  // Message returned by the API
  string api_message = 5;
}

// EnquireLienRequest contains parameters required for enquiring a lien
message EnquireLienRequest {
  // Account number for which to enquire lien
  string account_number = 1 [(validate.rules).string.min_len = 1];

  string request_id = 2 [(validate.rules).string.min_len = 1];
}

// EnquireLienResponse contains the response from the enquire lien operation
message EnquireLienResponse {
  // Unique identifier for the lien request
  string lien_id = 1;

  // Message returned by the API
  string api_message = 2;

  // Details of the lien
  LienDetails lien_details = 3;
}

// LienDetails contains the details of a lien
message LienDetails {
  // Amount marked as lien
  double amount = 1;

  // Currency code (e.g., INR)
  string currency_code = 2;

  // Reason code for the lien
  string reason_code = 3;

  // Additional remarks
  string remarks = 4;

  // Time from which the lien started
  google.protobuf.Timestamp start_date = 5;

  // Time until when the lien lasts
  google.protobuf.Timestamp end_date = 6;
}

message GetCommsRequest {
  // Account number for which to get communications
  string account_number = 1;

  // time from which the lien will be applied
  google.protobuf.Timestamp start_date = 2;

  // time until which the lien will be applied
  google.protobuf.Timestamp end_date = 3;

  // amount that is marked as lien for the account
  google.type.Money amount = 4;
}

message GetCommsResponse {
  repeated comms.Communication communications = 1;
}
