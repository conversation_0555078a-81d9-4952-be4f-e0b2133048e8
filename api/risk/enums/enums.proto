//go:generate gen_sql -types=ScreenerCriteria,ScreenerAction,LEAReportOrigin,DisputeState,DisputeVerdict

syntax = "proto3";

package enums;

option go_package = "github.com/epifi/gamma/api/risk/enums";
option java_package = "com.github.epifi.gamma.api.risk.enums";

// Action is the flow current activity leads to
enum Action {
  ACTION_UNSPECIFIED = 0;

  ACTION_FULL_FREEZE = 1;

  ACTION_CREDIT_FREEZE = 2;

  ACTION_UNFREEZE = 3;

  ACTION_DEBIT_FREEZE = 4;

  ACTION_LIEN = 5;
}

// State will determine what's the current user position is in the workflow for that Action
enum State {
  STATE_UNSPECIFIED = 0;

  // db entry added to be processed, next step is to send to BANK
  STATE_INITIATED = 1;

  // mail/ comms triggered to bank for action on their side
  STATE_SENT_TO_BANK = 2;

  // action performed by bank AND fi : this marks a successful workflow
  STATE_SUCCESS = 3;

  // refused by bank, reason will reflect in failure_Reason
  STATE_REFUSED = 4;

  // manual intervention needed eg: state of user from bank doesn't change after 'x' retries
  STATE_MANUAL_INTERVENTION = 5;

  // cancelled workflow, no further steps to be taken for this
  STATE_CANCELED = 6;

  // At times, workflows may not go to terminal state as intended due to external dependencies e.g., vendor API failures
  // To overcome such limitations, manual overrides are supported
  // Below enum is the counter part of STATE_SUCCESS except that it is updated manually
  STATE_SUCCESS_MANUAL_OVERRIDE = 7;

  // At times we want to manually reject the workflow e.g., dev/ risk-ops human errors
  // To overcome such limitations, manual overrides are supported
  STATE_REJECT_MANUAL_OVERRIDE = 8;

  // failed to perform required action, reason will reflect in failure_reason
  STATE_FAILED = 9;
}

// Provenance: the beginning of something's existence; something's origin.
// e.g. FI (FI initiated), BANK (BANK initiated), LEA (Law enforcement agency initiated)
// This enum represents different entry provenance of request in the system
enum Provenance {
  PROVENANCE_UNSPECIFIED = 0;

  PROVENANCE_FI = 1;

  PROVENANCE_BANK = 2;

  PROVENANCE_LEA = 3;
}

enum RequestReason {
  REQUEST_REASON_UNSPECIFIED = 0;

  REQUEST_REASON_FRAUDULENT_ACCOUNT = 1;
  // For other remarks will contain detailed remark
  REQUEST_REASON_OTHER = 2;
  // freeze reason: kyc issues like face match, liveness, name match
  REQUEST_REASON_CORE_KYC_ISSUE = 3;
  // freeze reason: suspicious email patterns, AA info mismatch, location based issues
  // and also if data analytics has pointed a user as risky
  REQUEST_REASON_PROFILE_INDICATORS = 4;
  // freeze reason: penny drop abuse as name suggests
  REQUEST_REASON_PENNY_DROP_ABUSE = 5;
  // freeze reason: received funds from known fraudsters
  REQUEST_REASON_RECEIVED_FUNDS_FROM_FRAUDSTERS = 6;
  // freeze reason: high number of withdrawals from atms
  REQUEST_REASON_HIGH_ATM_WITHDRAWAL_COUNT = 7;
  // freeze reason: intersection rule in transaction monitoring
  REQUEST_REASON_TM_ALERT = 8;
  // freeze reason: transaction monitoring profile mismatch
  REQUEST_REASON_TM_PROFILE_MISMATCH = 9;
  // unfreeze reason: after due diligence
  REQUEST_REASON_DUE_DILIGENCE = 10;
  // unfreeze reason: after customer outcall
  REQUEST_REASON_CUSTOMER_OUTCALL = 11;
  // freeze reason: user's account is deleted since it was requested by user
  REQUEST_REASON_ACCOUNT_DELETION_REQUEST = 12;
  // freeze reason: denotes user's account is closed due to min kyc expiry
  REQUEST_REASON_MIN_KYC_ACCOUNT_EXPIRY = 13;
  // account frozen under LEA
  REQUEST_REASON_LEA_COMPLAINT = 14;
  // account inquired under LEA
  REQUEST_REASON_LEA_ENQUIRY = 15;
  // account frozen under NPCI
  REQUEST_REASON_NPCI_COMPLAINT = 16;
  // account frozen under FEDERAL rules
  REQUEST_REASON_FEDERAL_RULES = 17;
  // unfreeze reason: LEA
  REQUEST_REASON_LEA_UNFREEZE = 18;
  // freeze reason : LSO user having vkyc pending
  REQUEST_REASON_LSO_USER_VKYC_PENDING = 19;
  // freeze reason: block onboarding (no comms triggered)
  REQUEST_REASON_BLOCK_ONBOARDING = 20;
  // unfreeze reason to allow usage for cc/pl
  REQUEST_REASON_CC_OR_PL_USER = 21;
  // unfreeze reason: False Positive
  REQUEST_REASON_FALSE_POSITIVE = 22;
  // freeze reason: account was inactive for a certain period of time
  REQUEST_REASON_INACTIVITY = 23;
  // freeze reason: users who were part of revkyc queue and did not complete re vkyc
  REQUEST_REASON_RE_VKYC_NOT_COMPLETED = 24;
}

// exceptions that can occur in workflow
// states reason why overall state machine failed
// https://docs.google.com/document/d/1aWbyWW36X5STm2a3v8WuZWImbuazJPltFIcLvmnEe54/edit#bookmark=id.cvbvozdz65b8
enum FailureReason {
  FAILURE_REASON_UNSPECIFIED = 0;

  FAILURE_REASON_DROP_DEDUPE_ALREADY_MARKED = 1;

  FAILURE_REASON_DROP_DEDUPE_ALREADY_SENT = 2;

  FAILURE_REASON_DROP_DEDUPE_ALREADY_FROZEN = 3;

  FAILURE_REASON_DROP_DEDUPE_LEA_FROZEN = 4;

  FAILURE_REASON_DROP_CONFLICT_SENT_FOR_UNFREEZE = 5;

  FAILURE_REASON_CANCELLED = 6;
  // the requested action failed as account is closed
  FAILURE_REASON_ACCOUNT_CLOSED = 7;
  // the requested action failed as there is a permanent input mismatch in bank API
  // e.g. in account status FEDERAL API if there is mismatch in mobile number a permanent error is returned
  FAILURE_REASON_ACCOUNT_STATUS_INPUT_MISMATCH = 8;
}

// ENUMS for comms email trigger
// reason for keeping this as seperate entity
// as comms for user isn't tightly coupled
// with reason for block/ unblock
// https://docs.google.com/document/d/1Pvm5zHYTaG25jzFNB4zZvlrFaX_SU5ziKN883dNPlw8/edit#
enum CommsTemplate {
  COMMS_TEMPLATE_UNSPECIFIED = 0;

  COMMS_TEMPLATE_EMAIL_LEA_C_F = 1;

  COMMS_TEMPLATE_EMAIL_LEA_E_F = 2;

  COMMS_TEMPLATE_EMAIL_H_KYC = 3;

  COMMS_TEMPLATE_EMAIL_M_KYC = 4;

  COMMS_TEMPLATE_EMAIL_H_TM = 5;

  COMMS_TEMPLATE_EMAIL_L_TM = 6;

  COMMS_TEMPLATE_EMAIL_TF_CF = 7;

  COMMS_TEMPLATE_EMAIL_TF_C = 8;

  COMMS_TEMPLATE_EMAIL_ACC_UNF = 9;

  COMMS_TEMPLATE_EMAIL_CF = 10;

  COMMS_TEMPLATE_EMAIL_AFU_F = 11;

  COMMS_TEMPLATE_EMAIL_INT_ATM = 12;

  COMMS_TEMPLATE_EMAIL_SD = 13;

  COMMS_TEMPLATE_EMAIL_M_CF = 14;

  COMMS_TEMPLATE_ACC_UNF_CL = 15;

  COMMS_TEMPLATE_EMAIL_CL_AC = 16;

  COMMS_TEMPLATE_EMAIL_ONB_DENIED = 17;

  COMMS_TEMPLATE_EMAIL_H_P_KYC = 18;

  COMMS_TEMPLATE_EMAIL_INT_ATM_C = 19;

  COMMS_TEMPLATE_EMAIL_TM_RULE_1 = 20;

  COMMS_TEMPLATE_EMAIL_TM_RULE_2 = 21;

  COMMS_TEMPLATE_EMAIL_TM_RULE_3 = 22;

  COMMS_TEMPLATE_EMAIL_INACTIVITY_CF = 23;

  COMMS_TEMPLATE_EMAIL_ACC_DF = 24;

  COMMS_TEMPLATE_EMAIL_VKYC_CF = 25;

  COMMS_TEMPLATE_EMAIL_M_KYC_PCF = 26;
}

// EntityType is the object sent to Risk Evaluators.
// These are usually a mix of multiple entities like customer can be mix of actor, user, liveness_scores etc.
enum EntityType {

  ENTITY_TYPE_UNSPECIFIED = 0;

  ENTITY_TYPE_CUSTOMER = 1;

  ENTITY_TYPE_ACCOUNT = 2;

  ENTITY_TYPE_PAYMENT_ADDRESS = 3;

  ENTITY_TYPE_TRANSACTION = 4;

  ENTITY_TYPE_CONTROL_LIST = 5;
}

// AccountFreezeStatus signifies actual account status (restrictions)
enum AccountFreezeStatus {
  ACCOUNT_FREEZE_STATUS_UNSPECIFIED = 0;
  // User account was found fraudulent and total freeze was applied on the account
  ACCOUNT_FREEZE_STATUS_TOTAL_FREEZE = 1;
  // Account is restricted by credit freeze
  ACCOUNT_FREEZE_STATUS_CREDIT_FREEZE = 2;
  // Account is restricted by debit freeze
  ACCOUNT_FREEZE_STATUS_DEBIT_FREEZE = 3;
  // There are no restrictions on the account.
  ACCOUNT_FREEZE_STATUS_UNFROZEN = 4;
}

// Enum to represent different criteria for which risk screener checks can be invoked
// Set of checks performed and threshold for failure could be different based on what purpose the screener is getting invoked for
// EX: checks(and threshold) required in fi-lite onboarding vs savings account onboarding will differ
enum ScreenerCriteria {
  SCREENER_CRITERIA_UNSPECIFIED = 0;
  // user is onboarding onto fi savings account
  SCREENER_CRITERIA_SAVINGS_ACCOUNT_ONBOARDING = 1;
  // user is onboarding on app only for fi lite features
  SCREENER_CRITERIA_FI_LITE_ONBOARDING = 2;
  // user is reonboarding on the app
  SCREENER_CRITERIA_REOOBE = 3;
  // user is doing savings account onboarding via B2B salary flow
  SCREENER_CRITERIA_B2B_SAVINGS_ACCOUNT_ONBOARDING = 4;
  // user is onboarding for personal loans with liquiloans
  SCREENER_CRITERIA_LOANS_PL_FI_LITE_LIQUILOANS = 5;
  // user is onboarding for federal CREDIT CARD through fi lite flow
  SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_ONBOARDING = 6;
  // user has downloaded their credit report, and we are adding an offer in an async process
  SCREENER_CRITERIA_FEDERAL_CREDIT_CARD_OFFER_CREATION = 7;
  // to be used for generic set of checks if the user is going through loans journey
  SCREENER_CRITERIA_LOANS_ONBOARDING = 8;
  // user is doing savings account onboarding with non-resident region
  SCREENER_CRITERIA_NR_SAVINGS_ACCOUNT_ONBOARDING = 9;
  // user is onboarding without savings account - direct to home
  SCREENER_CRITERIA_D2H_ONBOARDING = 10;
  // user onboarding for stock guardian loans
  SCREENER_CRITERIA_STOCK_GUARDIAN_LOANS = 11;
}

enum ScreenerAction {
  SCREENER_ACTION_UNSPECIFIED = 0;
  // all the checks have passed and user can be moved to next stage
  SCREENER_ACTION_PASS = 1;
  // user has failed screener checks and needs to be manually reviewed before proceeding further
  SCREENER_ACTION_MANUAL_REVIEW = 2;
  // user has failed some critical screener checks and should be blocked from proceeding further
  SCREENER_ACTION_FAIL = 3;
}

// Signifies risk level of the user against a risk check.
// These level can be used to take actions against the user such as
// fail or pass onboarding/re-onboarding attempt or send for manual review.
enum RiskSeverity {
  RISK_SEVERITY_UNSPECIFIED = 0;

  RISK_SEVERITY_LOW = 1;

  RISK_SEVERITY_MEDIUM = 2;

  RISK_SEVERITY_HIGH = 3;

  RISK_SEVERITY_CRITICAL = 4;
}

// common enum to be used in cases where data freshness needs to be decided by clients for certain data
enum DataFreshness {
  DATA_FRESHNESS_UNSPECIFIED = 0;
  // makes a fresh vendor call
  DATA_FRESHNESS_REAL_TIME = 1;
  // data is fetched from db if it not older than x mins, otherwise makes a vendor call
  // worst case staleness could be upto 10 mins
  DATA_FRESHNESS_NEAR_REAL_TIME = 3;
  // data is fetched from db if it is not older than x days, otherwise makes a vendor call
  // worst case staleness could be upto a week
  DATA_FRESHNESS_RECENT = 2;
  // data will be fetched from db only, return record not exist if data unavailable
  DATA_FRESHNESS_LAST_KNOWN = 4;
}

// enums to identify origin point for a LEA report
enum LEAReportOrigin {
  // No proper origin
  LEA_REPORT_ORIGIN_UNSPECIFIED = 0;
  // Complaint on Fi accounts
  LEA_REPORT_ORIGIN_FI_FEDERAL = 1;
  // Complaint on Jupiter/other Fed account and not Fi
  LEA_REPORT_ORIGIN_FEDERAL = 2;
  // NPCI complaint
  LEA_REPORT_ORIGIN_NPCI = 3;
  // Complaints received through other banks
  LEA_REPORT_ORIGIN_OTHER_BANK = 4;
  // Complaints directly from police stations
  LEA_REPORT_ORIGIN_POLICE_STATION = 5;
  // National Cyber Crime Reporting Portal
  LEA_REPORT_ORIGIN_NCCRP = 6;
}

// Current state of dispute.
enum DisputeState {
  DISPUTE_STATE_UNSPECIFIED = 0;
  DISPUTE_STATE_INITIATED = 1;
  DISPUTE_STATE_CLOSED = 2;
}

enum DisputeVerdict {
  DISPUTE_VERDICT_UNSPECIFIED = 0;
  // Dispute request is accepted and the amount is reversed.
  DISPUTE_VERDICT_ACCEPTED = 1;
  DISPUTE_VERDICT_REJECTED = 2;
}
