syntax = "proto3";

package risk;

import "api/accounts/account_type.proto";
import "api/risk/enums/enums.proto";
import "api/typesv2/common/boolean.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/risk";
option java_package = "com.github.epifi.gamma.api.risk";

// tech design : https://docs.google.com/document/d/1RA_NCtQSLE_HvI4GYjCFqxJyXo5op0SPGFfWVauyZHM/edit#

// Reason for which request was entered onto our system
message RequestReason {
  // Specifies reason for which action is being taken
  enums.RequestReason reason = 1;
  // remarks should be non empty if reason is other
  string remarks = 2;
}

// Account status with reason received from bank api
// Federal API Doc : https://docs.google.com/document/d/1QCFihSNkV2hwpjewV-4sXLRzY2MCE42r/edit#bookmark=id.im8dvgfgq2uv
message BankActionReason {
  // Specifies reason for which action is being taken eg: 52:
  string status = 1;
  // reason for above status eg: NEO BANKING- LIVENESS CHECK FAILURE
  string reason = 2;
}

message RiskBankActions {
  string id = 1;

  // Client request id corresponding to the celestial workflow
  string client_req_id = 2;

  string actor_id = 3;

  // account onto consideration
  string account_id = 4;

  // type of account on which action is taken eg: Savings, credit-card
  accounts.Type account_type = 5;

  // Vendor - bank identifier/ action executing authority eg: Federal for account block/unblock
  vendorgateway.Vendor vendor = 6;

  // Action is the flow current activity leads to
  enums.Action action = 7;

  // State will determine what's the current user position is in the workflow for that Action
  enums.State state = 8;

  RequestReason request_reason = 9;

  // exceptions that can occur in workflow
  // states reason why overall state machine failed
  enums.FailureReason failure_reason = 10;

  // Account status with reason received from bank api
  BankActionReason bank_action_reason = 11;

  // Provenance: the beginning of something's existence; something's origin.
  // e.g. FI (FI initiated), BANK (BANK initiated), LEA (Law enforcement agency initiated)
  // This enum represents different entry provenance of request in the system
  enums.Provenance provenance = 12;

  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  google.protobuf.Timestamp deleted_at = 15;

  // Array of strings for comms trigger
  // Will support multiple types of comms
  repeated enums.CommsTemplate comms_template = 16;
  // based on if an entry is recon we might have separate handling like
  // not de-duping the entry, not sending to bank etc.
  api.typesv2.common.BooleanEnum is_recon = 17;

  // date at which action vendor takes action on this case
  // data can be populated manually, by API response
  // or can be null if no info is present
  google.protobuf.Timestamp bank_action_date = 18;

  // id of the lien request which was used to add lien for this lien action, will only be present in case of lien action
  string lien_request_id = 19;
}

// RiskBankActionsFieldMask is the enum representation of all the RiskBankActions fields.
// Meant to be used as field mask to help with database updates
enum RiskBankActionsFieldMask {
  RISK_BANK_ACTIONS_UNSPECIFIED = 0;
  ID = 1;
  CLIENT_REQ_ID = 2;
  ACTOR_ID = 3;
  ACCOUNT_ID = 4;
  ACCOUNT_TYPE = 5;
  VENDOR = 6;
  ACTION = 7;
  STATE = 8;
  REQUEST_REASON = 9;
  FAILURE_REASON = 10;
  BANK_ACTION_REASON = 11;
  PROVENANCE = 12;
  CREATED_AT = 13;
  UPDATED_AT = 14;
  DELETED_AT = 15;
  ALL = 16;
  COMMS_TEMPLATE = 17;
  IS_RECON = 18;
  BANK_ACTION_DATE = 19;
  LIEN_REQUEST_ID = 20;
}
