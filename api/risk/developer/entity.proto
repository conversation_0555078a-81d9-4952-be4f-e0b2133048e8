// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package risk.developer;

option go_package = "github.com/epifi/gamma/api/risk/developer";
option java_package = "com.github.epifi.gamma.api.risk.developer";

// Different entity types exposed from risk service
enum Entity {

  RISK_ENTITY_UNSPECIFIED = 0;

  RISK_BANK_ACTIONS = 1;

  RISK_DATA = 2;

  RISK_ALERTS = 3;

  RISK_REVIEW_ACTIONS = 4;

  RISK_SCREENER_ATTEMPTS = 5;

  RISK_ENTITY_FORM = 6;

  RISK_UNIFIED_LEA_COMPLAINTS = 7;

  RISK_LIEN_REQUESTS = 8;
}
