syntax = "proto3";

package auth;

import "api/auth/device_registration_status.proto";
import "api/auth/enums.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/phone_number.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/auth";
option java_package = "com.github.epifi.gamma.api.auth";

message TokenDetails {
  string id = 1;
  api.typesv2.common.PhoneNumber phone_number = 2;
  TokenType tokenType = 3;
  string actor_id = 4;
  string afu_id = 5;
  api.typesv2.common.Device device = 6;
  auth.DeviceRegistrationStatus device_registration_status = 7;
  google.protobuf.Timestamp last_activity = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp deleted_at = 10;
  string token = 11;
  TokenDeletionReason token_deletion_reason = 12;
  string email = 13;
}

// An enum to represent the type of authentication tokens
enum TokenType {
  TOKEN_TYPE_UNSPECIFIED = 0;
  // A short lived authentication token
  ACCESS_TOKEN = 1;
  // Refresh tokens mitigate the risk of a long-lived access_token
  // The idea of refresh tokens is that if an access token is compromised,
  // because it is short-lived,
  // the attacker has a limited window in which to abuse it.
  REFRESH_TOKEN = 2;
  // Access token for waitlist users
  WAITLIST_ACCESS_TOKEN = 3;
  // Access token to call insights API from web-view
  APP_INSIGHTS_ACCESS_TOKEN = 4;
  // Access token to be used for user authentication with chatbot vendor
  CHATBOT_ACCESS_TOKEN = 5;
  // Access token for web onboarding users; generated with the phone number
  WEB_LITE_ACCESS_TOKEN = 6;
  // HANDSHAKE_TOKEN is generated when we complete verification of handshake code.
  BKYC_HANDSHAKE_TOKEN = 7;
  // Refresh token for Genie (Biometric KYC app); generated with Email OTP
  GENIE_REFRESH_TOKEN = 8;
  // Access token for Genie (Biometric KYC app); generated with MPIN and refresh token
  GENIE_ACCESS_TOKEN = 9;
  // Refresh token for webform used for balance transfer of MIN KYC closed accounts; generated with Phone/Email OTP
  WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_REFRESH_TOKEN = 10;
  // Access token for webform used for balance transfer of MIN KYC closed accounts; generated after PAN verification
  WEB_CLOSED_ACC_MIN_KYC_BALANCE_TRANSFER_ACCESS_TOKEN = 11;
  // Access token for webform used for risk outcall.
  RISK_OUTCALL_WEBFORM_ACCESS_TOKEN = 12;
  // Access token for web login - used for auth in web flows
  // This token is generated after OTP verification
  WEB_ACCESS_TOKEN = 13;
  // Access token to be used for user authentication with networth mcp
  NETWORTH_MCP_ACCESS_TOKEN = 14;
  // Access token for Nugget Chatbot; generated when passing data for SDK init
  NUGGET_CHATBOT_ACCESS_TOKEN = 15;
}
