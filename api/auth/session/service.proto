syntax = "proto3";

package auth.session;

import "api/auth/session/session.proto";
import "api/rpc/status.proto";
import "validate/validate.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/device.proto";

option go_package = "github.com/epifi/gamma/api/auth/session";
option java_package = "com.github.epifi.gamma.api.auth.session";

service SessionManager {
  // GetSessionLoginUrl generates a signed URL for session-based login.
  rpc GetSessionLoginUrl (GetSessionLoginUrlRequest) returns (GetSessionLoginUrlResponse);
  // CreateSession creates a new session, generates a token, and stores sessionId:auth_token in redis
  rpc CreateSession (CreateSessionRequest) returns (CreateSessionResponse);
  // ValidateSession is used to validate session if valid and active
  // gets auth_token from redis stored while CreateSession and validates auth_token
  rpc ValidateSession (ValidateSessionRequest) returns (ValidateSessionResponse);
}

message GetSessionLoginUrlRequest {
  string session_id = 1 [(validate.rules).string.min_len = 1];
}

message GetSessionLoginUrlResponse {
  rpc.Status status = 1;
  // login url with signed session_id with timestamp
  // eg: fi.money/mcp_login?sess_id={signed_session_id}
  string session_login_url = 2;
}

message CreateSessionRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  string session_id = 2 [(validate.rules).string.min_len = 1];

  // required for creating token
  api.typesv2.common.Device device = 3;
  api.typesv2.common.PhoneNumber phone_number = 4;
}

message CreateSessionResponse {
  enum Status {
    // Success
    OK = 0;
    // Internal error
    INTERNAL = 13;
    // session is expired or not found
    SESSION_EXPIRED = 101;
  }
  rpc.Status status = 1;
  SessionDetails session_details = 2;
}

message ValidateSessionRequest {
  string session_id = 1 [(validate.rules).string.min_len = 1];
}

message ValidateSessionResponse {
  enum Status {
    // Success
    OK = 0;
    // Internal error
    INTERNAL = 13;
    // session is expired or not found
    SESSION_EXPIRED = 101;
  }
  rpc.Status status = 1;
  SessionDetails session_details = 2;
}
