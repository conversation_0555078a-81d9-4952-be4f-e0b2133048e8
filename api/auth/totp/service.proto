syntax = "proto3";

package auth.totp;

import "api/auth/totp/enums.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/auth/totp";
option java_package = "com.github.epifi.gamma.api.auth.totp";


// Service responsible for handling TOTP related auth
service Totp {
  // GenerateTotp returns totp and it's expiry for given purpose
  rpc GenerateTotp (GenerateTotpRequest) returns (GenerateTotpResponse);
  // VerifyTotp verifies if a given totp matches with a generated totp
  rpc VerifyTotp (VerifyTotpRequest) returns (VerifyTotpResponse);
}

message GenerateTotpRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  enums.Purpose purpose = 2 [(validate.rules).enum = {not_in: [0]}];
}

message GenerateTotpResponse {
  rpc.Status status = 1;
  string totp = 2;
  google.protobuf.Timestamp expiry_timestamp = 3;
}

message VerifyTotpRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  enums.Purpose purpose = 2 [(validate.rules).enum = {not_in: [0]}];
  string totp = 3 [(validate.rules).string.min_len = 6];
}

message VerifyTotpResponse {
  enum Status {
    OK = 0;
    STATUS_INVALID_CODE = 101;
  }
  rpc.Status status = 1;
}
