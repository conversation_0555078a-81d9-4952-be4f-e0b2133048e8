syntax = "proto3";

package deposit;

import "google/type/money.proto";
import "google/type/dayofweek.proto";

option go_package = "github.com/epifi/gamma/api/deposit";
option java_package = "com.github.epifi.gamma.api.deposit";

// AutoSaveRuleParams have the params required to create an auto save rule
message AutoSaveRuleParams {
  oneof rule {
    AutoSaveDailyParams daily = 1;
    AutoSaveWeeklyParams weekly = 2;
    AutoSaveMonthlyParams monthly = 3;
  }
}

// AutoSaveDailyParams have the params required for a daily fit rule
message AutoSaveDailyParams {
  google.type.Money amount = 1;
}

// AutoSaveWeeklyParams have the params required for a weekly fit rule
message AutoSaveWeeklyParams {
  google.type.Money amount = 1;
  google.type.DayOfWeek week_day = 2;
}

// AutoSaveMonthlyParams have the params required for a monthly fit rule
message AutoSaveMonthlyParams {
  google.type.Money amount = 1;
  int32 day_of_month = 2;
}
