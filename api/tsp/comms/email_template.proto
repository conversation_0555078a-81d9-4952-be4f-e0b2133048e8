syntax = "proto3";

package tsp.comms;

import "api/tsp/comms/enums.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/tsp/comms";
option java_package = "com.github.epifi.gamma.api.tsp.comms";

// All email templates will be defined here which the clients can use to specify what they want to use
// Each template will have corresponding options to set the values
enum EmailType {
  EMAIL_TYPE_UNSPECIFIED = 0;
  EMAIL_TYPE_SAMPLE = 1;
  // email to be sent after nbfc loan is disbursed
  LOAN_CONFIRMATION_EMAIL = 2;
}

message EmailOption {
  // template options which will replace the variables in the template
  oneof option {
    // todo: to be remove, added just for reference
    SampleEmailOption sample_email_option = 1;

    LoanConfirmationEmailOption loan_confirmation_email_option = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message SampleEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 1];

  oneof option {
    SampleEmailOptionV1 sample_email_option_v1 = 2;
  }
}

message SampleEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message LoanConfirmationEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 2];
  oneof option {
    LoanConfirmationEmailV1 loans_payment_file_email_v1 = 2;
  }
}

message LoanConfirmationEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string name = 2;
  string last_four_digits_of_account_number = 3;
  string contact_number = 4;
  string lsp_logo = 5;
  string lsp_name = 6;
}
