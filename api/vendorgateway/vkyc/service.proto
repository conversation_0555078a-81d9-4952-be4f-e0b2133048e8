/*
Protos related to the Video KYC of customers.
*/
syntax = "proto3";

package vendorgateway.vkyc;

import "api/vendors/karza/fed_vkyc.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vkyc/vkyc.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "api/typesv2/user.proto";
import "api/typesv2/document_details.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/file.proto";
import "google/type/latlng.proto";
import "api/typesv2/verdict.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/vkyc";
option java_package = "com.github.epifi.gamma.api.vendorgateway.vkyc";

/*
Refer to KARZA HTTP Response Code
Document: https://api.totalkyc.com/documentation
  HTTP Response Code values
  OK =  "200";
  BAD_REQUEST = "400" ;
  UNAUTHORIZED_ACCESS = "401";
  INSUFFICIENT_CREDITS  = "402" ;
  INTERNAL_SERVER_ERROR = "500";
  SOURCE_UNAVAILABLE  = "503";
  ENDPOINT_REQUEST_TIMED_OUT  = "504";

enum KARZA_HTTP_RESPCODE {
  200 = 0;
  400 = 1;
  401 = 2;
  402 = 3;
  500 = 4;
  503 = 5;
  504 = 6;
}
*/
/*
Karza Video KYC GetSession API Call Request
*/
message GenerateSessionTokenRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  repeated ProductId product_id_list = 2;
  VKYCPriorityType vkyc_priority_type = 3;
}

/*
Karza Video KYC GetSession API Call Response Message
*/
message GenerateSessionTokenResponse {
  rpc.Status status = 1;
  // request id returned by KARZA
  string request_id = 2;
  // JWT token or session token
  string karza_token = 3;
}

/*
VKYC Add a New customer Request
*/
message AddNewCustomerRequest {
  vendorgateway.RequestHeader header = 1;
  // no need to be passed from client as it is getting populated at VG
  string karza_token = 2;
  string customer_id = 3;
  enum Consent {
    UNSPECIFIED = 0;
    Y = 1;
  }
  Consent consent = 4;
  AddNewCustomerapplicantFormData applicant_form_data = 5;
  AddNewCustomerpanData pan_data = 6;
  bool auto_schedule = 7;
  AddNewCustomerAadhaarData aadhaar_data = 8;
  VKYCPriorityType vkyc_priority_type = 9;
}

/*
VKYC Add a New customer Response Message
*/
message AddNewCustomerResponse {
  rpc.Status status = 1;
  string transaction_id = 2;
  string customer_id = 3;
  string request_id = 4;
}

message UpdateCustomerV3Request {
  vendorgateway.RequestHeader header = 1;
  // no need to be passed from client as it is getting populated at VG
  string karza_token = 2;
  string customer_id = 3;
  ApplicationFormData application_form_data = 4;
  IndividualDocs docs = 5;
  CallAllocation call_allocation = 6;
  VKYCPriorityType vkyc_priority_type = 7;
  string transaction_id = 8;
  repeated UpdateCustomerV3RequestMask update_customer_v3_request_mask_list = 10;
}

enum UpdateCustomerV3RequestMask{
  UPDATE_CUSTOMER_V3_REQUEST_MASK_UNSPECIFIED = 0;
  UPDATE_CUSTOMER_V3_REQUEST_MASK_INCOME_OCCUPATION = 1;
  UPDATE_CUSTOMER_V3_REQUEST_MASK_LOCATION = 2;
  UPDATE_CUSTOMER_V3_REQUEST_CUSTOMER_ID = 3;
  UPDATE_CUSTOMER_V3_REQUEST_EPAN = 4;
  UPDATE_CUSTOMER_V3_REQUEST_PRODUCT_TYPE = 5;
  UPDATE_CUSTOMER_V3_REQUEST_PREFERRED_LANGUAGES = 6;
}

message UpdateCustomerV3Response {
  rpc.Status status = 1;
  string transaction_id = 2;
  string customer_id = 3;
  string request_id = 4;
}

/*
VKYC Generate Customer Session Token Request
*/
message GenerateCustomerTokenRequest {
  vendorgateway.RequestHeader header = 1;
  // no need to be passed from client as it is getting populated at VG
  string karza_token = 2;
  string transaction_id = 3;
  VKYCPriorityType vkyc_priority_type = 4;
}

/*
VKYC Generate Customer Session Token Response
*/
message GenerateCustomerTokenResponse {
  rpc.Status status = 1;
  string user_token = 2;
  string request_id = 3;
}

/*
VKYC Get Available Slots for a customer Request
*/
message GetSlotRequest {
  vendorgateway.RequestHeader header = 1;
  // no need to be passed from client as it is getting populated at VG
  string customer_token = 2;
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
  string transaction_id = 5;
  VKYCPriorityType vkyc_priority_type = 6;
}

/*
VKYC Get Slots for a customer Response
*/
message GetSlotResponse {
  rpc.Status status = 1;
  repeated SlotDetails slot_details_list = 2;
  string request_id = 3;
  int32 status_code = 4;
}

message BookSlotRequest {
  vendorgateway.RequestHeader header = 1;
  // no need to be passed from client as it is getting populated at VG
  string customer_token = 2;
  string slot_id = 3;
  string transaction_id = 4;
  VKYCPriorityType vkyc_priority_type = 5;
}

message BookSlotResponse {
  rpc.Status status = 1;
  bool is_booked = 2;
  string request_id = 3;
  // reason returned in case slot was not booked
  string reason = 4;
}

message GetAgentsInSlotRequest {
  vendorgateway.RequestHeader header = 1;
  // no need to be passed from client as it is getting populated at VG
  string customer_token = 2;
  string slot_id = 3;
  string transaction_id = 4;
  VKYCPriorityType vkyc_priority_type = 5;
}

message GetAgentsInSlotResponse {
  rpc.Status status = 1;
  // TODO(Shubham) to fix message used for GetAgentsInSlotResponseData
  repeated vendors.karza.GetAgentsInSlotResponseData agent_list = 2;
}

message GenerateCustomerWeblinkRequest {
  vendorgateway.RequestHeader header = 1;
  // no need to be passed from client as it is getting populated at VG
  string customer_token = 2;
  string transaction_id = 3;
  VKYCPriorityType vkyc_priority_type = 4;
}

message GenerateCustomerWeblinkResponse {
  rpc.Status status = 1;
  string transaction_id = 2;
  string request_id = 3;
  // reason returned in case web link was not generated
  string reason = 4;
  string web_link = 5;
}

message TransactionStatusEnquiryRequest {
  vendorgateway.RequestHeader header = 1;
  string karza_token = 2;
  string transaction_id = 3;
  VKYCPriorityType vkyc_priority_type = 4;
}

message TransactionStatusEnquiryResponse {
  rpc.Status status = 1;
  string request_id = 2;
  string reason = 3;
  message TransactionData {
    string request_id = 1;
    string event = 2;
    message Data {
      string application_id = 1;
      string customer_id = 2;
      string agent_user_name = 3;
      google.protobuf.Timestamp timestamp = 4;
      google.protobuf.Timestamp customer_arrival_time = 5;
      google.protobuf.Timestamp call_start_time = 6;
      string agent_id = 7;
      bool call_success = 8;
      string reason = 9;
      google.protobuf.Timestamp scheduled_time = 10;
      string approved_by_agent = 11;
      int32 call_duration = 12;
      string user_agent_string = 13;
      string session_id = 14;
      int32 expected_wait_time = 15;
      string auditor_status = 16;
      repeated api.typesv2.Language preferred_languages = 17;
      string last_stage_completed = 18;
      google.protobuf.Timestamp call_connection_time = 19;
      bool is_customer_blocked = 20;
      string agentEmployeeId = 21;
      google.protobuf.Timestamp call_end_time = 22;
      bool terminal_state = 23;
      string agent_remark = 24;
      string auditor_remark = 25;
      string auditor_failure_reason = 26;
    }
    Data data = 3;
    string status = 4;
    string event_version = 5;
    string event_type = 6;
    // raw_event is the event name we received from the vendor
    string raw_event = 7;
  }
  repeated TransactionData transaction_data_list = 4;
}

message ReScheduleSlotRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_token = 2;
  string slot_id = 3;
  string transaction_id = 4;
  VKYCPriorityType vkyc_priority_type = 5;
}

message ReScheduleSlotResponse {
  rpc.Status status = 1;
  bool is_booked = 2;
  string request_id = 3;
  // reason returned in case slot was not booked
  string reason = 4;
}

message TriggerCallbackRequest {
  vendorgateway.RequestHeader header = 1;
  string karza_token = 2;
  CallbackType callback_type = 3;
  repeated string transaction_id = 4;
  VKYCPriorityType vkyc_priority_type = 5;
}

enum CallbackType {
  CALLBACK_TYPE_UNSPECIFIED = 0;
  CALLBACK_TYPE_AGENT = 1;
  CALLBACK_TYPE_AUDITOR = 2;
}

// denotes the kind of priority for the user depending on which, vendor credentials would be decided at VG
// using enum instead of bool since more than one credentials might be expected in future
enum VKYCPriorityType {
  // default 0 value can be inferred as existing general/non-priority flow
  VKYC_PRIORITY_TYPE_UNSPECIFIED = 0;
  // GENERAL denotes the users assigned in the first most introduced priority
  VKYC_PRIORITY_TYPE_DEFAULT_PRIORITY = 1;
  // denotes the users who need to re do the vkyc
  VKYC_PRIORITY_TYPE_RE_VKYC = 2;
  // Federal loans vkyc
  VKYC_PRIORITY_TYPE_LOAN = 3;
}

message TriggerCallbackResponse {
  rpc.Status status = 1;
  string request_id = 2;
}

message AddNewCustomerV3Request {
  vendorgateway.RequestHeader header = 1;
  // no need to be passed from client as it is getting populated at VG
  string karza_token = 2;
  string customer_id = 3;
  ApplicationFormData application_form_data = 4;
  IndividualDocs docs = 5;
  CallAllocation call_allocation = 6;
  VKYCPriorityType vkyc_priority_type = 7;
  string transaction_id = 8;
}

message AddNewCustomerV3Response {
  rpc.Status status = 1;
  string transaction_id = 2;
  string request_id = 3;
}

message GetKarzaSessionTokenRequest {
  vendorgateway.RequestHeader header = 1;
  ProductId product_id = 2;
  // priority type helps to decide karza key we have to use
  // at time of impl only unspecified priority type is getting used for both epan and vkyc
  VKYCPriorityType priority_type = 3;
}

message GetKarzaSessionTokenResponse {
  rpc.Status status = 1;
  string token = 2;
}

message GetAgentAvailabilityRequest {
  vendorgateway.RequestHeader header = 1;
  // its will get populate in vg layer
  string karza_token = 2;
}

message GetAgentAvailabilityResponse {
  rpc.Status status = 1;
  // number of agent logged in but sitting idle
  int32 idle_agent = 2;
  // number of agent taking calls
  int32 busy_agent = 3;
  // number of agent allocated to fi
  int32 total_agent = 4;
}

// fetch k token for agent availability api
message AgentDashboardAuthRequest {
  vendorgateway.RequestHeader header = 1;
}

message AgentDashboardAuthResponse {
  rpc.Status status = 1;
  string access_token = 2;
}

message SendAuditorDataRequest {
  vendorgateway.RequestHeader header = 1;
  string application_id = 2;
  string transaction_id = 3;
  string customer_code_id = 4;
  ReviewerDetails auditor_details = 5;
  google.protobuf.Timestamp auditor_decision_completed_at = 6;
  api.typesv2.Verdict auditor_decision = 7;
}

message SendAuditorDataResponse {
  rpc.Status status = 1;
}

message SendAgentDataRequest {
  vendorgateway.RequestHeader header = 1;
  bool call_success = 2;
  string failure_reason = 3;
  string application_id = 4;
  string customer_code_id = 5;
  string transaction_id = 6;
  string user_agent_string = 7;
  ReviewerDetails agent_details = 8;
  api.typesv2.PanDocumentDetails pan_details = 9;
  api.typesv2.PassportData passport_details = 10;
  api.typesv2.CountryIdDetails country_id_details = 11;
  api.typesv2.common.BooleanEnum agent_view_on_pan_face_match = 12;
  api.typesv2.common.BooleanEnum agent_view_on_passport_face_match = 13;
  api.typesv2.common.BooleanEnum agent_view_on_national_id_face_match = 14;
  ApplicationFormData application_details = 15;
  PanMatchResults pan_match_results = 16;
  api.typesv2.File captured_applicant_image = 17;
  api.typesv2.File agent_dash_call_recording = 18;
  IPDetails i_p_details = 19;
  google.type.LatLng location = 20;
  google.protobuf.Timestamp call_start_time = 21;
  google.protobuf.Timestamp call_end_time = 22;
  repeated Question questions = 23;
  repeated StageDetail stage_details = 24;
  api.typesv2.Verdict approved_by_agent = 25;
  google.protobuf.Timestamp agent_approval_time = 26;
  DeviceDetails device_details = 27;
  PassportMatchResults passport_match_results = 28;
  CountryIdMatchResults country_id_match_results = 29;
  float call_duration = 30;
}

message SendAgentDataResponse {
  rpc.Status status = 1;
}

// Vendor gateway video kyc service
service vkyc {

  // RPC to add new customer using details at karza end. This uses karza session token
  // We need to pass federal's customer id for this
  // The API does not have a check on customer id and returns success response with
  // unique transaction id even for same customer id calls
  rpc AddNewCustomer (AddNewCustomerRequest) returns (AddNewCustomerResponse) {
  }

  // RPC to get karza user token or customer token which is required for all customer specific api's
  // The customer token is fetched using transaction id generated for the customer
  // in the create new customer call
  rpc GenerateCustomerToken (GenerateCustomerTokenRequest) returns (GenerateCustomerTokenResponse) {
  }

  // RPC to get available slots for a customer by passing customer token
  // The customer token is unique for a transaction id
  rpc GetSlot (GetSlotRequest) returns (GetSlotResponse) {
  }

  // Book a slot given the slot id, the token to be passed is customer token
  rpc BookSlot (BookSlotRequest) returns (BookSlotResponse) {
  }

  // returns the list of available agents for the slot id passed
  rpc GetAgentsInSlot(GetAgentsInSlotRequest) returns (GetAgentsInSlotResponse) {
  }

  // Generate the web link for customer using user token
  rpc GenerateCustomerWeblink(GenerateCustomerWeblinkRequest) returns (GenerateCustomerWeblinkResponse) {
  }

  rpc TransactionStatusEnquiry(TransactionStatusEnquiryRequest) returns (TransactionStatusEnquiryResponse) {
  }

  // reschedule slot, if already a call was booked for transaction id
  rpc ReScheduleSlot(ReScheduleSlotRequest) returns (ReScheduleSlotResponse) {
  }

  // trigger callback to avoid waiting due to delay at vendor's end
  rpc TriggerCallback(TriggerCallbackRequest) returns (TriggerCallbackResponse) {
  }

  // RPC to update customer details at karza's end.
  // This is an updated API from karza which also returns error reason wherever possible
  // We're starting to use this rpc to support passing income occupation details
  rpc UpdateCustomerV3 (UpdateCustomerV3Request) returns (UpdateCustomerV3Response) {
  }

  // RPC to add new customer using details at karza end.
  // This is an updated API from karza which also returns error reason wherever possible
  // We're starting to use this rpc to support passing income occupation details
  rpc AddNewCustomerV3 (AddNewCustomerV3Request) returns (AddNewCustomerV3Response) {
  }

  // RPC to generate session token for karza products
  // At time of implementation we have support for epan and vkyc product
  // It use in memory to store token and generate new token only if existing one is expired
  // It should be ideally in common karza service, but in intrest of time we are keeping it in vkyc service
  rpc GetKarzaSessionToken(GetKarzaSessionTokenRequest) returns (GetKarzaSessionTokenResponse) {
  }

  rpc GetAgentAvailability(GetAgentAvailabilityRequest) returns (GetAgentAvailabilityResponse) {
  }

  // RPC to send Auditor data to vendor once call is reviewed by auditor
  rpc SendAuditorData(SendAuditorDataRequest) returns (SendAuditorDataResponse) {
  }

  // RPC to send Agent data to vendor once call is reviewed by agent or call is failed
  rpc SendAgentData(SendAgentDataRequest) returns (SendAgentDataResponse) {
  }
}
