syntax = "proto3";

package vendorgateway.digilocker;

import "api/rpc/status.proto";
import "api/typesv2/date.proto";
import "api/vendorgateway/request_header.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/digilocker";
option java_package = "com.github.epifi.gamma.api.vendorgateway.digilocker";

// The Digilocker service allows generating a link for signing in to a user's Digilocker account
// and authorizing the vendor (e.g., Perfios) to access selected documents for a limited time.
// The link includes a request ID used to track the process.
// Authorized documents can be listed using ListAuthorizedDocuments and downloaded using DownloadDocuments.
service Digilocker {

  // Generates a link for the user to log in and authorize the vendor to access their Digilocker documents.
  // Each link has a request ID for tracking.
  rpc GenerateAccountAccessLink (GenerateAccountAccessLinkRequest) returns (GenerateAccountAccessLinkResponse);

  // Returns a list of documents authorized by the user for vendor access, based on the request ID.
  // Each document has a URI for downloading.
  rpc ListAuthorizedDocuments (ListAuthorizedDocumentsRequest) returns (ListAuthorizedDocumentsResponse);

  // Downloads the documents using the request ID and document URIs.
  rpc DownloadDocuments (DownloadDocumentsRequest) returns (DownloadDocumentsResponse);
}

message GenerateAccountAccessLinkRequest {
  vendorgateway.RequestHeader header = 1;

  // The URL where the user will be redirected to at the end of the process
  // of user authorizing access to their Digilocker documents via web log in.
  // requestId is expected to be present in query params in this URL post authorization and is to be used in listing and downloading documents
  string redirect_url = 2;
}

message GenerateAccountAccessLinkResponse {
  rpc.Status status = 1;

  // A unique identifier for tracking this request, used for listing and downloading documents across multiple RPCs.
  string request_id = 2;

  // A URL generated for the user to authorize Digilocker access.
  // This is a link for the OAuth 2.0 authorization flow, allowing the user to log in and authorize access to specific documents.
  // Key parameters:
  // - response_type(default=code): Requests an authorization code to be exchanged for a token.
  // - client_id: The unique identifier for the requesting client (Perfios in this case).
  // - redirect_uri: The URI where the authorization code will be sent after the user grants access.
  // - state: A secure token (JWT) to manage the request session, ensuring it's tied to the original request with an expiry time (`exp`) and `requestId`.
  // - code_challenge and code_challenge_method: Part of the PKCE (Proof Key for Code Exchange) flow, enhancing the security of the authorization process.
  string link = 3;
}

message ListAuthorizedDocumentsRequest {
  vendorgateway.RequestHeader header = 1;

  // The unique ID generated in the GenerateAccountAccessLink RPC for listing documents authorized by the user
  string request_id = 2;
}

message DocumentSummary {
  // The name of the document, typically including the issuer's details.
  // e.g. Aadhaar Card
  string name = 1;

  // The last modification date of a document like Aadhaar
  api.typesv2.Date date = 2;

  // The URI generated by the issuer's department to access the document.
  string uri = 3;

  // A keyword representing the type of document (e.g., AADHAR, PAN).
  string doc_type = 4;

  // A description of the document, typically provided by the issuing authority.
  string description = 5;

  // The unique ID of the government authority that issued the document.
  string issuer_id = 6;

  // The name of the government authority that issued the document.
  string issuer_name = 7;

  // A list of MIME types that specify the formats in which the document is available (e.g., PDF, XML).
  repeated string mime_types = 8;

  // Indicates whether a parsed JSON version of the document is available.
  bool is_parsed_json_available = 9;
}

message ListAuthorizedDocumentsResponse {
  rpc.Status status = 1;

  // A list of summary of the documents that the user has authorized access to.
  repeated DocumentSummary documents_summary = 2;
}

message DownloadRequest {
  // The URI generated by the issuer's department to access the document.
  string document_uri = 1;

  // Flag to return the base64-encoded PDF of the document.
  bool include_pdf_base64 = 2;

  // Flag to return the XML format of the document.
  bool include_xml = 3;

  // Flag to return the parsed data of the document.
  bool include_parsed_data = 4;

  // Flag to return the JSON format of the document.
  bool include_json = 5;
}

message DownloadDocumentsRequest {
  // Header containing metadata for the request, such as authentication and other details.
  vendorgateway.RequestHeader header = 1;

  // A unique identifier for tracking this request, used for listing and downloading documents across multiple RPCs.
  string request_id = 2;

  // A list of documents to be downloaded, each containing details
  // like the document's URI and the formats to be returned (PDF, XML, JSON, etc.).
  repeated DownloadRequest download_requests = 3;
}

message DocumentMetadata {
  string timestamp = 1;
  string time_to_live = 2;

  // Date on which the document was verified
  string verified_on = 3;
}

message DocumentOwner {
  // UID of Aadhaar parsed from xml
  string uid = 1;

  Photograph photograph = 2;

  HolderAddress address = 3;

  string date_of_birth = 4;
  string gender = 5;
  string name = 6;
  string marital_status = 7;
  string title = 8;

  string phone = 9;
  string religion = 10;
  string email = 11;

  // name of relative: son, wife or daughter
  string relative_name = 12;

  // relationship type - "s" for son, "w" for wife "d" for daughter
  string relative_relationship = 13;
}

message DocumentIssuer {
  // code of issuer
  string code = 1;

  // unique identity number of issuer
  string uid = 2;

  // TIN of the issuer
  string tin = 3;

  IssuerAddress address = 4;

  // Type of issuer (ex: CG, SG etc.)
  string type = 5;

  // name of issuing body (ex: Income Tax Department)
  string name = 6;
}

message Photograph {
  // Base64-encoded representation of the photo's binary content.
  string encoded_photo = 1;

  // The file format of the photo (e.g., JPEG, PNG).
  string photo_format = 2;
}

message IssuerAddress {
  string address_line_1 = 1;

  string address_line_2 = 2;

  string house = 3;

  string landmark = 4;

  string locality = 5;

  // Village, town or city
  string vtc = 6;

  string district = 7;

  string state = 8;

  string country = 9;

  // PIN code
  string pin = 10;

  string type = 11;
}

message HolderAddress {
  // Care of (c/o) field.
  string care_of = 1;
  // line 1 of address of document holder
  string address_line_1 = 2;
  // line 2 of address of document holder
  string address_line_2 = 3;
  string house = 4;
  string locality = 5;
  string landmark = 6;
  // Post office.
  string post_office = 7;
  // village or town or city name
  string vtc = 8;
  string district = 9;
  string sub_dist = 10;
  string state = 11;
  string country = 12;
  string pin = 13;
  // Type of address (e.g., residential, business).
  string type = 14;
}

message ParsedDocument {
  DocumentMetadata document_metadata = 1;
  DocumentOwner document_owner = 2;
  string status = 3;
  string type = 4;
  string name = 5;
  string language = 6;
  string issue_date = 7;
  string number = 8;
  string issued_at = 9;
  string valid_from_date = 10;
  DocumentIssuer document_issuer = 11;
}

message ParsedDocumentInfo {
  // Indicates whether the XML document's signature is verified.
  bool is_xml_signature_verified = 1;

  // Status of the document parsing process (e.g., SUCCESS or FAILURE).
  string parsing_status = 2;

  // Parsed document data, containing all relevant information.
  ParsedDocument parsed_document = 3;
}

message Base64EncodedPdf {
  // Base64-encoded content
  string content = 1;
  // SUCCESS or FAILURE in fetching
  string status = 2;
}

message XmlDocument {
  // if xml signature is verified or not
  bool signature_verified = 1;
  string content = 2;

  // SUCCESS or FAILURE in fetching
  string status = 3;
}

message RawDocument {
  Base64EncodedPdf base64_encoded_pdf = 1;
  string json = 2;
  XmlDocument xml_document = 3;
}

message Document {
  // Document URI generated by the issuer department
  string uri = 1;

  // Details of parsed file of doc
  ParsedDocumentInfo parsed_document_info = 2;

  // Raw files of document in available formats
  RawDocument raw_document = 3;
}

message DownloadDocumentsResponse {
  rpc.Status status = 1;

  repeated Document documents = 2;
}
