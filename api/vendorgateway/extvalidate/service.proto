// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.extvalidate;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vendor_status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/name.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/extvalidate";
option java_package = "com.github.epifi.gamma.api.vendorgateway.extvalidate";

service ExternalValidate {
  // Checks if bank account exists by performing penny drop into the account
  rpc VerifyBankAccount (VerifyBankAccountRequest) returns (VerifyBankAccountResponse);
}

message VerifyBankAccountRequest {
  vendorgateway.RequestHeader header = 1;
  string ifsc = 2;
  string account_number = 3;
}

message VerifyBankAccountResponse {
  rpc.Status status = 1;
  // Request ID from karza.
  string request_id = 2;
  // VendorStatus includes vendor status code and bank response description.
  vendorgateway.VendorStatus vendor_status = 3;
  // Indicates if token bank account was verified
  api.typesv2.common.BooleanEnum is_verified = 4;
  string account_number = 5;
  string ifsc = 6;
  api.typesv2.common.Name account_holder_name = 7;
  FailureReason failureReason = 8;
}

enum FailureReason {
  FAILURE_REASON_UNSPECIFIED = 0;
  FAILURE_REASON_INVALID_ACCOUNT_NUMBER = 1;
  FAILURE_REASON_ACCOUNT_CLOSED = 2;
  // IMPS Service not available for the selected bank
  FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK = 3;
  // Unknown vendor response, usually Invalid ID Number or Combination of Inputs from Karza
  FAILURE_REASON_UNKNOWN = 4;
}
