// protolint:disable MAX_LINE_LENGTH

// Defines a service for integrating with the PAN Validation service
// provided by the partner bank

syntax = "proto3";

package vendorgateway.pan;

import "api/pan/epan/epan_attempts.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/file.proto";
import "api/typesv2/gender.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vkyc/service.proto";
import "api/vendors/wealth/nsdl.proto";
import "google/type/date.proto";
import "api/vendorgateway/vendor_status.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/pan";
option java_package = "com.github.epifi.gamma.api.vendorgateway.pan";

service PAN {
  // Validate takes in a list of PAN numbers and returns the
  // status corresponding to each PAN if they're active, expired, fake etc.
  rpc Validate (ValidateRequest) returns (ValidateResponse) {
    option (rpc.skip_tokenization) = true;
  }

  // GetEPANStatus API for ePAN provides all the stages/events that were encountered in the ePAN journey.
  // In case, of a callback or event failure, client can consume this API to get the final stage/error where the ePAN journey was ended.
  // this api can be also used to poll the epan status at any point of time
  rpc GetEPANStatus (GetEPANStatusRequest) returns (GetEPANStatusResponse);

  rpc PanProfile (PanProfileRequest) returns (PanProfileResponse);

  rpc EmploymentVerificationAdvanced (EmploymentVerificationAdvancedRequest) returns (EmploymentVerificationAdvancedResponse) {
    option (rpc.instrument_billing) = true;
  };

  // ValidateAndGetEpanInfo is designed to verify a given EPAN document and extract relevant information associated with it using inhouse API.
  // The code for the API can be found https://github.com/epiFi/delta
  rpc ValidateAndGetEpanInfo (ValidateAndGetEpanInfoRequest) returns (ValidateAndGetEpanInfoResponse);

  // PANAadhaarValidation is used to get PAN Aadhaar details of a given PAN number.
  rpc PANAadhaarValidation (PANAadhaarValidationRequest) returns (PANAadhaarValidationResponse);

  // RPC to upload file on the federal vendor sftp server
  rpc UploadFileToSFTP (UploadFileToSFTPRequest) returns (UploadFileToSFTPResponse);

  // ValidateV2 API takes pan id, name and date of birth and tells if the details are valid.
  rpc ValidateV2 (ValidateV2Request) returns (ValidateV2Response);
}

message UploadFileToSFTPRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // name with which the file is to be uploaded in sftp
  string remote_path = 2;
  // file that needed to be uploaded in sftp
  api.typesv2.File file = 3;
}

message UploadFileToSFTPResponse {
  rpc.Status status = 1;
}

message PANAadhaarValidationRequest {
  vendorgateway.RequestHeader header = 1;
  string pan = 2;
}

message PANAadhaarValidationResponse {
  enum Status {
    OK = 0;
    PAN_AADHAAR_NOT_LINKED = 101;
    MASKED_AADHAAR_NOT_FOUND = 102;
  }
  rpc.Status status = 1;
  PANAadhaarValidationResult pan_aadhaar_validation_result = 2;
  vendorgateway.VendorStatus vendor_status = 18;
}

message ValidateRequest {
  vendorgateway.RequestHeader header = 1;

  // pan numbers to fetch the status for.
  // Right now, max 5 PAN numbers are supported.
  repeated string pan_numbers = 2;
}

message ValidateResponse {
  rpc.Status status = 1;

  // PAN records with statuses corresponding to the PAN numbers in request
  repeated Record records = 2;
}

// Record defines the status of each PAN
message Record {
  string PAN = 1;
  bool is_valid = 2;
  api.typesv2.common.Name name = 3;  // name of the PAN holder
  google.type.Date last_updated_date = 4;
  // status denotes aadhaar is linked with pan card or not
  PanAadhaarLinkStatus pan_aadhaar_link_status = 5;
}

enum PanAadhaarLinkStatus {
  PAN_AADHAAR_LINK_STATUS_UNSPECIFIED = 0;
  // Aadhaar Seeding is Successful
  PAN_AADHAAR_LINK_STATUS_TRUE = 1;
  // Aadhaar Seeding is Unsuccessful or Aadhaar is not seeded.
  PAN_AADHAAR_LINK_STATUS_FALSE = 2;
  // Aadhaar seeding Not applicable or the user is exempted from linking aadhaar
  PAN_AADHAAR_LINK_STATUS_NA_OR_EXEMPT = 3;
}

message PANAadhaarValidationResult {
  string pan = 1;
  string aadhaar_last4_digits = 2;
  string masked_aadhaar = 3;
}


message GetEPANStatusRequest {
  vendorgateway.RequestHeader header = 1;
  // This field will denote client request id that was passed by client in SDK invocation stage.
  string client_req_id = 2;
  string karza_token = 3;
  vkyc.VKYCPriorityType vkyc_priority_type = 4;
}

message GetEPANStatusResponse {
  rpc.Status status = 1;
  string event = 2;
}

message PanProfileRequest {
  vendorgateway.RequestHeader header = 1;
  bool consent = 2;
  string pan = 3;
  string aadhaar_last_four = 4;
  google.type.Date dob = 5;
  api.typesv2.common.Name name = 6;
  api.typesv2.PostalAddress address = 7;

  WantDetails want_details = 8;

  message WantDetails {
    bool get_contact_details = 1;
    bool pan_status = 2;
    bool is_salaried = 3;
    bool is_director = 4;
    bool is_sole_prop = 5;
    bool fathers_name = 6;
  }

  ClientData client_data = 9;

  message ClientData {
    string case_id = 1;
  }
}

message PanProfileResponse {
  rpc.Status status = 1;

  int64 status_code = 2;
  string request_id = 3;
  Result result = 4;

  message Result {
    string pan = 1;
    api.typesv2.common.Name name = 2;
    api.typesv2.Gender gender = 3;
    bool aadhaar_linked = 4;
    bool aadhaar_match = 5;
    google.type.Date dob = 6;
    api.typesv2.PostalAddress address = 7;
    api.typesv2.common.PhoneNumber mobile_no = 8;
    string email_id = 9;

    repeated ProfileMatch profile_match = 10;

    message ProfileMatch {
      Parameter parameter = 1;
      double match_score = 2;
      bool match_result = 3;
    }

    AuthorizedSignatory authorized_signatory = 11;

    message AuthorizedSignatory {
      string pan = 1;
      api.typesv2.common.Name name = 2;
      api.typesv2.Gender gender = 3;
      string aadhaar_linked = 4;
      google.type.Date dob = 5;
      api.typesv2.common.PhoneNumber mobile_no = 6;
      string email_id = 7;
      api.typesv2.PostalAddress address = 8;
    }

    Status status = 12;
    google.type.Date issued_date = 13;
    bool is_salaried = 14;
    bool is_director = 15;
    bool is_sole_prop = 16;
    api.typesv2.common.Name fathers_name = 17;
  }

  ClientData client_data = 5;
}

message ClientData {
  string case_id = 1;
}

enum Parameter {
  PARAMETER_UNSPECIFIED = 0;
  NAME = 1;
  DOB = 2;
  ADDRESS = 3;
}

enum Status {
  PAN_STATUS_UNSPECIFIED = 0;
  PAN_STATUS_ACTIVE = 1;
  PAN_STATUS_INACTIVE = 2;
}

message EmploymentVerificationAdvancedRequest {
  vendorgateway.RequestHeader header = 1;
  api.typesv2.common.PhoneNumber mobile_number = 2;
  string email_id = 3;
  string pan = 4;
  bool trigger_pan_flow = 5;
  ClientData client_data = 6;
}

message EmploymentVerificationAdvancedResponse {
  rpc.Status status = 1;
  string raw_response = 2;
}

message ValidateAndGetEpanInfoRequest {
  vendorgateway.RequestHeader header = 1;
  string request_id = 2;
  api.typesv2.File epan = 3;
  string password = 4;
}

message ValidateAndGetEpanInfoResponse {
  rpc.Status status = 1;
  .pan.epan.EPANData epan_data = 2;
  .pan.epan.EPANValidationStatus epan_validation_status = 3;
  string signed_by = 4;
  string certificate_issuer = 5;
}

message ValidateV2Request {
  vendorgateway.RequestHeader header = 1;
  string pan = 2;
  string name_on_card = 3;
  google.type.Date date_of_birth = 4;
  string father_name = 5;
}

message ValidateV2Response {
  rpc.Status status = 1;
  vendors.wealth.NsdlPanStatus pan_status = 2;
  api.typesv2.common.BooleanEnum name_on_card_match = 3;
  api.typesv2.common.BooleanEnum date_of_birth_match = 4;
  PanAadhaarLinkStatus aadhar_seeding_status = 5;
  api.typesv2.common.BooleanEnum father_name_match = 6;
}
