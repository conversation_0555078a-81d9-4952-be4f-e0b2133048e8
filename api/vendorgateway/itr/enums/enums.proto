// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.itr.enums;

option go_package = "github.com/epifi/gamma/api/vendorgateway/itr/enums";
option java_package = "com.github.epifi.gamma.api.vendorgateway.itr.enums";

// Types of ITR forms
// Reference - https://incometaxindia.gov.in/pages/downloads/income-tax-return.aspx
enum ItrFormType {
  ITR_FORM_TYPE_UNSPECIFIED = 0;
  ITR_FORM_TYPE_ONE = 1;
  ITR_FORM_TYPE_TWO = 2;
  ITR_FORM_TYPE_THREE = 3;
  ITR_FORM_TYPE_FOUR = 4;
  ITR_FORM_TYPE_FIVE = 5;
  ITR_FORM_TYPE_SIX = 6;
  ITR_FORM_TYPE_SEVEN = 7;
}

enum ItrTaxpayerStatus {
  ITR_TAXPAYER_STATUS_UNSPECIFIED = 0;
  ITR_TAXPAYER_STATUS_INDIVIDUAL = 1;
}

enum ItrTaxpayerResidentialStatus {
  ITR_TAXPAYER_RESIDENTIAL_STATUS_UNSPECIFIED = 0;
  ITR_TAXPAYER_RESIDENTIAL_STATUS_RESIDENT = 1;
  ITR_TAXPAYER_RESIDENTIAL_STATUS_RESIDENT_BUT_NOT_ORDINARILY_RESIDENT = 2;
  ITR_TAXPAYER_RESIDENTIAL_STATUS_NON_RESIDENT = 3;
}
