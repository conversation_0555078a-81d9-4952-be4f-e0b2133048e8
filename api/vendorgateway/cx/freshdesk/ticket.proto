// protolint:disable MAX_LINE_LENGTH

// A service for handling communication with users through notifications.
syntax = "proto3";

package cx.freshdesk;

import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/device.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk";
option java_package = "com.github.epifi.gamma.api.vendorgateway.cx.freshdesk";

// mapping the attribute as it is from freshsdesk payload
// for attribute meanings please see this : https://developers.freshdesk.com/api/#tickets
message Ticket {
  // Ticket attachments. The total size of these attachments cannot exceed 20MB.
  repeated TicketAttachment attachments = 1 [json_name = "attachments"];

  // Email address added in the 'cc' field of the incoming ticket email
  repeated string cc_emails = 2 [json_name = "cc_emails"];

  // ID of the company to which this ticket belongs
  int64 company_id = 3 [json_name = "company_id"];

  // Key value pairs containing the names and values of custom fields.
  CustomFieldsWithEnum custom_fields_with_enum = 4 [json_name = "custom_fields_with_enum"];

  // Set to true if the ticket has been deleted/trashed. Deleted tickets will not be displayed in any views except the "deleted" filter
  bool deleted = 5 [json_name = "deleted"];

  // HTML content of the ticket
  string description = 6 [json_name = "description"];

  // Content of the ticket in plain text
  string description_text = 7 [json_name = "description_text"];

  // Timestamp that denotes when the ticket is due to be resolved
  google.protobuf.Timestamp due_by = 8 [json_name = "due_by"];

  // Email address of the requester. If no contact exists with this email address in Freshdesk, it will be added as a new contact.
  string email = 9 [json_name = "email"];

  // ID of email config which is used for this ticket.
  //(i.e., <EMAIL>/<EMAIL>)
  int64 email_config_id = 10 [json_name = "email_config_id"];

  // Facebook ID of the requester. A contact should exist with this facebook_id in Freshdesk.
  string facebook_id = 11 [json_name = "facebook_id"];

  // Timestamp that denotes when the first response is due
  google.protobuf.Timestamp fr_due_by = 12 [json_name = "fr_due_by"];

  // Set to true if the ticket has been escalated as the result of first response time being breached
  bool fr_escalated = 13 [json_name = "fr_escalated"];

  // Email address(e)s added while forwarding a ticket
  repeated string fwd_emails = 14 [json_name = "fwd_emails"];

  // ID of the group to which the ticket has been assigned
  int64 group_id = 15 [json_name = "group_id"];

  // Unique ID of the ticket
  int64 id = 16 [json_name = "id"];

  // Set to true if the ticket has been escalated for any reason
  bool is_escalated = 17 [json_name = "is_escalated"];

  // Name of the requester
  string name = 18 [json_name = "name"];

  // Phone number of the requester. If no contact exists with this phone number in Freshdesk,
  // it will be added as a new contact. If the phone number is set and the email address is not, then the name attribute is mandatory.
  string phone = 19 [json_name = "phone"];

  // Priority of the ticket
  Priority priority = 20 [json_name = "priority"];

  // ID of the product to which the ticket is associated
  int64 product_id = 21 [json_name = "product_id"];

  // Email address added while replying to a ticket
  repeated string reply_cc_emails = 22 [json_name = "reply_cc_emails"];

  // User ID of the requester. For existing contacts, the requester_id can be passed instead of the requester's email.
  int64 requester_id = 23 [json_name = "requester_id"];

  // ID of the agent to whom the ticket has been assigned
  int64 responder_id = 24 [json_name = "responder_id"];

  // The channel through which the ticket was created
  Source source = 25 [json_name = "source"];

  // Set to true if the ticket has been marked as spam
  bool spam = 26 [json_name = "spam"];

  // Status of the ticket
  Status status = 27 [json_name = "status"];

  // Subject of the ticket
  string subject = 28 [json_name = "subject"];

  // Tags that have been associated with the ticket
  repeated string tags = 29 [json_name = "tags"];

  Requester requester = 30 [json_name = "requester"];

  // Email addresses to which the ticket was originally sent
  repeated string to_emails = 31 [json_name = "to_emails"];

  // Twitter handle of the requester. If no contact exists with this handle in Freshdesk, it will be added as a new contact.
  string twitter_id = 32 [json_name = "twitter_id"];

  // Helps categorize the ticket according to the different kinds of issues your support team deals with.
  string type = 33 [json_name = "type"];

  // Ticket creation timestamp
  google.protobuf.Timestamp created_at = 34 [json_name = "created_at"];

  // Ticket updated timestamp
  google.protobuf.Timestamp updated_at = 35 [json_name = "updated_at"];

  Group group = 36 [json_name = "group"];

  // this field should be used if you just need to access custom field values for display purpose
  CustomFieldsWithValue custom_fields_with_value = 37 [json_name = "custom_fields_with_value"];
}

message TicketAttachment {
  // location of uploaded file in ticket-attachments bucket
  string file_path = 1;
}

message CustomFieldsWithEnum {
  string actual_user_id = 1 [json_name = "cf_actual_user_id"];
  bool callback_customer = 2 [json_name = "cf_callback_customer"];
  ProductCategory product_category = 3 [json_name = "cf_product_category"];
  TransactionType transaction_type = 4 [json_name = "cf_transaction_type"];
  DisputeStatus dispute_status = 5 [json_name = "cf_dispute_status"];
  string entity_id = 6 [json_name = "cf_entityid"];
  oneof product_category_details {
    ProductCategoryDetailsWaitlist product_category_details_waitlist = 7;
    ProductCategoryDetailsOnboarding product_category_details_onboarding = 9;
    ProductCategoryDetailsAccounts product_category_details_accounts = 10;
    ProductCategoryDetailsDebitCard product_category_details_debit_card = 29;
    ProductCategoryDetailsTransactions product_category_details_transactions = 32;
    ProductCategoryDetailsWealthMutualFunds product_category_details_wealth_mutual_funds = 53;
    ProductCategoryDetailsSave product_category_details_save = 54;
  }
  oneof sub_category {
    SubCategoryWaitlistManualApproval sub_category_waitlist_manual_approval = 8;
    SubCategoryPin sub_category_pin = 11;
    SubCategoryDebitCardActivation sub_category_debit_card_activation = 30;
    SubCategoryDebitCardDelivery sub_category_debit_card_delivery = 31;
    SubCategoryTransactionsDebitedViaFiApp sub_category_transactions_debited_via_fi_app = 33;
    SubCategoryTransactionsDebitedFromFiAccountViaOtherApp sub_category_transactions_debited_from_fi_account_via_other_app = 34;
    SubCategoryMinKycExpiry sub_category_min_kyc_expiry = 45;
    SubCategoryTransactionsCardsATM sub_category_transactions_cards_atm = 48;
    SubCategoryTransactionsUPIUnableToTransact sub_category_transactions_upi_unable_to_transact = 49;
    SubCategoryWealthMutualFundsInvestmentTransactionSuccessful sub_category_wealth_mutual_funds_investment_transaction_successful = 52;
    SubCategorySaveFixedDeposit sub_category_save_fixed_deposit = 55;
    SubCategorySaveSmartDeposit sub_category_save_smart_deposit = 56;
  }
  // field to indicate os type of user associated to the ticket
  api.typesv2.common.Platform os_type = 20 [json_name = "cf_os"];
  // indicates app version
  int64 app_version = 21 [json_name = "cf_app_version"];
  // date of the transaction
  // this field will be present if the ticket is due to a txn issue
  google.protobuf.Timestamp transaction_date = 22 [json_name = "cf_transaction_date"];
  // provenance of the txn associated with the ticket
  string transaction_provenance = 23 [json_name = "cf_transaction_provenance"];
  // identifies if ticket validation has to be skipped for given ticket or not
  bool validation_status = 24 [json_name = "cf_validation_status"];
  // identifies if ticket is being re-opened or not
  bool reopen_status = 25 [json_name = "cf_reopen_status"];
  // custom field to indicate using what mode ticket was resolved
  // for example auto-resolution, bulk resolution, manual resolution etc
  ResolutionMode resolution_mode = 27 [json_name = "cf_resolution_mode"];
  // stores JSON data identifying record from Sherlock which is mark against that ticket
  string product_category_meta = 28 [json_name = "cf_product_category_meta"];
  // identifies if all attempts to capture user's feedback on issue resolution was exhausted
  bool is_all_issue_resolution_feedback_attempt_exhausted = 35 [json_name = "cf_is_dispute_tat_feedback_attempt_exhausted"];
  // this field should be set if user has clicked yes on issue resolution feedback email sent out for dispute case
  bool is_user_clicked_yes_for_dispute_feedback = 36 [json_name = "cf_is_user_clicked_yes_for_dispute_feedback"];
  // this field should be set if user has clicked no on issue resolution feedback email sent out for dispute case
  bool is_user_clicked_no_for_dispute_feedback = 37 [json_name = "cf_is_user_clicked_no_for_dispute_feedback"];
  // sprinklr case number as received in webhook event from sprinklr
  int64 sprinklr_case_number = 38 [json_name = "cf_sprinklr_case_number"];
  // ozonetel call recording link
  string call_recording_link = 39 [json_name = "cf_call_recording_link"];
  // expected resolution date is determined by using sla config
  google.protobuf.Timestamp expected_resolution_date = 40 [json_name = "cf_expected_resolution_date"];
  // this field will determine who will be able to see the ticket: agent, customer or both
  TicketVisibility ticket_visibility = 41 [json_name = "cf_ticket_visibility"];
  // dispute id is internal Fi id to track the dispute raised by client.
  string dispute_id = 42 [json_name = "cf_dispute_id"];
  // UTR(unique transaction reference) associated, if any, with the ticket. Eg: For fund transfer in case of Min KYC account closure
  string utr = 43 [json_name = "cf_utr"];
  // To indicate whether the UTR filed is updated in the context of Min KYC account closure
  // will be used by ops to send automation mails with UTR
  bool is_utr_updated_for_min_kyc_account_closure = 44 [json_name = "cf_is_utr_updated_for_min_kyc_account_closure"];
  // SavingsAccountBalance categorizes savings account based on the balance present
  SavingsAccountBalance savings_account_balance = 46 [json_name = "cf_savings_account_balance"];
  // Federal customer Id of the user
  string customer_id = 47 [json_name = "cf_customer_id"];
  // Whether there is any Monorail ticket ID associated with this Freshdesk ticket
  MonorailRaised monorail_raised = 50 [json_name = "cf_monorail_raised"];
  // Monorail ticket ID associated with this Freshdesk ticket
  int64 monorail_ticket_id = 51 [json_name = "cf_monorail_ticket_id"];
  // To indicate whether a call is ongoing for this ticket. This is a Yes/No dropdown field
  // This will be used to skip agent validation on Sherlock during the call
  api.typesv2.common.BooleanEnum is_call_ongoing = 57 [json_name = "cf_is_call_ongoing"];
  // boolean to denote whether call summary provided by DS model is attached to ticket
  api.typesv2.common.BooleanEnum is_call_summary_added = 58 [json_name = "cf_is_call_summary_added_do_not_edit"];
  // stores JSON data containing the metadata for loan outcall
  string loan_outcall_metadata = 59 [json_name = "cf_loan_outcall_metadata"];
}

message CustomFieldsWithValue {
  string actual_user_id = 1 [json_name = "cf_actual_user_id"];
  bool callback_customer = 2 [json_name = "cf_callback_customer"];
  string product_category = 3 [json_name = "cf_product_category"];
  string transaction_type = 4 [json_name = "cf_transaction_type"];
  string dispute_status = 5 [json_name = "cf_dispute_status"];
  string entity_id = 6 [json_name = "cf_entityid"];
  string product_category_details = 7 [json_name = "cf_product_category_details"];
  string sub_category = 8 [json_name = "cf_subcategory"];
  string os_type = 9 [json_name = 'cf_os'];
  int64 app_version = 10 [json_name = "cf_app_version"];
  // date of the transaction
  // this field will be present if the ticket is due to a txn issue
  google.protobuf.Timestamp transaction_date = 11 [json_name = "cf_transaction_date"];
  // provenance of the txn associated with the ticket
  string transaction_provenance = 12 [json_name = "cf_transaction_provenance"];
  // identifies if ticket validation has to be skipped for given ticket or not
  bool validation_status = 24 [json_name = "cf_validation_status"];
  // identifies if ticket is being re-opened or not
  bool reopen_status = 25 [json_name = "cf_reopen_status"];
  // custom field to indicate response received from federal for that escalated ticket
  string response_from_federal_cc = 26 [json_name = "cf_response_from_federal_cc"];
  // custom field to indicate using what mode ticket was resolved
  // for example auto-resolution, bulk resolution, manual resolution etc
  string resolution_mode = 27 [json_name = "cf_resolution_mode"];
  // stores JSON data identifying record from Sherlock which is mark against that ticket
  string product_category_meta = 28 [json_name = "cf_product_category_meta"];
  // identifies if all attempts to capture user's feedback on issue resolution was exhausted
  bool is_all_issue_resolution_feedback_attempt_exhausted = 29 [json_name = "cf_is_dispute_tat_feedback_attempt_exhausted"];
  // this field should be set if user has clicked yes on issue resolution feedback email sent out for dispute case
  bool is_user_clicked_yes_for_dispute_feedback = 30 [json_name = "cf_is_user_clicked_yes_for_dispute_feedback"];
  // this field should be set if user has clicked no on issue resolution feedback email sent out for dispute case
  bool is_user_clicked_no_for_dispute_feedback = 31 [json_name = "cf_is_user_clicked_no_for_dispute_feedback"];
  // sprinklr case number as received in webhook event from sprinklr
  int64 sprinklr_case_number = 32 [json_name = "cf_sprinklr_case_number"];
  // ozonetel call recording link
  string call_recording_link = 33 [json_name = "cf_call_recording_link"];
  // expected resolution date is determined using sla config
  string expected_resolution_date = 34 [json_name = "cf_expected_resolution_date"];
  // this field will determine who will be able to see the ticket: agent, customer or both
  string ticket_visibility = 35 [json_name = "cf_ticket_visibility"];
  // dispute id is internal Fi id to track the dispute raised by client.
  string dispute_id = 36 [json_name = "cf_dispute_id"];
  // UTR(unique transaction reference) associated, if any, with the ticket. Eg: For fund transfer in case of Min KYC account closure
  string utr = 37 [json_name = "cf_utr"];
  // To indicate whether the UTR filed is updated in the context of Min KYC account closure
  // will be used by ops to send automation mails with UTR
  bool is_utr_updated_for_min_kyc_account_closure = 38 [json_name = "cf_is_utr_updated_for_min_kyc_account_closure"];
  // To categorize savings account based on the balance present
  string savings_account_balance = 39 [json_name = "cf_savings_account_balance"];
  // Federal customer Id of the user
  string customer_id = 40 [json_name = "cf_customer_id"];
  // Whether there is any Monorail ticket ID associated with this Freshdesk ticket
  // This is a dropdown field with yes / no options
  string monorail_raised = 41 [json_name = "cf_monorail_raised"];
  // Monorail ticket ID associated with this Freshdesk ticket
  int64 monorail_ticket_id = 42 [json_name = "cf_monorail_ticket_id"];
  // To indicate whether a call is ongoing for this ticket. This is a Yes/No dropdown field
  // This will be used to skip agent validation on Sherlock during the call
  string is_call_ongoing = 43 [json_name = "cf_is_call_ongoing"];
  // boolean to denote whether call summary provided by DS model is attached to ticket
  string is_call_summary_added = 44 [json_name = "cf_is_call_summary_added_do_not_edit"];
  // stores JSON data containing the metadata for loan outcall
  string loan_outcall_metadata = 45 [json_name = "cf_loan_outcall_metadata"];
}

message Requester {
  string email = 1;

  int64 id = 2;

  string mobile = 3;

  string name = 4;

  string phone = 5;
}

// Freshdesk ticket properties enums
enum Source {
  SOURCE_UNSPECIFIED = 0;
  EMAIL = 1;
  PORTAL = 2;
  PHONE = 3;
  CHAT = 7;
  MOBIHELP = 8;
  FEEDBACK_WIDGET = 9;
  OUTBOUND_EMAIL = 10;
}

enum ProductCategory {
  PRODUCT_CATEGORY_UNSPECIFIED = 0;
  TRANSACTION = 1;
  ACCOUNTS = 2;
  ONBOARDING = 3;
  SAVE = 4;
  WAITLIST = 5;
  RE_ONBOARDING = 6;
  PRODUCT_CATEGORY_REWARDS = 7;
  PRODUCT_CATEGORY_FIT = 8;
  PRODUCT_CATEGORY_DEBIT_CARD = 9;
  PRODUCT_CATEGORY_REFERRALS = 10;
  PRODUCT_CATEGORY_CONNECTED_ACCOUNTS = 11;
  PRODUCT_CATEGORY_FRAUD_AND_RISK = 12;
  PRODUCT_CATEGORY_JUMP_P2P = 13;
  PRODUCT_CATEGORY_PROFILE = 14;
  PRODUCT_CATEGORY_SALARY_ACCOUNT = 15;
  PRODUCT_CATEGORY_SEARCH = 16;
  PRODUCT_CATEGORY_WEALTH_ONBOARDING = 17;
  PRODUCT_CATEGORY_WEALTH_MUTUAL_FUNDS = 18;
  PRODUCT_CATEGORY_APP_CRASH = 19;
  PRODUCT_CATEGORY_DATA_DELETION = 20;
  PRODUCT_CATEGORY_SCREENER = 21;
  PRODUCT_CATEGORY_GOOGLE_TOKEN_EXPIRED = 22;
  PRODUCT_CATEGORY_LANGUAGE_CALLBACK = 23;
  PRODUCT_CATEGORY_CATEGORY_NOT_FOUND = 24;
  PRODUCT_CATEGORY_KYC_OUTCALL = 25;
  PRODUCT_CATEGORY_TRANSACTION_ISSUES = 26;
  PRODUCT_CATEGORY_REWARDS_NEW = 27;
  PRODUCT_CATEGORY_REFERRALS_NEW = 28;
  PRODUCT_CATEGORY_GENERAL_ENQUIRIES_ABOUT_FI = 29;
  PRODUCT_CATEGORY_NO_RESPONSE_OR_BLANK_CHAT = 30;
  PRODUCT_CATEGORY_CALL_DROP_OR_DISCONNECTED = 31;
  PRODUCT_CATEGORY_INSTANT_LOANS = 32;
  PRODUCT_CATEGORY_TIERING_PLANS = 33;
  PRODUCT_CATEGORY_CREDIT_CARD = 34;
  PRODUCT_CATEGORY_US_STOCKS = 35;
  PRODUCT_CATEGORY_DEVICE = 36;
  PRODUCT_CATEGORY_RISK = 37;
  PRODUCT_CATEGORY_ON_APP_TRANSACTIONS = 38;
  PRODUCT_CATEGORY_OFF_APP_TRANSACTIONS = 39;
  PRODUCT_CATEGORY_INSTANT_SALARY = 40;
  PRODUCT_CATEGORY_SIMPLIFI_CREDIT_CARD = 41;
  PRODUCT_CATEGORY_LAMF = 42;
  PRODUCT_CATEGORY_MAGNIFI_CREDIT_CARD = 43;
  PRODUCT_CATEGORY_SALARY_LITE = 44;
  PRODUCT_CATEGORY_FI_STORE = 45;
  PRODUCT_CATEGORY_GENERAL_ENQUIRY = 46;
  PRODUCT_CATEGORY_APP_RELATED = 47;
  PRODUCT_CATEGORY_DEPOSITS_AND_INVESTMENTS = 48;
  PRODUCT_CATEGORY_INCOMPLETE_CONVERSATION = 49;
  PRODUCT_CATEGORY_LOANS = 50;
  PRODUCT_CATEGORY_NET_WORTH = 51;
  PRODUCT_CATEGORY_SERVICE_REQUESTS = 52;
}

enum ProductCategoryDetailsWaitlist {
  PRODUCT_CATEGORY_DETAILS_UNSPECIFIED = 0;
  MANUAL_WHITELISTING = 1;
}

enum ProductCategoryDetailsOnboarding {
  PRODUCT_CATEGORY_DETAILS_ONBOARDING_UNSPECIFIED = 0;
  APP_DOWNLOAD_ISSUE = 1;
  DEVICE_CHECK_FAILURE = 2;
  PHONE_NUMBER_OTP = 3;
  EMAIL_SELECTION_FAILURE = 4;
  MOTHER_FATHER_NAME = 5;
  PAN_NAME_VALIDATION_FAILURE = 6;
  EXISTING_FEDERAL_ACCOUNT = 7;
  KYC = 8;
  LIVENESS = 9;
  FACEMATCH_FAIL = 10;
  UN_NAME_CHECK = 11;
  CONFIRM_CARD_MAILING_ADDRESS = 12;
  UPI_CONSENT_FAILURE = 13;
  DEVICE_REGISTRATION_FAILURE = 14;
  CUSTOMER_CREATION_FAILURE = 15;
  ACCOUNT_OPENING_DELAYED = 16;
  CARD_CREATION_FAILURE = 17;
  CARD_PIN_SET_FAILURE = 18;
  UPI_SETUP_FAILURE = 19;
  VKYC = 20;
  REONBOARDING = 21;
}

enum ProductCategoryDetailsAccounts {
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_PIN = 1;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_MIN_KYC_EXPIRY = 2;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CLOSURE_REQUEST = 3;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_OPENING_ISSUES = 4;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_UPGRADE_DOWNGRADE = 5;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_BALANCE_TRANSFER = 6;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CATEGORY_NOT_FOUND = 7;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_CHEQUEBOOK_RELATED = 8;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_DORMANT = 9;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_FEES_AND_CHARGES = 10;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_KYC_RELATED = 11;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_LIEN_ON_ACCOUNT = 12;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_RE_LOGIN_ISSUES = 13;
  PRODUCT_CATEGORY_DETAILS_ACCOUNTS_SALARY_PROGRAM = 14;
}

enum SubCategoryMinKycExpiry {
  SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_MIN_KYC_EXPIRY_BALANCE_REFUND = 1;
}

enum SubCategoryPin {
  SUB_CATEGORY_PIN_UNSPECIFIED = 0;
  SUB_CATEGORY_PIN_UPI_PIN = 1;
  SUB_CATEGORY_PIN_DEVICE_PIN = 2;
  SUB_CATEGORY_PIN_APP_PIN = 3;
}

enum ProductCategoryDetailsDebitCard {
  PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_ACTIVATION = 1;
  PRODUCT_CATEGORY_DETAILS_DEBIT_CARD_DELIVERY = 2;
}

enum SubCategoryDebitCardActivation {
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNSPECIFIED = 0;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_QR_CODE_NOT_WORKING = 1;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_UNABLE_TO_SET_PIN = 2;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_POS = 3;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_CONTACTLESS = 4;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_CANNOT_ENABLE_ATM_WITHDRAWAL = 5;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_OTP_NOT_RECEIVED = 6;
  SUB_CATEGORY_DEBIT_CARD_ACTIVATION_HOW_TO_ACTIVATE = 7;
}

enum SubCategoryDebitCardDelivery {
  SUB_CATEGORY_DEBIT_CARD_DELIVERY_UNSPECIFIED = 0;
  SUB_CATEGORY_DEBIT_CARD_DELIVERY_TRACKING = 1;
  SUB_CATEGORY_DEBIT_CARD_DELIVERY_DID_NOT_RECEIVED_CARD = 2;
}

enum SubCategoryWaitlistManualApproval {
  SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_UNSPECIFIED = 0;
  SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_NEW = 1;
  SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_APPROVED = 2;
  SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_REJECTED = 3;
  SUB_CATEGORY_WAITLIST_MANUAL_APPROVAL_ON_HOLD = 4;
}

enum ProductCategoryDetailsTransactions {
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_VIA_FI_APP = 1;
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP = 2;
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_CARDS_ATM = 3;
  PRODUCT_CATEGORY_DETAILS_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT = 4;
}

enum ProductCategoryDetailsWealthMutualFunds {
  PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL = 1;
}

enum ProductCategoryDetailsSave {
  PRODUCT_CATEGORY_DETAILS_SAVE_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_SAVE_FIXED_DEPOSIT = 1;
  PRODUCT_CATEGORY_DETAILS_SAVE_SMART_DEPOSIT = 2;
}


// App related L2 categories
enum ProductCategoryDetailsApp {
  PRODUCT_CATEGORY_DETAILS_APP_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_APP_RELATED_ISSUES = 1;
  PRODUCT_CATEGORY_DETAILS_APP_RE_LOGIN_ISSUES = 2;
}

// Card related L2 categories
enum ProductCategoryDetailsCard {
  PRODUCT_CATEGORY_DETAILS_CARD_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_CARD_REQUEST = 1;
  PRODUCT_CATEGORY_DETAILS_CARD_SETTINGS = 2;
}

// FIT related L2 categories
enum ProductCategoryDetailsFit {
  PRODUCT_CATEGORY_DETAILS_FIT_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_FIT_RULES = 1;
}

// Jump related L2 categories
enum ProductCategoryDetailsJump {
  PRODUCT_CATEGORY_DETAILS_JUMP_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_JUMP_GENERAL = 1;
}

// Mutual Funds related L2 categories
enum ProductCategoryDetailsMutualFunds {
  PRODUCT_CATEGORY_DETAILS_MUTUAL_FUNDS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_MUTUAL_FUNDS_INVESTMENTS = 1;
  PRODUCT_CATEGORY_DETAILS_MUTUAL_FUNDS_ONBOARDING = 2;
  PRODUCT_CATEGORY_DETAILS_MUTUAL_FUNDS_WITHDRAWALS = 3;
}

// US Stocks related L2 categories
enum ProductCategoryDetailsUSStocks {
  PRODUCT_CATEGORY_DETAILS_US_STOCKS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_US_STOCKS_GENERAL = 1;
  PRODUCT_CATEGORY_DETAILS_US_STOCKS_WALLET_ISSUES = 2;
  PRODUCT_CATEGORY_DETAILS_US_STOCKS_BUYING = 3;
}

// Product related L2 categories
enum ProductCategoryDetailsProduct {
  PRODUCT_CATEGORY_DETAILS_PRODUCT_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_PRODUCT_DEPRECATED = 1;
}

// Communication related L2 categories
enum ProductCategoryDetailsCommunication {
  PRODUCT_CATEGORY_DETAILS_COMMUNICATION_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_COMMUNICATION_BLANK_CHAT = 1;
  PRODUCT_CATEGORY_DETAILS_COMMUNICATION_CALL_DROP = 2;
  PRODUCT_CATEGORY_DETAILS_COMMUNICATION_INCOMPLETE_EMAIL = 3;
}

// Loan related L2 categories
enum ProductCategoryDetailsLoan {
  PRODUCT_CATEGORY_DETAILS_LOAN_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_LOAN_APPLICATION_DISBURSAL = 1;
  PRODUCT_CATEGORY_DETAILS_LOAN_BUREAU_CIBIL = 2;
  PRODUCT_CATEGORY_DETAILS_LOAN_COLLECTIONS = 3;
  PRODUCT_CATEGORY_DETAILS_LOAN_EMI_RELATED = 4;
  PRODUCT_CATEGORY_DETAILS_LOAN_LAMF_SHORTFALL = 5;
  PRODUCT_CATEGORY_DETAILS_LOAN_CLOSURE = 6;
  PRODUCT_CATEGORY_DETAILS_LOAN_OUTCALLING = 7;
}

// Connection related L2 categories
enum ProductCategoryDetailsConnection {
  PRODUCT_CATEGORY_DETAILS_CONNECTION_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_CONNECTION_UNABLE_TO_CONNECT = 1;
  PRODUCT_CATEGORY_DETAILS_CONNECTION_UNABLE_TO_DISCONNECT = 2;
}

// Rewards related L2 categories
enum ProductCategoryDetailsRewards {
  PRODUCT_CATEGORY_DETAILS_REWARDS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_REWARDS_FI_POINTS_NOT_REFUNDED = 1;
  PRODUCT_CATEGORY_DETAILS_REWARDS_GIFT_CARDS = 2;
  PRODUCT_CATEGORY_DETAILS_REWARDS_INCORRECT_REWARD = 3;
  PRODUCT_CATEGORY_DETAILS_REWARDS_NOT_RECEIVED = 4;
}

// Security related L2 categories
enum ProductCategoryDetailsSecurity {
  PRODUCT_CATEGORY_DETAILS_SECURITY_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_SECURITY_BANK_INITIATED_FREEZE = 1;
  PRODUCT_CATEGORY_DETAILS_SECURITY_LEA_NPCI_COMPLAINT = 2;
}

// Service request L2 categories
enum ProductCategoryDetailsServiceRequest {
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_CALLBACK = 1;
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_DATA_DELETION = 2;
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_NACH_MANDATES = 3;
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_REVOKE_APP_ACCESS = 4;
  PRODUCT_CATEGORY_DETAILS_SERVICE_REQUEST_STOP_SERVICES = 5;
}

// Transaction related L2 categories
enum ProductCategoryDetailsTransaction {
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_AMOUNT_DEBITED = 1;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_AMOUNT_DEBITED_NOT_CREDITED = 2;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_AUTOMATED_PAYMENTS = 3;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_UNAUTHORIZED = 4;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_CHEQUE = 5;
  PRODUCT_CATEGORY_DETAILS_TRANSACTION_DATA_NOT_REFRESHED = 6;
}

// Business related L2 categories
enum ProductCategoryDetailsBusiness {
  PRODUCT_CATEGORY_DETAILS_BUSINESS_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_BUSINESS_COLLABORATION = 1;
}

// Net banking related L2 categories
enum ProductCategoryDetailsNetBanking {
  PRODUCT_CATEGORY_DETAILS_NET_BANKING_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_NET_BANKING_GENERAL = 1;
}

// User related L2 categories
enum ProductCategoryDetailsUser {
  PRODUCT_CATEGORY_DETAILS_USER_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_USER_UNREGISTERED = 1;
  PRODUCT_CATEGORY_DETAILS_USER_UNABLE_TO_PAY = 2;
}

// Credit related L2 categories
enum ProductCategoryDetailsCredit {
  PRODUCT_CATEGORY_DETAILS_CREDIT_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_CREDIT_PENDING_TO_FI = 1;
}

// Document related L2 categories
enum ProductCategoryDetailsDocument {
  PRODUCT_CATEGORY_DETAILS_DOCUMENT_UNSPECIFIED = 0;
  PRODUCT_CATEGORY_DETAILS_DOCUMENT_REQUEST = 1;
}

enum SubCategoryTransactionsDebitedViaFiApp {
  SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_MERCHANT = 1;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_VIA_FI_APP_BUT_NOT_CREDITED_TO_BENEFICIARY = 2;
}

enum SubCategorySaveFixedDeposit {
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_UNSPECIFIED = 0;
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_PRE_CLOSURE = 1;
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_MATURITY = 2;
  SUB_CATEGORY_SAVE_FIXED_DEPOSIT_INTEREST_PAYOUT = 3;
}

enum SubCategorySaveSmartDeposit {
  SUB_CATEGORY_SAVE_SMART_DEPOSIT_UNSPECIFIED = 0;
  SUB_CATEGORY_SAVE_SMART_DEPOSIT_PRE_CLOSURE = 1;
  SUB_CATEGORY_SAVE_SMART_DEPOSIT_MATURITY = 2;
  SUB_CATEGORY_SAVE_SMART_DEPOSIT_INTEREST_PAYOUT = 3;
}

enum SubCategoryTransactionsDebitedFromFiAccountViaOtherApp {
  SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_MERCHANT = 1;
  SUB_CATEGORY_TRANSACTIONS_DEBITED_FROM_FI_ACCOUNT_VIA_OTHER_APP_BUT_NOT_CREDITED_TO_BENEFICIARY = 2;
}

enum SubCategoryTransactionsCardsATM {
  SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTIONS_CARDS_ATM_DEBITED_BUT_NOT_DISPENSED_AT_MACHINE = 1;
}

enum SubCategoryTransactionsUPIUnableToTransact {
  SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTIONS_UPI_UNABLE_TO_TRANSACT_UPI_PIN_TRIES_EXCEEDED = 1;
}

enum SubCategoryWealthMutualFundsInvestmentTransactionSuccessful {
  SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNSPECIFIED = 0;
  SUB_CATEGORY_WEALTH_MUTUAL_FUNDS_INVESTMENT_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED = 1;
}

enum TransactionType {
  TRANSACTION_TYPE_UNSPECIFIED = 0;
  UPI = 1;
  RTGS = 2;
  NEFT = 3;
  DEBIT_CARD = 4;
  IMPS = 5;
  ECOM = 6;
  POS_ATM = 7;
  INTRA_BANK = 8;
  // Adding an enum to explicitly differentiate cases of unset versus set to Unknown
  TRANSACTION_TYPE_UNKNOWN = 9;
}
// Freshdesk ticket properties enums
enum Status {
  STATUS_UNSPECIFIED = 0;
  OPEN = 2;
  PENDING = 3;
  RESOLVED = 4;
  CLOSED = 5;
  WAITING_ON_THIRD_PARTY = 7;
  ESCALATED_TO_L2 = 8;
  ESCALATED_TO_FI_ENG = 9;
  ESCALATED_TO_FI_OM = 10;
  ESCALATED_TO_FEDERAL = 11;
  // The freshdesk ticket has been sent to Product team for resolution
  SEND_TO_PRODUCT = 12;
  // The product team has accepted and is working on the ticket
  WAITING_ON_PRODUCT = 13;
  // The issue resolved by product has been reopened
  REOPEN = 14;
  // The product team asked for more clarification from the customer
  NEEDS_CLARIFICATION_FROM_CX = 15;
  // Status corresponding to "Waiting on Customer" on freshdesk.
  WAITING_ON_CUSTOMER = 16;
}

enum Group {
  GROUP_UNSPECIFIED = 0;

  CALLBACK = 1;

  EPIFI_ESCALATION = 2;

  ESCALATED_CASES_CLOSURE = 3;

  FEDERAL_ESCALATIONS = 4;

  L1_SUPPORT = 5;

  L2_SUPPORT = 6;

  NON_SFTP_ESCALATIONS = 7;

  SFTP_ESCALATIONS = 8;

  SFTP_PENDING_GROUP = 9;

  FEDERAL_UPDATES = 10;

  L1_SUPPORT_WAITLIST = 11;

  GROUP_RISK_OPS = 12;

  GROUP_ACCOUNT_CLOSURE_RISK_BLOCK = 13;

  GROUP_L1_SUPPORT_CALL = 14;

  GROUP_L1_SUPPORT_CHAT = 15;

  GROUP_L1_SUPPORT_EMAIL = 16;

  GROUP_L1_SUPPORT_SOCIAL_MEDIA = 17;

  GROUP_OUTBOUND_CALL_BACK = 18;

  GROUP_LOAN_OUTBOUND_CALL = 20;
}

// Freshdesk ticket properties enums
enum Priority {
  PRIORITY_UNSPECIFIED = 0;
  LOW = 1;
  MEDIUM = 2;
  HIGH = 3;
  URGENT = 4;
}

// Freshdesk ticket properties enums
enum AssociationType {
  ASSOCIATION_TYPE_UNSPECIFIED = 0;
  PARENT = 1;
  CHILD = 2;
  TRACKER = 3;
  RELATED = 4;
}

enum DisputeStatus {
  DISPUTE_STATUS_UNSPECIFIED = 0;
  ACCEPTED = 1;
  REJECTED = 2;
}

enum ResolutionMode {
  RESOLUTION_MODE_UNSPECIFIED = 0;
  // the ticket was resolved using some automation job
  RESOLUTION_MODE_AUTO_RESOLUTION = 1;
  // the ticket was resolved in bulk using sherlock
  RESOLUTION_MODE_BULK_RESOLUTION = 2;
  // ticket was resolved manually by support agent
  RESOLUTION_MODE_MANUAL_RESOLUTION = 3;
  // ticket was resolved by watson service
  RESOLUTION_MODE_WATSON_RESOLUTION = 4;
}

// enum to represent ticket visibility, to determine whom to show the ticket
enum TicketVisibility {
  TICKET_VISIBILITY_UNSPECIFIED = 0;
  // only show ticket to agent
  TICKET_VISIBILITY_ONLY_AGENT = 1;
  // only show ticket to customer
  TICKET_VISIBILITY_ONLY_CUSTOMER = 2;
  // show ticket to both agent and customer
  TICKET_VISIBILITY_ALL = 3;
}

// enum to categorize savings account based on the balance present
enum SavingsAccountBalance {
  SAVINGS_ACCOUNT_BALANCE_UNSPECIFIED = 0;
  SAVINGS_ACCOUNT_BALANCE_LESS_THAN_1 = 1;
  SAVINGS_ACCOUNT_BALANCE_OTHER = 2;
}

// Whether there is any Monorail ticket ID associated with this Freshdesk ticket
// This is a dropdown field with yes / no options
enum MonorailRaised {
  MONORAIL_RAISED_UNSPECIFIED = 0;
  MONORAIL_RAISED_YES = 1;
  MONORAIL_RAISED_NO = 2;
}

message TicketFilter {

  // name of field on which tickets needs to be filtered
  TicketField ticket_field_name = 1;

  // list of acceptable values for the field
  // different fields could have different types
  // but we will use string for all and will convert to respective type
  // these values will have either OR relation i.e even if one of the values matches ticket will be returned
  repeated string values = 2; // TODO(sachin): Try using google/protobuf/any.proto
  // can be used when the field is of date type
  DateFilter date_filter = 3;
  message DateFilter {
    // The filter value can be a date range or a single date
    oneof value {
      // If it is range filter, At least one of min_val or max_val must be specified
      Range range = 1;
      // If the filter is for single date
      google.protobuf.Timestamp exact_val = 2;
    }
    message Range {
      // lower bound of the range
      google.protobuf.Timestamp min_val = 1;
      // upper bound of the range
      google.protobuf.Timestamp max_val = 2;
    }
  }
}

// list of tickets fields on which filters could be applied
enum TicketField {
  // id of the agent to which the ticket is assigned
  AGENT_ID = 0; // int64

  // the last updated time for the ticket (should be in unix seconds)
  UPDATED_AT = 1; // unix seconds

  // current status of the ticket
  // Status enum values defined in vendorgateway/cx/freshdesk/ticket.proto should be passed as string
  STATUS = 2; // int64

  // group to which ticket is assigned
  // Group enum values defined in vendorgateway/cx/freshdesk/ticket.proto should be passed as string
  GROUP = 3; // group enum

  PRODUCT_CATEGORY = 4;

  PRODUCT_CATEGORY_DETAILS = 5;

  SUBCATEGORY = 6;

  CREATED_AT = 7;
}

// Ticket conversation object
message TicketConversation {
  string body = 1;
  string body_text = 2;
  int64 id = 3;
  bool incoming = 4;
  bool private = 5;
  int64 user_id = 6;
  string support_email = 7;
  // TODO: This is not the source enum defined for ticket source. Refer https://developers.freshdesk.com/api/#conversations
  // Leaving it untouched since it doesn't have any use case so far.
  Source source = 8;
  // TODO: This is not the source enum defined for Product category.
  // Leaving it untouched since it doesn't have any use case so far.
  ProductCategory category = 9;
  repeated string to_emails = 10;
  string from_email = 11;
  repeated string cc_emails = 12;
  repeated string bcc_emails = 13;
  int64 ticket_id = 14;
  repeated Attachment attachments = 15;
  google.protobuf.Timestamp last_edited_at = 16;
  int64 last_edited_user_id = 17;
  google.protobuf.Timestamp created_at = 18;
  google.protobuf.Timestamp updated_at = 19;
}

message Attachment {
  // Identifier of the attachment object, auto-generated when a file is attached to the response or private note.
  int64 id = 1;
  // Format of the attached file. For example: image/jpeg/pdf.
  string content_type = 2;
  int64 size = 3;
  string name = 4;
  string attachment_url = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

// message to unmarshal http get response
message getAllFreshdeskTicketsResponse {
  int64 total = 1 [json_name = 'total'];
  repeated Ticket results = 2 [json_name = 'results'];
}

// Raw ticket object without enum
// this can used in cases where we don't need enum validations
// for example in bulk ticket updates we allow providing values in csv and need to update without enum conversion and validation
message TicketRaw {
  // Email address added in the 'cc' field of the incoming ticket email
  repeated string cc_emails = 2 [json_name = "cc_emails"];

  // ID of the company to which this ticket belongs
  int64 company_id = 3 [json_name = "company_id"];

  CustomFieldsWithValue custom_fields = 4 [json_name = "custom_fields"];

  // Set to true if the ticket has been deleted/trashed. Deleted tickets will not be displayed in any views except the "deleted" filter
  bool deleted = 5 [json_name = "deleted"];

  // HTML content of the ticket
  string description = 6 [json_name = "description"];

  // Content of the ticket in plain text
  string description_text = 7 [json_name = "description_text"];

  // Timestamp that denotes when the ticket is due to be resolved
  google.protobuf.Timestamp due_by = 8 [json_name = "due_by"];

  // Email address of the requester. If no contact exists with this email address in Freshdesk, it will be added as a new contact.
  string email = 9 [json_name = "email"];

  // ID of email config which is used for this ticket.
  //(i.e., <EMAIL>/<EMAIL>)
  int64 email_config_id = 10 [json_name = "email_config_id"];

  // Facebook ID of the requester. A contact should exist with this facebook_id in Freshdesk.
  string facebook_id = 11 [json_name = "facebook_id"];

  // Timestamp that denotes when the first response is due
  google.protobuf.Timestamp fr_due_by = 12 [json_name = "fr_due_by"];

  // Set to true if the ticket has been escalated as the result of first response time being breached
  bool fr_escalated = 13 [json_name = "fr_escalated"];

  // Email address(e)s added while forwarding a ticket
  repeated string fwd_emails = 14 [json_name = "fwd_emails"];

  // ID of the group to which the ticket has been assigned
  int64 group_id = 15 [json_name = "group_id"];

  // Unique ID of the ticket
  int64 id = 16 [json_name = "id"];

  // Set to true if the ticket has been escalated for any reason
  bool is_escalated = 17 [json_name = "is_escalated"];

  // Name of the requester
  string name = 18 [json_name = "name"];

  // Phone number of the requester. If no contact exists with this phone number in Freshdesk,
  // it will be added as a new contact. If the phone number is set and the email address is not, then the name attribute is mandatory.
  string phone = 19 [json_name = "phone"];

  // Priority of the ticket
  int32 priority = 20 [json_name = "priority"];

  // ID of the product to which the ticket is associated
  int64 product_id = 21 [json_name = "product_id"];

  // Email address added while replying to a ticket
  repeated string reply_cc_emails = 22 [json_name = "reply_cc_emails"];

  // User ID of the requester. For existing contacts, the requester_id can be passed instead of the requester's email.
  int64 requester_id = 23 [json_name = "requester_id"];

  // ID of the agent to whom the ticket has been assigned
  int64 responder_id = 24 [json_name = "responder_id"];

  // The channel through which the ticket was created
  int32 source = 25 [json_name = "source"];

  // Set to true if the ticket has been marked as spam
  bool spam = 26 [json_name = "spam"];

  // Status of the ticket
  int64 status = 27 [json_name = "status"];

  // Subject of the ticket
  string subject = 28 [json_name = "subject"];

  // Tags that have been associated with the ticket
  repeated string tags = 29 [json_name = "tags"];

  Requester requester = 30 [json_name = "requester"];

  // Email addresses to which the ticket was originally sent
  repeated string to_emails = 31 [json_name = "to_emails"];

  // Twitter handle of the requester. If no contact exists with this handle in Freshdesk, it will be added as a new contact.
  string twitter_id = 32 [json_name = "twitter_id"];

  // Helps categorize the ticket according to the different kinds of issues your support team deals with.
  string type = 33 [json_name = "type"];

  // Ticket creation timestamp
  google.protobuf.Timestamp created_at = 34 [json_name = "created_at"];

  // Ticket updated timestamp
  google.protobuf.Timestamp updated_at = 35 [json_name = "updated_at"];

  // Ticket attachments. The total size of these attachments cannot exceed 20MB.
  repeated TicketAttachment attachments = 36 [json_name = "attachments"];
}

enum SortByColumn {
  SORT_BY_COLUMN_UNSPECIFIED = 0;
  // tickets needs to be sorted on updated_at column
  SORT_BY_COLUMN_UPDATED_AT = 1;
  // tickets needs to be sorted on created_at column
  SORT_BY_COLUMN_CREATED_AT = 2;
  // tickets needs to be sorted on st column
  SORT_BY_COLUMN_STATUS = 3;
}

// proto message to specify how tickets should be ordered
message TicketSortingInfo {
  // if not passed by default created_at will be used
  SortByColumn sort_by_column = 1;
  // if set to false tickets will be sorted in desc order
  bool is_asc = 2;
}

// enum to indicate which optional column needs to be included in the response in addition to default ticket fields
enum IncludeColumn {
  INCLUDE_COLUMN_UNSPECIFIED = 0;
  // to be used when ticket requester details are needed along with ticket response
  INCLUDE_COLUMN_REQUESTER = 1;
  // to be used when ticket description in needed along with ticket response
  INCLUDE_COLUMN_DESCRIPTION = 2;
}

// L3 categories for account closure requests
enum SubCategoryAccountsClosure {
  SUB_CATEGORY_ACCOUNTS_CLOSURE_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_FREEZE_ON_ACCOUNT = 1;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_IN_APP_REQUEST_RECEIVED = 2;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_MANUAL_REQUEST = 3;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_NOT_CLOSABLE_DUE_TO_PENDING_CHARGES = 4;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_REDIRECTED_TO_APP = 5;
  SUB_CATEGORY_ACCOUNTS_CLOSURE_FULL_KYC_ACCOUNT_CLOSED = 6;
}

// L3 categories for account opening issues
enum SubCategoryAccountsOpening {
  SUB_CATEGORY_ACCOUNTS_OPENING_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_OPENING_ADD_FUNDS_ON_APP = 1;
  SUB_CATEGORY_ACCOUNTS_OPENING_APP_DOWNLOAD = 2;
  SUB_CATEGORY_ACCOUNTS_OPENING_DEVICE_REGISTRATION_FAILURE = 3;
  SUB_CATEGORY_ACCOUNTS_OPENING_CARD_CREATION_PIN_SETUP_FAILURE = 4;
  SUB_CATEGORY_ACCOUNTS_OPENING_CONSENT_RELATED = 5;
  SUB_CATEGORY_ACCOUNTS_OPENING_CUSTOMER_CREATION = 6;
  SUB_CATEGORY_ACCOUNTS_OPENING_EXISTING_FEDERAL_ACCOUNT = 7;
  SUB_CATEGORY_ACCOUNTS_OPENING_KYC_RELATED = 8;
  SUB_CATEGORY_ACCOUNTS_OPENING_LIVENESS_FACEMATCH_ISSUE = 9;
  SUB_CATEGORY_ACCOUNTS_OPENING_APP_SCREENING = 10;
  SUB_CATEGORY_ACCOUNTS_OPENING_REFUND_FOR_ADD_FUNDS = 11;
  SUB_CATEGORY_ACCOUNTS_OPENING_REOPEN_CLOSED_ACCOUNT = 12;
  SUB_CATEGORY_ACCOUNTS_OPENING_STUCK_AT_EMAIL_VERIFICATION = 13;
  SUB_CATEGORY_ACCOUNTS_OPENING_VKYC_ISSUES = 14;
}

// L3 categories for account upgrade/downgrade
enum SubCategoryAccountsUpgradeDowngrade {
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_FUNDS_ADDED_BUT_ACCOUNT_NOT_UPGRADED = 1;
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_TIER_DOWNGRADE = 2;
  SUB_CATEGORY_ACCOUNTS_UPGRADE_DOWNGRADE_WITHIN_COOL_OFF_PERIOD = 3;
}

// L3 categories for account KYC related issues
enum SubCategoryAccountsKYC {
  SUB_CATEGORY_ACCOUNTS_KYC_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_KYC_MIN_KYC_EXPIRED = 1;
  SUB_CATEGORY_ACCOUNTS_KYC_UNABLE_TO_SUBMIT_FORM = 2;
  SUB_CATEGORY_ACCOUNTS_KYC_NOT_UPDATED = 3;
  SUB_CATEGORY_ACCOUNTS_KYC_UPDATED_BUT_ACCOUNT_NOT_ACTIVATED = 4;
  SUB_CATEGORY_ACCOUNTS_KYC_SIGNATURE_NOT_UPDATED = 5;
}

// L3 categories for chequebook related issues
enum SubCategoryAccountsChequebook {
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_CHARGES_WAIVER = 1;
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_NOT_RECEIVED = 2;
  SUB_CATEGORY_ACCOUNTS_CHEQUEBOOK_UNABLE_TO_UPLOAD_SIGNATURE = 3;
}

// L3 categories for account fees and charges
enum SubCategoryAccountsFeesCharges {
  SUB_CATEGORY_ACCOUNTS_FEES_CHARGES_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_FEES_CHARGES_AMB = 1;
}

// L3 categories for account information
enum SubCategoryAccountsInfo {
  SUB_CATEGORY_ACCOUNTS_INFO_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_INFO_ACCOUNT_INFO = 1;
  SUB_CATEGORY_ACCOUNTS_INFO_ACCOUNT_TIER_DETAILS_BENEFITS = 2;
  SUB_CATEGORY_ACCOUNTS_INFO_APP_SETTINGS = 3;
  SUB_CATEGORY_ACCOUNTS_INFO_NR_ACCOUNT = 4;
  SUB_CATEGORY_ACCOUNTS_INFO_RE_KYC_ISSUES = 5;
  SUB_CATEGORY_ACCOUNTS_INFO_INFORMATION_REGARDING_LIEN = 6;
}

// L3 categories for app login issues
enum SubCategoryAccountsAppLogin {
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_ISSUES = 1;
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_DEVICE_PASSWORD_NOT_ACCEPTED = 2;
  SUB_CATEGORY_ACCOUNTS_APP_LOGIN_RE_LOGIN_BEFORE_SIGNUP = 3;
}

// L3 categories for app-related issues
enum SubCategoryAppRelated {
  SUB_CATEGORY_APP_RELATED_UNSPECIFIED = 0;
  SUB_CATEGORY_APP_RELATED_GENERAL_QUERIES = 1;
  SUB_CATEGORY_APP_RELATED_INSURANCE_RELATED = 2;
  SUB_CATEGORY_APP_RELATED_REGISTRATION = 3;
  SUB_CATEGORY_APP_RELATED_UPGRADE_DOWNGRADE_ISSUE = 4;
  SUB_CATEGORY_APP_RELATED_APP_CRASH = 5;
  SUB_CATEGORY_APP_RELATED_FEATURE_NOT_LOADING = 6;
  SUB_CATEGORY_APP_RELATED_USER_FEEDBACK = 7;
  SUB_CATEGORY_APP_RELATED_FULFILLMENT_RELATED = 8;
  SUB_CATEGORY_APP_RELATED_REWARDS_RELATED = 9;
  SUB_CATEGORY_APP_RELATED_REDIRECTED_TO_BANK = 10;
}

// L3 categories for card-related issues
enum SubCategoryCardRequest {
  SUB_CATEGORY_CARD_REQUEST_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_REQUEST_CARD_DAMAGED = 1;
  SUB_CATEGORY_CARD_REQUEST_CARD_NOT_REQUIRED = 2;
  SUB_CATEGORY_CARD_REQUEST_LOST_STOLEN = 3;
  SUB_CATEGORY_CARD_REQUEST_DIGITAL_CARD = 4;
}

enum SubCategoryCardSettings {
  SUB_CATEGORY_CARD_SETTINGS_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_SETTINGS_ACTIVATE_CARD = 1;
  SUB_CATEGORY_CARD_SETTINGS_CHANGE_USAGE_SETTINGS = 2;
  SUB_CATEGORY_CARD_SETTINGS_PIN_FAILING = 3;
  SUB_CATEGORY_CARD_SETTINGS_TEMPORARY_FREEZE = 4;
  SUB_CATEGORY_CARD_SETTINGS_UNABLE_TO_CHANGE_USAGE_SETTINGS = 5;
}

enum SubCategoryCardDelivery {
  SUB_CATEGORY_CARD_DELIVERY_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_DELIVERY_RTO_REDISPATCH = 1;
  SUB_CATEGORY_CARD_DELIVERY_RTO_REFUND = 2;
}

enum SubCategoryCardCharges {
  SUB_CATEGORY_CARD_CHARGES_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_CHARGES_AMC = 1;
  SUB_CATEGORY_CARD_CHARGES_ECOM_POS_DECLINE_FEES = 2;
  SUB_CATEGORY_CARD_CHARGES_OTHER_CHARGES = 3;
}

enum SubCategoryCardInfo {
  SUB_CATEGORY_CARD_INFO_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_INFO_CARD_DETAILS = 1;
  SUB_CATEGORY_CARD_INFO_DELIVERY_RELATED = 2;
  SUB_CATEGORY_CARD_INFO_MIN_KYC_USER = 3;
}

// L3 categories for ATM transactions
enum SubCategoryATMTransactions {
  SUB_CATEGORY_ATM_TRANSACTIONS_UNSPECIFIED = 0;
  SUB_CATEGORY_ATM_TRANSACTIONS_CDM_CASH_DEPOSIT_NOT_CREDITED = 1;
  SUB_CATEGORY_ATM_TRANSACTIONS_DEBITED_BUT_NOT_DISPENSED = 2;
  SUB_CATEGORY_ATM_TRANSACTIONS_DEBITED_BUT_PARTIALLY_DISPENSED = 3;
  SUB_CATEGORY_ATM_TRANSACTIONS_UNABLE_TO_WITHDRAW_DOMESTIC = 4;
  SUB_CATEGORY_ATM_TRANSACTIONS_UNABLE_TO_WITHDRAW_INTERNATIONAL = 5;
}

// L3 categories for transaction issues
enum SubCategoryTransactionIssues {
  SUB_CATEGORY_TRANSACTION_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTION_ISSUES_AMOUNT_DEBITED_BUT_NOT_CREDITED = 1;
  SUB_CATEGORY_TRANSACTION_ISSUES_TXN_FAILED_DOMESTIC = 2;
  SUB_CATEGORY_TRANSACTION_ISSUES_TXN_FAILED_INTERNATIONAL = 3;
  SUB_CATEGORY_TRANSACTION_ISSUES_ORDER_FAILED_REFUND_NOT_RECEIVED = 4;
  SUB_CATEGORY_TRANSACTION_ISSUES_ORDER_PENDING = 5;
}

// L3 categories for fixed deposit and smart deposit
enum SubCategoryFDSD {
  SUB_CATEGORY_FD_SD_UNSPECIFIED = 0;
  SUB_CATEGORY_FD_SD_UNABLE_TO_CREATE = 1;
  SUB_CATEGORY_FD_SD_UNABLE_TO_MODIFY = 2;
  SUB_CATEGORY_FD_SD_UNABLE_TO_PAUSE = 3;
  SUB_CATEGORY_FD_SD_CANCEL_AUTO_RENEWAL = 4;
  SUB_CATEGORY_FD_SD_FD_CLOSED_BUT_AMOUNT_NOT_RECEIVED = 5;
  SUB_CATEGORY_FD_SD_INCORRECT_MATURITY_AMOUNT = 6;
  SUB_CATEGORY_FD_SD_UNABLE_TO_CLOSE_FD = 7;
  SUB_CATEGORY_FD_SD_SD_CLOSED_BUT_AMOUNT_NOT_RECEIVED = 8;
  SUB_CATEGORY_FD_SD_UNABLE_TO_CLOSE_SD = 9;
}

// L3 categories for FIT rules
enum SubCategoryFITRules {
  SUB_CATEGORY_FIT_RULES_UNSPECIFIED = 0;
  SUB_CATEGORY_FIT_RULES_FIT_RULE_NOT_EXECUTED = 1;
  SUB_CATEGORY_FIT_RULES_INCORRECT_AMOUNT_DEPOSITED = 2;
  SUB_CATEGORY_FIT_RULES_FIT_RULE_INFORMATION = 3;
}

// L3 categories for Jump
enum SubCategoryJump {
  SUB_CATEGORY_JUMP_UNSPECIFIED = 0;
  SUB_CATEGORY_JUMP_PORTFOLIO_MISMATCH = 1;
  SUB_CATEGORY_JUMP_WITHDRAWAL_ISSUES = 2;
}

// L3 categories for Mutual Funds
enum SubCategoryMutualFunds {
  SUB_CATEGORY_MUTUAL_FUNDS_UNSPECIFIED = 0;
  SUB_CATEGORY_MUTUAL_FUNDS_SIP_NOT_DEDUCTED = 1;
  SUB_CATEGORY_MUTUAL_FUNDS_TRANSACTION_SUCCESSFUL_UNITS_NOT_ALLOTTED = 2;
  SUB_CATEGORY_MUTUAL_FUNDS_STUCK_IN_SCREENING = 3;
  SUB_CATEGORY_MUTUAL_FUNDS_WITHDRAWAL_FROM_OTHER_PLATFORM = 4;
  SUB_CATEGORY_MUTUAL_FUNDS_INCORRECT_AMOUNT_CREDITED = 5;
  SUB_CATEGORY_MUTUAL_FUNDS_PAUSE_AUTO_INVESTMENT = 6;
}

// L3 categories for US Stocks
enum SubCategoryUSStocks {
  SUB_CATEGORY_US_STOCKS_UNSPECIFIED = 0;
  SUB_CATEGORY_US_STOCKS_AMOUNT_DEBITED_NOT_CREDITED_TO_WALLET = 1;
}

// L3 categories for Fi Store
enum SubCategoryFiStore {
  SUB_CATEGORY_FI_STORE_UNSPECIFIED = 0;
  SUB_CATEGORY_FI_STORE_DIRECT_TO_HOME = 1;
  SUB_CATEGORY_FI_STORE_PHYSICAL_MERCHANDISE = 2;
}

// L3 categories for Salary Programs
enum SubCategorySalaryPrograms {
  SUB_CATEGORY_SALARY_PROGRAMS_UNSPECIFIED = 0;
  SUB_CATEGORY_SALARY_PROGRAMS_INSTANT_SALARY = 1;
  SUB_CATEGORY_SALARY_PROGRAMS_SALARY_LITE = 2;
  SUB_CATEGORY_SALARY_PROGRAMS_INFORMATION_REGARDING_CHARGES = 3;
}

// L3 categories for Communication
enum SubCategoryCommunication {
  SUB_CATEGORY_COMMUNICATION_UNSPECIFIED = 0;
  SUB_CATEGORY_COMMUNICATION_SPAM = 1;
  SUB_CATEGORY_COMMUNICATION_CALLBACK = 2;
  SUB_CATEGORY_COMMUNICATION_REQUEST_FOR_MORE_INFO = 3;
}

// L3 categories for Loans
enum SubCategoryLoans {
  SUB_CATEGORY_LOANS_UNSPECIFIED = 0;
  SUB_CATEGORY_LOANS_APPLICATION_FAILED = 1;
  SUB_CATEGORY_LOANS_DISBURSAL_PENDING = 2;
  SUB_CATEGORY_LOANS_ISSUE_WITH_LOAN_APPLICATION = 3;
  SUB_CATEGORY_LOANS_LOAN_DISBURSED_BUT_ACCOUNT_NOT_CREATED = 4;
  SUB_CATEGORY_LOANS_CONSENT_WITHDRAWAL_FOR_CIBIL_ENQUIRY = 5;
  SUB_CATEGORY_LOANS_REQUEST_FOR_BUREAU_CORRECTION = 6;
  SUB_CATEGORY_LOANS_BORROWERS_DEMISE = 7;
  SUB_CATEGORY_LOANS_HARASSMENT_COMPLAINT = 8;
  SUB_CATEGORY_LOANS_PAYMENT_LINK_TO_BE_SENT = 9;
  SUB_CATEGORY_LOANS_REQUEST_FOR_SETTLEMENT = 10;
  SUB_CATEGORY_LOANS_REQUESTING_EMI_EXTENSION = 11;
  SUB_CATEGORY_LOANS_REPAYMENT_SCHEDULE = 12;
  SUB_CATEGORY_LOANS_EMI_NOT_DEDUCTED = 13;
  SUB_CATEGORY_LOANS_NACH_RE_REGISTRATION = 14;
  SUB_CATEGORY_LOANS_PAYMENT_STATUS_NOT_UPDATED = 15;
  SUB_CATEGORY_LOANS_LOAN_DETAILS_AND_STATUS = 16;
  SUB_CATEGORY_LOANS_LOAN_PRE_CLOSURE = 17;
  SUB_CATEGORY_LOANS_LOAN_REPAYMENT = 18;
  SUB_CATEGORY_LOANS_PAY_MARGIN_AMOUNT = 19;
  SUB_CATEGORY_LOANS_PLEDGE_MORE_FUNDS = 20;
  SUB_CATEGORY_LOANS_PLEDGED_MUTUAL_FUNDS_SOLD = 21;
  SUB_CATEGORY_LOANS_DELAY_IN_CLOSURE = 22;
  SUB_CATEGORY_LOANS_PAID_BUT_MF_NOT_UNPLEDGED = 23;
  SUB_CATEGORY_LOANS_PRE_CLOSURE = 24;
  SUB_CATEGORY_LOANS_PRE_DISBURSEMENT = 25;
  SUB_CATEGORY_LOANS_SALES = 26;
  SUB_CATEGORY_LOANS_SERVICE = 27;
  SUB_CATEGORY_LOANS_EMI_PAID_BUT_ECS_NACH_RETURN_CHARGED = 28;
  SUB_CATEGORY_LOANS_LATE_PAYMENT_FEES = 29;
  SUB_CATEGORY_LOANS_UNABLE_TO_PAY_VIA_APP = 30;
  SUB_CATEGORY_LOANS_UNABLE_TO_PAY_VIA_COLLECTIONS_LINK = 31;
}

// L3 categories for Assets
enum SubCategoryAssets {
  SUB_CATEGORY_ASSETS_UNSPECIFIED = 0;
  SUB_CATEGORY_ASSETS_ASSETS_VIA_MANUAL_FORMS = 1;
  SUB_CATEGORY_ASSETS_EPFO = 2;
  SUB_CATEGORY_ASSETS_INDIAN_STOCKS = 3;
  SUB_CATEGORY_ASSETS_LOANS = 4;
  SUB_CATEGORY_ASSETS_NPS = 5;
  SUB_CATEGORY_ASSETS_OTHER_BANK_ACCOUNTS = 6;
  SUB_CATEGORY_ASSETS_ABOUT_NEGATIVE_BALANCE = 7;
  SUB_CATEGORY_ASSETS_ABOUT_NETWORTH = 8;
  SUB_CATEGORY_ASSETS_CONNECTED_ACCOUNTS = 9;
  SUB_CATEGORY_ASSETS_UNABLE_TO_ADD_ASSETS_LIABILITIES = 10;
}

// L3 categories for Rewards
enum SubCategoryRewards {
  SUB_CATEGORY_REWARDS_UNSPECIFIED = 0;
  SUB_CATEGORY_REWARDS_CONVERT_TO_CASH = 1;
  SUB_CATEGORY_REWARDS_PLAY_AND_WIN = 2;
  SUB_CATEGORY_REWARDS_POWER_UP = 3;
  SUB_CATEGORY_REWARDS_TRAVEL_MILES = 4;
  SUB_CATEGORY_REWARDS_HOW_TO_GET_REWARDS = 5;
  SUB_CATEGORY_REWARDS_HOW_TO_REFER = 6;
  SUB_CATEGORY_REWARDS_REWARDS_STATEMENT = 7;
  SUB_CATEGORY_REWARDS_AMOUNT_NOT_REFUNDED = 8;
  SUB_CATEGORY_REWARDS_EXPIRED_VOUCHER_RECEIVED = 9;
  SUB_CATEGORY_REWARDS_FI_POINTS_NOT_REFUNDED = 10;
  SUB_CATEGORY_REWARDS_CAMPAIGN_SPECIFIC = 11;
  SUB_CATEGORY_REWARDS_DEBIT_CARD_OFFERS = 12;
  SUB_CATEGORY_REWARDS_REFERRAL = 13;
  SUB_CATEGORY_REWARDS_TIERING_REWARDS = 14;
}

// L3 categories for KYC
enum SubCategoryKYC {
  SUB_CATEGORY_KYC_UNSPECIFIED = 0;
  SUB_CATEGORY_KYC_NON_KYC_RELATED = 1;
  SUB_CATEGORY_KYC_VKYC_RELATED = 2;
}

// L3 categories for Account Security
enum SubCategoryAccountSecurity {
  SUB_CATEGORY_ACCOUNT_SECURITY_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNT_SECURITY_REQUEST_TO_UNFREEZE = 1;
  SUB_CATEGORY_ACCOUNT_SECURITY_FREEZE_RELATED = 2;
  SUB_CATEGORY_ACCOUNT_SECURITY_ADDITIONAL_INFORMATION = 3;
  SUB_CATEGORY_ACCOUNT_SECURITY_NOC_RELATED = 4;
}

// L3 categories for Language Support
enum SubCategoryLanguage {
  SUB_CATEGORY_LANGUAGE_UNSPECIFIED = 0;
  SUB_CATEGORY_LANGUAGE_ASSAMESE = 1;
  SUB_CATEGORY_LANGUAGE_BENGALI = 2;
  SUB_CATEGORY_LANGUAGE_HINDI = 3;
  SUB_CATEGORY_LANGUAGE_KANNADA = 4;
  SUB_CATEGORY_LANGUAGE_MALAYALAM = 5;
  SUB_CATEGORY_LANGUAGE_ORIYA = 6;
  SUB_CATEGORY_LANGUAGE_TAMIL = 7;
  SUB_CATEGORY_LANGUAGE_TELUGU = 8;
}

// L3 categories for Data and Statements
enum SubCategoryDataStatements {
  SUB_CATEGORY_DATA_STATEMENTS_UNSPECIFIED = 0;
  SUB_CATEGORY_DATA_STATEMENTS_REQUEST_DATA_DELETION = 1;
  SUB_CATEGORY_DATA_STATEMENTS_BANK_STATEMENT = 2;
  SUB_CATEGORY_DATA_STATEMENTS_MUTUAL_FUNDS_STATEMENT = 3;
  SUB_CATEGORY_DATA_STATEMENTS_SIGNED_BANK_STATEMENT = 4;
  SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_JUMP = 5;
  SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_MUTUAL_FUNDS = 6;
  SUB_CATEGORY_DATA_STATEMENTS_TAX_STATEMENT_US_STOCKS = 7;
  SUB_CATEGORY_DATA_STATEMENTS_US_STOCKS_STATEMENT = 8;
}

// L3 categories for Mandates
enum SubCategoryMandates {
  SUB_CATEGORY_MANDATES_UNSPECIFIED = 0;
  SUB_CATEGORY_MANDATES_ACTIVE_MANDATES_DETAILS = 1;
  SUB_CATEGORY_MANDATES_CANCEL_SI_NACH_MANDATES = 2;
}

// L3 categories for Profile Updates
enum SubCategoryProfileUpdates {
  SUB_CATEGORY_PROFILE_UPDATES_UNSPECIFIED = 0;
  SUB_CATEGORY_PROFILE_UPDATES_CHANGE_EMPLOYMENT_DETAILS = 1;
  SUB_CATEGORY_PROFILE_UPDATES_CONTACT_DETAILS_UPDATE = 2;
  SUB_CATEGORY_PROFILE_UPDATES_DOB_CHANGE = 3;
  SUB_CATEGORY_PROFILE_UPDATES_NAME_CHANGE_FATHER_MOTHER = 4;
  SUB_CATEGORY_PROFILE_UPDATES_NAME_CHANGE_USER = 5;
}

// L3 categories for Device Issues
enum SubCategoryDeviceIssues {
  SUB_CATEGORY_DEVICE_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_DEVICE_ISSUES_CARDS = 1;
  SUB_CATEGORY_DEVICE_ISSUES_DEVICE_LOST = 2;
  SUB_CATEGORY_DEVICE_ISSUES_PROMOTIONAL_COMMS = 3;
}

// L3 categories for Transaction Types
enum SubCategoryTransactionTypes {
  SUB_CATEGORY_TRANSACTION_TYPES_UNSPECIFIED = 0;
  SUB_CATEGORY_TRANSACTION_TYPES_GOODS_SERVICES_NOT_DELIVERED = 1;
  SUB_CATEGORY_TRANSACTION_TYPES_INCORRECT_AMOUNT = 2;
  SUB_CATEGORY_TRANSACTION_TYPES_NOT_VISIBLE_ON_APP = 3;
  SUB_CATEGORY_TRANSACTION_TYPES_IN_APP_UPI_P2M = 4;
  SUB_CATEGORY_TRANSACTION_TYPES_IN_APP_UPI_P2P = 5;
  SUB_CATEGORY_TRANSACTION_TYPES_OFF_APP_UPI_P2M = 6;
  SUB_CATEGORY_TRANSACTION_TYPES_OFF_APP_UPI_P2P = 7;
  SUB_CATEGORY_TRANSACTION_TYPES_INTRA_BANK = 8;
  SUB_CATEGORY_TRANSACTION_TYPES_NACH_ECS_CHARGES = 9;
  SUB_CATEGORY_TRANSACTION_TYPES_RECURRING_PAYMENT_CANCELLED_BUT_AMOUNT_DEBITED = 10;
  SUB_CATEGORY_TRANSACTION_TYPES_CHEQUE_DEPOSIT = 11;
  SUB_CATEGORY_TRANSACTION_TYPES_INTERNATIONAL_REMITTANCE = 12;
  SUB_CATEGORY_TRANSACTION_TYPES_MERCHANT_REFUND = 13;
  SUB_CATEGORY_TRANSACTION_TYPES_OTHER_DOMESTIC_TRANSACTIONS = 14;
  SUB_CATEGORY_TRANSACTION_TYPES_DEPOSITING_CASH = 15;
  SUB_CATEGORY_TRANSACTION_TYPES_IPO = 16;
  SUB_CATEGORY_TRANSACTION_TYPES_TRANSACTION_RELATED_ENQUIRY = 17;
}

// L3 categories for UPI Issues
enum SubCategoryUPIIssues {
  SUB_CATEGORY_UPI_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_UPI_ISSUES_UNABLE_TO_LINK_FEDERAL_ACCOUNT_TO_OTHER_APPS = 1;
  SUB_CATEGORY_UPI_ISSUES_BANK_TRANSFER = 2;
  SUB_CATEGORY_UPI_ISSUES_INTERNATIONAL_TRANSACTIONS = 3;
  SUB_CATEGORY_UPI_ISSUES_LIMIT_EXCEEDED = 4;
  SUB_CATEGORY_UPI_ISSUES_PIN_TRIES_EXCEEDED = 5;
  SUB_CATEGORY_UPI_ISSUES_UPI_ISSUE = 6;
}

// L3 categories for International Transactions
enum SubCategoryInternationalTransactions {
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_UNSPECIFIED = 0;
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_INTERNATIONAL = 1;
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_STOP_CHEQUE_PAYMENT = 2;
  SUB_CATEGORY_INTERNATIONAL_TRANSACTIONS_FEES_AND_CHARGES = 3;
}

// L3 categories for Certificates
enum SubCategoryCertificates {
  SUB_CATEGORY_CERTIFICATES_UNSPECIFIED = 0;
  SUB_CATEGORY_CERTIFICATES_BALANCE_CERTIFICATE = 1;
  SUB_CATEGORY_CERTIFICATES_INTEREST_CERTIFICATE = 2;
}

// L3 categories for Eligibility Issues
enum SubCategoryEligibility {
  SUB_CATEGORY_ELIGIBILITY_UNSPECIFIED = 0;
  SUB_CATEGORY_ELIGIBILITY_NOT_ELIGIBLE = 1;
  SUB_CATEGORY_ELIGIBILITY_OTP_NOT_RECEIVED = 2;
}

// L3 categories for Profile Changes
enum SubCategoryProfileChanges {
  SUB_CATEGORY_PROFILE_CHANGES_UNSPECIFIED = 0;
  SUB_CATEGORY_PROFILE_CHANGES_ADDRESS_CHANGE = 1;
  SUB_CATEGORY_PROFILE_CHANGES_UNAUTHORISED_TRANSACTION = 2;
}

// L3 categories for Card Usage Issues
enum SubCategoryCardUsage {
  SUB_CATEGORY_CARD_USAGE_UNSPECIFIED = 0;
  SUB_CATEGORY_CARD_USAGE_CARD_NOT_ACCEPTED = 1;
  SUB_CATEGORY_CARD_USAGE_CONTACTLESS_NOT_WORKING = 2;
  SUB_CATEGORY_CARD_USAGE_ATM_DECLINE_FEES = 3;
  SUB_CATEGORY_CARD_USAGE_FUEL_CHARGES = 4;
  SUB_CATEGORY_CARD_USAGE_TCS_DEDUCTIONS = 5;
}

// L3 categories for Balance Issues
enum SubCategoryBalanceIssues {
  SUB_CATEGORY_BALANCE_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_BALANCE_ISSUES_BALANCE_NOT_UPDATED = 1;
  SUB_CATEGORY_BALANCE_ISSUES_DOUBLE_DEBIT = 2;
  SUB_CATEGORY_BALANCE_ISSUES_INCORRECT_AMOUNT_DEBITED = 3;
  SUB_CATEGORY_BALANCE_ISSUES_EXCESS_AMOUNT_PAID = 4;
}

// L3 categories for App Access Issues
enum SubCategoryAppAccess {
  SUB_CATEGORY_APP_ACCESS_UNSPECIFIED = 0;
  SUB_CATEGORY_APP_ACCESS_NO_APP_ACCESS = 1;
  SUB_CATEGORY_APP_ACCESS_UNABLE_TO_INVEST = 2;
  SUB_CATEGORY_APP_ACCESS_UNABLE_TO_WITHDRAW = 3;
  SUB_CATEGORY_APP_ACCESS_UNABLE_TO_ADD_FUNDS = 4;
}

// L3 categories for Payment Issues
enum SubCategoryPaymentIssues {
  SUB_CATEGORY_PAYMENT_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_PAYMENT_ISSUES_SENT_TO_WRONG_USER = 1;
  SUB_CATEGORY_PAYMENT_ISSUES_CASH_DEPOSIT_AT_BRANCH = 2;
}

// L3 categories for Fixed/Smart Deposit Issues
enum SubCategoryDepositIssues {
  SUB_CATEGORY_DEPOSIT_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_CREATE_FD = 1;
  SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_CREATE_SD = 2;
  SUB_CATEGORY_DEPOSIT_ISSUES_UNABLE_TO_REDEEM = 3;
}

// L3 categories for Account Status Issues
enum SubCategoryAccountStatus {
  SUB_CATEGORY_ACCOUNT_STATUS_UNSPECIFIED = 0;
  SUB_CATEGORY_ACCOUNT_STATUS_ACCOUNT_FROZEN_CLOSED = 1;
  SUB_CATEGORY_ACCOUNT_STATUS_EMAIL_ADDRESS = 2;
  SUB_CATEGORY_ACCOUNT_STATUS_PHONE_NUMBER = 3;
  SUB_CATEGORY_ACCOUNT_STATUS_BOUNCE_CHARGE = 4;
}

// L3 categories for Reward Issues
enum SubCategoryRewardIssues {
  SUB_CATEGORY_REWARD_ISSUES_UNSPECIFIED = 0;
  SUB_CATEGORY_REWARD_ISSUES_VOUCHER_NOT_RECEIVED = 1;
  SUB_CATEGORY_REWARD_ISSUES_FOREX_RATE_ISSUE = 2;
}

// L3 categories for Stock Trading Issues
enum SubCategoryStockTrading {
  SUB_CATEGORY_STOCK_TRADING_UNSPECIFIED = 0;
  SUB_CATEGORY_STOCK_TRADING_UNABLE_TO_BUY = 1;
  SUB_CATEGORY_STOCK_TRADING_MONEY_NOT_CREDITED = 2;
  SUB_CATEGORY_STOCK_TRADING_UNABLE_TO_SELL = 3;
}
