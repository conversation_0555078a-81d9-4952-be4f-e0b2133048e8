syntax = "proto3";

import "api/vendorgateway/request_header.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/rpc/status.proto";

package cx.chatbot.nugget;

option go_package = "github.com/epifi/gamma/api/vendorgateway/cx/chatbot/nugget";
option java_package = "com.github.epifi.gamma.api.vendorgateway.cx.chatbot.nugget";

service NuggetChatbotService {
  rpc FetchAccessToken(FetchAccessTokenRequest) returns (FetchAccessTokenResponse);
}

message FetchAccessTokenRequest{
  vendorgateway.RequestHeader header = 1;
  string actor_id = 2;
  string client_request_id = 3;
  api.typesv2.common.Platform platform = 4;
  api.typesv2.common.Name name = 5;
  string email = 6;
  api.typesv2.common.PhoneNumber phone_number = 7;
}

message FetchAccessTokenResponse {
  rpc.Status status = 1;
  string access_token = 2;
}