syntax = "proto3";

package vendorgateway.zenduty;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/struct.proto";


option go_package = "github.com/epifi/gamma/api/vendorgateway/zenduty";
option java_package = "com.github.epifi.gamma.api.vendorgateway.zenduty";


service Zenduty {
  rpc SendAlert (SendAlertRequest) returns (SendAlertResponse) {};
}

// SendAlertRequest defines the request to send an alert to Zenduty
message SendAlertRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  string message = 2;
  string summary = 5;
  AlertType alert_type = 3;
  google.protobuf.Struct payload = 4;
}

message SendAlertResponse {
  rpc.Status status = 1;
}

enum AlertType {
  ALERT_TYPE_UNSPECIFIED = 0;
  ALERT_TYPE_CRITICAL = 1;
  ALERT_TYPE_ACKNOWLEDGED = 2;
  ALERT_TYPE_RESOLVED = 3;
  ALERT_TYPE_ERROR = 4;
  ALERT_TYPE_WARNING = 5;
  ALERT_TYPE_INFO = 6;
}
