syntax = "proto3";

package vendorgateway.creditcard;

import "api/rpc/status.proto";
import "api/typesv2/common/device.proto";
import "api/vendorgateway/request_header.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "google/type/money.proto";
import "google/type/date.proto";
import "api/firefly/v2/enums/enums.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/creditcard";
option java_package = "com.github.epifi.gamma.api.vendorgateway.creditcard";

// VG service to enable credit card related services for Epifi users.
service CreditCard {
  // RPC to generate an authentication token for client to auth with credit card SDK
  rpc GenerateCreditCardSdkAuthToken(GenerateCreditCardSdkAuthTokenRequest) returns (GenerateCreditCardSdkAuthTokenResponse);
  // RPC to update credit card delivery info at credit card vendor.
  rpc UpdateCreditCardDeliveryState(UpdateCreditCardDeliveryStateRequest) returns (UpdateCreditCardDeliveryStateResponse);
}

message GenerateCreditCardSdkAuthTokenRequest {
  // Common request header across all vendor gateway APIs.
  // Denotes the vendor that is supposed to process this request.
  vendorgateway.RequestHeader header = 1;
  // Applicant type (ETB, NTB, PRE-APPROVED)
  api.firefly.v2.enums.CreditCardApplicantType applicant_type = 2 [(validate.rules).enum = {not_in: 0}];

  // User information
  UserInfo user_info = 3 [(validate.rules).message.required = true];

  // Device information
  DeviceInfo device_info = 4 [(validate.rules).message.required = true];

  // PAN information (conditional optional)
  // This information is required only when request is for initial onboarding auth token generation,
  // otherwise, populating this field can be omitted.
  PanInfo pan_info = 5;

  // Consent information (optional)
  ConsentInfo consent_info = 6;

  // Pre-approved information (conditional optional)
  // This information is required only when the applicant type is PRE-APPROVED and request is for initial onboarding auth token generation.
  // Otherwise, populating value for this field can be omitted.
  PreApprovedInfo pre_approved_info = 7;
}

message GenerateCreditCardSdkAuthTokenResponse {
  // Status of the request.
  rpc.Status status = 1;
  CreditCardSdkModuleName module_name = 2;
  string auth_token = 3;
  TokenGenerationAdditionalInfo additional_info = 4;
}

message TokenGenerationAdditionalInfo {
  string workflow_id = 1;
  string user_local_id = 2;
  string external_user_id = 3;
  api.firefly.v2.enums.CardRequestStatus workflow_status = 4;
  api.firefly.v2.enums.CardRequestStage workflow_state = 5;
  string workflow_message = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}


enum CreditCardSdkModuleName {
  CREDIT_CARD_SDK_MODULE_NAME_UNSPECIFIED = 0;
  CREDIT_CARD_SDK_MODULE_NAME_ONBOARDING = 1;
  CREDIT_CARD_SDK_MODULE_NAME_CMS = 2;
}

message DeviceInfo {
  api.typesv2.common.Device device = 1;
}

message UserInfo {
  // User's email address.
  string email_address = 1 [(validate.rules).string.min_len = 1];
  // User's phone number.
  api.typesv2.common.PhoneNumber phone_number = 2 [(validate.rules).message.required = true];
  // Optional:
  string phone_type = 3;
  // User's internal user ID at vendor's end.
  string internal_user_id = 4 [(validate.rules).string = {min_len: 1, max_len: 100}];
}

message PanInfo {
  // PAN number.
  string pan_number = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
  // Full name of user associated with the PAN.
  api.typesv2.common.Name user_name = 2 [(validate.rules).message.required = true];
}

message PreApprovedInfo {
  // Pre-approved limit.
  google.type.Money pre_approved_limit = 1 [(validate.rules).message.required = true];
  // Pre-approved expiration date.
  google.type.Date pre_approved_exp = 2 [(validate.rules).message.required = true];
}

message ConsentInfo {
  repeated Consent consents = 1;
}

message Consent {
  // Indicates if the consent is recorded.
  bool is_consent_recorded = 1;
  // Type of the consent.
  string consent_type = 2;
  // Category of the consent.
  string consent_category = 3;
}

message UpdateCreditCardDeliveryStateRequest {
  // Common request header across all vendor gateway APIs.
// Denotes the vendor that is supposed to process this request.
  vendorgateway.RequestHeader header = 1;
  // carrier partner of the tracking request (DELHIVERY, BLUEDART etc)
  string carrier = 2 [(validate.rules).string = {min_len: 1, max_len: 100}];
  // delivery state of the shipment
  DeliveryState delivery_state = 3 [(validate.rules).enum = {not_in: 0}];
  string tracking_url = 4;
  string user_id = 5 [(validate.rules).string = {min_len: 1, max_len: 100}];
}

message UpdateCreditCardDeliveryStateResponse {
  // Status of the request.
  rpc.Status status = 1;
}

enum DeliveryState {
  DELIVERY_STATE_UNSPECIFIED = 0;
  DELIVERED = 1;
  RETURNED_TO_ORIGIN = 2;
}
