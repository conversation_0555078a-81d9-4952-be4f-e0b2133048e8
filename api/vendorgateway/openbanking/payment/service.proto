// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendorgateway.openbanking.payment;

import "api/accounts/account_type.proto";
import "api/order/payment/payment_protocol.proto";
import "api/rpc/status.proto";
import "api/typesv2/account/enums.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/openbanking/header/auth.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vendor_status.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/payment";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.payment";

// The Payment Service enables money transfer services from an account that belongs to
// partner bank and any other savings/current bank account that exists in India.
//
// It facilitates transfer process using below technology solutions also known as
// payment protocols in epiFi terminology:
//   * Intra bank transfer
//   * NEFT
//   * IMPS
//   * RTGS
//
// Note: This service doesn't cater to UPI transactions
service Payment {
  // RPC facilitates payment process using below technology solutions also known as
  // payment protocols in epiFi terminology:
  //   * Intra bank transfer
  //   * NEFT
  //   * IMPS
  //   * RTGS
  //
  // This is an asynchronous API such that, payment may not be completed immediately.
  // This API, hence, will respond with an Acknowledgement message immediately.
  // Only exception to asynchronous API is when beneficiary account type in pay request is of
  // deposit account type(Fixed Deposit/Recurring Deposit/Smart Deposit), then this API will synchronous and
  // payment will be completed immediately and unique transaction identifier will be provided in response.
  //
  // Any updates on the transaction at the partner bank including the transaction completion are sent
  // using call back mechanism that is not in scope of this service.
  //
  // The final transaction message will have details of the payment status, remitter's and
  // beneficiary’s account numbers, amount transferred etc. Based on the transaction status,
  // the client application can take appropriate action.
  //
  // Both the response messages will have the same ReferenceID, with which the client had invoked this API.
  //
  // Caller can enquire for the transaction status using the StatusEnquiry RPC method
  rpc Pay (PayRequest) returns (PayResponse);

  // RPC facilitates enquiry of transaction status - identified by the Request ID
  // RPC responds with the status, and status reason of the transaction
  //
  // It is advised against StatusEnquiry aggressively to keep minimal load on partner banks
  // Client should also handle scenarios such as RequestNotFound at partner bank whereas the actual status may be that
  // is not processed by the bank. Client should double confirm the status before dropping the transaction
  rpc GetStatus (GetStatusRequest) returns (GetStatusResponse);

  // RPC to check status of add fund transaction.
  // In add fund, amount from epifi pool account will be credited to user account by vendor.
  // This status check API returns the status of add fund transaction.
  rpc GetAddFundStatus (GetAddFundStatusRequest) returns (GetAddFundStatusResponse);

  // RPC to fetch Remitter Account and Remitter Name for NEFT/RTGS transactions based on CBS transaction-id and date.
  // This is a synchronous call i.e. client can get response against RPC in this call itself.
  rpc GetRemitterDetails (GetRemitterDetailsRequest) returns (GetRemitterDetailsResponse);

  // GetCsisStatus rpc to check the CSIS mode of federal bank. Based on response of CSIS, some neobanking services will not be available
  // For example during CSIS time min kyc user can't create deposit transaction etc.
  rpc GetCsisStatus (GetCsisStatusRequest) returns (GetCsisStatusResponse);

  // RPC to fetch Remitter Account and Remitter Name for IMPS/UPI/INTRA transactions based on CBS transaction-id/UTR and date.
  // This is a synchronous call i.e. client can get response against RPC in this call itself.
  // Sync delay of at max 60 min is expected from vendor side i.e. it will return record not found for a given transaction and remitter details after the delay time for the same transaction
  // It will provide remitter detail for T+5 days only for Federal vendor.
  rpc GetRemitterDetailsV1 (GetRemitterDetailsV1Request) returns (GetRemitterDetailsV1Response);

  // RPC to fetch Beneficiary Account Name for IMPS/RTGS/NEFT transactions based on beneficiary account number and IFSC code.
  rpc GetBeneficiaryNameDetails(GetBeneficiaryNameDetailsRequest) returns (GetBeneficiaryNameDetailsResponse);
}

// Account defines a set of account attributes using which a payment can be made
message Account {
  // Account number defined by the partner bank
  string account_number = 1 [(validate.rules).string = {min_len: 6, max_len: 32}];

  // IFSC code corresponding to the bank's branch at which account was created
  string ifsc_code = 2 [(validate.rules).string = {min_len: 4, max_len: 32}];

  // Name of the primary account holder
  string account_holder_name = 3;

  // Account type
  accounts.Type account_type = 4 [(validate.rules).enum = {not_in: [0]}];

  // Phone number of the account owner where notifications are to be received
  api.typesv2.common.PhoneNumber phone_number = 5;

  // Email ID of the account owner where notifications are to be received
  string email = 6;

  // Address of the account owner
  // TBD(pruthvi): Address field is set on RTGS API of federal. Confirm if this is necessary
  string address = 7;

  enum NotificationFlag {
    NOTIFICATION_FLAG_UNSPECIFIED = 0;
    // Email and SMS notifications will be sent to customer's mobile and email-id
    BOTH = 1;
    // Only email notification will be sent to customer's email-id
    EMAIL = 2;
    // Only SMS notification will be sent to customer's mobile
    SMS = 3;
    // No notification will be sent
    NONE = 4;
  }

  // Notification flag for sending transaction notification to the account owner
  NotificationFlag notification_flag = 8;

  // Account Product Offering associated with the AccountType.
  // This has to be used in combination with AccountType to derive the correct AccType to send to vendor.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE together will derive a unique AccType to send to vendor.
  api.typesv2.account.AccountProductOffering apo = 17;
}

message PayRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  RequestHeader header = 1;

  // A set of authentication attributes that are common across Open banking requests
  header.Auth auth = 2;

  // Caller function should pass unique value to each call, to identify the request
  // There won't be any explicit uniqueness checks that are performed by the RPC implementation
  // Uniqueness is expected to hold across all the requests to a partner bank
  string request_id = 3;

  // Timestamp of the event when transaction request is received at epiFi's transaction service
  google.protobuf.Timestamp trans_time = 4;

  // Payment protocol through which the transaction is to be processed
  // If the protocol is not compatible with the remitter or beneficiary's payment instrument details,
  // the request will be rejected without further processing
  order.payment.PaymentProtocol protocol = 5;

  // Account details of the Remitter
  // Remitter is the owner of the account that sends the payment
  Account remitter = 6;

  // Account details of the Beneficiary
  // Beneficiary is the owner of the account that receives the payment
  Account beneficiary = 7;

  // Amount that the remitter wishes to pay to the beneficiary
  google.type.Money amount = 8;

  // Description of the Transaction entered by the remitter
  string user_remarks = 9;
}

message PayResponse {
  enum Status {
    // payment request has been accepted successfully
    // the caller will be updated about the transaction status
    // via callback from partner bank.
    // Further the caller can do enquiry call to check the final status
    // of the payment
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    // Partner bank will send update once it is aware of the state
    //
    // The state can also be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller can also enquire the transaction status to resolve the ambiguity.
    UNKNOWN = 2;

    // payment initiation has failed
    // this is usually returned when there is a validation failure returned in the ack
    FAILED = 100;

    // payment succeeded at partner bank's end
    // this status code is usually returned for deposit transfer
    // where payment happen synchronously.
    // The caller need not do enquiry call on getting the status
    PAYMENT_SUCCESS = 101;

    // payment failed at partner bank's end due to some business failure
    // examples of business failure could be insufficient funds, account freeze, etc.
    // some particular status codes returned from partner bank related to business errors will map to this
    // failure in the Pay RPC
    // Alerts for business failures will be ignored.
    BUSINESS_FAILURE = 102;

    // payment failed due to some technical failure at the partner bank.
    // Alerts will be triggered for technical failures.
    TECHNICAL_FAILURE = 103;

    // invalid cred block error
    INVALID_CRED_BLOCK = 104;

    // device key not registered with vendor
    DEVICE_KEY_NOT_REGISTER = 105;
  }
  // Denotes the status of payment request
  rpc.Status status = 1;

  // response code as sent by the vendor bank
  string raw_response_code = 2;

  // description of the response code as sent by the vendor bank
  string raw_response_description = 3;

  // action need to perform based on response
  string raw_response_action = 4;

  // Unique transaction reference to identify transaction if completed.
  // This field will be present only in case where payment transaction can be completed in sync with call.
  // For ex.: Fund transfer to deposit account from savings account can be done in sync for federal.
  string utr = 5;
  // epifi status code corresponding to the raw status code sent by the vendor
  string status_code = 6;
  // description of the status code for the payer
  string status_description_payer = 7;
  // description of the status code for the payee
  string status_description_payee = 8;
  // vendor_status encapsulates the vendor's status code and message returned from vendor api.
  vendorgateway.VendorStatus vendor_status = 9;
}

message GetStatusRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  RequestHeader header = 1;

  // A set of authentication attributes that are common across Open banking requests
  header.Auth auth = 2;

  // Caller function should pass unique value to each call, to identify the request
  // There won't be any explicit uniqueness checks that are performed by the RPC
  // Uniqueness is expected to hold across all the requests for each vendor
  string request_id = 3;

  // Payment protocol through which the original transaction was initiated
  order.payment.PaymentProtocol protocol = 4;

  // Original ReferenceId i.e., ReferenceId of the transaction about which the status is sought.
  // API will return the status of the transaction identified by the this reference Id.
  string original_request_id = 5;

  // Remitter's phone number
  string phone_number = 6;
}

message GetStatusResponse {
  enum Status {
    // payment was successful
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    // Partner bank will send update once it is aware of the state.
    //
    // The state can also be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller should re-enquire after sometime, to resolve the ambiguity
    UNKNOWN = 2;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // payment is still being processed at the partner bank's end
    // the caller should retry the enquiry after sometime
    IN_PROGRESS = 51;

    // payment processing has failed at vendor's end
    // this is usually returned as per the vendor status code mapping returned
    FAILED = 100 [deprecated = true];

    // Transaction is failed due to some internal failure and can be retried. One of the reason might be if the
    // bank's server is down.
    TRANSIENT_FAILURE = 101 [deprecated = true];

    // payment failed due to business failure
    // which occur mostly due to wrong information or inputs provided by the user.
    // for eg. wrong MPIN entered by user
    BUSINESS_FAILURE = 102;

    // payment failed due to technical failure
    // transient failures like system outages will also be
    // marked as Technical Failure
    // for eg. validation error
    TECHNICAL_FAILURE = 103;

    // device key not registered with vendor
    DEVICE_KEY_NOT_REGISTERED = 104;

    // Invalid cred block error
    INVALID_CRED_BLOCK = 105;

    // Device Temporarily Deactivated by bank Code: OBE0170
    DEVICE_TEMPORARILY_DEACTIVATED = 106;

    // transaction is in Deemed state
    // we get ResponseReason as 'DEEMED APPROVED'
    // for txns which are in deemed state
    DEEMED = 107;
  }
  // Denotes the status of transaction
  rpc.Status status = 1;

  // unique identifier for a transaction
  // it may be empty and non empty depending on the status of transaction
  string utr = 2;

  // response code as sent by the vendor bank
  string raw_response_code = 3;

  // description of the response code as sent by the vendor bank
  string raw_response_description = 4;

  // epifi status code corresponding to the raw status code
  string status_code = 5;

  // description of the epifi status code for payer
  string status_description_payer = 6;

  // description of the status code for payee
  string status_description_payee = 7;

  // timestamp of transaction execution timestamp
  google.protobuf.Timestamp transaction_timestamp = 8;
}

message GetAddFundStatusRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  RequestHeader header = 1;

  // A set of authentication attributes that are common across Open banking requests
  header.Auth auth = 2;

  // Original ReferenceId i.e., ReferenceId of the transaction about which the status is sought.
  // API will return the status of the transaction identified by the this reference Id.
  string original_request_id = 3;

  // Remitter's phone number
  api.typesv2.common.PhoneNumber phone_number = 4;
}

message GetAddFundStatusResponse {
  enum Status {
    // payment was successful
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    // Partner bank will send update once it is aware of the state.
    //
    // The state can also be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller should re-enquire after sometime, to resolve the ambiguity
    UNKNOWN = 2;

    // Transaction record not found
    // this means that the second leg of the transaction
    // has not been initiated yet
    //
    // Record not found should not be treated as terminal error immediately
    // There can be scenarios where the transaction are delayed in the queues at vendor
    // due to which "Record not found" can be expected even though transaction is in queue
    RECORD_NOT_FOUND = 5;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // payment is still being processed at the partner bank's end
    // the caller should retry the enquiry after sometime
    IN_PROGRESS = 51;

    // payment processing has failed at vendor's end
    // this is usually returned as per the vendor status code mapping returned
    FAILED = 100;

    // payment processing has failed because user's account is frozen
    ACCOUNT_FROZEN = 101;
  }
  // Denotes the status of transaction
  rpc.Status status = 1;

  // unique identifier for a transaction
  // it may be empty and non empty depending on the status of transaction
  string utr = 2;

  // response code as sent by the vendor bank
  string raw_response_code = 3;

  // description of the response code as sent by the vendor bank
  string raw_response_description = 4;

  // epifi status code corresponding to the raw status code
  string status_code = 5;

  // timestamp of transaction execution timestamp
  google.protobuf.Timestamp transaction_timestamp = 6;

  // description of the epifi status code for payer
  string status_description_payer = 7;

  // description of the status code for payee
  string status_description_payee = 8;

  // rrn for the first leg of the transactions
  string upi_cust_ref_id = 9;

  // reversal_transaction_id: unique identifier for the refund txn. E.g. Reversal of first
  // leg due to 2nd leg failure
  // it may be empty and non empty depending on the status of transaction and api response
  string reversal_transaction_id = 10;

  // Customer reference ID of the reversal transaction i.e. RRN.
  // e.g. Reversal of first leg due to 2nd leg failure
  string reversal_cust_ref_id = 11;
}

message GetRemitterDetailsRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // cbs id of the transction. CBS-ID is generated from vendor for every transaction. ( on-app and off app)
  // using cbs_tran_id and transaction date , vendor can identify unique transaction.
  string cbs_tran_id = 2;

  // date on which transaction happens
  google.protobuf.Timestamp txn_datetime = 3;
}

message GetRemitterDetailsResponse {
  enum Status {
    // Request has been processed successfully
    OK = 0;

    // remitter details not found with given parameters.
    RECORD_NOT_FOUND = 2;

    // Invalid argument passed in the request
    // This include duplicate request Id, Invalid sender details, invalid date,
    // missing required field, maximum length exceeded or special character found in parameter.
    INVALID_ARGUMENT = 3;
  }

  rpc.Status status = 1;

  // bank account number of remitter
  string remitter_account_no = 2;

  // bank ifsc number of remitter
  string remitter_ifsc_code = 3;

  // name of the remitter
  string remitter_name = 4;
}


message GetCsisStatusRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
}

message GetCsisStatusResponse {
  enum Status {
    // Normal banking duration. All banking services are available
    OK = 0;

    // Bank Switched to CSIS mode. Service need to disable CSIS affected services.
    CSIS_SWITCH_ON = 150;

    // Neo Banking services will start switching back to the Normal working status from CSIS mode.
    // Poll to get OK response to enable csis effected neobanking services.
    CSIS_SWITCHING_TO_NORMAL = 151;
  }

  rpc.Status status = 1;
}

message GetRemitterDetailsV1Request {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // cbs id of the transction. CBS-ID is generated from vendor for every transaction. ( on-app and off app)
  // using cbs_tran_id and transaction date , vendor can identify unique transaction.
  //
  // Required in case of INTRA bank transfer. It will be blank in case of UPI/IMPS
  string cbs_tran_id = 2;

  // date on which transaction happens
  google.protobuf.Timestamp txn_datetime = 3;

  // only IMPS/INTRA/UPI is allowed input for latest GetRemitterDetailsV1
  order.payment.PaymentProtocol payment_protocol = 4;

  // Required in case of UPI/IMPS. It will be blank in case of INTRA bank transfer.
  string utr = 5;
}

message GetRemitterDetailsV1Response {
  enum Status {
    // Request has been processed successfully
    OK = 0;

    // remitter details not found with given parameters.
    // There is possibility of sync delay at vendor end. It is expected to receive Not found for valid cbs-id.
    // In this case client need to call again after some time. Maximum delay is expected to be 1 hour.
    RECORD_NOT_FOUND = 2;

    // Invalid argument passed in the request
    // This include duplicate request Id, Invalid sender details, invalid date,
    // missing required field, maximum length exceeded or special character found in parameter.
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;

  oneof remitter_info {
    RemitterAccountInfo remitter_account_info = 2;
    RemitterUpiInfo remitter_upi_info = 3;
  }
}

// Remitter details in case of bank account transfer(for incoming credits to fi user for protocol INTRA/IMPS)
message RemitterAccountInfo {
  // bank account number of remitter
  string remitter_account_no = 1;

  // bank ifsc number of remitter
  string remitter_ifsc_code = 2;

  // name of the remitter
  string remitter_name = 3;
}

// Remitter details in case of UPI incoming credit to fi user.
message RemitterUpiInfo {
  string payer_vpa = 1;
  string payee_vpa = 2;
}

message GetBeneficiaryNameDetailsRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // InitMode denotes the type of payment initiation mode for the transaction
  InitMode initiation_mode = 2 [(validate.rules).enum = {not_in: [0]}];

  // remitter_type denotes the user type of the remitter for the transaction
  RemitterType remitter_type = 3 [(validate.rules).enum = {not_in: [0]}];

  // remitter_name denotes the name of the remitter for the transaction
  string remitter_name = 4 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 99];

  // only IMPS/NEFT/RTGS is allowed input for this API
  order.payment.PaymentProtocol payment_protocol = 5 [(validate.rules).enum = {in: [2,3,4]}];

  // account number of the beneficiary corresponding to which the name is to be fetched
  string beneficiary_account_num = 6 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 35];

  // ifsc code of the beneficiary corresponding to which the name is to be fetched
  string beneficiary_ifsc = 7 [(validate.rules).string.min_len = 1, (validate.rules).string.max_len = 11];

  // InitMode denotes the type of payment initiation mode for the transaction
  enum InitMode {
    INIT_MODE_UNSPECIFIED = 0;
    INIT_MODE_DOMESTIC = 1;
    INIT_MODE_FIR = 2;
  }

  // RemitterType denotes the type of remitter for the transaction. In the context of epifi, given that we only onboard
  // individual users, it is always expected to be individual.
  enum RemitterType {
    REMITTER_TYPE_UNSPECIFIED = 0;
    REMITTER_TYPE_CORPORATE = 1;
    REMITTER_TYPE_INDIVIDUAL = 2;
  }
}

message GetBeneficiaryNameDetailsResponse {
  rpc.Status status = 1;
  string beneficiary_name = 2;
  string response_code = 3;
  string response_reason = 4;
  string reference_id = 5;
}
