// protolint:disable MAX_LINE_LENGTH

// Vendor gateway APIs for customer management with a partner bank. This includes operations
// like create, update, UN name check, and other operations.

syntax = "proto3";

package vendorgateway.openbanking.customer;

import "api/employment/employment_data.proto";
import "api/kyc/kyc.proto";
import "api/rpc/status.proto";
import "api/typesv2/category.proto";
import "api/typesv2/pep.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/community.proto";
import "api/typesv2/date.proto";
import "api/typesv2/designation.proto";
import "api/typesv2/document_details.proto";
import "api/typesv2/disability_type.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/marital_status.proto";
import "api/typesv2/qualification.proto";
import "api/typesv2/salary.proto";
import "api/vendorgateway/openbanking/customer/dedupe/flow.proto";
import "api/vendorgateway/openbanking/header/auth.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vendor_status.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/postal_address.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/customer";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.customer";


// Customer service provides the API's to create, update and perform other operations
// on a customer at the bank's end.
service Customer {
  // Initiates customer creation at the partner bank's end. Returns either a success
  // or failure code. This API is async and the actual response would have to be
  // fetched using the check status call.
  rpc CreateCustomer (CreateCustomerRequest) returns (CreateCustomerResponse);

  // API to check the status of customer creation where the customer's CIF
  // (Customer Information File) number at the bank's end is provided.
  rpc CheckCustomerStatus (CheckCustomerStatusRequest) returns (CheckCustomerStatusResponse);

  // Initiates customer creation for non resident (NR) user at the partner bank's end. Returns either a success
  // or failure code.
  rpc CreateCustomerForNonResident (CreateCustomerForNonResidentRequest) returns (CreateCustomerForNonResidentResponse);

  rpc CheckCustomerStatusForNonResident (CheckCustomerStatusForNonResidentRequest) returns (CheckCustomerStatusForNonResidentResponse);

  // Api to check if the customer with personal details is already registered with bank outside of epiFi
  // Api returns the customer id if the customer already has a bank account with banks
  rpc DedupeCheck (DedupeCheckRequest) returns (DedupeCheckResponse);

  // Api to fetch customer details
  rpc FetchCustomerDetails (FetchCustomerDetailsRequest) returns (FetchCustomerDetailsResponse);

  // Api to check if the customer is eligible to open an account with the Bank
  // the api return the application id and a url to perform eKYC if the customer is eligible
  rpc CheckProductEligibility (CheckProductEligibilityRequest) returns (CheckProductEligibilityResponse);

  // Api to check vkyc status on federal end.
  // it will return vkyc status as per federal bank and we wil sync as per response
  rpc EnquireVKYCStatus (EnquireVKYCStatusRequest) returns (EnquireVKYCStatusResponse);

  // UpgradeKYCLevel is used to perform KYC upgrade of user post onboarding.
  // Once this returns Success, we can use DedupeCheck to poll on the latest status of the upgrade.
  rpc UpgradeKYCLevel (UpgradeKYCLevelRequest) returns (UpgradeKYCLevelResponse);
  //create loan customer for federal a2l flow
  rpc CreateLoanCustomer (CreateLoanCustomerRequest) returns (CreateLoanCustomerResponse);
  // check the status if the loan customer Id is created by federal i.e CreateLoanCustomer api was successfully
  rpc LoanCustomerCreationStatus (LoanCustomerCreationStatusRequest) returns (LoanCustomerCreationStatusResponse);
}

// Request to create a customer with the bank.
message CreateCustomerRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Unique identifier for this request to be generated by epiFi.
  // In case of status check or retries, this request ID is to be
  // sent.
  string request_id = 2;

  // The full legal name of the customer.
  api.typesv2.common.Name name = 3;

  // Date of birth of the customer.
  google.type.Date date_of_birth = 4;

  // Permanent address of the customer.
  google.type.PostalAddress permanent_address = 5;

  // Current address of the customer.
  google.type.PostalAddress current_address = 6;

  api.typesv2.Gender gender = 7;

  api.typesv2.common.PhoneNumber phone_number = 8;

  // PAN number of the customer.
  string pan_number = 9;

  // Correspondence email of the customer.
  string email = 10;
  // father name of the customer
  string father_name = 11;
  // mother name of customer
  string mother_name = 12;
  // details of identity proof
  ProofDetails identity_proof = 13;
  // details of address proof
  ProofDetails address_proof = 14;
  // device details of the customer
  header.Auth device_details = 15;
  // UID reference key received in eKYC OTP verification response
  string uid_no = 16;
  // Sign ID in case of CKYC download data
  string sign_image = 17;
  // Employment type of user
  employment.EmploymentType type = 18;
  // Annual income of user
  float annual_income = 19;

  // flow via which kyc level was updated used for deciding sender code currently
  CustomerCreationFlow customer_creation_flow = 20;
  // Occupation of user
  employment.OccupationType occupation_type = 21;

  // Sol ID (Branch ID) for the customer creation
  string sol_id = 22;
  api.typesv2.DisabilityType disability_type = 23;
  api.typesv2.Category category = 24;
  api.typesv2.PepCategory pep_category = 25;
}

message ProofDetails {
  kyc.IdProofType type = 1;
  string id_number = 2;
  google.type.Date id_issue_date = 3;
  google.type.Date id_expiry_date = 4;
}

// Response for the CreateCustomer method. Returns with either a success or
// failure. Actual response is to be obtained with a status check API.
message CreateCustomerResponse {
  enum Status {
    OK = 0;
    INVALID_SENDER_DETAILS = 100;
    SENDER_NOT_ENABLED = 101;
    DUPLICATE_REQUESTID = 102;
    NO_REQUESTID = 103;
    EXCEPTION = 104;
    INVALID_DEVICE = 105;
    INVALID_TRAN = 106;
    FIELD_MISSING = 107;
    INVALID_INPUT = 108;
    CUSTOMER_CREATION_INPROGRESS = 109;
    DUPLICATE_CUSTOMER_ID = 110;
  }
  rpc.Status status = 1;
  vendorgateway.VendorStatus vendor_status = 2;
}

// Request to create a customer for non resident user with the bank.
message CreateCustomerForNonResidentRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Unique identifier for this request to be generated by epiFi.
  // In case of status check or retries, this request ID is to be
  // sent.
  string request_id = 2;

  // The full legal name of the customer.
  api.typesv2.common.Name name = 3;

  // Date of birth of the customer.
  api.typesv2.Date date_of_birth = 4;

  // Permanent address of the customer.
  google.type.PostalAddress permanent_address = 5;

  // Current address of the customer.
  google.type.PostalAddress current_address = 6;

  api.typesv2.Gender gender = 7;

  api.typesv2.common.PhoneNumber phone_number = 8;

  // PAN number of the customer.
  string pan_number = 9;

  // Correspondence email of the customer.
  string email = 10;
  // father name of the customer
  string father_name = 11;
  // mother name of customer
  string mother_name = 12;
  // Employment type of user
  employment.EmploymentType employment_type = 13;

  // flow via which kyc level was updated used for deciding sender code currently
  CustomerCreationFlow customer_creation_flow = 15;
  // Occupation of user
  employment.OccupationType occupation_type = 16;

  // Sol ID (Branch ID) for the customer creation
  string sol_id = 17;
  api.typesv2.PassportData passport_data = 18;
  api.typesv2.SalaryRange annual_income_range = 19;
  // user's signature image in base64 (optional field)
  string sign_image = 20;
  api.typesv2.MaritalStatus marital_status = 21;
  // PAN is not required if user opted for form 60
  api.typesv2.common.BooleanEnum form_60_opted_flag = 22;
}

// Response for the CreateCustomerForNonResident method. Returns with either a success or
// failure. Actual response is to be obtained with a status check API.
message CreateCustomerForNonResidentResponse {
  enum Status {
    OK = 0;
    INVALID_SENDER_DETAILS = 100;
    SENDER_NOT_ENABLED = 101;
    DUPLICATE_REQUESTID = 102;
    NO_REQUESTID = 103;
    EXCEPTION = 104;
    INVALID_DEVICE = 105;
    INVALID_TRAN = 106;
    FIELD_MISSING = 107;
    INVALID_INPUT = 108;
    CUSTOMER_CREATION_INPROGRESS = 109;
    DUPLICATE_CUSTOMER_ID = 110;
  }
  rpc.Status status = 1;
  vendorgateway.VendorStatus vendor_status = 2;
}

// Request message for customer status enquiry.
message CheckCustomerStatusRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // The same request id sent in the customer creation API.
  string original_request_id = 2;
  // device auth details - device id, device token, user profile id
  header.Auth device_details = 3;
  // mobile number
  string mobile_number = 4;
}

// Response message for customer status enquiry.
message CheckCustomerStatusResponse {
  string bank_customer_id = 1;

  enum Status {
    OK = 0;
    SENDER_NOT_ENABLED = 100;
    INVALID_INPUT = 101;
    SERVER_ERROR = 102;
    FIELD_MISSING = 103;
    SOCKET_EXCEPTION = 104;
    EXCEPTION = 105;
    // If the initial customer creation request did not reach vendor we get details not found.
    // We can retry customer creation with the same requestId
    DETAILS_NOT_FOUND = 106;

    // For many customer creation enquiry requests we are getting response like
    // "timeout occurred whilst performing", "a remote host did not respond within the timeout",
    // but the customer creation is still succeeding for such requests, hence handling them
    // to reduce noise in alerts
    VENDOR_INTERMITTENT_FLAKINESS = 107;
    KYC_FAILED = 108;
    INVALID_DEVICE_TOKEN = 109;
    SERVICE_NOT_AVAILABLE_FOR_SENDER_CODE = 110;
    // For some customer creation enquiry request, we get FAILURE in ResponseAction and on every subsequent polling,
    // we get the same response. This status will be used to track that and mark them as terminally failed in consumer.
    RESPONSE_ACTION_FAILURE = 111;
  }
  rpc.Status status = 2;

  // time at which customer creation was successful at vendor's end
  google.protobuf.Timestamp created_at = 8;
  vendorgateway.VendorStatus vendor_status = 9;
}

message CheckCustomerStatusForNonResidentRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // The same request id sent in the customer creation API.
  string original_request_id = 2;
}

message CheckCustomerStatusForNonResidentResponse {
  enum Status {
    OK = 0;
    SENDER_NOT_ENABLED = 100;
    INVALID_INPUT = 101;
    SERVER_ERROR = 102;
    FIELD_MISSING = 103;
    SOCKET_EXCEPTION = 104;
    EXCEPTION = 105;
    // If the initial customer creation request did not reach vendor we get details not found.
    // We can retry customer creation with the same requestId
    DETAILS_NOT_FOUND = 106;

    // For many customer creation enquiry requests we are getting response like
    // "timeout occurred whilst performing", "a remote host did not respond within the timeout",
    // but the customer creation is still succeeding for such requests, hence handling them
    // to reduce noise in alerts
    VENDOR_INTERMITTENT_FLAKINESS = 107;
    KYC_FAILED = 108;
    INVALID_DEVICE_TOKEN = 109;
    SERVICE_NOT_AVAILABLE_FOR_SENDER_CODE = 110;
    // For some customer creation enquiry request, we get FAILURE in ResponseAction and on every subsequent polling,
    // we get the same response. This status will be used to track that and mark them as terminally failed in consumer.
    RESPONSE_ACTION_FAILURE = 111;
  }
  rpc.Status status = 1;

  string bank_customer_id = 2;
  vendorgateway.VendorStatus vendor_status = 3;
}

message DedupeCheckRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // PAN no of the user
  string pan_number = 2;

  // Aadhar number of the user
  string aadhaar_number = 3;

  // Passport number of the user
  string passport_number = 4;

  // Driving license of the user
  string driving_license = 5;

  // Voter id of the user
  string voter_id = 6;

  // mobile number of the user
  string mobile_num = 7 [deprecated = true];
  api.typesv2.common.PhoneNumber phone_number = 14;

  // user id
  string user_id = 8;

  // denotes UidReferenceKey(rrn)
  string uid_reference_key = 10;

  // date of birth of the user
  google.type.Date date_of_birth = 11;

  // id to uniquely identify each request.
  string request_id = 12;

  string email_id = 13;

  // It used to decide userId in dedupe request ('EPIFI' or 'EPIFINR')
  vendorgateway.openbanking.customer.dedupe.Flow flow = 15;
}

enum DedupeStatus {
  DEDUPE_STATE_UNSPECIFIED = 0;
  // The given customer doesnt have a prior account with vendor
  CUSTOMER_DOEST_NOT_EXISTS = 1;
  // Dedupe call has failed on vendor
  FAILED = 2;
  // Customer has a prior account with vendor
  CUSTOMER_EXISTS = 3;
  // Customer is duplicate customer, but the dob and/or phone number details doesnt match with the federal account
  CUSTOMER_EXISTS_DETAILS_MISMATCH = 4 [deprecated = true];
  // Customer exists but is NRI
  CUSTOMER_EXISTS_NRI = 5;
  // Customer exists but is Minor
  CUSTOMER_EXISTS_MINOR = 6;
  // Customer exists but kyc but restricted from opening some accounts
  CUSTOMER_EXISTS_PARTIAL_KYC = 7;
  // Multiple customer exists in federal
  CUSTOMER_EXISTS_MULTIPLE_CUSTOMER_ID = 8;
  // Phone number mismatch - Customer is dedupe customer, but the phone number doesnt match with the federal account
  CUSTOMER_EXISTS_PHONE_MISMATCH = 9;
  // Dob mismatch - Customer is dedupe customer, but the dob doesnt match with the federal account
  CUSTOMER_EXISTS_DOB_MISMATCH = 10;
  // The Phone number is already linked to multiple accounts
  CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS = 11;
  // The Phone number is linked to an existing account
  CUSTOMER_EXISTS_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT = 12;
  // Customer has an existing account but the partner bank didn't return a valid customer id
  // to be mapped to the user.
  CUSTOMER_EXISTS_MISSING_CUSTOMER_ID = 13;
  // Customer has an existing account with partial profile details (kyc_profile_flag = N)
  // User already has a savings account where some profile details are not updated.
  // User will have to update profile details with Federal before they can continue.
  CUSTOMER_EXISTS_PARTIAL_PROFILE = 14;
  // Customer has been blocked by the bank
  BLOCKED = 15;
  // Customer blocked due to days past dues as they had pending dues availing credit services from bank.
  BLOCKED_AS_DAYS_PAST_DUES = 16;
  // Email is not linked to this customer.
  CUSTOMER_EXISTS_EMAIL_MISMATCH = 17;
  // Email is linked to multiple accounts at the vendor's side.
  CUSTOMER_EXISTS_EMAIL_LINKED_TO_MULTIPLE_ACCOUNTS = 18;
  // Email is linked to some existing account at the vendor's side.
  CUSTOMER_EXISTS_EMAIL_LINKED_TO_EXISTING_ACCOUNT = 19;
  // Customer with KYC expired or due at the vendor's end.
  CUSTOMER_EXISTS_KYC_NOT_VALID = 20;
  // Phone number is linked to other customer
  CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_EXISTING_ACCOUNT = 21;
  // Email is linked to other customer
  CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_EXISTING_ACCOUNT = 22;
  // No customer found with PAN, but phone number is linked to multiple accounts at the vendor's side.
  CUSTOMER_DOES_NOT_EXIST_PHONE_NUMBER_LINKED_TO_MULTIPLE_ACCOUNTS = 23;
  // No customer found with PAN, but email is linked to other multiple accounts at the vendor's side.
  CUSTOMER_DOES_NOT_EXIST_EMAIL_LINKED_TO_MULTIPLE_ACCOUNTS = 24;
  // Customer has an existing NRI account but the partner bank didn't return a valid customer id
  CUSTOMER_EXISTS_NRI_MISSING_CUSTOMER_ID = 25;
}

enum KYCFlag {
  KYC_FLAG_UNSPECIFIED = 0;

  KYC_FLAG_EXISTS = 1;

  KYC_FLAG_DOES_NOT_EXISTS = 2;
}

enum CreditCardFlag {
  CREDIT_CARD_FLAG_UNSPECIFIED = 0;
  // in case credit card exists and cc team needs to stop onboarding for a user.
  CREDIT_CARD_FLAG_ALREADY_EXISTS = 1;
  CREDIT_CARD_FLAG_DOES_NOT_EXIST = 2;
}

message DedupeCheckResponse {
  // Flag denoting if date of birth matches the bank record or not
  string dob_flag = 3;

  // Flag denoting if mobile no matches the bank record or not
  string mobile_flag = 4;

  // Customer id of the user registered with bank
  string customer_id = 5;

  // rpc status for the api response
  rpc.Status status = 6;

  // Customer name
  string customer_name = 7;

  DedupeStatus dedupe_status = 8;

  // customers linked with phone number
  int64 customers_linked_with_phone_number = 9;

  // raw response from vendor api
  string raw_response = 10;

  KYCFlag k_y_c_flag = 11;

  CreditCardFlag credit_card_flag = 12;

  int64 customers_linked_with_email_id = 13;

  // etb customer pan flag
  PanFlag pan_flag = 14;

  // etb customer aadhaar flag
  AadhaarFlag aadhaar_flag = 15;

  vendorgateway.VendorStatus vendor_status = 16;
}

enum PanFlag {
  PAN_FLAG_UNSPECIFIED = 0;
  PAN_FLAG_EXISTS = 1;
  PAN_FLAG_DOES_NOT_EXIST = 2;
}

enum AadhaarFlag {
  AADHAAR_FLAG_UNSPECIFIED = 0;
  AADHAAR_FLAG_EXISTS = 1;
  AADHAAR_FLAG_DOES_NOT_EXIST = 2;
}

message FetchCustomerDetailsRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // device auth details - device id, device token, user profile id
  header.Auth auth = 2;

  string request_id = 3;

  api.typesv2.common.PhoneNumber phone_number = 4;

  ChannelType channel_type = 5;
}

enum ChannelType {
  CHANNEL_TYPE_UNSPECIFIED = 0;

  APP = 1;

  // customer care / sherlock
  CC = 2;
}

message FetchCustomerDetailsResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  api.typesv2.common.Name customer_name = 2;

  api.typesv2.Gender gender = 3;

  api.typesv2.common.Name father_or_husband_name = 4;

  string mother_maiden_name = 5;

  api.typesv2.common.PhoneNumber phone_number = 6;

  string email_id = 7;

  google.type.Date date_of_birth = 8;

  api.typesv2.MaritalStatus marital_status = 9;

  bool Nre = 10;

  string occupation = 11;

  VendorAddress communication_address = 12;

  VendorAddress permanent_address = 13;

  VendorAddress shipping_address = 14;

  string salutation = 15;

  // list of all the accounts present for a customer at Federal's end
  // this includes savings and deposit accounts
  repeated Account account_list = 16;

  employment.OccupationType occupation_type = 17;

  vendorgateway.VendorStatus vendor_status = 18;
}

message VendorAddress {
  string address_line1 = 1;
  string address_line2 = 2;
  string city_code = 3;
  string state_code = 4;
  string country_code = 5;
  string pin_code = 6;
}

message Account {
  string account_number = 1;
  string account_type = 2;
  string scheme_code = 3;
}

// ProductType here enumerates the types of product that can be created
enum ProductType {
  PRODUCT_TYPE_UNSPECIFIED = 0;
  // indicates savings account
  SAVINGS_ACCOUNT = 1;
}

message CheckProductEligibilityRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // unique identifier for this request
  string request_id = 2;
  // user mobile number
  api.typesv2.common.PhoneNumber mobile_number = 3;
  // user's pan number
  string pan = 4;
  // user's deviceId
  string device_id = 5;
  // the type of product for which we are checking the eligibility
  ProductType product_type = 6;
}

message CheckProductEligibilityResponse {
  rpc.Status status = 1;
  // application reference id is the one with which the user's whole onboarding journey is done.
  string application_reference_id = 2;
  // tells us whether the customer is new or existing one to the bank - dedupe status
  DedupeStatus dedupe_status = 3;
  // tells us whether the user is eligible
  bool is_eligible = 4;
  // token that needs to be passed during eKYC
  string ekyc_token = 5;
  // url for doing eKYC
  string ekyc_url = 6;
  // code and description received from vendor
  vendorgateway.VendorStatus vendor_status = 7;
}

enum CustomerCreationFlow {
  CUSTOMER_CREATION_FLOW_UNSPECIFIED = 0;
  // indicates onboarding O type flow, can be extended to CKYC L and S type
  CUSTOMER_CREATION_FLOW_VKYC_ONBOARDING_LSO = 1;
  // indicates OTP based Min KYC CIF creation(CKYC failed, user subjected to EKYC)
  CUSTOMER_CREATION_FLOW_MIN_EKYC = 2;
  // CKYC based details fetched with PAN & Aadhar. Since Aadhar data is hashed, customer had to be routed to EKYC to fetch aadhar number
  // subsequently we onboard as full KYC customer
  CUSTOMER_CREATION_FLOW_CKYC_EKYC = 3;
  // indicates CKYC based full KYC
  CUSTOMER_CREATION_FLOW_CKYC = 4;
  // indicates that the customer has come to customer creation after completing VKYC (a blanket type for all use cases other than LSO or MIN KYC Dedupe)
  CUSTOMER_CREATION_FLOW_VKYC = 5;
  // indicates that the customer is a min kyc dedupe, and we performed vkyc to convert customer to full KYC.
  CUSTOMER_CREATION_FLOW_DEDUPE_MIN_KYC = 6;
  // indicates that the customer is full KYC through biometric kyc
  CUSTOMER_CREATION_FLOW_BKYC = 7;
}

message EnquireVKYCStatusRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // ekycrrn of user
  string ekyc_rrn = 2;
}

message EnquireVKYCStatusResponse {
  enum Status {
    SUCCESS = 0;
    PARSING_ERROR = 101;
    INVALID_INPUTS = 102;
    INTERNAL = 103;
    MANDATORY_FIELDS_MISSING = 104;
    INVALID_CONTENT_TYPE = 105;
    INVALID_REQUEST_TYPE = 106;
  }
  rpc.Status status = 1;
  // call status on federal end
  VKYCStatus vkyc_status = 2;
  // remark for vkyc
  string message = 3;
  // vkyc callback date
  google.type.Date date = 4;
}

enum VKYCStatus {
  VKYC_STATUS_UNSPECIFIED = 0;
  // indicates vkyc is still in review auditor action pending
  VKYC_STATUS_IN_REVIEW = 1;
  // indicates vkyc is approved by auditor
  VKYC_STATUS_APPROVED = 2;
  // indicates vkyc is rejected by auditor
  VKYC_STATUS_REJECTED = 3;
  // agent call back in missing on federal end
  VKYC_STATUS_AGENT_CALL_BACK_MISSING = 4;
  // auditor callback is missing on federal end
  VKYC_STATUS_AUDITOR_CALL_BACK_MISSING = 5;
  // ekyc rrn is not valid
  VKYC_STATUS_INVALID_CUSTOMER_ID = 6;
  // no valid account exist for given ekyc rrn
  VKYC_STATUS_ACCOUNT_NOT_EXIST = 7;
  // vkyc approved on fed bt scheme change didnt change on fed end
  VKYC_STATUS_VKYC_SCHEME_CHANGE_ERROR = 8;
  // auditor marked vkyc under recapture bucket
  VKYC_STATUS_AUDITOR_RECAPTURE = 9;
  // KYC full KYC upgradation pending, Kyc haven't processed yet and will be approved in the future
  VKYC_STATUS_FULL_KYC_UPGRADE_PENDING = 10;
  // indicates vkyc is rejected by agent
  VKYC_STATUS_AGENT_REJECTED = 11;
}

message UpgradeKYCLevelRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // customer id of the user at the vendor's end
  string vendor_customer_id = 2;
  // ekyc rrn obtained post successful kyc
  string ekyc_rrn = 3;
  // sign capture during KYC process
  string sign_image = 4;
}

message UpgradeKYCLevelResponse {
  rpc.Status status = 1;
  // code and description received from vendor
  vendorgateway.VendorStatus vendor_status = 2;
}

message CreateLoanCustomerRequest {
  vendorgateway.RequestHeader header = 1;
  string request_id = 2;
  string sol_id = 3;
  string bre_ref_number = 4;
  string uid_no = 5;
  api.typesv2.common.Name name = 6;
  string father_name = 7;
  string mother_name = 8;
  string email = 9;
  google.type.Date date_of_birth = 10;
  api.typesv2.Gender gender = 11;
  api.typesv2.MaritalStatus marital_status = 12;
  api.typesv2.common.PhoneNumber phone_number = 13;
  // Contact details of the customer.
  google.type.PostalAddress communication_address = 14;
  google.type.PostalAddress permanent_address = 15;
  ProofDetails identity_proof = 16;
  // details of address proof
  ProofDetails address_proof = 17;
  string pan_number = 18;
  //base64 string of the user's signature image
  string sign_image = 19;
  employment.EmploymentType employment_type = 20;
  float annual_income = 21;
  employment.OccupationType occupation_type = 22;
  api.typesv2.Community community = 23;
  api.typesv2.Qualification qualification = 24;
  api.typesv2.Designation designation = 25;
  api.typesv2.DisabilityType disability_type = 26;
  api.typesv2.Category category = 27;
}

message CreateLoanCustomerResponse {
  rpc.Status status = 1;
  string request_id = 2;
  vendorgateway.VendorStatus vendor_status = 3;
}

message LoanCustomerCreationStatusRequest {
  vendorgateway.RequestHeader header = 1;
  // Todo(vipul/sharath) : Need to understand what is expected to be sent here
  string request_id = 2;
  // Todo(vipul/sharath) : Need to understand what is expected to be sent here
  string cif_request_id = 3;
}

message LoanCustomerCreationStatusResponse {
  string request_id = 1;
  string customer_id = 2;
  string customer_name = 3;
  rpc.Status status = 4;
  vendorgateway.VendorStatus vendor_status = 5;
  google.protobuf.Timestamp cif_created_time = 6;
}
