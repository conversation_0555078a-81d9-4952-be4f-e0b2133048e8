// protolint:disable MAX_LINE_LENGTH

// Vendor gateway APIs for ChequeBook Request and ChequeBook Tracking

syntax = "proto3";

package vendorgateway.openbanking.bank_customer;

import "api/bankcust/enums.proto";
import "api/employment/employment_data.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/phone_number.proto";
import "api/user/profile.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vendor_status.proto";
import "api/vendorstore/vendorstore.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/postal_address.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/bank_customer";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.bank_customer";

service BankCustomer {
  rpc OrderChequebook (OrderChequebookRequest) returns (OrderChequebookResponse);
  rpc TrackChequeBook (TrackChequebookRequest) returns (TrackChequebookResponse);
  // IssueDigitalCancelledCheque rpc to issue a digital cancelled cheque for any federal account.
  rpc IssueDigitalCancelledCheque (IssueDigitalCancelledChequeRequest) returns (IssueDigitalCancelledChequeResponse);
  rpc UpdateProfileAtBank (UpdateProfileAtBankRequest) returns (UpdateProfileAtBankResponse);
  rpc CheckProfileUpdateStatus (CheckProfileUpdateStatusRequest) returns (CheckProfileUpdateStatusResponse);
}

message CheckProfileUpdateStatusRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique identifier for this request to be generated by epiFi.
  // In case of status check or retries, this request ID is to be
  // sent.
  string request_id = 2;
}

message CheckProfileUpdateStatusResponse {
  enum Status {
    OK = 0;
    IN_PROGRESS = 51;
  }
  rpc.Status status = 1;
  vendorgateway.VendorStatus vendor_status = 2;
}

message UpdateProfileAtBankRequest {
  vendorgateway.RequestHeader header = 1;
  // Unique identifier for this request to be generated by epiFi.
  // In case of status check or retries, this request ID is to be sent.
  string request_id = 2;
  bankcust.ProfileUpdateChannel channel = 3;
  string customer_id = 4;
  string ekyc_rrn = 5;
  google.type.Date date_of_birth = 6;
  google.type.PostalAddress communication_address = 7;
  // User declared monthly salary range
  user.SalaryRange salary_range = 8;
  employment.OccupationType occupation_type = 9;
  string pan = 10;
  // identifier for updation, eg. OCCUPATION
  bankcust.ProfileField profile_field = 11;
}

message UpdateProfileAtBankResponse {
  enum Status {
    OK = 0;
    DUPLICATE_REQUEST = 101;
  }
  rpc.Status status = 1;
  vendorgateway.VendorStatus vendor_status = 2;
}

// Represents request object to request cheque book
message OrderChequebookRequest {
  // Common request header across all vendorgateway APIs.
  // Denotes the vendor that is supposed to process this request

  enum DeliveryPoint {
    DELIVERY_POINT_UNSPECIFIED = 0;
    DELIVERY_POINT_BRANCH = 1;
    DELIVERY_POINT_CUSTOMER = 2;
  }

  enum ChequebookLeaf {
    CHEQUEBOOK_LEAF_UNSPECIFIED = 0;
    CHEQUEBOOK_LEAF_TEN = 1;
    CHEQUEBOOK_LEAF_TWENTY = 2;
    CHEQUEBOOK_LEAF_FIFTY = 3;
    CHEQUEBOOK_LEAF_HUNDRED = 4;
  }

  vendorgateway.RequestHeader header = 1;

  // unique for each request - Request Id
  string request_id = 2;

  // Registered customer device ID
  string device_id = 3;

  // device_token will be available in device registration response
  string device_token = 4;

  // mobile number of user
  api.typesv2.common.PhoneNumber phone_number = 5;

  // MPIN should be passed
  string cred_block = 6;

  // account_number of user
  string account_number = 7;

  ChequebookLeaf cheque_number = 8;

  DeliveryPoint delivery_point = 9;
}

// Represents response object for request cheque book
message OrderChequebookResponse {
  enum Status {
    OK = 0;
    // invalid argument
    INVALID_ARGUMENT = 3;
    // If no cheque book request are found
    NOT_FOUND = 5;
    // Internal Error
    INTERNAL = 13;
    // Exceeded the maximum allowed cheque leaf
    EXCEEDED_MAX_CHEQUEBOOK_LEAF = 101;
    // Minimum Balance  Error
    MINIMUM_BALANCE = 102;
    // no signature error
    NO_SIGNATURE = 103;
    // Invalid sender details
    INVALID_SENDER_DETAILS = 104;
    // duplicate request id
    DUPLICATE_REQUEST_ID = 105;
    // transaction limit exceeded
    TRANSACTION_LIMIT_EXCEEDED = 106;
    // internal error from federal
    INTERNAL_SERVICE_DOWN = 107;
    // service not enable for the sender code
    SERVICE_NOT_ENABLED = 108;
    // invalid message
    INVALID_MESSAGE = 109;
    // If delivery point is B(Branch)
    NEO_ERROR = 110;
    // Invalid cheque leaf number other than 10->Allowed cheque leaf numbers, at times of implementation
    DNMERR_ERROR = 111;
    // Account NPA
    ACCOUNT_NPA = 112;
    // Address error
    ADDRESS_ERROR = 113;
    // State Code Error
    STATE_CODE_ERROR = 114;
    // Pin code Error
    PIN_CODE_ERROR = 115;
    // Country Code Error
    COUNTRY_CODE_ERROR = 116;
    // Account Error
    ACCOUNT_ERROR = 117;
    // general error other than mentioned above for code 0BE0999
    GENERAL_ERROR = 118;
    // Invalid pin
    INVALID_PIN = 119;
    // number of pin tries exceeded, Maximum 3 attempts with a cool down period of 24hrs.
    NUMBER_OF_PIN_TRIES_EXCEEDED = 120;
    // secure pin not set, need to handled on client side
    TRANSACTION_PIN_NOT_SET = 121;
  }

  rpc.Status status = 1;

  google.protobuf.Timestamp transaction_timestamp = 3;
}

// Represents request object for track cheque book
message TrackChequebookRequest {
  // Common request header across all vendorgateway APIs.
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // unique for each request - Request Id
  string request_id = 2;

  // Registered customer device ID
  string device_id = 3;

  // device_token will be available in device registration response
  string device_token = 4;

  // mobile number of user
  api.typesv2.common.PhoneNumber phone_number = 5;

  // MPIN should be passed
  string cred_block = 6;

  // account_number of user
  string account_number = 7;
}

message TrackChequebookResponse {
  enum Status {
    OK = 0;
    // If no cheque book request are found
    NOT_FOUND = 5;
    // Internal Error
    INTERNAL = 13;
  }

  enum CourierName {
    COURIER_NAME_UNSPECIFIED = 0;
    COURIER_NAME_ARAMEX_COURIER = 1;
    COURIER_NAME_DELHIVERY = 2;
    COURIER_NAME_EXPRESS_IT_COURIER = 3;
    COURIER_NAME_EXQUISITE_SOLUTIONS = 4;
    COURIER_NAME_FIRST_FLIGHT_COURIER = 5;
    COURIER_NAME_PROFESSIONAL_COURIER = 6;
    COURIER_NAME_INDIA_POST_SERVICE_KOCHI = 7;
    COURIER_NAME_INDIA_POST_SERVICE_UDUPI = 8;
    COURIER_NAME_QUANTIUM_COURIER_CUSTOMER = 9;
    COURIER_NAME_QUANTIUM_COURIER_DOMESTIC = 10;
    COURIER_NAME_QUANTIUM_COURIER_FEDERAL_BANK = 11;
    COURIER_NAME_SPEED_AND_SAFE = 12;
  }

  message TrackResult {
    string request_id = 1;
    string account_number = 2;
    CourierName courier_cd = 3;
    string bar_code = 4;
    google.protobuf.Timestamp request_date = 5;
  }

  rpc.Status status = 1;

  // track result
  repeated TrackResult track_result = 2;

  google.protobuf.Timestamp transaction_timestamp = 3;
}

message IssueDigitalCancelledChequeRequest {
  // Common request header across all vendorgateway APIs.
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // account number of the federal account on which digital cancelled cheque will be issued.
  string account_number = 2 [(validate.rules).string = {min_len: 1, max_len: 35}];

  // unique for each request
  string request_id = 3;
}

message IssueDigitalCancelledChequeResponse {
  enum Status {
    OK = 0;
    // unknown
    UNKNOWN = 2;
    // invalid argument
    INVALID_ARGUMENT = 3;
    // digital cancelled cheque already exist for account.
    ALREADY_EXISTS = 6;
    // Internal Error
    INTERNAL = 13;
  }

  // Denotes the status of response
  rpc.Status status = 1;

  // vendor api response.
  vendorstore.VendorResponse vendor_response = 2;
}
