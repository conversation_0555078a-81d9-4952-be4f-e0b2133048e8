// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendorgateway.openbanking.upi;

import "api/accounts/account_type.proto";
import "api/card/card.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/account/enums.proto";
import "api/upi/complaint/complaint.proto";
import "api/upi/cred_block.proto";
import "api/upi/customer.proto";
import "api/upi/device.proto";
import "api/upi/mandate/mandate.proto";
import "api/upi/merchant.proto";
import "api/upi/qr_details.proto";
import "api/upi/txnref/txn_reference.proto";
import "api/upi/upi.proto";
import "api/vendorgateway/openbanking/upi/header.proto";
import "api/vendorgateway/openbanking/upi/upi.proto";
import "api/vendorgateway/vendor_status.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/upi";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.upi";

// UPI service enable UPI related actions like
// GetVPA, GetToken, VerifyPA etc.
service UPI {

  // Returns the token to be used for registering the app to use NPCI common library
  // The registration flow is as follows
  //    1) Execute “Get Challenge” service to receive a challenge from common library.
  //    2) Use the generated challenge to get the token from PSP server
  //    3) Use the token to register the app with common library
  rpc GetToken (GetTokenRequest) returns (GetTokenResponse) {}

  // RPC method to validate a VPA entered by an customer on PSP app with NPCI and return verified name of the associated
  // customer.
  // This RPC is mandatory as per the protections against phishing guidelines from NPCI.
  // Payee’s PSP application should mandatorily send verified payee’s name to NPCI as part of the collect request.
  rpc ValidateAddress (ValidateAddressRequest) returns (ValidateAddressResponse) {}

  // Secure PIN a.k.a UPI PIN will be used to authenticate all modes of transactions
  // i.e., IMPS, RTGS, NEFT, UPI.
  //
  // This would enhance the user’s experience by using the same PIN for all transactions.
  //
  // This RPC helps to set Secure PIN for the given account
  //
  // Note that Secure PIN is against an account and not a UPI VPA
  rpc SetPIN (SetPINRequest) returns (SetPINResponse);

  // RPC method to initiate a UPI payment with the partner bank.
  // It can be used for both Direct Pay and Collect Pay transactions.
  //
  // This is an asynchronous API such that, payment may not be completed immediately.
  // This API, hence, will respond with an Acknowledgement message immediately.
  //
  // Any updates on the transaction at the partner bank including the transaction SUCCESS are sent
  // using call back `RespPay` mechanism that is not in scope of this service.
  //
  // transaction id in the request header is used as an unique identifier which can be used by a caller to map
  // callbacks to a request.
  //
  // Caller can enquire for the transaction status after a time period of Transaction expiry time + 90 seconds
  // as per NPCI guidelines
  rpc ReqPay (ReqPayRequest) returns (ReqPayResponse) {}

  // RPC method to create/update/revoke UPI Mandate with the partner bank
  rpc ReqMandate (ReqMandateRequest) returns (ReqMandateResponse) {}

  // RespAuthDetails is the response call back interface to return back auth details to NPCI.
  // after processing the ReqAuthDetails API, PSP should send response to NPCI the
  // authorization by calling the “RespAuthDetails” API .
  rpc RespAuthDetails (RespAuthDetailsRequest) returns (RespAuthDetailsResponse) {
    option (rpc.skip_tokenization) = false;
  }

  // RespAuthValCust is the response call back interface to return back auth val cust to NPCI
  rpc RespAuthValCust (RespAuthValCustRequest) returns (RespAuthValCustResponse) {}

  // RespAuthMandate is the response call back interface to return back auth mandate to NPCI
  rpc RespAuthMandate (RespAuthMandateRequest) returns (RespAuthMandateResponse) {
    option (rpc.skip_tokenization) = false;
  }

  // RespMandateConfirmation is the response call back interface to return back mandate confirmation to NPCI
  rpc RespMandateConfirmation (RespMandateConfirmationRequest) returns (RespMandateConfirmationResponse) {}

  // Used to request OTP from issuer bank via UPI for ATM PIN validation
  // Returns secure_url for redirection to bank page for OTP validation
  rpc GenerateUpiOtp (GenerateUpiOtpRequest) returns (GenerateUpiOtpResponse) {}

  // This API allows the customer to set new UPIPIN for the first time
  // Flow will be like below
  //  1. In the mobile registration process, last 6 digit of the card number and expiry date are captured in acquirer PSP app.
  //  2. PSP initiates ReqOTP along with card details to NPCI.
  //  3. NPCI sends the RespOTP to PSP.
  //  4. App will call the Common Library(CL) with the bank specified URL in the
  //     specified format. CL will auto-populate the OTP and redirect call to Bank_url
  //  5. Issuer PSP will generate a “Response ID” and store against the Token generated in RespOTP. “Response ID”
  //     will be used to link the ReqRegMob with this transactions. Issuer PSP sends the response to issuer bank page
  //     with the “Response ID” and status of ATM PIN verification.
  rpc RegisterMobile (RegisterMobileRequest) returns (RegisterMobileResponse) {
    option (rpc.skip_tokenization) = false;
  }

  // This API allows to fetch all encryption public keys from NPCI and UIDAI.
  // PSP can cache the list of public keys.
  // These public keys will be used to capture sensitive data like card digits, expiry
  // These keys will be used by CL (Common Library)
  rpc ListKeys (ListKeysRequest) returns (ListKeysResponse) {};

  // This API allow PSPs to find the list of accounts linked to the mobile by
  // a particular account provider (for our case it is federal).
  rpc ListAccount (ListAccountRequest) returns (ListAccountResponse) {};

  // This API will allow to list all account provider
  // PSPs will maintain the list and check for registered account providers
  // before registering a customer account within application
  rpc ListAccountProviders (ListAccountProviderRequest) returns (ListAccountProviderResponse) {};

  // RespTxnConfirmation is for response call back to NPCI to confirm the transaction
  // Will be send as a call back after receiving ReqTxnConfirmation
  rpc RespTxnConfirmation (RespTxnConfirmationRequest) returns (RespTxnConfirmationResponse) {};

  // Creates virtual Id for an account
  rpc CreateVirtualId (CreateVirtualIdRequest) returns (CreateVirtualIdResponse);

  // Response for req validate address call back
  // on receiving reqValAddress call back epifi needs to respond if the VPA in request is valid or not
  rpc RespValidateAddress (RespValidateAddressRequest) returns (RespValidateAddressResponse);

  // rpc to request for the status of the upi transaction
  // Note : should request for status only after the specified timeout period.
  rpc ReqCheckTxnStatus (ReqCheckTxnStatusRequest) returns (ReqCheckTxnStatusResponse);

  // RPC to fetch list of verified address (VPA) from UPI
  // This is async API and ack will be returned from this rpc.
  // One or more callback will be sent to UPI service with list of Verified address.
  // Number of callback depends on page size in request and total verified entries.
  // Ex. If total verified entries are 10000 and page size is 1000 then total 10 callbacks will be received.
  rpc ListVae (ListVaeRequest) returns (ListVaeResponse);

  // rpc to request for the status of the upi transaction for urn (intent/qr) based payments
  // The business logic will be same  to ReqCheckTxnStatus rpc
  // For ReqCheckTxnStatus rpc status RecordNotFound is not expected
  // whereas for ReqCheckURNTxnStatus status RecordNotFound is expected in some cases.
  // This is because in case of URN payments the user can anytime quit in between order creation and
  // transaction initiation.
  // For eg. for an intent based payment the user goes to the 3rd party app but returns back to fi
  // without making a payment
  rpc ReqCheckURNTxnStatus (ReqCheckTxnStatusRequest) returns (ReqCheckTxnStatusResponse);

  // rpc to enquire account balance
  // It validates the cred data and returns the available or ledger balance or both depending on the bank.
  rpc BalanceEnquiry (BalanceEnquiryRequest) returns (BalanceEnquiryResponse);

  // ReqCheckMandateStatus checks for mandate status
  rpc ReqCheckMandateStatus (ReqCheckTxnStatusRequest) returns (ReqCheckTxnStatusResponse);

  // ReqCheckComplaintStatus checks for complaint status
  rpc ReqCheckComplaintStatus (ReqCheckComplaintStatusRequest) returns (ReqCheckComplaintStatusResponse);

  // RaiseComplaint rpc to raise a complaint on a upi transaction to NPCI via vendor. A dispute will be created at vendor
  // end if not already created. It will give already exist error if already dispute/complaint is created on the transaction
  // (transaction will be identified from TransactionHeader.OriginalTxnId ). After dispute creation at vendor side NPCI will
  // follow up the payment status from the bank involve in transaction(Beneficiary and Remitter Bank) and update it transaction status
  // from payer and payee psp both. NPCI will send auto updates for any update on transaction after successful dispute creation.
  // A user can also call ReqComplaintStatus to check current status of complaint.
  // This RPC will return CRN(complaint reference number) with success status code.
  rpc RaiseComplaint (RaiseComplaintRequest) returns (RaiseComplaintResponse);

  // ReqActivation rpc is used to activate the customer to do international transaction.
  // Customer selects the option to activate their account for international transactions through UPI.
  // Customer may either scan a global UPI QR and app will prompt customer to enable UPI International
  // or the customer may go to the settings option and enable the same even before doing the international transaction.
  // This is an one time activity. Customer enters the UPI PIN and authorizes the activation request.
  rpc ReqActivation (ReqActivationRequest) returns (ReqActivationResponse);

  //  NPCI will maintain the list of all registered PSPs and their details. This API allows the PSPs
  //  to  request  the  list  of  all  registered  PSPs  for  local  caching.  This  data  should  be  used  for
  //  validating payment address before initiating the transaction.
  rpc ListPsp (ListPspRequest) returns (ListPspResponse);

  // RegMapper register the phone number/numeric id as upi number with vendor
  // Supports addition of upi number and modifying existing upi number
  // Upi Number - alias for vpa which can be used receive payments
  rpc RegMapper (RegMapperRequest) returns (RegMapperResponse);

  // GetMapperInfo is used to fetch information for given UPI number or VPA
  // It has 3 types : Fetch, Check and Port
  // GetMapperInfo is mandatory in case of :
  // 1) Creation of UPI Number (Mobile Number /Numeric ID)
  // 2) Transfer of Mobile Number to different PSP
  rpc GetMapperInfo (GetMapperInfoRequest) returns (GetMapperInfoResponse);

  // During ‘PORT’ (Transfer of Mobile Number from One PSP to Other PSP),
  // the Previous PSP will be notified through ReqMapperConfirmation
  // RespMapperConfirmation is for response call back to NPCI to confirm the above
  // PORT request. Will be send as a call back after receiving ReqMapperConfirmation
  rpc RespMapperConfirmation (RespMapperConfirmationRequest) returns (RespMapperConfirmationResponse);

  // Customer has to activate for international transaction unlike domestic transaction.
  // ActivateInternationalPayments(NPCI Api name: `ReqActivation`) is used for the activation of international payments for the customer
  // This is an extra step of safety and control measure added by NPCI, for exposure to the international transactions.
  // The same API will have provision for Updation, deactivation & Query.
  rpc ActivateInternationalPayments (ActivateInternationalPaymentsRequest) returns (ActivateInternationalPaymentsResponse);

  // Customer has to validate QRs scanned either static or dynamic.
  // ValidateInternationalQr(NPCI Api name: `ReqValQr`) is used to validate international QRs before transaction initiation.
  rpc ValidateInternationalQr (ValidateInternationalQrRequest) returns (ValidateInternationalQrResponse);

  // GetUpiLite -
  // 1. triggers the upi lite activation with vendor for a given upi account
  // 2. gives LITE REFERENCE NUMBER (LRN) in response, which will be used
  //    as reference number during creation of upi lite account.
  // 3. For all the future actions on upi lite account, lrn will be
  //    used as a unique identifier b/w Fi and vendor.
  rpc GetUpiLite (GetUpiLiteRequest) returns (GetUpiLiteResponse);

  // SyncUpiLiteInfo-
  // 1. Delete upi lite account which have 0 balance
  // 2. Fetch account status and detail for upi lite account
  rpc SyncUpiLiteInfo (SyncUpiLiteInfoRequest) returns (SyncUpiLiteInfoResponse);
}

// request object to get token for registering the app with NPCI common library
message GetTokenRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // cred block to get the token to register the device with NPCI common library
  // Data inside cred block should be in the format - device id|app id|mobile number|challenge
  // device id, app id, mobile number and challenge are concatenated including pipe characters in between.
  .upi.CredBlock cred_block_challenge = 3;
}

// response object to get token for registering the app with NPCI common library
message GetTokenResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Device registration failed. One of reason could be error at NPCI end.
    DEVICE_REGISTRATION_FAILED = 100;
    // EXTERNAL_ERROR_AT_VENDOR
    VENDOR_EXTERNAL_ERROR = 120;
  }
  // Status of the request
  rpc.Status status = 1;
  // token to be used for registering the app with NPCI common library
  string token = 2;
}

message ValidateAddressRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // Information of the customer who is adding a beneficiary is passed as payer information
  message Payer {
    // payment address (such as Aadhaar number, Mobile number, Debit/Credit Card, virtual payment address, etc.)
    string payment_address = 1;

    // name of the payer
    string name = 2;

    // type of the payer
    .upi.CustomerType type = 3;

    // information payer information
    .upi.CustomerInformation info = 4;

    // payer device information
    // to be used to calculate risk score at NPCI
    .upi.Device device = 5;
  }

  Payer payer = 3;

  // payee customer information
  message Payee {
    // payment address (such as Aadhaar number, Mobile number, Debit/Credit Card, virtual payment address, etc.)
    // Information of the VPA to be validated is passed as payee address
    string payment_address = 1;
  }

  Payee payee = 4;
  // Optional field added to track UPI API usage on behalf of whom(actor) the API is called
  string actor_id = 5;
}

message ValidateAddressResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Invalid payee VPA passed
    INVALID_VPA = 100;
    // invalid merchant psp
    INVALID_MERCHANT = 101;
    // transaction not permitted to the vpa by psp
    TRANSACTION_NOT_PERMITTED_BY_PSP = 102;
    // vpa restricted by the customer
    VPA_RESTRICTED = 103;
    // PSP is not registered with NPCI
    PSP_NOT_REGISTERED = 104;
    // PSP not available at the moment
    PSP_NOT_AVAILABLE = 105;
    // error occurred due to psp timeout at vendor side.
    PSP_TIMEOUT = 112;
    // Beneficiary account does not exist
    BENEFICIARY_ACCOUNT_DOES_NOT_EXIST = 106;
    // Deprecated in the favour of EXTERNAL_ERROR_AT_VENDOR
    EXTERNAL_ERROR = 113 [deprecated = true];
    // PSP/Bank is Non responsive
    BANK_IS_NON_RESPONSIVE = 114;
    // Remitter account does not exist
    REMITTER_ACCOUNT_DOES_NOT_EXIST = 115;
    // EXTERNAL_ERROR_AT_VENDOR
    VENDOR_EXTERNAL_ERROR = 120;
    // MERCHANT ERROR (PAYEE PSP)
    PAYEE_PSP_MERCHANT_ERROR = 121;
    // Suspected Fraud, Decline / Transactions declined
    // based on risk score by beneficiary
    SUSPECTED_FRAUD = 122;
    // upi number is inactive
    // Note: validate address is used
    // to validate upi number in upi
    // number search
    MAPPING_INACTIVE = 123;
  }

  rpc.Status status = 1;

  string req_msg_id = 2;

  // Mask Name of the Beneficiary to whom payee VPA belongs to
  string mask_name = 3;

  // Merchant Classification Code
  string mcc = 4;

  // merchant details will be populated if the customer is merchant
  .upi.MerchantDetails merchant = 5;

  // vpa that is verified in the request
  // In some cases like verifying upi number vpa, the vpa in the response
  // will be vpa linked to the upi number
  string vpa = 6;

  // ifsc code of the account linked to the vpa
  string ifsc = 7;

  // type of the account linked to the vpa
  accounts.Type account_type = 8;

  // Account Product Offering associated with the AccountType.
  // This is derived from the accType received from the vendor.
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE together will derive a unique AccType to send to vendor.
  api.typesv2.account.AccountProductOffering apo = 9;

  // specifies features that are supported/Restricted.
  // Example - Value "O1" for mandate supported.
  // Example2 - In case of merchants if we get "05", it states that the merchant is not allowed to receive payments via Rupay Credit card.
  // https://drive.google.com/drive/folders/1WsUVRBTCxCu55ktPbGpKqYx0q25eLUzk
  FeatureSupported feature_supported = 11;
}

// Request message for setting PIN
message SetPINRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Common txn attributes that contain data like unique identifier for txn
  // customer_ref, notes regarding txn
  // Transaction here means an API call to NPCI
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // Payer details
  message Payer {
    // virtual payment address
    string vpa_address = 1;
    // Merchant Classification Code – MCC
    // MCC (Merchant Category Code) is an ISO defined standard to classify merchant categories
    // in retail payments across all payment modes, including UPI
    string mcc_code = 2;
    // name associated with vpa
    string name = 3;
    // type of payer
    .upi.CustomerType type = 5;
    // payer device information
    .upi.Device device = 6;
    // Account details of customer account_ref, ifsc, account_type (savings,current)
    .upi.CustomerAccountDetails account_details = 7;
  }
  Payer payer = 3;

  // This cred block contain encrypted data/info of old pin of subType MPIN
  .upi.CredBlock old_pin_cred = 4;
  // This cred block contain encrypted data/info of new pin of subType MPIN
  .upi.CredBlock new_pin_cred = 5;
}

// Response message for setting PIN
message SetPINResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Indicates that request failed
    FAILED = 100;
    // bank server down
    BANK_SERVER_DOWN = 101;
    // invalid upi pin
    INVALID_UPI_PIN = 102;
    // debit card not found
    DEBIT_CARD_NOT_FOUND = 103;
    // MPIN NOT set/created by user
    MPIN_NOT_SET = 104;
    // debit card details used to
    // set pin is restricted
    CARD_RESTRICTED = 105;
    // user's account is inactive/dormant
    INACTIVE_ACCOUNT = 106;
    // error occurred due to psp timeout at vendor side.
    PSP_TIMEOUT = 112;
    // Remitter's account is inactive/dormant
    REMITTER_INACTIVE_OR_DORMANT_ACCOUNT = 113;
    // EXTERNAL_ERROR_AT_VENDOR
    VENDOR_EXTERNAL_ERROR = 120;
    // Encryption Error at Vendor
    VENDOR_ENCRYPTION_ERROR = 121;
    // No of pin tries exceeded by the user
    NUMBER_OF_PIN_TRIES_EXCEEDED = 122;
  }
  // Status of the request
  rpc.Status status = 1;
  // raw response code sent by the vendors
  string raw_response_code = 2;
  // raw status description send by the vendors
  string raw_status_description = 3;
}

// Request struct to initiate a payment request over UPI.
// Note: not all fields in the request are mandatory
// As a General thumb rule below can be used-
// If it is a PAY txn below fields are mandatory
//    Under Payer
//      - Address, Info, Device, Account, Amount, Cred
//    Under Payee
//      - Address, Amount
//If it is a COLLECT txn below fields are mandatory.
//     Under Payee
//      - Address, Info, Device, Account, Amount
//     Under Payer
//       - Address, Amount
message ReqPayRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // TODO(nitesh): consider adding meta in the request vs populating it in simulator

  enum Type {
    TYPE_UNSPECIFIED = 0;

    // Allows a customer(payer) to send money to another customer's(payee) account over UPI.
    PAY = 1;

    // Allows a customer(payee) to request funds from another customer's(payer) account over UPI.
    COLLECT = 2;
  }

  // Type of pay request.
  Type req_type = 3;

  // Set of rules that governs the payment
  // Caller can optionally set them based on the requirement.
  message Rules {
    // Defines expiry time of the request. NPCI times out a request in case it doesn't receive a response from payer or
    // payee PSP with in defined expiry time.
    // So for request of type `COLLECT`, expire time defines the time period till which a payer can initiate
    // the payment.
    //
    // It is also used as a threshold beyond which if caller doesn't get a response from NPCI, it can call for status check.
    // The standard guidelines says called can check status after expiry time + 90 seconds.
    //
    // Request expiry time can vary from 1 minute to max 64800 minutes.
    // If not explicitly set by caller. The default expiry is set to 30 minutes.
    uint32 expire_after = 1;

    // TODO(nitesh): add documentation when we get clarity over the field.
    google.type.Money min_amount = 2;
  }

  // Optional: rules to be set for a payment.
  Rules rules = 4;

  // Details of the payer.
  .upi.Customer payer = 5;

  // Details of the payees.
  repeated .upi.Customer payees = 6;
}

// Response struct to initiate a payment request over UPI.
message ReqPayResponse {
  enum Status {
    // payment request has been accepted successfully
    // the caller will be updated about the transaction status
    // via callback from partner bank.
    // Further the caller can do enquiry call to check the final status
    // of the payment
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    // Partner bank will send update once it is aware of the state
    //
    // The state can also be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller can also enquire the transaction status to resolve the ambiguity.
    UNKNOWN = 2;

    // payment initiation has failed
    // this is usually returned when there is a validation failure returned in the ack
    FAILED = 100;
  }
  // Request acknowledgement status
  rpc.Status status = 1;

  // TODO(nitesh): add documentation when we get clarity over the field.
  string req_msg_id = 2;

  // raw status code as sent by the vendor
  string raw_status_code = 3;
  // description for the raw status code
  string raw_status_description = 4;
  // epifi status code corresponding to the raw status code
  string status_code = 5;
  // description of the status code for the payer
  string status_description_payer = 6;
  // description of the status code for payee
  string status_description_payee = 7;
}

//Request struct to create a mandate request over UPI
message ReqMandateRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // Type of  request.
  .upi.mandate.MandateType req_type = 3;

  // Optional: rules to be set for a payment.
  .upi.Rules rules = 4;

  // Details of the payer.
  .upi.Customer payer = 5;

  // Details of the payees.
  repeated .upi.Customer payees = 6;

  // Details of the mandate.
  .upi.mandate.Mandate mandate = 7;

  // mandate initiated by payer/payee
  .upi.mandate.MandateInitiatedBy initiated_by = 8;

}

// Response struct to create a mandate request over UPI.
message ReqMandateResponse {
  enum Status {
    // Returned a success
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    UNKNOWN = 2;

    // Indicates that request failed
    FAILED = 100;
  }
  // Request acknowledgement status
  rpc.Status status = 1;

  string req_msg_id = 2;

  // raw status code as sent by the vendor
  string raw_status_code = 3;
  // description for the raw status code
  string raw_status_description = 4;
  // epifi status code corresponding to the raw status code
  string status_code = 5;
  // description of the status code for the payer
  string status_description_payer = 6;
  // description of the status code for payee
  string status_description_payee = 7;

}

// Request struct for RespAuthDetails API call
// RespAuthDetails API is the response call back interface to return back details. After processing the ReqAuthDetails API,
// PSP should send response to NPCI the authorization by calling the “RespAuthDetails” API .
message RespAuthDetailsRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;
  // set of common response attribute shared across all UPI response interfaces.
  vendorgateway.openbanking.upi.ResponseHeader resp = 3;

  // TODO(raunak) should type be part of transaction header?
  enum Type {
    TYPE_UNSPECIFIED = 0;

    // Allows a customer(payer) to send money to another customer's(payee) account over UPI.
    PAY = 1;

    // Allows a customer(payee) to request funds from another customer's(payer) account over UPI.
    COLLECT = 2;
  }

  // Type of  request.
  Type req_type = 4;

  message Rules {
    // Defines expiry time of the request. NPCI times out a request in case it doesn't receive a response from payer or
    // payee PSP with in defined expiry time.
    // So for request of type `COLLECT`, expire time defines the time period till which a payer can initiate
    // the payment.
    //
    // It is also used as a threshold beyond which if caller doesn't get a response from NPCI, it can call for status check.
    // The standard guidelines says called can check status after expiry time + 90 seconds.
    //
    // Request expiry time can vary from 1 minute to max 64800 minutes.
    // If not explicitly set by caller. The default expiry is set to 30 minutes.
    uint32 expire_after = 1;

    google.type.Money min_amount = 2;
  }

  // Optional: rules to be set for a payment.
  Rules rules = 5;

  // Details of the payer.
  .upi.Customer payer = 6;

  // Details of the payees.
  repeated .upi.Customer payees = 7;

  // represents UPI version to be followed for the request
  // In case UPI version is unspecified V2.0 is chosen as default
  .upi.UpiVersion upi_version = 8;
}

// Response struct for RespAuthDetails.
message RespAuthDetailsResponse {
  enum Status {
    // payment request has been accepted successfully
    // the caller will be updated about the transaction status
    // via callback from partner bank.
    // Further the caller can do enquiry call to check the final status
    // of the payment
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    // Partner bank will send update once it is aware of the state
    //
    // The state can also be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller can also enquire the transaction status to resolve the ambiguity.
    //
    // NOTE: The status is valid while initiating payment against collect request
    // using RespAuth
    UNKNOWN = 2;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // payment initiation has failed
    // this is usually returned when there is a validation failure returned in the ack
    //
    // NOTE: The status is valid while initiating payment against collect request
    // using RespAuth
    FAILED = 100;
  }
  // Request acknowledgement status
  rpc.Status status = 1;
  // unique identifier for each request
  string req_msg_id = 2;

  // raw status code as sent by the vendor
  string raw_status_code = 3;
  // description for the raw status code
  string raw_status_description = 4;
  // epifi status code corresponding to the raw status code
  string status_code = 5;
  // description of the status code for the payer
  string status_description_payer = 6;
  // description of the status code for payee
  string status_description_payee = 7;

}

// Request struct for RespAuthValCust API call
message RespAuthValCustRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;
  // set of common response attribute shared across all UPI response interfaces.
  vendorgateway.openbanking.upi.ResponseHeader resp = 3;

  // Type of  request.
  .upi.mandate.MandateType req_type = 4;

  // Details of the payer.
  .upi.Customer payer = 5;

  // Details of the payee.
  .upi.Customer payee = 6;

  // represents UPI version to be followed for the request
  // In case UPI version is unspecified V2.0 is chosen as default
  .upi.UpiVersion upi_version = 7;

  // Transaction execution time
  google.protobuf.Timestamp exec_timestamp = 8;
}

// Response struct for RespAuthValCust.
message RespAuthValCustResponse {
  enum Status {
    // payment request has been accepted successfully
    // the caller will be updated about the transaction status
    // via callback from partner bank.
    // Further the caller can do enquiry call to check the final status
    // of the payment
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    // Partner bank will send update once it is aware of the state
    //
    // The state can also be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller can also enquire the transaction status to resolve the ambiguity.
    //
    // NOTE: The status is valid while initiating payment against collect request
    // using RespAuth
    UNKNOWN = 2;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // payment initiation has failed
    // this is usually returned when there is a validation failure returned in the ack
    //
    // NOTE: The status is valid while initiating payment against collect request
    // using RespAuth
    FAILED = 100;
  }
  // Request acknowledgement status
  rpc.Status status = 1;
  // unique identifier for each request
  string req_msg_id = 2;

  // raw status code as sent by the vendor
  string raw_status_code = 3;
  // description for the raw status code
  string raw_status_description = 4;
  // epifi status code corresponding to the raw status code
  string status_code = 5;
  // description of the status code for the payer
  string status_description_payer = 6;
  // description of the status code for payee
  string status_description_payee = 7;

}

// Request struct for RespAuthMandate API call
message RespAuthMandateRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;
  // set of common response attribute shared across all UPI response interfaces.
  vendorgateway.openbanking.upi.ResponseHeader resp = 3;

  // Type of  request.
  .upi.mandate.MandateType req_type = 4;

  // Optional: rules to be set for a payment.
  .upi.Rules rules = 5;

  // Details of the payer.
  .upi.Customer payer = 6;

  // Details of the payees.
  repeated .upi.Customer payees = 7;

  // represents UPI version to be followed for the request
  // In case UPI version is unspecified V2.0 is chosen as default
  .upi.UpiVersion upi_version = 8;

  // Details of the mandate.
  .upi.mandate.Mandate mandate = 9;

  // mandate initiated by payer/payee
  .upi.mandate.MandateInitiatedBy initiated_by = 10;
}

// Response struct for RespAuthMandate.
message RespAuthMandateResponse {
  enum Status {
    // Returned a success
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    UNKNOWN = 2;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // Indicates that request failed
    FAILED = 100;
  }
  // Request acknowledgement status
  rpc.Status status = 1;
  // unique identifier for each request
  string req_msg_id = 2;

  // raw status code as sent by the vendor
  string raw_status_code = 3;
  // description for the raw status code
  string raw_status_description = 4;
  // epifi status code corresponding to the raw status code
  string status_code = 5;
  // description of the status code for the payer
  string status_description_payer = 6;
  // description of the status code for payee
  string status_description_payee = 7;

}

// Request struct for RespMandateConfirmation API call
message RespMandateConfirmationRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;
  // set of common response attribute shared across all UPI response interfaces.
  vendorgateway.openbanking.upi.ResponseHeader resp = 3;

  // Type of  request.
  .upi.mandate.MandateType req_type = 4;

  // mandate initiated by payer/payee
  .upi.mandate.MandateInitiatedBy initiated_by = 5;
}

// Response struct for RespMandateConfirmation.
message RespMandateConfirmationResponse {
  enum Status {
    // Returned a success
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    UNKNOWN = 2;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // Indicates that request failed
    FAILED = 100;
  }
  // Request acknowledgement status
  rpc.Status status = 1;
  // unique identifier for each request
  string req_msg_id = 2;

  // raw status code as sent by the vendor
  string raw_status_code = 3;
  // description for the raw status code
  string raw_status_description = 4;
  // epifi status code corresponding to the raw status code
  string status_code = 5;
  // description of the status code for the payer
  string status_description_payer = 6;
  // description of the status code for payee
  string status_description_payee = 7;
}


//Request for UPI otp
message GenerateUpiOtpRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Common txn attributes that contain data like unique identifier for txn
  // customer_ref, notes regarding txn
  // Transaction here means an API call to NPCI
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  .upi.Customer payer = 3;

  // This will be used to capture card details, OTP and pin of user
  // Cred block.
  // In case remitter bank is supporting ATM_REDIRECT,
  // the payer psp should populate the card details in
  // the ReqOtp API itself.
  // With these card details, the remitter bank should form the ATMPIN redirect URL and
  // provide in the RespOtp API for authentication purpose
  // TODO (vivek): remove if we are not going to support ATM_REDIRECT
  repeated .upi.CredBlock cred_block = 4;

  // otp type to be generated
  enum ReqOtpType {
    REQ_OTP_TYPE_BANK = 0;
    REQ_OTP_TYPE_UIDAI = 1;
    REQ_OTP_TYPE_BANK_UIDAI = 2;
  }
  ReqOtpType req_otp_type = 5;
}

// Response for get UPI otp from issuer PSP
message GenerateUpiOtpResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // The request does not have valid authentication credentials for the
    // operation.
    // Reason: Invalid cred block - Either card details or Otp or both
    // that are part of cred block are wrong
    UNAUTHENTICATED = 16;

    // error occurred due to psp timeout at vendor side.
    PSP_TIMEOUT = 112;

    // account for which we hae requested otp doesn't exist
    // this happens in case there is a sync delay at vendor's end
    ACCOUNT_DOES_NOT_EXIST = 101;

    // Deprecated in the favour of EXTERNAL_ERROR_AT_VENDOR
    EXTERNAL_ERROR = 113 [deprecated = true];

    // EXTERNAL_ERROR_AT_VENDOR
    VENDOR_EXTERNAL_ERROR = 120;
  }
  // Status of the request
  rpc.Status status = 1;

  // App will call the Common Library(CL) with this URL in the
  // specified format. CL will auto-populate the OTP.
  // CL will then call this url to re-direct to issuer page for ATM PIN capture.
  // Ref: Bank_URL in UPI docs
  string secure_url = 2;
  // raw response code sent by the vendors
  string raw_response_code = 3;
  // raw status description send by the vendors
  string raw_status_description = 4;
}

message RegisterMobileRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Common txn attributes that contain data like unique identifier for txn
  // customer_ref, notes regarding txn
  // Transaction here means an API call to NPCI
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  .upi.Customer payer = 3;

  // This will be use to capture card, otp and pin of user
  // Cred block
  repeated .upi.CredBlock cred_block = 4;

  // Card info i.e. last 6 digit of card, and expiry in MMYY format
  card.BasicCardInfo card_info = 5;

  // In register mobile request, expected format type varies
  // with the type of flow initiated
  // E.g. For Debit card initiated flow, expected format is FORMAT2
  // Using Aadhaar Number, expected format is FORMAT3
  .upi.FormatType format_type = 6;
}

message RegisterMobileResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Indicates that request failed
    FAILED = 100;
    // debit card is inactive
    CARD_INACTIVE = 101;
    // bank server is down
    BANK_SERVER_DOWN = 102;
    // registration blocked temporarily
    // due to max retries
    MAX_REGISTRATION_RETRIES = 103;
    // incorrect atm pin
    INCORRECT_ATM_PIN = 104;
    // debit card not found
    DEBIT_CARD_NOT_FOUND = 105;
    // debit card is restricted
    DEBIT_CARD_RESTRICTED = 106;
    // debit card has expired
    DEBIT_CARD_EXPIRED = 107;
    // incorrect mpin
    INCORRECT_MPIN = 108;
    // incorrect otp
    INCORRECT_OTP = 109;
    // otp has expired
    OTP_EXPIRED = 110;
    // max otp retries
    MAX_OTP_RETRIES = 111;
    // error occurred due to psp timeout at vendor side.
    PSP_TIMEOUT = 112;
    // Remitter's account is inactive or dormant
    REMITTER_INACTIVE_OR_DORMANT_ACCOUNT = 113;
    // EXTERNAL_ERROR_AT_VENDOR
    VENDOR_EXTERNAL_ERROR = 120;
    // Encryption Error at vendor
    VENDOR_ENCRYPTION_ERROR = 121;
  }
  // Status of the request
  rpc.Status status = 1;

  // Message identifier to correlate between request and response
  // This message id is generated while sending request and sent into Head block
  string req_msg_id = 2;

  // timestamp
  google.protobuf.Timestamp timestamp = 3;

  // raw response code sent by the vendors
  string raw_response_code = 4;

  // raw status description send by the vendors
  string raw_status_description = 5;
}

message ListKeysRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Common txn attributes that contain data like unique identifier for txn
  // customer_ref, notes regarding txn
  // Transaction here means an API call to NPCI
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;
  // Default page size is 1000. Size could range from 1000-10000
  int32 page_size = 3 [(validate.rules).int32 = {gte: 0, lt: 10001}];

  // Based on txn type response to this API will change.
  enum TxnType {
    // Unspecified txn type
    TXN_TYPE_UNSPECIFIED = 0;
    // List keys txn type will be used to fetch get public keys.
    LIST_KEYS = 1;
    // List PSP Keys will be used to get keys for intenet/QR validation
    LIST_PSP_KEYS = 2;
  }
  // If txn_type is List Keys then list key response will
  // return key_xml_payload in sync with this request
  //
  // If txn_type is List PSP keys then ack response will be returned in sync and
  // list of psp keys will be sent to UPI in async response.
  // One or more call back can be send to UPI based on page size.
  // For ex: If total 100 psp keys exist and page size is 10, then 10 callback will be sent to UPI service.
  TxnType txn_type = 4 [(validate.rules).enum = {not_in: [0]}];
}

message ListKeysResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Indicates that request failed
    FAILED = 100;
  }
  // Status of the request
  rpc.Status status = 1;
  // key_xml payload
  string key_xml_payload = 2;
}

message ListAccountRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Common txn attributes that contain data like unique identifier for txn
  // customer_ref, notes regarding txn
  // Transaction here means an API call to NPCI
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  .upi.Customer payer = 3;
  // Optional field added to track UPI API usage on behalf of whom(actor) the API is called
  string actor_id = 4;
}

message ListAccountResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Indicates that request failed
    FAILED = 100;
    // no bank account exist for request
    NO_ACCOUNT_EXIST = 101;
    // mobile associated with multiple customer id
    MULTIPLE_CUSTOMER_ID_FOR_MOBILE = 102;
    // error occurred due to psp timeout at vendor side.
    PSP_TIMEOUT = 112;
    // EXTERNAL_ERROR_AT_VENDOR
    VENDOR_EXTERNAL_ERROR = 120;
    // Transaction not permitted to the account
    TRANSACTION_DENIED_ON_ACCOUNT = 103;
  }
  // Status of the request
  rpc.Status status = 1;

  // list of account info received from NPCI for a given IFSC and mobile number
  repeated upi.UpiAccountInfoVendor account_info_vendor = 2;

  // raw response code sent by the vendors
  string raw_response_code = 3;

  // raw status description send by the vendors
  string raw_status_description = 4;

  // vendor_status encapsulates the vendor's status code and message returned from vendor api.
  vendorgateway.VendorStatus vendor_status = 5;
}

message ListAccountProviderRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Common txn attributes that contain data like unique identifier for txn
  // customer_ref, notes regarding txn
  // Transaction here means an API call to NPCI
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;
}

// Details of versioning
message Version {
  // version number
  string number = 1;
  // version descriptions
  string description = 2;
  // description of mandatory flag
  enum Mandatory {
    MANDATORY_UNSPECIFIED = 0;
    MANDATORY_TRUE = 1;
    MANDATORY_FALSE = 2;
  }
  Mandatory mandatory = 3;
}

// Details of registered Account Providers List
message AccountPvd {
  // Name of the account provider
  string name = 1;
  // IIN of Account Provider
  string iin = 2;
  string ifsc = 3;
  // status of the account provider if it is active or not
  bool active = 4;
  // url link provided by account provider
  string url = 5;
  // name of the SPOC
  string spoc_name = 6;
  // email of the SPOC
  string spoc_email = 7;
  // phone of the SPOC
  string spoc_phone = 8;

  // List of NPCI Products for which account provider is live
  enum Products {
    PRODUCTS_UNSPECIFIED = 0;
    PRODUCTS_AEPS = 1;
    PRODUCTS_IMPS = 2;
    PRODUCTS_CARD = 3;
    PRODUCTS_NFS = 4;
  }
  Products product = 9;

  // Register format of the account provider information in the UPI system
  enum MobRegFormat {
    MOB_REG_FORMAT_UNSPECIFIED = 0;
    MOB_REG_FORMAT_FORMAT1 = 1;
    MOB_REG_FORMAT_FORMAT2 = 2;
    MOB_REG_FORMAT_ATM_REDIRECT = 3;
  }
  MobRegFormat mob_reg_format = 10;

  // version supported
  repeated Version version_supported = 11;

  // Last Modified date of the account provider information in the UPI system
  google.protobuf.Timestamp last_modified_ts = 12;
}

message ListAccountProviderResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Indicates that request failed
    FAILED = 100;
  }
  // Status of the request
  rpc.Status status = 1;

  // Account Providers List
  repeated AccountPvd account_pvd_list = 2;

  // raw response code sent by the vendors
  string raw_response_code = 3;

  // raw status description send by the vendors
  string raw_status_description = 4;
}

// Request struct for RespTxnConfirmation
message RespTxnConfirmationRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;
  // set of common response attribute shared across all UPI response interfaces.
  vendorgateway.openbanking.upi.ResponseHeader resp = 3;
  // represents UPI version to be followed for the request
  // In case UPI version is unspecified V2.0 is chosen as default
  .upi.UpiVersion upi_version = 4;
}

// Response struct for RespTxnConfirmation.
message RespTxnConfirmationResponse {
  enum Status {
    OK = 0;
  }
  // Request acknowledgement status
  rpc.Status status = 1;
  // unique identifier for each request
  string req_msg_id = 2;
  // raw status code as sent by the endor
  string raw_status_code = 3;
  // description for the raw status code
  string raw_status_description = 4;
  // epifi status code corresponding to the raw status code
  string status_code = 5;
  // description of the status code for the payer
  string status_description_payer = 6;
  // description of the status code for payee
  string status_description_payee = 7;
}

// Request object for creating virtualId
// Todo(raunak) Confirm request details after more clarification from federal
message CreateVirtualIdRequest {
  // common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // unique and Random id for each request
  string txn_id = 2;

  // customer Mobile number
  string mobile_number = 3;

  // customer virtual  id
  string virtual_id = 4;

  // customer account number
  string account_number = 5;

  // IFSC code
  string ifsc = 6;

  // customer MMID
  string MMID = 7;

  // customer name
  string customer_name = 8;

  // customer account type
  accounts.Type account_type = 9;

  // value should be 'Y' or 'N'
  string default_account_flag = 10;

  // customer bank name
  string customer_bank_name = 11;

  string d_length = 12;

  // Bank will provide this field value
  string MCC = 13;

  // customer mobile operator
  string mobile_operator = 14;

  // Value is the encrypted actual customer account number
  string account_reference_number = 15;

  enum OperationType {
    // unspecified
    OPERATION_TYPE_UNSPECIFIED = 0;
    // creating virtual Id for the first time
    CREATE_VIRTUAL_ID = 1;
    // update the virtual id
    // DEPRECATED - In favour of MODIFY_ACCOUNT
    UPDATE_VIRTUAL_ID = 2 [deprecated = true];
    // delink upi account
    DELINK_ACCOUNT = 3;
    // disable the virtual id
    DISABLE_VIRTUAL_ID = 4;
    // update the virtual id of the account
    MODIFY_ACCOUNT = 5;
    // add account to an existing virtual id
    ADD_ACCOUNT = 6;
  }

  // operation type of the request
  OperationType operation_type = 16;

  // Account Product Offering associated with the AccountType.
  // This has to be used in combination with AccountType to derive the correct AccType to send to vendor.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE together will derive a unique AccType to send to vendor.
  api.typesv2.account.AccountProductOffering apo = 17;
}

// Response message for create virtual Id
message CreateVirtualIdResponse {
  enum Status {
    // Success
    OK = 0;
    // InvalidArgument indicates client specified an invalid argument
    INVALID_ARGUMENT = 3;
    // VPA Already exists
    ALREADY_EXISTS = 6;
    // Internal error
    INTERNAL = 13;
    // Sender details are invalid
    UNAUTHENTICATED = 16;
    // account does not exist/ account already have vId
    INVALID_ACCOUNT = 101;
    // validation error at partner bank
    VALIDATION_ERROR = 102;
    // EXTERNAL_ERROR_AT_VENDOR
    VENDOR_EXTERNAL_ERROR = 120;
  }
  rpc.Status status = 1;

  // unique and Random id for each request
  string txn_id = 2;

  // customer virtual  id
  string virtual_id = 3;

  // customer mobile number
  string mobile_number = 4;

  // raw response code sent by the vendors
  string raw_response_code = 5;

  // raw status description send by the vendors
  string raw_status_description = 6;
}

message RespValidateAddressRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  message Resp {
    string req_msg_id = 1;
    // result of the request - SUCCESS/FAILURE
    string result = 2;
    // mask name of the user to which the VPA belongs
    string maskName = 3;
    // error code for the request
    string errCode = 4;
    // code for the request
    // mandatory in case of success
    string code = 5;
    // customer account type
    accounts.Type account_type = 6;

    // customer type
    .upi.CustomerType type = 7;
    // ifsc code for the customer account
    string IFSC = 8;

    // Merchant details
    .upi.MerchantDetails merchant_details = 9;

    // type of vpa. For eg, 'UPIMANDATE' is returned if the vpa is for a mandate
    PType p_type = 10;

    message FeatureSupported {
      // value 01 for mandate
      string value = 1;
    }

    // specifies features that are supported
    FeatureSupported feature_supported = 11;

    // Account Product Offering associated with the AccountType.
    // This has to be used in combination with AccountType to derive the correct AccType to send to vendor.
    //
    // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE together will derive a unique AccType to send to vendor.
    api.typesv2.account.AccountProductOffering apo = 12;
  }

  Resp resp = 3;

  // represents UPI version to be followed for the request
  // In case UPI version is unspecified V2.0 is chosen as default
  .upi.UpiVersion upi_version = 4;

  // epifi user's vpa that has initiated or is involved in the transaction
  // will be used to determine the orgId to be used
  string epifi_customer_vpa = 5;
}

// Specifies the features that are either supported or restricted.
// Example 1: A value of "O1" indicates that mandates are supported.
// Example 2: For merchants, a value of "05" signifies that the merchant is restricted from receiving payments via Rupay Credit Card.
// The format of this field consists of two-character codes representing specific features, separated by a |.
// If no features are supported, this field will be nil rather than empty.
// Reference: https://drive.google.com/drive/folders/1WsUVRBTCxCu55ktPbGpKqYx0q25eLUzk
message FeatureSupported {
  // value 01 for mandate
  // value O5 for merchant that are restricted from receiving payments via Rupay Credit Card.
  string value = 1;
}

// Response struct for RespValidateAddress.
message RespValidateAddressResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Request acknowledgement status
  rpc.Status status = 1;
  // unique identifier for each request
  string req_msg_id = 2;
}

message ReqCheckTxnStatusRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headerss across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  enum CheckTxnSubType {
    // unspecified
    CHECK_TXN_SUBTYPE_UNSPECIFIED = 0;
    // type PAY
    PAY = 1;
    // type COLLECT
    COLLECT = 2;
    // type mandate
    MANDATE = 3;
  }
  // sub type for the CheckTxn Request eg. PAY, COLLECT
  CheckTxnSubType sub_type = 3;

  // orgId for the transaction
  string org_txn_id = 4;

  // transaction date
  google.protobuf.Timestamp orgTxnDate = 5;

  // epifi user's vpa that has initiated or is involved in the transaction
  // will be used to determine the orgId to be used
  string epifi_customer_vpa = 6;
  // Optional field added to track UPI API usage on behalf of whom(actor) the API is called
  string actor_id = 7;
}

message ReqCheckTxnStatusResponse {
  enum Status {
    // transaction was successful
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    // Partner bank will send update once it is aware of the state.
    //
    // The state can also be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller should re-enquire after sometime, to resolve the ambiguity
    UNKNOWN = 2;

    // no transaction found at vendor for the given txnID
    RECORD_NOT_FOUND = 5;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // transaction still getting processed
    IN_PROGRESS = 51;

    // transaction in deemed state
    DEEMED = 100;

    // payment processing has failed at vendor's end
    // this is usually returned as per the vendor status code mapping returned
    FAILED = 101 [deprecated = true];

    // payment failed due to business failure
    // which occur mostly due to wrong information or inputs provided by the user.
    // for eg. wrong MPIN entered by user
    BUSINESS_FAILURE = 102;

    // payment failed due to technical failure
    // for eg. some processing error at vendor's
    // end due to system outage
    TECHNICAL_FAILURE = 103;
    // error occurred due to psp timeout at vendor side.
    PSP_TIMEOUT = 112;

    // in case of upi transaction for urn (intent/qr) based payments the user
    // might be trying to add money from same account,which is not allowed
    // so we get the same ifsc and account for payer and payee
    PAYER_PAYEE_IFSC_MATCH = 104;

    // in case of upi transaction for urn (intent/qr) based payments some of the third party
    // apps are allowing amount update,in this case we will get amount/transaction id mismatch
    // since our system has the old amount
    AMOUNT_OR_TRANSACTION_ID_MISMATCH = 105;

    // debit timed out at vendor
    DEBIT_TIMED_OUT = 106;

    // transaction limit reached for remitter while adding funds
    TRANSACTION_LIMIT_REACHED_BY_REMITTER = 107;

    // EXTERNAL_ERROR_AT_VENDOR
    VENDOR_EXTERNAL_ERROR = 120;

    // For some cases calling ReqCheckTxnStatus is not allowed. This could be due to below reasons(not limited to):
    // 1. ReqCheckTxnStatus is not allowed for transaction older than x days.
    //    For older than x days, manual enquiry need to be done with vendor. ( For NPCI x = 90 days)
    ENQUIRY_NOT_ALLOWED = 121;
  }
  // Request acknowledgement status
  rpc.Status status = 1;

  // reference received in checkTxn
  // contains the payer/payee details with the status code for transaction
  message Ref {
    enum RefType {
      // unspecified
      REF_TYPE_UNSPECIFIED = 0;
      // PAYER
      PAYER = 1;
      // PAYEE
      PAYEE = 2;
    }
    // type of ref eg, Payer, Payee
    RefType type = 1;

    // sequence number
    string seq_num = 2;

    // VPA of the Payer/Payee
    string vpa = 3;
    // settlement amount
    string sett_amount = 4;

    // original amount
    string org_amount = 5;

    // settlement currency
    string sett_currency = 6;

    // TODO(raunak) description?
    string approval_num = 7;

    // account details of payer/payee
    .upi.CustomerAccountDetails account_details = 8;
    // response code
    string respCode = 9;

    // reversal response code
    string reversal_resp_code = 10;

    // registered name of the customer
    string name = 11;
  }
  //  list of reference received in checkTxn
  repeated Ref ref = 4;

  // Transaction's customer reference number.
  // In case of UPI it is a 12-digit unique ID generated by UPI switch.
  // A customer gets an custRefId on initiating a transaction and use in case of any query regarding his txn.
  // custRefId will be same across all the parties of a transaction similar to transaction id.
  // It's just a more readable proxy to identify a transaction for a customer.
  // Only difference being CustRef is unique w.r.t a customer while TxnID is unique across whole NPCI system.
  string cust_ref_id = 5;
  // raw status code as sent by the
  string raw_status_code = 6;
  // description of the raw status code
  string raw_status_description = 7;
  // epifi status code for the payer
  string status_code_payer = 8;
  // description of the status code for payer
  string status_description_payer = 9;
  // status code for the payee
  string status_code_payee = 10;
  // description of the status code for payee
  string status_description_payee = 11;
  // original transaction execution time
  google.protobuf.Timestamp original_transaction_date = 12;
  // transaction remarks
  string remarks = 13;
}

message ListVaeRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Common txn attributes that contain data like unique identifier for txn
  // customer_ref, notes regarding txn
  // Transaction here means an API call to NPCI
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;
  // Default page size is 1000. Size could range from 1000-10000
  int32 page_size = 3 [(validate.rules).int32 = {gte: 1, lt: 10001}];
}

message ListVaeResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message BalanceEnquiryRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // Details of the payer.
  .upi.Customer payer = 3;
}

message BalanceEnquiryResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Incorrect UPI pin
    INVALID_SECURE_PIN = 100;
    // Secure pin validation retries exhausted
    PIN_RETRIES_EXCEEDED = 101;
    // error occurred due to psp timeout at vendor side.
    PSP_TIMEOUT = 112;
    // mpin not set by User
    MPIN_NOT_SET_BY_USER = 113;
    // EXTERNAL_ERROR_AT_VENDOR
    VENDOR_EXTERNAL_ERROR = 120;
    // B3- TRANSACTION_NOT_PERMITTED_TO_ACCOUNT
    TRANSACTION_NOT_PERMITTED_TO_ACCOUNT = 114;
  }
  rpc.Status status = 1;
  // The balance available in the account for transacting
  // In a normal scenario, account balance and current balance are the same.
  //
  // But, sometimes banks tend to put certain chunk of money from an account
  // on hold and is not available for a normal transaction.
  // This can be a result of user initiated mandate or for any
  // upcoming standing instructions.
  google.type.Money available_balance = 2;
  // The balance available in an account as per the account ledger.
  // In a normal scenario, account balance and current balance are the same.
  //
  // But, sometimes banks tend to put certain chunk of money from an account
  // on hold and is not available for a normal transaction.
  // This can be a result of user initiated mandate or for any
  // upcoming stand instructions.
  //
  // The field is populated only in case current balance and ledger
  // balance differs.
  google.type.Money ledger_balance = 3;

  // raw status code as sent by the vendor
  string raw_status_code = 4;

  // vendor_status encapsulates the vendor's status code and message returned from vendor api.
  vendorgateway.VendorStatus vendor_status = 5;
}

enum PType {
  P_TYPE_UNSPECIFIED = 0;
  UPI_MANDATE = 1;
}

message ReqCheckComplaintStatusRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headerss across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  enum CheckComplaintTxnSubType {
    // unspecified
    CHECK_COMPLAINT_TXN_SUBTYPE_UNSPECIFIED = 0;
    // enum type for checking dispute status on a transaction
    DISPUTE = 1;
  }
  // sub type for the CheckTxn Request eg. PAY, COLLECT
  CheckComplaintTxnSubType sub_type = 3;

  // orgId for the transaction
  string org_txn_id = 4;

  // transaction date
  google.protobuf.Timestamp org_txn_date = 5;

  // epifi user's vpa that has initiated or is involved in the transaction
  // will be used to determine the orgId to be used
  string epifi_customer_vpa = 6;
}

message ReqCheckComplaintStatusResponse {
  enum Status {
    // transaction was successful
    OK = 0;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // transaction still getting processed
    IN_PROGRESS = 51;
  }
  // Request acknowledgement status
  rpc.Status status = 1;

  //  list of reference received in checkTxn for payer and payee with complaint response
  repeated .upi.txnref.TransactionReference txn_ref = 2;

  // Info regarding the state of the raised dispute
  vendorgateway.openbanking.upi.ComplaintDisputeState complaint_dispute_state = 3;
}

message RaiseComplaintRequest {
  // common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // user provided complain with action and reason.
  // based on this corresponding code for action and flag will be sent vendor
  .upi.complaint.Complaint complaint = 3;

  // epifi user's vpa that has initiated or is involved in the transaction
  // will be used to determine the orgId to be used
  string epifi_customer_vpa = 4;

  // date when the transaction was initiated
  google.protobuf.Timestamp org_txn_date = 5;
}

message RaiseComplaintResponse {
  enum Status {
    // transaction was successful
    OK = 0;

    // no transaction found at vendor for the given txnID
    RECORD_NOT_FOUND = 5;

    // System faced internal errors while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // unique complaint reference number issued from vendor.
  // This can be used to follow up with NPCI on all channels.
  string complaint_ref_no = 2;

  // list of reference received for payer in complaint response.
  repeated .upi.txnref.TransactionReference ref = 3;
}

message ReqActivationRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // type of reqActivation, Currently we are having "International" and "Upi Services" type.
  // type "Upi Services" is for future use
  enum ReqActivationType {
    REQ_ACTIVATION_TYPE_UNSPECIFIED = 0;
    INTERNATIONAL = 1;
    UPI_SERVICES = 2;
  }
  ReqActivationType type = 3;

  // reqActivation api can take action related to Activation, Updation, deactivation & Query.
  enum ReqActivationAction {
    REQ_ACTIVATION_ACTION_UNSPECIFIED = 0;
    ACTIVATION = 1;
    UPDATE = 2;
    DEACTIVATION = 3;
    QUERY = 4;
  }
  ReqActivationAction action = 4;

  // TODO(Lucky to add description for valStart)
  string valStart = 5;

  // TODO(Lucky to add description for valEnd)
  string valEnd = 6;

  // Details of the payer.
  .upi.Customer payer = 7;

  // code related to payer
  string code = 8;
}

message ReqActivationResponse {
  enum Status {
    // activation request has been accepted successfully
    // the caller will be updated about the activation status
    // via callback from partner bank.
    // Further the caller can do enquiry call to check the final status
    // of the activation
    OK = 0;

    // Partner bank is unaware of the activation status and hence marked as a SUSPECT.
    // Partner bank will send update once it is aware of the state
    //
    // The state can also be returned in case epiFi gets some unexpected
    // response code from partner bank.
    UNKNOWN = 2;

    // activation initiation has failed
    // this is usually returned when there is a validation failure returned in the ack
    FAILED = 100;
  }
  // Request acknowledgement status
  rpc.Status status = 1;

  // unique identifier for each request
  string req_msg_id = 2;

  // raw status code as sent by the vendor
  string raw_status_code = 3;
  // description for the raw status code
  string raw_status_description = 4;
}

message ListPspRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Common txn attributes that contain data like unique identifier for txn
  // customer_ref, notes regarding txn
  // Transact...ion here means an API call to NPCI
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;
}

// Details related to registered PSP
message Psp {
  // Name of the account provider
  string name = 1;
  // Codes defined for the PSP
  repeated string codes = 2;
  // status of the account provider if it is active or not
  bool active = 3;
  // url link provided by account provider
  string url = 4;
  // name of the SPOC
  string spoc_name = 5;
  // email of the SPOC
  string spoc_email = 6;
  // phone of the SPOC
  string spoc_phone = 7;
  // Last Modified date of the PSP information in the UPI system
  google.protobuf.Timestamp last_modified_ts = 8;
  // version supported
  repeated Version version_supported = 9;
}

message ListPspResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Indicates that request failed
    FAILED = 100;
  }
  // Status of the request
  rpc.Status status = 1;

  // PSP List
  repeated Psp psp_list = 2;
}

message RegMapperRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // Details of the payer.
  .upi.Customer payer = 3;

  enum ReqType {
    // unspecified
    REQ_TYPE_STATUS = 0;
    // type for registering upi number
    REQ_TYPE_CMREGISTRATION = 1;
  }

  // type of the request
  ReqType type = 4;

  enum OpType {
    // unspecified
    OP_TYPE_UNSPECIFIED = 0;
    // Create Numeric ID / Mobile Number as UPI Number
    OP_TYPE_ADD = 1;
    // Modify an already existing Upi Number
    OP_TYPE_MODIFY = 2;
  }
  // Operation to be performed by the request
  OpType op = 5;
}

message RegMapperResponse {
  enum Status {
    // transaction was successful
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // validation check while processing the request failed
    VALIDATION_ERROR = 17;
    // mapping exists with the vendor/NPCI
    MAPPING_EXISTS = 18;
    // mapping doesnot exists with the vendor/NPCI
    MAPPING_DOES_NOT_EXISTS = 19;
    // mapping blocked
    MAPPING_BLOCKED = 20;
    // mapping inactive
    MAPPING_INACTIVE = 21;
    // vpa belongs to another mobile
    VPA_MAPPED_TO_ANOTHER_MOBILE = 22;
    // vpa belongs to diff org
    VPA_OF_DIFFERENT_ORG = 23;
    // mapping activation not allowed
    MAPPING_ACTIVATION_NOT_ALLOWED = 24;
    // pre vpa not same as cache
    PREVVPA_NOT_SAME_AS_CACHE = 25;
    // cannot activate deregistered mobile
    CANNOT_ACTIVATE_DEREGISTERED_MOBILE = 26;
    // cooling period not over
    COOLING_PERIOD_NOT_OVER = 27;
    // txn subtype id no regIdDetails present
    TXN_SUBTYPE_ID_NO_REGIDDETAILS_PRESENT = 28;
    // proper txn subtype not present
    PROPER_TXN_SUBTYPE_NOT_PRESENT = 29;
    // proper txn type not present
    PROPER_TXN_TYPE_NOT_PRESENT = 30;
    // mapping against vpa absent
    MAPPING_AGAINST_VPA_ABSENT = 31;
    // cannot port numeric id
    CANNOT_PORT_NUMERIC_ID = 32;
    // id mapped to different vpa
    ID_MAPPED_TO_DIFFERENT_VPA = 33;
  }

  rpc.Status status = 1;
  // raw status code sent by the vendor
  string raw_status_code = 2;
  // raw status description send by the vendor
  string raw_status_description = 3;
  // details for the registered upi number
  .upi.RegIdDetails reg_id_details = 4;
}

message GetMapperInfoRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // Details of the payer.
  .upi.Customer payer = 3;

  enum ReqType {
    // unspecified
    REQ_TYPE_UNSPECIFIED = 0;
    // type to fetch the last updated status of the UPI Number
    REQ_TYPE_CHECK = 1;
    // type to fetch info for a vpa or UPI number based on request sub type
    REQ_TYPE_FETCH = 2;
    // type to use while transferring mobile number from one PSP to Other PSP
    REQ_TYPE_PORT = 3;

  }

  enum ReqSubType {
    // unspecified
    REQ_SUB_TYPE_UNSPECIFIED = 0;
    // type for fetching details for given numeric id
    REQ_SUB_TYPE_ID = 1;
    // type for fetching details for given vpa
    REQ_SUB_TYPE_VPA = 2;
  }

  // type of the request
  ReqType type = 4;
  // sub type of the request
  ReqSubType sub_type = 5;
}

message GetMapperInfoResponse {
  enum Status {
    // transaction was successful
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // validation check while processing the request failed
    VALIDATION_ERROR = 17;
    // mapping exists with the vendor/NPCI
    MAPPING_EXISTS = 18;
    // mapping doesnot exists with the vendor/NPCI
    MAPPING_DOES_NOT_EXISTS = 19;
    // mapping blocked
    MAPPING_BLOCKED = 20;
    // mapping inactive
    MAPPING_INACTIVE = 21;
    // vpa belongs to diff psp
    VPA_OF_DIFFERENT_ORG = 22;
    // type not supported
    TYPE_NOT_SUPPORTED_FOR_GETMAPPERINFO = 23;
    // RegIdDetails are compulsary in case of subtype = ID
    TXN_SUBTYPE_ID_NO_REGIDDETAILS_PRESENT = 24;
    // Proper txn subtype not present
    PROPER_TXN_SUBTYPE_NOT_PRESENT = 25;
    // Proper txn type not present
    PROPER_TXN_TYPE_NOT_PRESENT = 26;
    // check action not permitted
    CHECK_ACTION_NOT_PERMITTED = 27;
    // mapping against vpa absent
    MAPPING_AGAINST_VPA_ABSENT = 28;
    // id mapped to different vpa
    ID_MAPPED_TO_DIFFERENT_VPA = 29;
  }

  rpc.Status status = 1;
  // raw status code sent by the vendor
  string raw_status_code = 2;
  // raw status description send by the vendor
  string raw_status_description = 3;
  // details for the registered upi number
  repeated .upi.RegIdDetails reg_id_details = 4;
}

// Request struct for RespMapperConfirmation
message RespMapperConfirmationRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;
  // set of common response attribute shared across all UPI response interfaces.
  vendorgateway.openbanking.upi.ResponseHeader resp = 3;
  // represents UPI version to be followed for the request
  // In case UPI version is unspecified V2.0 is chosen as default
  .upi.UpiVersion upi_version = 4;

  enum ReqType {
    // unspecified
    REQ_TYPE_UNSPECIFIED = 0;
    // type for registering upi number
    REQ_TYPE_CMREGISTRATION = 1;
  }

  // type of the request
  ReqType type = 5;
}

// Response struct for RespMapperConfirmation.
message RespMapperConfirmationResponse {
  enum Status {
    // success
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Request acknowledgement status
  rpc.Status status = 1;
  // unique identifier for each request
  string req_msg_id = 2;
  // raw status code as sent by the endor
  string raw_status_code = 3;
  // description for the raw status code
  string raw_status_description = 4;
  // epifi status code corresponding to the raw status code
  string status_code = 5;
}


message ActivateInternationalPaymentsRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // Details of the payer.
  .upi.Customer payer = 3;

  // type of request initiated with vendor
  enum ReqType {
    REQ_TYPE_UNSPECIFIED = 0;
    REQ_TYPE_INTERNATIONAL = 1;
    REQ_TYPE_UPI_SERVICES = 2;
  }
  // type of the request
  ReqType req_type = 4;

  enum ActionType {
    // unspecified
    ACTION_TYPE_UNSPECIFIED = 0;
    // activation of international payments
    ACTION_TYPE_ACTIVATION = 1;
    // update some details
    ACTION_TYPE_UPDATE = 2;
    // deactivation of international payments
    ACTION_TYPE_DEACTIVATION = 3;
    // query the status of account activation for international payments
    ACTION_TYPE_QUERY = 4;
  }

  // type of action to be performed
  ActionType action_type = 5;

  // start time for activation of international payments
  google.protobuf.Timestamp val_start = 6;

  // expiry time for the international payments for the user
  google.protobuf.Timestamp val_end = 7;
}

message ActivateInternationalPaymentsResponse {
  enum Status {
    // success
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // raw status code as sent by the vendor
  string raw_status_code = 2;

}

message ValidateInternationalQrRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // Details of the payer.
  .upi.Customer payer = 3;

  enum ReqType {
    REQ_TYPE_UNSPECIFIED = 0;
    REQ_TYPE_INTERNATIONAL_QR = 1;
  }
  // type of the request
  ReqType req_type = 4;

}

message ValidateInternationalQrResponse {
  enum Status {
    // success
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // raw status code as sent by the vendor
  string raw_status_code = 2;

  // unique identifier for each request
  string req_msg_id = 3;

  // Customer Details
  .upi.Customer customer_details = 4;

  // qr details for each transaction
  .upi.QRDetails qr_details = 5;
}

message GetUpiLiteRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headers across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // cred block to be passed to vendor in request payload
  // in order to trigger upi lite creation for the
  // upi account
  repeated .upi.CredBlock cred_block = 3;

  // customer for which upi lite account will be activated
  .upi.Customer payer = 4;
}

message GetUpiLiteResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;

  // lrn (Lite Reference Number) -
  // - to be used to store
  //   as reference number while creating upi lite account
  // - lrn will be used for all future actions on upi lite
  //   account
  string lrn = 2;

  // key_xml payload - client needs to share this xml payload
  // with CL, after successful activation
  // of LRN (Lite Reference Number)
  string key_xml_payload = 3;
}

message SyncUpiLiteInfoRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Set of common txn headerss across UPI APIs
  vendorgateway.openbanking.upi.TransactionHeader txn_header = 2;

  // orgId for the transaction
  string org_txn_id = 3;

  // transaction date
  google.protobuf.Timestamp orgTxnDate = 4;

  // lite reference number unique for every lite account.
  // Its a combination of various parameter (device id, upi id, app id, phone number, account number and ifsc)
  // and its unique for both PSP and vendor
  string lrn = 5;
}

message SyncUpiLiteInfoResponse {
  enum Status {
    // transaction was successful
    OK = 0;

    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    // Partner bank will send update once it is aware of the state.
    //
    // The state can also be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller should re-enquire after sometime, to resolve the ambiguity
    UNKNOWN = 2;

    // no transaction found at vendor for the given txnID
    RECORD_NOT_FOUND = 5;

    // System faced internal errors while processing the request
    INTERNAL = 13;


    // payment processing has failed at vendor's end
    // this is usually returned as per the vendor status code mapping returned
    FAILED = 101 [deprecated = true];

    // payment failed due to technical failure
    // for eg. some processing error at vendor's
    // end due to system outage
    TECHNICAL_FAILURE = 103;
  }
  // Request acknowledgement status
  rpc.Status status = 1;

  // reference received in checkTxn
  // contains the payer/payee details with the status code for transaction
  message Ref {
    enum RefType {
      // unspecified
      REF_TYPE_UNSPECIFIED = 0;
      // PAYER
      PAYER = 1;
      // PAYEE
      PAYEE = 2;
    }
    // type of ref eg, Payer, Payee
    RefType type = 1;

    // sequence number
    string seq_num = 2;

    // VPA of the Payer/Payee
    string vpa = 3;
    // settlement amount
    string sett_amount = 4;

    // original amount
    string org_amount = 5;

    // settlement currency
    string sett_currency = 6;

    // TODO(raunak) description?
    string approval_num = 7;

    // account details of payer/payee
    .upi.CustomerAccountDetails account_details = 8;
    // response code
    string respCode = 9;

    // reversal response code
    string reversal_resp_code = 10;

    // registered name of the customer
    string name = 11;
  }
  //  list of reference received in checkTxn
  repeated Ref ref = 4;

  // Transaction's customer reference number.
  // In case of UPI it is a 12-digit unique ID generated by UPI switch.
  // A customer gets an custRefId on initiating a transaction and use in case of any query regarding his txn.
  // custRefId will be same across all the parties of a transaction similar to transaction id.
  // It's just a more readable proxy to identify a transaction for a customer.
  // Only difference being CustRef is unique w.r.t a customer while TxnID is unique across whole NPCI system.
  string cust_ref_id = 5;
  // raw status code as sent by the
  string raw_status_code = 6;
  // description of the raw status code
  string raw_status_description = 7;
  // epifi status code for the payer
  string status_code_payer = 8;
  // description of the status code for payer
  string status_description_payer = 9;
  // status code for the payee
  string status_code_payee = 10;
  // description of the status code for payee
  string status_description_payee = 11;
  // original transaction execution time
  google.protobuf.Timestamp original_transaction_date = 12;
  // transaction remarks
  string remarks = 13;
  // lite reference number unique for every lite account.
  // Its a combination of various parameter (device id, upi id, app id, phone number, account number and ifsc)
  // and its unique for both PSP and vendor
  string lrn = 14;
  // sync data in case of upi lite
  string syncData = 15;
}
