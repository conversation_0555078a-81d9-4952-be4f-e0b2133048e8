// protolint:disable MAX_LINE_LENGTH

// Vendor gateway APIs for savings account management with a partner bank.
// This includes operations like create, update, and other operations.

syntax = "proto3";

package vendorgateway.openbanking.savings;

import "api/rpc/status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/nominee.proto";
import "api/typesv2/source_of_funds.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/openbanking/header/auth.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vendor_status.proto";
import "google/protobuf/timestamp.proto";
import "api/typesv2/user.proto";
import "api/user/profile.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/savings";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.savings";

// Savings service provides various API's to operate on a savings account.
service Savings {
  // Initiates creation of the account for the given customer(s). This is an async API.
  rpc CreateAccount (CreateAccountRequest) returns (CreateAccountResponse);

  // API to check the status of account creation. Returns account number.
  rpc CheckAccountStatus (CheckAccountStatusRequest) returns (CheckAccountStatusResponse);

  // Enquires the latest known balance of an account
  rpc GetBalance (GetBalanceRequest) returns (GetBalanceResponse);

  // RPC to enquire opening balance for an account
  // It returns the opening balance corresponding to the account number on a given date starting from 00:00:00+5:30
  rpc GetOpeningBalance (GetOpeningBalanceRequest) returns (GetOpeningBalanceResponse);

  // RPC to enquire balance, the response received is stale compared to GetBalance.
  rpc GetBalanceV1 (GetBalanceV1Request) returns (GetBalanceV1Response);

  // updating the exisiting nominee details
  rpc UpdateNominees (UpdateNomineeRequest) returns (UpdateNomineeResponse);
}

// KYC done by user can either be min kyc or full kyc. This enum should be maintain in sync with kyc.KYCLevel
enum KYCLevel {
  UNSPECIFIED = 0;
  // Various conditions lead to a customer becoming min kyc customer:
  // 1. Dedupe customer, with partial kyc and goes through ekyc
  // 2. CKYC fails due to some reason, and customer taken through ekyc
  MIN_KYC = 1;
  // Under the following conditions, a customer can become a full kyc customer:
  // 1. Dedupe customer with full kyc done
  // 2. Dedupe customer, with partial kyc and goes through ckyc successfully
  FULL_KYC = 2;
}

// Request message for initiation of creation of a savings account.
message CreateAccountRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Unique request ID generated by epiFi.
  string request_id = 2;

  // Customer ID(s) of the operators of this bank account.
  repeated string bank_customer_id = 3;
  // device details of the customer
  header.Auth device_details = 4;
  string customer_name = 5;
  string account_no = 6;
  string mobile_no = 7;
  string email_id = 8;
  api.typesv2.Nominee nominee = 9;
  // account type - min account/ full account depends on kyc level
  // vendor_sku is directly used instead of evaluation through kyc level
  KYCLevel kyc_level = 10 [deprecated = true];

  // product type of savings account to be created.
  string vendor_sku = 11;

  // Sol ID with which the account is supposed to be created.
  string sol_id = 12;

  // Source of funds for savings account
  api.typesv2.SourceOfFunds source_of_funds = 13;
  // if account will be used to receive any scholarship
  bool scholarship_flag = 14;
  // if account will be used for Direct Benefit Transfer
  bool dbt_flag = 15;
  user.SalaryRange annual_txn_volume = 16;
  api.typesv2.PurposeOfSavingsAccount purpose_of_savings_account = 17;
}

// Response message for CreateAccount. Returns either success or failure for
// initiation of creation of the account the bank's end. The
// actual account details need to be fetched in the check status call.
message CreateAccountResponse {
  enum Status {
    OK = 0;
    STATUS_UNKNOWN = 100;
    INVALID_SENDER_DETAILS = 102;
    SENDER_NOT_ENABLED = 103;
    DUPLICATE_REQUESTID = 104;
    NO_REQUESTID = 105;
    APPLICATION_ERROR = 106;
    INVALID_DEVICE = 107;
    INVALID_TRAN = 108;
    SB_ACCOUNT_IN_PROCESS = 109;
    DEVICE_NOT_LINKED = 110;
    INVALID_DATA = 111;
    INVALID_INPUT = 112;
  }
  rpc.Status status = 1;
  vendorgateway.VendorStatus vendor_status = 2;
}

// Request message for checking account creation status.
message CheckAccountStatusRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // The same request ID which was passed in the CreateAccount API.
  string request_id = 2;

  // device params
  header.Auth device_details = 3;

  // phone number
  api.typesv2.common.PhoneNumber phone_number = 4;
}

// Response message for checking account status. Returns the
// account number of the created account.
message CheckAccountStatusResponse {
  // Account number of the savings account that was created. This is string to
  // accommodate leading zeros which sometimes exist in account numbers.
  string account_number = 1;

  enum Status {
    OK = 0;
    STATUS_UNKNOWN = 100;
    DUPLICATE_REQUESTID = 102;
    NO_REQUESTID = 103;
    INVALID_SENDER_DETAILS = 104;
    SENDER_NOT_ENABLED = 105;
    INSERTION_EXCEPTION = 106;
    EXCEPTION = 107;
    FIELD_EXCEPTION = 108;
    SB_ACCOUNT_IN_PROCESS = 109;
    INVALID_DATA = 110;
    SOCKET_EXCEPTION = 111;
    // If the initial customer creation request did not reach vendor we get details not found.
    // We can retry customer creation with the same requestId
    DETAILS_NOT_FOUND = 112;
    // federal ResponseAction":"FAILURE","ResponseCode":"OBE0059"
    INVALID_DEVICE_TOKEN = 113;
    // federal ResponseAction":"FAILURE","ResponseCode":"SB_012"
    SAVINGS_ACCOUNT_FAILURE = 114;
  }
  rpc.Status status = 2;

  // time at which account creation was successful at vendor's end
  google.protobuf.Timestamp created_at = 3;
  vendorgateway.VendorStatus vendor_status = 4;
}


// Request for checking balance of a savings account.
message GetBalanceRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // registered device info of user/customer
  header.Auth auth = 2;

  // Unique request ID for this request generated by epiFi.
  string request_id = 3;

  // Account number of the bank account whose balance needs to
  // be fetched.
  string account_number = 4;

  // phone number of the user
  api.typesv2.common.PhoneNumber phone_number = 5;
}

// Response message for check balance. Returns the current balance amount, the ledger balance
// which is the opening balance available on the given day along with he currency code and account status
message GetBalanceResponse {
  enum Status {
    OK = 0;

    // Device Temporarily Deactivated by bank Code: OBE0170
    DEVICE_TEMPORARILY_DEACTIVATED = 100;
  }
  rpc.Status status = 1;

  // Available Balance of the account as of balance_at timestamp
  google.type.Money available_balance = 2;

  // Ledger balance of the account as of balance_at timestamp
  google.type.Money ledger_balance = 3;

  // Currency of the balance amount
  string currency = 4;

  // Account status of the given account
  string account_status = 5;

  // timestamp corresponding to which balance is returned from the bank
  google.protobuf.Timestamp balance_at = 6;
}

message GetOpeningBalanceRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // account number to identify the account for which opening balance needs
  // to be fetched
  string account_number = 2;

  // date on which opening balance need to be enquired
  google.type.Date date = 3 [deprecated = true];

  // timestamp for which opening balance need to be enquired
  google.protobuf.Timestamp balance_at = 8;

  // id of registered device
  string device_id = 4;

  // token corresponding to the registered device
  string device_token = 5;

  // customer id of the actor to which the account belongs to
  string customer_id = 6;

  // phone number of the account holder
  api.typesv2.common.PhoneNumber phone_number = 7;

  // User group flag for using new closing balance api
  bool is_closing_balance_user_group = 9;
}

message GetOpeningBalanceResponse {
  enum Status {
    OK = 0;

    // for response code "OBE0170" from vendor, "Device is deactivated temporary by bank"
    DEVICE_TEMPORARILY_DEACTIVATED = 101;
  }

  rpc.Status status = 1;

  string account_number = 2;

  string account_name = 3;

  google.type.Money opening_balance = 5;

  // Represents the time when the last closing balance for the account was computed w.r.t to the enquired date.
  // Opening balance for an account for a given date d = closing balance on d-1.
  // So depending on the time when enquiry is triggered to banking servers, the date can help a client
  // understand if bank side EOD has finished by looking at `bank_eod_at` and if it's not finished then closing balance corresponding to what date is returned by the bank.
  // e.g. if we are enquiring about opening balance on a current date say `14-12-2021` the API returns closing balance of `13-12-2021`
  //  but if we enquire opening balance at `14-12-2021T00:01:00+05:30` and bank EOD process has not finished then the api will return closing balance of `12-12-2021`. Since, `13-12-2021` closing isn't processed.
  // In such cases client should re-enquire and wait until bank EOD is finished and closinf balance corresponding to `13-12-2021` is returned
  google.protobuf.Timestamp last_closed_at = 6;

  // This field refers to the last date when the last EOD was performed by the bank
  google.protobuf.Timestamp bank_eod_at = 7;
}

message GetBalanceV1Request {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  string request_id = 2;

  string account_number = 3;
}

message GetBalanceV1Response {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;
  // Name of the customer
  api.typesv2.common.Name customer_name = 2;

  // (Available Balance +  Lien Balance + amount in clearance) = Ledger Balance
  google.type.Money ledger_balance = 3;

  google.type.Money available_balance = 4;

  google.type.Money clearance_balance = 5;

  google.type.Money lien_balance = 6;
  // Timestamp for which the given balance was calculated
  google.protobuf.Timestamp balance_at = 7;
  // There can be freeze conditions with respect to an account, freeze status gives a standard code about it
  string freeze_status = 8;
  // Reason for the freeze status
  string freeze_reason = 9;
}

message UpdateNomineeRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // ADD / ENQUIRY
  string req_type = 2;
  // unique request id for each request to the vendor,
  // will be used to enquire the status
  string req_id = 31;
  // Unique identifier (account numberwith sol id)
  string foracid = 3;
  // Unique request id for each requestapi/vendors/federal/update_nominee.proto
  string service_req_id = 4;
  // ekyc rrn number received from aadhaar ekyc
  string ekyc_crrn = 5;
  // Nominee name
  string nominee_name = 6;
  // Nominee registration number
  string nominee_reg_no = 7;
  // Nominee relation to customer
  string nominee_rel_type = 8;
  // Y/N (whetherNominee is a Minor)
  string nominee_minor_flag = 9;
  // Nominee date of birth
  string nominee_dob = 10;
  // Nominee Addr line 1
  string nominee_addr_line1 = 11;
  // Nominee Addr line 2
  string nominee_addr_line2 = 12;
  // Nominee Addr line 3
  string nominee_addr_line3 = 13;
  // Nominee City Code
  string nominee_city = 14;
  // Nominee state code
  string nominee_state = 15;
  // Nominee country code
  string nominee_country = 16;
  // Nominee Pin Code
  string nominee_postal_code = 17;
  // if Nominee is Minor
  string guardian_code = 18;
  // if Nominee is minor
  string guardian_name = 19;
  // to identify which channel
  string channel = 20;
  string reserve_free_text1 = 21;
  string reserve_free_text2 = 22;
  string reserve_free_text3 = 23;
  string reserve_free_text4 = 24;
  string reserve_free_text5 = 25;
  string reserve_free_text6 = 26;
  string reserve_free_text7 = 27;
  string reserve_free_textt8 = 28;
  string reserve_free_text9 = 29;
  string reserve_free_text10 = 30;
}

message UpdateNomineeResponse {
  rpc.Status status = 4;
  // Request Id which was sent to vendor
  string request_id = 1;
  string cbs_response = 2;
  string cbs_status = 3;
}

