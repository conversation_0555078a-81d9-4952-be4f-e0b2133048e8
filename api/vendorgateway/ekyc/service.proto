// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.ekyc;

import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vendor_status.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/ekyc";
option java_package = "com.github.epifi.gamma.api.vendorgateway.ekyc";

service EKYC {
  // rpc to validate name and dob collected.
  // This is a security check performed at the bank's end. It is required to match the data acquired
  // by eKYC process and data collected as part of Customer on-boarding to be able to create create Customer ID.
  rpc NameDobValidationForEkyc (NameDobValidationForEkycRequest) returns (NameDobValidationForEkycResponse);

  // rpc to check to if the mobile number provided during onboarding is linked to aadhar
  // https://docs.google.com/document/d/1Qpn2FWQU9Qf6Y_iEQjIDiXSVi_TKWn2j/edit
  rpc ValidateAadharMobile (ValidateAadharMobileRequest) returns (ValidateAadharMobileResponse);
}

message NameDobValidationForEkycRequest {
  // contains vendor to be used
  vendorgateway.RequestHeader header = 1;
  // UID Reference Key provided in the EKYC response
  string uid_reference_key = 2;
  // Customer name as per EKYC record
  api.typesv2.common.Name customer_name = 3;
  // date of birth of customer as per EKYC record
  google.type.Date dob = 4;
}

message NameDobValidationForEkycResponse {
  // rpc status
  rpc.Status status = 1;
  // API raw response
  string raw_response = 2;

  string aadhar_last4_digits = 3;
}

message ValidateAadharMobileRequest {
  vendorgateway.RequestHeader header = 1;
  string request_id = 2;
  string ekyc_rrn = 3;
  api.typesv2.common.PhoneNumber mobile_number = 4;
}

message ValidateAadharMobileResponse {
  rpc.Status status = 1;
  api.typesv2.common.BooleanEnum is_aadhar_linked_to_mobile_number = 2;
  vendorgateway.VendorStatus vendor_status = 3;
}
