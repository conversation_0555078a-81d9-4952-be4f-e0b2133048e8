syntax = "proto3";

package vendorgateway.risk;

import "api/auth/internal/auth_factor_update.proto";
import "api/auth/liveness/types.proto";
import "api/employment/employment_data.proto";
import "api/kyc/internal/kyc_attempt.proto";
import "api/kyc/kyc.proto";
import "api/risk/case_management/alert.proto";
import "api/rpc/status.proto";
import "api/screener/service.proto";
import "api/tiering/external/external.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/user.proto";
import "api/user/user.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/risk/enums.proto";
import "api/vendors/inhouse/risk.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/latlng.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/risk";
option java_package = "com.github.epifi.gamma.api.vendorgateway.risk";

service Risk {
  // DetectRisk rpc will be used to measure the riskiness of a user
  // based on various parameters collected during the user's onboarding journey
  rpc DetectRisk (DetectRiskRequest) returns (DetectRiskResponse);

  // DetectReOnboardingRisk will be used to measure the riskiness of a user
  // while performing re onboarding
  rpc DetectReOnboardingRisk (DetectReOnboardingRiskRequest) returns (DetectReOnboardingRiskResponse);

  // DetectLocationRisk returns risk score and risk severity against input location.
  rpc DetectLocationRisk (DetectLocationRiskRequest) returns (DetectLocationRiskResponse);

  // GetBureauIdRiskDetails is used to integrate with Bureau's Alt Data Workflow
  rpc GetBureauIdRiskDetails (GetBureauIdRiskDetailsRequest) returns (GetBureauIdRiskDetailsResponse);

  // GetCasePrioritisationScore is used to get the case prioritisation score
  rpc GetCasePrioritisationScore (GetCasePrioritisationScoreRequest) returns (GetCasePrioritisationScoreResponse);
}

enum Outcome {
  OUTCOME_UNSPECIFIED = 0;
  // Indicates the request has been accepted and the full response is available.
  OUTCOME_ACCEPTED = 1;
  // Indicates the request is still in progress for some services.
  OUTCOME_AWAITING = 2;
  // Indicates the request has been rejected.
  OUTCOME_REJECTED = 3;
}

// AttributesStatus represents the status of a particular attributes service i.e. EmailAttributes, PhoneAttributes, etc.
enum AttributeStatus {
  ATTRIBUTE_STATUS_OK = 0;
  ATTRIBUTE_STATUS_FAILED = 1;
  // NOT_FOUND indicates attributes not found
  ATTRIBUTE_STATUS_NOT_FOUND = 2;
  // NOT_EXECUTED indicates attribute service not executed
  ATTRIBUTE_STATUS_NOT_EXECUTED = 3;
}

message GetCasePrioritisationScoreRequest {
  vendorgateway.RequestHeader header = 1;
  repeated .risk.case_management.AlertWithRuleDetails alert_with_rule_details = 2;
  string actor_id = 3;
  string request_id = 4;
  string model_version = 5;
}

message ModelResponseInfo {
  string name = 1;
  float score = 2;
}

message GetCasePrioritisationScoreResponse {
  rpc.Status status = 1;
  string request_id = 2;

  //depricated, use ModelResponseInfo in place of model_version and score
  string model_version = 3;
  float score = 4;

  string raw_vendor_response = 5;
  repeated ModelResponseInfo model_info = 6;
}

message GetBureauIdRiskDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  string request_id = 2;
  UserData user_data = 3;
}

message UserData {
  string name = 1;
  string email_id = 2;
  api.typesv2.common.PhoneNumber phone_number = 3;
}

message GetBureauIdRiskDetailsResponse {
  rpc.Status status = 1;
  Outcome outcome = 2;
  string request_id = 3;
  EmailAttributes email_attributes = 4;
  EmailNameAttributes email_name_attributes = 5;
  PhoneAttributes phone_attributes = 6;
  PhoneNameAttributes phone_name_attributes = 7;
  PhoneNetworkAttributes phone_network_attributes = 8;
  EmailSocialAttributes email_social_attributes = 9;
  PhoneSocialAttributes phone_social_attributes = 10;
  RiskAttributes risk_attributes = 11;
  string raw_response = 12;
}

message EmailAttributes {
  AttributeStatus attribute_status = 1;
  bool email_exists = 2;
  int32 digital_age = 3;
  bool domain_exists = 4;
  string domain_risk_level = 5;
  string domain_category = 6;
  string domain_corporate = 7;
  int32 unique_hits = 8;
  string email_final_recommendation = 9;
  string last_verification_date = 10;
  string first_verification_date = 11;
}

message EmailNameAttributes {
  AttributeStatus attribute_status = 1;
  string address = 2;
  int32 digital_age = 3;
  int64 phone_number = 4;
  bool first_name_match = 5;
  bool last_name_match = 6;
  bool business_name_detected = 7;
  string email_footprint_strength = 8;
  int32 email_name_digital_age = 9;
  int32 email_spcl_chars = 10;
  // the maximum similarity score between names extracted from an email address and names provided in the request.
  double ndr_score = 11;
  // the ratio of number of names that were unmatched to the total number of names found in the request.
  double unr_score = 12;
  // represents at which extent the name matches with email
  double name_match_score = 13;
  int32 name_email_match = 14;
  bool multiple_phone_attached = 15;
}

message PhoneAttributes {
  AttributeStatus attribute_status = 1;
  string name = 2;
  string source = 3;
  string vpa = 4;
}

message PhoneNameAttributes {
  AttributeStatus attribute_status = 1;
  string address = 2;
  int32 digital_age = 3;
  bool first_name_match = 4;
  bool last_name_match = 5;
  bool business_name_detected = 6;
  double ndr_score = 7;
  double unr_score = 8;
  double name_match_score = 9;
  string footprint_strength_overall = 10;
  int32 phone_name_digital_age = 11;
}

message PhoneNetworkAttributes {
  AttributeStatus attribute_status = 1;
  string imsi = 2;
  string current_network_name = 3;
  string current_network_region = 4;
  string current_network_country_code_iso2 = 5;
  bool is_phone_reachable = 6;
  bool is_valid_phone_number = 7;
  string number_billing_type = 8;
  bool number_has_porting_history = 9;
  string ported_from_network_name = 10;
  string ported_from_network_region = 11;
  string ported_from_network_country_code_iso2 = 12;
  bool roaming = 13;
  string roaming_network_name = 14;
  string roaming_network_region = 15;
  string roaming_network_country_code_iso2 = 16;
}

message PhoneSocialAttributes {
  AttributeStatus attribute_status = 1;
  string amazon = 2;
  string flipkart = 5;
  string housing = 6;
  string indiamart = 7;
  string instagram = 8;
  string isWABusiness = 9;
  string jeevansaathi = 10;
  string jiomart = 11;
  string microsoft = 12;
  string mobile = 13;
  string paytm = 14;
  string shaadi = 15;
  string skype = 16;
  string swiggy = 19;
  string toi = 20;
  string whatsapp = 21;
  string yatra = 22;
  string zoho = 23;
}

message EmailSocialAttributes {
  AttributeStatus attribute_status = 1;
  string amazon = 2;
  string booking = 3;
  string email = 4;
  string flickr = 5;
  string flipkart = 6;
  string housing = 7;
  string instagram = 8;
  string jeevansaathi = 9;
  string microsoft = 10;
  string paytm = 11;
  string pinterest = 12;
  string quora = 13;
  string shaadi = 14;
  string skype = 15;
  string spotify = 16;
  string toi = 17;
  string wordpress = 18;
  string yatra = 19;
  string zoho = 20;
}

message RiskAttributes {
  AttributeStatus attribute_status = 1;
  string address1 = 2;
  string address2 = 3;
  string landmark = 4;
  string area_name = 5;
  string area_pincode = 6;
  string society_name = 7;
  string street_name = 8;
  string city_name = 9;
  string state_name = 10;
  string address_insights = 11;
  string address_risk = 12;
  float address_completeness_score = 13;
  int32 common_email_count = 14;
  bool email_fraud = 15;
  int32 email_fraud_count = 16;
  bool email_fraud_network = 17;
  string email_identity_trust = 18;
  int32 email_social_media_count = 19;
  string identity_confidence = 20;
  string telecom_risk = 21;
  bool is_phone_reachable = 22;
  bool phone_fraud = 23;
  int32 phone_fraud_count = 24;
  bool phone_fraud_network = 25;
  string phone_identity_trust = 26;
  int32 phone_social_media_count = 27;
  repeated string negative_insights = 28;
  repeated string positive_insights = 29;
  int32 upi_phone_name_match = 30;
  double upi_phone_name_match_score = 31;
  double alternate_risk_score = 32;
}

message DetectReOnboardingRiskRequest {
  vendorgateway.RequestHeader header = 1;
  string actor_id = 2;
  string request_id = 3;
  // Actor ID of the user who referred the current user
  string referrer_actor_id = 4;
  // Device of the user
  api.typesv2.common.Device device = 5;
  string email_id = 6;
  api.typesv2.common.BooleanEnum is_credit_report_present = 7;
  api.typesv2.common.BooleanEnum credit_report_download_consent = 8;
  api.typesv2.common.BooleanEnum is_device_premium = 10;
  google.type.Date dob = 11;
  // Name match score between gmail and pan name
  float gmail_pan_name_match_score = 12;
  api.typesv2.common.PhoneNumber phone_number = 13;
  // The count of users which have used the same email in screener
  int32 screener_mail_count = 14;
  google.type.LatLng geolocation = 15;
  // Optional threshold to pass if we want to control the threshold from backend
  // If not passed, then threshold will be controlled from risk service side
  float threshold = 16;
  repeated auth.liveness.LivenessStatus liveness_statuses = 17;
  repeated float liveness_scores = 18;
  repeated string liveness_inhouse_errors = 19;
  repeated float liveness_inhouse_scores = 20;
  repeated float facematch_scores = 21;
  repeated kyc.FailureType ckyc_errors = 22;
  string hashed_phone_number = 23;
  api.typesv2.common.Name father_name = 25;
  api.typesv2.common.Name mother_name = 26;
  api.typesv2.common.BooleanEnum onboarding_ekyc_number_mismatch = 27;
  api.typesv2.common.Name user_pan_name = 28;
  repeated float otp_scores = 29;
  string user_city = 30;
  string user_postal_code = 31;
  kyc.KYCLevel kyc_level = 32;
  auth.afu.OverallStatus overall_afu_status = 33;
  repeated auth.afu.AuthFactor auth_factors = 34;
  auth.afu.FailureReason failure_reason = 35;
  auth.afu.Context context = 36;
  int64 afu_attempt_num = 37;
  repeated float afu_liveness_score = 38;
  repeated float afu_otp_score = 39;
  repeated float afu_facematch_score = 40;
  // state (administrative area) of the address provided by the user in shipping address
  string user_state = 41;
  google.type.LatLng afu_geolocation = 42;
  string onboarding_completed_at = 43;
  repeated vendors.inhouse.CreditReportAttributeInfo cb_details = 44;
  repeated auth.liveness.LivenessStatus afu_liveness_statuses = 45;
  employment.EmploymentData employment_data = 46;
  repeated screener.CheckDetails check_details = 47;
  repeated AFUAttempt afu_attempts = 48;
  AccountInfo account_info = 49;
  user.Profile profile = 50;
  map<string, api.typesv2.PropertyValue> device_property_value_map = 51;
  OnboardingDetails onboarding_details = 52;
  api.typesv2.common.Device old_device = 53;
}

message DetectReOnboardingRiskResponse {
  rpc.Status status = 1;
  float score = 2;
  float threshold = 3;
  // bool flag to denote if the user is risky
  api.typesv2.common.BooleanEnum risky_user = 4;
  // response time of the API in seconds
  google.protobuf.Duration time = 5;
  // list of errors returned by the API
  repeated string error = 6;
  string raw_vendor_response = 7;
}

message DetectRiskRequest {
  vendorgateway.RequestHeader header = 1;
  string actor_id = 2;
  string request_id = 3;
  // Actor ID of the user who referred the current user
  string referrer_actor_id = 4;
  // Device of the user
  api.typesv2.common.Device device = 5;
  string email_id = 6;
  api.typesv2.common.BooleanEnum is_credit_report_present = 7;
  api.typesv2.common.BooleanEnum credit_report_download_consent = 8;
  api.typesv2.common.BooleanEnum is_device_premium = 10;
  google.type.Date dob = 11;
  // Name match score between gmail and pan name
  float gmail_pan_name_match_score = 12;
  api.typesv2.common.PhoneNumber phone_number = 13;
  // The count of users which have used the same email in screener
  int32 screener_mail_count = 14;
  google.type.LatLng geolocation = 15;
  // Optional threshold to pass if we want to control the threshold from backend
  // If not passed, then threshold will be controlled from risk service side
  float threshold = 16;
  repeated auth.liveness.LivenessStatus liveness_statuses = 17;
  repeated float liveness_scores = 18;
  repeated string liveness_inhouse_errors = 19;
  repeated float liveness_inhouse_scores = 20;
  repeated float facematch_scores = 21;
  repeated kyc.FailureType ckyc_errors = 22;
  string hashed_phone_number = 23;
  api.typesv2.common.Name father_name = 25;
  api.typesv2.common.Name mother_name = 26;
  api.typesv2.common.BooleanEnum onboarding_ekyc_number_mismatch = 27;
  api.typesv2.common.Name user_pan_name = 28;
  repeated float otp_scores = 29;
  repeated vendors.inhouse.CreditReportAttributeInfo cb_details = 30;
  employment.EmploymentData employment_data = 31;
  map<string, api.typesv2.PropertyValue> device_property_value_map = 32;

  enum ModelVersion {
    MODEL_VERSION_UNSPECIFIED = 0;
    MODEL_VERSION_V0 = 1;
    MODEL_VERSION_V1 = 2;
  }

  // this specifies which version of the ds model will be used
  ModelVersion model_version = 33;

}

message DetectRiskResponse {
  rpc.Status status = 1;
  float score = 2;
  float threshold = 3;
  // bool flag to denote if the user is risky
  api.typesv2.common.BooleanEnum risky_user = 4;
  // response time of the API in seconds
  google.protobuf.Duration time = 5;
  // list of errors returned by the API
  repeated string error = 6;
  string raw_vendor_response = 7;
}

message DetectLocationRiskRequest {
  vendorgateway.RequestHeader header = 1 [(validate.rules).message.required = true];

  string actor_id = 2 [(validate.rules).string.min_len = 1];

  string request_id = 3 [(validate.rules).string.min_len = 1];

  google.type.LatLng lat_lng = 4 [(validate.rules).message.required = true];

  string pincode = 5 [(validate.rules).string.min_len = 1];
}

message DetectLocationRiskResponse {
  enum Status {
    OK = 0;

    INVALID_ARGUMENT = 3;

    NOT_FOUND = 5;

    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // location risk score will be in range [0, 1]
  float score = 2;

  RiskSeverity risk_severity = 3;

  string model_version = 4;

  string raw_vendor_response = 5;
}

// AFUAttempt contains afu data for a single afu attempt such as liveness and facematch scores.
message AFUAttempt {
  auth.afu.OverallStatus overall_status = 1;
  repeated auth.afu.AuthFactor auth_factors = 2;
  repeated float liveness_scores = 3;
  repeated float otp_scores = 4;
  repeated float facematch_scores = 5;
  google.protobuf.Timestamp created_at = 6;
  repeated auth.liveness.LivenessStatus liveness_statuses = 7;
}

// AccountInfo contains info related to user account.
message AccountInfo {
  tiering.external.Tier tier = 1;
  google.protobuf.Timestamp created_at = 2;
}

// OnboardingDetails contains onboarding details and onboarding risk checks related info for user.
message OnboardingDetails {
  float onboarding_risk_model_score = 1;
}
