syntax = "proto3";

package vendorgateway.lending.preapprovedloan.liquiloans;

import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/bank_account_details.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/common/image_type.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/lending/preapprovedloan/enums.proto";
import "api/vendorgateway/lending/preapprovedloan/liquiloans/enums.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.preapprovedloan.liquiloans";

service Liquiloans {
  rpc GetCreditLineSchemes (GetCreditLineSchemesRequest) returns (GetCreditLineSchemesResponse);

  // This serves as the first interaction of a new user with Liquiloans, if all details are correct, a new user will be
  // created on Liquiloans side and an unique applicant_id will be returned.
  rpc AddPersonalDetails (AddPersonalDetailsRequest) returns (AddPersonalDetailsResponse);

  rpc AddBankingDetails (AddBankingDetailsRequest) returns (AddDetailsResponse);

  rpc AddAddressDetails (AddAddressDetailsRequest) returns (AddDetailsResponse);

  rpc AddEmploymentDetails (AddEmploymentDetailsRequest) returns (AddDetailsResponse);

  rpc ApplicantLookup (ApplicantLookupRequest) returns (ApplicantLookupResponse);

  rpc GetMandateLink (GetMandateLinkRequest) returns (GetMandateLinkResponse);

  rpc GetMandateStatus (GetMandateStatusRequest) returns (GetMandateStatusResponse);

  rpc MakeDrawdown (MakeDrawdownRequest) returns (MakeDrawdownResponse);

  rpc GetPdfAgreement (GetPdfAgreementRequest) returns (GetPdfAgreementResponse);

  rpc SendBorrowerAgreementOtp (SendBorrowerAgreementOtpRequest) returns (SendBorrowerAgreementOtpResponse);

  rpc VerifyBorrowerAgreementOtp (VerifyBorrowerAgreementOtpRequest) returns (VerifyBorrowerAgreementOtpResponse);

  rpc GetLoanStatus (GetLoanStatusRequest) returns (GetLoanStatusResponse);

  rpc VerifyAndDownloadCkyc (VerifyAndDownloadCkycRequest) returns (VerifyAndDownloadCkycResponse);

  rpc GetApplicantStatus (GetApplicantStatusRequest) returns (GetApplicantStatusResponse);

  //  This RPC fetch repayment schedules for a previously created loan application.
  rpc GetRepaymentSchedule (GetRepaymentScheduleRequest) returns (GetRepaymentScheduleResponse);

  rpc UploadDocument (UploadDocumentRequest) returns (UploadDocumentResponse);
  // Purpose of this API is to save repayment collection details against the loan application.
  rpc SaveCollection (SaveCollectionRequest) returns (SaveCollectionResponse);

  rpc HashGenerationForOkyc (HashGenerationForOkycRequest) returns (HashGenerationForOkycResponse);

  rpc CaptchaGenerationForOkyc (CaptchaGenerationForOkycRequest) returns (CaptchaGenerationForOkycResponse);

  rpc GenerateOtpForOkyc (GenerateOtpForOkycRequest) returns (GenerateOtpForOkycResponse);

  rpc ValidateOtpForOkyc (ValidateOtpForOkycRequest) returns (ValidateOtpForOkycResponse);

  // UpdateLead is used to update the lead details on Liquiloans side.
  // vendor doc: https://epifi.slack.com/files/U03A6H5EXEH/F05U99A8VC6/epifi_update_lead_api.docx
  rpc UpdateLead (UpdateLeadRequest) returns (UpdateLeadResponse);

  // CancelLead API frees the loan amount whih might have been frozen as user might have tried to take a loan in the past but did not complete the loan journey
  // CancelLead API Doc : https://liquiloans.slack.com/files/U04HF36Q4TG/F061C53M2KX/loan_cancellation_api__1_.pdf
  rpc CancelLead (CancelLeadRequest) returns (CancelLeadResponse);

  rpc ForeClosureDetails (ForeClosureDetailsRequest) returns (ForeClosureDetailsResponse);

  // UpdateApplicantUdf RPC used to update any UDFs (User Defined Fields) for ann applicant in the LL system
  rpc UpdateApplicantUdf (UpdateApplicantUdfRequest) returns (UpdateApplicantUdfResponse);

  // CreateRepaymentSchedule RPC creates repayment schedule for a loan after drawdown is done
  rpc CreateRepaymentSchedule (CreateRepaymentScheduleRequest) returns (CreateRepaymentScheduleResponse);
  // SaveCharges RPC is used to post charges against a loan on Liquiloans side
  // the RPC is idempotent based on the charge_id field
  rpc SaveCharges (SaveChargesRequest) returns (SaveChargesResponse);
  // GetApplicationSoa RPC is used to fetch the Statement of Account (SoA) for a given application
  rpc GetApplicationSoa (GetApplicationSoaRequest) returns (GetApplicationSoaResponse);
}

message SaveChargesRequest {
  vendorgateway.RequestHeader header = 1;
  LoanProgram loan_program = 2;
  string loan_id = 3 [(validate.rules).string.min_len = 1];
  // date to which the charge is applied
  // if charge date is between EMI N's due date and EMI N+1's due date, then the charge will be applied to EMI N
  // e.g. if charge date is 2021-08-18 and EMI 3's due date is 2021-08-10, EMI 4's due date is 2021-09-10, then the charge will be applied to EMI 3
  google.type.Date date = 4;
  // charge amount
  google.type.Money amount = 5;
  // unique identifier for the charge on a loan
  // idempotency will be checked on this field
  string charge_id = 6;
  string remarks = 7;
  SchemeVersion scheme_version = 8;
}
message SaveChargesResponse {
  rpc.Status status = 1;
}

message CreateRepaymentScheduleRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2;
  LoanProgram loan_program = 3;
  TenureFrequency repayment_frequency = 4;
  int32 emi_tenure = 5;
  google.type.Date emi_start_date = 6;
  repeated Schedule schedules = 7;
  SchemeVersion scheme_version = 8;
  message Schedule {
    google.type.Date due_date = 1;
    google.type.Money principal = 2;
    google.type.Money interest = 3;
    google.type.Money total_amount = 4;
    google.type.Money other_charges = 5;
    // optional
    google.type.Money principal_outstanding = 6;
  }
}

// returns the below statuses for known errors
// AlreadyExists: If the repayment schedule is already created for the given loan_id
// FailedPreCondition: If the loan is already disbursed
message CreateRepaymentScheduleResponse {
  rpc.Status status = 1;
}

message UpdateApplicantUdfRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  LoanProgram loan_program = 3;
  google.type.Money monthly_income = 4;
  IncomeDataSource income_data_source = 5;
  SchemeVersion scheme_version = 6;
}

message UpdateApplicantUdfResponse {
  rpc.Status status = 1;
}

message ForeClosureDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  int64 application_id = 2;
  LoanProgram loan_program = 3;
  SchemeVersion scheme_version = 4;
}

message ForeClosureDetailsResponse {
  rpc.Status status = 1;
  string message = 2;
  Data data = 3;
  string failure_reason = 4;

  message Data {
    string application_id = 1;
    google.type.Money total_outstanding = 2;
    google.type.Money principal_outstanding = 3;
    google.type.Money interest_outstanding = 4;
    google.type.Money penalty_charges = 5;
    google.type.Money fees_charges = 6;
    google.type.Money other_charges = 7;
  }
}

message CancelLeadRequest {
  vendorgateway.RequestHeader header = 1;
  string application_id = 2;
  string applicant_id = 3;
  LoanProgram loan_program = 4;
  SchemeVersion scheme_version = 5;
}

message CancelLeadResponse {
  rpc.Status status = 1;
  string message = 2;
  Data data = 3;
  string failure_reason = 4;
  message Data {
    string application_id = 1;
    string applicant_id = 2;
  }
}

message UpdateLeadRequest {
  vendorgateway.RequestHeader header = 1;
  string application_id = 2;
  google.type.Money amount = 3;
  string urn = 4;
  UpdateLeadSchemeDetails scheme_details = 5;
  LoanProgram loan_program = 6;
  SchemeVersion scheme_version = 7;
}

message UpdateLeadSchemeDetails {
  TenureFrequency installment_frequency = 1;
  // e.g: 36
  int32 installment_tenure = 2;
  // e.g: 180
  google.type.Money processing_fees = 3;
  // e.g: 0.43
  double roi_percentage = 4 [(validate.rules).double.gte = 0, (validate.rules).double.lte = 100];
  google.type.Date installment_start_date = 5;
  double subvention_percentage = 6 [(validate.rules).double.gte = 0, (validate.rules).double.lte = 100];
  FeeType processing_fees_type = 7;
  RoiType roi_type = 8;
  FeeAppliedOn roi_applied_on = 9;
  FeeAppliedOn processing_fees_customer_applied_on = 10;
  // ??
  bool processing_fees_customer_gst = 11;
  // ??
  bool roi_gst = 12;
  FeeType gst_type = 13;
  // e.g: 18
  double gst_value = 14;
}

message UpdateLeadResponse {
  rpc.Status status = 1;
  string loan_id = 2;
}

message GetCreditLineSchemesRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  LoanProgram loan_program = 3;
  SchemeVersion scheme_version = 4;
}

message GetCreditLineSchemesResponse {
  rpc.Status status = 1;
  CreditLineDetails credit_line_details = 2;
  repeated CreditLineScheme credit_line_schemes = 3;

  message CreditLineDetails {
    string applicant_id = 1;
    //Represents the total credit limit approved for a user (available limit + block limit)
    google.type.Money upper_limit = 2;
    //Represents the amount a user can make a drawdown request for, after a drawdown, this limit will decrease
    google.type.Money available_limit = 3;
    //Represents the amount that user has alreaday taken. When user will make a drawdown, this limit will increase
    google.type.Money block_limit = 4;
  }

  message CreditLineScheme {
    Roi roi = 1;
    AmountConstraints amount_constraints = 2;
    google.type.Date emi_due_date = 3; //YYYY-MM-DD
    Tenure tenure = 4;
    PfFees pf_fees = 5;

    message AmountConstraints {
      google.type.Money max_emi_allowed = 1;
      google.type.Money min_drawdown_amount = 2;
      google.type.Money max_drawdown_amount = 3;
    }
  }
}

message AddPersonalDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  api.typesv2.common.Name name = 2;
  string email = 3;
  api.typesv2.common.PhoneNumber contact_number = 4;
  string pan = 5;
  api.typesv2.Gender gender = 6;
  google.type.Date dob = 7;
  string urn = 8;
  LoanProgram loan_program = 9;
  // send only for AA based income
  google.type.Money monthly_income = 10;
  IncomeDataSource income_data_source = 11;
  SchemeVersion scheme_version = 12;
}

message AddPersonalDetailsResponse {
  rpc.Status status = 1;
  Data data = 2;
  string failure_reason = 3;

  message Data {
    string applicant_id = 1;
    api.typesv2.common.Name name = 2;
    string email = 3;
    api.typesv2.common.PhoneNumber contact_number = 4;
  }
}

message AddBankingDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  api.typesv2.BankAccountDetails bank_account_details = 3;
  LoanProgram loan_program = 4;
  SchemeVersion scheme_version = 5;
}

message AddAddressDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  api.typesv2.PostalAddress address = 3;
  api.typesv2.AddressType address_type = 9;
  LoanProgram loan_program = 10;
  SchemeVersion scheme_version = 11;
}

message AddEmploymentDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  api.typesv2.EmploymentType occupation = 3;
  string organization_name = 4;
  google.type.Money monthly_income = 5;
  string work_email = 6;
  LoanProgram loan_program = 7;
  SchemeVersion scheme_version = 8;
}

// common response to be used for multiple APIs as Liquiloans returns same response structure for multiple APIs.
message AddDetailsResponse {
  enum Status {
    OK = 0;
    // e.g.: "{"monthly_income":["Monthly income should be between Rs 2,000 to Rs 1,00,00,000"]}" for AddEmploymentDetails API
    INCOME_VALIDATION_ERROR = 101;
  }
  rpc.Status status = 1;
  string failure_reason = 2;
}

message ApplicantLookupRequest {
  vendorgateway.RequestHeader header = 1;
  string pan = 2;
  LoanProgram loan_program = 3;
  SchemeVersion scheme_version = 4;
}

message ApplicantLookupResponse {
  rpc.Status status = 1;
  Data data = 2;
  string failure_reason = 3;

  message Data {
    string applicant_id = 1;
    string name = 2;
    string pan = 3;
    DetailsStatus details_status = 4;
    ActivationStatus activation_status = 5;

    message DetailsStatus {
      bool banking_detail_status = 1;
      bool address_detail_status = 2;
      bool employment_detail_status = 3;
    }

    message ActivationStatus {
      bool agreement_status = 1;
      bool mandate_status = 2;
    }
  }
}

message GetMandateLinkRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  LoanProgram loan_program = 3;
  bool recreate_url = 4;
  SchemeVersion scheme_version = 5;
}

message GetMandateLinkResponse {
  enum Status {
    OK = 0;
    // LL API returns below response if mandate creation limit is exhausted for calling mandate link API for that particular customer in the given program
    // {"status":false,"message":"Cannot recreate mandate, limit exhausted.","data":[],"code":400,"checksum":null}
    MANDATE_CREATION_LIMIT_EXHAUSTED_FOR_CUSTOMER = 101;
  }
  rpc.Status status = 1;
  string mandate_url = 2;
  string mandate_id = 3;
}

message GetMandateStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  LoanProgram loan_program = 3;
  SchemeVersion scheme_version = 9;
}

message GetMandateStatusResponse {
  enum Status {
    OK = 0;
    // LL API returns below response if we call mandate status API first even before calling get mandate link API
    // Ref: https://epifi.slack.com/archives/C04HAQ9VC5T/p1711524336675849
    // {"status":false,"message":"Improper Log Found","data":[],"code":400,"checksum":null}
    // Not returning grpc status NotFound here since it can be mapped to actual http 404 status code
    // but LL is returning http 400 status code for this error
    MANDATE_RECORD_NOT_FOUND = 101;
  }
  rpc.Status status = 1;
  MandateStatus mandate_status = 2;
}

message MakeDrawdownRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  LoanTenure tenure = 3;
  google.type.Money amount = 4;
  string urn = 5;

  message LoanTenure {
    TenureFrequency tenure_frequency = 1;
    int32 value = 2;
  }
  LoanProgram loan_program = 6;
  SchemeVersion scheme_version = 7;
}

message MakeDrawdownResponse {
  rpc.Status status = 1;
  string loan_id = 2;
  string applicant_id = 3;
  string urn = 4;
  string failure_reason = 5;
}

message GetPdfAgreementRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  string application_id = 3;
  LoanProgram loan_program = 4;
  SchemeVersion scheme_version = 5;
}

message GetPdfAgreementResponse {
  rpc.Status status = 1;
  string pdf_base64_text = 2;
  string doc_id = 3;
}

message SendBorrowerAgreementOtpRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  string application_id = 3;
  LoanProgram loan_program = 4;
  SchemeVersion scheme_version = 5;
}

message SendBorrowerAgreementOtpResponse {
  rpc.Status status = 1;
}

message VerifyBorrowerAgreementOtpRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  string application_id = 3;
  string doc_id = 4;
  string otp = 5;
  LoanProgram loan_program = 6;
  SchemeVersion scheme_version = 7;
}

message VerifyBorrowerAgreementOtpResponse {
  rpc.Status status = 1;
  //signed copy of the agreement pdf after OTP verificatin
  string agreement_signed_copy = 2;
  string failure_reason = 3;
}

message GetLoanStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string application_id = 2;
  string urn = 3;
  LoanProgram loan_program = 4;
  SchemeVersion scheme_version = 5;
}

message GetLoanStatusResponse {
  rpc.Status status = 1;
  LoanStatus loan_status = 2;
  google.protobuf.Timestamp last_status_timestamp = 3;
  string failure_reason = 4;

  message LoanStatus {
    LoanData loan_data = 1;
    LoanDetails loan_details = 2;

    message LoanData {
      string loan_id = 1;
      string loan_code = 2;
      Status status = 3;
      string urn = 4;
      string utr = 5;
    }

    message LoanDetails {
      google.type.Money amount = 1;
      google.type.Money disbursed_amount = 3;
      google.type.Date disbursement_date = 4;
      google.type.Money emi_amount = 5;
      int32 tenure = 6;
      double roi = 7;
    }
  }
}

message VerifyAndDownloadCkycRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  string pan = 3;
  string auth_factor = 4;
  AuthFactorType auth_factor_type = 5;
  LoanProgram loan_program = 6;
  SchemeVersion scheme_version = 7;
}

message VerifyAndDownloadCkycResponse {
  rpc.Status status = 1;
  CkycData ckyc_data = 2;
  string failure_reason = 3;

  message CkycData {
    PersonalData personal_data = 1;
    api.typesv2.common.ImageType image_type = 2;
    string photo = 3;
  }

  message PersonalData {
    string ckyc_no = 1;
    api.typesv2.common.Name name = 2;
    google.type.Date kyc_date = 3;
    api.typesv2.Gender gender = 4;
    google.type.Date dob = 5;
    api.typesv2.common.Name father_name = 6;
    api.typesv2.common.Name mother_name = 7;
    api.typesv2.common.PhoneNumber mob_num = 8;
    string email = 9;
    Addresses addresses = 10;

    message Addresses {
      api.typesv2.PostalAddress permanent_address = 1;
      api.typesv2.PostalAddress correspondence_address = 2;
      bool perm_corr_address_same = 3;
    }
  }
}

message GetApplicantStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string applicant_id = 2;
  LoanProgram loan_program = 3;
  SchemeVersion scheme_version = 4;
}

message GetApplicantStatusResponse {
  rpc.Status status = 1;
  ApplicantStatus applicant_status = 2;
}

message Tenure {
  int32 min_tenure = 1;
  int32 max_tenure = 2;
  liquiloans.TenureFrequency tenure_frequency = 3;
}

message Roi {
  double roi_value = 1;
  liquiloans.RoiType roi_type = 2;
}

message PfFees {
  liquiloans.PfType pf_type = 1;
  double pf_fees = 2;
}

message GetRepaymentScheduleRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2;
  LoanProgram loan_program = 3;
  // will choose the LL API based on the api_version
  // if api_version is not provided, it will default to REPAYMENT_SCHEDULE_API_VERSION_V2
  RepaymentScheduleApiVersion api_version = 4;
  SchemeVersion scheme_version = 5;
}

message GetRepaymentScheduleResponse {
  rpc.Status status = 1;
  repeated Schedule schedules = 2;
  string failure_reason = 3;

  message Schedule {
    string application_id = 1;
    int32 installment_number = 2;
    google.type.Date due_date = 3;
    google.type.Money due_amount = 4;
    google.type.Money principal_amount = 5;
    google.type.Money interest_amount = 6;
    PaymentStatus payment_status = 7;
    google.type.Date received_date = 8;
    google.type.Money received_amount = 9;
    google.type.Money paid_principal_amount = 10;
    google.type.Money paid_interest_amount = 11;
    // interest charges incurred due to late payment of EMI
    google.type.Money lpi = 12;
    google.type.Money other_charges = 13;
    google.type.Money bounce_charges = 14;
    google.type.Money post_payment_principal_outstanding = 15;
    google.type.Money post_payment_interest_outstanding = 16;
    // (only populated for REPAYMENT_SCHEDULE_API_VERSION_V4)
    google.type.Money waived_charges = 17;
    // amount paid by user out of lpi (late payment interest)
    // (only populated for REPAYMENT_SCHEDULE_API_VERSION_V4)
    google.type.Money paid_late_payment_interest = 18;
    // amount paid till now by user out of other_charges
    // (only populated for REPAYMENT_SCHEDULE_API_VERSION_V4)
    google.type.Money paid_other_charges = 19;
    // amount paid till now by user out of bounce_charges
    // (only populated for REPAYMENT_SCHEDULE_API_VERSION_V4)
    google.type.Money paid_bounce_charges = 20;
    // (only populated for REPAYMENT_SCHEDULE_API_VERSION_V4)
    google.type.Money post_payment_charges_outstanding = 21;
  }
}

message UploadDocumentRequest {
  vendorgateway.RequestHeader header = 1;
  LoanProgram loan_program = 2;
  bytes file = 3;
  string loan_id = 4;
  DocumentType document_type = 5;
  SchemeVersion scheme_version = 6;
}

message UploadDocumentResponse {
  rpc.Status status = 1;
  string document_id = 2;
  string document_type_id = 3;
  string file_name = 4;
  string file_path = 5;
}

message SaveCollectionRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2;
  LoanProgram loan_program = 3;
  repeated PaymentSchedule payment_schedules = 4;
  SchemeVersion scheme_version = 5;
  message PaymentSchedule {
    PaymentMode payment_mode = 1;
    google.type.Date transaction_date = 2;
    PaymentStatus payment_status = 3;
    // Represents the total transaction amount paid by the user
    google.type.Money paid_total_amount = 4;
    // UTR or Payment Reference number
    string voucher_no = 5;
    google.type.Money bounce_charges = 6;
    google.type.Money collection_charges = 7;
    // Any charges that were posted to LL using SaveCharges API should be passed in this field
    google.type.Money other_charges = 8;
    // Late payment interest charges
    google.type.Money lpi_charges = 9;
    // settelment_date is optional field, all the operations and decisions are taken based on transaction_date
    google.type.Date settelment_date = 10;
    google.type.Date due_date = 11;
  }
}

message SaveCollectionResponse {
  rpc.Status status = 1;
  string failure_reason = 2;
}

message HashGenerationForOkycRequest {
  vendorgateway.RequestHeader header = 1;
  int64 application_id = 2;
  LoanProgram loan_program = 3;
  SchemeVersion scheme_version = 4;
}

message HashGenerationForOkycResponse {
  rpc.Status status = 1;
  Data data = 2;
  message Data {
    string hash = 1;
  }
}

message CaptchaGenerationForOkycRequest {
  vendorgateway.RequestHeader header = 1;
  string hash = 2;
  int64 application_id = 3;
  LoanProgram loan_program = 4;
  SchemeVersion scheme_version = 5;
}

message CaptchaGenerationForOkycResponse {
  rpc.Status status = 1;
  Data data = 2;
  message Data {
    string captcha_image = 1;
    string request_token = 2;
    string captcha_txn_id = 3;
  }
}

message GenerateOtpForOkycRequest {
  vendorgateway.RequestHeader header = 1;
  string hash = 2;
  int64 uid_number = 3;
  int64 applicant_id = 4;
  string captcha_code = 5;
  string request_token = 6;
  string captcha_txn_id = 7;
  LoanProgram loan_program = 8;
  SchemeVersion scheme_version = 9;
}

message GenerateOtpForOkycResponse {
  rpc.Status status = 1;
  Data data = 2;
  message Data {
    string txn_id = 1;
  }
}

message ValidateOtpForOkycRequest {
  vendorgateway.RequestHeader header = 1;
  string hash = 2;
  int64 otp = 3;
  int64 applicant_id = 4;
  string request_token = 5;
  LoanProgram loan_program = 8;
  SchemeVersion scheme_version = 9;
}

message ValidateOtpForOkycResponse {
  rpc.Status status = 1;
}

message GetApplicationSoaRequest {
  vendorgateway.RequestHeader header = 1;
  string application_id = 2;
  LoanProgram loan_program = 3;
  SchemeVersion scheme_version = 4;
}

message GetApplicationSoaResponse {
  rpc.Status status = 1;
  Data data = 2;
  message Data {
    string link = 1;
    google.protobuf.Value application_id = 2;
  }
}
