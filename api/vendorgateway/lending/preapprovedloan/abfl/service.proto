syntax = "proto3";

package vendorgateway.lending.preapprovedloan.abfl;

import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/document_type.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/gender.proto";
import "api/vendorgateway/lending/preapprovedloan/abfl/enums.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/latlng.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/abfl";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.preapprovedloan.abfl";

service Abfl {
  rpc CreateToken (CreateTokenRequest) returns (CreateTokenResponse);
  rpc PerformCKyc (PerformCkycRequest) returns (PerformCkycResponse);
  rpc PollingStatusCkyc (PollingStatusCkycRequest) returns (PollingStatusCkycResponse);
  rpc DecisionEngineBre (DecisionEngineBreRequest) returns (DecisionEngineBreResponse);
  rpc BrePolling (BrePollingRequest) returns (BrePollingResponse);
  rpc UnifiedCkyc (UnifiedKycRequest) returns (UnifiedKycResponse);
  rpc UnifiedKyStatus (UnifiedKycStatusRequest) returns (UnifiedKycStatusResponse);
  rpc ImpsPennyDrop (ImpsPennyDropRequest) returns (ImpsPennyDropResponse);
  rpc RepaymentScheduleEstimate (RepaymentScheduleEstimateRequest) returns (RepaymentScheduleEstimateResponse);
  rpc RepaymentScheduleKfs (RepaymentScheduleKfsRequest) returns (RepaymentScheduleKfsResponse);
  rpc SanctionLetterPdfGenerator (SanctionLetterRequest) returns (SanctionLetterResponse);
  rpc DigitalSign (DigitalSignRequest) returns (DigitalSignResponse);
  rpc DigitalSignStatus (DigitalSignStatusRequest) returns (DigitalSignStatusResponse);
  rpc DigitalSignCallback (DigitalSignCallbackRequest) returns (DigitalSignCallbackResponse);
  rpc LoanDisbursement (LoanDisbursementRequest) returns (LoanDisbursementResponse);
  rpc LoanDisbursementStatus (LoanDisbursementStatusRequest) returns (LoanDisbursementStatusResponse);
  rpc EmandateRequest (EMandateRequest) returns (EmandateResponse);
  rpc GetEmandateStatus (EmandateStatusRequest) returns (EmandateStatusResponse);
  rpc SaveCommonDetails (SaveCommonDetailsRequest) returns (SaveCommonDetailsResponse);
  rpc GetTransactionDetails (GetTransactionDetailsRequest) returns (GetTransactionDetailsResponse);
  rpc PostLatLong (LatLongRequest) returns (LatLongResponse);
  rpc DigilockerCreateProfile (DigilockerCreateProfileRequest) returns (DigilockerCreateProfileResponse);
  rpc DigilockerFetchProfile (DigilockerFetchProfileRequest) returns (DigilockerFetchProfileResponse);
  rpc GetLoanAgreement (CreateLoanAgreementRequest) returns (CreateLoanAgreementResponse);

  // APIs for the ABFL PWA journey
  rpc CreateCustomer (CreateCustomerRequest) returns (CreateCustomerResponse);
  rpc PushDataForJourney (PushDataForJourneyRequest) returns (PushDataForJourneyResponse);
  rpc CreateJourneySession (CreateJourneySessionRequest) returns (CreateJourneySessionResponse);
}

message CreateCustomerRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  api.typesv2.common.PhoneNumber mobile_number = 3;
}

message CreateCustomerResponse {
  rpc.Status status = 1;
}

message PushDataForJourneyRequest {
  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  api.typesv2.common.PhoneNumber mobile = 3;
  string pan = 4;
  string email = 5;
  google.type.Date dob = 6; // YYYY-MM-DD
  string campaign_type = 7;
}

message PushDataForJourneyResponse {
  rpc.Status status = 1;
}

message CreateJourneySessionRequest {
  message CampaignParams {
    string utm_source = 1;
    string utm_content = 2;
    string utm_medium = 3;
    string utm_campaign = 4;
  }

  vendorgateway.RequestHeader header = 1;
  string customer_id = 2;
  string program = 3;
  string redirect_url = 4;
  string source = 5;
  CampaignParams campaign_params = 6;
}

message CreateJourneySessionResponse {
  rpc.Status status = 1;
  string journey_url = 2;
}


message CreateTokenRequest {
  vendorgateway.RequestHeader header = 1;
}

message CreateTokenResponse {
  rpc.Status status = 1;
  CreateToken create_token_resp = 2;
  message CreateToken {
    string token = 1;
  }
}

message PerformCkycRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  google.type.Date dob = 3;
  string pan_number = 4;
}

message PerformCkycResponse {
  rpc.Status status = 1;
  PerformCkycData perform_ckyc_data = 2;

  message PerformCkycData {
    string request_id = 1;
  }
}

message PollingStatusCkycRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  string z_request_id = 3;
}

message PollingStatusCkycResponse {
  rpc.Status status = 1;
  PollingStatusCkycData polling_status_ckyc_data = 2;
  string request_id = 3;
  string account_id = 4;

  message PollingStatusCkycData {
    Result result = 1;

    message Result {
      SourceOutput source_output = 1;

      message SourceOutput {
        string status = 1;
        CkycRequestStatus ckyc_request_status = 2;
        CkycPersonalDetails ckyc_personal_details = 3;

        message CkycPersonalDetails {
          api.typesv2.common.Name ckyc_maiden_full_name = 1;
          string ckyc_form60 = 2;
          CkycAddresses ckyc_addresses = 3;
          api.typesv2.Gender ckyc_gender = 4;
          string ckyc_pan = 5;
          string ckyc_const_type = 6;
          api.typesv2.common.Name ckyc_father_full_name = 7;
          string ckyc_number = 8;
          api.typesv2.common.Name ckyc_mother_full_name = 9;
          CkycContactDetails ckyc_contact_details = 10;
          api.typesv2.common.Name ckyc_full_name = 11;
          CkycImageDetails ckyc_image_details = 12;
          //          map<string, CkycImageData> ckyc_image_details = 12;

          google.type.Date ckyc_dob = 13;
          string ckyc_remarks = 14;
          api.typesv2.common.Name ckyc_spouse_full_name = 15;
          CkycIdDetails ckyc_id_details = 16;
          CkycAccountType ckyc_acc_type = 17;
          CkycDeclarationDetails ckyc_declaration_details = 18;

          message CkycDeclarationDetails {
            google.type.Date ckyc_date_of_declaration = 1;
            int64 ckyc_no_of_ids = 2;
            int64 ckyc_no_of_images = 3;
            int64 ckyc_no_of_rel_person = 4;
            string ckyc_place_of_declaration = 5;
            string ckyc_type_of_doc_submitted = 6;
            string ckyc_ver_branch = 7;
            google.type.Date ckyc_ver_date = 8;
            string ckyc_ver_designation = 9;
            string ckyc_ver_emp_code = 10;
            string ckyc_ver_name = 11;
          }

          message CkycIdDetails {
            CkycIdDetail ckyc_id_1 = 1;
            CkycIdDetail ckyc_id_2 = 2;
            CkycIdDetail ckyc_id_3 = 3;
          }

          message CkycIdDetail {
            string ckyc_id_no = 1;
            string ckyc_id_type = 2;
            string ckyc_ver_status = 3;
          }

          message CkycImageDetails {
            CkycImageData ckyc_image_1 = 1;
            CkycImageData ckyc_image_2 = 2;
            CkycImageData ckyc_image_3 = 3;
            CkycImageData ckyc_image_4 = 4;
            CkycImageData ckyc_image_5 = 5;
            CkycImageData ckyc_image_6 = 6;
            CkycImageData ckyc_image_7 = 7;
            CkycImageData ckyc_image_8 = 8;
            CkycImageData ckyc_image_9 = 9;
            CkycImageData ckyc_image_10 = 10;
          }

          message CkycImageData {
            string ckyc_image_data = 1;
            CkycImageType ckyc_img_type = 2;
          }

          message CkycContactDetails {
            string ckyc_email = 1;
            string ckyc_mobile_isd = 2;
            string ckyc_mobile_no = 3;
            string ckyc_off_tel_no = 4;
            string ckyc_off_tel_std = 5;
            string ckyc_res_tel_no = 6;
            string ckyc_res_tel_std = 7;
          }

          message CkycAddresses {
            api.typesv2.PostalAddress ckyc_cor_address = 1;
            bool ckyc_per_add_same_as_cor = 2;
            api.typesv2.PostalAddress ckyc_per_address = 3;
          }
        }

        message CkycRequestStatus {
          string ckyc_arn = 1;
          bool ckyc_is_success = 2;
          string ckyc_rejection_message = 3;
        }
      }
    }
  }
}

message DecisionEngineBreRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  CustomerReport customer_report = 3;

  message CustomerReport {
    KycInfo kyc_info = 1;
    LoanApplicationRequest loan_application_request = 2;

    message LoanApplicationRequest {
      google.type.Money requested_loan_amount = 1;
      double roi = 2;
      int32 tenure = 3;
      string customer_identifier = 4;
    }

    message KycInfo {
      api.typesv2.common.Name full_name = 1;
      api.typesv2.common.PhoneNumber mobile = 2;
      string email = 3;
      string pan_number = 4;
      api.typesv2.Gender gender = 5;
      google.type.Date dob = 6;
      api.typesv2.PostalAddress address = 7;
      google.type.Money annual_household_income = 8;
      api.typesv2.EmploymentType employment_type = 16;
      // this is the email id of the customer provided by the organisation that they are working which is taken at employment step
      string organization_email = 17;
      string organization_name = 18;
    }
  }
}

message DecisionEngineBreResponse {
  rpc.Status status = 1;
}

message BrePollingRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
}

message BrePollingResponse {
  rpc.Status status = 1;
  bool is_retryable = 2;
  string message = 3;
  BrePollingData bre_polling_data = 4;

  message BrePollingData {
    string account_id = 1;
    string ccc_id = 2;
    // this is max loan amount and minimum loan amount is hard coded
    google.type.Money loan_amount = 3;
    string async_id = 4;
    double roi = 5;
    // this is max tenure in months and minimum tenure is hard coded
    double tenure = 6;
    google.type.Money maximum_emi_amount = 7;
  }
}

message UnifiedKycRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  string ccc_id = 3;
  string transaction_id = 4;
  string selfie = 5;
  string ckyc_doc_front_photo = 6;
  string ckyc_customer_photo = 7;
  api.typesv2.PostalAddress declared_address = 8;
  api.typesv2.common.Name declared_name = 9;
  google.type.Date declared_dob = 10;
  string declared_pan = 11;
  api.typesv2.common.Name nsdl_name = 12;
  api.typesv2.Gender gender = 13;
  string email = 14;
  google.type.Date ckyc_dob = 15;
  api.typesv2.common.PhoneNumber mobile = 16;
  api.typesv2.common.Name ckyc_name = 17;
  api.typesv2.DocumentType ckyc_doc_type = 18;
  api.typesv2.PostalAddress ckyc_address = 19;
  string nsdl_pan = 20;
  string okyc_xml = 21; // digilocker aadhaar xml base64 encoded
  google.type.LatLng lat_lng = 22;
  string street_address = 23;
}

message UnifiedKycResponse {
  rpc.Status status = 1;
  UnifiedKycData unified_kyc_data = 2;

  message UnifiedKycData {
    string transaction_id = 1;
    string kyc_status = 2;
    string account_id = 3;
  }
}

message UnifiedKycStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  string transaction_id = 3;
  string ccc_id = 4;
}

message UnifiedKycStatusResponse {
  rpc.Status status = 1;
  UnifiedKycStatusData unified_kyc_status_data = 2;

  message UnifiedKycStatusData {
    string kyc_type = 1;
    string transaction_id = 2;
    bool is_kyc_done = 3 [deprecated = true];
    bool is_retryable = 4;
    UkycStatus ukyc_status = 5;
  }
}

message ImpsPennyDropRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  string account_number = 3;
  string ifsc_code = 4;
  string bank_name = 5;
  string bank_branch = 6;
  string email = 7;
  api.typesv2.common.Name account_holder_name = 8;
  string name_match_type = 9;
  string use_combined_solution = 10;
  bool allow_partial_match = 11;
  ClientData client_data = 12;

  message ClientData {
    string case_id = 1;
  }
}

message ImpsPennyDropResponse {
  rpc.Status status = 1;
  ImpsPennyDropData imps_penny_drop_data = 2;

  message ImpsPennyDropData {
    string ibl_ref_no = 1;
    string customer_ref_no = 2;
    string status_desc = 3;
    google.type.Money amount = 4;
    string tran_type = 5;
    string status_code = 6;
  }
}

message RepaymentScheduleEstimateRequest {
  vendorgateway.RequestHeader header = 1;
  google.type.Money loan_amount = 2;
  double rate_of_interest = 3;
  int32 loan_tenure = 4;
}

message RepaymentScheduleEstimateResponse {
  rpc.Status status = 1;
  RepaymentScheduleEstimateData repayment_schedule_estimate_data = 2;

  message LoanDetails {
    google.type.Money loan_amount = 1;
    double rate_of_interest = 2;
    int64 loan_tenure = 3;
    google.type.Date disbursal_date = 4;
    google.type.Date first_emi_date = 5;
  }

  message Installments {
    int64 installment_number = 1;
    google.type.Date due_date = 2;
    google.type.Money due_amount = 3;
    google.type.Money principal_component = 4;
    google.type.Money interest_component = 5;
    google.type.Money principal_outstanding = 6;
  }

  message RepaymentScheduleEstimateData {
    LoanDetails loan_details = 1;
    repeated Installments installments = 2;
  }
}

message RepaymentScheduleKfsRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  LoanDetails loan_details = 3;
  repeated Installments installments = 4;
  google.type.Money processing_fee_including_gst = 5;

  message LoanDetails {
    google.type.Money loan_amount = 1;
    double rate_of_interest = 2;
    int64 loan_tenure = 3;
    google.type.Date disbursal_date = 4;
    double pf_with_gst_percentage = 5;
    int64 number_of_installments = 6;
    api.typesv2.common.Name applicant_name = 7;
  }

  message Installments {
    int64 installment_number = 1;
    google.type.Date due_date = 2;
    google.type.Money due_amount = 3;
    google.type.Money principal_component = 4;
    google.type.Money interest_component = 5;
    google.type.Money principal_outstanding = 6;
  }
}

message RepaymentScheduleKfsResponse {
  rpc.Status status = 1;
  RepaymentScheduleKfsData repayment_schedule_kfs_data = 2;

  message RepaymentScheduleKfsData {
    google.type.Money loan_amount = 1;
    google.type.Money total_interest = 2;
    google.type.Money sum_other_charges = 3;
    google.type.Money sum_borrower = 4;
    string repayment_frequency = 5;
    string frequency_type = 6;
    string tenure_of_loan = 7;
    int64 number_of_installments = 8;
    google.type.Money emi_amount = 9;
    int64 annual_percentage_rate = 10;
    double internal_rate_of_return = 11;
    string kfs_pdf = 12;
  }
}

message SanctionLetterRequest {
  vendorgateway.RequestHeader header = 1;
  string ccc_id = 2;
  string account_id = 3;
  string pan = 4;
  string email = 5;
  google.type.Date date_of_birth = 6;
  google.type.Money loan_amount = 7;
  api.typesv2.common.Name name = 8;
  google.protobuf.Timestamp offer_timestamp = 9;
  double roi = 10;
  google.type.Money emi = 11;
  int32 tenure = 12;
}

message SanctionLetterResponse {
  rpc.Status status = 1;
  SanctionLetterData sanction_letter_data = 2;

  message SanctionLetterData {
    string account_id = 1;
    string base64_pdf = 2;
  }
}

message DigitalSignRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  string email = 3;
  bool merged_pdf_flag = 4;
  string sanction_letter = 5;
  string loan_agreement = 6;
  string key_fact_statement = 7;
  api.typesv2.common.PhoneNumber mobile_number = 8;
  string unsigned_merged_pdf = 9;
  string ip_address = 10;
  google.protobuf.Timestamp timestamp = 11;
}

message DigitalSignResponse {
  rpc.Status status = 1;
  DigitalSignData digital_sign_data = 2;

  message DigitalSignData {
    string pdf_request_type = 1;
  }
}

message DigitalSignStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
}

message DigitalSignStatusResponse {
  rpc.Status status = 1;
  DigitalSignStatusData digital_sign_status_data = 2;

  message DigitalSignStatusData {
    string account_id = 1;
    string pdf_request_type = 2;
    string short_url = 3;
    string status = 4;
  }
}

message LoanDisbursementRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  google.type.Money loan_amount = 3;
  google.type.Money disbursal_amount = 4;
  int64 tenure = 5;
  BankDetails bank_details = 6;
  repeated Charges charges = 7;
  MandateDetails mandate_details = 8;
  string unique_id = 9;

  message BankDetails {
    string account_number = 1;
    string ifsc_code = 2;
  }

  message Charges {
    google.type.Money charge_amount = 1;
    google.type.Money final_amount = 2;
  }

  message MandateDetails {
    string reference_no = 1;
    google.type.Money emandate_amount = 2;
  }
}

message LoanDisbursementResponse {
  rpc.Status status = 1;
  LoanDisbursementData loan_disbursement_data = 2;

  message LoanDisbursementData {
    string message = 1;
    string account_id = 2;
    string unique_id = 3;
  }
}

message LoanDisbursementStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  string unique_id = 3;
}

message LoanDisbursementStatusResponse {
  rpc.Status status = 1;
  message LoanDisbursementStatusData {
    string deal_no = 1;
    string loan_no = 2;
    string disbursal_status = 3;
    string utr_no = 4;
  }
  LoanDisbursementStatusData loan_disbursement_status_data = 2;
}

message DigitalSignCallbackRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
}

message DigitalSignCallbackResponse {
  message DigitalSignCallbackData {
    string account_id = 1;
    string status = 2;
    string pdf_file = 3;
  }

  rpc.Status status = 1;
  DigitalSignCallbackData digital_sign_callback_data = 2;
  string pdf_request_type = 3;
}

message EMandateRequest {
  message Customer {
    api.typesv2.common.Name name = 1;
    string email = 2;
    api.typesv2.common.PhoneNumber contact = 3;
  }

  message BankAccount {
    string bank_name = 1;
    string account_number = 2;
    string ifsc_code = 3;
    api.typesv2.common.Name beneficiary_name = 4;
  }

  message SubscriptionRegistration {
    BankAccount bank_account = 1;
  }

  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  Customer customer = 3;
  string currency = 4;
  google.type.Money amount = 5;
  SubscriptionRegistration subscription_registration = 6;
}

message EmandateResponse {
  message CustomerDetails {
    string id = 1;
    api.typesv2.common.Name name = 2;
    string email = 3;
    api.typesv2.common.PhoneNumber contact = 4;
    string gstin = 5;
    string billing_address = 6;
    string shipping_address = 7;
    string customer_name = 8;
    string customer_email = 9;
    api.typesv2.common.PhoneNumber customer_contact = 10;
  }

  message Data {
    string id = 1;
    string receipt = 2;
    string invoice_number = 3;
    string customer_id = 4;
    CustomerDetails customer_details = 5;
    string order_id = 6;
    string payment_id = 7;
    bool partial_payment = 8;
    google.type.Money gross_amount = 9;
    google.type.Money tax_amount = 10;
    google.type.Money taxable_amount = 11;
    google.type.Money amount = 12;
    google.type.Money amount_paid = 13;
    google.type.Money amount_due = 14;
    string currency_symbol = 16;
    string short_url = 17;
    string view_less = 18;
    string type = 19;
    string idempotency_key = 20;
    string auth_link_status = 21;
    string razorpay_status = 22;
  }

  rpc.Status status = 1;
  string response_status = 2;
  Data data = 3;
}

message EmandateStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
}

message EmandateStatusResponse {
  message Data {
    string razorpay_cust_id = 1;
    string token_id = 2;
    string token_status = 3;
    string failure_reason = 4;
    string mrn_no = 5;
    string code = 6;
    string desc = 7;
  }
  rpc.Status status = 1;
  MandateStatus e_mandate_status = 2;
  Data data = 3;
}

message SaveCommonDetailsRequest {
  message References {
    api.typesv2.common.Name name = 1;
    api.typesv2.PostalAddress address = 2;
    api.typesv2.common.PhoneNumber phone_number = 3;
  }

  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  string ccc_id = 3;
  int32 loan_tenure = 4;
  google.type.Money loan_amount = 5;
  api.typesv2.common.Name loan_application_name = 6;
  api.typesv2.Gender gender = 7;
  int32 age = 8;
  string personal_email_id = 9;
  api.typesv2.common.PhoneNumber mobile_number = 10;
  api.typesv2.PostalAddress address = 11;
  string employment_type = 12;
  api.typesv2.common.Name fathers_name = 13;
  api.typesv2.common.Name mothers_name = 14;
  string designation = 15;
  google.type.Money monthly_income = 16;
  repeated References references = 17;
  api.typesv2.PostalAddress office_address = 18;
  google.protobuf.Timestamp nsdl_check_timestamp = 19;
  string nsdl_status = 20;
  google.protobuf.Timestamp customer_consent_timestamp = 21;
  google.protobuf.Timestamp bank_verification_timestamp = 22;
  string customer_bank_account_number = 23;
  string customer_bank_ifsc_code = 24;
  string kyc_type = 25;
  google.protobuf.Timestamp kyc_timestamp = 26;
  google.protobuf.Timestamp mandate_register_timestamp = 27;
  string end_use_of_loan = 28;
}

message SaveCommonDetailsResponse {
  rpc.Status status = 1;
}

message GetTransactionDetailsRequest {
  string account_id = 1;
  string deal_no = 2;
  enum RetrieveLevel {
    RETRIEVE_LEVEL_UNSPECIFIED = 0;
    /*
    When we need to retrieve basic transaction information.
    Returns loan level aggregate details like overdue, total charges etc.
     */
    RETRIEVE_LEVEL_BASIC = 1;
    /*
    Used when we need detailed transaction information.
    Installment level information is returned.
     */
    RETRIEVE_LEVEL_EXTENDED = 2;
  }
  RetrieveLevel retrieve_level = 3;
}

enum loanAccountStatus {
  LOAN_ACCOUNT_STATUS_UNSPECIFIED = 0;
  LOAN_ACCOUNT_STATUS_ACTIVE = 1;
  LOAN_ACCOUNT_STATUS_CLOSED = 2;
}

message GetTransactionDetailsResponse {
  rpc.Status status = 1;
  repeated RepaymentDetails repayment_details_list = 2; // structure of the repayment details of the user
  loanAccountStatus loan_account_status = 3;

  message RepaymentDetails {
    string repayment_id = 1; // installment id
    google.type.Date due_date = 2; // due date of any repayment
    google.type.Money principal = 3; // principal amount of any emi
    google.type.Money interest = 4; // interest amount of any emi
    PaymentStatus payment_status = 5;// describes whether the payment has been done or not
    enum PaymentStatus {
      PAYMENT_STATUS_UNSPECIFIED = 0;
      PAYMENT_STATUS_PAID = 1;
      PAYMENT_STATUS_UNPAID = 2;
      PAYMENT_STATUS_DEFAULT = 3;
    }
  }
}

message ImpsCallbackResponse {
  message Data {
    string ibl_ref_no = 1;
    string customer_ref_no = 2;
    string status_desc = 3;
    float amount = 4;
    string tran_type = 5;
    string status_code = 6;
    string utr = 7;
    string payment_date = 8;
    string imps_bene_name = 9;
    string rrr_ref_no = 10;
  }
  rpc.Status status = 1;
  Data data = 2;
}

message LatLongRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  google.type.LatLng lat_lng = 3;
  api.typesv2.PostalAddress address = 4;
}

message LatLongResponse {
  rpc.Status status = 1;
  string account_id = 2;
}

message DigilockerCreateProfileRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
}

message DigilockerCreateProfileResponse {
  rpc.Status status = 1;
  string capture_link = 2;
  string profile_id = 3;
  string partner_request_id = 4;
}

message DigilockerFetchProfileRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  string profile_id = 3;
}

message DigilockerFetchProfileResponse {
  rpc.Status status = 1;
  string profile_id = 2;
  repeated Task tasks = 3;
  string digi_aadhaar_xml = 4; // digilocker aadhaar xml base64 encoded

  message Task {
    api.typesv2.PostalAddress address = 1;
    int32 age = 2;
    google.type.Date dob = 3; // YYYY-MM-DD
    api.typesv2.DocumentType doc_type = 4;
    api.typesv2.Gender gender = 5; // "M", "F"
    api.typesv2.common.Name name = 6;
    string street_address_string = 7;
  }
}

message CreateLoanAgreementRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  api.typesv2.common.Name name = 3;
  string pan = 4;
  api.typesv2.common.PhoneNumber mobile = 5;
  google.type.Date dob = 7;
  google.type.Money loan_amount = 8;
  google.type.Money emi = 9;
  int32 tenure = 10;
  float roi = 11;
  api.typesv2.PostalAddress address = 12;
  string email = 13;
  google.type.Money processing_fee = 14;
  string bounce_charges = 15;
  string prepayment_charges = 16;
}

message CreateLoanAgreementResponse {
  rpc.Status status = 1;
  Data data = 3;
  message Data {
    string account_id = 1;
    // this field will receive base64
    string loan_agreement = 2;
  }
}
