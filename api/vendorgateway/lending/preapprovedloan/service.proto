syntax = "proto3";

package vendorgateway.lending.preapprovedloan;

import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/bank_account_details.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/gender.proto";
import "api/vendorgateway/lending/preapprovedloan/enums.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "google/type/postal_address.proto";


option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.preapprovedloan";

// PreApprovedLoans service to enable our domain service to connect with the vendors for doing all sorts of pre approved lending
service PreApprovedLoan {
  // RPC to check if a customer is eligible for a loan, if yes, get response with the offer
  rpc GetInstantLoanOffer (GetInstantLoanOfferRequest) returns (GetInstantLoanOfferResponse);

  // RPC to get OTP after getting the loan offer
  rpc GetInstantLoanOTP (GetInstantLoanOtpRequest) returns (GetInstantLoanOtpResponse);

  // RPC to submit loan application based on loan eligibility.
  rpc GetInstantLoanApplication (GetInstantLoanApplicationRequest) returns (GetInstantLoanApplicationResponse);

  // RPC to fetch the current status of a loan application request, identified by an Application ID
  rpc GetInstantLoanInfo (GetInstantLoanInfoRequest) returns (GetInstantLoanInfoResponse);

  // RPC to enable the user to remove this loan application
  rpc GetInstantLoanUnblock (GetInstantLoanUnblockRequest) returns (GetInstantLoanUnblockResponse);

  // RPC to enable the user to check the status of any Instant Loan APIs in general (redundant)
  rpc GetInstantLoanStatusEnquiry (GetInstantLoanStatusEnquiryRequest) returns (GetInstantLoanStatusEnquiryResponse);

  // RPC to fetch the remaining balance loan amount by sending the loan account number
  rpc GetInstantLoanBalance (GetInstantLoanBalanceRequest) returns (GetInstantLoanBalanceResponse);

  // RPC to enquire Payoff / Settlement amount for the given loan account number.
  rpc GetInstantLoanClosureEnquiry (GetInstantLoanClosureEnquiryRequest) returns (GetInstantLoanClosureEnquiryResponse);

  // RPC for user to close the loan account
  rpc CloseInstantLoanAccount (CloseInstantLoanAccountRequest) returns (CloseInstantLoanAccountResponse);

  // RPC to upload file on the vendor sftp server
  rpc UploadFile (UploadFileRequest) returns (UploadFileResponse);

  // RPC to fetch loan details
  // this call also fetches description of loan scheme, e.g. STF-HOUSING LOAN
  // can be added when all types of loans are supported
  rpc FetchLoanDetails (FetchLoanDetailsRequest) returns (FetchLoanDetailsResponse);

  // This API is disabled by Federal as per Digital Lending Guidelines
  // More info on the email thread: Epifi || Important Update: Sharing of Repayment Schedule with Customers
  rpc GetRepaymentSchedule (GetRepaymentScheduleRequest) returns (GetRepaymentScheduleResponse) {
    option deprecated = true;
  };

  // LoanAccountCreation API can be used to create a new loan account for the user in which loan amount will be disbursed
  rpc LoanAccountCreation (LoanAccountCreationRequest) returns (LoanAccountCreationResponse);

  // LoanAccountCreationEnquiry is used to get the account creation status of the user
  rpc LoanAccountCreationEnquiry (LoanAccountCreationEnquiryRequest) returns (LoanAccountCreationEnquiryResponse);

  // LoanDisbursement is used to make the request for disbursing the loan amount to the user
  rpc LoanDisbursement (LoanDisbursementRequest) returns (LoanDisbursementResponse);

  // GetLoanDisbursementEnquiry is used to check the status for disbursing the loan amount to the user
  rpc GetLoanDisbursementEnquiry (LoanDisbursementEnquiryRequest) returns (LoanDisbursementEnquiryResponse);

  // RPC to fetch if user is eligible for loan It also returns the offer details if the user is approved
  rpc CheckLoanEligibility (LoanEligibilityRequest) returns (LoanEligibilityResponse);
}

message CloseInstantLoanAccountRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  string request_id = 3;
  google.type.Date date = 4; // TODO(@prasoon): (DD-MM-YYYY) Update which date after more clarification
  string customer_account_number = 5; // Customer savings account for auto debit
  string loan_account_number = 6; // Customer loan account
  google.type.Money net_pay_amount = 7; // amount same as received from closure enquiry API response
  string pay_amount_id = 8; // ID same as received from closure enquiry API response
  string remarks = 9;
}

// Ack Response
message CloseInstantLoanAccountResponse {
  rpc.Status status = 1;
  string request_id = 2; //Unique ID for transaction
  string loan_account_number = 3;
  google.type.Money net_pay_amount = 4;
  string raw_response_action = 5; // Yet to be implemented by fed in Ack Resp
  string raw_response_reason = 6;
}

message GetInstantLoanClosureEnquiryRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  string account_number = 2;
  google.type.Date date = 3; // TODO(@prasoon): (DD-MM-YYYY) Update which date after more clarification
}

message GetInstantLoanClosureEnquiryResponse {
  enum Status {
    OK = 0;
    LOAN_ALREADY_CLOSED = 101;
  }
  rpc.Status status = 1;
  string account_number = 2;
  google.type.Date closure_date = 3;
  google.type.Money closure_amount = 4;
}

message GetInstantLoanBalanceRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  string reference_id = 2;
  string account_number = 3; // Loan Account Number
}

message GetInstantLoanBalanceResponse {
  rpc.Status status = 1;
  string account_number = 2;
  string account_status = 3;
  string account_type = 4;
  google.type.Money available_balance = 5;
  string balance_currency = 6;
  string customer_name = 7;
  google.type.Money ffd_balance = 8;
  google.type.Money float_balance = 9;
  google.type.Money ledger_balance = 10; // Used for balance check for loans
  string raw_reason = 11;
  string reference_id = 12;
  string raw_response = 13;
  google.type.Money user_defined_balance = 14;
}

message GetInstantLoanStatusEnquiryRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  string application_id = 2;
  ApiType api_type = 3;
}

message GetInstantLoanStatusEnquiryResponse {
  string application_id = 1;
  rpc.Status status = 2;
  string raw_response_code = 3;
  string raw_response_reason = 4;
}

message GetInstantLoanUnblockRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  string application_id = 2;
}

message GetInstantLoanUnblockResponse {
  string application_id = 1;
  rpc.Status status = 2;
  string raw_response_code = 3;
  string raw_response_reason = 4;
}

message GetInstantLoanInfoRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  string application_id = 2;
}

message GetInstantLoanInfoResponse {
  string application_id = 1;
  rpc.Status status = 2;
  string raw_response_code = 3;
  string raw_response_reason = 4;
  LoanState loan_state = 5;
}

message GetInstantLoanApplicationRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  string application_id = 2;
  string offer_id = 3;
  string otp = 4;
  api.typesv2.common.PhoneNumber phone_number = 5;
  string masked_account_number = 6;
  google.type.Money processing_fee = 7;
  string customer_device_ip = 8;
  google.type.Money loan_amount = 9;
  google.type.Money emi_amount = 10;
  double interest_rate = 11;
  int32 tenure_months = 12;
}

message GetInstantLoanApplicationResponse {
  string application_id = 1;
  rpc.Status status = 2;
  string raw_response_code = 3;
  string raw_response_reason = 4;
}

message GetInstantLoanOtpRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  string application_id = 2;
  string offer_id = 3;
  string otp_token = 4; // domain generated for federal usage
  api.typesv2.common.PhoneNumber phone_number = 5;
}

message GetInstantLoanOtpResponse {
  string application_id = 1;
  rpc.Status status = 2;
  string raw_response_code = 3;
  string raw_response_reason = 4;
  int64 otp = 5;
}

message GetInstantLoanOfferRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Pan and Phone number request method used to send to vendor API get from domain
  string pan = 2;
  api.typesv2.common.PhoneNumber phone_number = 3;
  string application_id = 4;
}

message GetInstantLoanOfferResponse {
  bool is_eligible = 1;
  string offer_id = 2;
  google.type.Money max_amount = 3;
  google.type.Money max_allowed_emi = 4;
  int32 max_tenure_months = 5;
  repeated RangeToFrom interest_rates = 6;
  repeated RangeToFrom processing_fee_percentages = 7;
  double service_tax_gst = 8;
  int64 expiry_timestamp = 9; //expiry date timestamp in seconds
  string application_id = 10;
  rpc.Status status = 11;
  string raw_response_code = 12;
  string raw_response_reason = 13;

  message RangeToFrom {
    google.type.Money range_from = 1;
    google.type.Money range_to = 2;
    Type type = 3;
    double value = 4;
  }

  enum Type {
    // Percentage type
    TYPE_PERCENTAGE = 0;
    // Value type
    TYPE_VALUE = 1;
    // Unspecified
    TYPE_UNSPECIFIED = 2;
  }
}

message UploadFileRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Remote path of vendor sftp server where file will be uploaded
  string remote_path = 2;
  // URL where the file is hosted and needs to be downloaded
  string file_url = 3;
}

message UploadFileResponse {
  rpc.Status status = 1;
}

message FetchLoanDetailsRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  string customer_id = 2;
  api.typesv2.common.PhoneNumber phone_number = 3;
}

message FetchLoanDetailsResponse {
  rpc.Status status = 1;
  repeated LoanDetails loan_details = 2;

  message LoanDetails {
    string account_number = 1;
    api.typesv2.common.PhoneNumber phone_number = 2;
    google.type.Money loan_amount = 3;
    LoanPeriod loan_period = 4;
    google.type.Date open_date = 5; // DD-MMM-YYYY
    google.type.Date end_date = 6; // DD-MMM-YYYY
    double interest_rate = 7;
    AccountType account_type = 8;
    google.type.Date next_pay_date = 9; // DD-MMM-YYYY
    google.type.Money next_pay_amount = 10;
    google.type.Money due_amount = 11;
    LoanStatus loan_status = 12;
    google.type.Money ledger_balance = 13;
  }

  message LoanPeriod {
    int32 days = 1;
    int32 months = 2;
  }

  enum AccountType {
    ACCOUNT_TYPE_UNSPECIFIED = 0;
    ACCOUNT_TYPE_LOANS = 1;
  }

  enum LoanStatus {
    LOAN_STATUS_UNSPECIFIED = 0;
    LOAN_STATUS_ACTIVE = 1;
    LOAN_STATUS_CLOSED = 2;
  }
}

message GetRepaymentScheduleRequest {
  vendorgateway.RequestHeader header = 1;
  string request_uuid = 2;
  string service_request_id = 3;
  google.protobuf.Timestamp message_datetime = 4;
  string account_number = 5;
  string sol_id = 6;
}

message GetRepaymentScheduleResponse {
  rpc.Status status = 1;
  repeated InstallmentInfo installment_infos = 2;

  message InstallmentInfo {
    google.type.Money combined_installment_amount = 1;
    google.type.Money cumulative_interest_amount = 2;
    google.type.Money cumulative_principal_amount = 3;
    google.protobuf.Timestamp flow_date = 4;
    google.type.Money installment_amount = 5;
    google.type.Money interest_amount = 6;
    google.type.Money principal_amount = 7;
    google.type.Money principal_outstanding_amount = 8;
    string serial_number = 9;
  }
}

message LoanAccountCreationRequest {
  vendorgateway.RequestHeader header = 1;
  string request_id = 2;
  string sol_id = 3;
  UserDetails user_details = 4;
  LoanDetails loan_details = 5;
  CredentialCategory credential_category = 6;
  // Unique Mandate Reference Number
  string UMRN = 7;
  // reference number returned by the loan eligibility API
  string bre_ref_num = 8;
  message UserDetails {
    string bre_ref_num = 1;
    string customer_id = 2;
    api.typesv2.common.Name customer_name = 3;
    string pan_no = 4;
    google.type.PostalAddress address = 5;
  }

  message LoanDetails {
    google.type.Money sanction_limit = 1;
    int32 loan_period_months = 2;
    int32 no_of_installment = 3;
    google.type.Money installment_amount = 4;
    double interest_rate = 5;
    google.type.Date value_date = 6;
    google.type.Money variable_processing_fee = 7;
    google.type.Money margin_money_amount = 8;
    google.type.Money interest_subvention_amount = 9;
    string operative_account = 10;
    int32 drawing_power = 11;
    string bc_loan_id = 12;
  }
}

message LoanAccountCreationResponse {
  rpc.Status status = 1;
  string request_id = 2;
  string category = 3;
  string customer_id = 4;
  string loan_account_number = 5;
  google.protobuf.Timestamp loan_created_time = 6;
  AccountCreationStatus account_creation_status = 7;

  enum AccountCreationStatus {
    ACCOUNT_CREATION_STATUS_UNSPECIFIED = 0;
    ACCOUNT_CREATION_STATUS_IN_PROGRESS = 1;
    ACCOUNT_CREATION_STATUS_SUCCESS = 2;
  }
}

message LoanAccountCreationEnquiryRequest {
  vendorgateway.RequestHeader header = 1;
  string request_id = 2;
  string loan_request_id = 3;
  CredentialCategory credential_category = 4;
}

message LoanAccountCreationEnquiryResponse {
  rpc.Status status = 1;
  string request_id = 2;
  string loan_number = 3;
  string customer_name = 4;
  google.protobuf.Timestamp loan_created_time = 5;
}

message LoanDisbursementRequest {
  vendorgateway.RequestHeader header = 1;
  string request_id = 2;
  string customer_id = 3;
  string loan_account_number = 4;
  google.type.Money loan_amount = 5;
  string operative_account_number = 6;
  api.typesv2.BankAccountDetails beneficiary_account_details = 7;
  string pennydrop_tran_id = 8;
  google.type.Date pennydrop_tran_date = 9;
  CredentialCategory credential_category = 10;
}

message LoanDisbursementResponse {
  rpc.Status status = 1;
  string reference_number = 2;
}

message LoanDisbursementEnquiryRequest {
  vendorgateway.RequestHeader header = 1;
  string request_id = 2;
  string loan_request_id = 3;
  CredentialCategory credential_category = 4;
}

message LoanDisbursementEnquiryResponse {
  rpc.Status status = 1;
  string reference_number = 2;
}

message LoanEligibilityRequest {
  vendorgateway.RequestHeader header = 1;
  LoanApplication loan_application = 2;
  string s_dedupe_id = 3;
}

message LoanApplication {
  string application_id = 1;
  google.type.Money loan_amount = 2;
  Borrower borrower = 3;
  ApplicantType applicant_type = 4;
}

enum ApplicantType {
  APPLICANT_TYPE_UNSPECIFIED = 0;
  APPLICANT_TYPE_ETB = 1;
  APPLICANT_TYPE_NTB = 2;
}

message Borrower {
  string pan = 1;
  api.typesv2.common.Name name = 2;
  string source_ip_v4 = 3;
  ContactDetails contact_details = 4;
  google.type.Date dob = 5;
  api.typesv2.Gender gender = 6;
}

message ContactDetails {
  api.typesv2.PostalAddress communication_address = 1;
  api.typesv2.PostalAddress permanent_address = 2;
  string email = 3;
  api.typesv2.common.PhoneNumber phone = 4;
}

message LoanEligibilityResponse {
  string reference_id = 1;
  ResponseDetails response_details = 4;
  rpc.Status status = 5;
  message ResponseDetails {
    string response_code = 1;
    string response_desc = 2;
    Response response = 3;
  }

  message Response {
    string eligibility_status = 1;
    EligibilityDecision eligibility_decision = 2;
    google.type.Money approved_amount = 3;
    string cibil_status = 4;
    uint32 cibil_score = 5;
    string fraud_check_decision = 6;
    string fraud_check_status = 7;
    //ScoringResponseWithCoapp scoring_response_with_coapp = 8;
    double roi = 8;
    google.type.Money emi = 9;
    google.type.Money max_eligible_emi = 10;
    double processing_fee_percentage = 11;
    bytes raw_rejection_reasons = 12;
  }
}

enum EligibilityDecision {
  ELIGIBILITY_DECISION_UNSPECIFIED = 0;
  ELIGIBILITY_DECISION_ELIGIBLE = 1;
  ELIGIBILITY_DECISION_NOT_ELIGIBLE = 2;
}

//message ScoringResponseWithCoapp {
//  google.type.Date response_date = 1;
//  Summary summary = 2;
//  ApplicantResult applicant_result = 3;
//}

//message Summary {
//  string application_decision = 1;
//  google.type.Money application_approved_amount = 2;
//  string status = 3;
//}

//message ApplicantResult {
//  google.type.Date response_date = 1;
//  string scoring_ref_id = 2;
//  string status = 3;
//  string policy_name = 4;
//  string policy_id = 5;
//  string decision = 6;
//  google.type.Money eligibility_amount = 7;
//  string eligibility_decision = 8;
//  string applicant_id = 9;
//  google.type.Money eligibility_approved_amount = 10;
//  EligibilityResponse eligibility_response = 11;
//  repeated Rule rules = 12;
//  DerivedFields derived_fields = 13;
//}

//message Rule {
//  int64 criteria_id = 1;
//  string rule_name = 2;
//  Values values = 3;
//  string outcome = 4;
//  string remark = 5;
//  string exp = 6;
//  message Values {
//    int64 cibil_score = 1;
//  }
//}



//message DerivedFields {
//  string policy_name = 1;
//  int32 policy_id = 2;
//  string constant_fields_current_date = 3;
//  string custom_fields_dpd30plus_3months_it = 4;
//  string custom_fields_dpd90_plus_12mon_it = 5;
//  int32 custom_fields_pb_pf_rate = 6;
//  int32 custom_fields_experian_score = 7;
//  string custom_fields_writeoff_stlmt_suit_fld_18m = 8;
//  int32 custom_fields_max_income_to_be_considered = 9;
//  int32 custom_fields_pb_interest_rate = 10;
//  string custom_fields_active_npa_check = 11;
//  int32 calculated_fields_max_tenure = 12;
//}
//
//message EligibilityResponse {
//  string eligibility_id = 1;
//  google.type.Money approved_amount = 2;
//  string decision = 3;
//  string remark = 4;
//  string eligibility_name = 5;
//  //  repeated MatchedEligibility matched_eligibility = 6; //DON'T HAVE ANY DATA FOR IT
//}

//message MatchedEligibility {
//  Values values = 1;
//  string eligibility_id = 2;
//  int64 grid_id = 3;
//  string decision = 4;
//  string compute_disp = 5;
//  string compute_logic = 6;
//  google.type.Money max_amount = 7;
//  google.type.Money min_amount = 8;
//  int64 dp = 9;
//  int64 max_tenor = 10;
//  string remark = 11;
//  google.type.Money computed_amount = 12;
//  google.type.Money eligibility_amount = 13;
//  int64 cnt = 14;
//  string grid_exp = 15;
//  int64 mul_prod = 16;
//  string eligibility_name = 17;
//  AdditionalFields additional_fields = 18;
//  int64 dt_submit = 19;
//  message Values {
//    int64 foir_cc = 1;
//  }
//}

//message AdditionalFields {
//  string max = 1;
//}

