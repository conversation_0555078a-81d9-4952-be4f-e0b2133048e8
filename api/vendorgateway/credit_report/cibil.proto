//go:generate gen_sql -types=AtlasGetAuthQuestionsResponse
syntax = "proto3";

package vendorgateway.credit_report;

import "api/rpc/status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/credit_report/enums.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/credit_report";
option java_package = "com.github.epifi.gamma.api.vendorgateway.credit_report";

message AtlasPingRequest {
  vendorgateway.RequestHeader header = 1;
  string client_key = 2;
  string request_key = 3;
}

message AtlasPingResponse {
  rpc.Status status = 1;
  string response_key = 2;
}

message AtlasFulfillOfferRequest {
  vendorgateway.RequestHeader header = 1;
  string client_key = 2;
  string request_key = 3;
  UserDetails user_details = 4;

  message UserDetails {
    api.typesv2.common.Name name = 1;
    string email = 2;
    api.typesv2.common.PhoneNumber phone_number = 3;
    string pan = 4;
    google.type.Date date_of_birth = 5;
  }
}

message AtlasFulfillOfferResponse {
  enum Status {
    // Returned an success
    OK = 0;
    // denotes that either first name or last name was missing in the request.
    INVALID_ARGUMENT_INCOMPLETE_USER_NAME = 101;
    // denotes that customer already exists with different client_user_key, use that.
    CONFLICT_SSN_EXISTS = 102;
    // denotes that report is not found for user, it is a permanent failure.
    NOT_FOUND_NO_HIT = 103;
  }
  rpc.Status status = 1;
  string response_key = 2;
  AtlasResponseStatus vendor_status = 3;
  AtlasErrorResponse atlas_error_response = 4;
}

message AtlasGetAuthQuestionsRequest {
  vendorgateway.RequestHeader header = 1;
  string client_key = 2;
  string request_key = 3;
}

message AtlasGetAuthQuestionsResponse {
  rpc.Status status = 1;
  string response_key = 2;
  string challenge_configuration_id = 3;
  AtlasAuthQuestion auth_question = 4;
  AtlasAuthQueue question_type = 5;
  AtlasErrorResponse atlas_error_response = 6;
  google.protobuf.Timestamp time_of_response = 7;
}

message AtlasAuthQuestion {
  AnswerChoice answer_choice = 1;
  bool resend_eligible = 2;
  bool last_chance_question = 3;
  bool skip_eligible = 4;
  string full_question_text = 5;
  string key = 6;
}

message AnswerChoice {
  string display_text = 1;
  string key = 2;
  string choice_id = 3;
}

message AtlasVerifyAuthAnswersRequest {
  vendorgateway.RequestHeader header = 1;
  string client_key = 2;
  string request_key = 3;
  string challenge_configuration_id = 4;
  string question_key = 5;
  string answer_key = 6;
  oneof answer_specific_input {
    bool resend_otp = 7;
    bool skip_question = 8;
    string user_input = 9;
  }
}

message AtlasVerifyAuthAnswersResponse {
  rpc.Status status = 1;
  string response_key = 2;
  AtlasResponseStatus vendor_response_status = 3;
  AtlasErrorResponse atlas_error_response = 4;
  google.protobuf.Timestamp time_of_response = 5;
}

message AtlasGetCustomerAssetsRequest {
  vendorgateway.RequestHeader header = 1;
  string client_key = 2;
  string request_key = 3;
}

message AtlasGetCustomerAssetsResponse {
  rpc.Status status = 1;
  string response_key = 2;
  bytes raw_report = 3;
  AtlasErrorResponse atlas_error_response = 4;
}

message AtlasGetProductWebUrlRequest {
  vendorgateway.RequestHeader header = 1;
  string client_key = 2;
  string request_key = 3;
}

message AtlasGetProductWebUrlResponse {
  rpc.Status status = 1;
  string response_key = 2;
  string web_url = 3;
  AtlasErrorResponse atlas_error_response = 4;
}

message AtlasErrorResponse {
  oneof Response {
    AtlasFailureResponse atlas_failure_response = 1;
    AtlasServiceErrorResponse atlas_service_error_response = 2;
  }
}

message AtlasFailureResponse {
  string ie_transaction_id = 1;
  string message = 2;
  AtlasFailureEnum failure_enum = 3;
  string service_user_id = 4;
  string btx_ref_key = 5;
  string time_period = 6;
  string second_failure_sch_id = 7;
  string severity = 8;
  string failure_sch_id = 9;
  string customer_id = 10;
  string client_user_key = 11;
  string client_refresh_url = 12;
}

message AtlasServiceErrorResponse {
  string error_detail = 1;
  AtlasErrorStatus error_status = 2;
  string error_message = 3;
  string failure = 4;
  string application_name = 5;
  string method_name = 6;
  string method_argument = 7;
  string method_response = 8;
  string primary_entity_id = 9;
  string primary_entity_name = 10;
  string secondary_entity_id = 11;
  string secondary_entitiy_name = 12;
}
