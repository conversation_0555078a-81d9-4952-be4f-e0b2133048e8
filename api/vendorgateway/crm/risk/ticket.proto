syntax = "proto3";

package vendorgateway.crm.risk;

import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/sort.proto";
import "api/vendorgateway/crm/risk/enums.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/crm/risk";
option java_package = "com.github.epifi.gamma.api.vendorgateway.crm.risk";

message RiskTicket {
  // Unique ID of the ticket
  uint64 id = 1;

  // this field is mandatory for creating a ticket
  string unique_external_user_id = 2;
  // As part of case workflow, some form of communication may be expected b/w risk analyst
  // and the user. Communication can either be over phone or email and will be populated only when
  // needed at CRM's end.
  // e.g., when details like salary proof are required from user
  UserDetails user_details = 3;
  // ID of the user contact created in crm, we will need this id to fetch other users details present in crm
  uint64 user_contact_id = 4;

  // Subject line of the ticket
  // Optional field
  string subject = 5;
  // Content of the description in plain text
  // Optional field
  string description_text = 6;

  // As part of training agents or assessing rules, sample tickets may be generated
  // This flag helps distinguish sample tickets vs live tickets
  api.typesv2.common.BooleanEnum is_sample = 7;
  // Set to true if the ticket has been deleted/trashed. Deleted tickets will not be displayed in any views except the "deleted" filter
  api.typesv2.common.BooleanEnum is_deleted = 8;

  // Priority of the ticket
  // By default it will be set to low if not passed while creating the ticket
  Priority priority = 9;
  // Status of the ticket
  // Status will be set to CREATED by default if not passed while creating a ticket
  Status status = 10;
  ReviewType review_type = 11;
  // Risk tickets are either manually reviewed vs auto reviewed
  // This field indicates the verdict of the ticket post the review
  Verdict verdict = 12;

  // agent group to which ticket is assigned to
  // default field in crm
  AgentGroup agent_group = 13;
  // crm contact/agent ID of the agent to whom the ticket has been assigned
  uint64 assigned_agent_id = 14;

  // We will be using it to add tags required for assignment like
  // Rule names, Batch names, Queue types etc
  repeated string tags = 15;

  google.protobuf.Timestamp created_at = 16;
  google.protobuf.Timestamp updated_at = 17;

  // Ticket will be marked on sleep/hold till expiry and will have limited visibility
  // Ticket will be unsnoozed when either status or assigned agent changes.
  google.protobuf.Timestamp snoozed_till = 18;

  // confidence score of the case (0 - 100)
  float confidence_score = 19 [(validate.rules).float = {gte: 0, lte: 100}];

  // it defines which user is worked on the ticket previously. It will help in determining who we should assign the
  // ticket once it moved back from out-call or other flow. It typically happens when case is moved around to
  // different agents in its lifecycle.
  string last_assigned_analyst_email = 20;

  // new fields to be added to riskticket
  float cf_model1_score = 21;
  string cf_model1_name = 22;
  float cf_model2_score = 23;
  string cf_model2_name = 24;

  string model_selected = 25;

}

message UserDetails {
  string email = 1;

  api.typesv2.common.PhoneNumber phone_number = 2;

  api.typesv2.common.Name name = 3;
}

//filters for risk ticket list
message RiskFilters {
  google.protobuf.Timestamp updated_at = 1;

  //agent email
  string assigned_to_email = 2;

  repeated Status statuses = 3;

  repeated Priority priorities = 4;

  repeated ReviewType review_types = 5;

  uint64 agent_id = 6;

  // True - if response should have only snoozed tickets
  // False - If response should not have snoozed tickets
  // Unspecified - If response should include both
  api.typesv2.common.BooleanEnum snoozed = 7;

  // upper(included) and lower(excluded) limit of confidence score
  NumericRange confidence_score_range = 8;

  // ticket created at timestamp range (from, to]
  TimestampRange created_at_range = 9;

  repeated string tags = 10;

  TimestampRange updated_at_range = 11;
}

message NumericRange {

  float lower = 1;

  float upper = 2;
}

message TimestampRange {
  // Exclusive from timestamp.
  google.protobuf.Timestamp from = 1;
  // Inclusive to timestamp.
  google.protobuf.Timestamp to = 2;
}

message SortableRiskFilters {
  // filters options should include all filters that need to applied
  repeated FilterOption filter_options = 1;

  string unique_external_user_id = 2;

  // ticket field on which all tickets will be sorted
  RiskTicketFieldMask sort_by_ticket_field = 3 [(validate.rules).enum = {not_in: [0]}];

  api.typesv2.common.SortOrder order = 4 [(validate.rules).enum = {not_in: [0]}];
}

enum RiskTicketFieldMask {
  RISK_TICKET_FIELD_MASK_UNSPECIFIED = 0;
  RISK_TICKET_FIELD_MASK_CREATED_AT = 1;
  RISK_TICKET_FIELD_MASK_UPDATED_AT = 2;
}

enum FilterOption {
  FILTER_OPTION_UNSPECIFIED = 0;
  FILTER_OPTION_UNIQUE_EXTERNAL_ID = 1;
}
