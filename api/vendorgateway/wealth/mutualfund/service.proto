syntax = "proto3";

package vendorgateway.wealth.mutualfund;

import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/wealth/mutualfund/fatca.proto";
import "api/vendorgateway/wealth/mutualfund/elog.proto";
import "api/vendorgateway/wealth/mutualfund/nft.proto";
import "api/vendorgateway/wealth/mutualfund/obsolete_fund.proto";
import "api/vendorgateway/wealth/mutualfund/order_feed_status.proto";
import "api/vendorgateway/wealth/mutualfund/order_feed.proto";
import "api/rpc/status.proto";
import  "google/type/money.proto";
import "api/typesv2/nominee.proto";
import "api/typesv2/common/phone_number.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "api/vendors/mfcentral/get_cas_document.proto";
import "api/vendors/mfcentral/get_transaction_status.proto";


option go_package = "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund";

// MutualFund Service is used for communicating with an RTA for all MutualFund related use cases
// It supports the following use cases
//  * Processing Forward Order Feed File
//  * Processing Order Feed File
//  * Processing FATCA file
//  * Processing Elog Submission Files
//  * Get Status of uploaded Order Feed File
service MutualFund {
  // ProcessOrderFeedFile rpc processes the order feed file and sends to the RTA. This API takes in a Vendor
  // and a file_path of the order feed file in the respective s3 bucket as input. Based on the Vendor, a decision is
  // made on which API needs to be invoked and the file is downloaded from the s3 bucket and is processed into the
  // format that is expected by the vendor.
  rpc ProcessOrderFeedFile(ProcessOrderFeedFileRequest) returns (ProcessOrderFeedFileResponse);

  // ProcessFATCAFile rpc processes the FATCA file and sends to the RTA. This API takes in a Vendor
  // and a file_path of the FATCA file in the respective s3 bucket as input. Based on the Vendor, a decision is
  // made on which API needs to be invoked and the file is downloaded from the s3 bucket and is processed into the
  // format that is expected by the vendor.
  rpc ProcessFATCAFile(ProcessFATCAFileRequest) returns (ProcessFATCAFileResponse);

  // ProcessElogFile rpc processes the Elog file and sends to the RTA. This API takes in a Vendor
  // and a file_path of the Elog file in the respective s3 bucket as input. Based on the Vendor, a decision is
  // made on which API needs to be invoked and the file is downloaded from the s3 bucket and is processed into the
  // format that is expected by the vendor.
  rpc ProcessElogFile(ProcessElogFileRequest) returns (ProcessElogFileResponse);

  // GetOrderFeedFileStatus queries the RTA for knowing the status of an already uploaded order feed file.
  // This API takes in a Vendor, a file_name and a report_type of the Order Feed file that was already uploaded.
  // report_type is a filter for filtering out the orders from the order feed file by their processed_status.
  // Based on the Vendor, a decision is made on which API needs to be invoked and a query is done to the RTA for
  // fetching the status of the uploaded file.
  rpc GetOrderFeedFileStatus(GetOrderFeedFileStatusRequest) returns (GetOrderFeedFileStatusResponse);

  rpc ProcessOrderFeedFileSync(ProcessOrderFeedFileSyncRequest) returns (ProcessOrderFeedFileSyncResponse);

  // ProcessNFTFile  processes the NFT file and sends to the RTA. This API takes in a Vendor
  // and a file_path of the NFT file in the respective s3 bucket as input. Based on the Vendor, a decision is
  // made on which API needs to be invoked and the file is downloaded from the s3 bucket and is processed into the
  // format that is expected by the vendor.
  rpc ProcessNFTFile(ProcessNFTFileRequest) returns (ProcessNFTFileResponse);

  // GetFolioDetails takes in a folioID, amc and vendor and fetches the information stored for a folio by the vendor
  // and returns the same data.
  rpc GetFolioDetails(GetFolioDetailsRequest) returns (GetFolioDetailsResponse);

  // GetMfHistoricalNavs return all historical nav amounts for the given isin in the given time range
  rpc GetMfHistoricalNavs(GetMfHistoricalNavsRequest) returns (GetMfHistoricalNavsResponse);

  // GetObsoleteFunds returns all the funds which are obsolete for eg.
  // Liquidated Funds: A fund liquidation occurs when a fund closes down its operations completely, sells off its assets and generally distributes substantially all of
  // its assets in cash to its shareholders
  // Merged Funds: When two funds are merged, the assets of one fund are transferred to the assets of another,
  // The transferor fund then ceases to exist.
  rpc GetObsoleteFunds(GetObsoleteFundsRequest) returns (GetObsoleteFundsResponse);

  rpc UpdateNomineeDetails(UpdateNomineeDetailsRequest) returns (UpdateNomineeDetailsResponse);
  // UpdateFolioEmail api initiates the process for updating email id linked given list of folios
  rpc UpdateFolioEmail(UpdateFolioEmailRequest) returns (UpdateFolioEmailResponse);
  // UpdateFolioMobile api initiates the process for updating mobile number linked given list of folios.
  rpc UpdateFolioMobile(UpdateFolioMobileRequest) returns (UpdateFolioMobileResponse);
  // Submit and verify OTP for any of the mf central apis such as CAS Summary, CAS detailed, Lien marking, NFT apis etc.
  rpc VerifyMfCentralOtp(VerifyMfCentralOtpRequest) returns (VerifyMfCentralOtpResponse);
  // Get CAS document for a customer. This can either return CAS Detailed response or CAS Summary response based on the which api is used to trigger OTP SubmitCasSummary or SumbitCasDetailed.
  rpc GetCasDocument(GetCasDocumentRequest) returns (GetCasDocumentResponse);
  // Submit details for user validation for getting user MF CAS summary
  rpc SubmitCasSummary(SubmitCasSummaryRequest) returns (SubmitCasSummaryResponse);
  // Get transaction status for particular request id.
  rpc GetTransactionStatus(GetTransactionStatusRequest) returns (GetTransactionStatusResponse);

}

message GetMfHistoricalNavsRequest {
  vendorgateway.RequestHeader header = 1;
  // unique identifier for the mutual fund
  string isin = 2 [deprecated = true];
  // unique id used by morning star for identifying a mutual fund
  string mstar_id = 6;
  // start date from which the nav details are requested
  google.protobuf.Timestamp start_date = 3;
  // end date upto which the nav details are requested
  google.protobuf.Timestamp end_date = 4;
  // do not populate this field, it will be fetched and populated by vg rpc impl
  string access_code = 5;
}

message GetMfHistoricalNavsResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  repeated MfNavDetails mf_nav_details = 2;
}

message MfNavDetails {
  string isin = 1;
  string mstar_id = 4;
  google.type.Money nav = 2;
  google.protobuf.Timestamp nav_date = 3;
}

message ProcessOrderFeedFileRequest {
  vendorgateway.RequestHeader header = 1;
  string file_path = 2;
}

message ProcessOrderFeedFileResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    TRANSIENT_FAILURE = 100;
    PERMANENT_FAILURE = 101;
  }
  rpc.Status status = 1;
  string file_name = 2;
  string reference_number = 3;
  string result = 4;
}

message ProcessFATCAFileRequest {
  vendorgateway.RequestHeader header = 1;
  string file_path = 2;
}

message ProcessFATCAFileResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    TRANSIENT_FAILURE = 100;
    PERMANENT_FAILURE = 101;
  }
  rpc.Status status = 1;
  string file_name = 2;
  string reference_number = 3;
  int32 total_records = 4;
  int32 success_records = 5;
  int32 error_records = 6;
  repeated ProcessFATCAFileResult process_fatca_file_result = 7;
}

message ProcessElogFileRequest {
  vendorgateway.RequestHeader header = 1;
  string file_path = 2;
}

message ProcessElogFileResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    TRANSIENT_FAILURE = 100;
    PERMANENT_FAILURE = 101;
  }
  rpc.Status status = 1;
  string file_name = 2;
  string reference_number = 3;
  int32 total_records = 4;
  int32 success_records = 5;
  int32 error_records = 6;
  repeated ProcessElogFileResult process_elog_file_result = 7;
}

enum OrderFeedFileStatusReportType {
  STATUS_UNSPECIFIED = 0;
  SUCCESS = 1; // Filter all the Successful orders from the order feed file
  FAILURE = 2; // Filter all the Failed orders from the order feed file
  PENDING = 3; // Filter all the Pending orders from the order feed file
  ALL = 4;     // Return all orders irrespective of their status
}

message GetOrderFeedFileStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string file_name = 2;
  OrderFeedFileStatusReportType report_type = 3;
}

message GetOrderFeedFileStatusResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  string file_name = 2;
  string reference_number = 3;
  int32 row_count = 4;
  int32 processed_records = 5;
  int32 rejected_records = 6;
  int32 pending_records = 7;
  repeated GetOrderFeedFileStatusResult get_order_feed_file_status_result = 8;
}

message ProcessOrderFeedFileSyncRequest {
  vendorgateway.RequestHeader header = 1;
  string file_path = 2;
  bool isLastAttempt = 3;
  bool enable_karvy250_feed = 4;
}
message ProcessOrderFeedFileSyncResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    TRANSIENT_FAILURE = 100;
  }

  rpc.Status status = 1;
  string file_name = 2;
  string reference_number = 3;
  int32 total_records = 4;
  int32 success_records = 5;
  int32 error_records = 6;
  repeated ProcessOrderFeedFileSyncResult process_order_feed_file_sync_result = 7;
}

message ProcessNFTFileRequest {
  vendorgateway.RequestHeader header = 1;
  string file_path = 2;
  // Acknowledgement_images_files are documents in which there is an undertaking from the customer that they allow change of details in their folio.
  // This is mandatory for an rta to process NFT transactions.
  repeated string acknowledgement_images_file_paths = 3;
}

message ProcessNFTFileResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    TRANSIENT_FAILURE = 100;
    PERMANENT_FAILURE = 101;
  }

  rpc.Status status = 1;
  string file_name = 2;
  string reference_number = 3;
  int32 total_records = 4;
  int32 success_records = 5;
  int32 error_records = 6;
  repeated ProcessNFTFileResult process_nft_file_result = 7;
}


message GetFolioDetailsRequest {
  vendorgateway.RequestHeader header = 1;

  string amc_code = 2;
  string folio_id = 3;
  string pan_number = 4;
}
message GetFolioDetailsResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    TRANSIENT_FAILURE = 100;
    PERMANENT_FAILURE = 101;
  }
  rpc.Status status = 1;

  repeated string mobile_numbers = 2;
  repeated string email_ids = 3;
}


message MorningStarAccessTokenResponse {
  rpc.Status status = 1;
  string token = 2;
  google.protobuf.Timestamp expires_at = 3;
}


message GetObsoleteFundsRequest{
  vendorgateway.RequestHeader header = 1;
  ObsoleteFundType obsolete_fund_type = 2;
}

message GetObsoleteFundsResponse{
  enum Status {
    OK = 0;
    INTERNAL = 13;
    TRANSIENT_FAILURE = 100;
    PERMANENT_FAILURE = 101;
  }
  rpc.Status status = 1;
  repeated ObsoleteFund obsolete_funds = 2;
}

enum ObsoleteFundType{
  ObsoleteFundType_UNSPECIFIED = 0;
  LIQUIDATED = 1;
  MERGED = 2;
}

message UpdateNomineeDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  repeated NomineeUpdateForFolioData nominee_update_data = 2;
}

message UpdateNomineeDetailsResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  repeated NomineeUpdateForFolioStatus nominee_update_for_folio_status = 2;
}

message NomineeUpdateForFolioData {
  string folio_number = 1;
  string amc_code = 2;
  api.typesv2.common.PhoneNumber mobile_number = 3;
  string vendor_request_id = 4;
  string pan_number = 5;
  oneof NomineeData {
    bool nominee_opt_out_flag = 6;
    NomineeDetails nominee_details = 7;
  }
}

message NomineeDetails {
  repeated api.typesv2.Nominee nominees = 1;
}

message NomineeUpdateForFolioStatus {
  enum Status {
    Status_UNSPECIFIED = 0;
    Status_SUCCESS = 1;
    Status_FAILURE = 2;
  }

  enum FailureReason {
    FailureReason_UNSPECIFIED = 0;
    FailureReason_UNKNOWN = 1;
    FailureReason_INVALID_ARN_CODE = 2;
    FailureReason_EMPTY_NOMINEE_DETAILS_FOR_OPT_IN = 3;
    FailureReason_NOMINEE_DETAILS_PRESENT_FOR_OPT_OUT = 4;
    FailureReason_INVALID_TOKEN = 5;
    FailureReason_TOKEN_EXPIRED = 6;
    FailureReason_VALIDATION_FAILURE = 7;
  }

  string folio_number = 1;
  string amc_code = 2;
  Status status = 3;
  FailureReason failure_reason = 4;
}

message UpdateFolioEmailRequest {
  vendorgateway.RequestHeader header = 1;
  string client_ref_no = 2;
  string pan = 3;
  string pekrn = 4;
  oneof source_identifier {
    string email = 5;
    api.typesv2.common.PhoneNumber mobile = 6;
  }
  repeated UpdateFolioEmailData update_folios_email = 7 [(validate.rules).repeated.min_items = 1];
  string source = 8;
}

message UpdateFolioEmailResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  string client_ref_no = 2;
  int64 request_id = 3;
  string otp_reference_id = 4;
  string user_subject_reference = 5;
}

message UpdateFolioMobileRequest {
  vendorgateway.RequestHeader header = 1;
  string client_ref_no = 2;
  string pan = 3;
  string pekrn = 4;
  oneof source_identifier {
    string email = 5;
    api.typesv2.common.PhoneNumber mobile = 6;
  }
  repeated UpdateFolioMobileData update_folios_mobile = 7 [(validate.rules).repeated.min_items = 1];
  string source = 8;
}

message UpdateFolioMobileResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  string client_ref_no = 2;
  int64 request_id = 3;
  string otp_reference_id = 4;
  string user_subject_reference = 5;
}

message VerifyMfCentralOtpRequest {
  vendorgateway.RequestHeader header = 1;
  string client_ref_no = 2;
  int64 request_id = 3;
  string user_subject_reference = 4;
  string otp_reference = 5;
  string entered_otp = 6;
}

message VerifyMfCentralOtpResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
    INCORRECT_OTP_ENTERED = 102;
    PENDING_PREVIOUS_REQUEST = 103;
  }
  rpc.Status status = 1;
  string client_ref_no = 2;
  int64 request_id = 3;
  string user_subject_reference = 4;
  string otp_reference = 5;
}

message UpdateFolioMobileData {
  string amc = 1;
  string folio_number = 2;
  api.typesv2.common.PhoneNumber new_mobile = 3;
  string mobile_relationship = 4;
}

message UpdateFolioEmailData {
  string amc = 1;
  string folio_number = 2;
  string new_email = 3;
  string email_relationship = 4;
}
message GetCasDocumentRequest {
  vendorgateway.RequestHeader header = 1;
  string client_ref_no = 2;
  // Needs to be same as received from investor consent.
  string req_id = 3;
}

message GetCasDocumentResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;
  oneof CASDocumentResposneEntity{
    CASSummaryRes cas_summary_res = 2;
  }
}

message CASSummaryRes{
  repeated vendors.mfcentral.CASSummaryData data = 1;
  repeated vendors.mfcentral.Portfolio portfolios = 2;
  vendors.mfcentral.CASInvestorDetails investor_details = 3;
  string statement_holding_filter = 4;
}
message SubmitCasSummaryRequest {
  RequestHeader header = 1;
  // client ref no is sent for callback to particular request in InvestorConsent AND GetCasDocument API.
  string client_ref_no = 2;
  string pan = 3;
  string pekrn = 4;
  oneof source_identifier {
    api.typesv2.common.PhoneNumber mobile = 5;
    string email = 6;
  }
  bool single_otp_flow = 7;
}

message SubmitCasSummaryResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    INVALID_ARGUMENT = 3;
    DUPLICATE_CLIENT_REFERENCE_NUMBER = 103;
    INVALID_PAN_MOBILE_COMBINATION = 104;
  }
  rpc.Status status = 1;
  int64 req_id = 2;
  // otp_ref and user_subject_reference needs to be sent in investor consent API.
  string otp_ref = 3;
  string user_subject_reference = 4;
}
message GetTransactionStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string client_ref_no = 2;
  string req_id = 3;
  string pan = 4;
  string pekrn = 5;
  oneof source_identifier{
    api.typesv2.common.PhoneNumber mobile = 6;
    string email = 7;
  }
}

message GetTransactionStatusResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;
  repeated vendors.mfcentral.TransactionDetails pending = 2;
  repeated vendors.mfcentral.TransactionDetails success = 3;
  repeated vendors.mfcentral.TransactionDetails failure = 4;
}
