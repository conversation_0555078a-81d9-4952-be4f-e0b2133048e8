syntax = "proto3";

package vendorgateway.wealth.mutualfund;

option go_package = "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund";

message ProcessOrderFeedFileSyncResult {
  // Deprecated. Please use the value of the `status` enum.
  string code = 1 [deprecated = true];
  int32  record_number = 2;
  string message = 3;
  string vendor_reference_number = 4;
  ProcessOrderFeedFileFailureReason failure_reason = 5;
  enum Status {
    UNSPECIFIED = 0;
    SUCCESS = 1;
    FAILURE = 2;
  }
  Status status = 6;
}

enum ProcessOrderFeedFileFailureReason {
  PROCESS_ORDER_FEED_FILE_FAILURE_REASON_UNSPECIFIED = 0;
  PROCESS_ORDER_FEED_FILE_FAILURE_REASON_MOBILE_NUMBER_MISMATCH = 1;
  // Nominee age should be Greater than or equal to 18
  // CAMS response code : 146
  //
  // Nominee is not allowed for Minor
  // Karvy response code : 0070
  NOMINEE_AGE_IS_LESS_THAN_18 = 2;
  // The Transaction is not permitted under the curreent setup rules
  // CAMS response code : 126
  // Investor date of birth is less than 18 years
  INVESTOR_DOB_LESS_THAN_18_YEARS = 3;
  // Rejected due to Minimum Amount condition failed
  // KARVY code : 0098
  MINIMUM_AMOUNT_VALIDATION_FAILED = 4;
  // Missing Units or Amount for Partial Redemption or Switch transaction
  // KARVY code : 0099
  MISSING_UNITS_OR_AMOUNT_FOR_REDEMPTION_OR_SWITCH = 5;

  // SIP failures
  // Invalid or Missing SIP_NEW flag
  // Karvy code: 0019
  SIP_NEW_FLAG_MISSING = 30;

  // Rejected due to nil units
  NO_UNITS_PRESENT_WITH_AMC = 31;
}
