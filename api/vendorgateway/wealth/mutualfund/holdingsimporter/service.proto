syntax = "proto3";

package vendorgateway.wealth.mutualfund.holdingsimporter;

import "api/vendorgateway/request_header.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/holdingsimporter";

service HoldingImporter {
  // CreateTransaction API, creates a transaction at vendor and gets back a transaction-id as the response.
  // For all the further calls for an actor, vendor expects this transaction id to be passed.
  rpc CreateTransaction(CreateTransactionRequest) returns (CreateTransactionResponse);

  // InitiateHoldingsImport performs the steps needed to Initiate data fetch required by the vendor
  // For eg: For small-case vendor, this does a generate otp call.
  rpc InitiateHoldingsImport(InitiateHoldingsImportRequest) returns (InitiateHoldingsImportResponse);

  // TriggerHoldingsImportFetchRequest triggers an async data fetch for importing holdings.
  rpc TriggerHoldingsImportFetch(TriggerHoldingsImportFetchRequest) returns (TriggerHoldingsImportFetchResponse);

  // VerifyAndDecrypt verifies and decrypts the payload with a given signature, and returns the decrypted response.
  rpc VerifyAndDecrypt(VerifyAndDecryptRequest) returns (VerifyAndDecryptResponse);

  // EncryptAndSign encrypts and signs tha payload which can be used for making mfcentral api calls.
  rpc EncryptAndSign(EncryptAndSignRequest) returns (EncryptAndSignResponse);
}

message CreateTransactionRequest {
  vendorgateway.RequestHeader header = 1;
  string actor_id = 2;
}

message CreateTransactionResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    TRANSIENT_FAILURE = 100;
    PERMANENT_FAILURE = 101;
  }
  rpc.Status status = 1;
  string transaction_id = 2;
  google.protobuf.Timestamp expire_at = 3;
}

message InitiateHoldingsImportRequest {
  vendorgateway.RequestHeader header = 1;

  // generated by vendor in the transaction_id api.
  // this is mandatory for small-case.
  string transaction_id = 2;
  // generated by be to uniquely identify a holdings import request.
  // the same client reference number should be passed in the TriggerHoldingsImportFetchRequest
  string client_reference_number = 3;
  string pan_number = 4;
  string mobile_number = 5;
  string actor_id = 6;
  string email_id = 7;
  // use single otp flow for statement fetch
  bool single_otp_flow = 8;
}

message InitiateHoldingsImportResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    TRANSIENT_FAILURE = 100;
    PERMANENT_FAILURE = 101;
    DUPLICATE_CLIENT_REFERENCE_NUMBER = 102;
    // User with given PAN has not invested in mutual funds. PAN does not have any folios under the RTAs.
    // https://docs.google.com/spreadsheets/d/1zCTNVs-6-YDn8FHknpjPwY1xXxJA0VDD/edit?gid=341864001#gid=341864001
    NO_HOLDINGS_FOUND = 105;
    // PAN has no folio registered with the given mobile number.
    // https://docs.google.com/spreadsheets/d/1zCTNVs-6-YDn8FHknpjPwY1xXxJA0VDD/edit?gid=341864001#gid=341864001
    INVALID_PAN_MOBILE_COMBINATION = 103;
    // invalid pan and email combination
    INVALID_PAN_EMAIL_COMBINATION = 104;
  }
  rpc.Status status = 1;
  // request generated by the server.
  string request_id = 2;
  OTPGenerationDetails otp_response = 3;
}


message TriggerHoldingsImportFetchRequest {
  vendorgateway.RequestHeader header = 1;
  // generated by vendor in the transaction_id api.
  // this is mandatory for small-case.
  string transaction_id = 2;
  // generated by be to uniquely identify a holdings import request.
  // this should be same as the client_reference_number used in InitiateHoldingsImport call.
  string client_reference_number = 3;
  string pan_number = 4;
  string mobile_number = 5;
  OTPVerificationDetails otp_details = 6;
  string actor_id = 7;
  // request_id generated by vendor as part of InitiateHoldingsImport rpc call
  string initiate_holdings_import_request_id = 8;
}

message OTPGenerationDetails {
  string otp_reference = 1;
}

message OTPVerificationDetails {
  string otp_reference = 1;
  string otp = 2;
}

message TriggerHoldingsImportFetchResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    TRANSIENT_FAILURE = 100;
    PERMANENT_FAILURE = 101;
    INCORRECT_OTP_ENTERED = 102;
    OTP_TIME_LIMIT_EXCEEDED = 103;
  }
  rpc.Status status = 1;
}

message VerifyAndDecryptRequest{
  vendorgateway.RequestHeader header = 1;
  string payload = 2;
  string signature = 3;
  string transaction_id = 4;
}

message VerifyAndDecryptResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  string decrypted_payload = 2;
}

message EncryptAndSignRequest {
  vendorgateway.RequestHeader header = 1;
  string payload = 2;
}

message EncryptAndSignResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  string encrypted_payload = 2;
  string signature = 3;
}
