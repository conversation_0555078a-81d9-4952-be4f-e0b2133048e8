syntax = "proto3";

package vendorgateway.bridgewise;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendors/catalog/bridgewise/bridgewise.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/stocks/catalog/bridgewise";
option java_package = "com.github.epifi.gamma.api.vendorgateway.stocks.catalog.bridgewise";

service Catalog {
  // GetAssetIdentifierDetails rpc retrieves detailed information about a specific asset based on the provided external identifier.
  // The external identifier can be of various types (e.g., ISIN, ticker-exchange) and is used to uniquely identify the asset across different exchanges.
  rpc GetAssetIdentifierDetails (GetAssetIdentifierDetailsRequest) returns (GetAssetIdentifierDetailsResponse);
  // GetCompany rpc retrieves detailed information about one or more companies based on the provided company ID.
  rpc GetCompany (GetCompanyRequest) returns (GetCompanyResponse);
  // GetCompanyFundamentalParameters rpc retrieves fundamental financial and operational parameters for a specified company based on the provided calendar year, quarter, and company ID.
  rpc GetCompanyFundamentalParameters (GetCompanyFundamentalParametersRequest) returns (GetCompanyFundamentalParametersResponse);
  // GetCompanyFundamentalParagraphs rpc Retrieves narrative paragraphs that provide qualitative insights into a company's fundamentals,
  // such as analysis summaries, section-specific commentary, or textual descriptions.
  rpc GetCompanyFundamentalParagraphs (GetCompanyFundamentalParagraphsRequest) returns (GetCompanyFundamentalParagraphsResponse);
  // GetCompanyMarketData rpc retrieves historical market data for a specified company within a given date range.
  rpc GetCompanyMarketData (GetCompanyMarketDataRequest) returns (GetCompanyMarketDataResponse);
  // GetCompanyLogos rpc retrieves logos associated with a specified company.
  // It only returns logos for companies that have logos available and can return empty array if nothing is found.
  rpc GetCompanyLogos (GetCompanyLogosRequest) returns (GetCompanyLogosResponse);
  // GetCompanyMarketStatistics rpc retrieves market performance statistics for a specified company.
  rpc GetCompanyMarketStatistics (GetCompanyMarketStatisticsRequest) returns (GetCompanyMarketStatisticsResponse);
  // GetCompanies rpc retrieves companies in a paginated manner based on given filter
  rpc GetCompanies (GetCompaniesRequest) returns (GetCompaniesResponse);
  // GetCompanyTradingItems rpc retrieves trading items for a specified company.
  rpc GetCompanyTradingItems (GetCompanyTradingItemsRequest) returns (GetCompanyTradingItemsResponse);
}

message GetAccessTokenRequest {
  vendorgateway.RequestHeader header = 1;
}

message GetAccessTokenResponse {
  rpc.Status status = 1;
  string access_token = 2;
  google.protobuf.Timestamp expires_at = 3;
}

message GetAssetIdentifierDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  // ex: INE758E01017 (isin), JIOFIN-NSEI (ticker_exchange), HDFCBANK-NSEI (ticker_exchange), 
  string identifier_value = 2 [(validate.rules).string.min_len = 1];
  // ex: isin, ticker_exchange
  IdentifierType identifier_type = 3 [(validate.rules).enum.defined_only = true];
}

message GetAssetIdentifierDetailsResponse {
  rpc.Status status = 1;
  repeated vendors.catalog.bridgewise.AssetDetails asset_details = 2;
}

message GetCompanyRequest {
  vendorgateway.RequestHeader header = 1;
  string company_id = 2 [(validate.rules).string.min_len = 1];
}

message GetCompanyResponse {
  rpc.Status status = 1;
  repeated vendors.catalog.bridgewise.CompanyDetails companies = 2;
}

message GetCompanyFundamentalParametersRequest {
  vendorgateway.RequestHeader header = 1;
  string company_id = 2 [(validate.rules).string.min_len = 1];
  int64 calendar_year = 4;
  int64 calendar_quarter = 5;
  PeriodType period_type = 6;
}

message GetCompanyFundamentalParametersResponse {
  rpc.Status status = 1;
  repeated vendors.catalog.bridgewise.CompanyFundamentalParameters parameters = 2;
}

message GetCompanyFundamentalParagraphsRequest {
  vendorgateway.RequestHeader header = 1;
  string company_id = 2 [(validate.rules).string.min_len = 1];
}

message GetCompanyFundamentalParagraphsResponse {
  rpc.Status status = 1;
  repeated vendors.catalog.bridgewise.CompanyFundamentalParagraph paragraphs = 2;
}

message GetCompanyMarketDataRequest {
  vendorgateway.RequestHeader header = 1;
  string trading_item_id = 2;
  string company_id = 3 [(validate.rules).string.min_len = 1];
  google.type.Date from_date = 4;
  google.type.Date to_date = 5;
}

message GetCompanyMarketDataResponse {
  rpc.Status status = 1;
  repeated vendors.catalog.bridgewise.CompanyMarketData market_data = 2;
}

message GetCompanyLogosRequest {
  vendorgateway.RequestHeader header = 1;
  repeated string company_ids = 2 [(validate.rules).repeated.min_items = 1];
}

message GetCompanyLogosResponse {
  rpc.Status status = 1;
  repeated vendors.catalog.bridgewise.CompanyLogo logos = 2;
}

message GetCompanyMarketStatisticsRequest {
  vendorgateway.RequestHeader header = 1;
  string company_id = 2 [(validate.rules).string.min_len = 1];
}

message GetCompanyMarketStatisticsResponse {
  rpc.Status status = 1;
  repeated vendors.catalog.bridgewise.CompanyMarketStatistics market_statistics = 2;
}

enum IdentifierType {
  IDENTIFIER_TYPE_UNSPECIFIED = 0;
  IDENTIFIER_TYPE_ISIN = 1;
  IDENTIFIER_TYPE_TICKER_EXCHANGE = 2;
}

enum PeriodType {
  PERIOD_TYPE_UNSPECIFIED = 0;
  PERIOD_TYPE_ANNUAL = 1;
  PERIOD_TYPE_QUARTER = 2;
  PERIOD_TYPE_TTM = 3;
}

message GetCompaniesRequest {
  vendorgateway.RequestHeader header = 1;
  // exchanges should be comma separated, Ex: NSEI,BSE,LSE,NYSE etc.
  repeated string exchanges = 2;
  // number of securities to fetch for each page
  int32 page_size = 3;
  // page number starts from 1
  int32 page = 4;
}

message GetCompaniesResponse {
  rpc.Status status = 1;
  vendors.catalog.bridgewise.CompaniesData companies = 2;
}

message GetCompanyTradingItemsRequest {
  vendorgateway.RequestHeader header = 1;
  string company_id = 2 [(validate.rules).string.min_len = 1];
}

message GetCompanyTradingItemsResponse {
  rpc.Status status = 1;
  repeated vendors.catalog.bridgewise.AssetDetails asset_details = 2;
}
