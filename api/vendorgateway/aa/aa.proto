syntax = "proto3";

package vendorgateway.aa;

option go_package = "github.com/epifi/gamma/api/vendorgateway/aa";
option java_package = "com.github.epifi.gamma.api.vendorgateway.aa";

// Different types of AA which provide service
enum AaEntity {
  AA_ENTITY_UNSPECIFIED = 0;

  AA_ENTITY_AA_ONE_MONEY = 1;

  AA_ENTITY_AA_AUJUS_DEFAULT = 2;

  AA_ENTITY_AA_AUJUS_ALTERNATE = 3;

  AA_ENTITY_AA_FINVU = 4;
}
