syntax = "proto3";

package vendorgateway;

option go_package = "github.com/epifi/be-common/api/vendorgateway";
option java_package = "com.github.epifi.gamma.api.vendorgateway";

// Represents a vendor or partner that epifi integrates with.
enum Vendor {
  VENDOR_UNSPECIFIED = 0;

  // Federal bank is one of epifi's open banking partners.
  FEDERAL_BANK = 1;

  // SMS, phone calls
  TWILIO = 2;

  // SMS, phone calls
  EXOTEL = 3;

  // Email
  SEND_GRID = 4;

  // Account Aggregator
  ONE_MONEY = 5;

  // AWS SES
  AWS_SES = 6;

  // Google FCM
  FCM = 7;

  // Liveness/FM
  VERI5 = 8;

  // Liveness/FM/Data
  KARZA = 9;

  FRESHDESK = 10;

  FRESHCHAT = 11;

  // Reward offers
  LOYLTY_REWARDZ = 12;

  // CX phone calls
  Ozonetel = 13;

  // SMS vendor
  ACL = 14;

  // In house
  IN_HOUSE = 15;

  // SMS vendor
  KALEYRA = 16;

  // FITTT cricket vendor
  ROANUZ = 17;

  IPSTACK = 18;

  // CVLKRA wealth vendor
  CVLKRA = 19;

  // Sahamati central registry vendor
  SAHAMATI = 20;

  // Credit Report vendor
  EXPERIAN = 21;

  // Card Shipment Tracking vendor
  SHIPWAY = 22;

  // NSDL wealth vendor
  NSDL = 23;

  // CKYC cersai vendor
  CKYC = 24;

  // Aadhaar E-Sign vendor
  MANCH = 25;

  // Doc Sign Vendor
  DIGIO = 26;

  // MutualFund Vendor
  CAMS = 27;

  // Seon Vendor
  SEON = 28;

  // EGV offers vendor
  QWIKCILVER = 29;

  // OCR vendor
  INHOUSE_OCR = 30;

  // Digilocker vendor
  DIGILOCKER = 31;

  // Finvu AA vendor
  FINVU = 32;

  // p2p investment vendor
  LIQUILOANS = 33;

  // Karvy MutualFund Vendor
  KARVY = 34;

  // AXIS_BANK open banking Vendor
  AXIS_BANK = 35;

  // GPlace
  GPLACE = 36;

  GOOGLE = 37;

  // Maxmind is a provider of IP -> Location data.
  MAXMIND = 38;

  // PAYU - Affluence score provider
  PAYU = 39;

  // BUREAU is Risk signals data provider
  BUREAU = 40;

  // Drona pay is txn risk score provider
  DRONA_PAY = 41;

  // Signzy - Domain Name details provider
  SIGNZY = 42;

  //M2P is the facilitator of credit card operations on federal bank
  M2P = 43;

  // Leegality: e-sign facilitator
  LEEGALITY = 44;

  //ALPACA is US investment provider
  ALPACA = 45;

  // SENSEFORTH: Chatbot vendor
  SENSEFORTH = 46;

  // AML vendor TSS
  TSS = 47;

  // RISKCOVRY is the vendor facilitating health insurance policy purchase.
  RISKCOVRY = 48;

  // MORNINGSTAR: catalog vendor for us-stock
  MORNINGSTAR = 49;

  // MF_CENTRAL helps in importing all mutual fund holdings data for a user
  MF_CENTRAL = 50;

  // MF_CENTRAL helps in importing all mutual fund holdings data for a user and helps in analytics
  SMALL_CASE = 51;

  // THRIWE is an offer vendor which facilitates the fulfillment of benefits package offers on our offer catalog.
  THRIWE = 52;

  // VISTARA facilitates transfer of air miles and seat upgrade vouchers to Vistara account
  VISTARA = 53;

  // IDFC Vendor
  IDFC = 54;

  // Fennel Feature Store Vendor
  FENNEL_FEATURE_STORE = 55;

  // collections vendor for loans
  // https://www.credgenics.com
  CREDGENICS = 56;

  // CRM used to manage salary program leads
  LEADSQUARED = 57;

  // DREAMFOLKS is the vendor for issuing lounge access coupons in real time
  DREAMFOLKS = 58;

  // DPANDA is an external vendor for offers redemption using fi-coins
  DPANDA = 59;

  // POSHVINE is an external vendor for offers redemption using fi-coins
  POSHVINE = 60;

  // ABFL vendor is for providing personal loans
  ABFL = 61;

  // SESHAASAI is a printing vendor for cards
  SESHAASAI = 62;

  // TransUnion CIBIL Limited is a credit information company operating in India
  CIBIL = 63;

  // MONEYVIEW is a distribution loans vendor partner.
  MONEYVIEW = 64;
  // TARTAN is vendor for payroll management.
  TARTAN = 65;
  // GUPSHUP is vendor for whatsapp comms.
  GUPSHUP = 66;
  // RAZORPAY is a vendor for payment gateway
  RAZORPAY = 67;
  // FIFTYFIN is the vendor for LAMF
  FIFTYFIN = 68;

  // loan management systems used in lending
  FINFLUX = 69;

  SETU = 70;

  // Video SDK is an external vendor that provides the infrastructre for making video-calls
  VIDEO_SDK = 71;

  UQUDO = 72;

  ONSURITY = 73;

  // SGPL vendor is for providing personal loans which is our NBFC entity
  STOCK_GUARDIAN_LSP = 74;

  VISA = 75;

  // NETCORE is a vendor for SMS and WhatsApp comms.
  NETCORE = 76;

  MOENGAGE = 77;

  // LENDEN vendor is for providing personal loans
  LENDEN = 78;

  // SCIENAPTIC is a vendor for getting specific features on a user
  // e.g. features derived from sms data
  SCIENAPTIC = 79;

  // Analytics Service for generating Heatmaps for apps
  // https://learn.microsoft.com/en-us/clarity/mobile-sdk/android-sdk?tabs=kotlin#setcustomuserid
  MS_CLARITY = 80;

  // Airtel is a vendor for SMS and WhatsApp comms.
  AIRTEL = 81;

  // Ignosis is a vendor for AA data analysis
  IGNOSIS = 82;

  // Bridgewise is a vendor for stocks data
  BRIDGEWISE = 83;

  // Saven is a vendor that provides infrastructure for credit cards manages cc onboarding and ccms related operations
  SAVEN = 84;

  // PUBLIC_NSDL is a vendor for nps data
  PUBLIC_NSDL = 85;

  // card printing vendor
  MCT = 86;

  // Zenduty is a vendor for alerting and incident management
  ZENDUTY = 87;

  ZOMATO = 88;
}
