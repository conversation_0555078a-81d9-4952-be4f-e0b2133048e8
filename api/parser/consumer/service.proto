//go:generate gen_queue_pb
syntax = "proto3";

package parser.consumer;

import "api/parser/enums/enums.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/common/ownership.proto";
import "api/parser/consumer/transaction_backfill.proto";

option go_package = "github.com/epifi/gamma/api/parser/consumer";
option java_package = "com.github.epifi.gamma.api.parser.consumer";

service Consumer {
  // InitiateTxnBackfill will process all the txn published through
  // spark job and based on DataOwnerService we will call parser service
  // and will create workflow which will call parser service
  // and publish the response from parser in a topic
  rpc InitiateTxnBackfill(InitiateTxnBackfillRequest) returns (InitiateTxnBackfillResponse);
}

message InitiateTxnBackfillRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  // batched transaction payload for backfill
  repeated TxnBackfillPayload txn_backfill_batch = 2;

  // ownership under which txn data is persisted
  api.typesv2.common.Ownership data_entity = 3;

  // DataOwnerService will name of the service which owns the transaction data.
  // This is used to keep record of the transaction data fed to the parser service.
  parser.enums.DataOwnerService data_owner_service = 4;

  // unique id for scoop job to identify which scoop job created this payload and use it to create directory in S3 buckets
  // for dumping data
  string scoop_job_id = 5;
}

message InitiateTxnBackfillResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

