// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb

syntax = "proto3";

package leads.consumer;

import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/leads/consumer";
option java_package = "com.github.epifi.gamma.api.leads.consumer";

service Consumer {
  // ProcessDeleteUserEvent processes the event for deleting a user from the leads system
  rpc ProcessDeleteUserEvent(ProcessDeleteUserEventRequest) returns (ProcessDeleteUserEventResponse);
}

message ProcessDeleteUserEventRequest {
  // A set of all the common attributes to be contained in a queue consumer request
  queue.ConsumerRequestHeader request_header = 1;

  // actor id of the user to be deleted
  string actor_id = 2;
}

message ProcessDeleteUserEventResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}
