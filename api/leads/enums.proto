//go:generate gen_sql -types=UserLeadStatus,ProductType
syntax = "proto3";

package leads;

option go_package = "github.com/epifi/gamma/api/leads";
option java_package = "com.github.epifi.gamma.api.leads";

// UserLeadStatus represents the current status of a user lead in the system.
enum UserLeadStatus {
  // Initial status of the lead when it is created in our system.
  USER_LEAD_STATUS_LEAD_CREATED = 0;
  // User entity is created in the product service's system
  USER_LEAD_STATUS_USER_CREATED = 1;
  // If the lead does not reach any other terminal status within expiry window, it will be marked as expired.
  USER_LEAD_STATUS_EXPIRED = 2;
  // User got rejected from all possible product journey flows
  USER_LEAD_STATUS_REJECTED = 3;
  // Product onboarding is completed
  USER_LEAD_STATUS_CONVERTED = 4;
 // User is duplicate
  USER_LEAD_STATUS_DUPLICATE = 5;
}

// ProductType represents the type of product associated with the lead.
enum ProductType {
  // Default value, should not be used.
  PRODUCT_TYPE_UNSPECIFIED = 0;
  // FI Personal Loan product type.
  PRODUCT_TYPE_FI_PERSONAL_LOAN = 1;
}

enum UserLeadFieldMask {
  USER_LEAD_FIELD_MASK_UNSPECIFIED = 0;
  USER_LEAD_FIELD_MASK_ACTOR_ID = 1;
  USER_LEAD_FIELD_MASK_PERSONAL_DETAILS = 2;
  USER_LEAD_FIELD_MASK_ADDITIONAL_DETAILS = 3;
  USER_LEAD_FIELD_MASK_LEAD_STATUS = 4;
  USER_LEAD_FIELD_MASK_COMPLETED_AT = 5;
  USER_LEAD_FIELD_MASK_EXPIRED_AT = 6;
}
