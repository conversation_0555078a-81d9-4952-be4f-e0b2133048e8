syntax = "proto3";

package leads;

import "api/leads/enums.proto";
import "api/leads/user_lead.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/phone_number.proto";

option go_package = "github.com/epifi/gamma/api/leads";
option java_package = "com.github.epifi.gamma.api.leads";

service UserLeadSvc {
  // CreateLead creates a new lead in system
  rpc CreateLead (CreateLeadRequest) returns (CreateLeadResponse) {};

  // GetActiveLeads returns active leads that match one or more of the provided identifiers
  rpc GetActiveLeads (GetActiveLeadsRequest) returns (GetActiveLeadsResponse) {};

  // SetActorId sets the actor id for the given lead ids
  rpc SetActorId (SetActorIdRequest) returns (SetActorIdResponse) {};
}

message SetActorIdRequest {
  // lead ids for which actor id needs to be set
  repeated string lead_ids = 1;
  string actor_id = 2;
}

message SetActorIdResponse {
  rpc.Status status = 1;
}

message GetActiveLeadsRequest {
  // user identifiers
  string pan = 1;
  api.typesv2.common.PhoneNumber phone_number = 2;
  string email = 3;

  // optional, if empty active leads for all product types will be returned
  repeated ProductType product_types = 4;
}

message GetActiveLeadsResponse {
  rpc.Status status = 1;
  // key is the integer value of ProductType enum
  map<int32, UserLead> product_type_to_active_lead_map = 2;
}

message CreateLeadRequest {
  string client_request_id = 1;
  string client_id = 2;
  api.typesv2.common.PhoneNumber phone_number = 3;
  string email = 4;
  string pan = 5;
  PersonalDetails personal_details = 6;
  AdditionalDetails additional_details = 7;
  ProductType product_type = 8;
}

message CreateLeadResponse {
  rpc.Status status = 1;
  enum Status {
    OK = 0;
    STATUS_LEAD_EXISTS_WITH_CLIENT_REQUEST_ID = 101;
    STATUS_LEAD_EXISTS_WITH_USER_IDENTIFIERS = 102;
    STATUS_USER_CANNOT_START_PRODUCT_JOURNEY = 103;
  }
}
