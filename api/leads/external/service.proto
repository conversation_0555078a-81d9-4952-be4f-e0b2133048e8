syntax = "proto3";

package leads.external;

import "api/rpc/method_options.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/leads/external";
option java_package = "com.github.epifi.gamma.api.leads.external";

// https://docs.google.com/document/d/1YNlMpnQYsOVl9WDZTsxHzQlQ956bNXqd3yR6_15_v14/edit?usp=sharing
service Lead {
  // CreateLoansLead creates a new lead for loans
  // Returns below http status codes based on basic validations
  // 400 - Some of the input arguments are missing or invalid
  // 401 - Unauthorized
  // 500 - Internal server error
  rpc CreateLoansLead (CreateLoansLeadRequest) returns (CreateLoansLeadResponse) {
    option (google.api.http) = {
      post: "/v1/leads/loans/create"
      body: "*"
    };
    option (rpc.auth_required) = true;
  };
}

// CreateLoansLeadRequest represents the request payload for creating a new loan lead.
// All mandatory fields must be provided, and conditional mandatory fields depend on specific business rules.
message CreateLoansLeadRequest {
  // Unique identifier for the request. Must not exceed 40 characters.
  string client_request_id = 1 [json_name = "client_request_id"];

  // Customer's full name details
  Name name = 2 [json_name = "name"];

  // Customer's phone number. Must be exactly 10 digits.
  string phone_number = 3 [json_name = "phone_number"];

  // Customer's email address. Must contain '@' and '.'
  string email = 4 [json_name = "email"];

  // Customer's PAN card number. Must be a valid PAN format.
  string pan = 5 [json_name = "pan"];

  // Customer's date of birth in YYYY-MM-DD format. Customer must be over 21 years old.
  string dob = 6 [json_name = "dob"];

  Address current_address = 7 [json_name = "current_address"];
  EmploymentDetails employment_details = 8 [json_name = "employment_details"];
  LoanRequirement loan_requirement = 9 [json_name = "loan_requirement"];

  // Custom key-value pairs for client-specific data.
  // Keys and values must be pre-agreed upon with the lead system.
  map<string, string> custom_fields = 10 [json_name = "custom_fields"];
}

message CreateLoansLeadResponse {
  // 101 - Lead already exists for the given identifiers
  // 102 - Lead already exists with the given client request id
  Status status = 1 [json_name = "status"];
}

message Status {
  // custom status codes
  optional uint32 code = 1 [json_name = "code"];
  string message = 2 [json_name = "message"];
}

message Name {
  string first = 1 [json_name = "first"];
  string middle = 2 [json_name = "middle"];
  string last = 3 [json_name = "last"];
}

message Address {
  string pincode = 1 [json_name = "pincode"];
}

message EmploymentDetails {
  string employment_type = 1 [json_name = "employment_type"];
  string monthly_income = 2 [json_name = "monthly_income"];
}

message LoanRequirement {
  string desired_loan_amount = 1 [json_name = "desired_loan_amount"];
}
