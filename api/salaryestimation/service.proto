syntax = "proto3";

package api.salaryestimation;

import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/status.proto";
import "api/typesv2/salaryestimation/source_flow_params.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/salaryestimation";
option java_package = "com.github.epifi.gamma.api.salaryestimation";

// Income service provides functionality around detection and estimation of income and/or salary of a user
// via different methods like the user's transactions fetched from account aggregator ecosystem,
// user-provided SMS data, bank statements, etc.
service SalaryEstimation {
  // Returns salary if available for the actor
  rpc GetSalary (GetSalaryRequest) returns (GetSalaryResponse);

  // Orchestrates the salary estimation flow for the actor
  rpc ComputeSalary (ComputeSalaryRequest) returns (ComputeSalaryResponse);
}

message GetSalaryRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message GetSalaryResponse {
  rpc.Status status = 1;

  Salary salary = 2;
  string l1_analysis_signed_url = 3;
}

message Salary {
  typesv2.salaryestimation.Source source = 1;

  SalaryAccount salary_account = 2;

  google.protobuf.Timestamp computed_at = 3;
}

message SalaryAccount {
  string account_id = 1;

  // TODO(Brijesh): Add exhaustive list of features needed by all clients of salary estimation service
}

message ComputeSalaryRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];

  // Client is used to determine the service to notify or callback on exit after salary estimation
  Client client = 2;

  // Client request ID is a unique identifier provided by the client for a unique request.
  // It is used to identify the data shared and the analysis attempt made against the client's request.
  string client_req_id = 3 [(validate.rules).string.min_len = 1];

  typesv2.salaryestimation.Source source = 4;

  typesv2.salaryestimation.SourceFlowParams source_flow_params = 5;

  bool require_holding_screen = 6;
}

message ComputeSalaryResponse {
  rpc.Status status = 1;

  frontend.deeplink.Deeplink next_action = 2;
}

// Client is used to determine the service to notify or callback on exit after salary estimation
enum Client {
  CLIENT_UNSPECIFIED = 0;

  CLIENT_LOANS = 1;
}
