//go:generate gen_sql -types=JourneyType,JourneyState,JourneyStateInfo
syntax = "proto3";

package api.investment.event_processor;

import "api/comms/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/investment/event_processor";
option java_package = "com.github.epifi.gamma.api.investment.event_processor";

// UserJourney tells about the status of a user's journey through investment instruments like US stocks, peer-to-peer investments (Jump), etc.
message User<PERSON>ourney {
  // unique identifier of a user journey
  string id = 1;

  string actor_id = 2;

  // A journey applicable to the user based on event
  // like US stocks onboarding journey, US stocks buying journey, etc.
  JourneyType journey_type = 3;

  // latest state of actor in a particular journey, derived from events
  // an event can be mapped to different states based on the journey of a user
  // for example, an event of a user visiting a stock details page can be mapped
  // to one state when user is going through onboarding journey, and another state when going through buying journey
  JourneyState latest_journey_state = 4;

  // last time the user entered the latest state, based on an event
  google.protobuf.Timestamp last_entered_at = 5;

  // information related to journey state
  // e.g. event properties, notifications already sent to the user, etc.
  // This is only maintained for the latest journey state
  JourneyStateInfo journey_state_info = 6;

  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  google.protobuf.Timestamp deleted_at = 9;
}

enum UserJourneyFieldMask {
  USER_JOURNEY_FIELD_MASK_UNSPECIFIED = 0;
  USER_JOURNEY_FIELD_MASK_ID = 1;
  USER_JOURNEY_FIELD_MASK_ACTOR_ID = 2;
  USER_JOURNEY_FIELD_MASK_JOURNEY_TYPE = 3;
  USER_JOURNEY_FIELD_MASK_LATEST_JOURNEY_STATE = 4;
  USER_JOURNEY_FIELD_MASK_LAST_ENTERED_AT = 5;
  USER_JOURNEY_FIELD_MASK_JOURNEY_STATE_INFO = 6;
  USER_JOURNEY_FIELD_MASK_CREATED_AT = 7;
  USER_JOURNEY_FIELD_MASK_UPDATED_AT = 8;
  USER_JOURNEY_FIELD_MASK_DELETED_AT = 9;
}

enum JourneyType {
  JOURNEY_TYPE_UNSPECIFIED = 0;

  // Journey of user until they are eligible for onboarding onto US stocks
  JOURNEY_TYPE_US_STOCKS_ONBOARDING = 1;

  // Journey of user who has successfully onboarded and can purchase shares of US stocks
  JOURNEY_TYPE_US_STOCKS_BUY = 2;

  // Journey of user who has not invested in jump yet, until they enter pin for investment
  JOURNEY_TYPE_JUMP_NON_INVESTED_USER = 3;
}

enum JourneyState {
  JOURNEY_STATE_UNSPECIFIED = 0;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2181&t=ddpi8YlwxbsGBouc-4
  JOURNEY_STATE_US_STOCKS_ONB_STOCK_DETAILS_VISITED = 1;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A3551&t=ddpi8YlwxbsGBouc-4
  JOURNEY_STATE_US_STOCKS_DOC_SUBMISSION_SCREEN_VISITED = 3;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=335%3A5524&t=ddpi8YlwxbsGBouc-4
  JOURNEY_STATE_US_STOCKS_ONB_PAN_UPLOAD_SCREEN_VISITED = 4;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2341&t=ddpi8YlwxbsGBouc-4
  JOURNEY_STATE_US_STOCKS_ONB_DISCLOSURES_SCREEN_VISITED = 5;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2727&t=ddpi8YlwxbsGBouc-4
  JOURNEY_STATE_US_STOCKS_ONB_EMPLOYMENT_DETAILS_SCREEN_VISITED = 6;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=861%3A3894&t=ddpi8YlwxbsGBouc-4
  JOURNEY_STATE_US_STOCKS_ONB_INVESTMENT_PLAN_SCREEN_VISITED = 7;

  // ref:https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A3222&t=ddpi8YlwxbsGBouc-4
  JOURNEY_STATE_US_STOCKS_ONB_TNC_SCREEN_VISITED = 8;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3395%3A14745&t=ddpi8YlwxbsGBouc-4
  JOURNEY_STATE_US_STOCKS_BUY_EXPLORATION_STARTED = 9;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1196%3A5224&t=ddpi8YlwxbsGBouc-4
  JOURNEY_STATE_US_STOCKS_BUY_STOCK_DETAILS_VIEWED = 10;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1013-4586&t=VcKUY63vRkNsX71Y-4
  JOURNEY_STATE_US_STOCKS_BUY_INPUT_AMOUNT_SCREEN_VISITED = 11;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1013%3A4462&t=ddpi8YlwxbsGBouc-4
  JOURNEY_STATE_US_STOCKS_BUY_AMOUNT_SUMMARY_SCREEN_VISITED = 12;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=7003-23175&t=82o9R78Ea8m45FSz-4
  JOURNEY_STATE_US_STOCKS_ONB_CONNECT_NEW_ACCOUNT_FOR_SOF_SCREEN_VISITED = 13;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=7003-23039&t=82o9R78Ea8m45FSz-4
  JOURNEY_STATE_US_STOCKS_ONB_FETCH_EXISTING_ACCOUNT_STATEMENT_FOR_SOF_SCREEN_VISITED = 14;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-11562&t=asVkojb0z1mzwJ0m-0
  JOURNEY_STATE_JUMP_INTRO_SCREEN_VISITED_AND_ELIGIBILITY_NOT_CHECKED = 15;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8873-13021&t=jh8XP2y7YDJqPKex-0
  JOURNEY_STATE_JUMP_INTRO_SCREEN_VISITED_AND_FLEXI_UNLOCKED = 16;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8873-13021&t=jh8XP2y7YDJqPKex-0
  JOURNEY_STATE_JUMP_INTRO_SCREEN_VISITED_AND_SHORT_UNLOCKED = 17;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8873-13021&t=jh8XP2y7YDJqPKex-0
  JOURNEY_STATE_JUMP_INTRO_SCREEN_VISITED_AND_LONG_UNLOCKED = 18;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8873-13021&t=jh8XP2y7YDJqPKex-0
  JOURNEY_STATE_JUMP_INTRO_SCREEN_VISITED_AND_BOOSTER_UNLOCKED = 19;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  JOURNEY_STATE_JUMP_FLEXI_INVEST_SCREEN_VISITED = 20;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  JOURNEY_STATE_JUMP_SHORT_INVEST_SCREEN_VISITED = 21;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  JOURNEY_STATE_JUMP_LONG_INVEST_SCREEN_VISITED = 22;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  JOURNEY_STATE_JUMP_BOOSTER_INVEST_SCREEN_VISITED = 23;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  JOURNEY_STATE_JUMP_SWIPED_TO_INVEST = 24 [deprecated = true];

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  JOURNEY_STATE_JUMP_SWIPED_TO_INVEST_FLEXI = 25;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  JOURNEY_STATE_JUMP_SWIPED_TO_INVEST_SHORT = 26;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  JOURNEY_STATE_JUMP_SWIPED_TO_INVEST_LONG = 27;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  JOURNEY_STATE_JUMP_SWIPED_TO_INVEST_BOOSTER = 28;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=20995-3972&mode=dev
  JOURNEY_STATE_US_STOCKS_ONB_USS_CREATE_RISK_PROFILE_VISITED = 29;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011-26228&mode=dev
  JOURNEY_STATE_US_STOCKS_ONB_COLLECT_RISK_LEVEL_VISITED = 30;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21375-12544&mode=dev
  JOURNEY_STATE_US_STOCKS_ONB_RISK_DISCLOSURE_VISITED = 31;
}

// information related to journey state
message JourneyStateInfo {
  map<string, string> event_properties = 1;

  repeated comms.CampaignName notifications_sent = 2;

  // this list contains all the nudges that were activated for the user during the journey and their current status
  repeated NudgeInfo nudge_infos = 3;
}

message NudgeInfo {
  // nudge identifier
  string nudge_id = 1;
  NudgeStatus nudge_status = 2;
  // journey state for which the nudge was activated
  JourneyState journey_state = 3;
  // id used to uniquely identify a nudge entry for an actor, used for exiting a BE triggered nudge entry
  string entry_event_id = 4;
}

// represents the status of a nudge for a particular actor
enum NudgeStatus {
  NUDGE_STATUS_UNSPECIFIED = 0;
  // nudge is currently active for the actor
  NUDGE_STATUS_ACTIVE = 1;
  // nudge is exited and no longer active for the user
  NUDGE_STATUS_EXITED = 2;
}
