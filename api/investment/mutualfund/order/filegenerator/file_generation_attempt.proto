syntax = "proto3";

package api.investment.mutualfund.filegenerator;

import "api/vendorgateway/vendor.proto";
import "api/investment/mutualfund/order/filegenerator/service.proto";
import "google/protobuf/timestamp.proto";


option go_package = "github.com/epifi/gamma/api/investment/mutualfund/order/filegenerator";

message FileGenerationAttempt {
  string id = 1;
  string file_name = 2;
  filegenerator.FileStatus status = 3;
  filegenerator.FileType file_type = 4;
  vendorgateway.Vendor vendor_name = 5;
  string client_request_id = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  google.protobuf.Timestamp deleted_at = 9;
}

enum FileGenerationAttemptFieldMask {
  FILE_GENERATION_ATTEMPT_FIELD_MASK_UNSPECIFIED = 0;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_FILE_NAME = 1;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_FILE_STATUS = 2;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_CLIENT_REQUEST_ID = 3;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_UPDATED_AT = 4;
  FILE_GENERATION_ATTEMPT_FIELD_MASK_DELETED_AT = 5;
}
