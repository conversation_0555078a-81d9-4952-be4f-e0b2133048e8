//go:generate gen_queue_pb
syntax = "proto3";

package api.investment.mutualfund.external;

import "api/frontend/deeplink/deeplink.proto";
import "api/investment/mutualfund/external/mf_single_otp_cas_import_request.proto";
import "api/investment/mutualfund/external/mutual_fund_external_holdings_summary.proto";
import "api/investment/mutualfund/external/mutual_fund_external_order.proto";
import "api/investment/mutualfund/external/mutual_fund_external_request.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/vendor.proto";
import "api/vendorgateway/wealth/mutualfund/service.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "google/type/date.proto";


option go_package = "github.com/epifi/gamma/api/investment/mutualfund/external";
option java_package = "com.github.epifi.gamma.api.investment.mutualfund.external";

service MFExternalOrders {
  // FetchAllExternalMFOrders is a paginated rpc which fetches all the mutual fund external orders
  rpc FetchAllExternalMFOrders(FetchAllExternalMFOrdersRequest)returns (FetchAllExternalMFOrdersResponse);
  // InitiateHoldingsImport (BE) initiates otp creation for external mutual funds import.
  // for smallcase: makes vg call to HoldingImporter rpcs (CreateTransaction and InitiateHoldingsImport).
  rpc InitiateHoldingsImport(InitiateHoldingsImportRequest) returns (InitiateHoldingsImportResponse);
  // TriggerHoldingsImportFetch verifies the otp created for external mutual funds import
  // for smallcase: makes vg call to HoldingImporter rpc (TriggerHoldingsImportFetch)
  rpc TriggerHoldingsImportFetch(TriggerHoldingsImportFetchRequest) returns (TriggerHoldingsImportFetchResponse);
  // GetHoldingsImportStatus polls the status of import request for MF holdings.
  // eg. CREATE_TRANSACTION_SUCCESSFUL, OTP_VERIFICATION_SUCCESSFUL etc.
  // Deprecated in favor of GetMFHoldingsImportRequest
  rpc GetHoldingsImportStatus(GetHoldingsImportStatusRequest) returns (GetHoldingsImportStatusResponse){
    option deprecated = true;
  }
  // CreateMFHoldingsImport initializes the MF holdings import flow, stores the exit deeplink in the table for that particular request.
  rpc CreateMFHoldingsImport(CreateMFHoldingsImportRequest) returns (CreateMFHoldingsImportResponse);

  // UpdateHoldingsImportState updates the State of an MF holdings import request, on the basis of external id.
  rpc UpdateHoldingsImportState(UpdateHoldingsImportStateRequest) returns (UpdateHoldingsImportStateResponse);
  // GetMFHoldingsSummary returns per mutual fund folio-scheme level summary for an actor
  rpc GetMFHoldingsSummary(GetMFHoldingsSummaryRequest)returns (GetMFHoldingsSummaryResponse);
  // GetHoldingsImportRequestsInDateRange fetches the the mf holdings import requests for an actor in a given date range.
  // If date range is not given then requests with defined limit will be fetched.
  // If limit is passed as zero, then default value of 10 will be used.
  rpc GetHoldingsImportRequestsInDateRange(GetHoldingsImportRequestsInDateRangeRequest) returns (GetHoldingsImportRequestsInDateRangeResponse);
  // GetHoldingsRefreshEligibility returns the eligibility status of the actor to create a new request/ reuse an old request or not allow any request to be created.
  rpc GetHoldingsRefreshEligibility(GetHoldingsRefreshEligibilityRequest)returns(GetHoldingsRefreshEligibilityResponse);
  // InitiateNFT will create an entry in the mf_external_nft_requests table with details provided in the request.
  rpc InitiateNft(InitiateNftRequest) returns (InitiateNftResponse);
  // GenerateNFTOtp generates the otp if the request type needs an otp validation
  rpc GenerateNftOtp(GenerateNftOtpRequest) returns (GenerateNftOtpResponse);
  // VerifyNFTOtp will verify the otp against the corresponding otp medium based on the request status.
  rpc VerifyNftOtp(VerifyNftOtpRequest) returns (VerifyNftOtpResponse);
  // GetNFTStatus gets the status of the nft request
  rpc GetNftStatus(GetNftStatusRequest) returns (GetNftStatusResponse);
  // FetchAdditionalCasStatement can be used to fetch additional CAS statements (without OTP verification) for a user if one statement has already been fetched in last 10 mins.
  // This API currently only supports fetching CAS summary statement but can be extended to support fetching CAS Detailed statement as well.
  // CAS statement generation is an async process at vendor's end. So, caller should poll this API until it reaches a terminal status.
  rpc FetchAdditionalCasStatement(FetchAdditionalCasStatementRequest) returns (FetchAdditionalCasStatementResponse);
  // GetMFHoldingsImportRequest fetches the entire response of import request for MF holdings
  rpc GetMFHoldingsImportRequest(GetMFHoldingsImportRequestRequest) returns (GetMFHoldingsImportRequestResponse);
  // DeleteAllMfExternalDetails soft deletes all the mf external details for the given actor
  rpc DeleteAllMfExternalDetails(DeleteAllMfExternalDetailsRequest) returns (DeleteAllMfExternalDetailsResponse);
  // GetMFTransactionsByDateRange returns net transaction units for each mutual fund scheme for the given actor in the specified date range.
  rpc GetMFTransactionsByDateRange(GetMFTransactionsByDateRangeRequest) returns (GetMFTransactionsByDateRangeResponse);
}

message FetchAdditionalCasStatementRequest {
  string actor_id = 1;
  // client request id of the successfully completed primary statement fetch request placed via InitiateHoldingsImport rpc.
  string primary_statement_client_req_id = 2;
  // phone/email to used for fetching the report from vendor
  oneof contact_identifier {
    api.typesv2.common.PhoneNumber phone_number = 3;
    string email = 4;
  }
  // type of statement to be fetched from vendor
  CasStatementType statement_type = 5;
}

message FetchAdditionalCasStatementResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // status of the statement fetch process
  MfSingleOtpCasImportStatus import_status = 2;
  MfSingleOtpCasImportSubStatus import_sub_status = 3;
  // statement fetched from vendor
  oneof statement {
    vendorgateway.wealth.mutualfund.CASSummaryRes cas_summary_statement = 4;
  }
}


message FetchAllExternalMFOrdersRequest{
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  rpc.PageContextRequest page_context_request = 2;

}

message FetchAllExternalMFOrdersResponse{

  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;

  // slice of mutual fund external orders
  repeated MutualFundExternalOrder mutual_fund_external_orders = 2;

  rpc.PageContextResponse page_context_response = 3;
}

message InitiateHoldingsImportRequest{
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  string external_id = 2 [(validate.rules).string.min_len = 1];
  // optional: phone number
  api.typesv2.common.PhoneNumber phone_number = 3 [deprecated = true];
  // optional: otp medium. phone number with wealth onboarding is used in case no value is provided
  OtpMedium otp_medium = 4;
  // optional pan number to used to fetch the holdings
  // if not value is provided, the pan number will be fetched from the user profile details
  // pan input is only allowed for flows with provenance
  //  PROVENANCE_ANALYSER
  //  PROVENANCE_NET_WORTH_REFRESH
  //  PROVENANCE_MONEY_SECRETS
  //  PROVENANCE_WEALTH_BUILDER_MUTUAL_FUND_REPORT
  string pan = 5;
}

message InitiateHoldingsImportResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // duplicate initiate holdings import request
    DUPLICATE_REQUEST = 102;
    // User with given PAN has not invested in mutual funds. PAN does not have any folios under the RTAs.
    // https://docs.google.com/spreadsheets/d/1zCTNVs-6-YDn8FHknpjPwY1xXxJA0VDD/edit?gid=341864001#gid=341864001
    NO_HOLDINGS_FOUND = 105;
    // invalid pan and mobile number combination
    INVALID_PAN_MOBILE_COMBINATION = 103;
    // invalid pan and email combination
    INVALID_PAN_EMAIL_COMBINATION = 104;
  }
  // rpc response status
  rpc.Status status = 1;
  // unique identifier for MFHoldingsImportRequestTracker, client to resend this while verifying the OTP.
  string external_id = 2;
}

message TriggerHoldingsImportFetchRequest{
  string actor_id = 1;
  // unique identifier for MFHoldingsImportRequestTracker
  string external_id = 2;
  string otp = 3;
}

message TriggerHoldingsImportFetchResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // OTP verify retries exhausted
    RETRIES_EXHAUSTED = 101;
    // Incorrect OTP entered
    INCORRECT_OTP_ENTERED = 102;
    // Time limit exceeded for the entered OTP
    OTP_TIME_LIMIT_EXCEEDED = 103;
  }
  // rpc response status
  rpc.Status status = 1;
  bool is_otp_verified = 2;
}

message GetHoldingsImportStatusRequest{
  oneof identifier{
    // external_id as a unique identifier for MFHoldingsImportRequestTracker, should fetch the HoldingsImportStatus for this id.
    string external_id = 1;
    // actor_id as an identifier will fetch the latest request HoldingsImportStatus for an actor.
    string actor_id = 2;
  }
}

message GetHoldingsImportStatusResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  State holdings_import_state = 2;
  FailureReason failure_reason = 3;
  string external_id = 4;
  frontend.deeplink.Deeplink exit_deeplink = 5;
  Provenance provenance = 6;
  FlowType flow_type = 7;
}

message CreateMFHoldingsImportRequest{
  string actor_id = 1;
  // exit deeplink for the MF holdings import.
  frontend.deeplink.Deeplink exit_deeplink = 2;
  Provenance provenance = 3;
  FlowType flow_type = 4;
}

message CreateMFHoldingsImportResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  string external_id = 2;
}

message UpdateHoldingsImportStateRequest{
  string external_id = 1 [(validate.rules).string.min_len = 1];
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  State holdings_import_state = 3 [(validate.rules).enum = {not_in: [0]}];
  FailureReason failure_reason = 4 [(validate.rules).enum = {not_in: [0]}];
}

message UpdateHoldingsImportStateResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message GetMFHoldingsSummaryRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message GetMFHoldingsSummaryResponse {
  enum Status {
    OK = 0;
    // No data foun for the actor
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  // list of mutual fund holding summaries
  repeated MutualFundExternalHoldingsSummary mutual_fund_external_holdings_summaries = 2;
}

message GetHoldingsImportRequestsInDateRangeRequest{
  string actor_id = 1;
  google.protobuf.Timestamp from_time = 2;
  google.protobuf.Timestamp to_time = 3;
  int64 limit = 4;
  external.State state = 5;
}

message GetHoldingsImportRequestsInDateRangeResponse{
  enum Status {
    OK = 0;
    // No data foun for the actor
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  repeated MFHoldingsImportRequestTracker holdings_import_requests = 2;
}

message GetHoldingsRefreshEligibilityRequest{
  string actor_id = 1;
}


message GetHoldingsRefreshEligibilityResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  // RequestEligibilityStatus is the enum which defines the eligibility status of the actor to create an MF holdings import request.
  RequestEligibilityStatus request_eligibility_status = 2;
  //IneligibilityReason to be populated only in case if RequestEligibilityStatus is 'ACTOR_INELIGIBLE'
  IneligibilityReason ineligibility_reason = 3;
  // to be populated in case of re-using the old request.
  string external_id = 4;
}

enum RequestEligibilityStatus{
  REQUEST_ELIGIBILITY_STATUS_UNSPECIFIED = 0;
  REQUEST_ELIGIBILITY_STATUS_REUSE_OLD_REQUEST = 1;
  REQUEST_ELIGIBILITY_STATUS_INELIGIBLE = 2;
  REQUEST_ELIGIBILITY_STATUS_CREATE_NEW_REQUEST = 3;
}

enum IneligibilityReason{
  INELIGIBILITY_REASON_UNSPECIFIED = 0;
  INELIGIBILITY_REASON_TOO_MANY_REQUESTS = 1;
  INELIGIBILITY_REASON_REFRESH_ALREADY_SUCCEEDED = 2;
}


message InitiateNftRequest {
  string actor_id = 1;
  // optional: This field will be auto generated in case the the field is empty
  string external_id = 2;
  NFTRequestType request_type = 3;
  NFTRequestData request_data = 4;
  Provenance provenance = 5;
  vendorgateway.Vendor vendor = 6;
}

message InitiateNftResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  string external_id = 2;
}

message GenerateNftOtpRequest {
  string actor_id = 1;
  // unique identifier for MfExternalNFTRequest
  string external_id = 2;
  // medium with which otp needs to be generated
  OtpMedium otp_medium = 3;
}

message GenerateNftOtpResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // invalid pan and mobile number combination
    INVALID_PAN_MOBILE_COMBINATION = 103;
    // invalid pan and email combination
    INVALID_PAN_EMAIL_COMBINATION = 104;
  }
  // rpc response status
  rpc.Status status = 1;
}

message VerifyNftOtpRequest {
  string actor_id = 1;
  // unique identifier for MfExternalNFTRequest
  string external_id = 2;
  // Based on the status the otp will be validated with the respective medium. For eg, in case of updateEmail NFT request, the otp will be validated for
  // user auth in case the status is USER_AUTH_INITIATED, else it will be validated for new email validation in case the status is NEW_EMAIL_VERIFICATION_INITIATED.
  string otp = 3;
}

message VerifyNftOtpResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // OTP verify retries exhausted
    RETRIES_EXHAUSTED = 101;
    // Incorrect OTP entered
    INCORRECT_OTP_ENTERED = 102;
    OTP_EXPIRED = 103;
  }
  // rpc response status
  rpc.Status status = 1;
  bool is_otp_verified = 2;
}

message GetNftStatusRequest {
  string actor_id = 1;
  // unique identifier for MfExternalNFTRequest
  string external_id = 2;
}

message GetNftStatusResponse {
  enum Status {
    OK = 0;
    // No data found for the actor
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  NFTRequestStatus nft_request_status = 2;
  NFTRequestSubStatus nft_request_sub_status = 3;
}

message GetMFHoldingsImportRequestRequest{
  oneof identifier{
    // external_id as a unique identifier for MFHoldingsImportRequestTracker, should fetch the HoldingsImportStatus for this id.
    string external_id = 1;
    // actor_id as an identifier will fetch the latest request HoldingsImportStatus for an actor.
    string actor_id = 2;
  }
}

message GetMFHoldingsImportRequestResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  // entire response
  MFHoldingsImportRequestTracker mf_holdings_import_request_tracker = 2;
}

message DeleteAllMfExternalDetailsRequest{
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message DeleteAllMfExternalDetailsResponse{
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message GetMFTransactionsByDateRangeRequest{
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // The start date from which transactions should be considered.
  google.type.Date from_date = 2;
  // The end date up to which transactions should be considered.
  google.type.Date to_date = 3;
}

message GetMFTransactionsByDateRangeResponse{
  rpc.Status status = 1;
  // List of mutual fund external orders, each representing a transaction for a specific scheme and date.
  repeated MutualFundExternalOrder mutual_fund_external_orders = 2;
}
