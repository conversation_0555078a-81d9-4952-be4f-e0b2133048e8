//go:generate gen_queue_pb
//go:generate gen_sql -types=FailureReason,State,VendorData,Provenance,MFHoldingsImportRequestMetaData,NFTRequestType,NFTRequestStatus,NFTRequestSubStatus,NFTDetails,FlowType

syntax = "proto3";

package api.investment.mutualfund.external;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund/external";
option java_package = "com.github.epifi.gamma.api.investment.mutualfund.external";

message MFHoldingsImportRequestTracker {
  string id = 1;
  string actor_id = 2;
  string consent_id = 3;
  // this is the id which gets generated when CreateTransaction at vendor is successful.
  // for all further calls to the vendor this transaction id has to be passed. this will act as a unique identifier for this table.
  string transaction_id = 4;
  // unique 10 digit number generated at the time of creating the transaction to verify the update webhook payload to be consumed.
  string client_reference_number = 5;
  // state of the MFHoldingsImportRequestTracker for a transaction id.
  State state = 6;
  // FailureReason of the failed State
  FailureReason failure_reason = 7;
  vendorgateway.Vendor vendor = 8;
  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  // to be populated in case of soft delete.
  google.protobuf.Timestamp deleted_at = 11;

  // response sent by vendor
  VendorData vendor_data = 12;

  // unique identifier for the table which can be shared.
  string external_id = 13;

  // MFHoldingsImportRequestMetaData stores all the metadata corresponding to the MF analyser request.
  // for eg. entry/exit deeplink
  MFHoldingsImportRequestMetaData meta_data = 14;

  // identifier for the originator of the holdings import request.
  Provenance provenance = 15;
  // flow type of the request - first time import or refresh
  FlowType flow_type = 16;
}

// Provenance specifies the originator of a request.
enum Provenance {
  PROVENANCE_UNSPECIFIED = 0;
  PROVENANCE_ANALYSER = 1;
  PROVENANCE_LAMF = 2;
  PROVENANCE_NET_WORTH_REFRESH = 3;
  PROVENANCE_MONEY_SECRETS = 4;
  PROVENANCE_WEALTH_BUILDER_MUTUAL_FUND_REPORT = 5;
  PROVENANCE_WEALTH_BUILDER_ONBOARDING = 6;
}

enum State {
  STATE_UNSPECIFIED = 0;
  FAILED = 1;
  // State corresponding to a successful vendor api create transaction for a transaction_id.
  CREATE_TRANSACTION_SUCCESSFUL = 2;
  // State corresponding to initiation of an otp-generation call for a transaction_id.
  OTP_GENERATION_INITIATED = 3;
  // State corresponding to a successful vendor api otp generation for a transaction_id.
  OTP_GENERATION_SUCCESSFUL = 4;
  // State corresponding to initiation of vendor api otp verification for a transaction_id.
  OTP_VERIFICATION_INITIATED = 5;
  // State corresponding to a successful OTP verification for a transaction_id.
  OTP_VERIFICATION_SUCCESSFUL = 6;
  // State corresponding to the successful MutualFundExternalOrder refresh for an actor.
  EXTERNAL_ORDERS_REFRESH_SUCCESSFUL = 7;
  // State corresponding to the initiation of mf holdings import flow.
  // This state is required to indicate that the entry/exit deeplinks have been stored.
  MF_HOLDINGS_IMPORT_INITIATION_SUCCESSFUL = 8;
}

enum FailureReason {
  FAILURE_REASON_UNSPECIFIED = 0;
  // FailureReason corresponding failure in vendor api create transaction.
  CREATE_TRANSACTION_FAILED = 1;
  // FailureReason corresponding to OTP generation failure.
  OTP_GENERATION_FAILED = 2;
  // FailureReason corresponding to OTP verification failure.
  OTP_VERIFICATION_FAILED = 3;
  // FailureReason corresponding to the failed refresh for MutualFundExternalOrder for an actor.
  EXTERNAL_ORDERS_REFRESH_FAILED = 4;
  // FailureReason corresponding to the failure in initializing the entry point.
  MF_HOLDINGS_IMPORT_INITIATION_FAILURE = 5;
  // FailureReason corresponding to the failure in importing the MF holdings.
  MF_HOLDINGS_IMPORT_FAILURE = 6;
  // FailureReason corresponding to the failure in decryption of the MF holdings.
  MF_HOLDINGS_DECRYPTION_FAILURE = 7;
  // FailureReason corresponding to the failed refresh because mf external data is empty.
  EMPTY_EXTERNAL_MF_DATA_FAILURE = 8;
  // FailureReason corresponding to otp generation failure due to duplicate request.
  OTP_GENERATION_FAILED_DUPLICATE_REQUEST = 9;
  // FailureReason corresponding to otp generation failure due to invalid pan and mobile number combination.
  OTP_GENERATION_FAILED_INVALID_PAN_MOBILE = 10;
  // FailureReason corresponding to otp generation failure due to invalid pan and email combination.
  OTP_GENERATION_FAILED_INVALID_PAN_EMAIL = 11;
  // User with given PAN has not invested in mutual funds. PAN does not have any folios under the RTAs.
  // https://docs.google.com/spreadsheets/d/1zCTNVs-6-YDn8FHknpjPwY1xXxJA0VDD/edit?gid=341864001#gid=341864001
  OTP_GENERATION_FAILED_NO_HOLDINGS_FOUND = 15;
  // When there is no transaction in MF holdings payload
  MF_HOLDINGS_NO_TRANSACTION = 12;
  // When empty MF holdings payload is received from vendor(Smallcase)
  MF_HOLDINGS_EMPTY_PAYLOAD = 13;
  // FailureReason corresponding to the failure in name match in MF holdings payload and actor name
  MF_HOLDINGS_NAME_MATCH_FAILED = 14;
}

enum FlowType {
  FLOW_TYPE_UNSPECIFIED = 0;
  FLOW_TYPE_MF_HOLDINGS_FIRST_TIME_IMPORT = 1;
  FLOW_TYPE_MF_HOLDINGS_REFRESH = 2;
}

message VendorData {
  InitiateHoldingsImportResponseVendorData initiate_holdings_import_response = 1;
}

message InitiateHoldingsImportResponseVendorData {
  string otp_reference = 1;
  string request_id = 2;
}

enum MFHoldingsImportRequestTrackerFieldMask {
  MF_HOLDINGS_IMPORT_REQUEST_TRACKER_FIELD_MASK_UNSPECIFIED = 0;
  TRANSACTION_ID = 1;
  CONSENT_ID = 2;
  CLIENT_REFERENCE_NUMBER = 3;
  STATE = 4;
  FAILURE_REASON = 5;
  VENDOR = 6;
  VENDOR_DATA = 7;
  META_DATA = 8;
  MF_HOLDINGS_IMPORT_REQUEST_TRACKER_FIELD_MASK_PROVENANCE = 9;
}

message MFHoldingsImportRequestMetaData {
  // exit_deeplink is the deeplink stored when the MF analyser flow is initiated,
  // to ensure the redirection while exiting the MF analyser flow
  frontend.deeplink.Deeplink exit_deeplink = 1;

  // otp_verification_attempts ensure that the retries for OTP verification process is capped.
  int32 otp_verification_attempts = 2;
  // phone number with which the user fetching the mf holdings
  api.typesv2.common.PhoneNumber phone_number = 3;
  // map between client reference number and corresponding otp request
  map<string, OtpRequest> otp_requests = 4;
}

message OtpMedium {
  enum OtpMediumType {
    OTP_MEDIUM_TYPE_UNSPECIFIED = 0;
    OTP_MEDIUM_TYPE_PHONE_NUMBER = 1;
    OTP_MEDIUM_TYPE_EMAIL = 3;
  }
  OtpMediumType otp_medium_type = 1;
  oneof identifier {
    api.typesv2.common.PhoneNumber phone_number = 2;
    string email = 3 [(validate.rules).string.email = true];
  }
}

message OtpRequest {
  // unique client reference number for the otp request
  string client_reference_number = 1;
  // otp_reference returned by vendor as part of generate otp
  string otp_reference = 2;
  // request_id returned by vendor as part of generate otp
  string request_id = 3;
  OtpMedium otp_medium = 4;
  // state of the otp request
  State state = 5;
  // FailureReason of the failed State
  FailureReason failure_reason = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

// NFT stands for Non Financial Transaction. This table contains details for requests such as updating email, updating phone for various folios etc.
message MfExternalNFTRequest {
  string id = 1;
  string actor_id = 2;
  // unique id used for tracking the request
  string external_id = 3;
  // unique id with which vendor request is initiated
  string client_reference_id = 4;
  // field tells the type of operation eg. UPDATE_EMAIL, UPDATE_MOBILE etc.
  NFTRequestType type = 5;
  NFTRequestStatus status = 6;
  NFTRequestSubStatus sub_status = 7;
  // identifier for the originator of the holdings import request.
  Provenance provenance = 8;
  vendorgateway.Vendor vendor = 9;
  NFTDetails details = 10;
  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  // to be populated in case of soft delete.
  google.protobuf.Timestamp deleted_at = 13;
}

enum NFTRequestType {
  NFT_REQUEST_TYPE_UNSPECIFIED = 0;
  NFT_REQUEST_TYPE_EMAIL_UPDATE = 1;
  NFT_REQUEST_TYPE_MOBILE_UPDATE = 2;
}

enum NFTRequestStatus {
  NFT_REQUEST_STATUS_UNSPECIFIED = 0;
  NFT_REQUEST_STATUS_REQUEST_INITIATED = 1;
  NFT_REQUEST_STATUS_USER_AUTH_INITIATED = 2;
  NFT_REQUEST_STATUS_NEW_EMAIL_VERIFICATION_INITIATED = 3;
  NFT_REQUEST_STATUS_NEW_MOBILE_VERIFICATION_INITIATED = 4;
  NFT_REQUEST_STATUS_VERIFICATION_COMPLETE = 5;
  NFT_REQUEST_STATUS_SUCCESS = 6;
  NFT_REQUEST_STATUS_FAILED = 7;
}

// RequestSubStatus can have values for success, failure or any intermediate case.
enum NFTRequestSubStatus {
  NFT_REQUEST_SUB_STATUS_UNSPECIFIED = 0;
  NFT_REQUEST_SUB_STATUS_INVALID_PAN_EMAIL = 1;
  NFT_REQUEST_SUB_STATUS_INVALID_PAN_MOBILE = 2;
  NFT_REQUEST_SUB_STATUS_DUPLICATE_CLIENT_REF_ID = 3;
  NFT_REQUEST_SUB_STATUS_INVALID_INPUT = 4;
  NFT_REQUEST_SUB_STATUS_INVALID_REQUEST = 5;
  NFT_REQUEST_SUB_STATUS_PREVIOUS_PENDING_REQUEST = 6;
}

message NFTDetails {
  NFTRequestData request_data = 1;
  NFTVendorData vendor_data = 2;
  // auth details for validating the user
  OtpMedium auth_details = 3;
}

message NFTRequestData {
  oneof details {
    EmailUpdateRequestDetails email_update_details = 1;
    MobileUpdateRequestDetails mobile_update_details = 2;
  }
}

message NFTVendorData {
  // unique request id generated by the vendor for future reference, such as tracking request status etc.
  string request_id = 1;
  // contains otp info for authentication of the user.
  OtpInfo auth_otp_info = 2;
  // vendor details specific to each update type
  oneof details {
    EmailUpdateVendorData email_update_vendor_data = 3;
    MobileUpdateVendorData mobile_update_vendor_data = 4;
  }
}

message EmailUpdateRequestDetails {
  // List of folios and new emails to which each needs to be updated.
  repeated FolioEmailUpdateDetails update_details = 1;
}

message MobileUpdateRequestDetails {
  // List of folios and new phone numbers to which each needs to be updated.
  repeated FolioMobileUpdateDetails update_details = 1;
}

message FolioEmailUpdateDetails {
  string folio_number = 1;
  string new_email = 2;
  string amc_code = 3;
  MutualFundNftNewDetailsRelationship relationship = 4;
}

message FolioMobileUpdateDetails {
  string folio_number = 1;
  api.typesv2.common.PhoneNumber new_mobile = 2;
  string amc_code = 3;
  MutualFundNftNewDetailsRelationship relationship = 4;
}

message EmailUpdateVendorData {
  // this contains otp details for validating the new email to which the email of folios are being updated
  OtpInfo new_email_verification = 1;
}

message MobileUpdateVendorData {
  // this contains otp details for validating the new mobile to which the phone of folios are being updated
  OtpInfo new_mobile_verification = 1;
}

message OtpInfo {
  // identifier against which the otp is verified
  string otp_reference_number = 1;
  string request_id = 2;
  string user_subject_reference = 3;
  OtpStatus status = 4;
  OtpMedium otp_medium = 5;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}

enum OtpStatus {
  OTP_STATUS_UNSPECIFIED = 0;
  OTP_STATUS_GENERATED = 1;
  OTP_STATUS_VERIFICATION_INCORRECT_OTP = 2;
  OTP_STATUS_VERIFICATION_FAILED = 3;
  OTP_STATUS_VERIFICATION_SUCCESS = 4;
}

enum MfExternalNFTRequestFieldMask {
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_ID = 1;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_ACTOR_ID = 2;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_EXTERNAL_ID = 3;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_CLIENT_REFERENCE_ID = 4;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_TYPE = 5;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_STATUS = 6;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_SUB_STATUS = 7;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_PROVENANCE = 8;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_VENDOR = 9;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_DETAILS = 10;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_CREATED_AT = 11;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_UPDATED_AT = 12;
  MF_EXTERNAL_NFT_REQUEST_FIELD_MASK_DELETED_AT = 13;
}

enum MutualFundNftNewDetailsRelationship {
  MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_UNSPECIFIED = 0;
  MUTUAL_FUND_NFT_NEW_DETAILS_RELATIONSHIP_SELF = 1;
}
