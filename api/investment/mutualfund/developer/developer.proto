// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package api.investment.mutualfund.developer;

import "google/protobuf/timestamp.proto";
import "api/investment/mutualfund/order/order.proto";
import "api/investment/mutualfund/order/file_state.proto";
import "api/investment/mutualfund/order/prerequisite_handler/service.proto";
import "api/investment/mutualfund/order/filegenerator/service.proto";
import "api/investment/mutualfund/order/filegenerator/entity_file_map.proto";
import "api/investment/mutualfund/order/filegenerator/file_generation_attempt.proto";
import "api/investment/mutualfund/payment_handler/payment.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund/developer";
option java_package = "com.github.epifi.gamma.api.investment.mutualfund.developer";

enum MutualFundEntity {
  MUTUAL_FUND_ENTITY_UNSPECIFIED = 0;

  // To fetch mutual_fund orders by
  MUTUAL_FUND_ORDERS = 1;

  // To get mutual_fund investment summary of an actor
  MUTUAL_FUND_FOLIO_LEDGER_FOR_ACTOR = 2;

  // To get the prerequisite status of an actor.
  MUTUAL_FUND_ACTOR_PREREQUISITES = 3;

  // To get the file in which an entity is present. Entity here can be an actor_id or an order_id.
  MUTUAL_FUND_ENTITY_FILE_MAPPER = 4;

  // To get the details of a file generated by file generator.
  MUTUAL_FUND_FILE_GENERATION_ATTEMPTS = 5;

  // To fetch details from mf_batch_order_processing_details
  MUTUAL_FUND_BATCH_ORDER_PROCESSING_DETAILS = 6;

  // To fetch details from mf_batch_order_processing_details
  MUTUAL_FUND_BATCH_ORDER_PROCESSING_STEP_DETAILS = 7;

  // To fetch details of a file communication with a vendor.
  MUTUAL_FUND_FILE_STATE = 8;

  // To fetch payment details of an order
  MUTUAL_FUND_PAYMENTS = 9;

  // To fetch the available funds.
  MUTUAL_FUNDS = 10;

  // To fetch the available AMCs
  MUTUAL_FUND_AMC_INFOS = 11;

  // To fetch all related info to an order
  ORDER_SUMMARY = 12;

  // to fetch all the collections for a screen
  MF_COLLECTIONS = 13;

  // get funds in collections
  FUNDS_IN_COLLECTION = 14;

  // get all the filters
  MF_FILTERS = 15;

  // get all the filter groups
  ALL_FILTERS_WITH_GROUPS = 16;

  // fetch past orders for an actor
  PAST_ORDERS_FOR_ACTOR = 17;

  // order timeline for an order
  ORDER_TIMELINE = 18;

  // collection filter mapping
  COLLECTION_FILTER_MAPPING = 19;

  // To get SIP ledger with given SipRegistrationNumber/ActorId
  MUTUAL_FUND_SIP_LEDGER=20;

  MUTUAL_FUND_EXTERNAL_HOLDINGS_IMPORT_REQUESTS = 21;

  // get a dynamic ui element for a variant
  DYNAMIC_UI_ELEMENT_VARIANT_CONTENT = 22;

  // get dynamic ui element evaluator configuration for screen and usecase
  DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG = 23;

  // fetch nft requests from actor id
  MF_EXTERNAL_NFT_REQUESTS = 24;

  // fetch otp cas import requests from actor id
  MF_SINGLE_OTP_CAS_IMPORT_REQUESTS = 25;
}

message FolioDetails {
  string id = 1;

  // Folio number related with fund
  string folio_id = 2;

  // Actor associated with the folio
  string actor_id = 3;

  // Primary identifier to the mutual fund data model.
  string mutual_fund_id = 4;

  // Optional goal, by default would be empty. Need this field to support jars or specific goals in the future
  string investment_goal = 5;

  string balance_units = 6;

  // Number of units redeemable from the folio. This would vary depending on the lock-in period of certain mutual funds. eg: ELSS
  string redeemable_units = 7;

  // Total amount invested in the folio.
  string invested_value = 8;

  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  google.protobuf.Timestamp deleted_at = 11;

}

// Order Summaries represents list of OrderSummary.
message OrderSummaries {
  repeated OrderSummary order_summary = 1;
}

message EntityFileDetails {
  filegenerator.EntityFileMap entity_file_map = 1;
  filegenerator.FileGenerationAttempt file_generation_attempt = 2;
  order.FileState file_state = 3;
}

message PaymentDetails {
  payment_handler.Payment payment = 1;
  string utr_ref_number = 2;
  google.protobuf.Timestamp transaction_time = 3;
}

// Order Summary represents all details related to an order.
message OrderSummary{

  order.Order order_details = 1;

  // The key of the map represents different prerequisites in string format
  map<string, order.prerequisite_handler.State> prerequisites = 2;

  // The key of the map represents different entity types in string format
  map<string, EntityFileDetails> entity_file_details = 3;
  PaymentDetails payment_details = 4;
}
