syntax = "proto3";

package api.investment.mutualfund.catalog;

import "api/rpc/status.proto";
import "api/investment/mutualfund/mutual_fund.proto";
import "api/investment/mutualfund/catalog/collections.proto";
import "api/investment/mutualfund/folio_ledger.proto";
import "api/rpc/page.proto";
import "google/type/money.proto";
import "api/investment/mutualfund/order/order.proto";
import "google/protobuf/timestamp.proto";
import "api/typesv2/common/device.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund/catalog";
option java_package = "com.github.epifi.gamma.api.investment.mutualfund.catalog";

/*
Catalog manager service for mutual funds helps with management of various mutual fund instruments or schemes that are
offered by epiFi.
The service has RPCs that interacts with the catalog store in database to keep it upto date.

NOTE: other instruments might also need a catalog manager with similar RPCs(if not exactly same). We are taking an
informed decision to keep separate services.
- The mutual fund catalog is fairly static and there aren't many regular updates. NAV needs daily update.
- For stocks, the catalog has more dynamic and real time field updates (like price). The catalog management could be
  fairly complex in this case.
*/
service CatalogManager {
  // RPC creates a new instrument or a mutual fund scheme in our catalog store.
  rpc CreateMutualFund (CreateMutualFundRequest) returns (CreateMutualFundResponse) {}

  // RPC facilitates to update the dynamic fields in the scheme. For example: NAV that needs update everyday.
  // Other field updates are not common but might still be needed. Keeping the contract generic for now.
  rpc UpdateMutualFund (UpdateMutualFundRequest) returns (UpdateMutualFundResponse) {}

  // RPC returns the mutual fund definition for the given input id.
  rpc GetMutualFund (GetMutualFundRequest) returns (GetMutualFundResponse) {}

  //RPC return the mutual fund definition for the given input filters
  //    ISIN no
  //    MF name
  //    AMC
  //    Fund category(Equity/Debt/Cash)
  //    Status(available/not available)
  rpc GetPaginatedMutualFundByFilter(GetPaginatedMutualFundByFilterRequest) returns (GetPaginatedMutualFundByFilterResponse) {}
  // RPC creates a new AMC_INFO in our catalog store.
  rpc CreateAmcInfo (CreateAmcInfoRequest) returns (CreateAmcInfoResponse) {}

  // RPC facilitates to update the dynamic fields in the scheme. For example: NAV that needs update everyday.
  // Other field updates are not common but might still be needed. Keeping the contract generic for now.
  rpc UpdateAmcInfo (UpdateAmcInfoRequest) returns (UpdateAmcInfoResponse) {}

  // RPC returns the mutual fund definition for the given input id.
  rpc GetAmcInfoById (GetAmcInfoByIdRequest) returns (GetAmcInfoByIdResponse) {}

  // RPC returns the mutual fund definition for the given input amc.
  rpc GetAmcInfoByAMC (GetAmcInfoByAMCRequest) returns (GetAmcInfoByAMCResponse) {}

  rpc UpdateMutualFundByISIN (UpdateMutualFundByISINRequest) returns (UpdateMutualFundByISINResponse) {}

  // RPC returns the mutual fund definition for the given input id.
  rpc GetMutualFundByISIN (GetMutualFundByISINRequest) returns (GetMutualFundByISINResponse) {}

  // UploadCatalogFile is intended to be invoked via sherlock
  // rpc UploadCatalogFile uploads the catalog update feed file to S3 bucket
  rpc UploadCatalogFile(UploadCatalogFileRequest) returns (UploadCatalogFileResponse) {}

  // TODO : Move following apis to a separate portfolio service
  // RPC returns the Investment summary information for requested actor.
  // Used primarily for serving the Frontend RPC 'InvestmentSummary'
  rpc GetInvestmentSummaryInfo(GetInvestmentSummaryInfoRequest) returns (GetInvestmentSummaryInfoResponse) {}

  // RPC returns the paginated investment summary for requested actor and all invested mutual funds
  // Used for serving the Frontend RPC 'InvestmentDigest'
  rpc GetInvestments(GetInvestmentsRequest) returns (GetInvestmentsResponse){}

  // RPC returns the summary for all investments per MF for requested actor
  rpc GetInvestmentSummaryForMfs(GetInvestmentSummaryForMfsRequest) returns (GetInvestmentSummaryForMfsResponse){}

  // RPC returns the investment summary for requested actor and invested mutual fund
  // investment summary include total investment, current value of investment and growth of investment
  rpc GetInvestment(GetInvestmentRequest) returns (GetInvestmentResponse){}

  // GetMutualFundCategoryAverage RPC returns category average for a mutual fund category
  // category will be like Index funds, Large Cap funds, ELSS funds
  // MutualFund.MutualFundCategoryName contains list of all the mutual funds
  // RPC is intended to be called from frontend to populate category average
  rpc GetMutualFundCategoryAverage(GetMutualFundCategoryAverageRequest) returns (GetMutualFundCategoryAverageResponse) {}

  // RPC returns a list of mutual fund definitions for the given input identifiers.
  rpc GetMutualFunds (GetMutualFundsRequest) returns (GetMutualFundsResponse) {}

  // GetCollections Returns list of all collections based on the filters passed.
  // following filters are supported
  //        1. screen: collections for specified screen will be sent in response
  // if not filter is passed then all collections will be sent in response
  rpc GetCollections(GetCollectionsRequest) returns (GetCollectionsResponse) {}

  // GetCollectionInfo returns collection definition for the given input identifier
  rpc GetCollectionInfo(GetCollectionInfoRequest) returns (GetCollectionInfoResponse) {}

  // GetWatchListsByActor returns all the watchlist for a given actor
  rpc GetWatchListsByActor(GetWatchListsByActorRequest) returns (GetWatchListsByActorResponse) {}

  // CheckFundsInCollection returns a map to check if a mutual fund is part of a collection
  rpc CheckFundsInCollection(CheckFundsInCollectionRequest) returns (CheckFundsInCollectionResponse){}

  // CheckFundsInWatchList returns a map to check if a mutual fund is part of an actor's watchlist
  rpc CheckFundsInWatchList(CheckFundsInWatchListRequest) returns (CheckFundsInWatchListResponse) {}

  // GetFundsInCollection returns a paginated response with list of mutual funds for a collection
  // sorted by a sort_param
  // in order sort_order present in the request
  rpc GetFundsInCollection(GetFundsInCollectionRequest) returns (GetFundsInCollectionResponse) {}

  // GetWatchListedFundsForActor returns a paginated response with list of mutual funds in a watchlist
  // sorted by a sort_param
  // in order sort_order present in the requests
  rpc GetWatchListedFundsForActor(GetWatchListedFundsForActorRequest) returns (GetWatchListedFundsForActorResponse) {}

  // UpdateWatchList adds/removes a mutual fund from a users's watchlist
  // returns a success or non-success response
  rpc UpdateWatchList(UpdateWatchListRequest) returns (UpdateWatchListResponse) {}

  // GetRecentFundsForActor returns list of mutual funds that the user viewed recently
  rpc GetRecentFundsForActor(GetRecentFundsForActorRequest) returns (GetRecentFundsForActorResponse) {}

  // CreateCollection adds a new collection
  rpc CreateCollection(CreateCollectionRequest) returns (CreateCollectionResponse) {}

  // UpdateCollection can update details of an existing collection
  rpc UpdateCollection(UpdateCollectionRequest) returns (UpdateCollectionResponse) {}

  // AddFundToCollection adds a mutual fund to a collection
  rpc AddFundToCollection(AddFundToCollectionRequest) returns (AddFundToCollectionResponse) {}

  // UpdateFundInfoInCollection changes the details related to an MF in a collection
  rpc UpdateFundInCollection(UpdateFundInCollectionRequest) returns (UpdateFundInCollectionResponse) {}

  // RemoveFundsFromCollection removes one or more MFs from a collection
  rpc RemoveFundFromCollection(RemoveFundFromCollectionRequest) returns (RemoveFundFromCollectionResponse) {}

  // RPC returns the folio ledger details for requested folioID and mutual fund identifier
  rpc GetFolioDetails(GetFolioDetailsRequest) returns (GetFolioDetailsResponse){}

  /* Advanced MF filters/screener related RPCs */

  // GetAvailableFilters resolves the list of selections and de-selections done by user to arrive at a final list of selected filters
  rpc GetSelectedFilters(GetSelectedFiltersRequest) returns (GetSelectedFiltersResponse) {}

  // GetAllUpdatedFilters returns updated list of all filters for input list of selected filters and expanded groups
  rpc GetAllUpdatedFilters(GetAllUpdatedFiltersRequest) returns (GetAllUpdatedFiltersResponse) {}

  // GetAvailableFilters returns the list of filters with more than zero funds based on selected filters
  rpc GetAvailableFilters(GetAvailableFiltersRequest) returns (GetAvailableFiltersResponse) {}

  // GetFiltersForCollection returns the list of filters for a collection
  rpc GetFiltersForCollection(GetFiltersForCollectionRequest) returns (GetFiltersForCollectionResponse) {}

  // GetFundsForFilters returns a paginated list of mutual funds for selected filters and collection, sorted by a param, in order specified
  rpc GetFundsForFilters(GetFundsForFiltersRequest) returns (GetFundsForFiltersResponse) {}

  // ReconcileFolios takes in a list of folioIDs as input and reconciles the folioData keeping mf_orders as source of truth
  rpc ReconcileFolios(ReconcileFoliosRequest) returns (ReconcileFoliosResponse) {}

  // RPC GetFolioLedgersByFilter is a paginated rpc that returns list of folios based on filters
  rpc GetFolioLedgersByFilter(GetFolioLedgersByFilterRequest) returns (GetFolioLedgersByFilterResponse) {}

  // RPC UpdateNomineeForActor takes in an actor id and updates the nominee for that actor at rta for all folios.
  // Currently nominee is not supported in the input. This can be added later if needed.
  rpc UpdateNomineeForActor(UpdateNomineeForActorRequest) returns (UpdateNomineeForActorResponse) {}

  // CreateOrUpdateSearchCatalog is used to get data from mutual funds crdb and update the search catalog with the same
  // Only MUTUAL_FUND_IDENTIFIER_ID is supported
  rpc CreateOrUpdateSearchCatalog(CreateOrUpdateSearchCatalogRequest) returns (CreateOrUpdateSearchCatalogResponse) {}

  // Enqueues a fund to be added to the "Recents" collection of a user
  rpc ProcessRecentlyVisitedFund(ProcessRecentlyVisitedFundRequest) returns (ProcessRecentlyVisitedFundResponse);

  // GetFolioWiseBalance returns a map of mutualfund ids with corresponding folios and corresponding
  // balance units and redeemable units to their folios
  rpc GetFolioWiseBalance(GetFolioWiseBalanceRequest) returns (GetFolioWiseBalanceResponse);

  // CalculateAmountGrowth takes in an amount, investment frequency and fund_id and calculates how much the amount
  // grows over a period of time. The amount to which the investment is defined as aspirational_amount and is
  // calculated with a simple logic described here.
  // refer: https://docs.google.com/document/d/19BRapyp0xqDnGJsP-obK5FADpYBfGeVAkbe2WcEDl-Y/edit#heading=h.f5zzprhdw8mt
  rpc CalculateAmountGrowth(CalculateAmountGrowthRequest) returns (CalculateAmountGrowthResponse);

  // GetNavHistory fetches all the nav values for the given list of ISINs for the given list of dates
  rpc GetNavHistory(GetNavHistoryRequest) returns (GetNavHistoryResponse);

  // GetNavHistoryByMutualFundId fetches all the nav values for the given list of mutual fund ids for the given list of dates
  rpc GetNavHistoryByMutualFundId(GetNavHistoryByMutualFundIdRequest) returns (GetNavHistoryByMutualFundIdResponse);

  // GetNavChartData fetches all the nav values for the given list of mutual fund id for the given duration
  // fetch data point from [curTime-duration,curTime]
  // pre-defined Max data point will be send by adjusting granularity of data point, if more data point lies during interval
  rpc GetNavChartData(GetNavChartDataRequest) returns (GetNavChartDataResponse);

  // GetHistoricalReturn is used to fetch approx amount user would have gained
  // if user has investment in current fund in given duration
  // note: if client ask for duration > mutual fund running time, then we all time return is given to client
  rpc GetHistoricalReturn(GetHistoricalReturnRequest) returns (GetHistoricalReturnResponse);
}

message GetHistoricalReturnRequest{
  string mutual_fund_id = 1;
  google.type.Money amount = 2;
  int32 investment_duration_in_months = 3;
  InvestmentCycle investment_cycle = 4;
}

message GetHistoricalReturnResponse{
  rpc.Status status = 1;
  google.type.Money current_investment_value = 2;
  double return_percent = 3;
  google.type.Money total_invested_amount = 4;
}

message GetSelectedFiltersRequest {
  string collection_id = 1;

  repeated string selected_filter_ids = 2;

  repeated string deselected_filter_ids = 3;
}

message GetSelectedFiltersResponse {
  // rpc response status
  rpc.Status status = 1;

  string collection_id = 2;

  repeated string selected_filter_ids = 3;
}

message GetAllUpdatedFiltersRequest {
  repeated string selected_filter_ids = 1;

  repeated string expanded_filter_group_ids = 2;
}

message GetAllUpdatedFiltersResponse {
  // rpc response status
  rpc.Status status = 1;

  // key is the filter-group id
  map<string, catalog.FundFilterGroupWithFilters> filterGroups = 2;
}

message GetAvailableFiltersRequest {
  repeated string selected_filter_ids = 1;
}

message GetAvailableFiltersResponse {
  // rpc response status
  rpc.Status status = 1;

  repeated string available_filter_ids = 2;
}

message GetFiltersForCollectionRequest {
  string collection_id = 1;
}

message GetFiltersForCollectionResponse {
  // rpc response status
  rpc.Status status = 1;

  // key is the filter-group id
  map<string, catalog.FundFilterGroupWithFilters> filterGroups = 2;
}

message GetFundsForFiltersRequest {
  // for pagination
  rpc.PageContextRequest page_context = 1;

  // sort_param will be used to sort the response
  FundSortParam sort_param = 2;

  enum SortOrder {
    DESCENDING = 0;
    ASCENDING = 1;
  }
  SortOrder sort_order = 3;

  // if collection id is empty only return funds that satisfy the filters. Else use collection id for filtering on funds with Fi-factor for the collection
  // Based on the other filters, Fi-factor funds can be included or discarded
  string collection_id = 4;

  repeated string filter_ids = 5;

  string actor_id = 6;
  // app_platform and app_version are optional fields needed to filter out results based on
  // app version
  api.typesv2.common.Platform app_platform = 7;
  uint32 app_version = 8;
  // text to perform regular text search on the catalog
  // Would be using the name fields for this
  string search_text = 9;
}

message GetFundsForFiltersResponse {
  // rpc response status
  rpc.Status status = 1;

  rpc.PageContextResponse page_context = 2;

  repeated catalog.FilteredMutualFundInfo fund_infos = 3;

  // total funds after filtering, populated in first page only
  uint32 total_funds = 4;
}

message GetCollectionsRequest {
  // screens will be of type collections.CollectionScreen
  catalog.CollectionScreen screen = 1;
  //  actor id is required for user group check
  string actor_id = 2;
}

message GetCollectionsResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  repeated catalog.Collection collections = 2 [deprecated = true];

  // key is the collection id
  map<string, catalog.CollectionWithFundFilterCount> collections_with_filter_counts = 3;
}

message GetCollectionInfoRequest {
  oneof id {
    string collection_id = 1;
    string collection_name = 2;
  }
}
message GetCollectionInfoResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  catalog.Collection collection = 2;
  // total number of filters that the collection is comprised of
  int32 filter_count = 3;
}

message GetWatchListsByActorRequest {
  string actor_id = 1;
}

message GetWatchListsByActorResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  repeated catalog.WatchList watch_lists = 2;
}

message UpdateWatchListRequest {
  enum RequestType {
    REQUEST_TYPE_UNSPECIFIED = 0;
    // Add mutual fund to user's watchlist
    // If fund is already added this is effectively a no-op
    ADD_TO_WATCHLIST = 1;
    // Remove mutual fund from user's watchlist
    // If fund is already not present this is effectively a no-op
    REMOVE_FROM_WATCHLIST = 2;
  }
  string actor_id = 1;
  string mutual_fund_id = 2;
  RequestType request_type = 3;
  // Optional parameter for now.
  // This would be needed when we start supporting multiple watchLists for an actor
  string watchlist_id = 4;
}

message UpdateWatchListResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // No operation was done as part of this request
    NO_OP = 100;
  }
  rpc.Status status = 1;
}

message GetRecentFundsForActorRequest {
  string actor_id = 1;
  // optional sort_param that will be used to sort the response
  // if nil then the response would be sorted in order by their time of addition to recent funds
  FundSortParam sort_param = 2;
  enum SortOrder {
    DESCENDING = 0;
    ASCENDING = 1;
  }
  SortOrder sort_order = 3;
  // app_platform and app_version are optional fields that will be relevant only when the entry point of the rpc request
  // is from a user app. app_platform represents the platform(for eg: Android, ios) from which the request came and
  // app_version represent the build version of that app
  api.typesv2.common.Platform app_platform = 4;
  uint32 app_version = 5;
}

message GetRecentFundsForActorResponse {
  rpc.Status status = 1;
  // list of recent mutual funds info
  repeated RecentMutualFundInfo recent_fund_infos = 2;
}

message CheckFundsInCollectionRequest {
  repeated string mutual_fund_ids = 1;
  oneof identifier{
    string collection_id = 2;
    string collection_name = 3;
  }
}
message CheckFundsInCollectionResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  // key will be mutual fund id
  map<string, CollectionFundInfo>  fund_info = 2;
}

message CollectionFundInfo{
  bool isPresent = 1;
}

message WatchlistFundInfo{
  bool isPresent = 1;
  catalog.WatchListFundDisplayInfo watchlist_display_info = 2;
}

message CheckFundsInWatchListRequest {
  repeated string mutual_fund_ids = 1;
  string actor_id = 2;
}

message CheckFundsInWatchListResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  // key will be mutual fund id
  map<string, WatchlistFundInfo>  fund_info = 2;
}

message GetFundsInCollectionRequest{
  // for pagination
  rpc.PageContextRequest page_context = 1;
  // sort_param will be used to sort the response
  FundSortParam sort_param = 2;
  enum SortOrder {
    DESCENDING = 0;
    ASCENDING = 1;
  }
  SortOrder sort_order = 3;
  // will be used for user group check
  string actor_id = 4;
  string collection_id = 5;

  // app_platform and app_version are optional fields that will be relevant only when the entry point of the rpc request
  // is from a user app. app_platform represents the platform(for eg: Android, ios) from which the request came and
  // app_version represent the build version of that app
  api.typesv2.common.Platform app_platform = 6;
  uint32 app_version = 7;
  // text to perform search on
  string search_text = 8;
}

/*
  Generic sort param for sorting all get Mutual funds calls
 */
enum FundSortParam {
  FUND_SORT_PARAM_UNSPECIFIED = 0;
  FIVE_YR_RETURNS = 1;
  THREE_YR_RETURNS = 2;
  ONE_YR_RETURNS = 3;
  FUND_SIZE = 4;
  EXPENSE_RATIO = 5;
  FUND_AGE = 6;
  RECENTLY_VISITED = 7;
  WATCHLIST_ADDED_BY = 8[deprecated = true];
  // Applicable only for text search
  RELEVANCY = 9;
  // POPULARITY = 10;
  WATCHLISTED_AT = 10;
  MIN_SIP_AMOUNT = 11;
}

message GetFundsInCollectionResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  repeated CollectionMutualFundInfo fund_infos = 3;
  // we be sent only for first page
  uint32 total_funds = 4;
}

message CollectionMutualFundInfo{
  // collection identifier
  string collection_id = 1;

  // mutual fund related fields
  string mutual_fund_id = 2;

  Amc amc = 3;

  FundNameMetadata name_data = 4;

  OptionType option_type = 5;


  AssetClass asset_class = 6;

  // The name of the assigned category of a fund(index fund etc..)
  // Not Modifiable
  MutualFundCategoryName category_name = 7;

  // Risk level of a fund that is decided by a fundhouse(High risk/moderate risk/etc..)
  // Modifiable montlhy by AMC
  FundhouseDefinedRiskLevel fundhouse_defined_risk_level = 8;


  // % avg return by the fund over different intervals
  // Funds can be sorted based on this field
  // updated daily
  Returns returns = 9;

  // various matrices like (tracking error/ alpha/ information ratio/capture ratio) to evalute fund performance
  PerformanceMetrics performance_metrics = 10;

  // fundamental information about a fund like(AUM, MarketCap, FundManagers, expense ratio etc..)
  FundFundamentalDetails fund_fundamental_details = 11;

  // internal fields for display display
  // Modifiable montlhy by Fi content team
  FiContent fi_content = 12;

  // display info specific to both fund and collection
  catalog.CollectionFundDisplayInfo collection_fund_display_info = 13;

  // Direct / Regular mutual fund
  PlanType plan_type = 14;

  // current nav of mutual fund
  google.type.Money nav = 15;

  // Transaction Constraints
  TransactionConstraints txn_constraints = 16;

  // Computed Min Sip amount derived from transaction constraints
  // Would be max(Int32) i.e '2147483647' if Sip is not supported
  int32 computed_min_sip_amount = 17;
}

message RecentMutualFundInfo{
  // time when the user last viewed this fund
  google.protobuf.Timestamp last_viewed_time = 1;

  // mutual fund related fields
  string mutual_fund_id = 2;

  Amc amc = 3;

  FundNameMetadata name_data = 4;

  OptionType option_type = 5;


  AssetClass asset_class = 6;

  // The name of the assigned category of a fund(index fund etc..)
  // Not Modifiable
  MutualFundCategoryName category_name = 7;

  // Risk level of a fund that is decided by a fundhouse(High risk/moderate risk/etc..)
  // Modifiable montlhy by AMC
  FundhouseDefinedRiskLevel fundhouse_defined_risk_level = 8;

  // % avg return by the fund over different intervals
  // Funds can be sorted based on this field
  // updated daily
  Returns returns = 9;

  // various matrices like (tracking error/ alpha/ information ratio/capture ratio) to evalute fund performance
  PerformanceMetrics performance_metrics = 10;

  // fundamental information about a fund like(AUM, MarketCap, FundManagers, expense ratio etc..)
  FundFundamentalDetails fund_fundamental_details = 11;

  // internal fields for display display
  // Modifiable monthly by Fi content team
  FiContent fi_content = 12;

  // Direct / Regular mutual fund
  PlanType plan_type = 13;

  TransactionConstraints txn_constraints = 14;
  // Computed Min Sip amount derived from transaction constraints
  // Would be max(Int32) i.e '2147483647' if Sip is not supported
  int32 computed_min_sip_amount = 15;
}

message GetWatchListedFundsForActorRequest{
  // for pagination
  rpc.PageContextRequest page_context = 1;
  // sort_param will be used to sort the response
  FundSortParam sort_param = 2;
  enum SortOrder {
    DESCENDING = 0;
    ASCENDING = 1;
  }
  SortOrder sort_order = 3;
  // will be used for user group check
  string actor_id = 4;
  string watch_list_id = 5;
  // app_platform and app_version are optional fields that will be relevant only when the entry point of the rpc request
  // is from a user app. app_platform represents the platform(for eg: Android, ios) from which the request came and
  // app_version represent the build version of that app
  api.typesv2.common.Platform app_platform = 6;
  uint32 app_version = 7;
}
message GetWatchListedFundsForActorResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  repeated WatchListedMutualFundInfo fund_infos = 3;
  // we be sent only for first page
  uint32 total_funds = 4;
}

message WatchListedMutualFundInfo{
  // watchlist identifier
  string watch_list_id = 1;

  // mutual fund related fields
  string mutual_fund_id = 2;

  Amc amc = 3;

  FundNameMetadata name_data = 4;

  OptionType option_type = 5;


  AssetClass asset_class = 6;

  // The name of the assigned category of a fund(index fund etc..)
  MutualFundCategoryName category_name = 7;

  // Risk level of a fund that is decided by a fundhouse(High risk/moderate risk/etc..)
  FundhouseDefinedRiskLevel fundhouse_defined_risk_level = 8;


  // % avg return by the fund over different intervals
  // Funds can be sorted based on this field
  Returns returns = 9;

  // various matrices like (tracking error/ alpha/ information ratio/capture ratio) to evalute fund performance
  PerformanceMetrics performance_metrics = 10;

  // fundamental information about a fund like(AUM, MarketCap, FundManagers, expense ratio etc..)
  FundFundamentalDetails fund_fundamental_details = 11;

  // internal fields for display display
  FiContent fi_content = 12;

  // Direct / Regular mutual fund
  PlanType plan_type = 13;

  // display info of a fund in a watchlist
  catalog.WatchListFundDisplayInfo display_info = 14;

  // current nav of mutual fund
  google.type.Money nav = 15;

  // Transaction Constraints
  TransactionConstraints txn_constraints = 16;

  // Computed Min Sip amount derived from transaction constraints
  // Would be max(Int32) i.e '2147483647' if Sip is not supported
  int32 computed_min_sip_amount = 17;
}

message GetMutualFundCategoryAverageRequest{
  MutualFundCategoryName category = 1;
}

message GetMutualFundCategoryAverageResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  MutualFundCategoryAverage category_average = 2;
}

message CreateMutualFundRequest {
  MutualFund fund = 1;
}

message CreateMutualFundResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  MutualFund fund = 2;
}

message UpdateMutualFundRequest {
  MutualFund updated_fund = 1;
}

message UpdateMutualFundResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message GetMutualFundRequest {
  // unique identifier for the mutual fund entry in table.
  string id = 1;
}

message GetMutualFundResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  MutualFund mutual_fund = 2;
}

message GetMutualFundsRequest {
  MutualFundIdentifier fund_identifier = 1;
  repeated string ids = 2;
}

message GetMutualFundsResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  map<string, MutualFund>  mutual_funds = 2;
}


message GetPaginatedMutualFundByFilterRequest {
  // filters for the mutual fund in table.
  Amc amc = 1;//    AMC name
  string mutual_fund_name = 2;//    Mutual Fund name
  string isin_number = 3;//    ISIN no
  AssetClass asset_class = 4;//    Fund category(Equity/Debt/Cash)
  rpc.PageContextRequest page_context = 5; // for pagination
  MutualFundInternalStatus internal_status = 6; // Status(available/not available)
  MutualFundFieldMask sort_param = 7;
  enum SortOrder {
    DESCENDING = 0;
    ASCENDING = 1;
  }
  SortOrder sort_order = 8;

  // if actor_id is not empty, then only the funds that are whitelisted for the user_groups + the funds that are whitelisted
  // for all actors will be returned for the input filter conditions.
  // if actor_id is empty, then all funds are returned for the input filter conditions.
  string actor_id = 9;

  // app_platform and app_version are optional fields that will be relevant only when the entry point of the rpc request
  // is from a user app. app_platform represents the platform(for eg: Android, ios) from which the request came and
  // app_version represent the build version of that app
  api.typesv2.common.Platform app_platform = 10;
  uint32 app_version = 11;

}

message GetPaginatedMutualFundByFilterResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  repeated MutualFund mutual_funds = 2;
  rpc.PageContextResponse page_context = 3;
  // will be sent only for first page
  uint32 total_funds = 4;
}

message CreateAmcInfoRequest {
  AmcInfo amc_info = 1;
}

message CreateAmcInfoResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  AmcInfo amc_info = 2;
}

message UpdateAmcInfoRequest {
  AmcInfo updated_amc_info = 1;
}

message UpdateAmcInfoResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message GetAmcInfoByIdRequest {
  // unique identifier for the mutual fund entry in table.
  string id = 1;
}

message GetAmcInfoByIdResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  AmcInfo amc_info = 2;
}

message UpdateMutualFundByISINRequest {
  MutualFund updated_fund = 1;
  repeated MutualFundFieldMask field_masks = 2;
}

message UpdateMutualFundByISINResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message GetMutualFundByISINRequest {
  // unique identifier for the mutual fund entry in table.
  string isin = 1;
}

message GetMutualFundByISINResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  MutualFund mutual_fund = 2;
}

message GetAmcInfoByAMCRequest {
  Amc amc = 1;
}

message GetAmcInfoByAMCResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  AmcInfo amc_info = 2;
}

message GetInvestmentSummaryInfoRequest{
  string actor_id = 1;
}

message GetInvestmentSummaryInfoResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  InvestmentSummaryInfo investment_summary = 2;
}

// TODO: Add sort params and order.
message GetInvestmentsRequest{
  string actor_id = 1;
  rpc.PageContextRequest page_context = 2; // for pagination
  // key in this map will be FilterField enum
  // all the filters will be applied with "AND" operations
  repeated Filter filters = 3;
}

enum FilterFieldMask{
  FilterFieldMask_UNSPECIFIED = 0;
  CREATED_AT = 1;
  AMC = 2;
}

message Filter{
  enum Comparator{
    EQUAL = 0;
    GREATER_OR_EQUAL = 1;
    SMALLER_OR_EQUAL = 2;
    GREATER = 3;
    SMALLER = 4;
  }
  Comparator comparator = 1;
  FilterFieldMask filter_field = 2;
  oneof filter_value{
    double numeric_val = 3;
    string string_val = 4;
    google.protobuf.Timestamp time_val = 5;
    Amc amc_val = 6;
  }
}

message GetInvestmentsResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  repeated MutualFundInvestmentInfo mutual_funds_investment_infos = 2;
  rpc.PageContextResponse page_context = 3;

}

/*
  Investment summary holds the aggregated information for an actor across all the folios
  Used for serving the Frontend rpc 'InvestmentSummary'
 */
message InvestmentSummaryInfo{
  google.type.Money current_value = 1;
  google.type.Money total_invested_value = 2;
  double growth_percentage = 3;
  google.protobuf.Timestamp nav_updated_at = 4;
  // Flag to identify if there are orders in non-terminal state
  bool pending_orders_present = 5;
  // Optional field for holding the latest pending order. Please verify before using
  order.Order latest_pending_order = 6;
  double balancedUnits = 7;
  // Latest updated time for folio
  google.protobuf.Timestamp latest_folio_updated_at = 8;
  // Optional field for holding the latest order placed by user
  order.Order latest_order = 9;
  // This field is passed only when there are non-terminal orders
  google.type.Money in_progress_buy_order_amount = 10;
  // Total number of pending orders. This should be zero if pending_orders_present = false
  int32 pending_orders_count = 11;
  // returns true if user has at-least one order in any state except CREATION_ON_HOLD
  bool has_investment = 12;
}

/*
  Wrapper over InvestmentSummaryInfo. Holds aggregated information for all the folios for
  an actor+mutual fund combination.
  Used for serving the Frontend rpc 'InvestmentDigest'
 */
message MutualFundInvestmentInfo{
  string mutual_fund_id = 1;
  string mutual_fund_name = 2;
  Amc amc = 3;
  AssetClass asset_class = 4;
  InvestmentSummaryInfo investment_summary = 5;
  // Latest timestamp when the folio ledger was updated
  google.protobuf.Timestamp folio_updated_at = 6;
  // mutual fund is available for investment on FI app
  MutualFundInternalStatus internal_fund_status = 7;
  // Latest timestamp when a order was placed
  google.protobuf.Timestamp last_transaction_date = 8;
  MutualFundCategoryName category_name = 9;
}

message GetInvestmentRequest{
  string mutual_fund_id = 1;
  string actor_id = 2;
}

message GetInvestmentResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  InvestmentSummaryInfo investment_summary = 2;
}

// GetFolioDetailsStatus represent the status of a folio detail fetch. If a folio number is present in the db, SUCCESS is
// returned, if it is not present, NOT_FOUND is returned
enum GetFolioDetailsStatus {
  GetFolioDetailsStatus_UNSPECIFIED = 0;
  GetFolioDetailsStatus_SUCCESS = 1;
  GetFolioDetailsStatus_NOT_FOUND = 2;
}
message GetFolioDetailsOutput {
  mutualfund.FolioLedger folio_ledger = 1;
  GetFolioDetailsStatus status = 2;
}

message GetFolioDetailsRequest{
  // folio_ids are generally unique identifiers. But it could be possible that different rta generate the same folio_id
  // so, we are making use of a mutual fund identifier to fetch details of a folio.
  repeated string folio_ids = 1;
  oneof mutual_fund_identifier{
    string scheme_code = 2;
    string id = 3;
  }
  mutualfund.Amc amc = 4;
}

message GetFolioDetailsResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  map<string, GetFolioDetailsOutput> folio_id_to_details_map = 2;
}


message UploadCatalogFileRequest {
  // name of file to upload
  string file_name = 1;
  // byte content of file to be uploaded
  bytes file_content = 2;
  // folder inside S3 bucket where file will be uploaded
  string folder_name = 3;
}

message UploadCatalogFileResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message GetInvestmentSummaryForMfsRequest{
  string actor_id = 1;
  repeated string mf_ids = 2;
}

message InvestmentSummary{
  google.type.Money total_invested_value = 1;
  google.type.Money current_value = 2;
  double growth_percentage = 3;
  bool invested = 4;
}

message GetInvestmentSummaryForMfsResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  map<string, InvestmentSummary>  investment_summaries = 2;
}

message CreateCollectionRequest {
  catalog.Collection collection = 1;
}

message CreateCollectionResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message UpdateCollectionRequest {
  catalog.Collection collection = 1;
}

message UpdateCollectionResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message AddFundToCollectionRequest {
  string mutual_fund_id = 1;
  string collection_id = 2;
  // display only information for a mutual fund in a collection
  catalog.CollectionFundDisplayInfo display_info = 3;
}

message AddFundToCollectionResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message UpdateFundInCollectionRequest {
  string mutual_fund_id = 1;
  string collection_id = 2;
  // display only information for a mutual fund in a collection
  catalog.CollectionFundDisplayInfo display_info = 3;
}

message UpdateFundInCollectionResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message RemoveFundFromCollectionRequest {
  string mutual_fund_id = 1;
  string collection_id = 2;
}

message RemoveFundFromCollectionResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message GetFolioLedgersByFilterRequest {
  rpc.PageContextRequest page_context = 1; // for pagination
  string actor_id = 2;
  repeated string mutual_fund_ids = 3;
}

message GetFolioLedgersByFilterResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  repeated mutualfund.FolioLedger folio_ledgers = 3;
}

message UpdateNomineeForActorRequest {
  string actor_id = 1;
  // list of folio_ids belonging to the actor. If an empty array is passed, then all folio_ids of the actor is updated.
  repeated string folio_ids = 2;
}

message UpdateNomineeForActorResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message CreateOrUpdateSearchCatalogRequest{
  enum RequestType {
    UNSPECIFIED = 0;
    FULL_REFRESH = 1;
    ID_REFRESH = 2;
  }
  RequestType request_type = 1;
  // Needed when syncing catalog data by ID
  // Can be ignored for 'FULL_SYNC'
  MutualFundIdentifier fund_identifier = 2 [(validate.rules).enum = {in: [1]}];
  repeated string ids = 3;
}

message CreateOrUpdateSearchCatalogResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message ProcessRecentlyVisitedFundRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  string mf_id = 2 [(validate.rules).string.min_len = 1];
}

message ProcessRecentlyVisitedFundResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}

message ReconcileFoliosRequest {
  repeated string folio_ids = 1;
  Amc amc = 2;
}

message ReconcileFoliosResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  repeated string reconciliation_successful_folios = 2;
  map<string, string> folio_id_to_reconciliation_failure_reason = 3;
}

message GetFolioWiseBalanceRequest{
  string mutual_fund_id = 1;
  string actor_id = 2;
}

message GetFolioWiseBalanceResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;

  // this is an array of folio balance details which includes FolioDetails corresponding to an actorId and mutualfundId
  repeated FolioDetails folio_details = 2;
}

message FolioDetails{
  string folio_number = 1;
  float balance_units = 2;
  float withdrawable_units = 3;
}

enum InvestmentFrequency {
  UNSPECIFIED = 0;
  ONE_TIME = 1;
  DAILY = 2;
  WEEKLY = 3;
  MONTHLY = 4;
}

message CalculateAmountGrowthRequest {
  InvestmentFrequency investment_frequency = 1;
  google.type.Money amount = 2;
  string mutual_fund_id = 3;
}

message CalculateAmountGrowthResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // INSUFFICIENT_DATA is returned when backend doesn't have enough data to calculate growth amount.
    INSUFFICIENT_DATA = 100;
    // Not Possible to reach growth amount
    GROWTH_AMOUNT_NOT_POSSIBLE = 101;
  }
  // rpc response status
  rpc.Status status = 1;
  google.type.Money growth_amount = 2;
  int32 days = 3;
}

message GetNavHistoryRequest {
  repeated NavRequest nav_requests = 1 [(validate.rules).repeated.max_items = 20];
}

// Nav details for the given mutual fund scheme will be fetched for the given list of dates
message NavRequest {
  string isin = 1;
  repeated google.protobuf.Timestamp dates = 2;
}

message GetNavHistoryResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  repeated NavHistory nav_histories = 2;
}

message NavHistory {
  string isin = 1;
  string mutual_fund_id = 4;
  repeated NavDetails nav_details = 2;
  // missing_dates refer to the dates for which nav was requested but the value was not present in the DB.
  repeated google.protobuf.Timestamp missing_dates = 3;
}

message NavDetails {
  // the nav is given for this date
  google.protobuf.Timestamp date = 1;
  // The nav value for scheme on the given date.
  google.type.Money nav = 2;
  // The nav source tells the source of the mutual fund nav value. It can either by given by vendor or it can be derived from the previous nav.
  MutualFundNavSource nav_source = 3;
  // If the nav is derived from previous date nav then nav_dervied_date refers to that date, else it is same as date field.
  google.protobuf.Timestamp nav_derived_date = 4;
}

message GetNavHistoryByMutualFundIdRequest {
  repeated NavRequestByMutualFundId nav_requests = 1 [(validate.rules).repeated.max_items = 500];
}

// Nav details for the given mutual fund scheme will be fetched for the given list of dates
message NavRequestByMutualFundId {
  string mutual_fund_id = 1;
  repeated google.protobuf.Timestamp dates = 2;
}

message GetNavHistoryByMutualFundIdResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  repeated NavHistory nav_histories = 2;
}

message GetNavChartDataRequest{
  string mutual_fund_id = 1;
  // represent duration of graph
  // data point will be fetch from [cur_time-duration,cur_time] according to granularity
  NavChartDuration duration = 2;
}

message GetNavChartDataResponse{
  // rpc response status
  rpc.Status status = 1;
  // represent the data point required for plotting graph
  repeated NavChartDataPoint nav_chart_data_points = 2;
}

message NavChartDataPoint {
  // since time stamp contain more granularity
  // can be converted into any format according to clients requirement
  google.protobuf.Timestamp nav_date = 1;
  // nav at nav_date
  // reason: double to ease mathematical operation at client
  //  nav is in INR
  double nav = 2;
}

message NavChartDuration{
  oneof chart_duration{
    int32 duration_in_months = 1;
    // if client want data time from start of mf and current time
    bool from_start_of_fund = 2;
  }
}


message InvestmentCycle{
  oneof investment_cycle{
    int32 duration_in_days = 1;
    int32 duration_in_months = 2;
    // if the client is expected to do one time investment
    bool is_one_time_investment = 3;
  }
}
