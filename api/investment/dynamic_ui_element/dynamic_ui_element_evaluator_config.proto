syntax = "proto3";

package api.investment.dynamic_ui_element;

import "google/protobuf/timestamp.proto";


option go_package = "github.com/epifi/gamma/api/investment/dynamic_ui_element";
option java_package = "com.github.epifi.gamma.api.investment.dynamic_ui_element";


//go:generate gen_sql -types=DynamicUIScreen,DynamicUIUsecase,EvaluatorExpressionConfig
message DynamicUIElementEvaluatorConfig {
  // primary identifier for dynamic_ui_element_evaluator_config DB model
  string id = 1;
  // DynamicUIScreen is the screen for which the ui element has to be configured,
  DynamicUIScreen dynamic_ui_screen = 2;
  // DynamicUIUsecase is the usecase on the screen for eg. banner,recommendations unit etc.
  DynamicUIUsecase dynamic_ui_usecase = 3;
  EvaluatorExpressionConfig evaluator_expression_config = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  int64 deleted_at_unix = 7;
}

enum DynamicUIScreen {
  DYNAMIC_UI_SCREEN_UNSPECIFIED = 0;
  DYNAMIC_UI_SCREEN_HOME = 1;
  DYNAMIC_UI_SCREEN_INVEST_LANDING = 2;
  DYNAMIC_UI_SCREEN_MF_LANDING = 3;
  // deeplink screen name :  USSTOCKS_LANDING_SCREEN
  // figma
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=11989-30263&mode=dev
  DYNAMIC_UI_SCREEN_USSTOCKS_LANDING = 4;
  // deeplink screen name : USSTOCKS_COLLECTION_SCREEN
  // figma
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=3939-18683&mode=dev
  DYNAMIC_UI_SCREEN_USSTOCKS_COLLECTION = 5;

  // Collections to show on US stocks feature pages of Epifi website
  DYNAMIC_UI_SCREEN_USSTOCKS_WEBSITE = 6;
}

enum DynamicUIUsecase {
  DYNAMIC_UI_USECASE_UNSPECIFIED = 0;
  DYNAMIC_UI_USECASE_BANNER = 1;
  DYNAMIC_UI_USECASE_RECOMMENDATIONS_COMPONENT = 2;
  // figma of one UI component that will be rendered
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=11989-30852&mode=dev
  DYNAMIC_UI_USECASE_USSTOCKS_LANDING_COMPONENT = 3;
  // figma of collection component in UI
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=3939-19294&mode=dev
  DYNAMIC_UI_USECASE_USSTOCKS_COLLECTION = 4;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=22987-30281&mode=design&t=i1yH7UsImlvqD4PO-4
  DYNAMIC_UI_USECASE_USSTOCKS_HOME_GTM_WIDGET_3_POINTS = 5;
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=24291-37028&mode=design&t=i1yH7UsImlvqD4PO-4
  DYNAMIC_UI_USECASE_USSTOCKS_HOME_GTM_WIDGET_4_POINTS = 6;
  // Display content within a card.
  // Optionally include tabs for switching between different tabs content views if available.
  // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
  DYNAMIC_UI_USECASE_USSTOCKS_HOME_TABBED_CARD = 7;

  // https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks---FFF?node-id=39303-11334&t=uKkwxSoWB1OIcL4B-0
  DYNAMIC_UI_USECASE_USFUNDS_LANDING_COMPONENT = 8;
}

// EvaluatorExpressionConfig contains a list of EvaluatorExpression
message EvaluatorExpressionConfig {
  repeated EvaluatorExpression evaluator_expressions = 1;
}

// EvaluatorExpression contains a custom expression for a segment, and the variant that corresponds to it.
message EvaluatorExpression {
  // expression is a custom expression for segment, A/B, time-range etc.
  string expression = 1;
  string variant_name = 2;
}

enum DynamicUIElementEvaluatorConfigFieldMask {

  DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG_FIELD_MASK_UNSPECIFIED = 0;
  DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG_FIELD_MASK_DYNAMIC_UI_SCREEN = 1;
  DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG_FIELD_MASK_DYNAMIC_UI_USECASE = 2;
  DYNAMIC_UI_ELEMENT_EVALUATOR_CONFIG_FIELD_MASK_EVALUATOR_EXPRESSION_CONFIG = 3;
}
