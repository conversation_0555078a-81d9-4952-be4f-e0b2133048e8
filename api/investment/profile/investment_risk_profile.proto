syntax = "proto3";

//go:generate gen_sql -types=InvestmentRiskLevel
package api.investment.profile;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/investment/profile";
option java_package = "com.github.epifi.gamma.api.investment.profile";

// InvestmentRiskProfile is a recommended investment risk level and breakdown across instruments provided to user based on the survey taken
message InvestmentRiskProfile {
  // primary identifier for investment_strategy DB model
  string id = 1;
  // actor id of the customer
  string actor_id = 2;
  // primary identifier of investment_risk_survey table (foreign key)
  string risk_survey_id = 3;
  // overall risk score of the user calculated based on the survey
  float overall_risk_score = 4;

  // InvestmentRiskLevel assigned to the user based on investment risk Q&A and existing user profile info
  InvestmentRiskLevel risk_level = 5;

  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  int64 deleted_at_unix = 8;
}

// InvestmentRiskLevel suggested to user based on user's survey
// each enum will be mapped to a configurable distribution of investment instruments and instrument level sub categories
enum InvestmentRiskLevel {
  INVESTMENT_RISK_LEVEL_UNSPECIFIED = 0;

  // relatively low risk investments like income fund, gilt fund etc
  INVESTMENT_RISK_LEVEL_CONSERVATIVE = 1;

  // relatively moderate risk investments like debt index fund, corporate bond etc
  INVESTMENT_RISK_LEVEL_MODERATE = 2;

  // Moderate and aggressive are the only two risk levels now.
  // relatively high risk investments like index large cap, credit risk etc
  INVESTMENT_RISK_LEVEL_MODERATELY_AGGRESSIVE = 3 [deprecated = true];

  // Moderate and aggressive are the only two risk levels now.
  // very high risk investments like mid cap, small cap etc
  INVESTMENT_RISK_LEVEL_VERY_AGGRESSIVE = 4 [deprecated = true];

  // High risk investments like equity funds, etc.
  INVESTMENT_RISK_LEVEL_AGGRESSIVE = 5;
}
