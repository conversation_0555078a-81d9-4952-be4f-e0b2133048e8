// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package order.aa;

import "api/order/aa/transaction.proto";
import "api/order/payment/accounting_entry_type.proto";
import "api/order/payment/payment_protocol.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/sort.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/order/aa";
option java_package = "com.github.epifi.gamma.api.order.aa";

// TODO : Move below RPCs to CA Service
service AccountAggregator {
  // GetTransactions fetches a list of transactions belonging to the pi.
  // for credit transaction will return transaction only if pi_to in pi_filter
  // for debit transaction will return transaction only if pi_from in pi_filter
  // Thus, rpc method returns the list in pages. It returns all the transactions from specified start_timestamp.
  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  // The max number of transactions returned is bounded by the page_size specified in the request, while offset helps in
  // eliminating initial entries which are already returned in the last call.
  //
  // Sorting logic - The transactions are sorted based on executed timestamp
  rpc GetTransactions (GetTransactionsRequest) returns (GetTransactionsResponse) {}

  // GetTransactionCountForPis returns the txn count for the pi ids passed in the request
  rpc GetTransactionCountForPis (GetTransactionCountForPisRequest) returns (GetTransactionCountForPisResponse) {}

  // GetTransactionCountForActors returns the txn count for the actor ids passed in the request
  rpc GetTransactionCountForActors (GetTransactionCountForActorsRequest) returns (GetTransactionCountForActorsResponse) {}

  // GetTransaction returns a single transaction based on the transaction identifier passed
  rpc GetTransaction (GetTransactionRequest) returns (GetTransactionResponse);

  // GetTransactionsCount returns the count of transactions for given actor and filter.
  rpc GetTransactionsCount (GetTransactionsCountRequest) returns (GetTransactionsCountResponse);

  // BatchUpsertEnrichedTransaction updates the transaction. If the transaction is not present,
  // it creates a new transaction entry in the database
  rpc BatchUpsertEnrichedTransaction (BatchUpsertEnrichedTransactionRequest) returns (BatchUpsertEnrichedTransactionResponse);

  // GetTransactionsV1 returns list of transactions corresponding to given identifier
  rpc GetTransactionsV1 (GetTransactionsV1Request) returns (GetTransactionsV1Response);

  // BatchCreateEnrichedTransactions creates Enriched transactions in Db and returns the enriched transactions with generated id populated in them
  rpc BatchCreateEnrichedTransactions (BatchCreateEnrichedTransactionsRequest) returns (BatchCreateEnrichedTransactionsResponse);

  // GetTransactionCountForAccountId returns number of enriched transactions for a given accountId
  rpc GetTransactionCountForAccountId (GetTransactionCountForAccountIdRequest) returns (GetTransactionCountForAccountIdResponse);

  // GetEnrichedTransactionsByAccountId is a paginated rpc to get Enriched Transactions by AccountId. It returns Enriched Transactions and token to fetch next transactions.
  rpc GetEnrichedTransactionsByAccountId (GetEnrichedTransactionsByAccountIdRequest) returns (GetEnrichedTransactionsByAccountIdResponse);
}

message GetTransactionsRequest {
  // list of pis for which the transactions needs to be fetched
  // for credit transaction will return transaction only if pi_to in pi_filter
  // for debit transaction will return transaction only if pi_from in pi_filter
  repeated string pi_filter = 1 [(validate.rules).repeated.min_items = 1];

  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp start_timestamp = 2 [(validate.rules).timestamp.required = true];

  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  // Page size must be in the range [10, 40]
  // minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
  int32 page_size = 3 [(validate.rules).int32 = {gte: 10, lte: 40}];

  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 offset = 4 [(validate.rules).int32.gte = 0];

  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  bool is_descending = 5;

  // txn field masks to ensure only desired fields are returned from aa transactions table.
  // this helps in optimization fo data returned and make the query more efficient.
  // caller can choose to get all field of transaction by specifying ALL in field mask
  // Only first transaction corresponding to an order is returned.
  repeated order.aa.AATransactionFieldMask txn_field_masks = 6;

  // optional: transaction type wrt the given actor.
  // The caller can use this to filter out the results corresponding to a particular tranactionType.
  // If kept empty, by default all the transaction (credit or debit) related to the actor are returned
  // If TransactionType is TRANSACTION_TYPE_UNSPECIFIED, all the transaction related to the actor are returned.
  order.payment.AccountingEntryType transaction_type = 7;

  // optional: timestamp till ending for which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  // If kept empty, by default all the transactions related to the actor are returned.
  google.protobuf.Timestamp end_timestamp = 8;

  // optional: list of other payment instrument ids TO or FROM which payments was made.
  // It is used to fetch transactions between a list of from and to pi_ids
  // In case of debit transaction, it corresponds to the list of to_pi_ids
  // In case of credit transaction it corresponds to the list of from_pi_ids
  repeated string other_pi_filter = 9;

  // optional: order payment_protocol filter. orders with payment_protocol in this list are considered.
  // If kept empty, by default all the orders related to the actor are returned
  repeated order.payment.PaymentProtocol payment_protocol = 10;
}

message GetTransactionsResponse {
  enum Status {
    OK = 0;

    // in case transactions are not found for a given set of request params.
    RECORD_NOT_FOUND = 5;

    // internal error while processing the request
    INTERNAL = 13;
  }
  // denotes the status of the GetTransactions Request
  rpc.Status status = 1;

  // list of transactions
  repeated order.aa.Transaction transactions = 2;
}

message GetTransactionCountForPisRequest {
  // list of pi ids for which we need the txn count for
  repeated string pi_ids = 1;
}

message GetTransactionCountForPisResponse {
  enum Status {
    OK = 0;

    // internal error while processing the request
    INTERNAL = 13;
  }

  // denotes the status of the GetTransactions Request
  rpc.Status status = 1;

  // map between pi id and txn count
  map<string, int32> pi_id_to_txn_count_map = 2;
}

message GetTransactionCountForActorsRequest {
  // list of actor ids for which we need the txn count for
  repeated string actor_ids = 1;
}

message GetTransactionCountForActorsResponse {
  enum Status {
    OK = 0;

    // internal error while processing the request
    INTERNAL = 13;
  }

  // denotes the status of the GetTransactions Request
  rpc.Status status = 1;

  // map between actor id and txn count
  map<string, int32> actor_id_to_txn_count_map = 2;
}

message GetTransactionRequest {
  oneof identifier {
    string id = 1;
  }
}

message GetTransactionResponse {
  enum Status {
    OK = 0;

    // in case transactions are not found for a given set of request params.
    NOT_FOUND = 5;

    // internal error while processing the request
    INTERNAL = 13;
  }

  // denotes the status of the GetTransaction Request
  rpc.Status status = 1;

  order.aa.Transaction transaction = 2;
}

message GetTransactionsCountRequest {
  // actor id of the user
  string actor_id = 1;

  // optional: list of other pi ids
  repeated string other_pi_ids = 2;

  // starting time from which we want the transactions
  google.protobuf.Timestamp start_time = 3;
  // ending time until which we want the transactions
  google.protobuf.Timestamp end_time = 4;

  // optional: transaction type wrt the given actor.
  // The caller can use this to filter out the results corresponding to a particular transactionType.
  // If kept empty, by default all the orders (credit or debit) related to the actor are returned
  // If TransactionType is TRANSACTION_TYPE_UNSPECIFIED, all the orders related to the actor are returned.
  order.payment.AccountingEntryType transaction_type = 5;

  // optional: payment_protocol filter. orders with payment_protocol in this list are considered
  repeated order.payment.PaymentProtocol payment_protocols = 6;
}

message GetTransactionsCountResponse {
  enum Status {
    OK = 0;

    // internal error while processing the request
    INTERNAL = 13;
  }

  // denotes the status of the GetTransaction Request
  rpc.Status status = 1;

  int64 count = 2;
}

message BatchUpsertEnrichedTransactionRequest {
  repeated order.aa.Transaction transaction_list = 1 [(validate.rules).repeated = {min_items: 1, max_items: 1000}];
}

message BatchUpsertEnrichedTransactionResponse {
  rpc.Status status = 1;
}

message GetTransactionsV1Request {
  oneof identifier {
    BatchAATxnIds batch_aa_txn_ids = 1;
  }
}

message BatchAATxnIds {
  repeated string aa_txn_id_list = 1 [(validate.rules).repeated = {min_items: 1, max_items: 1000}];
}

message GetTransactionsV1Response {
  rpc.Status status = 1;
  repeated order.aa.Transaction transaction_list = 2;
}

message BatchCreateEnrichedTransactionsRequest {
  repeated order.aa.Transaction transaction_list = 1 [(validate.rules).repeated = {min_items: 1, max_items: 1000}];
}

message BatchCreateEnrichedTransactionsResponse {
  rpc.Status status = 1;
  repeated order.aa.Transaction transaction_list = 2;
}

message GetTransactionCountForAccountIdRequest {
  string account_id = 1;
}

message GetTransactionCountForAccountIdResponse {
  rpc.Status status = 1;
  uint32 count = 2;
}

message GetEnrichedTransactionsByAccountIdRequest {
  // page context for pagination
  rpc.PageContextRequest page_context = 1;
  // mandatory
  string account_id = 2 [(validate.rules).string.min_len = 1];
  // optional param to fetch transactions satisfying the given filters
  EnrichedTransactionFilters filters = 3;
  // sort order of results by executed_at
  api.typesv2.common.SortOrder sort_order = 4 [(validate.rules).enum = {not_in: [0]}];
}

message GetEnrichedTransactionsByAccountIdResponse {
  // below are the status codes that can be returned by this RPC
  // 200 - OK
  // 5 - NOTFOUND - ( when no transactions for an account are found )
  // 13 - INTERNAL
  rpc.Status status = 1;
  // page context response
  rpc.PageContextResponse page_context = 2;
  // list of transactions satisfying the criteria
  repeated order.aa.Transaction enriched_transaction_list = 3;
}

message EnrichedTransactionFilters {
  // optional param to only fetch transactions done after given timestamp : inclusive
  // If this parameter is not passed, all transactions are considered
  google.protobuf.Timestamp executed_after = 1;
}
