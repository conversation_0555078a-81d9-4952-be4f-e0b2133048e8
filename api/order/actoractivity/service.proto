// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package actoractivity;

import "api/accounts/account_type.proto";
import "api/order/actoractivity/activity/activity.proto";
import "api/order/actoractivity/enums/activity_source.proto";
import "api/order/payment/accounting_entry_type.proto";
import "api/order/payment/payment_protocol.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/visual_element.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/order/actoractivity";
option java_package = "com.github.epifi.gamma.api.order.actoractivity";

// Service to manage actor activity
// Will contain rpcs
//  1) To fetch all the activity for an actor in a paginated manner
service ActorActivity {
  // GetActivities fetches a list of activities to a combination of actor and accounts passed in the request.
  // The activities list can grow very big with time.
  // Thus, rpc method returns the list in pages. It returns all the activities from specified start_timestamp.
  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  // The max number of activities returned is bounded by the page_size specified in the request, while offset helps in
  // eliminating initial entries which are already returned in the last call.
  //
  // Sorting logic - The orders are sorted based on last updated timestamp of the order.
  //
  // NOTE - one of the account filter or the pi filter should be present
  // if both account and pi filters are present, all the activities associated with the account
  // or th pi will be returned
  rpc GetActivities (GetActivitiesRequest) returns (GetActivitiesResponse) {}

  // GetFinancialActivities fetches a list of financial activities for the actor passed in the request
  // It fetches all the financial activities of fi account, connected account etc. based on the type of request
  // This method returns the list in pages. It returns all the activities from specified start_timestamp.
  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  // The max number of activities returned is bounded by the page_size specified in the request, while offset helps in
  // eliminating initial entries which are already returned in the last call.
  //
  // Sorting logic - The orders are sorted based on last updated timestamp of the order.
  rpc GetFinancialActivities (GetFinancialActivitiesRequest) returns (GetFinancialActivitiesResponse) {}
}


message GetActivitiesRequest {
  // current actor id for which the activities needs to be fetched
  string current_actor_id = 1;

  message AccountFilter {
    // account Id to filter the results
    string account_id = 1;
    // type of the account for the account id
    accounts.Type account_type = 2;
  }
  // list of accounts to filter the results
  // only activities associated with the account id in these filters will be returned
  repeated AccountFilter account_filter = 2;
  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp activities_start_timestamp = 3;
  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  // Page size must be in the range [10, 40]
  // minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
  int32 page_size = 4 [(validate.rules).int32 = {gte: 10, lte: 40}];
  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  // deprecated. use order offset and aaTxnOffset
  int32 activity_offset = 5 [deprecated = true];
  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  bool descending = 6;
  // list of PiIds to filter the result.
  // Activities associated with these piIds will be returned
  // deprecated in favour of use of payment_filter options
  repeated string pi_filter = 7 [deprecated = true];
  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 order_offset = 8;
  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 aa txns starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 aa_txn_offset = 9;

  // timestamp till which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  // If kept empty, by default all the timestamps related to the actor are returned
  google.protobuf.Timestamp activities_end_timestamp = 10;

  message PaymentFilter {
    // optional: order payment_protocol filter. orders with payment_protocol in this list are considered.
    // If kept empty, by default all the orders related to the actor are returned
    repeated order.payment.PaymentProtocol payment_protocol = 1;

    // Optional: The minimum amount or the lower limit of the amount to be searched on.
    google.type.Money from_amount = 2;

    // Optional: The maximum amount or the upper limit of the amount to be searched on.
    google.type.Money to_amount = 3;

    // optional: transaction type wrt the given actor.
    // The caller can use this to filter out the results corresponding to a particular transactionType.
    // If kept empty, by default all the orders (credit or debit) related to the actor are returned
    // If TransactionType is TRANSACTION_TYPE_UNSPECIFIED, all the orders related to the actor are returned.
    order.payment.AccountingEntryType transaction_type = 5;

    // list of PiIds to filter the result.
    // Activities associated with these piIds will be returned
    repeated string pi_filter = 6;
  }

  // Optional: filter based on payment specific fields such as protocol, transaction type, amount, etc.
  PaymentFilter payment_filter = 11;

  // page landing timestamp is the time when user opens the all transactions page.
  // since this activity is called in paginated fashion to render all transactions, this timestamp is used to fetch the data in their updated state till this time using time travel query. This is because order status can change while user is scrolling the all transactions page which can break the pagination logic.
  // If user want to get latest he needs to exit and come back to this page
  //  NOTE: this timestamp is only used when the rpc is called to render all transactions page
  google.protobuf.Timestamp page_landing_timestamp = 12;

  // based on entry point we decide which backend order RPC to be called, due to evolving different needs of internal teams and UI
  // we have decided to keep different RPC one for internal services and one for UI / app
  enum EntryPointType {

    ENTRY_POINT_TYPE_UNSPECIFIED = 0;

    ENTRY_POINT_TYPE_UI = 1;

    ENTRY_POINT_TYPE_INTERNAL_SERVICE = 2;
  }

  EntryPointType entry_point_type = 13;

  // flag to determine if activities for soft-deleted users should be fetched or not
  // This is helpful when the activities are between users where one of them is soft-deleted (this can happen when the account is closed etc).
  // For e.g., using this for all-txns page will help when the non-deleted user is browsing all the txns. Otherwise, it would lead to missing txns or page breaking.
  bool fetch_soft_deleted_users = 14;
}

message GetActivitiesResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // no activity found for the actor
    RECORD_NOT_FOUND = 5;
    // Internal error while processing the request
    INTERNAL = 13;
  }
  // rpc status for the request
  rpc.Status status = 1;

  message Activity {
    // icon image url
    string icon_url = 1;
    // bold desc / title for the activity
    string title = 2;
    // short desc for the activity
    string short_desc = 3;
    // short desc icon url
    string short_desc_icon_url = 4;
    // Amount involved in the transaction
    google.type.Money amount = 5;

    enum AmountBadge {
      // unspecified
      AMOUNT_BADGE_UNSPECIFIED = 0;
      DEBIT = 1;
      CREDIT = 2;
      SAVINGS = 3;
      FAILED = 4;
      REVERSED = 5;
      IN_PAYMENT = 6;
    }
    // badge to be applied on the amount
    AmountBadge amount_badge = 6;
    // timestamp of the activity
    // given the activity will be in terminal state, it will be equal to last updated time
    google.protobuf.Timestamp activity_timestamp = 7;
    // unique identifier representing the activity
    // will change depending on the type of the activity
    // for pay and collect fund transfers will be equal to order id
    string activity_id = 8;
    // actor activity of the second actor involved in the transaction
    // we need the second actor id in the response to populate the actor colour
    string second_actor_id = 9;

    message DepositAccountIdentifier {
      string account_id = 1;
      // type of the account for the account id
      accounts.Type account_type = 2;
    }

    oneof deeplink_identifier {
      // unique identifier for the timeline between
      // the two actors involved in the activity
      string timeline_id = 10;

      DepositAccountIdentifier deposit_account_identifier = 11;
    }

    enum Type {
      ACTIVITY_TYPE_UNSPECIFIED = 0;
      IMPS_TRANSACTION_SUCCESS = 1;
      IMPS_TRANSACTION_FAILED = 2;
      NEFT_TRANSACTION_SUCCESS = 3;
      NEFT_TRANSACTION_FAILED = 4;
      RTGS_TRANSACTION_SUCCESS = 5;
      RTGS_TRANSACTION_FAILED = 6;
      UPI_TRANSACTION_SUCCESS = 7;
      UPI_TRANSACTION_FAILED = 8;
      INTRABANK_TRANSACTION_SUCCESS = 9;
      INTRABANK_TRANSACTION_FAILED = 10;
      ATM_TRANSACTION_SUCCESS = 11;
      ATM_TRANSACTION_FAILED = 12;
      SMART_DEPOSIT_CREATED = 13;
      SMART_DEPOSIT_MATURED = 14;
      SMART_DEPOSIT_PRECLOSED = 15;
      SMART_DEPOSIT_AMT_ADDED = 16;
      FIXED_DEPOSIT_CREATED = 17;
      FIXED_DEPOSIT_MATURED = 18;
      FIXED_DEPOSIT_PRECLOSED = 19;
      IMPS_TRANSACTION_REVERSED = 20;
      NEFT_TRANSACTION_REVERSED = 21;
      RTGS_TRANSACTION_REVERSED = 22;
      UPI_TRANSACTION_REVERSED = 23;
      INTRABANK_TRANSACTION_REVERSED = 24;
      ATM_TRANSACTION_REVERSED = 25;
      DEBIT_CARD_TRANSACTION_SUCCESS = 26;
      DEBIT_CARD_TRANSACTION_FAILED = 27;
      DEBIT_CARD_TRANSACTION_REVERSED = 28;
      SMART_DEPOSIT_INTEREST_CREDIT = 29;
      FIXED_DEPOSIT_INTEREST_CREDIT = 30;
      NEFT_TRANSACTION_PENDING = 31;
      RTGS_TRANSACTION_PENDING = 32;
      UPI_TRANSACTION_PENDING = 33;
      INTRABANK_TRANSACTION_PENDING = 34;
      IMPS_TRANSACTION_PENDING = 35;
      ENACH_TRANSACTION_FAILED = 36;
      ENACH_TRANSACTION_SUCCESS = 37;
      ENACH_TRANSACTION_PENDING = 38;
      ENACH_TRANSACTION_REVERSED = 39;
    }
    Type activity_type = 12;

    ActivityEntryPoint activity_entry_point = 13;

    // partner tag for the activity
    // e.g. AutoPay logo for the upi
    // mandate autoPay txn
    api.typesv2.common.VisualElement partner_tag = 14;
  }
  // list of activities for the actor
  repeated Activity activities = 2;
}

message GetFinancialActivitiesRequest {
  // current actor id for which the activities needs to be fetched
  string current_actor_id = 1;

  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp activities_start_timestamp = 2;

  // timestamp till which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  // If kept empty, by default all the timestamps related to the actor are returned
  google.protobuf.Timestamp activities_end_timestamp = 3;

  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  // Page size must be in the range [10, 40]
  // minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
  int32 page_size = 4 [(validate.rules).int32 = {gte: 10, lte: 40}];

  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  bool descending = 5;

  // activity source specifies the source of financial activities that we want
  // we can specify whether we want fi transactions activities or connected account transactions activities
  // If kept empty, by default all the financial activities related to the actor are returned
  // If TransactionSource is ACTIVITY_SOURCE_UNSPECIFIED, all the financial activities related to the actor are returned.
  order.actoractivity.enums.ActivitySource activity_source = 6;

  message ActivityOffSet {
    // fi_txn_offset lets the caller control the number of records that needs to be skipped
    // starting from start timestamp for fi txns
    int32 fi_txn_offset = 1;
    // connected_account_txn_offset lets the caller control the number of records that needs to be skipped
    // starting from start timestamp for connected account txns
    int32 connected_account_txn_offset = 2;
  }

  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // It contains offset for different type of txns such as fi and connected account separately
  ActivityOffSet activity_offset = 7;

  message PaymentFilter {
    // optional: order payment_protocol filter. orders with payment_protocol in this list are considered.
    // If kept empty, by default all the orders related to the actor are returned
    repeated order.payment.PaymentProtocol payment_protocol = 1;

    // Optional: The minimum amount or the lower limit of the amount to be searched on.
    google.type.Money from_amount = 2;

    // Optional: The maximum amount or the upper limit of the amount to be searched on.
    google.type.Money to_amount = 3;

    // optional: transaction type wrt the given actor.
    // The caller can use this to filter out the results corresponding to a particular transactionType.
    // If kept empty, by default all the orders (credit or debit) related to the actor are returned
    // If TransactionType is TRANSACTION_TYPE_UNSPECIFIED, all the orders related to the actor are returned.
    order.payment.AccountingEntryType transaction_type = 4;

    message PiFilter {
      // optional: list of internal PiIds to filter the result.
      // Activities associated with only these piIds will be returned
      repeated string internal_pi_filter = 1;

      // optional: list of connected account PiIds to filter the result.
      // Activities associated with only these piIds will be returned
      repeated string aa_pi_filter = 2;

      // optional: list of other internal PiIds to filter the result.
      // Activities associated with between piIds for the actor and other given piIds will be returned
      repeated string other_internal_pi_filter = 3;

      // optional: list of other connected account PiIds to filter the result.
      // Activities associated with between connected account piIds for the actor and other given
      // connected account piIds will be returned
      repeated string other_aa_pi_filter = 4;
    }

    // list of internal and connected account piIds to filter the result
    // Activities associated with only these piIds will be returned
    PiFilter pi_filter = 5;
  }

  // Optional: filter based on payment specific fields such as protocol, transaction type, amount, etc.
  PaymentFilter payment_filter = 8;
}

message GetFinancialActivitiesResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // no activity found for the actor
    RECORD_NOT_FOUND = 5;
    // Internal error while processing the request
    INTERNAL = 13;
  }
  // rpc status for the request
  rpc.Status status = 1;

  // list of activities for the actor
  repeated order.actoractivity.activity.Activity activities = 2;
}

// entry point for the activity in the actor activity service
enum ActivityEntryPoint {
  // unspecified
  ACTIVITY_ENTRY_POINT_UNSPECIFIED = 0;
  // activity is fetched from order service
  ORDER = 1;
  // activity is fetched from aa service
  AA = 2;
  // activity is fetched from cc service
  CREDIT_CARD = 3;
}
