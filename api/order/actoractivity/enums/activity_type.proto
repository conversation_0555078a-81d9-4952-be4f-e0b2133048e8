// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package order.actoractivity.enums;

option go_package = "github.com/epifi/gamma/api/order/actoractivity/enums";
option java_package = "com.github.epifi.gamma.api.order.actoractivity.enums";

// type of activity like NEFT,IMPS transaction or
enum ActivityType {
  ACTIVITY_TYPE_UNSPECIFIED = 0;
  IMPS_TRANSACTION_SUCCESS = 1;
  IMPS_TRANSACTION_FAILED = 2;
  NEFT_TRANSACTION_SUCCESS = 3;
  NEFT_TRANSACTION_FAILED = 4;
  RTGS_TRANSACTION_SUCCESS = 5;
  RTGS_TRANSACTION_FAILED = 6;
  UPI_TRANSACTION_SUCCESS = 7;
  UPI_TRANSACTION_FAILED = 8;
  INTRABANK_TRANSACTION_SUCCESS = 9;
  INTRABANK_TRANSACTION_FAILED = 10;
  ATM_TRANSACTION_SUCCESS = 11;
  ATM_TRANSACTION_FAILED = 12;
  SMART_DEPOSIT_CREATED = 13;
  SMART_DEPOSIT_MATURED = 14;
  SMART_DEPOSIT_PRECLOSED = 15;
  SMART_DEPOSIT_AMT_ADDED = 16;
  FIXED_DEPOSIT_CREATED = 17;
  FIXED_DEPOSIT_MATURED = 18;
  FIXED_DEPOSIT_PRECLOSED = 19;
  IMPS_TRANSACTION_REVERSED = 20;
  NEFT_TRANSACTION_REVERSED = 21;
  RTGS_TRANSACTION_REVERSED = 22;
  UPI_TRANSACTION_REVERSED = 23;
  INTRABANK_TRANSACTION_REVERSED = 24;
  ATM_TRANSACTION_REVERSED = 25;
  DEBIT_CARD_TRANSACTION_SUCCESS = 26;
  DEBIT_CARD_TRANSACTION_FAILED = 27;
  DEBIT_CARD_TRANSACTION_REVERSED = 28;
  SMART_DEPOSIT_INTEREST_CREDIT = 29;
  FIXED_DEPOSIT_INTEREST_CREDIT = 30;
  NEFT_TRANSACTION_PENDING = 31;
  RTGS_TRANSACTION_PENDING = 32;
  UPI_TRANSACTION_PENDING = 33;
  INTRABANK_TRANSACTION_PENDING = 34;
  IMPS_TRANSACTION_PENDING = 35;
  ENACH_TRANSACTION_FAILED = 36;
  ENACH_TRANSACTION_SUCCESS = 37;
  ENACH_TRANSACTION_PENDING = 38;
  ENACH_TRANSACTION_REVERSED = 39;
}
