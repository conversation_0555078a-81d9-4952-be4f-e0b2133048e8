// protolint:disable MAX_LINE_LENGTH
// Defines the grpc service that is registered with queue subscriber. The rpc method defined is invoked on receiving a
// queue message.
syntax = "proto3";

package order;

import "api/accounts/account_type.proto";
import "api/aws/s3/s3.proto";
import "api/order/payment/notification/merchant_details.proto";
import "api/order/payment/notification/parsed_txn_particulars.proto";
import "api/order/payment/notification/txn_details.proto";
import "api/order/workflow.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/account/enums.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/order";
option java_package = "com.github.epifi.gamma.api.order";

service Consumer {
  // ProcessOrder orchestrates the order state machine according to the underlying domain service.
  // This method is invoked by queue subscriber to consume order related queue packet.
  rpc ProcessOrder (ProcessOrderRequest) returns (ProcessOrderResponse) {}

  // ProcessInboundTxnOrder creates an entry in order and transaction for the notification received from vendor.
  // Since we'll get notifications for transactions initiated from our end as well, we'll de-dup using request_id.
  // This method is invoked by queue subscriber to notifications from vendor.
  rpc ProcessInboundTxnOrder (ProcessInboundTxnOrderRequest) returns (ProcessInboundTxnOrderResponse) {}

  // ProcessWorkflow does order workflow orchestration with underlying domain service
  // This method is invoked by queue subscriber to consumer order workflow related queue packer
  rpc ProcessWorkflow (ProcessWorkflowRequest) returns (ProcessWorkflowResponse) {}

  // ProcessDeclineCardTransactionsDataDump processes the event publish after addition of new file to sftp(aws managed sftp) server.
  // It will get the file name from event request. We will process the decline data and add order and transactions for
  // the failed card transactions.
  rpc ProcessDeclineCardTransactionsDataDump (ProcessDeclineCardTransactionsDataDumpRequest) returns (ProcessDeclineCardTransactionsDataDumpResponse) {}

  // ProcessUpiInboundTxnOrder is wrapper consumer over ProcessInboundTxnOrder to segregate UPI and NON-UPI transaction.
  // This is done to isolate upi and non-upi inbound transaction processing in case of UPI outages. It will internally call the ProcessInboundTxnOrder consumer function to process the transaction.
  rpc ProcessUpiInboundTxnOrder (ProcessInboundTxnOrderRequest) returns (ProcessInboundTxnOrderResponse) {}

  // ProcessTxnNotification will process transaction notification.
  // If Transaction is for add fund  check if the 1st leg is created
  // then will update the utr for 1st leg transaction.
  rpc ProcessTxnNotification (ProcessTxnNotificationRequest) returns (ProcessTxnNotificationResponse) {}
}

message ProcessOrderRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  string order_id = 2 [(validate.rules).string.min_len = 1];

  // A boolean to denote if the payment is reversed or not
  //
  // Once an order is in terminal state, the order orchestrator does not accept new payloads
  // to prevent stale updates and reverse state machine updates
  //
  // An order will be in terminal state once the original transaction is deemed to be success.
  // Given that the reversal transaction happens post the original transaction, order orchestrator
  // needs a flag to determine if it can process the payload even if the order is in terminal state.
  //
  // This reversal boolean will act as one
  bool is_reversal = 3;
}


message ProcessOrderResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

// TODO(kunal): Add validation after getting spec clarity
message ProcessInboundTxnOrderRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  // Vendor bank where transaction occurred
  vendorgateway.Vendor partner_bank = 2 [(validate.rules).enum = {not_in: [0]}];

  order.payment.notification.TransactionDetails transaction_details = 3;
  order.payment.notification.MerchantDetails merchant_details = 4 [deprecated = true];
  string request_id = 6;
  // details fetched after parsing the txn particulars
  order.payment.notification.ParsedTxnParticulars parsed_txn_particulars = 7;

  // field to specify the account type to which inbound notification belongs
  accounts.Type notification_account_type = 8;

  // Enum to denote notification endpoint version. With upgrading version different reliability/availability/delay is possible.
  enum NotificationEndPointVersion {
    NOTIFICATION_END_POINT_VERSION_UNSPECIFIED = 0;
    // V0 version endpoint. Possibility of delayed notification during high load time. Also some notification might not reach to Fi from vendor on this endpoint (vendor side constraint).
    NOTIFICATION_END_POINT_VERSION_V0 = 1;
    // V0 upgraded to V1 version with more reliability.
    NOTIFICATION_END_POINT_VERSION_V1 = 2;
  }
  NotificationEndPointVersion notification_end_point_version = 9;
  // Timestamp indicating the arrival of vendor notification
  google.protobuf.Timestamp notification_timestamp = 10;

  // Account Product Offering associated with the AccountType.
  // The value has to be used along with AccountType for accurate identification of the account.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
  api.typesv2.account.AccountProductOffering apo = 11;

  // Workflow to be used during creation of the order.
  // Note: this is optional.
  // Usage: Can be used to override NO_OP workflow for specific cases.
  // For e.g., in case of OFF_APP_UPI Temporal Preempt (i.e. to send via SQS post short enquiry), we can use OFF_APP_UPI workflow.
  optional OrderWorkflow order_workflow = 12;
}

message ProcessInboundTxnOrderResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessWorkflowRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  string order_id = 2;
}

message ProcessWorkflowResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessDeclineCardTransactionsDataDumpRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;
  // The notification message that Amazon S3 sends to publish an event is in the JSON format.
  repeated aws.s3.Record records = 2 [json_name = "Records"];
}

message ProcessDeclineCardTransactionsDataDumpResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

// Data received in the files for processing card failure transactions
message CardFailureTransaction {
  // timestamp at which transaction was done
  google.protobuf.Timestamp transaction_timestamp = 1;
  google.type.Money amount = 2;
  // response code for failed txn
  string raw_response_code = 3;
  // reason for transaction failure
  string raw_response_reason = 4;
  // transaction category can be ECOM/ATM/POS
  order.CardTransactionCategory transaction_category = 5;
  // name of merchant
  string merchant_name = 6;
  // merchant location
  string merchant_location = 7;
  // merchant id
  string merchant_id = 8;
  // merchant category code
  string mcc_code = 9;
  string atm_id = 10;
  // unique identifier for a transaction
  string arn = 11;
  // account number of user
  string account_number = 12;
  // vendor
  vendorgateway.Vendor vendor = 13;
  // masked card number
  string masked_card_number = 14;
}

// transaction category can be either ECOMM/ATM/POS
enum CardTransactionCategory {
  CARD_TRANSACTION_CATEGORY_UNSPECIFIED = 0;
  // Ecomm txn
  CARD_TRANSACTION_CATEGORY_ECOMM = 1;
  // ATM txn
  CARD_TRANSACTION_CATEGORY_ATM = 2;
  // POS txn
  CARD_TRANSACTION_CATEGORY_POS = 3;
}

message ProcessTxnNotificationRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  // order id
  string order_id = 2;

  // utr for the 1st leg txn
  string utr = 3;
}

message ProcessTxnNotificationResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}
