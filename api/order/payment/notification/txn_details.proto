// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package order.payment.notification;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";
import "api/order/payment/transaction.proto";

option go_package = "github.com/epifi/gamma/api/order/payment/notification";
option java_package = "com.github.epifi.gamma.api.order.payment.notification";


// TransactionDetails transaction details received in vendor transaction notifications for epiFi account
message TransactionDetails {
  string account_number = 1 [(validate.rules).string = {min_len: 1, max_len: 35}];
  string id = 2;
  string type = 3;
  google.protobuf.Timestamp timestamp = 4;
  google.type.Date date = 5;
  google.type.Date value_date = 6 [(validate.rules).message.required = true];
  google.type.Money amount = 7 [(validate.rules).message.required = true];
  string particular = 8 [(validate.rules).string.min_len = 1];
  string reference_number = 9;
  string remarks = 10;
  string report_code = 11;

  // type of notification
  enum NotificationEventType {
    option deprecated = true;
    NOTIFICATION_TYPE_UNSPECIFIED = 0;

    // notification received on a credit to the account
    CREDIT = 1;

    // notification received on a debit to the account
    DEBIT = 2;
  }

  NotificationEventType notification_event_type = 12 [(validate.rules).enum = {not_in: [0]}];

  // additional transaction related information received from notification from the partner banks
  // in case of federal below holds true:
  // UPI - TransactionId
  // Inward NEFT/RTGS - Sender Account Number and IFSC
  // Outward NEFT/RTGS - Beneficiary Account Number and IFSC
  // Card Transactions - Merchant Details(MCC, City, CountryCode)
  string additional_particular = 13;

  // to identify a transaction uniquely (Combination of Tran_Id, Tran_date and batch_serial_id will be unique)
  string batch_serial_id = 14 [(validate.rules).string.min_len = 1];

  // Instrument(like DD/Cheque) Details
  order.payment.InstrumentDetails instrument_details = 15;
}

