// protolint:disable MAX_LINE_LENGTH

/*
Protos relating to the VKYC karza customer info DAO model
*/

syntax = "proto3";

package kyc.vkyc;

import "google/protobuf/timestamp.proto";
import "api/frontend/account/screening/service.proto";
import "api/employment/employment_data.proto";

option go_package = "github.com/epifi/gamma/api/kyc/vkyc";
option java_package = "com.github.epifi.gamma.api.kyc.vkyc";

// VKYCKarzaCustomerInfo proto for db table
message VKYCKarzaCustomerInfo {

  // Primary identifier to vkyc_karza customer info
  string id = 1;

  // Vkyc summary id of main vkyc table
  string vkyc_summary_id = 2;

  // Bank vendor(federal) customer id. Will have a uniqueness constraint on this
  string customer_id = 3;

  // Karza customer identifier corresponding to bank vendor customer id
  string transaction_id = 4;

  // This will contain the latest customer info passed to karza for vkyc.
  VKYCKarzaCustomerInfoTransactionMetadata transaction_metadata = 5;

  // denotes the kind of priority for the user depending on which, vendor credentials would be decided at VG
  // using enum instead of bool since more than one credentials might be expected in future
  VKYCPriorityType vkyc_priority_type = 8;

  // created at
  google.protobuf.Timestamp created_at = 6;

  // updated at
  google.protobuf.Timestamp updated_at = 7;
}

message VKYCKarzaCustomerInfoTransactionMetadata {
  // as customer weblink is permanent for an transaction(customer) we can store this
  // at customer info level
  string weblink = 1;
  // date when kyc was performed(expected to be ekyc)
  google.protobuf.Timestamp kyc_date = 2;
  // Unique id received against aadhaar validation(rnn number will be stored in format according to vendor need)
  string ekyc_rrn_no = 3;
  google.protobuf.Timestamp vkyc_registered_at = 4;
  frontend.account.screening.AnnualSalary annual_salary = 5;
  employment.EmploymentType employment_type = 6;
}

// VKYCCallScheduleFieldMask is used to mask columns to update in DB Update call
enum VKYCKarzaCustomerInfoFieldMask {
  VKYC_KARZA_CUSTOMER_INFO_FIELD_MASK_UNSPECIFIED = 0;
  VKYC_KARZA_CUSTOMER_INFO_FIELD_MASK_TRANSACTION_METADATA = 1;
  VKYC_KARZA_CUSTOMER_INFO_FIELD_MASK_CUSTOMER_ID = 2;
  VKYC_KARZA_CUSTOMER_INFO_FIELD_MASK_TRANSACTION_ID = 3;
}

// denotes the kind of priority for the user depending on which, vendor credentials would be decided at VG
// using enum instead of bool since more than one credentials might be expected in future
enum VKYCPriorityType {
  // default 0 value can be inferred as existing general/non-priority flow
  VKYC_PRIORITY_TYPE_UNSPECIFIED = 0;
  // DEFAULT_PRIORITY denotes the users assigned in the first most introduced priority
  VKYC_PRIORITY_TYPE_DEFAULT_PRIORITY = 1;
  // denotes the users who need to re do the vkyc
  VKYC_PRIORITY_TYPE_RE_VKYC = 2;
  // Federal loans vkyc
  VKYC_PRIORITY_TYPE_LOAN = 3;
}
