// protolint:disable MAX_LINE_LENGTH

/*
Protos relating to the KYC that are internal to the domain such as data models
*/


syntax = "proto3";

package kyc;

import "api/kyc/kyc.proto";
import "api/pan/enums.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/kyc";
option java_package = "com.github.epifi.gamma.api.kyc";


// KycAttempt is an internal domain concept for KYC.
// It stores data for each KYC attempt on behalf of an Actor. KYC can be attempted
// multiple times for a single Actor e.g. in case CKYC fails and we switch to e-KYC
// in the second attempt or user had manual KYC done a second time after the first one expires etc.
message KycAttempt {
  string kyc_attempt_id = 1;
  string client_req_id = 12;
  string actor_id = 2;
  string source = 3;  // vendor source e.g. "Federal"
  kyc.KycType kyc_type = 4;
  KYCState state = 5;
  KYCAttemptRequestParams request_params = 6;
  LivenessParams liveness_params = 7 [deprecated = true];
  repeated FaceMatchParams face_match_params = 8 [deprecated = true];
  Scores scores = 9;
  FailureReason failure_reason = 10;
  BKYCInfo bkyc_info = 13;
  google.protobuf.Timestamp updated_at = 11;
  google.protobuf.Timestamp created_at = 14;
}

// Status of the FM attempt
enum StatusFaceMatch {
  STATUS_FACE_MATCH_UNSPECIFIED = 0;
  STATUS_FACE_MATCH_VIDEO_NOT_RECEIVED = 1;
  // Pending the vendor response.
  STATUS_FACE_MATCH_PENDING = 2;
  STATUS_FACE_MATCH_PASSED = 3;
  // Failed means FM failed and shouldn't be retried.
  STATUS_FACE_MATCH_FAILED = 4;
  // FM Expired means that the current attempt of FM has expired. It can be retried.
  STATUS_FACE_MATCH_EXPIRED = 6;
  STATUS_FACE_MATCH_RETRY = 5;
  // Mark Liveness success for a user after verifying it through human eyes.
  STATUS_FACE_MATCH_MANUALLY_PASSED = 7;
}

message FaceMatchParams {
  // Unique ID that maps the Face Match request in Liveness service.
  string request_id = 1;
  api.typesv2.common.Image reference_image = 2;
  StatusFaceMatch status = 3;
  float fm_threshold = 9;

  // This maps a CKYC Record with the face match request.
  // A single CKYC attempt could fetch multiple records
  string record_identifier = 4;

  // Represents how close the match was. Used for CKYC record selection.
  float face_match_score = 5;
}

// Status of the Liveness request
enum StatusLiveness {
  STATUS_LIVENESS_UNSPECIFIED = 0;
  // Video Not Received.
  STATUS_LIVENESS_VIDEO_NOT_RECEIVED = 1;
  // User video received and stored in AWS.
  STATUS_LIVENESS_VIDEO_RECEIVED = 2;
  // Failed means liveness check failed and shouldn't be retried.
  STATUS_LIVENESS_FAILED = 3;
  // Liveness Expired means that the current attempt of Liveness has expired. It can be retried.
  STATUS_LIVENESS_EXPIRED = 7;
  STATUS_LIVENESS_RETRY = 4;
  // Pending on vendor response.
  STATUS_LIVENESS_PENDING = 5;
  STATUS_LIVENESS_PASSED = 6;
  // Mark Liveness success for a user after verifying it through human eyes.
  STATUS_LIVENESS_MANUALLY_PASSED = 8;
}

// Error of the Liveness request
enum ErrorLiveness {
  ERROR_LIVENESS_UNSPECIFIED = 0;
  // FACE Not detected
  ERROR_LIVENESS_FACE_NOT_DETECTED = 1;
  // Multiple faces detected
  ERROR_LIVENESS_MULTIPLE_FACES_DETECTED = 3;
  // Invalid Video
  ERROR_LIVENESS_INVALID_VIDEO = 2;
  // Face poorly detected
  ERROR_LIVENESS_FACE_POORLY_DETECTED = 4;
  // Face is too far
  ERROR_LIVENESS_FACE_TOO_FAR = 5;
  // Face is too close
  ERROR_LIVENESS_FACE_TOO_CLOSE = 6;
  // Face is too dark
  ERROR_LIVENESS_FACE_TOO_DARK = 7;
  // Face is too bright
  ERROR_LIVENESS_FACE_TOO_BRIGHT = 8;
  // No face detected in video
  ERROR_LIVENESS_NO_FACE_DETECTED = 9;
}

message LivenessParams {
  // Unique ID that maps the Liveness request in Liveness service.
  string request_id = 1;
  // OTP to be spoken by the user.
  string otp = 2;
  float otp_score = 7;
  float liveness_score = 8;
  float liveness_threshold = 9;
  float otp_threshold = 10;
  StatusLiveness status = 3;
  ErrorLiveness error_liveness = 11;
  string video_location = 4;
  api.typesv2.common.Image image = 5;
  int64 attempt_count = 6;
  bool disable_face_tracking_android = 12;
}

// KYCAttemptRequestParams stores data for async processing of a KYC request
// for e.g. PAN Number / DOB for async processing of Search CKYC request
message KYCAttemptRequestParams {
  CKYCSearchRequestParams ckyc_search = 1;
  CKYCDownloadRequestParams ckyc_download = 2;
  KYCLevel level = 3;
  // Date of birth given by the user. It's matched with the DOB in the KYC record.
  google.type.Date given_dob = 4;
  // Name given by the user. It's matched with name in the KYC record.
  api.typesv2.common.Name given_name = 5;
  // Name of the user returned in dedupe response
  api.typesv2.common.Name dedupe_name = 6;
  // denotes source which is trying to initiate ekyc
  kyc.EkycSource ekyc_source = 7;
  // Unique id received against aadhaar validation.
  string ekyc_rrn_no = 8;
  // Struct to store the details of onboarding number and ekyc registered number mismatch
  EKYCNumberMismatch ekyc_number_mismatch = 9;
  // struct to store Dob info
  DOBDiscrepancy dob_discrepancy_info = 10;
  // In CKYC, father and mother names are matched with the given names by the user
  // In EKYC, care-of field received is matched with the given parent names by the user
  NameMatchInfo father_name_match = 11;
  NameMatchInfo mother_name_match = 12;
  // hash of the last 4 digits of aadhaar received from ekyc name dob validation API
  string ekyc_name_dob_validation_aadhaar_digits_hash = 13;
  // hash of the last 2 digits of aadhaar received from ekyc name dob validation API
  string ekyc_name_dob_validation_aadhaar_last2_digits_hash = 14;
}

// BKYCInfo has all data related to Biometric KYC flow for a user.
message BKYCInfo {
  BKYCAgentInfo bkyc_agent_info = 1;
  // denotes source which is trying to initiate biometric kyc
  kyc.BKYCSource bkyc_source = 2;
  // denotes the type of pan used for customer due diligence during biometric kyc.
  pan.PanType pan_type = 3;
  // s3 filepath where the PAN image is uploaded.
  string pan_image_s3_url = 4;
  // consent id for the customer due diligence consent taken from the agent.
  string customer_due_diligence_consent_id = 5;
}

// BKYCAgentInfo has information related to the agent who performs the Biometric KYC.
message BKYCAgentInfo {
  // actor id of agent
  string actor_id = 1;
  // location token while doing kyc
  string location_token = 2;
  // ip address while doing kyc
  string ip_address_token = 3;
}

message NameMatchInfo {
  // Score received from the inhouse name match model
  float name_match_score = 1;
  // Boolean flag to store if the names have matched
  api.typesv2.common.BooleanEnum name_matched = 2;
}

message DOBDiscrepancy {
  // The flag stores the mismatch of ekyc dob and profile dob
  api.typesv2.common.BooleanEnum discrepancy_exists = 1;
  // dob stored in users table
  google.type.Date user_profile_dob = 2;
  // dob received in ekyc record
  google.type.Date ekyc_dob = 3;
}

message EKYCNumberMismatch {
  // The flag stores the mismatch of onboarding number with the number on which aadhar otp is sent
  api.typesv2.common.BooleanEnum is_number_mismatch = 1;
  // Last 4 digits of the ekyc registered phone number
  string ekyc_registered_number = 2;
  // Last 4 digits of the onboarding number
  string onboarding_number = 3;
}

// CKYCSearchRequest stores details required to make Search CKYC call to vendor
message CKYCSearchRequestParams {
  kyc.IdProof id_proof = 1;
  google.type.Date dob = 2;
}

// CKYCDownloadRequest stores details required to make Download CKYC call to vendor
message CKYCDownloadRequestParams {
  repeated string ckyc_numbers = 1;

  // name entered by user while onboarding. this is used to match
  // details from download response to validate the user.
  api.typesv2.common.Name given_name = 2;

  // phone number entered by user while onboarding. this is used
  // to match details from download response to validate the user.
  api.typesv2.common.PhoneNumber phone_number = 3;

  // name fetched from PAN Validation API. this is used to match
  // details from download response to validate the user.
  api.typesv2.common.Name pan_name = 4;

  // CKYC Reference ID received in the search response
  // This is required to be passed while downloading CKYC data
  string ckyc_ref_id = 5;
}

// KYC Progress State Machine Values
enum KYCState {
  KYC_STATE_UNSPECIFIED = 0;

  // Request for CKYC initiated; next step is CKYC Search API
  CKYC_INIT = 1;

  // Results found in CKYC Search API; next step is CKYC Download API
  CKYC_SEARCH_FOUND = 2;

  // CKYC doesn't exist for the user; Terminal state
  CKYC_SEARCH_NOT_FOUND = 3;

  // CKYC Search upstream API error; retryable error
  // Previous state is always CKYC_SEARCH_ERROR
  CKYC_SEARCH_ERROR = 4;

  // Max Retries Limit Reached. Max retries can be found in config.
  CKYC_SEARCH_MAX_RETRIES = 5;

  // CKYC Search was found but validations for the record in response failed.
  // e.g: L/S/O type users.
  CKYC_SEARCH_VALIDATION_FAILED = 19;

  // Result found in CKYC Download API; CKYC Vendor Process is complete.
  CKYC_DOWNLOAD_FOUND = 6;

  // CKYC Download API error or invalid results; retryable error
  CKYC_DOWNLOAD_ERROR = 7;

  // Max Retries Limit Reached. Max retries can be found in config.
  // Previous state is always CKYC_DOWNLOAD_ERROR
  CKYC_DOWNLOAD_MAX_RETRIES = 8;

  // EKYC initiated by the client SDK.
  EKYC_INIT = 9;

  // EKYC failed on client SDK on biz logic. Not a technical failure.
  EKYC_FAILED = 10;

  // When EKYC is completed on client, it sends the user demographic data
  // which is used for customer creation and account opening with partner bank
  EKYC_DATA_RECEIVED = 11;

  // CKYC Download was Found but validation of details in CKYC response failed.
  // For e.g. Name match validation failed or phone number match failed.
  CKYC_DOWNLOAD_VALIDATION_FAILED = 13;

  // BKYC initiated by the client sdk
  BKYC_INIT = 14;

  // BKYC data uploaded but users havn't give consent to upgrade
  BKYC_IN_REVIEW = 15;

  // BKYC failed due to some validation issue
  BKYC_FAILED = 16;

  // BKYC data deleted
  BKYC_EXPIRED = 17;

  // BKYC validation successful
  BKYC_SUCCESS = 18;
}

// KYCAttemptFieldMask is used to mask columns to update in DB Update call
enum KYCAttemptFieldMask {
  KYC_ATTEMPT_NONE = 0;
  KYC_ATTEMPT_ID = 1;
  ACTOR_ID = 2;
  SOURCE = 3;
  KYC_TYPE = 4;
  KYC_STATUS = 5;
  LIVENESS_PARAMS = 6;
  FACE_MATCH_PARAMS = 7;
  REQUEST_PARAMS = 8;
  SCORES = 9;
  FAILURE_REASON = 10;
  BKYC_INFO = 11;
}

enum NameMatchCriteria {
  MATCH_CRITERIA_UNSPECIFIED = 0;
  FULL_NAME_MATCH = 1;
  FIRST_LAST_MATCH = 2;
  REVERSE_FIRST_LAST_MATCH = 3;
  SHORT_NAME_MATCH = 4;
}

// Type of KYC failure state. If a KYC Attempt is in failure state,
// it represents the cause of KYC Attempt being in failure state.
// Applicable for both CKYC & EKYC.
enum FailureType {
  REASON_UNSPECIFIED = 0;

  // Error due to technical reasons like API error, server down etc.
  TECHNICAL_ERROR = 1;

  // Name given by the user and got in the KYC data didn't match
  NAME_MISMATCH = 2;

  // CKYC has valid ID proofs required to open bank account
  PHONE_NUMBER_MISMATCH = 3;

  // CKYC record doesn't have valid ID proofs required to open bank account.
  // Special case of INVALID_ID_PROOF failure type. When CKYC has
  // PAN and masked Aadhar as ID Proofs, the user's KYC has to be
  // treated as Full KYC, but for other cases, it has to be treated as Min KYC
  INVALID_ID_PROOF_BUT_FULL_KYC = 8;

  // CKYC record has PAN & Aadhar and other documents as well
  // But we are giving priority to PAN & Aadhar to open full account
  // It is different from INVALID_ID_PROOF_BUT_FULL_KYC in terms that we are choosing it rather than enforcing it
  PAN_UID_PRIORITISED = 19;

  // CKYC record doesn't have valid ID proofs required to open bank account.
  // All invalid ID proof scenarios other than specified in INVALID_ID_PROOF_BUT_FULL_KYC.
  INVALID_ID_PROOF = 4;

  // No photo present in kyc record
  INVALID_PHOTO = 5;

  // KYC Identifier - CKYC number for CKYC & UID Reference Key for EKYC
  INVALID_UNIQUE_ID = 6;

  // EKYC record received from the client is validated with the vendor
  // It matches name and DOB with the actual EKYC record at vendor's end
  VENDOR_VALIDATION_FAILED = 9;

  // Date of birth given by the user and got in the KYC data didn't match
  DOB_MISMATCH = 10;

  // Empty address in KYC data
  EMPTY_ADDRESS = 11;

  // Not used anymore
  // Since EKYC is done at the clients end, it sends a flag
  // that identifies whether the EKYC was a success or failure.
  // The value of this flag depends on the EKYC response from the vendor.
  FAILURE_FLAG = 7 [deprecated = true];

  //CKYC number starts with L which means we cannot onboard the user as per bank instructions
  CKYC_L_FLAG = 12;
  //CKYC number starts with S which means we cannot onboard the user as per bank instructions
  CKYC_S_FLAG = 13;
  //CKYC number starts with O which means we cannot onboard the user as per bank instructions
  CKYC_O_FLAG = 14;

  // Empty state in KYC data
  EMPTY_STATE = 15;

  // Age is less than 18 as per KYC dob
  MINOR_AGE = 16;

  // gender and honorific doesn't match for the user
  GENDER_HONORIFIC_MISMATCH = 17;

  // Invalid state in KYC data
  INVALID_STATE = 18;

  // received status other then Y from client
  RECEIVED_UNSUCCESSFUL_STATUS_FROM_CLIENT = 20;

  // kyc agent could not verify the documents
  AGENT_VALIDATION_FAILED = 21;

  // either ePAN data is not present or Pan was not captured and sent.
  PAN_DATA_MISSING = 22;

  // Mobile number linked to aadhaar is not same as user's number
  AADHAAR_MOBILE_MISMATCH = 23;

  // Failure from NameDobValidation VG API
  // NameDobValidation returns failure in case of a mismatch of name and dob of EKYC record with the values at CBS
  NAME_DOB_VALIDATION_FAILURE = 24;

  // The age is lower than the age threshold set based on business decision.
  MINIMUM_AGE_VALIDATION_FAILURE = 25;

  // This validation checks if the PAN and Aadhaar are linked by comparing the Aadhaar last 4 digits hash from
  // PAN Aadhaar Validation API with the hash stored in the KYC attempt data
  PAN_AADHAAR_VALIDATION_FAILURE = 26;
}

// Scores stores scores by matching various parameters
// for all records. While doing CKYC, there could be
// multiple KYC records that could be returned to us.
// We evaluate scores for all and select the best fit.
message Scores {
  repeated ScoresPerRecord scores_per_record = 1;

  // Record identifier of the selected KYC record
  // Applicable for CKYC.
  string selected_record_identifier = 2;
}

enum NameMatchDecision {
  NAMEMATCH_UNSPECIFIED = 0;
  NAMEMATCH_PASS = 1;
  NAMEMATCH_FAIL = 2;
}

message ScoresPerRecord {
  // KYC Identifier. CKYC No. in case of CKYC and
  // UID in case of EKYC.
  string record_identifier = 1;

  // Inhouse DS name match API decision & score
  NameMatchDecision inhouse_name_match_decision = 2;
  float inhouse_name_match_score = 3;

  // Old(Already being used) name match API decision & score
  NameMatchDecision old_name_match_decision = 4;
  float old_name_match_score = 5;

  string name1 = 6;
  string name2 = 7;
}

// NameMatchResult stores the name match scores.
// It's evaluated after comparing two names.
message NameMatchResult {
  repeated NameMatchScore scores = 1;
  float WeightedSumScore = 2;
}

// NameMatchScore stores the name match score for a given criteria.
// It's evaluated after comparing two names.
message NameMatchScore {
  NameMatchCriteria Criteria = 1;
  int32 Weight = 2;
  float Score = 3;
}

// FailurePoint represents a single reason for KYC attempt failure
message Failure {
  FailureType type = 1;

  // Additional details on the failure that can help in explaining the failure type.
  // The details vary per failure type, context specific. It's upto the
  // developer to use this field to their advantage.
  string description = 2;
}

// FailureReason stores the list of failure points on why a KYC
// attempt went into a failure state.
message FailureReason {
  // List of failures that led to the failure.
  // Why repeated? For CKYC, we can have different
  // failures for different CKYC records.
  repeated Failure failures = 1;
}
