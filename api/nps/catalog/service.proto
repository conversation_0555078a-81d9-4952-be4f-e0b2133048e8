syntax = "proto3";

package nps.catalog;

import "api/rpc/status.proto";
import "api/typesv2/date.proto";
import "api/typesv2/money.proto";
import "api/typesv2/common/date.proto";
import "api/nps/nps_schemes.proto";
import "api/typesv2/common/boolean.proto";


option go_package = "github.com/epifi/gamma/api/nps/catalog";

service NpsCatalog{
  // StoreNpsNavData stores the daily NAV data for all NPS schemes
  // This RPC is meant to be called by a daily cron job
  rpc StoreNpsNavData(StoreNpsNavDataRequest) returns (StoreNpsNavDataResponse);
  // FetchNPSNavChange fetches NAV change for given scheme between dates
  rpc FetchNPSNavChange(FetchNPSNavChangeRequest) returns (FetchNPSNavChangeResponse);
  // FetchNPSSchemeInfo fetches scheme and PFM details for given scheme IDs
  rpc FetchNPSSchemeInfo(FetchNPSSchemeInfoRequest) returns (FetchNPSSchemeInfoResponse);
  // IsTradingDay checks if the given date is a trading day
  rpc IsTradingDay(IsTradingDayRequest) returns (IsTradingDayResponse);
}

// IsTradingDayRequest represents a request to check if a date is a trading day
message IsTradingDayRequest {
  api.typesv2.Date date = 1;
}

// IsTradingDayResponse represents the response for checking if a date is a trading day
message IsTradingDayResponse {
  rpc.Status status = 1;
  api.typesv2.common.BooleanEnum is_trading_day = 2;
}

message StoreNpsNavDataRequest {
  // Date for which the nav data should be stored
  api.typesv2.Date date = 1;
}

message StoreNpsNavDataResponse {
  rpc.Status status = 1;
  int32 stored_nav_count = 2;
}

message FetchNPSNavChangeRequest {
  string scheme_id = 1 ;
  api.typesv2.common.Date from_date = 2;
  api.typesv2.common.Date to_date = 3;
}

message FetchNPSNavChangeResponse {
  rpc.Status status = 1;
  api.typesv2.Money from_date_nav = 2;
  api.typesv2.Money to_date_nav = 3;
  api.typesv2.Money nav_change = 4;
}

message FetchNPSSchemeInfoRequest {
  repeated string scheme_ids = 1 ;
}

message FetchNPSSchemeInfoResponse {
  rpc.Status status = 1;
  repeated NpsScheme nps_schemes = 2;
}
