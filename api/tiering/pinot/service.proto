syntax = "proto3";

package tiering.pinot;

import "api/rpc/status.proto";
import "api/typesv2/date.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/tiering/pinot";
option java_package = "com.github.epifi.gamma.api.tiering.pinot";

service EODBalance {
  // GetAverageEODBalanceInDateRange returns average EOD savings account balance for given date
  // If only X days data is available in last 30 days date range then returns average for X days(X<30)
  // Provided date filters will be inclusive [a,b]
  // If from_timestamp and to_timestamp is same then returns the count for 1 day, if available
  // mandatory fields: actorId, date range timestamps(from_timestamp and to_timestamp)
  rpc GetAverageEODBalanceInDateRange (GetAverageEODBalanceInDateRangeRequest) returns (GetAverageEODBalanceInDateRangeResponse);

  // GetAverageEODBalanceInDateRangeDayWise returns EOD savings account balance for given dates
  // Max 30 days time period is allowed
  // Provided date filters will be inclusive [a,b]
  // If from_timestamp and to_timestamp is same then returns the count for 1 day, if available
  // mandatory fields: actorId, date range timestamps(from_timestamp and to_timestamp)
  rpc GetAverageEODBalanceInDateRangeDayWise (GetAverageEODBalanceInDateRangeDayWiseRequest) returns (GetAverageEODBalanceInDateRangeDayWiseResponse);
}

message GetAverageEODBalanceInDateRangeRequest {
  // mandatory actor_id identifier
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // From timestamp of the date range
  // Provided timestamp will be rounded down to start of the day
  google.protobuf.Timestamp from_timestamp = 2 [(validate.rules).timestamp.required = true];
  // To timestamp of the date range
  // Provided timestamp will be rounded down to end of the day
  google.protobuf.Timestamp to_timestamp = 3 [(validate.rules).timestamp.required = true];
  // Optional timezone for interpreting the timestamps (e.g., "Asia/Kolkata", "UTC")
  // If not provided, timestamps will be interpreted in UTC
  string timezone = 4;
}

message GetAverageEODBalanceInDateRangeResponse {
  rpc.Status status = 1;
  // Average balance
  double avg_balance = 2;
  // If request number of days data is not available it returns the number of days that the data is returned for
  int64 num_of_days = 3;
}

message GetAverageEODBalanceInDateRangeDayWiseRequest {
  // mandatory actor_id identifier
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // From timestamp of the date range
  // Provided timestamp will be rounded down to start of the day
  google.protobuf.Timestamp from_timestamp = 2 [(validate.rules).timestamp.required = true];
  // To timestamp of the date range
  // Provided timestamp will be rounded down to end of the day
  google.protobuf.Timestamp to_timestamp = 3 [(validate.rules).timestamp.required = true];
  // Optional timezone for interpreting the timestamps (e.g., "Asia/Kolkata", "UTC")
  // If not provided, timestamps will be interpreted in UTC
  string timezone = 4;
}

message GetAverageEODBalanceInDateRangeDayWiseResponse {
  rpc.Status status = 1;
  repeated EODBalanceByDate day_wise_eod_balance = 2;

  message EODBalanceByDate {
    // EOD balance
    double balance = 1;
    api.typesv2.Date date = 2;
  }

}
