syntax = "proto3";

package tiering;

import "api/cx/sherlock_banners/collector.proto";
import "api/dynamic_elements/dynamic_elements.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/status.proto";
import "google/type/date.proto";
import "api/tiering/enums/enums.proto";
import "api/tiering/external/external.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/tiering";
option java_package = "com.github.epifi.gamma.api.tiering";

// Service to segregate users to different tiers based on some qualifying criteria
// design doc: https://docs.google.com/document/d/1VJnWhIgobYoqhnyh2PuorTTK7bZQI0gV_nIdkoCOzrc/edit#
service Tiering {
  // Get tier of a user or before a particular time
  // In case tiering is disabled for a user the rpc returns status code as DISABLED
  rpc GetTierAtTime (GetTierAtTimeRequest) returns (GetTierAtTimeResponse) {};
  // RPC to upgrade manually to a tier in case user was eligible
  rpc Upgrade (UpgradeRequest) returns (UpgradeResponse) {};
  // RPC to determine whether a component should be shown for a user or not
  // If required to show the component, it is recorded first that the component is being shown to them
  rpc ShowComponentToActor (ShowComponentToActorRequest) returns (ShowComponentToActorResponse);
  // RPC to record component shown for a user
  // INTERNAL in case of any server error
  rpc RecordComponentShownToActor (RecordComponentShownToActorRequest) returns (RecordComponentShownToActorResponse);
  // RPC to get all probable movements and their details from the actors current tier, to higher tiers
  // If actor is cooloff, it sends details required for retaining the actor in that tier
  // Each movement detail consists of a set of options and satisfying one of them will lead to movement to that particular tier
  // Each option consists of a set of actions and satisfying all of them will lead to an option being met
  rpc GetTieringPitchV2 (GetTieringPitchV2Request) returns (GetTieringPitchV2Response);
  // GetDetailsForCx fetches details required for Cx service to power sherlock agent view
  // INTERNAL in case of any server error
  rpc GetDetailsForCx (GetDetailsForCxRequest) returns (GetDetailsForCxResponse);
  // OverrideGracePeriod overrides users grace period with the given timestamp
  // INTERNAL in case of any server error
  rpc OverrideGracePeriod (OverrideGracePeriodRequest) returns (OverrideGracePeriodResponse);
  // OverrideCoolOffPeriod overrides users cool off period with the given timestamp
  // INTERNAL in case of any server error
  rpc OverrideCoolOffPeriod (OverrideCoolOffPeriodRequest) returns (OverrideCoolOffPeriodResponse);
  // Returns whether tiering is enabled for an actor or not
  // INTERNAL in case of any server error
  rpc IsTieringEnabledForActor (IsTieringEnabledForActorRequest) returns (IsTieringEnabledForActorResponse) {
    option deprecated = true;
  };
  // Return whether tiering should be pitched to the actor or not and all the associated details with regards to it
  // Pitch is with respect to a particular flow
  // INTERNAL in case of any server error
  // Deprecated in favour of GetTieringPitchV2
  rpc GetTieringPitch (GetTieringPitchRequest) returns (GetTieringPitchResponse) {
    option deprecated = true;
  };
  // Get criteria for actor that got referenced in eligible tier movement
  // If no eligible tier movement for actor is present then it will return current active criteria
  // INTERNAL in case of any server error
  // Deprecated in favour of GetTieringPitchV2
  rpc GetCriteriaForActor (GetCriteriaForActorRequest) returns (GetCriteriaForActorResponse) {
    option deprecated = true;
  };
  // IsActorEligibleForMovement checks whether for an actor if given movement type is eligible or not
  // INVALID_ARGUMENT if movement type is not handled
  // INTERNAL in case of any server error
  // Deprecated in favour of GetTieringPitchV2
  rpc IsActorEligibleForMovement (IsActorEligibleForMovementRequest) returns (IsActorEligibleForMovementResponse) {
    option deprecated = true;
  };
  // IsUserInGracePeriod checks whether user in grace period or not
  // INTERNAL in case of any server error
  // Deprecated in favour of GetTieringPitchV2
  rpc IsUserInGracePeriod (IsUserInGracePeriodRequest) returns (IsUserInGracePeriodResponse) {
    option deprecated = true;
  };

  // RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  // ActorId is a mandatory parameter in the Request
  // Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no elements found for the given user on this screen
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the list of targeted comms elements is fetched successfully
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse) {}

  // DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse);

  // RPC to fetch tiering backend config
  rpc GetConfigParams (GetConfigParamsRequest) returns (GetConfigParamsResponse);

  // RPC to evaluate tier with the current active criteria for actor
  // INTERNAL in case of any server error
  rpc EvaluateTierForActor (EvaluateTierForActorRequest) returns (EvaluateTierForActorResponse);

  // RPC to check if the actor is eligible for X(2%) cashback reward
  // checks are on various parameters like for example ABWII(Average balance while in infinite) is above certain threshold etc
  // Some internal details:
  //  If tier itself is not eligible for cashback reward this RPC will return false
  //  If any tier is always eligible for cashback reward(eg: SALARY) this RPC will return true without any other checks
  // INTERNAL in case of any server error
  rpc CheckIfActorIsEligibleForCashbackReward (CheckIfActorIsEligibleForCashbackRewardRequest) returns (CheckIfActorIsEligibleForCashbackRewardResponse);

  // GetTierTimeRangesForActor returns the list of Time Ranges when the user was in a particular tier
  rpc GetTierTimeRangesForActor (GetTierTimeRangesForActorRequest) returns (GetTierTimeRangesForActorResponse);

  // GetActorScreenInteractionDetails return the details of the actor's interaction with the screen
  rpc GetActorScreenInteractionDetails (GetActorScreenInteractionDetailsRequest) returns (GetActorScreenInteractionDetailsResponse);

  // GetActorDistintTiers returns the list of tiers user has been in
  rpc GetActorDistinctTiers (GetActorDistinctTiersRequest) returns (GetActorDistinctTiersResponse);

  // IsUserIneligibleForRewards checks if a user is ineligible for rewards
  // based on abuser segments on a given date
  // To be used only for CX agent purposes
  rpc IsUserEligibleForRewards (IsUserEligibleForRewardsRequest) returns (IsUserEligibleForRewardsResponse);

  // GetAMBInfo returns the current (projected) AMB and target AMB for a user
  // INTERNAL in case of any server error
  rpc GetAMBInfo (GetAMBInfoRequest) returns (GetAMBInfoResponse);

  // FetchSherlockBanners returns banners to show for AMB charges communication
  // INTERNAL in case of any server error
  rpc FetchSherlockBanners(cx.sherlock_banners.FetchSherlockBannersRequest) 
      returns (cx.sherlock_banners.FetchSherlockBannersResponse);
}

message IsUserEligibleForRewardsRequest {
  string actor_id = 1;
  google.type.Date date = 2;
}

message IsUserEligibleForRewardsResponse {
  rpc.Status status = 1;
  // List of tier to eligibility status mapping
  repeated TierToEligibilityStatus tier_to_eligibility_status = 2;
  message TierToEligibilityStatus {
    // Display string for tier plan
    tiering.external.Tier tier_name = 1;
    // Boolean to represent if a user is eligible for rewards.
    bool is_eligible = 2;
  }
}

message GetActorDistinctTiersRequest {
  // Actor for whom to get the distinct tiers
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Time range for getting the distinct tiers. The range will be specified time here till now.
  // MANDATORY field
  google.protobuf.Timestamp time_since = 2;

}

message GetActorDistinctTiersResponse {
  rpc.Status status = 1;
  // List of distinct tiers user has been in
  repeated tiering.external.Tier distinct_tiers = 2;
}

message GetTieringPitchV2Response {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    DISABLED = 101;
  }
  // Status of RPC response
  rpc.Status status = 1;
  // current tier of the actor
  tiering.external.Tier current_tier = 2;
  // details associated to move from current tier to the next tier
  // list will always be sorted in increasing order of tiers
  repeated external.MovementExternalDetails movement_details_list = 3;
  // details of the latest upgrade that happened for the user
  // can be nil if no upgrades happened
  external.LatestMovementDetails last_upgrade_details = 4;
  // details of the latest downgrade that happened for the user
  // can be nil if no downgrades happened
  external.LatestMovementDetails last_downgrade_details = 5;
  // base tier for the actor - will be either Standard or Regular
  tiering.external.Tier actor_base_tier = 6;
  // The criteria option type used when the user initially qualified for their current tier.
  tiering.enums.CriteriaOptionType entry_criteria_option_type = 7;
  // The criteria option type currently being used to evaluate the user's tier eligibility.
  tiering.enums.CriteriaOptionType current_criteria_option_type = 8;
  repeated tiering.enums.CriteriaOptionType current_criteria_option_types = 9;
}

message GetTieringPitchV2Request {
  // Actor Id (mandatory)
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message RecordComponentShownToActorRequest {
  // Actor Id (mandatory)
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Display Component enum (mandatory)
  tiering.enums.DisplayComponent component = 2;
}

message RecordComponentShownToActorResponse {
  // Status of RPC response
  // Success : record request was successful
  // Internal : record request was unsuccessful
  rpc.Status status = 1;
}

message GetTieringPitchRequest {
  // Actor id for whom add funds details are required
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Flow name for which pitch details are being requested - ex Add funds flow
  tiering.enums.TieringPitchFlowFilter tiering_pitch_flow = 2;
}

message GetTieringPitchResponse {
  // Status of RPC response
  rpc.Status status = 1;
  // Whether the pitch is enabled for the actor or not
  bool is_pitch_enabled = 2;
  // Pitch Details
  oneof tiering_pitch_details {
    // Pitch details related to add funds flow
    TieringPitchAddFundsDetails add_funds_details = 3;
    // Pitch details related to profile header section flow
    TieringPitchProfileSectionDetails profile_section_details = 4;
  }
}

message TieringPitchAddFundsDetails {
  // Tier which the user is currently in
  tiering.external.Tier current_tier = 1;
  // Min amount required to be in the current tier
  google.type.Money current_tier_min_amount = 2;
  // Immediate next tier to which user can upgrade by adding funds
  tiering.external.Tier next_tier = 3;
  // Min amount required to be in the next higher tier
  google.type.Money next_tier_min_amount = 4;
  // Current balance of the user
  google.type.Money current_balance_amount = 5;
  // Min amount for add funds page below which secondary CTA is shown
  // Min amount can be less than suggested amount
  google.type.Money min_amount = 6;
  // Amount suggested in the add funds page
  google.type.Money suggested_amount = 7;
  // Flag to determine whether pitch is to retain the user in current tier
  bool is_retention_pitch = 8;
}

message TieringPitchProfileSectionDetails {
  // Tier which the user is currently in
  tiering.external.Tier current_tier = 1;
  // Min amount required to be in the current tier
  google.type.Money current_tier_min_amount = 2;
  // Immediate next tier to which user can upgrade by adding funds
  tiering.external.Tier next_tier = 3;
  // Min amount required to be in the next higher tier
  google.type.Money next_tier_min_amount = 4;
  // Current balance of the user
  google.type.Money current_balance_amount = 5;
  // Whether user in grace period or not
  bool is_user_in_grace_period = 6;
  // Min amount for add funds page below which secondary CTA is shown
  google.type.Money min_amount = 7;
  // Time at which grace period expires for the user if the user is in grace period
  google.protobuf.Timestamp grace_period_expiry_time = 8;
}

message GetTierAtTimeRequest {
  // Actor for whom tier is needed
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Timestamp at which tier is required. In case service requires latest tier it should pass current time
  // MANDATORY field
  google.protobuf.Timestamp tier_timestamp = 2 [(validate.rules).timestamp.required = true];
}

message GetTierAtTimeResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    DISABLED = 101;
  }
  rpc.Status status = 1;
  // Tier info for the actor such as tier enum, name, timestamp
  external.TierInfo tier_info = 2;
}

message IsTieringEnabledForActorRequest {
  // Actor for whom to check
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message IsTieringEnabledForActorResponse {
  rpc.Status status = 1;
  bool is_enabled = 2;
}

message UpgradeRequest {
  // Actor for whom to upgrade
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Provide provenance based on the origin of the request
  // eg: OPT_IN for add_funds flow
  // MANDATORY field
  enums.Provenance provenance = 2 [(validate.rules).enum = {not_in: [0]}];
}

message UpgradeResponse {
  enum Status {
    OK = 0;
    DOWNGRADED = 101;
  }
  rpc.Status status = 1;
  tiering.external.Tier from_tier = 2;
  tiering.external.Tier to_tier = 3;
}

message ShowComponentToActorRequest {
  // Actor Id (mandatory)
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Component name (deprecated)
  string component_name = 2 [deprecated = true];
  // Display Component enum (mandatory)
  tiering.enums.DisplayComponent display_component = 3;
}

message ShowComponentToActorResponse {
  rpc.Status status = 1;
  // Flag to decide whether to show screen or not
  bool show_component = 2;
}

message GetCriteriaForActorRequest {
  // Actor for whom to fetch the criteria for
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message GetCriteriaForActorResponse {
  rpc.Status status = 1;
  // External tier representation
  // gives response as a list of tier and QC
  repeated external.TierExternalCriteria external_criteria = 2;
}

message IsActorEligibleForMovementRequest {
  // Actor for whom to check eligibility for
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // movement type to check eligibility for
  enums.TierMovementType movement_type = 2;
  // from_tier and to_tier to define the movement direction
  external.Tier from_tier = 3;
  external.Tier to_tier = 4;
}

message IsActorEligibleForMovementResponse {
  rpc.Status status = 1;
  bool is_eligible = 2;
}

message IsUserInGracePeriodRequest {
  // Actor for whom to check for
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message IsUserInGracePeriodResponse {
  rpc.Status status = 1;
  bool is_in_grace_period = 2;
}

message GetDetailsForCxRequest {
  // Actor for whom to fetch details for
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message GetDetailsForCxResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    DISABLED = 101;
  }
  rpc.Status status = 1;
  // Current tier of the user
  external.Tier current_tier = 2;
  // Flag to represent if the user is in grace
  bool is_user_in_grace = 3;
  // Grace timestamp if the user is in grace
  google.protobuf.Timestamp grace_period_till = 4;
  // Flag to represent if the user is in cool off
  bool is_user_in_cool_off = 5;
  // Cool off timestamp if the user is in grace
  google.protobuf.Timestamp cool_off_period_till = 6;
  // Last N movement histories of the user
  repeated external.TierMovementHistory movement_histories = 7;
  // Flag to represent if a user is a Rewards Abuser.
  bool is_a_rewards_abuser_user = 8;
  // latest criterias satisfied by user to be on current tier
  external.TierMovementCriterias tier_movement_criterias = 9;

  // Display string for current AMB
  string current_amb = 10;

  // Display string for required AMB
  string required_amb = 11;

  // Display string for shortfall
  string shortfall = 12;

  // AMB history details
  repeated AmbDetails amb_history = 13;
}

message AmbDetails {
  string dates = 1;
  string plan = 2;
  string amb_maintained = 3;
}

message OverrideGracePeriodRequest {
  // Actor for whom to override grace period for
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Timestamp to override grace period to
  google.protobuf.Timestamp override_timestamp = 2;
}

message OverrideGracePeriodResponse {
  rpc.Status status = 1;
}

message OverrideCoolOffPeriodRequest {
  // Actor for whom to override cool off for
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Timestamp to override cool off period to
  google.protobuf.Timestamp override_timestamp = 2;
}

message OverrideCoolOffPeriodResponse {
  rpc.Status status = 1;
}

message GetConfigParamsRequest {
  string actor_id = 1;
}

message GetConfigParamsResponse {
  rpc.Status status = 1;
  // tiering backend config values for DowngradeWindowDuration
  google.protobuf.Duration downgrade_window_duration = 2;
  // tiering backend config values for GraceWindowDuration
  google.protobuf.Duration grace_window_duration = 3;
  // tiering backend config values for GraceInitialWindowDuration
  google.protobuf.Duration grace_initial_window_duration = 4;
  // tiering backend config values for IsRegularTierEnabledForActor
  // this is evaluated only if actor id is provided in the request
  bool is_regular_tier_enabled_for_actor = 5;
  // config params for Regular Tier
  RegularTierConfigParams regular_tier_config_params = 6;
  // feature flag value for actor to enable multiple ways to enter tiering
  bool is_multiple_ways_to_enter_tiering_enabled_for_actor = 7;
  // segment of users for whom certain criteria based evaluations are excluded
  CriteriaSegmentExclusionConfigs criteria_segment_exclusion_configs = 8;
}

message CriteriaSegmentExclusionConfigs {
  repeated string salary_b2c_excluded_segments = 1;
  repeated string aa_salary_excluded_segments = 2;
}

message RegularTierConfigParams {
  // Min Avg Monthly balance required for Regular Tier
  google.type.Money min_balance_for_regular_tier = 1;
  // Monthly Charges for regular tier
  google.type.Money min_balance_penalty_for_regular_tier = 2;
}

message EvaluateTierForActorRequest {
  // Actor for whom to override cool off for
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  Options options = 2;

  message Options {
    bool to_eval_on_real_time_data = 1;
    bool to_eval_for_multiple_ways = 2;
    bool to_skip_app_access_check = 3;
  }
}

message EvaluateTierForActorResponse {
  rpc.Status status = 1;
  // Tier evaluated basis on current active criteria for actor
  tiering.external.Tier evaluated_tier = 2;
  // Criteria reference id that the tier evaluated against
  string criteria_reference_id = 3;
  // list of all criteria satisfied by the actor
  repeated enums.CriteriaOptionType EvaluatedTierSatisfiedCriteriaOptionTypes = 4;
}

message CheckIfActorIsEligibleForCashbackRewardRequest {
  // Actor for whom to check eligibility for cashback reward
  // MANDATORY field
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Tier to be evaluated for cashback reward eligibility
  tiering.external.Tier tier = 2;
}

message CheckIfActorIsEligibleForCashbackRewardResponse {
  rpc.Status status = 1;
  bool is_eligible = 2;
}

message GetTierTimeRangesForActorRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // list of tiers for which timeline is requested
  repeated tiering.external.Tier tiers = 2 [(validate.rules).repeated.min_items = 1];
  // only time_ranges that have to_time greater than filter_from are fetched and returned
  // this is to do a controlled fetch of the tier movement history (TMH) as TMH for an actor grows to a big value over time
  google.protobuf.Timestamp filter_from = 3 [(validate.rules).timestamp.required = true];
}

message GetTierTimeRangesForActorResponse {
  rpc.Status status = 1;
  // map of tiering.external.Tier.String() to TimeRange list
  // TimeRanges is the list of time ranges when user was in the tier
  map<string, TimeRanges> tier_time_ranges_map = 2;
}

message TimeRanges {
  repeated TimeRange time_ranges = 1;
}

message TimeRange {
  google.protobuf.Timestamp from_time = 1;
  google.protobuf.Timestamp to_time = 2;
}

message GetActorScreenInteractionDetailsRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  tiering.enums.TieringScreen screen = 2 [(validate.rules).enum = {not_in: [0]}];
  enum RequestType {
    REQUEST_TYPE_UNSPECIFIED = 0;
    REQUEST_TYPE_ADD = 1;
    REQUEST_TYPE_GET = 2;
  }
  RequestType request_type = 3 [(validate.rules).enum = {not_in: [0]}];
}

message GetActorScreenInteractionDetailsResponse {
  rpc.Status status = 1;
  tiering.enums.ActorScreenInteractionStatus response = 2;
}

message GetDropOffBottomSheetRequest {
  string actor_id = 1;
  enum TieringPitchMethod {
    TIERING_PITCH_METHOD_UNSPECIFIED = 0;
    // Through Add Funds
    ADD_FUNDS = 1;
    // Through Fixed Deposit
    FIXED_DEPOSIT = 2;
    // Through US Stocks
    US_STOCKS = 3;
  }
  TieringPitchMethod tiering_pitch_method = 2;
  tiering.external.Tier tier_to_pitch = 3;
}

message GetDropOffBottomSheetResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink deeplink = 2;
}

message GetAMBInfoRequest {
  // Actor ID (mandatory)
  string actor_id = 1 [(validate.rules).string.min_len = 1];
}

message GetAMBInfoResponse {
  // Status of RPC response
  rpc.Status status = 1;
  // Current (projected) AMB for the user
  google.type.Money current_amb = 2;
  // Target AMB required for the current tier
  google.type.Money target_amb = 3;
  // Current tier of the user
  tiering.external.Tier current_tier = 4;
  // Amount to be added to reach target AMB
  google.type.Money shortfall_amount = 5;
}
