// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=NotificationType,TransactionState,NotificationSource

syntax = "proto3";
package card.enums;

option go_package = "github.com/epifi/gamma/api/card/enums";
option java_package = "com.github.epifi.gamma.api.card.enums";

// Enum denoting the type of message received for card switch notifications
enum MessageType {
  MESSAGE_TYPE_UNSPECIFIED = 0;

  MESSAGE_TYPE_REQUEST = 1;

  MESSAGE_TYPE_RESPONSE = 2;

  MESSAGE_TYPE_ADVICE = 3;

  MESSAGE_TYPE_REVERSAL_REQUEST = 4;

  MESSAGE_TYPE_REVERSAL_RESPONSE = 5;

  MESSAGE_TYPE_REVERSAL = 6;

  MESSAGE_TYPE_LOG_ONLY = 7;
}

// Enum denoting the card transaction category for different card transactions like ECOMM, ATM, POS and NFC(contactless)
enum CardTransactionCategory {
  CARD_TRANSACTION_CATEGORY_UNSPECIFIED = 0;

  CARD_TRANSACTION_CATEGORY_ECOMM = 1;

  CARD_TRANSACTION_CATEGORY_ATM_WITHDRAWAL = 2;

  CARD_TRANSACTION_CATEGORY_NFC = 3;

  CARD_TRANSACTION_CATEGORY_POS = 4;
}

// Type of notification event received from vendor
enum NotificationType {
  NOTIFICATION_TYPE_UNSPECIFIED = 0;

  NOTIFICATION_TYPE_MOBILE_BANKING_REGISTRATION = 1;

  NOTIFICATION_TYPE_PIN_CHANGE = 2;

  NOTIFICATION_TYPE_PIN_SET = 3;

  NOTIFICATION_TYPE_LIMIT_UPDATE = 4;

  NOTIFICATION_TYPE_CARD_STATE_UPDATE = 5;

  NOTIFICATION_TYPE_TOKEN_COMPLETION = 6;

  NOTIFICATION_TYPE_TOKEN_EVENT = 7;

  NOTIFICATION_TYPE_TOKEN_AUTHORIZATION_TRANSACTION = 8;

  NOTIFICATION_TYPE_WITHDRAWAL = 9;

  NOTIFICATION_TYPE_VISA_MONEY_TRANSFER = 10;

  NOTIFICATION_TYPE_REFUND_TRANSACTION = 11;

  NOTIFICATION_TYPE_POS_ECOMM_PURCHASE = 12;

  NOTIFICATION_TYPE_RECURRING_PAYMENT_TRANSACTION = 13;

  NOTIFICATION_TYPE_RECURRING_REGISTRATION_TRANSACTION = 14;

  NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL = 15;

  NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_DEBIT = 16;

  NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CUSTOMER_CREDIT = 17;

  NOTIFICATION_TYPE_CARD_TO_CARD_FEDERAL_CARD_INITIATED = 18;

  NOTIFICATION_TYPE_CARD_TO_CARD_OTHER_BANK_CARD_FEDERAL_BANK_ATM = 19;

  NOTIFICATION_TYPE_MINI_STATEMENT = 20;

  NOTIFICATION_TYPE_BALANCE_ENQUIRY = 21;

  NOTIFICATION_TYPE_PIN_VALIDATION = 22;

  NOTIFICATION_TYPE_CARD_VALIDATION = 23;

  NOTIFICATION_TYPE_ACCOUNT_STATUS_ENQUIRY = 24;
}

// Indicates the manner in which transactions have entered the authorization switch
enum AuthorizationSwitch {
  AUTHORIZATION_SWITCH_UNSPECIFIED = 0;

  AUTHORIZATION_SWITCH_TELEPHONIC_DEVICE_REQUEST = 1;

  AUTHORIZATION_SWITCH_MAIL_OR_TELEPHONE_ORDER = 2;

  AUTHORIZATION_SWITCH_SECURITY_ALERT = 3;

  AUTHORIZATION_SWITCH_CUSTOMER_IDENTITY_VERIFIED = 4;

  AUTHORIZATION_SWITCH_SUSPECTED_FRAUD = 5;

  AUTHORIZATION_SWITCH_SECURITY_REASONS = 6;

  AUTHORIZATION_SWITCH_REPRESENTMENT_OF_ITEM = 7;

  AUTHORIZATION_SWITCH_PUBLIC_UTILITY_TERMINAL = 8;

  AUTHORIZATION_SWITCH_CUSTOMER_TERMINAL = 9;

  AUTHORIZATION_SWITCH_ADMINISTRATION_TERMINAL = 10;

  AUTHORIZATION_SWITCH_RETURNED_ITEM = 11;

  AUTHORIZATION_SWITCH_MANUAL_REVERSAL = 12;

  AUTHORIZATION_SWITCH_TERMINAL_ERROR_OR_COUNTED = 13;

  AUTHORIZATION_SWITCH_TERMINAL_ERROR_OR_NOT_COUNTED = 14;

  AUTHORIZATION_SWITCH_DEPOSIT_OUT_OF_BALANCE_OR_APPLIED_CONTENTS = 15;

  AUTHORIZATION_SWITCH_PAYMENT_OUT_OF_BALANCE_OR_APPLIED_CONTENTS = 16;

  AUTHORIZATION_SWITCH_WITHDRAWAL_HAD_ERROR_REVERSED = 17;

  AUTHORIZATION_SWITCH_UNATTENDED_TERMINAL_UNABLE_TO_RETAIN_CARD = 18;

  AUTHORIZATION_SWITCH_RESERVED_FOR_ISO_USE = 19;

  AUTHORIZATION_SWITCH_RESERVED_FOR_NATIONAL_USE = 20;

  AUTHORIZATION_SWITCH_ADDRESS_VERIFICATION = 21;

  AUTHORIZATION_SWITCH_RESERVED_FOR_PRIVATE_USE = 22;
}

// Entry mode of transaction in the switch
enum TransactionEntryMode {
  TRANSACTION_ENTRY_MODE_UNSPECIFIED = 0;

  TRANSACTION_ENTRY_MODE_MANUAL = 1;

  TRANSACTION_ENTRY_MODE_MAGNETIC_STRIPE = 2;

  TRANSACTION_ENTRY_MODE_BAR_CODE = 3;

  TRANSACTION_ENTRY_MODE_OCR = 4;

  TRANSACTION_ENTRY_MODE_INTEGRATED_CIRCUIT_CARD = 5;

  TRANSACTION_ENTRY_MODE_ISO_USE = 6;

  TRANSACTION_ENTRY_MODE_INTEGRATED_CIRCUIT_CARD_CONTACTLESS = 7;

  TRANSACTION_ENTRY_MODE_NATIONAL_USE = 8;

  TRANSACTION_ENTRY_MODE_PRIVATE_USE = 9;

  TRANSACTION_ENTRY_MODE_MAGNETIC_STRIPE_CONTACTLESS = 10;
}

// Transaction status, we will only get terminal transaction status in the notifications
enum TransactionState {
  TRANSACTION_STATE_UNSPECIFIED = 0;

  TRANSACTION_STATE_SUCCESS = 1;

  TRANSACTION_STATE_FAILURE = 2;
}

enum SwitchNotificationResponse {
  SWITCH_NOTIFICATION_RESPONSE_UNSPECIFIED = 0;

  SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_PROCESS_TRANSACTION = 1;

  SWITCH_NOTIFICATION_RESPONSE_INCORRECT_PIN = 2;

  SWITCH_NOTIFICATION_RESPONSE_UNABLE_TO_AUTHORIZE_TRANSACTION = 3;

  SWITCH_NOTIFICATION_RESPONSE_CARD_EXPIRED = 4;

  SWITCH_NOTIFICATION_RESPONSE_ECOM_TRANSACTIONS_NOT_ENABLED = 5;

  // Limit set by user in fi app
  SWITCH_NOTIFICATION_RESPONSE_DAILY_TRANSACTIONS_AMT_LIMIT_REACHED = 6;

  SWITCH_NOTIFICATION_RESPONSE_POS_NOT_SUPPORTED = 7;

  SWITCH_NOTIFICATION_RESPONSE_PIN_TRIES_EXCEEDED_FOR_TRANSACTIONS = 8;

  SWITCH_NOTIFICATION_RESPONSE_DUPLICATE_TRANSACTION = 9;

  SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_DECLINED = 10;

  SWITCH_NOTIFICATION_RESPONSE_TRANSACTION_TYPE_NOT_SUPPORTED = 11;

  SWITCH_NOTIFICATION_RESPONSE_INVALID_TRANSACTION = 12;

  SWITCH_NOTIFICATION_RESPONSE_INTERNATIONAL_TRANSACTIONS_NOT_ENABLED = 13;

  SWITCH_NOTIFICATION_RESPONSE_CONTACTLESS_CARD_USAGE_NOT_ENABLED = 14;

  SWITCH_NOTIFICATION_RESPONSE_INSUFFICIENT_FUNDS_FOR_TRANSACTION = 15;

  SWITCH_NOTIFICATION_RESPONSE_DAILY_WITHDRAWAL_LIMIT_REACHED = 16;

  SWITCH_NOTIFICATION_RESPONSE_LOW_FUNDS_FOR_TRANSACTION = 17;

  SWITCH_NOTIFICATION_RESPONSE_INVALID_EXPIRY_DATE = 18;

  SWITCH_NOTIFICATION_RESPONSE_NFC_NOT_ENABLED = 19;

  SWITCH_NOTIFICATION_RESPONSE_PRM_DECLINED = 20;

  SWITCH_NOTIFICATION_RESPONSE_CVV_ERROR = 21;

  SWITCH_NOTIFICATION_RESPONSE_DAILY_CONTACTLESS_PAYMENTS_LIMIT_EXCEEDED = 22;

  SWITCH_NOTIFICATION_RESPONSE_CARD_OFF_FOR_TRANSACTIONS = 23;

  SWITCH_NOTIFICATION_RESPONSE_HOST_DOWN = 24;

  SWITCH_NOTIFICATION_RESPONSE_DOMESTIC_TRANSACTIONS_NOT_ENABLED = 25;

  SWITCH_NOTIFICATION_RESPONSE_ELA_FAILURE = 26;

  SWITCH_NOTIFICATION_RESPONSE_POS_USAGE_NOT_ENABLED = 27;

  SWITCH_NOTIFICATION_RESPONSE_ATM_USAGE_NOT_ENABLED = 28;

  SWITCH_NOTIFICATION_RESPONSE_CAF_NOT_FOUND = 29;

  // Deprecated: Use SWITCH_NOTIFICATION_RESPONSE_ATM_USAGE_NOT_ENABLED instead.
  SWITCH_NOTIFICATION_RESPONSE_ATM_FLAG_OFF = 30;

  SWITCH_NOTIFICATION_RESPONSE_LOST_OR_STOLEN_CARD = 31;

  SWITCH_NOTIFICATION_RESPONSE_INELIGIBLE_ACCOUNT = 32;

  // switch interface hub decline
  SWITCH_NOTIFICATION_RESPONSE_SI_HUB_DECLINE = 33;

  SWITCH_NOTIFICATION_RESPONSE_UNAUTHORIZED_USAGE = 34;

  SWITCH_NOTIFICATION_RESPONSE_INVALID_CARD = 35;

  // Max daily limit set by bank
  SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_DAILY_MAX = 36;

  SWITCH_NOTIFICATION_RESPONSE_MESSAGE_EDIT_ERROR = 37;

  // Exceeds withdrawal limit of card
  SWITCH_NOTIFICATION_RESPONSE_AMOUNT_OVER_WITHDRAWAL_LIMIT = 38;

  SWITCH_NOTIFICATION_RESPONSE_CAF_STATUS_DECLINE = 39;

  SWITCH_NOTIFICATION_RESPONSE_FALLBACK_DECLINE = 40;

  SWITCH_NOTIFICATION_RESPONSE_APPROVED_NO_BALANCES = 41;

  SWITCH_NOTIFICATION_RESPONSE_DEST_NOT_AVAILABLE = 42;

  SWITCH_NOTIFICATION_RESPONSE_ATC_CHECK_FAILURE = 43;

  SWITCH_NOTIFICATION_RESPONSE_TOKEN_IN_APP_FLAG_OFF = 44;

  SWITCH_NOTIFICATION_RESPONSE_SYSTEM_ERROR = 45;

  SWITCH_NOTIFICATION_RESPONSE_NO_IDF = 46;

  // Security check failure (cryptogram failure)
  SWITCH_NOTIFICATION_RESPONSE_ARQC_FAILURE = 47;

  SWITCH_NOTIFICATION_RESPONSE_TOKEN_COF_FLAG_OFF = 48;

  SWITCH_NOTIFICATION_RESPONSE_INVALID_TXN_DATE = 49;

  // CAF STATUS 3 (indicating that card to be captured in CAF page 7)
  SWITCH_NOTIFICATION_RESPONSE_CARD_TO_BE_CAPTURED_IN_CAF = 50;

  SWITCH_NOTIFICATION_RESPONSE_BAD_CARD_STATUS  = 51;

  SWITCH_NOTIFICATION_RESPONSE_RESERVED_B24_CODE = 52;

  SWITCH_NOTIFICATION_RESPONSE_HSM_PARAM_ERROR = 53;

  SWITCH_NOTIFICATION_RESPONSE_MAX_CREDIT_PER_REFUND = 54;

  // Allowed number of uses exceeded
  SWITCH_NOTIFICATION_RESPONSE_USAGE_LIMIT_EXCEEDED = 55;

  SWITCH_NOTIFICATION_RESPONSE_TOKEN_NFC_FLAG_OFF = 56;

  SWITCH_NOTIFICATION_RESPONSE_HOST_NOT_AVAILABLE = 57;
}

// source of the notification eg - switch, acs
enum NotificationSource {
  NOTIFICATION_SOURCE_UNSPECIFIED = 0;
  NOTIFICATION_SOURCE_SWITCH = 1;
}

enum CardNotificationFieldMask {
  CARD_NOTIFICATION_FIELD_MASK_UNSPECIFIED = 0;
  CARD_NOTIFICATION_FIELD_MASK_ID = 1;
  CARD_NOTIFICATION_FIELD_MASK_CARD_ID = 2;
  CARD_NOTIFICATION_FIELD_MASK_RETRIEVAL_REFERENCE_NUMBER = 3;
  CARD_NOTIFICATION_FIELD_MASK_REQUEST_ID = 4;
  CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_DETAILS = 5;
  CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_TYPE = 6;
  CARD_NOTIFICATION_FIELD_MASK_MERCHANT_DETAILS = 7;
  CARD_NOTIFICATION_FIELD_MASK_AUTH_SWITCH_DETAILS = 8;
  CARD_NOTIFICATION_FIELD_MASK_REMITTER_DETAILS = 9;
  CARD_NOTIFICATION_FIELD_MASK_STATUS = 10;
  CARD_NOTIFICATION_FIELD_MASK_DETAILED_STATUS= 11;
  CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_SOURCE = 12;
  CARD_NOTIFICATION_FIELD_MASK_DEDUPE_ID = 13;
  CARD_NOTIFICATION_FIELD_MASK_NOTIFICATION_EVENT_TIME = 14;
  CARD_NOTIFICATION_FIELD_MASK_CREATED_AT = 15;
  CARD_NOTIFICATION_FIELD_MASK_UPDATED_AT = 16;
  CARD_NOTIFICATION_FIELD_MASK_DELETED_AT = 17;
  CARD_NOTIFICATION_FIELD_MASK_EXTERNAL_REF_ID = 18;
}
