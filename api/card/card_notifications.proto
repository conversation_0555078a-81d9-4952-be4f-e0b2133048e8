// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=NotificationDetails,MerchantDetails,AuthSwitchDetails,RemitterDetails,DetailedStatus

syntax = "proto3";

package card;

import "api/card/enums/notification.proto";
import "api/typesv2/common/boolean.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/card";
option java_package = "com.github.epifi.gamma.api.card";

// CardNotification is generic proto model for debit card notifications,
// notifications can come from various sources eg - switch, acs etc., and
// can be for a financial and non-financial transaction
message CardNotification {
  // Primary identifier to a notification database model.
  string id = 1;
  // Card id for which notification was received and reference to cards table
  string card_id = 2;
  string retrieval_reference_number = 3;
  // unique reference id sent by vendor
  string request_id = 4;
  NotificationDetails notification_details = 5;
  card.enums.NotificationType notification_type = 6;
  // merchant involved in the notification
  MerchantDetails merchant_details = 7;
  AuthSwitchDetails auth_switch_details = 8;
  RemitterDetails remitter_details = 9;
  // High level status of the transaction(financial/non-financial) for which notification was received
  card.enums.TransactionState status = 10;
  DetailedStatus detailed_status = 11;
  card.enums.NotificationSource notification_source = 12;
  // unique transaction reference to uniquely identify a notification. It can be a combination of multiple parameters
  // received from a notification.
  // We will transform the DedupeId message to sha 256 hash after doing proto.Marshal and keep this into our db. It will
  // be considered as unique identifier for de-duping notifications.
  DedupeId dedupe_id = 13;
  // time at which notification was created at the end of vendor
  // definition of notification_event_time might differ based on the notification type,
  // for eg - financial switch notification, notification_event_time will be same as the time of the txn.
  google.protobuf.Timestamp notification_event_time = 14;
  google.protobuf.Timestamp created_at = 15;
  google.protobuf.Timestamp updated_at = 16;
  google.protobuf.Timestamp deleted_at = 17;
  // ExternalRefId can be used to link or map a cardNotification entry with an external entity.
  // For example one of the cases is to map a notification with an order, order will store the ExternalRefId as ClientReqId .
  string external_ref_id = 18;
}

// Detailed status of the transaction for eg - ResponseCode, ResponseReason, InternalResponseCode
message DetailedStatus {
  string status_code = 1;
  string status_description = 2;
  string raw_status_code = 3;
  string raw_status_description = 4;
}

// Notification transaction details for which switch notification was received
message NotificationDetails {
  // dump of complete raw notification
  string raw_notification_data = 1;
  card.enums.MessageType message_type = 2;
  oneof Details {
    FinancialSwitchNotificationDetails financial_switch_notification_details = 3;
    NonFinancialSwitchNotificationDetails non_financial_switch_notification_details = 4;
  }
}

// Data object to store transaction details for notification associated with financial transaction
message FinancialSwitchNotificationDetails {
  card.enums.CardTransactionCategory txn_category = 1;
  google.protobuf.Timestamp txn_time = 2;
  google.type.Date txn_date = 3;
  google.type.Money txn_amount = 4;
  string txn_remarks = 5;
  string country_code = 6;
  api.typesv2.common.BooleanEnum is_forex_markup_transaction = 7;
  api.typesv2.common.BooleanEnum is_dcc_transaction = 8;
  api.typesv2.common.BooleanEnum is_card_tap_transaction = 9;
  api.typesv2.common.BooleanEnum is_device_tap_transaction = 10;
  google.type.Money txn_amount_in_org_currency = 11;
  // pincode of the ATM location, it will be only available for ATM transactions
  string atm_pincode = 12;
}

// Data object to store transaction details for notification associated with non-financial transaction
message NonFinancialSwitchNotificationDetails {}

message MerchantDetails {
  string merchant_name = 1;
  string merchant_location = 2;
  string merchant_id = 3;
  string sub_merchant_id = 4;
  string mcc = 5;
  string terminal_id = 6;
}

message AuthSwitchDetails {
  string auth_code = 1;
  card.enums.AuthorizationSwitch authorization_switch = 2;
}

message RemitterDetails {
  string remitter_instrument_type = 1;
  string remitter_code = 2;
  string payment_gateway = 3;
}

message DedupeId {
  string request_id = 1;
  string retrieval_reference_number = 2;
  google.protobuf.Timestamp txn_time = 3;
  string card_id = 4;
}
