// This files defines request response contracts for various activity types to be used in physical card dispatch flow
syntax = "proto3";

package card.activity.physicalcarddispatch;

import "api/card/provisioning/provisioning.proto";
import "api/card/provisioning/service.proto";
import "api/celestial/activity/header.proto";
import "api/order/order.proto";
import "api/typesv2/address.proto";
import "api/typesv2/money.proto";

option go_package = "github.com/epifi/gamma/api/card/activity/physicalcarddispatch";
option java_package = "com.github.epifi.gamma.api.card.activity.physicalcarddispatch";

message UpdateShippingAddressActivityRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;

  string card_id = 2;
}

message UpdateShippingAddressActivityResponse {
  // Common request header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;

  // boolean to determine if shipping address update stage has to be triggered again
  bool retrigger_shipping_address_update = 2;
}

message PhysicalCardDispatchActivityRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;

  string card_id = 2;

  // entry_point is used to identify from which flow physical dispatch was trigger
  card.provisioning.UIEntryPoint entry_point = 4;
}

message PhysicalCardDispatchActivityResponse {
  // Common request header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}

message CreateShippingPreferenceActivityRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;

  string card_id = 2;

  string actor_id = 3;

  api.typesv2.AddressType address_type = 4;
}

message CreateShippingPreferenceActivityResponse {
  // Common request header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}


// Request message for physical card dispatch activities
message InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;

  // card id of the card which needs to be dispatched
  string card_id = 2;

  // address type of the user's address where card needs to be delivered
  api.typesv2.AddressType address_type = 3;
}

message InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse {
  // Common request header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}

message UpdatePhysicalCardDispatchRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;

  // card id of the card which needs to be dispatched
  string card_id = 2;

  // state to which physical card dispatch request needs to be updated
  card.provisioning.RequestState state_to_update = 3;

  // sub status of the physical card dispatch request need to be updated
  card.provisioning.RequestSubStatus sub_status_to_update = 4;

  // last stage executed of during physical card dispatch request need to be updated
  card.provisioning.DCRequestStage stage_to_be_updated = 5;
}

message UpdatePhysicalCardDispatchResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}

message CheckPaymentStatusRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;

  string payment_client_req_id = 2;

  string card_id = 3;

  // entry_point is used to identify from which flow physical dispatch was trigger
  card.provisioning.UIEntryPoint entry_point = 4;

  string actor_id = 5;
}

message CheckPaymentStatusResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;

  order.OrderStatus payment_status = 2;
}

message CollectDebitCardChargesRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;

  string card_id = 3;

  // entry_point is used to identify from which flow physical dispatch was trigger
  card.provisioning.UIEntryPoint entry_point = 4;

  // amount to be paid to execute transaction
  api.typesv2.Money amount = 5;
}

message CollectDebitCardChargesResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}

message InitiateChargesCollectionRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;

  string card_id = 3;

  // entry_point is used to identify from which flow physical dispatch was trigger
  card.provisioning.UIEntryPoint entry_point = 4;

  // amount(without GST applied) to be paid to execute transaction
  api.typesv2.Money amount = 5;
}

message InitiateChargesCollectionResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}

message PollChargesCollectionStatusRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;

  string card_id = 3;

  // entry_point is used to identify from which flow physical dispatch was trigger
  card.provisioning.UIEntryPoint entry_point = 4;
}

message PollChargesCollectionStatusResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}
