// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb

syntax = "proto3";

package card.notification;

import "api/card/enums/notification.proto";
import "api/order/order.proto";
import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/card/notification";
option java_package = "com.github.epifi.gamma.api.card.notification";

// NotificationConsumerService for processing card related notifications.
// Currently we will process the events from vendor's switch for card financial and non financial transactions and
// either sync the data or record the data in our internal database. In case of financial notifications we will
// call payment service for creating order and transactions for the received notification event.
service NotificationConsumerService {
  // ProcessCardSwitchFinancialNotifications processes notification related to card transactions, this includes both
  // successful as well as failure transactions, we get various details for a transaction such as transaction_mode,
  // execution timestamp, amount, transaction type (recurring/one time) in the notifications. We will parse all these
  // information and create order and transactions for it. Payment service will take care of the dedupe logic in case
  // of duplicate notifications, dedupe will be done on combination of execution timestamp and arn. In case of
  // notification miss for successful transaction we will record the transaction via recon but won't be able
  // to record declined transactions as we don't have any other entry point for declined transactions. Future goals is
  // to send notifications for these card transactions.
  rpc ProcessCardSwitchFinancialNotifications (ProcessCardSwitchFinancialNotificationsRequest) returns (ProcessCardSwitchFinancialNotificationsResponse) {};

  // ProcessCardSwitchNonFinancialNotifications processes notifications related to non financial card operations such as
  // limit update, card status change, card pin set/change, we will process these notification and update the
  // states on our end and send corresponding communications to the user.
  rpc ProcessCardSwitchNonFinancialNotifications (ProcessCardSwitchNonFinancialNotificationsRequest) returns (ProcessCardSwitchNonFinancialNotificationsResponse) {};

  // ProcessForexTransactionsRefund RPC will  consume messages from a queue subscribing to order-update-topic and based on the preconditions for forex txn
 // refunds and then initiate the workflow to process refunds
  rpc ProcessForexTransactionsRefund (order.OrderUpdate) returns (ProcessForexTransactionsRefundResponse);

  // ProcessCardTransactions RPC will consume events from order-update topic, it will consume card txn and ignore the protocol related txns.
  rpc ProcessCardTransactions (order.OrderUpdate) returns (ProcessCardTransactionsResponse);
}

message ProcessCardTransactionsResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessForexTransactionsRefundResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCardSwitchFinancialNotificationsRequest {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;

  // Mode of Transaction :  ECOMM, POS, ATM or NFC
  card.enums.CardTransactionCategory card_transaction_category = 2;

  // unique reference id
  string request_id = 3;

  // timestamp at which financial event got executed
  google.protobuf.Timestamp executed_at = 4;

  // account number of the user
  string account_number = 5;

  // transaction amount
  google.type.Money amount = 6;

  // type of notification
  card.enums.NotificationType notification_type = 7;

  // Type of message such as reversal/advice/log only
  card.enums.MessageType message_type = 8;

  // unique identifier for a transaction
  string arn = 9;

  // in case of reversal original transaction details will be present. If not available then it will be blank. For
  // refunds the same will not be available
  string original_transaction_id = 10;

  // unique identifier for a card
  string vendor_card_id = 11;

  // country code of the merchant
  string country_code = 12;

  // remarks of the transaction
  string transaction_remarks = 13;

  // merchant name for the transaction
  string merchant_name = 14;

  // merchant id
  string merchant_id = 15;

  string payment_gateway = 16;

  string sub_merchant_id = 17;

  string terminal_id = 18;

  // acquiring bank for the transactions
  string acquiring_bank = 19;

  // mcc of the merchant
  string mcc = 20;

  // merchant location
  string merchant_location = 21;

  // TODO(priyansh) : Check with Prateek/Federal regarding what this field denotes
  card.enums.AuthorizationSwitch authorisation_switch = 22;

  // response code for txn
  string transaction_response_code = 23;

  // entry mode for the transaction
  card.enums.TransactionEntryMode transaction_entry_mode = 24;

  string auth_id = 25;

  // transaction state to determine if the transaction is successful or failed
  card.enums.TransactionState transaction_state = 26;

  // transaction response description
  card.enums.SwitchNotificationResponse switch_notification_response = 27;

  string remitter_code = 28;

  string remitter_instrument_type = 29;

  // dump complete notification processing request as a string
  string raw_notification_data = 30;

  // whether forex markup was charged for the transaction
  bool is_forex_markup_transaction = 31;

  google.protobuf.Timestamp transaction_time = 32;

  // whether DCC(Direct currency conversion) charges were applied for the transaction
  bool is_dcc_transaction = 33;

  // transaction amount in the original currency in which the transaction happened.
  google.type.Money txn_amount_in_org_currency = 34;

  // Whether it's a card tap transaction or nor.
  bool is_card_tap = 35;

  // Whether it's a device tap transaction or nor.
  bool is_device_tap = 36;

  // will be populated only for ATM transactions.
  string atm_pincode = 37;
}

message ProcessCardSwitchFinancialNotificationsResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCardSwitchNonFinancialNotificationsRequest {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;

  // timestamp at which event got completed
  google.protobuf.Timestamp completed_at = 2;

  // type of event
  card.enums.NotificationType notification_type = 3;

  // vendor card unique id
  string vendor_card_id = 4;

  // request id
  string request_id = 5;

  // dump complete notification processing request as a string
  string raw_notification_data = 6;
}

message ProcessCardSwitchNonFinancialNotificationsResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}
