// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package payload;

import "api/card/provisioning/service.proto";
import "api/typesv2/address.proto";
import "api/typesv2/money.proto";
option go_package = "github.com/epifi/gamma/api/card/workflow/payload";
option java_package = "com.github.epifi.gamma.api.card.workflow.payload";


message PhysicalCardDispatchInfo {
  // card id of the card which needs to be dispatched
  string card_id = 1;

  // address type of the user's address where card needs to be delivered
  api.typesv2.AddressType address_type = 2;

  // amount to be paid to execute transaction
  api.typesv2.Money amount = 3;

  // client request id associated with the payment initiated
  string payment_client_req_id = 4;

  // actor id of the user associated with card
  string actor_id = 5;

  // entry_point is used to identify from which flow physical dispatch was trigger
  card.provisioning.UIEntryPoint entry_point = 6;

  bool is_charges_collection_api_enabled = 7;

  api.typesv2.Money amount_without_gst = 8;

  // Flag to control charges collection flow, whether to go via v2 flow or v1 flow.
  // V2 signifying internal changes at card side, at vg layer endpoints are same only.
  bool is_charges_collection_api_v2_enabled = 9;
}
