// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package comms;

import "api/comms/enums.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/money.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/comms";
option java_package = "com.github.epifi.gamma.api.comms";

// proto message to be used in dao layer for email templates
message EmailTemplateData {
  string id = 1;
  // email type
  EmailType email_type = 2;
  // version of template
  TemplateVersion template_version = 3;
  // subject of email
  string subject = 4;
  // body of email
  string template = 5;
  google.protobuf.Timestamp CreatedAt = 6;
  google.protobuf.Timestamp UpdatedAt = 7;
}

// All email templates will be defined here which the clients can use to specify what they want to use
// Each template will have corresponding options to set the values
enum EmailType {
  EMAIL_TYPE_UNSPECIFIED = 0;

  WAITLIST_USER_ACCEPTED = 1;

  WAITLIST_USER_REJECTED = 2;

  WAITLIST_USER_CBO_ACCEPTED = 5;

  WAITLIST_USER_CBO_REJECTED = 6;

  SHERLOCK_VERIFICATION_EMAIL = 3;

  ACCOUNT_STATEMENT_EMAIL = 4;

  INTERNAL_OFFER_REDEMPTION_REPORT_EMAIL = 7;

  WAITLIST_USER_APP_ACCESS = 8;

  WAITLIST_USER_GMAIL = 9;

  WAITLIST_USER_FINITE_CODE = 10;

  VKYC_SCHEDULED_CALL = 11;

  VKYC_SCHEDULED_CALL_REMINDER = 12;

  WAITLIST_CBO_USER_FINITE_CODE = 13;

  MIN_KYC_USER_WELCOME_EMAIL = 14;

  FULL_KYC_USER_WELCOME_EMAIL = 15;

  WAITLIST_CBO_SHORTLIST = 16;

  WAITLIST_EXCLUSIVE_ACCESS_FINITE_CODE = 17;

  WAITLIST_CBO_IOS_SHORTLIST = 18;

  UN_NAME_CHECK_EMAIL = 19;

  FINITE_CODE_REMINDER_EMAIL = 20;

  DIRECT_ACCESS_FINITE_CODE = 21;

  CBO_UNKNOWN_DEVICE_FINITE_CODE = 22;

  ADD_FUND_SECOND_LEG_ALERT = 23;

  WAITLIST_IOS_WELCOME_EMAIL = 24;

  CBO_FEEDBACK_EMAIL = 25;

  WORK_EMAIL_VERIFICATION = 26;

  WAITLIST_IOS_APP_ACCESS = 27;

  WAITLIST_CBO_IOS_APP_ACCESS = 28;

  CX_AUTO_RESOLUTION_EMAIL = 29;

  DEPOSIT_MATURING_MIN_KYC_T_MINUS_X = 30;

  DEPOSIT_MATURING_MIN_KYC_T_PLUS_X = 31;

  INTERNAL_FINITE_CODE_DUMP_SHARE = 32;

  CX_BULK_USER_DETAILS_EMAIL = 33;

  CX_BULK_ACCOUNT_VALIDATIONS_EMAIL = 34;

  // Email template informing user that money has been transferred by the partner bank
  POST_ACCOUNT_CLOSURE_FUND_TRANSFER_EMAIL = 35;

  // Email template to inform user for min to full kyc conversion post onboarding
  MIN_TO_FULL_KYC_CONVERSION_EMAIL = 36;

  // Email template to mail federal that users are stuck in vkyc review state
  USER_STUCK_IN_VKYC_REVIEW_STATE_FEDERAL_EMAIL = 37;

  // Email template for mail to CX team for user verified account information for min kyc account expiration
  MIN_KYC_ACCOUNT_CLOSURE_USER_VERIFIED = 38;

  // Email template for mailing reports to internal folks
  INTERNAL_REPORT_EMAIL = 39;

  // Email template to share the generated execution report for a workflow
  WORKFLOW_EXECUTION_REPORT_EMAIL = 40;

  // Email template to capture user's feedback on raised issue
  ISSUE_RESOLUTION_USER_FEEDBACK_EMAIL = 41;

  // Email template to send account data for freeze to bank
  FEDERAL_MARK_ACCOUNTS_FOR_FREEZE_EMAIL = 42;

  // Email template to send account data for unfreeze to bank
  FEDERAL_MARK_ACCOUNTS_FOR_UNFREEZE_EMAIL = 43;

  // Email templates for risk-ops blocking/ unblocking/ credit-freeze
  // notification to users
  RISKOPS_USER_ACC_UNFREEZE = 44;
  RISKOPS_USER_HIGH_RISK_ACTIVITY_KYC = 45;
  RISKOPS_USER_LEA_FULL_FREEZE = 46;
  RISKOPS_USER_LOW_RISK_ACTIVITY = 47;
  RISKOPS_USER_MEDIUM_RISK_CF = 48;
  RISKOPS_USER_ONBOARDING_RISK_CF = 49;
  RISKOPS_USER_TOTAL_TO_CREDIT_FREEZE = 50;
  RISKOPS_USER_TXN_MONITOR_SOW = 51;

  // Email template to send p2p recon file to vendor
  P2P_RECON_FILE_EMAIL = 52;

  SALARY_PROGRAM_REGISTRATION_COMPLETED_EMAIL = 53;

  RISKOPS_USER_AFU_F = 54;
  RISKOPS_USER_INT_ATM = 55;
  RISKOPS_USER_M_KYC = 56;
  RISKOPS_USER_SD = 57;

  // Email template to send daily trade confirmation to users
  DAILY_TRADE_CONFIRMATION = 58;

  // Email template to send usstock monthly account statement report to users
  USSTOCKS_MONTHLY_ACCOUNT_STATEMENT_REPORT = 59;

  // Email template to send TaxStatement report to users for Mutual funds
  MF_TAX_STATEMENT_EMAIL = 60;

  // Email template to send issue details to user for issue processed by cx watson service
  CX_WATSON_INCIDENT_CREATION_EMAIL = 61;

  // Short version of Email template to send issue details to user for issue processed by cx watson service
  // This version is used if there is no ticket created for the incident
  CX_WATSON_INCIDENT_CREATION_EMAIL_SHORT = 62;

  // Email template for credit card controls: online/pos/international/contactless : enable/disable txns.
  CREDIT_CARD_CONTROLS_EMAIL = 63;

  // Email template for credit card transactions: atm/txn : success and failure.
  CREDIT_CARD_TRANSACTION_STATUS_EMAIL = 64;

  // Email template to send otp for mutual-fund one time buy orders.
  MF_ONE_TIME_BUY_OTP = 65;

  // Email template to send otp for mutual-fund sip registration.
  MF_REGISTER_SIP_OTP = 66;

  // risk-ops email templates
  RISKOPS_USER_ACC_UNF_CL = 67;
  RISKOPS_USER_CL_AC = 68;
  RISKOPS_USER_ONB_DENIED = 69;
  RISKOPS_USER_H_P_KYC = 70;
  RISKOPS_USER_INT_ATM_C = 71;
  RISKOPS_USER_TM_RULE_1 = 72;
  RISKOPS_USER_TM_RULE_2 = 73;
  RISKOPS_USER_TM_RULE_3 = 74;

  // email template to send deposit statement
  DEPOSIT_STATEMENT_EMAIL = 75;

  // Email template to send custom email for min-kyc expiry balance refund via watson service
  CX_WATSON_MIN_KYC_EXPIRY_BALANCE_REFUND_INCIDENT_CREATION_EMAIL = 76;

  // Email template to send credit card monthly statement
  CREDIT_CARD_STATEMENT_EMAIL = 77;
  // message to send in case spends on category exceed above limit
  CATEGORY_SPENDS_EXCEEDED_REMINDER_EMAIL = 78;
  // message to send a reminder for due date of credit card bill payment
  CREDIT_CARD_BILL_PAYMENT_DUE_DATE_REMINDER_EMAIL = 79;

  // Email template to send capital gains statement report to users for Mutual funds
  MF_CAPITAL_GAINS_EMAIL = 80;

  // Email template to send email to payee in case of  credit txn
  CREDIT_TXN_EMAIL = 81;

  // email template used by the auto action credit freeze
  RISK_USER_TM_AUTO_CF = 82;

  // email to notify user about card not activated yet
  CREDIT_CARD_NOT_ACTIVATED_EMAIL = 83;

  // email to inform user about card closure confirmation
  CREDIT_CARD_CLOSURE_CONFIRMATION_EMAIL = 84;

  // risk ops LEA info template
  RISKOPS_USER_LEA_INFO = 85;

  // welcome email to kyc agents
  KYC_AGENT_WELCOME_EMAIL = 86;

  // genie app login otp
  GENIE_LOGIN_OTP = 87;

  // Email template to send otp for mutual fund withdrawal
  MF_WITHDRAWAL_OTP = 88;
  // Email template to send forex refund file to federal
  DEBIT_CARD_FOREX_REFUND_EMAIL = 89;

  // Email send for work email verification from salary b2b lead generation web page
  SALARY_B2B_LEAD_WORK_EMAIL_VERIFICATION = 90;

  // User has verified the otp but has not booked a demo via salary B2B web page
  SALARY_B2B_LEAD_OTP_VERIFIED_SLOT_NOT_BOOKED = 91;

  // User has verified the otp and has booked a demo via salary B2B web page
  SALARY_B2B_LEAD_OTP_VERIFIED_SLOT_BOOKED = 92;

  // user could not verify work email
  SALARY_B2B_LEAD_WORK_EMAIL_NOT_VERIFIED = 93;

  // Jump investment maturing soon
  JUMP_INVESTMENT_MATURING_SOON = 94;

  // Jump eligibility check is success
  JUMP_ELIGIBILITY_CHECK_SUCCESS = 95;

  // Template with law enforcement authority contact details to be sent to LEA actor.
  // This is a follow-up email to the previous email containing complaint details email.
  RISK_LEA_CONTACT_INFO = 96;

  // Jump investment renewed
  JUMP_INVESTMENT_RENEWED = 97;

  // Jump investment paid out
  JUMP_INVESTMENT_PAID_OUT = 98;

  // Reminder email for outcall attempt made to a user during risk review
  // and user couldn't be reached.
  RISK_OUTCALL_ATTEMPT_REMINDER = 99;

  // Authorized/Unauthorized Dispute Escalated
  DISPUTE_ESCALATED_DMP_DISPUTE_STATUS_EMAIL = 100;

  // Authorized/Unauthorized Dispute Resolved (Status - Resolved)
  DISPUTE_RESOLVED_DMP_DISPUTE_STATUS_EMAIL = 101;

  // Authorized Dispute Chargeback Accepted (Status - Resolved Accepted)
  AUTHORIZED_DISPUTE_CHARGEBACK_ACCEPTED_DMP_DISPUTE_STATUS_EMAIL = 102;

  // Authorized Dispute Chargeback Rejected (Status - Resolved Rejected)
  AUTHORIZED_DISPUTE_CHARGEBACK_REJECTED_DMP_DISPUTE_STATUS_EMAIL = 103;

  // Unauthorized Dispute Chargeback Accepted (Status - Resolved Accepted)
  UNAUTHORIZED_DISPUTE_LIABILITY_ACCEPTED_DMP_DISPUTE_STATUS_EMAIL = 104;

  // Unauthorized Dispute Chargeback Rejected (Status - Resolved Rejected)
  UNAUTHORIZED_DISPUTE_LIABILITY_REJECTED_DMP_DISPUTE_STATUS_EMAIL = 105;

  // Used in Web Min KYC closed account balance transfer flow
  WEB_MIN_KYC_CLOSED_ACC_BALANCE_TRANSFER_OTP = 106;

  // This email is sent to a user after successful account creation with basic account details.
  EMAIL_SAVINGS_ACCOUNT_SUMMARY = 107;

  // Email template to inform user for min to full kyc conversion pre customer creation
  PRE_CUSTOMER_CREATION_MIN_TO_FULL_KYC_CONVERSION = 108;

  // Jump investment and withdrawal transaction status
  JUMP_INVESTMENT_DELAYED = 109;
  JUMP_INVESTMENT_SUCCESS = 110;
  JUMP_INVESTMENT_FAILED = 111;
  JUMP_WITHDRAWAL_DELAYED = 112;
  JUMP_WITHDRAWAL_SUCCESS = 113;
  JUMP_WITHDRAWAL_FAILED_ACCOUNT_FROZEN = 114;

  // LAMF progress status and reminder
  LAMF_LOAN_AMOUNT_DISBURSED = 115;
  LAMF_LOAN_CLOSED = 116;
  LAMF_LOAN_EMI_DUE_LOW_BALANCE = 117;
  LAMF_LOAN_EMI_OVERDUE = 118;

  // email template for sharing daily loans payment transaction file
  // currently we are using this template for IDFC
  LOANS_PAYMENT_FILE = 119;

  // email template for dropOff of web flow credit card eligible user
  CREDIT_CARD_ELIGIBLE_WEB_FLOW_EMAIL = 120;

  // email to be sent after successful card creation
  CREDIT_CARD_WELCOME_EMAIL = 121;

  // email template for sending sa closure requests to ops
  SAVINGS_ACCOUNT_CLOSURE_REQUEST = 122;

  // email to be sent to ITC for Green Points transfer
  CLUB_ITC_GREEN_POINTS_TRANSFER_REQUEST_EMAIL = 123;

  // email to be sent after successful magnifi card creation
  MAGNIFI_WELCOME_EMAIL = 124;

  // Email to be sent to user for risk out call purpose.
  RISK_OUTCALL = 125;

  // Email to be sent to user for credit freeze on savings account due to inactivity.
  RISKOPS_USER_INACTIVITY_CREDIT_FREEZE = 126;

  // Email to share health logs
  PUSH_APP_LOGS = 127;

  // Email to be sent to user for account debit freeze
  RISKOPS_USER_ACC_DF = 128;

  // Email to be sent to user for video KYC credit freeze
  RISKOPS_USER_VKYC_CF = 129;

  // email to be sent after successful CC EMI conversion
  CREDIT_CARD_EMI_CREATION_EMAIL = 130;

  // email to be sent after cancellation of CC EMI
  CREDIT_CARD_EMI_CANCELLATION_EMAIL = 131;

  // email to be sent after pre-closure of CC EMI
  CREDIT_CARD_EMI_PRE_CLOSURE_EMAIL = 132;

  // email to be sent after debit card amc charge deduction
  DEBIT_CARD_AMC_CHARGES = 133;

  // email to be sent on closing of emi
  CREDIT_CARD_EMI_CLOSURE_EMAIL = 134;

  RISKOPS_USER_M_KYC_PCF = 135;

  LAMF_CX_LOAN_APPLICATION_CLOSURE_RESOLUTION_EMAIL = 136;

  // this email is sent on resolution of cx ticket that was generated because user wasn't able to proceed further in the loan application due of some issue
  // expectation is that user should be unblocked to proceed further in the loan application before sending this email.
  LAMF_CX_LOAN_APPLICATION_RETRY_RESOLUTION_EMAIL = 137;

  LAMF_CX_LOAN_ACCOUNT_CLOSURE_RESOLUTION_EMAIL = 138;

  DEBIT_CARD_AMC_CHARGES_REPORT_EMAIL = 139;

  USS_TAX_DOCUMENT_EMAIL = 140;

  EMAIL_UPDATE_FEDERAL = 141;
  // email template to send yearly jump income statement to user
  JUMP_YEARLY_ACCOUNT_STATEMENT_EMAIL = 142;
  // Email template to send capital gains statement report to users for Mutual funds
  MF_EXTERNAL_CAPITAL_GAINS_EMAIL = 143;

  DEBIT_CARD_INTERNATIONAL_ATM_WITHDRAWAL_LIMIT_EMAIL = 144;

  // Email template to send LEA comms to users
  RISK_UNIFIED_LEA_EMAIL = 145;

  // Email template to send credit freeze form
  RISKOPS_USER_MEDIUM_RISK_CF_FORM = 146;

  // Email to be sent for esigning the document from TSP.
  STOCKGUARDIAN_LOAN_APPLICATION_ESIGN_EMAIL = 147;

  // email template for LEA comms to ask for relevant document
  RISK_UNIFIED_LEA_LAYER_INVESTIGATION_EMAIL = 148;

  // email to be sent after nbfc loan is disbursed
  LOAN_CONFIRMATION_EMAIL = 149;

  // email template for tiering rewards summary email
  // currently being used to sent tiering cashback summary
  TIERING_REWARDS_SUMMARY_EMAIL = 150;

  // email template to capture csat (customer satisfaction score) of user's ticket
  CX_TICKET_RESOLUTION_CSAT_EMAIL = 151;

  // email to be sent to vendor for kyc compliance status change for cc users
  CC_KYC_COMPLIANCE_STATUS_CHANGE_EMAIL = 152;
}

message StockguardianLoanApplicationEsignEmail {
  EmailType email_type = 1 [(validate.rules).enum.const = 147];
  oneof option {
    StockguardianLoanApplicationEsignEmailV1 stockguardian_loan_application_esign_email_v1 = 2;
  }
}

message StockguardianLoanApplicationEsignEmailV1 {
  string otp = 1;
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message DebitCardInternationalAtmWithdrawalLimitEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 144];
  oneof option {
    DebitCardInternationalAtmWithdrawalLimitEmailOptionV1 debit_card_international_atm_withdrawal_limit_email_option_v1 = 2;
  }
}

message DebitCardInternationalAtmWithdrawalLimitEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // name of the user
  api.typesv2.common.Name name = 2;
  // country in which txn was done
  string country = 3;
  google.type.Money withdrawal_amount_limit = 4;
}

message EmailUpdateFederalEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string first_name = 2;
  // Date when email update in initiated
  string requested_date = 3;
  // Timestamp when email update in initiated
  string requested_time = 4;
  string new_email = 5;
}

message EmailUpdateFederalEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 141];
  oneof option {
    EmailUpdateFederalEmailV1 email_update_federal_email_v1 = 2;
  }
}

message LamfCxLoanApplicationClosureResolutionEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 136];
  oneof option {
    LamfCxLoanApplicationClosureResolutionEmailOptionV1 lamf_cx_loan_application_closure_resolution_email_option_v1 = 2;
  }
}

message LamfCxLoanApplicationClosureResolutionEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message LamfCxLoanApplicationRetryResolutionEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 137];
  oneof option {
    LamfCxLoanApplicationRetryResolutionEmailOptionV1 lamf_cx_loan_application_retry_resolution_email_option_v1 = 2;
  }
}

message LamfCxLoanApplicationRetryResolutionEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message LamfCxLoanAccountClosureResolutionEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 138];
  oneof option {
    LamfCxLoanAccountClosureResolutionEmailOptionV1 lamf_cx_loan_account_closure_resolution_email_option_v1 = 2;
  }
}

message LamfCxLoanAccountClosureResolutionEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message CreditCardEmiClosureEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 134];
  oneof option {
    CreditCardEmiClosureEmailOptionV1 credit_card_emi_closure_email_option_v1 = 2;
  }
}

message CreditCardEmiClosureEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant_name = 2;
  string card_number = 3;
  string creation_date = 4;
  string completion_date = 5;
  string total_amount_paid = 6;
  string principal_amount = 7;
  string processing_fees = 8;
  string interest_paid = 9;
}

message CreditCardEmiPreClosureEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 132];
  oneof option {
    CreditCardEmiPreClosureEmailOptionV1 credit_card_emi_pre_closure_email_option_v1 = 2;
  }
}

message CreditCardEmiPreClosureEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant_name = 2;
  string due_amount = 3;
  string emi_amount = 4;
  string creation_date = 5;
  string total_charges = 6;
  string pre_closure_fees = 7;
}

message CreditCardEmiCancellationEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 131];
  oneof option {
    CreditCardEmiCancellationEmailOptionV1 credit_card_emi_cancellation_email_option_v1 = 2;
  }
}

message CreditCardEmiCancellationEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant_name = 2;
  string transaction_amount = 3;
  string emi_amount = 4;
  string cancellation_date = 5;
  string creation_date = 6;
  string next_bill_gen_date = 7;
  string card_last_four_digits = 8;
  string interest_charges = 9;
  string pre_closure_charges = 10;
  string amount_added_to_next_statement = 11;
}

message DebitCardAmcChargesEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 133];
  oneof option {
    DebitCardAmcChargesEmailOptionV1 debit_card_amc_charges_email_option_v1 = 2;
    DebitCardAmcChargesEmailOptionV2 debit_card_amc_charges_email_option_v2 = 3;
  }
}

message DebitCardAmcChargesEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string amc_charge_amount = 2;
  string txn_date = 3;
  string card_holder_name = 4;
}

message DebitCardAmcChargesEmailOptionV2 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];
  string amc_charge_amount = 2;
  string txn_date = 3;
  string card_holder_name = 4;
  string min_spends_threshold_amount = 5;
}

message DebitCardAmcChargesReportEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string heading = 2;
  string description = 3;
  string icon_url = 4;
  string background_color = 5;
  string file_gen_date = 6;
}


message DebitCardAmcChargesReportEmail {
  EmailType email_type = 1 [(validate.rules).enum.const = 139];
  oneof option {
    DebitCardAmcChargesReportEmailOptionV1 debit_card_amc_charges_report_email_option_v1 = 2;
  }
}

message CreditCardEmiCreationEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 130];
  oneof option {
    CreditCardEmiCreationEmailOptionV1 credit_card_emi_creation_email_option_v1 = 2;
  }
}

message CreditCardEmiCreationEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string merchant_name = 2;
  string transaction_amount = 3;
  string emi_amount = 4;
  string cancellation_date = 5;
  string creation_date = 6;
  string transaction_date = 7;
  string tenure = 8;
  string interest_rate = 9;
  string processing_fees = 10;
}

message RiskOpsVKYCCreditFreezeEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 129];
  oneof option {
    RiskOpsVKYCCreditFreezeEmailOptionV1 risk_ops_vkyc_credit_freeze_email_option_v1 = 2;
  }
}

message RiskOpsVKYCCreditFreezeEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // first name of the user
  string first_name = 2;

  // masked account number
  string masked_account_number = 3;
}

message PushAppLogsEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 127];
  oneof option {
    PushAppLogsEmailOptionV1 push_app_logs_email_option_v1 = 2;
  }
}

message PushAppLogsEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // deeplink using which user can share the app logs
  string deep_link = 2;
}

message ClubItcGreenPointsTransferRequestEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 123];
  oneof option {
    ClubItcGreenPointsTransferRequestEmailOptionV1 club_itc_green_points_transfer_request_email_option_v1 = 2;
  }
}

message ClubItcGreenPointsTransferRequestEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string report_date = 2;
  string body_text = 3;
}

message SaClosureRequestEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 122];
  oneof option {
    SaClosureSubmitToCxEmailV1 sa_closure_submit_to_cx_email_v1 = 2;
  }
}

message SaClosureSubmitToCxEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string current_date = 2;
  string additional_message = 3;
}

message CreditCardWelcomeEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 121];
  oneof option {
    CreditCardWelcomeEmailOptionV1 credit_card_welcome_email_option_v1 = 2;
  }
}

message CreditCardWelcomeEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string card_name = 2;
  string card_image_url = 3;
  string common_tncs = 4;
  string most_important_tncs = 5;
  string key_fact_statement = 6;
}

message LamfLoanAmountDisbursedEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 115];
  oneof option {
    LamfLoanAmountDisbursedEmailV1 lamf_amount_disbursed_email_v1 = 2;
  }
}

message LamfLoanAmountDisbursedEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
}

message MagnifiWelcomeEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 124];
  oneof option {
    MagnifiWelcomeEmailOptionV1 magnifi_welcome_email_option_v1 = 2;
  }
}

message MagnifiWelcomeEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}


message LamfLoanClosedEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 116];
  oneof option {
    LamfLoanClosedEmailV1 lamf_loan_closed_email_v1 = 2;
  }
}

message LamfLoanClosedEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
}

message LamfLoanEmiDueLowBalanceOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 117];
  oneof option {
    LamfLoanEmiDueLowBalanceEmailV1 lamf_loan_emi_due_low_balance_email_v1 = 2;
  }
}

message LamfLoanEmiDueLowBalanceEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string emi_amount = 3;
  string due_date = 4;
}

message LamfLoanEmiOverdueOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 118];
  oneof option {
    LamfLoanEmiOverdueEmailV1 lamf_loan_emi_overdue_email_v1 = 2;
  }
}

message LoansPaymentFileEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 119];
  oneof option {
    LoansPaymentFileEmailV1 loans_payment_file_email_v1 = 2;
  }
}

message LoanConfirmationEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 149];
  oneof option {
    LoanConfirmationEmailV1 loans_payment_file_email_v1 = 2;
  }
}

message LoanConfirmationEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string name = 2;
  string last_four_digits_of_account_number = 3;
  string contact_number = 4;
  string lsp_logo = 5;
  string lsp_name = 6;
}

message LoansPaymentFileEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string report_date = 2;
  // injecting entire body via params as the content is dynamic
  string body_text = 3;
}

message LamfLoanEmiOverdueEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
}

message JumpWithdrawalFailedEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 114];
  oneof option {
    JumpWithdrawalFailedEmailV1 jump_withdrawal_failed_email_v1 = 2;
  }
}

message JumpWithdrawalFailedEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string amount = 3;
  string ticked_id = 4;
  string plan_name = 5;
}

message JumpWithdrawalSuccessEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 113];
  oneof option {
    JumpWithdrawalSuccessEmailV1 jump_withdrawal_success_email_v1 = 2;
  }
}

message JumpWithdrawalSuccessEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string amount = 3;
  string ticked_id = 4;
  string plan_name = 5;
}

message JumpWithdrawalDelayedEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 112];
  oneof option {
    JumpWithdrawalDelayedEmailV1 jump_withdrawal_delayed_email_v1 = 2;
  }
}

message JumpWithdrawalDelayedEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string amount = 3;
  string ticked_id = 4;
  string plan_name = 5;
}

message JumpInvestmentFailedEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 111];
  oneof option {
    JumpInvestmentFailedEmailV1 jump_investment_failed_email_v1 = 2;
  }
}

message JumpInvestmentFailedEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string amount = 3;
  string ticked_id = 4;
  string plan_name = 5;
}


message JumpInvestmentSuccessEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 110];
  oneof option {
    JumpInvestmentSuccessEmailV1 jump_investment_success_email_v1 = 2;
  }
}

message JumpInvestmentSuccessEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string amount = 3;
  string ticked_id = 4;
  string plan_name = 5;
}

message JumpInvestmentDelayedEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 109];
  oneof option {
    JumpInvestmentDelayedEmailV1 jump_investment_delayed_email_v1 = 2;
  }
}

message JumpInvestmentDelayedEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string amount = 3;
  string ticked_id = 4;
  string plan_name = 5;
}

message SavingsAccountSummaryEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string interest_rate = 2;
  string current_date = 3;
}

message SavingsAccountSummaryEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 107];
  oneof option {
    SavingsAccountSummaryEmailV1 savings_account_summary_email_v1 = 2;
  }
}

message JumpInvestmentRenewedEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 97];
  oneof option {
    JumpInvestmentRenewedEmailV1 jump_investment_renewed_email_v1 = 2;
  }
}

message JumpInvestmentRenewedEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string investment_amount = 3;
  string scheme_name = 4;
  string maturity_amount = 5;
  string reinvestment_scheme_name = 6;
  string reinvestment_roi = 7;
  string redirection_link = 8;
}

message JumpInvestmentPaidOutEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 98];
  oneof option {
    JumpInvestmentPaidOutEmailV1 jump_investment_paid_out_email_v1 = 2;
  }
}

message WebMinKycClosedAccBalanceTransferEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 106];
  oneof option {
    WebMinKycClosedAccBalanceTransferEmailV1 web_min_kyc_closed_acc_balance_transfer_email_v1 = 2;
  }
}

message WebMinKycClosedAccBalanceTransferEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string otp = 2;
}

message JumpInvestmentPaidOutEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string investment_amount = 3;
  string scheme_name = 4;
  string maturity_amount = 5;
  string redirection_link = 6;
}

message JumpInvestmentMaturingSoonEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 94];
  oneof option {
    JumpInvestmentMaturingSoonEmailV1 jump_investment_maturing_soon_email_v1 = 2;
  }
}

message JumpInvestmentMaturingSoonEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string investment_amount = 3;
  string maturity_date = 4;
  string scheme_name = 5;
  string returns = 6;
  string maturity_amount = 7;
  string reinvestment_maturity_amount = 8;
  string reinvestment_returns = 9;
  string redirection_link = 10;
}

message JumpEligibilityCheckSuccessEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 95];
  oneof option {
    JumpEligibilityCheckSuccessEmailV1 jump_eligibility_check_success_email_v1 = 2;
  }
}

message JumpEligibilityCheckSuccessEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string investor_name = 2;
  string redirection_link = 3;
}

message SalaryB2BLeadWorkEmailVerificationEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 90];

  oneof option {
    SalaryB2BLeadWorkEmailVerificationEmailOptionV1 salary_b_2b_lead_work_email_verification_email_option_v1 = 2;
  }
}

message SalaryB2BLeadWorkEmailVerificationEmailOptionV1 {
  string otp = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message SalaryB2BLeadOTPVerifiedSlotNotBookedEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 91];

  oneof option {
    SalaryB2BLeadOTPVerifiedSlotNotBookedEmailOptionV1 salary_b_2b_lead_otp_verified_slot_not_booked_email_option_v1 = 2;
  }
}

message SalaryB2BLeadOTPVerifiedSlotNotBookedEmailOptionV1 {
  string name = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message SalaryB2BLeadOTPVerifiedSlotBookedEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 92];

  oneof option {
    SalaryB2BLeadOTPVerifiedSlotBookedEmailOptionV1 salary_b_2b_lead_otp_verified_slot_booked_email_option_v1 = 2;
  }
}

message SalaryB2BLeadOTPVerifiedSlotBookedEmailOptionV1 {
  string name = 1;
  string calendly_message = 2;
  // to be set as VERSION_V1
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 1];
}

message SalaryB2BLeadWorkEmailNotVerifiedEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 93];

  oneof option {
    SalaryB2BLeadWorkEmailNotVerifiedEmailOptionV1 salary_b_2b_lead_work_email_not_verified_email_option_v1 = 2;
  }
}

message SalaryB2BLeadWorkEmailNotVerifiedEmailOptionV1 {
  string name = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message WorkflowExecutionReportEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 40];

  oneof option {
    WorkflowExecutionReportEmailOptionV1 workflow_execution_report_email_option_v1 = 2;
  }
}

message WorkflowExecutionReportEmailOptionV1 {
  // message to be communicated to the user on the basis of the input filters
  string message = 1;

  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

// each template will have certain options which are the variables that will be filled in the template
message InternalFiniteCodeDumpShareOptionsV1 {
  // date for which the report is generated.
  string report_date = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message MinKycUserWelcomeEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 14];

  oneof option {
    MinKycUserWelcomeEmailOptionsV1 min_kyc_welcome_email_option_v1 = 2;

    MinKycUserWelcomeEmailOptionsV2 min_kyc_welcome_email_option_v2 = 3;

    MinKycUserWelcomeEmailOptionsV3 min_kyc_welcome_email_option_v3 = 4;
  }
}

message MinKycUserWelcomeEmailOptionsV1 {
  // name of the user
  string name = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message MinKycUserWelcomeEmailOptionsV2 {
  // name of the user
  string name = 1;
  // to be set as VERSION_V2
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 2];
}

message MinKycUserWelcomeEmailOptionsV3 {
  // name of the user
  string name = 1;
  // to be set as VERSION_V3
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 3];
}

// each template will have certain options which are the variables that will be filled in the template
message FullKycUserWelcomeEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 15];

  oneof option {
    FullKycUserWelcomeEmailOptionsV1 full_kyc_welcome_email_option_v1 = 2;

    FullKycUserWelcomeEmailOptionsV2 full_kyc_welcome_email_option_v2 = 3;
  }
}

message FullKycUserWelcomeEmailOptionsV1 {
  // name of the user
  string name = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message FullKycUserWelcomeEmailOptionsV2 {
  // name of the user
  string name = 1;
  // to be set as VERSION_V2
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 2];
}

message WaitlistUserAcceptedOptionsV1 {
  // name of the user
  string name = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message WaitlistUserRejectedOptionsV1 {
  // name of the user
  string name = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message WaitlistUserCBOAcceptedOptionsV1 {
  // name of the user
  string name = 1;
  // voucher code for CBO share.
  string voucher_code = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message WaitlistUserCBORejectedOptionsV1 {
  // name of the user
  string name = 1;
  // voucher code for CBO share.
  string voucher_code = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message WaitlistUserAppAccessOptionsV1 {
  // Name of the user.
  string name = 1;
  // App link.
  string app_link = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message WaitlistUserGmailInputOptionsV1 {
  // Name of the user.
  string name = 1;
  // Fi Page link to input gmail address.
  string gmail_input_link = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message SherlockVerificationEmailOptionsV1 {
  // name of the user
  string name_in_body = 1;
  // validity of email in minutes
  int64 expiry_minutes = 2;
  // verification link
  string verification_link = 3;
  // name in subject
  string name_in_subject = 4;
  // template version to be set as VERSION_V1
  TemplateVersion template_version = 5 [(validate.rules).enum.const = 1];
}

message AccountStatementEmailOptionsV1 {
  // name of the user
  string name = 1;
  // median amount of the transactions for the specified date range
  int64 median_amount = 2;
  // number of transactions for the specified date range
  int32 number_of_transactions = 3;
  // starting date from which statement needs to be fetched
  string from_date = 4;
  // ending date upto which statement needs to be fetched
  string to_date = 5;

  // to be set as VERSION_V1
  TemplateVersion template_version = 6 [(validate.rules).enum.const = 1];
}

message AccountStatementEmailOptionsV2 {
  // name of the user
  string name = 1;
  // median amount of the transactions for the specified date range
  int64 median_amount = 2;
  // number of transactions for the specified date range
  int32 number_of_transactions = 3;
  // starting date from which statement needs to be fetched
  string from_date = 4;
  // ending date upto which statement needs to be fetched
  string to_date = 5;

  // to be set as VERSION_V1
  TemplateVersion template_version = 6 [(validate.rules).enum.const = 2];

  // used to redirect to mutual_funds_page from the mutual_funds banner
  string mutual_funds_page_url = 7;
}

message DepositStatementEmailOptionsV1 {
  // name of the user
  string name = 1;
  // median amount of the transactions for the specified date range
  int64 median_amount = 2;
  // number of transactions for the specified date range
  int32 number_of_transactions = 3;
  // starting date from which statement needs to be fetched
  string from_date = 4;
  // ending date upto which statement needs to be fetched
  string to_date = 5;

  // to be set as VERSION_V1
  TemplateVersion template_version = 6 [(validate.rules).enum.const = 1];

  // used to redirect to mutual_funds_page from the mutual_funds banner
  string mutual_funds_page_url = 7;
}

message WaitlistUserFiniteCodeOptionsV1 {
  // Name of the user.
  string name = 1;
  // App link.
  string app_link = 2;
  // finite code
  string finite_code = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 1];
}

message WaitlistUserFiniteCodeOptionsV2 {
  // Name of the user.
  string name = 1;
  // App link.
  string app_link = 2;
  // finite code
  string finite_code = 3;
  // to be set as VERSION_V2
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 2];
}

message WaitlistUserFiniteCodeOptionsV3 {
  // Name of the user.
  string name = 1;
  // App link.
  string app_link = 2;
  // finite code
  string finite_code = 3;
  // to be set as VERSION_V3
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 3];
}

message InternalOfferRedemptionReportEmailOptionsV1 {
  // date for which the report is generated.
  string report_date = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message InternalReportEmailOptionsV1 {
  // title of the report
  string report_title = 1;
  // date for which the report is generated.
  string report_date = 2;
  // to be set as VERSION_V1
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 1];
}

message VKYCScheduleCallEmailOptionsV1 {
  // Name of the user.
  string first_name = 1;
  // date & time to be shown to user (eg Mon, Jan 18 3:00PM)
  google.protobuf.Timestamp date_time = 2;
  // calendar event name
  string calendar_event_name = 3;
  // calendar event details(description)
  string calendar_event_details = 4;
  // to be set as VERSION_V1
  TemplateVersion template_version = 5 [(validate.rules).enum.const = 1];
}

message VKYCScheduleCallReminderEmailOptionsV1 {
  // Name of the user.
  string first_name = 1;
  // date & time to be shown to user (eg Mon, Jan 18 3:00PM)
  google.protobuf.Timestamp date_time = 2;
  // calendar event name
  string calendar_event_name = 3;
  // calendar event details(description)
  string calendar_event_details = 4;
  // to be set as VERSION_V1
  TemplateVersion template_version = 5 [(validate.rules).enum.const = 1];
}

message WaitlistCboUserFiniteCodeEmailOptionsV1 {
  // Name of the user.
  string name = 1;
  // App link.
  string app_link = 2;
  // finite code
  string finite_code = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 1];
}

message WaitlistCboUserFiniteCodeEmailOptionsV2 {
  // Name of the user.
  string name = 1;
  // App link.
  string app_link = 2;
  // finite code
  string finite_code = 3;
  // to be set as VERSION_V2
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 2];
}

message WaitlistCboUserFiniteCodeEmailOptionsV3 {
  // Name of the user.
  string name = 1;
  // App link.
  string app_link = 2;
  // finite code
  string finite_code = 3;
  // to be set as VERSION_V3
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 3];
}

message WaitlistCboShortlistEmailOptionsV1 {
  // Name of the user.
  string name = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message WaitlistCboShortlistEmailOptionsV2 {
  // Name of the user.
  string name = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 2];
}

message WaitlistExclusiveAccessFiniteCodeOptionsV1 {
  // Name of the user.
  string name = 1;
  // App link.
  string app_link = 2;
  // finite code
  string finite_code = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 1];
}

message WaitlistCboIosShortlistEmailOptionsV1 {
  // Name of the user.
  string name = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message UNNameCheckEmailOptionV1 {
  // Name of the user
  string name = 1;
  // phone number
  string phone_number = 2;
  // DOB
  string dob = 3;
  // PAN
  string pan = 4;
  // permanent address
  string permanent_address = 5;
  // correspondence address
  string correspondence_address = 6;
  // ckyc no
  string ckyc_no = 7;
  // ekyc no
  string ekyc_no = 8;
  // to be set as VERSION_V1
  TemplateVersion template_version = 9 [(validate.rules).enum.const = 1];
}

message FiniteCodeReminderEmailOptionV1 {
  // name of the user
  string name = 1;
  // finite code
  string finite_code = 2;
  // to be set as VERSION_V1
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 1];
  // user can click on this link to opt-in for an ios app instead
  string ios_option_link = 4;
}

message FiniteCodeReminderEmailOptionV2 {
  // name of the user
  string name = 1;
  // finite code
  string finite_code = 2;
  // to be set as VERSION_V2
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 2];
  // user can click on this link to opt-in for an ios app instead
  string ios_option_link = 4;
}

message DirectAccessFiniteCodeEmailOptionV1 {
  // name of the user
  string name = 1;
  // finite code
  string finite_code = 2;
  // user can click on this link to opt-in for an ios app instead
  string ios_option_link = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 1];
}

message CboUnknownDeviceFiniteCodeEmailOptionV1 {
  // name of the user
  string name = 1;
  // finite code
  string finite_code = 2;
  // user can click on this link to opt-in for an ios app instead
  string ios_option_link = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 1];
}

message AddFundsSecondLegAlertEmailOptionV1 {
  // ReqId for the transaction
  string req_id = 1;

  // error code received during enquiry
  string error_code = 2;

  // description for the error code
  string error_description = 3;

  // account number of the user
  string account_number = 4;

  // utr for the transaction
  string utr = 5;

  // created timestamp of the transaction
  string created_timestamp = 6;

  // to be set as VERSION_V1
  TemplateVersion template_version = 7 [(validate.rules).enum.const = 1];
}

message WaitlistIosWelcomeEmailOptionsV1 {

  string first_name = 1;

  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message CboFeedbackEmailOptionsV1 {

  string first_name = 1;

  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistUserAcceptedEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 1];

  oneof option {
    WaitlistUserAcceptedOptionsV1 waitlist_user_accepted_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistUserRejectedEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 2];

  oneof option {
    WaitlistUserRejectedOptionsV1 waitlist_user_rejected_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistUserCBOAcceptedEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 5];

  oneof option {
    WaitlistUserCBOAcceptedOptionsV1 waitlist_user_cbo_accepted_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistUserCBORejectedEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 6];

  oneof option {
    WaitlistUserCBORejectedOptionsV1 waitlist_user_cbo_rejected_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistUserAppAccessEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 8];

  oneof option {
    WaitlistUserAppAccessOptionsV1 waitlist_user_app_access_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistUserGmailInputEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 9];

  oneof option {
    WaitlistUserGmailInputOptionsV1 waitlist_user_gmail_input_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message VKYCScheduleCallEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 11];

  oneof option {
    VKYCScheduleCallEmailOptionsV1 vkyc_schedule_call_email_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message VKYCScheduleCallReminderEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 12];

  oneof option {
    VKYCScheduleCallReminderEmailOptionsV1 vkyc_schedule_call_reminder_email_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message SherlockVerificationEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 3];

  oneof option {
    SherlockVerificationEmailOptionsV1 sherlock_verification_email_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message AccountStatementEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 4];

  oneof option {
    AccountStatementEmailOptionsV1 account_statement_email_option_v1 = 2;
    AccountStatementEmailOptionsV2 account_statement_email_option_v2 = 3;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message DepositStatementEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 75];

  oneof option {
    DepositStatementEmailOptionsV1 deposit_statement_email_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message InternalOfferRedemptionReportEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 7];

  oneof option {
    InternalOfferRedemptionReportEmailOptionsV1 internal_offer_redemption_report_email_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message InternalReportEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 39];

  oneof option {
    InternalReportEmailOptionsV1 internal_report_email_option_v1 = 2;
  }
}

message InternalFiniteCodeDumpShareOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 32];

  oneof option {
    InternalFiniteCodeDumpShareOptionsV1 internal_finite_code_dump_share_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistUserFiniteCodeEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 10];

  oneof option {
    WaitlistUserFiniteCodeOptionsV1 waitlist_user_finite_code_option_v1 = 2;

    WaitlistUserFiniteCodeOptionsV2 waitlist_user_finite_code_option_v2 = 3;

    WaitlistUserFiniteCodeOptionsV3 waitlist_user_finite_code_option_v3 = 4;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistCboUserFiniteCodeEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 13];

  oneof option {
    WaitlistCboUserFiniteCodeEmailOptionsV1 waitlist_cbo_user_finite_code_option_v1 = 2;

    WaitlistCboUserFiniteCodeEmailOptionsV2 waitlist_cbo_user_finite_code_option_v2 = 3;

    WaitlistCboUserFiniteCodeEmailOptionsV3 waitlist_cbo_user_finite_code_option_v3 = 4;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistCboShortlistEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 16];

  oneof option {
    WaitlistCboShortlistEmailOptionsV1 waitlist_cbo_shortlist_option_v1 = 2;

    WaitlistCboShortlistEmailOptionsV2 waitlist_cbo_shortlist_option_v2 = 3;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistExclusiveAccessFiniteCodeOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 17];

  oneof option {
    WaitlistExclusiveAccessFiniteCodeOptionsV1 waitlist_exclusive_access_finite_code_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistCboIosShortlistEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 18];

  oneof option {
    WaitlistCboIosShortlistEmailOptionsV1 waitlist_cbo_ios_shortlist_option_v1 = 2;
  }
}

message UNNameCheckEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 19];

  oneof option {
    UNNameCheckEmailOptionV1 un_name_check_email_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message FiniteCodeReminderEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 20];

  oneof option {
    FiniteCodeReminderEmailOptionV1 finite_code_reminder_email_option_v1 = 2;
    FiniteCodeReminderEmailOptionV2 finite_code_reminder_email_option_v2 = 3;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message DirectAccessFiniteCodeEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 21];

  oneof option {
    DirectAccessFiniteCodeEmailOptionV1 direct_access_finite_code_email_option_v1 = 2;
  }
}

// each template will have certain options which are the variables that will be filled in the template
message CboUnknownDeviceFiniteCodeEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 22];

  oneof option {
    CboUnknownDeviceFiniteCodeEmailOptionV1 cbo_unknown_device_finite_code_email_option_v1 = 2;
  }
}

message AddFundsSecondLegAlertEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 23];

  oneof option {
    AddFundsSecondLegAlertEmailOptionV1 add_fund_second_leg_alert_email_option_v1 = 2;
  }
}

message WaitlistIosWelcomeEmailOption {

  EmailType email_type = 1 [(validate.rules).enum.const = 24];

  oneof option {
    WaitlistIosWelcomeEmailOptionsV1 waitlist_ios_welcome_email_option_v1 = 2;
  }
}

message CboFeedbackEmailOption {

  EmailType email_type = 1 [(validate.rules).enum.const = 25];

  oneof option {
    CboFeedbackEmailOptionsV1 cbo_feedback_email_option_v1 = 2;
  }
}

message WorkEmailVerificationEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 26];

  oneof option {
    WorkEmailVerificationEmailOptionsV1 work_email_verification_email_option_v1 = 2;
    WorkEmailVerificationEmailOptionsV2 work_email_verification_email_option_v2 = 3;
  }
}

message WorkEmailVerificationEmailOptionsV1 {
  string otp = 1;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message WorkEmailVerificationEmailOptionsV2 {
  string otp = 1;
  // to be set as VERSION_V2
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 2];
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistIosAppAccessEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 27];

  oneof option {
    WaitlistIosAppAccessEmailOptionsV1 waitlist_ios_app_access_email_option_v1 = 2;
  }
}

message WaitlistIosAppAccessEmailOptionsV1 {
  // Name of the user.
  string name = 1;
  // App link.
  string app_link = 2;
  // finite code
  string finite_code = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 1];
}

// each template will have certain options which are the variables that will be filled in the template
message WaitlistCboIosAppAccessEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 28];

  oneof option {
    WaitlistCboIosAppAccessEmailOptionsV1 waitlist_cbo_ios_app_access_email_option_v1 = 2;
  }
}

message WaitlistCboIosAppAccessEmailOptionsV1 {
  // Name of the user.
  string name = 1;
  // App link.
  string app_link = 2;
  // finite code
  string finite_code = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 1];
}

message CxAutoResolutionEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 29];

  oneof option {
    CxAutoResolutionEmailOptionV1 cx_auto_resolution_email_option_v1 = 2;
  }
}

message CxAutoResolutionEmailOptionV1 {
  // Number of tickets processed
  int64 total_tickets_processed = 1;

  // Number of tickets updated on freshdesk
  int64 updated_ticket_count = 2;

  //Number of tickets resolved on freshdesk
  int64 resolved_count = 3;

  // current date
  string curr_date = 4;

  // to be set as VERSION_V1
  TemplateVersion template_version = 5 [(validate.rules).enum.const = 1];

  // product category: onboarding, cards, accounts, etc
  string product_category = 6;

  // email msg: optional msg to be send, like: check attachment, etc
  string email_msg = 7;
}

message DepositMaturingMinKycTMinusXEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 30];

  oneof option {
    DepositMaturingMinKycTMinusXEmailOptionV1 deposit_maturing_min_kyc_t_minus_x_email_option_v1 = 2;
  }
}

message DepositMaturingMinKycTMinusXEmailOptionV1 {
  // Name of the deposit account
  string deposit_name = 1;

  // Days before maturity
  // will be in string format with day/days as suffix based on number of days, i.e. 1 day, 7 days
  string x_days = 2;

  // type of deposit account, SMART DEPOSIT, FIXED DEPOSIT
  string deposit_type = 3;

  string maturity_date = 4;

  // to be set as VERSION_V1
  TemplateVersion template_version = 15 [(validate.rules).enum.const = 1];
}

message DepositMaturingMinKycTPlusXEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 31];

  oneof option {
    DepositMaturingMinKycTPlusXEmailOptionV1 deposit_maturing_min_kyc_t_plus_x_email_option_v1 = 2;
  }
}

message DepositMaturingMinKycTPlusXEmailOptionV1 {
  // Name of the deposit account
  string deposit_name = 1;

  // Days after maturity
  // will be in string format with day/days as suffix based on number of days, i.e. 1 day, 7 days
  string x_days = 2;

  // type of deposit account, SMART DEPOSIT, FIXED DEPOSIT
  string deposit_type = 3;

  // to be set as VERSION_V1
  TemplateVersion template_version = 15 [(validate.rules).enum.const = 1];
}

// template options to send cx bulk user details via email
message CxBulkUserDetailsEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 33];

  oneof option {
    CxBulkUserDetailsEmailOptionV1 cx_bulk_user_details_email_option_v1 = 2;
  }
}

message CxBulkUserDetailsEmailOptionV1 {
  // total Number of ids processed for fetching bulk user details
  int64 processed_ids_count = 1;

  // Number of ids failed while fetching the details
  int64 failed_ids_count = 2;

  // additional text like PFA attachments etc.
  string additional_msg = 3;

  // to be set as VERSION_V1
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 1];
}

// CxBulkAccountValidationsEmailOption is the template options to send external account validations via email
message CxBulkAccountValidationsEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 34];

  oneof option {
    CxBulkAccountValidationsEmailOptionV1 cx_bulk_account_validations_email_option_v1 = 2;
  }
}

message CxBulkAccountValidationsEmailOptionV1 {
  // total_validations is the number of verifications that were carried out
  int64 total_validations = 1;

  // successful_validations is the number of external account verifications that were successful
  int64 successful_validations = 2;

  // additional text like PFA attachments etc.
  string additional_msg = 3;

  // to be set as VERSION_V1
  TemplateVersion template_version = 4 [(validate.rules).enum.const = 1];
}

// PostAccountClosureFederalSheetOption is the template options to send email to user whose account closed and money transfer to another account by federal
message PostAccountClosureFundTransferEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 35];

  oneof option {
    PostAccountClosureFundTransferEmailOptionV1 post_account_closure_fund_transfer_email_option_v1 = 2;
  }
}

message PostAccountClosureFundTransferEmailOptionV1 {
  // first name of user
  string first_name = 1;

  // balance transfer to his account (in string since read from csv file)
  string balance_transfered = 2;

  // date of balance transfer
  string balance_transfered_date = 3;

  // utr number for user reference
  string utr_number = 4;

  // account number to which balance transfer
  string balance_transfered_account_number = 5;

  // to be set as VERSION_V1
  TemplateVersion template_version = 6 [(validate.rules).enum.const = 1];
}

// MinFullKydConversionEmailOption is the template options to inform user about min to full kyc conversion
message MinToFullKycConversionEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 36];

  oneof option {
    MinToFullKycConversionEmailOptionV1 min_to_full_kyc_conversion_email_option_v1 = 2;
  }
}

message MinToFullKycConversionEmailOptionV1 {
  // name of user
  string name = 1;

  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

// UserStuckInVkycReviewStateFederalEmail is the template options to inform user about min to full kyc conversion
message UserStuckInVkycReviewStateFederalEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 37];

  oneof option {
    UserStuckInVkycReviewStateFederalEmailOptionV1 user_stuck_in_vkyc_review_state_federal_email_option_v1 = 2;
  }
}

message UserStuckInVkycReviewStateFederalEmailOptionV1 {
  // to be set as VERSION_V1
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
}

message MinKycAccountClosureUserVerifiedEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 38];

  oneof option {
    MinKycAccountClosureUserVerifiedEmailOptionV1 min_kyc_account_closure_user_verified_mail_to_cx_v1 = 2;
  }
}

message MinKycAccountClosureUserVerifiedEmailOptionV1 {
  string current_date = 1;
  // additional text like PFA attachments etc.
  string additional_msg = 3;
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message IssueResolutionUserFeedbackEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 41];

  oneof option {
    IssueResolutionUserFeedbackEmailOptionV1 issue_resolution_user_feedback_email_option_v1 = 2;
  }
}

message IssueResolutionUserFeedbackEmailOptionV1 {
  // first name of the user
  string first_name = 1;
  // issue type: transaction, rewards, etc
  string issue_type = 2;
  // info on how many days issue will get resolved
  // keeping this is as string, since content may change for different issues
  string issue_resolution_eta_info = 3;
  // info on what will happen if yes button is clicked
  string yes_action_info = 4;
  // info on what will happen if no button is clicked
  string no_action_info = 5;
  // steps user has to perform to check the info
  string activity_steps = 6;
  // url which has to be hit when user clicks on yes button
  string yes_action_cta_url = 7;
  // url which has to be hit when user clicks on no button
  string no_action_cta_url = 8;
  // subject of the email
  // keeping it as string since it may differ for various issues
  string subject = 9;
  // to be set as VERSION_V1
  TemplateVersion template_version = 10 [(validate.rules).enum.const = 1];
}

message FederalMarkAccountsForFreezeEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 42];
  oneof option {
    FederalMarkAccountsForFreezeEmailOptionV1 federal_mark_accounts_for_freeze_email_option_v1 = 2;
  }
}

message FederalMarkAccountsForFreezeEmailOptionV1 {
  // Number of accounts for freeze action
  int64 total_accounts_for_freeze = 1;

  // date when mail is send
  string date = 2;

  // to be set as VERSION_V1
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 1];

  // email msg: optional msg to be send, like: check attachment, etc
  string email_msg = 4;
}

message FederalMarkAccountsForUnfreezeEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 43];
  oneof option {
    FederalMarkAccountsForUnfreezeEmailOptionV1 federal_mark_accounts_for_unfreeze_email_option_v1 = 2;
  }
}

message FederalMarkAccountsForUnfreezeEmailOptionV1 {
  // Number of accounts for unfreeze action
  int64 total_accounts_for_unfreeze = 1;

  // date when mail is send
  string date = 2;

  // to be set as VERSION_V1
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 1];

  // email msg: optional msg to be send, like: check attachment, etc
  string email_msg = 4;
}

message RiskOpsUserEmailOptionV1 {
  // first name of user
  string first_name = 1;

  // date when mail is send
  string date = 2;

  // to be set as VERSION_V1
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 1];

  // email msg: optional msg to be send, like: check attachment, etc
  string email_msg = 4;
}

message RiskOpsUserEmailOption {
  EmailType email_type = 1 [(validate.rules).enum = {in: [44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 57, 67, 68, 69, 70, 71, 72, 73, 74, 126, 128, 135]}];

  oneof option {
    RiskOpsUserEmailOptionV1 risk_ops_user_email_option_v1 = 2;
  }
}

message RiskAutoActionEmailOptionV1 {
  // to be set as VERSION_V1
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // first name of user
  string first_name = 2;

  // list of documents required from the user
  repeated string document_required_questions = 3;

  // information required from the user
  repeated string general_questions = 4;
}

message RiskAutoActionEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 82];

  oneof option {
    RiskAutoActionEmailOptionV1 risk_auto_action_email_option_v1 = 2;
  }
}

message RiskLEAEmailOptionV1 {
  // to be set as VERSION_V1
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // information provided to user
  // dynamic field
  repeated string info_fields = 4;
}

message RiskLEAEmailOptionV2 {
  // to be set as VERSION_V2
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];

  string complaint_details = 2;

  string lea_contact_info = 3;
}

message RiskLEAEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 85];

  oneof option {
    RiskLEAEmailOptionV1 risk_lea_email_option_v1 = 2;
    RiskLEAEmailOptionV2 risk_lea_email_option_v2 = 3;
  }
}

message RiskLEAContactInfoOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 96];

  oneof option {
    RiskLEAContactInfoOptionV1 lea_contact_info_option_v1 = 2;
  }
}

message RiskLEAContactInfoOptionV1 {
  // to be set as VERSION_V1
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  string complaint_details = 2;

  string lea_contact_info = 3;
}

message P2pReconFileEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 52];

  oneof option {
    P2pReconFileEmailOptionV1 p2p_recon_file_email_option_v1 = 2;
  }
}

message P2pReconFileEmailOptionV1 {
  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message SalaryProgramRegistrationCompletedEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 53];

  oneof option {
    SalaryProgramRegistrationCompletedEmailOptionV1 salary_program_registration_completed_email_option_v1 = 2;
  }
}

message SalaryProgramRegistrationCompletedEmailOptionV1 {
  // first name of the user
  string first_name = 1;

  // account name of user
  string account_holder_name = 2;

  // account number of user
  string account_number = 3;

  // ifsc code of user's account
  string ifsc_code = 4;

  // to be set as VERSION_V1
  TemplateVersion template_version = 5 [(validate.rules).enum.const = 1];
}

message UsStocksUserEmailOption {
  EmailType email_type = 1 [(validate.rules).enum = {in: [58, 59]}];

  oneof option {
    UsStocksUserEmailOptionV1 user_email_option_v1 = 2;
  }
}

message UsStocksUserEmailOptionV1 {
  // masked account number of user
  string masked_account_number = 1;
  // date of daily trade confirmation
  string statement_date = 2;
  // to be set as VERSION_V1
  TemplateVersion template_version = 3 [(validate.rules).enum.const = 1];
}

message MfTaxStatementEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 60];

  oneof option {
    MfTaxStatementEmailOptionV1 mf_tax_statement_email_option_v1 = 2;
  }

}

message MfTaxStatementEmailOptionV1 {
  // to be set as VERSION_V1
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // financial year for which we are sending the MfTaxStatement report
  string financial_year = 2;
}

message MfCapitalGainsEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 80];

  oneof option {
    MfCapitalGainsEmailOptionV1 mf_capital_gains_email_option_v1 = 2;
  }

}

message MfCapitalGainsEmailOptionV1 {
  // to be set as VERSION_V1
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // financial year for which we are sending the MfTaxStatement report
  string financial_year = 2;
}

message CxWatsonIncidentCreationEmailOption {
  EmailType email_type = 1 [(validate.rules).enum = {in: [61, 62, 76]}];

  oneof option {
    CxWatsonIncidentCreationEmailOptionV1 cx_watson_incident_creation_email_option_v1 = 2;
    CxWatsonIncidentCreationEmailOptionV2 cx_watson_incident_creation_email_option_v2 = 3;

  }
}

message MfOneTimeBuyOtpEmailOption {
  EmailType email_type = 1 [(validate.rules).enum = {in: [65]}];
  oneof option {
    MfOneTimeBuyOtpEmailOptionV1 mf_one_time_buy_otp_email_option_v1 = 2;
  }
}

message MfOneTimeBuyOtpEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string otp = 2;
  string fund_name = 3;
  api.typesv2.Money amount = 4;
}

message MfRegisterSIPOtpEmailOption {
  EmailType email_type = 1 [(validate.rules).enum = {in: [66]}];
  oneof option {
    MfRegisterSIPOtpEmailOptionV1 mf_register_sip_otp_email_option_v1 = 2;
  }
}

message MfRegisterSIPOtpEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string otp = 2;
  string fund_name = 3;
  api.typesv2.Money amount = 4;
}

message MfWithdrawalOtpEmailOption {
  EmailType email_type = 1 [(validate.rules).enum = {in: [88]}];
  oneof option {
    MfWithdrawalOtpEmailV1 mf_withdrawal_otp_email_v1 = 2;
  }
}

message MfWithdrawalOtpEmailV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string otp = 2;
}


message CxWatsonIncidentCreationEmailOptionV1 {
  // name of the user
  string name = 1;

  // issue faced by the user
  string issue_name = 2;

  // date and time when issue was identified by the client
  google.protobuf.Timestamp issue_identified_time = 3;

  // ticket id for the issue faced by user
  int64 ticket_id = 4;

  // expected date and time by which the issue will be resolved
  google.protobuf.Timestamp expected_resolution_time = 5;

  // subject of the email
  string subject = 6;

  // to set as VERSION_V1
  TemplateVersion template_version = 7 [(validate.rules).enum.const = 1];

  string url_for_answer_yes = 8;
}

message CxWatsonIncidentCreationEmailOptionV2 {
  // name of the user
  string name = 1;

  // custom message that should be mentioned in the email
  string msg = 2;

  // issue faced by the user
  string issue_name = 3;

  // ticket id for the issue faced by user
  int64 ticket_id = 4;

  // expected date and time by which the issue will be resolved
  google.protobuf.Timestamp expected_resolution_time = 5;

  // subject of the email
  string subject = 6;

  // to set as VERSION_V2
  TemplateVersion template_version = 7 [(validate.rules).enum.const = 2];

  string url_for_answer_yes = 8;
}

message CreditCardControlsEmailOptionV1 {
  // to set as VERSION_V1
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // card control event type for subject
  string card_control_event_type = 2;
  // last four digits of the card for subject.
  string last_four_digits = 3;
  string icon_url = 4;
  string heading = 5;
  string description = 6;
}

message CreditCardControlsEmailOptionV2 {
  // to set as VERSION_V2
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];

  // card control event type for subject
  string card_control_event_type = 2;
  // last four digits of the card for subject.
  string last_four_digits = 3;
  string icon_url = 4;
  string heading = 5;
  string description = 6;
  string box_title = 7;
  string box_description = 8;
  string bottom_description = 9;
  string collapse_box_description = 10;
  string box_color_class = 11;
  string additional_description = 12;
}

message CreditCardControlsEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 63];

  oneof option {
    CreditCardControlsEmailOptionV1 credit_card_controls_email_option_v1 = 2;
    CreditCardControlsEmailOptionV2 credit_card_controls_email_option_v2 = 3;
  }
}

message CreditCardTransactionStatusEmailOptionV1 {
  // to set as VERSION_V1
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];

  // card transaction event type for subject : atm/merchant txn success/declined
  string card_transaction_event_type = 2;
  // last four digits of the card for subject.
  string last_four_digits = 3;
  string icon_url = 4;
  string heading = 5;
  string description = 6;
  string box_title = 7;
  // Amount of txn is shown in the box
  google.type.Money txn_amount = 8;
  // green in success cases, red in failure cases.
  string background_color = 9;
  // txn can either be a merchant txn or atm txn
  oneof txn_type {
    string merchant = 10;
    string atm_outlet = 11;
  }
  google.protobuf.Timestamp txn_timestamp = 12;
  string masked_card_number = 13;
}

message CreditCardTransactionStatusEmailOptionV2 {
  // to set as VERSION_V2
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 2];

  // last four digits of the card for subject.
  string last_four_digits = 2;
  // card transaction event type for subject : atm/merchant txn success/declined
  string card_transaction_event_type = 3;
  string background_color = 4;
  string heading = 5;
  string description = 6;
  string box_title = 7;
  string box_description = 8;
  string icon_url = 9;
  string additional_description = 10;
  string box_color_class = 11;
  string collapse_box_description = 12;
  string collapse_item_description = 13;
  string table_row_details = 14;
}

message CreditCardTransactionStatusEmailOptionV3 {
  // to set as VERSION_V3
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 3];
  //  Email subject
  string email_subject = 2;
  // Name of the user
  string user_name = 3;
  // The detailed reason for failure of the transaction. This will be the first paragraph of the email
  // This includes the amount, masked card number, & card program
  string txn_failure_reason = 4;
  // This contains an explanation about what the user can do. Also will contain an action item link(if applicable)
  // This will be the second paragraph of the email
  string txn_failure_fix = 5;
}

message CreditCardTransactionStatusEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 64];
  oneof option {
    CreditCardTransactionStatusEmailOptionV1 credit_card_transaction_status_email_option_v1 = 2;
    CreditCardTransactionStatusEmailOptionV2 credit_card_transaction_status_email_option_v2 = 3;
    CreditCardTransactionStatusEmailOptionV3 credit_card_transaction_status_email_option_v3 = 4;

  }
}



message CreditCardStatementEmailOptionV1 {
  // to set as VERSION_V1
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // last four digits of the card for subject.
  string credit_card_last_four_digits = 2;
  google.type.Date statement_from_date = 3;
  google.type.Date statement_to_date = 4;
  google.type.Money total_due = 5;
  google.type.Money minimum_due = 6;
  int64 total_fi_coins_earned = 7;
  // link to navigate to pay cc bill on app
  string credit_card_pay_bill_url = 8;
  google.type.Date payment_due_date = 9;
  string credit_card_header_image = 10;
  string credit_card_image = 11;
}


message CreditCardStatementEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 77];
  oneof option {
    CreditCardStatementEmailOptionV1 credit_card_statement_email_option_v1 = 2;
  }
}

message CreditCardBillPaymentDueDateReminderEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 79];
  oneof option {
    ReminderEmailOptionV1 reminder_email_option_v1 = 2;
  }
}

message CategorySpendsExceededReminderEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 78];
  oneof option {
    ReminderEmailOptionV1 reminder_email_option_v1 = 2;
  }
}

message ReminderEmailOptionV1 {
  // to be set as VERSION_V1, needed to fetch from DB
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string amount = 2;
  string category = 3;
  string merchant = 4;
  google.protobuf.Timestamp date = 5;
  // denotes the link to be redirected to
  string deeplink = 6;
}

message CreditTxnEmailOptionV1 {
  // first_name of user
  string first_name = 1;
  // last four digit of user account
  string last_four_digit_of_account = 2;
  google.type.Money txn_amount = 3;
  string txn_date = 4;
  string sender_details = 5;
  // to be set as VERSION_V1
  TemplateVersion template_version = 6 [(validate.rules).enum.const = 1];
  // bank_name: name of the bank account in which money is credited
  string bank_name = 7;
  // account_type: type of account in which money is credited
  string account_type = 8;
}

message CreditTxnEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 81];
  oneof option {
    CreditTxnEmailOptionV1 credit_txn_email_option_v1 = 2;
  }

}

message CreditCardNotActivatedEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name customer_name = 2;
  int32 days_left_for_activation = 3;
  string activate_cc_url = 4;
  string cc_last_four_digits = 5;
  string open_fi_url = 6;
}

message CreditCardNotActivatedEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 83];
  oneof option {
    CreditCardNotActivatedEmailOptionV1 credit_card_not_activated_email_option_v1 = 2;
  }
}

message CreditCardClosureConfirmationEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name customer_name = 2;
  string cc_last_four_digits = 3;
}

message CreditCardClosureConfirmationEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 84];
  oneof option {
    CreditCardClosureConfirmationEmailOptionV1 credit_card_closure_confirmation_email_option_v1 = 2;
  }
}

message KycAgentWelcomeEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 86];
  oneof option {
    KycAgentWelcomeEmailOptionV1 kyc_agent_welcome_email_option_v1 = 2;
  }
}

message KycAgentWelcomeEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  api.typesv2.common.Name agent_name = 2;
  string agent_id = 3;
  string pin = 4;
}

message GenieLoginOTPEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 87];

  oneof option {
    GenieLoginOTPEmailOptionV1 genie_login_otp_email_option_v1 = 2;
  }
}

message GenieLoginOTPEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string otp = 2;
}

message DebitCardForexRefundEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 89];
  oneof option {
    DebitCardForexRefundEmailOptionV1 debit_card_forex_refund_email_option_v1 = 2;
  }
}

message CcKycComplianceStatusChangeEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 152];
  oneof option {
    CcKycComplianceStatusChangeEmailOptionV1 cc_kyc_compliance_status_change_email_option_v1 = 2;
  }
}

message CcKycComplianceStatusChangeEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string heading = 2;
  string description = 3;
  string icon_url = 4;
  string background_color = 5;
  string table_row_details = 6;
  string collapse_item_description = 7;
}


message PreCustomerCreationMinToFullKycConversionEmailOption {
  // type of template
  EmailType email_type = 1 [(validate.rules).enum.const = 108];

  oneof option {
    PreCustomerCreationMinToFullKycConversionEmailOptionV1 pre_customer_creation_min_to_full_kyc_conversion_email_option_v1 = 2;
  }
}

message PreCustomerCreationMinToFullKycConversionEmailOptionV1 {
  // name of user
  string name = 1;

  // to be set as VERSION_V1
  TemplateVersion template_version = 2 [(validate.rules).enum.const = 1];
}

message DebitCardForexRefundEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string heading = 2;
  string description = 3;
  string icon_url = 4;
  string background_color = 5;
  string table_row_details = 6;
  string collapse_item_description = 7;
}

message RiskOutcallAttemptReminderEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 125];
  oneof option {
    RiskOutcallAttemptReminderEmailOptionV1 risk_outcall_attempt_reminder_email_option_v1 = 2;
  }
}

message RiskOutcallAttemptReminderEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // user name
  api.typesv2.common.Name name = 2;
  // user phone number
  string masked_phone_number = 3;
  // Time window in which another outcall attempt will be made.
  string follow_up_window = 4;
  // outcall will be made with these verified numbers only.
  string verified_numbers = 5;
}

message DisputeStatusEmailOption1 {
  TemplateVersion template_version = 1;
  string first_name = 2;
  string dispute_case_number = 3;
}

message DisputeStatusEmailOption {
  EmailType email_type = 1 [(validate.rules).enum = {in: [100, 101, 102, 103, 104, 105]}];
  oneof option {
    DisputeStatusEmailOption1 dispute_status_email_options_v1 = 2;
  }
}

message CreditCardEligibleWebFlowEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 120];
  oneof option {
    CreditCardEligibleWebFlowEmailOptionV1 cc_eligible_web_flow_email_option_v1 = 2;
  }
}

message CreditCardEligibleWebFlowEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  string name = 2;
  string application_download_url = 3;
}

message RiskOutcallEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 125];
  oneof option {
    RiskOutcallEmailOptionV1 risk_outcall_email_option_v1 = 2;
  }
}

message RiskOutcallEmailOptionV1 {
  TemplateVersion template_version = 1 [(validate.rules).enum.const = 1];
  // user name
  string name = 2 [(validate.rules).string.min_len = 1];
  // Unique form url generated for user.
  string form_url = 3;
  // Expiry time of the outcall form
  string form_expiry = 4;
  // Subject will be modified if reminder email.
  bool is_reminder_email = 5;
}

message UssTaxDocumentEmailOption1 {
  TemplateVersion template_version = 1;
  // first name of user the documents are to be sent to
  string first_name = 2;
  // financial year in string e.g. 2023-204
  string financial_year_str = 3;
}

message UssTaxDocumentEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 140];
  oneof option {
    UssTaxDocumentEmailOption1 uss_tax_document_email_options_v1 = 2;
  }
}

message JumpYearlyStatementEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 142];
  oneof option {
    JumpYearlyStatementEmailOptionV1 jump_yearly_statement_email_option_v1 = 2;
  }
}

message JumpYearlyStatementEmailOptionV1 {
  TemplateVersion template_version = 1;
  // financial year in string e.g. 2023-204
  string financial_year_str = 2;
  // first name of user the documents are to be sent to
  string first_name = 3;
}

message MfExternalCapitalGainsEmail {
  EmailType email_type = 1 [(validate.rules).enum.const = 143];
  oneof option {
    MfExternalCapitalGainsEmailV1 mf_external_capital_gains_email_v1 = 2;
  }
}

message MfExternalCapitalGainsEmailV1 {
  TemplateVersion template_version = 1;
  // financial year in string e.g. 2023-204
  string financial_year_str = 2;
  // first name of user the documents are to be sent to
  string first_name = 3;
}

message RiskUnifiedLeaEmail {
  EmailType email_type = 1 [(validate.rules).enum.const = 145];
  oneof option {
    RiskUnifiedLeaEmailOption risk_unified_lea_email_v1 = 2;
  }
}

message RiskUnifiedLeaEmailOption {
  TemplateVersion template_version = 1;
  string first_name = 2;
  string account_restrictions = 3;
  string restriction_meaning = 4;
  string complaint_table_rows = 5;
  string reminder_text = 6;
}

message RiskUnifiedLEALayerInvestigationEmail {
  EmailType email_type = 1 [(validate.rules).enum.const = 148];
  oneof option {
    RiskUnifiedLEALayerInvestigationEmailOptionV1 risk_unified_lea_layer_investigation_email_option_v1 = 2;
  }

}

message RiskUnifiedLEALayerInvestigationEmailOptionV1 {
  TemplateVersion template_version = 1;
  string first_name = 2;
  string account_restrictions = 3;
  string restriction_meaning = 4;
  string complaint_list = 5;
  string reminder_text = 6;
}

message RiskopsUserMediumRiskCF {
  EmailType email_type = 1 [(validate.rules).enum.const = 146];
  oneof option {
    RiskopsUserMediumRiskCFOption riskops_user_medium_risk_cf_v1 = 2;
  }
}

message RiskopsUserMediumRiskCFOption {
  TemplateVersion template_version = 1;
  string first_name = 2;
  string form_url = 3;
}

message TieringRewardsSummaryEmailOption {
  // type of email template
  // to be set as TIERING_REWARDS_SUMMARY
  EmailType email_type = 1 [(validate.rules).enum.const = 150];
  oneof option {
    // tiering rewards summary option for tiering cashback summary : template v1
    TieringCashbackSummary tiering_rewards_summary_option_v1 = 2;
  }

  // tiering rewards summary option's v1 template used to show tiering cashback summary
  // figma : https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=5842-9794&t=IZ6Ca1BeYIriu4TH-0
  message TieringCashbackSummary {
    TemplateVersion template_version = 1;
    HeaderDetails header_details = 2;
    BodyDetails body_details = 3;
  }

  message HeaderDetails {
    string title = 1;
    string date = 2;
  }

  message BodyDetails {
    ProgressRingDetails progress_ring_details = 1;
    IconText title = 2;
    RewardDetails reward_details = 3;
    repeated IconText descriptions = 4;
    string cta_text = 5;
  }

  message RewardDetails {
    RewardDetail cash = 1;
    RewardDetail fi_coins = 2;
  }

  message RewardDetail {
    // should display this cell or not
    // Values :
    // 1. none : should not display the cell
    // 2. table-cell : should display the cell
    // Note : will display if empty
    string display_type = 1;
    IconText reward_type = 2;
    IconText reward_amount = 3;
    IconText reward_cap = 4;
  }

  message ProgressRingDetails {
    IconText title = 1;
    string progress = 2;
    string ring_colour = 3;
  }

  message IconText {
    string icon_url_identifier = 1;
    string text = 2;
  }
}


message CxTicketResolutionCsatEmailOption {
  EmailType email_type = 1 [(validate.rules).enum.const = 151];
  oneof option {
    CxTicketResolutionCsatEmailOptionVersion1 cx_ticket_resolution_csat_email_option_version1 = 2;
  }
}

message CxTicketResolutionCsatEmailOptionVersion1 {
  TemplateVersion template_version = 1;
  string first_name = 2;
  string ticket_id = 3;
  string csat_link = 4;
}

message EmailOption {
  // template options which will replace the variables in the template
  oneof option {
    WaitlistUserAcceptedEmailOption waitlist_user_accepted_email_option = 1;

    WaitlistUserRejectedEmailOption waitlist_user_rejected_email_option = 2;

    WaitlistUserCBOAcceptedEmailOption waitlist_user_cbo_accepted_email_option = 5;

    WaitlistUserCBORejectedEmailOption waitlist_user_cbo_rejected_email_option = 6;

    SherlockVerificationEmailOption sherlock_verification_email_option = 3;

    AccountStatementEmailOption account_statement_email_option = 4;

    InternalOfferRedemptionReportEmailOption internal_offer_redemption_report_email_option = 7;

    WaitlistUserAppAccessEmailOption waitlist_user_app_access_email_option = 8;

    WaitlistUserGmailInputEmailOption waitlist_user_gmail_input_email_option = 9;

    WaitlistUserFiniteCodeEmailOption waitlist_user_finite_code_email_option = 10;

    VKYCScheduleCallEmailOption vkyc_schedule_call_email_option = 11;

    VKYCScheduleCallReminderEmailOption vkyc_schedule_call_reminder_email_option = 12;

    WaitlistCboUserFiniteCodeEmailOption waitlist_cbo_user_finite_code_email_option = 13;

    MinKycUserWelcomeEmailOption min_kyc_user_welcome_email_option = 14;

    FullKycUserWelcomeEmailOption full_kyc_user_welcome_email_option = 15;

    WaitlistCboShortlistEmailOption waitlist_cbo_shortlist_email_option = 16;

    WaitlistExclusiveAccessFiniteCodeOption waitlist_exclusive_access_finite_code_option = 17;

    WaitlistCboIosShortlistEmailOption waitlist_cbo_ios_shortlist_email_option = 18;

    UNNameCheckEmailOption un_name_check_email_option = 19;

    FiniteCodeReminderEmailOption finite_code_reminder_email_option = 20;

    DirectAccessFiniteCodeEmailOption direct_access_finite_code_email_option = 21;

    CboUnknownDeviceFiniteCodeEmailOption cbo_unknown_device_finite_code_email_option = 22;

    AddFundsSecondLegAlertEmailOption add_funds_second_leg_alert_email_option = 23;

    WaitlistIosWelcomeEmailOption waitlist_ios_welcome_email_option = 24;

    CboFeedbackEmailOption cbo_feedback_email_option = 25;

    WorkEmailVerificationEmailOption work_email_verification_email_option = 26;

    WaitlistIosAppAccessEmailOption waitlist_ios_app_access_email_option = 27;

    WaitlistCboIosAppAccessEmailOption waitlist_cbo_ios_app_access_email_option = 28;

    CxAutoResolutionEmailOption cx_auto_resolution_email_option = 29;

    DepositMaturingMinKycTMinusXEmailOption deposit_maturity_min_kyc_t_minus_x_email_option = 30;

    DepositMaturingMinKycTPlusXEmailOption deposit_maturity_min_kyc_t_plus_x_email_option = 31;

    InternalFiniteCodeDumpShareOption internal_finite_code_dump_share_option = 32;

    CxBulkUserDetailsEmailOption cx_bulk_user_details_email_option = 33;

    CxBulkAccountValidationsEmailOption cx_bulk_account_validations_email_option = 34;

    PostAccountClosureFundTransferEmailOption post_account_closure_fund_transfer_email_option = 35;

    MinToFullKycConversionEmailOption min_to_full_kyc_conversion_email_option = 36;

    UserStuckInVkycReviewStateFederalEmailOption user_stuck_in_vkyc_review_state_federal_email_option = 37;

    MinKycAccountClosureUserVerifiedEmailOption min_kyc_account_closure_user_verified_cx_email_option = 38;

    InternalReportEmailOption internal_report_email_option = 39;

    WorkflowExecutionReportEmailOption workflow_execution_report_email_option = 40;

    IssueResolutionUserFeedbackEmailOption issue_resolution_user_feedback_email_option = 41;

    FederalMarkAccountsForFreezeEmailOption federal_mark_accounts_for_freeze_email_option = 42;

    FederalMarkAccountsForUnfreezeEmailOption federal_mark_accounts_for_unfreeze_email_option = 43;

    RiskOpsUserEmailOption risk_ops_user_email_option = 44;

    P2pReconFileEmailOption p2p_recon_file_email_option = 45;

    SalaryProgramRegistrationCompletedEmailOption salary_program_registration_completed_email_option = 46;

    UsStocksUserEmailOption us_stocks_user_email_option = 47;

    MfTaxStatementEmailOption mf_tax_statement_email_option = 48;

    CxWatsonIncidentCreationEmailOption cx_watson_incident_creation_email_option = 49;

    CreditCardControlsEmailOption credit_card_controls_email_option = 50;

    CreditCardTransactionStatusEmailOption credit_card_transaction_status_email_option = 51;

    MfOneTimeBuyOtpEmailOption mf_one_time_buy_otp_email_option = 52;

    MfRegisterSIPOtpEmailOption mf_register_sip_otp_email_option = 53;

    DepositStatementEmailOption deposit_statement_email_option = 54;

    CreditCardStatementEmailOption credit_card_statement_email_option = 55;

    CreditCardBillPaymentDueDateReminderEmailOption credit_card_bill_payment_due_date_reminder_email_option = 56;

    CategorySpendsExceededReminderEmailOption category_spends_exceeded_reminder_email_option = 57;

    MfCapitalGainsEmailOption mf_capital_gains_email_option = 58;

    CreditTxnEmailOption credit_txn_email_option = 59;

    RiskAutoActionEmailOption risk_auto_action_email_option = 60;

    CreditCardNotActivatedEmailOption credit_card_not_activated_email_option = 61;

    CreditCardClosureConfirmationEmailOption credit_card_closure_confirmation_email_option = 62;

    RiskLEAEmailOption risk_lea_email_option = 63;

    KycAgentWelcomeEmailOption kyc_agent_welcome_email_option = 64;

    GenieLoginOTPEmailOption genie_login_otp_email_option = 65;

    MfWithdrawalOtpEmailOption mf_withdrawal_otp_email_option = 66;

    DebitCardForexRefundEmailOption debit_card_forex_refund_email_option = 67;

    SalaryB2BLeadWorkEmailVerificationEmailOption salary_b_2b_lead_work_email_verification_email_option = 68;

    SalaryB2BLeadOTPVerifiedSlotNotBookedEmailOption salary_b_2b_lead_otp_verified_slot_not_booked_email_option = 69;

    SalaryB2BLeadOTPVerifiedSlotBookedEmailOption salary_b_2b_lead_otp_verified_slot_booked_email_option = 70;

    SalaryB2BLeadWorkEmailNotVerifiedEmailOption salary_b_2b_lead_work_email_not_verified_email_option = 71;

    JumpEligibilityCheckSuccessEmailOption jump_eligibility_check_success_email_option = 72;

    JumpInvestmentMaturingSoonEmailOption jump_investment_maturing_soon_email_option = 73;

    RiskLEAContactInfoOption risk_lea_contact_info_option = 74;

    JumpInvestmentRenewedEmailOption jump_investment_renewed_email_option = 75;

    JumpInvestmentPaidOutEmailOption jump_investment_paid_out_email_option = 76;

    RiskOutcallAttemptReminderEmailOption risk_outcall_attempt_reminder_email_option = 77;

    DisputeStatusEmailOption dispute_status_email_option = 78;

    WebMinKycClosedAccBalanceTransferEmailOption web_min_kyc_closed_acc_balance_transfer_email_option = 79;

    SavingsAccountSummaryEmailOption savings_account_summary_email_option = 80;

    PreCustomerCreationMinToFullKycConversionEmailOption pre_customer_creation_min_to_full_kyc_conversion_email_option = 81;

    JumpInvestmentDelayedEmailOption jump_investment_delayed_email_option = 82;

    JumpInvestmentSuccessEmailOption jump_investment_success_email_option = 83;

    JumpInvestmentFailedEmailOption jump_investment_failed_email_option = 84;

    JumpWithdrawalDelayedEmailOption jump_withdrawal_delayed_email_option = 85;

    JumpWithdrawalSuccessEmailOption jump_withdrawal_success_email_option = 86;

    JumpWithdrawalFailedEmailOption jump_withdrawal_failed_email_option = 87;

    LamfLoanAmountDisbursedEmailOption lamf_loan_amount_disbursed_email_option = 88;

    LamfLoanClosedEmailOption lamf_loan_closed_email_option = 89;

    LamfLoanEmiDueLowBalanceOption lamf_loan_emi_due_low_balance_option = 90;

    LamfLoanEmiOverdueOption lamf_loan_emi_overdue_option = 91;

    LoansPaymentFileEmailOption loans_payment_file_email_option = 92;

    CreditCardEligibleWebFlowEmailOption credit_card_eligible_web_flow_email_option = 93;

    // emails to be sent to the user just after respective card creation
    CreditCardWelcomeEmailOption credit_card_welcome_email_option = 94;

    SaClosureRequestEmailOption sa_closure_request_email_option = 95;

    ClubItcGreenPointsTransferRequestEmailOption club_itc_green_points_transfer_request_email_option = 96;

    // email to be sent to the user just after successful magnifi card creation
    MagnifiWelcomeEmailOption magnifi_welcome_email_option = 97;

    RiskOutcallEmailOption risk_outcall_email_option = 98;

    PushAppLogsEmailOption push_app_logs_email = 99;

    RiskOpsVKYCCreditFreezeEmailOption risk_ops_vkyc_credit_freeze_email_option = 101;

    CreditCardEmiCreationEmailOption credit_card_emi_creation_email_option = 102;
    CreditCardEmiCancellationEmailOption credit_card_emi_cancellation_email_option = 103;
    CreditCardEmiPreClosureEmailOption credit_card_emi_pre_closure_email_option = 104;

    DebitCardAmcChargesEmailOption debit_card_amc_charges_email_option = 105;
    CreditCardEmiClosureEmailOption credit_card_emi_closure_email_option = 106;

    LamfCxLoanApplicationClosureResolutionEmailOption lamf_cx_loan_application_closure_resolution_email_option = 107;
    LamfCxLoanAccountClosureResolutionEmailOption lamf_cx_loan_account_closure_resolution_email_option = 108;
    LamfCxLoanApplicationRetryResolutionEmailOption lamf_cx_loan_application_retry_resolution_email_option = 109;
    DebitCardAmcChargesReportEmail debit_card_amc_charges_report_email = 110;

    UssTaxDocumentEmailOption uss_tax_document_email_option = 111;
    EmailUpdateFederalEmailOption email_update_federal_email_option = 112;

    JumpYearlyStatementEmailOption jump_yearly_statement_email_option = 113;

    MfExternalCapitalGainsEmail mf_external_capital_gains_email = 114;

    DebitCardInternationalAtmWithdrawalLimitEmailOption debit_card_international_atm_withdrawal_limit_email_option = 115;

    RiskUnifiedLeaEmail risk_unified_lea_email = 116;

    StockguardianLoanApplicationEsignEmail stockguardian_loan_application_esign_email = 117;

    RiskopsUserMediumRiskCF riskops_user_medium_risk_cf = 118;

    RiskUnifiedLEALayerInvestigationEmail risk_unified_lea_layer_investigation_email = 119;

    LoanConfirmationEmailOption loan_confirmation_email_option = 120;

    TieringRewardsSummaryEmailOption tiering_rewards_summary_email_option = 121;

    CxTicketResolutionCsatEmailOption ticket_resolution_csat_email_option = 122;

    CcKycComplianceStatusChangeEmailOption cc_kyc_compliance_status_change_email_option = 123;
  }
}
