syntax = "proto3";

package rewards;

import "api/queue/consumer_headers.proto";
import "api/rewards/collected_data_type.proto";
import "api/rewards/reward_offer_type.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "google/type/postal_address.proto";

option go_package = "github.com/epifi/gamma/api/rewards";
option java_package = "com.github.epifi.gamma.api.rewards";


// proto for reward. Will be used to communicate between reward generator and processor and also in the APIs
message Reward {
  string id = 1;
  string ref_id = 2;
  // the secondary identifier of the action that generated the reward.
  // This allows us to generate multiple rewards for the same (actor_id, offer_id, ref_id) combination
  string secondary_ref_id = 28;
  string actor_id = 3;
  RewardStatus status = 4;
  // deprecated in favour of sub_status_v2
  string sub_status = 5 [deprecated = true];
  google.protobuf.Timestamp created_at = 6;
  RewardOptions reward_options = 7;
  RewardOption chosen_reward = 8;
  // if it's visible on the UI or not
  bool is_visible = 9;
  // payment reference like order id in case of cash reward
  string processing_ref = 10;
  // external_id is equivalent to reward id, but this external_id can be displayed
  // on the app while id shouldn't be due to security reasons.
  string external_id = 11;
  // display related properties for reward.
  RewardDisplay reward_display = 12;
  // rewardOfferId which is responsible for this reward
  string offer_id = 13;
  // type of reward offer which is responsible for this reward.
  RewardOfferType offer_type = 14;
  // to link reward with an external ref/entity e.g. finiteCodeClaimId for referral rewards.
  string external_ref = 15;
  // reason because of which the reward was clawed back. it will be CLAW_BACK_REASON_UNSPECIFIED unless reward has been moved to CLAWED_BACK state
  ClawbackReason clawback_reason = 16;
  // reference ID of event which lead to claw back of reward. it will be empty unless reward is clawed-back
  string clawback_ref_id = 17;
  // tags for rewards.
  // current use case is limited to display. The scope can be extended later, for e.g. filtering
  repeated RewardTag tags = 18;
  // type of event that was collected that lead to this reward being generated
  rewards.CollectedDataType action_type = 20;
  // reward specific metadata. can include any additional information related to the generated reward
  RewardMetadata reward_metadata = 21;
  // denotes how the reward will be claimed post generation
  ClaimType claim_type = 22;
  // denotes additional information related to the current status of the reward
  SubStatus sub_status_v2 = 23;
  // time at which the reward generating action occurred
  google.protobuf.Timestamp action_time = 24;
  google.protobuf.Timestamp updated_at = 25;
  google.protobuf.Timestamp deleted_at = 26;
  // denotes the time at which the reward will expire, is null then the reward will not auto expire
  google.protobuf.Timestamp expires_at = 27;
}

// Types of Rewards
// Todo : Update RewardOptionSchema in epiview/src/main/scala/com.epifi.dataplatform.epiview/commons/rewards/RewardsConstants.scala whenever ths is updated accordingly
enum RewardType {
  reserved 12;
  REWARD_TYPE_UNSPECIFIED = 0;
  // NO_REWARD denotes no reward was given.
  // used for cases where no reward was given.
  // eg : in case of a lucky draw.
  NO_REWARD = 1;
  CASH = 2;
  FI_COINS = 3;
  LUCKY_DRAW = 4;
  SMART_DEPOSIT = 5;
  GIFT_HAMPER = 6;
  METAL_CREDIT_CARD = 7;
  EGV_BASKET = 8;
  THRIWE_BENEFITS_PACKAGE = 9;
  US_STOCK = 10;
  CREDIT_CARD_BILL_ERASER = 11;
}

// Status of rewards
enum RewardStatus {
  // this status will be used to create reward but process it on-demand when user asks to redeem
  CREATED = 0;
  // this status will be used to create reward and auto process it without asking the user to redeem it
  PROCESSING_PENDING = 1;
  // this status will be used when reward redemption is pending at dependent system side
  // like in case of cash reward, if the payment is pending at bank side
  PROCESSING_IN_PROGRESS = 2;
  // for successfully processing the reward
  PROCESSED = 3;
  // for if processing is unsuccessful
  PROCESSING_FAILED = 4;
  // for reward failures like frozen account, min kyc account, etc which need manual intervention
  PROCESSING_MANUAL_INTERVENTION = 5;
  // for when a generated reward is taken back. it's a terminal state.
  CLAWED_BACK = 6;
  // for when the reward is generated but LOCKED, and can't be fulfilled
  LOCKED = 7;
  // for when the actor's account closed & reward didn't processed yet.
  EXPIRED = 8;
}

// TODO : Update ValidRewardSubStatus in data-platform whenever SubStatus is updated : src/main/scala/com/epifi/dataplatform/epiview/commons/rewards/RewardsConstants.scala
enum SubStatus {
  SUB_STATUS_UNSPECIFIED = 0;

  // sub statuses for `LOCKED` status
  // `SUB_STATUS_IMPLICITLY_LOCKED` means that implicit (code level) locking is in effect.
  // eg. when a reward is locked as user is a Min KYC or Fi-Lite user
  SUB_STATUS_IMPLICITLY_LOCKED = 1;
  // `SUB_STATUS_EXPLICITLY_LOCKED` means that explicit locking (configured for reward offer) is in effect
  // eg. when a reward is locked on tier update event.
  SUB_STATUS_EXPLICITLY_LOCKED = 2;
  // sub status for 'EXPIRY' status
  // eg. when user's account has been closed completely then we update to expiry state.
  SUB_STATUS_ACCOUNT_CLOSURE_EXPIRY = 3;

  // sub status when the fi-coins have expired
  SUB_STATUS_FI_COINS_EXPIRED = 4;

  // sub statuses for `PROCESSED` status (intentional gap in field numbers as these statuses)
  SUB_STATUS_CREDITED_FI_COINS = 100;
  SUB_STATUS_REGISTERED_FOR_LUCKY_DRAW = 101;

  // sub statuses for `MANUAL_INTERVENTION` status
  SUB_STATUS_TRANSACTION_NOT_ALLOWED = 102;
}

enum ClawbackReason {
  CLAWBACK_REASON_UNSPECIFIED = 0;
  // for cases when a reward is clawed back if the transaction for which the reward was generated was reversed.
  // e.g. credit card refunds
  TXN_REVERSAL = 1;
}

// defines the theme for displaying reward tile on APP.
// Based on theme, the APP decides the images to show on reward tile and corresponding animation to
// play while claiming the reward. If RewardTileThemeType is THEME_TYPE_UNSPECIFIED, the APP fallbacks
// to a default theme.
enum RewardTileThemeType {
  REWARD_TILE_THEME_UNSPECIFIED = 0;
  PLANT_THEME_CHRYSANTHEMUM = 1;
  PLANT_THEME_ORCHID = 2;
}

// RewardDisplay stores display related properties for reward.
message RewardDisplay {
  // background image to display on reward tile when reward is in unclaimed state.
  string tile_bg_image_before_claim = 1;
  // background image to display on reward tile after reward is claimed.
  string tile_bg_image_after_claim = 2;
  // theme for displaying reward tile on APP. Based on theme, the APP decides the images to show on reward tile
  // and corresponding animation to play while claiming the reward
  RewardTileThemeType reward_tile_theme_type = 3;
  // flag to decide whether we want the flower animation to be skippable or not. it's named this way to keep the default
  // value as 'false' and only make animation un-skippable if offer is configured that way
  bool is_animation_unskippable = 4;
  // flag to decide whether we'd want to skip the whole animation altogether
  // use case: in case booster is applied, we'd want to skip animation to give time for further frames
  bool skip_animation = 5;
  // Determines how fast the money plant animation should run
  // note: The expected default value is 1.5, 1.0 for normal speed, 1.5 for faster, 0.5 for slower and so on
  float animation_speed = 6;
}

// for Cash reward
message Cash {
  google.type.Money amount = 1;
  // remark to be used for displaying with cash txn.
  // if txn_remark is not specified a default remark is used.
  string txn_remark = 2;
}

// for Fi Coins reward
message FiCoins {
  uint32 units = 1;
  google.protobuf.Timestamp expires_at = 2;
}

// for Lucky Draw reward
message LuckyDraw {
  string lucky_draw_id = 1;
}

// for NO_Reward rewardType
message NoReward {
}

// for Metal credit card rewardType
message MetalCreditCard {
}

// for EGV basket rewardType
message EgvBasket {
  // ids of offers configured in offers service, which need to be given as reward
  repeated string egv_offer_ids = 1;
}

message ThriweBenefitsPackage {
  // denotes the id of offer configured in the offer service which would be used for fulfilling thriwe benefits package reward.
  string offer_id = 1;
}

// for CC bill eraser reward
message CreditCardBillEraser {
  // amount to be added to user's CC existing limit
  google.type.Money amount = 1;
}

// for SmartDeposit Reward
message SmartDeposit {
  // amount to be deposited in SD
  google.type.Money amount = 1;
  // SD maturity date.
  // maturity date will be used only for new reward SD.
  google.protobuf.Timestamp maturity_date = 2;
  // used to calculate maturity date, if
  // maturity date is already not calculated
  RewardTimeConfig maturity_date_config = 4;
  // nominee details are required to create a new SD
  repeated DepositNomineeInfo nominee_info_list = 5;

  // deposit name
  string name = 6;
}

// nominee info for creating a new SD as part of reward
message DepositNomineeInfo {
  string nominee_id = 1;
  string percentage_share = 2;
}

// for GiftHamper reward
message GiftHamper {
  // name of vendor who will provide the gift hamper
  string vendor_name = 1;
  // vendor specific product identifier
  string vendor_product_id = 2;
  // address where hamper is to be shipped
  google.type.PostalAddress shipping_address = 3;
  // name of product like "Monsoon Harvest Hamper"
  string product_name = 4;
}

message RewardEvent {
  queue.ConsumerRequestHeader request_header = 1;
  Reward reward = 2;

  // if true, suppresses all notifications related to reward processing
  bool should_suppress_notifications = 3;
}

message RewardTimeConfig {
  oneof config {
    google.protobuf.Timestamp absolute_time = 1;
    uint32 relative_time_in_minutes = 2;
  }

  // intentional gap in field numbers to accommodate new fields in one-of config
  // if this flag is set to true then the computed time using the above one-of config should be rounded up to the start of the next day in IST timezone.
  bool round_up_to_start_of_next_day = 5;
}

message RewardOptionDisplay {
  // Deprecated : use before_claim_title and after_claim_title
  string title = 1;
  string icon = 2;
  string bg_color = 3;
  string before_claim_title = 4;
  string after_claim_title = 5;
  // html formatted details
  // e.g for a Gift Hamper Reward, a detail entry can look like
  // {
  //  header : "Description"
  //  body :"<b>Gift Box Contains :</b> <br>
  //  1 Toasted Millet Muesli <br>
  //  1 Choco chip oat clusters and Ragi flakes with banana <br>
  //  1 Box of 12 Crunchy granola bars - dark chocolate & espresso."
  // }
  message HtmlFormattedDetail {
    string header = 1;
    string body = 2;
  }
  repeated HtmlFormattedDetail html_formatted_details = 6;
  // banner text displayed on top of the reward option card at the time of choosing the option e.g. "10% EXTRA REWARD"
  // Design : https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=5953%3A65408
  // can be empty if banner text does not needs to be displayed.
  string before_claim_banner_text = 7;
  // tags for reward to be propagated eventually.
  // current use case: for display purpose once the option is chosen.
  repeated RewardTag tags = 8;
}

message RewardOption {
  string id = 1;
  RewardOptionDisplay display = 2;
  google.protobuf.Timestamp processing_date = 3;
  RewardTimeConfig reward_processing_time_config = 4;
  // type of reward namely Cash, FiCoins etc.
  RewardType reward_type = 5;
  // to store the individual (ranging from base, intermediate, final) reward units calculation entries.
  // source of calculations currently include:
  // 1. default config, i.e. base config
  // 2. booster configs
  RewardUnitsCalculationInfo reward_units_calculation_info = 6;
  // current usage includes specific handling of display details of reward option. it's an optional field and can be used for the following reward types if we want to distinguish between options:
  // 1. gift_hamper
  // 2. egv_basket
  string product_sku = 7;
  // intentional gap to accommodate new fields in future
  // TODO : Update UnitRewardTypes or RewardOptionSchema in data-platform whenever option is updated : src/main/scala/com/epifi/dataplatform/epiview/commons/rewards/RewardsConstants.scala
  oneof option {
    Cash cash = 11;
    FiCoins fi_coins = 12;
    LuckyDraw lucky_draw = 13;
    SmartDeposit smart_deposit = 14;
    GiftHamper gift_hamper = 15;
    MetalCreditCard metal_credit_card = 16;
    EgvBasket egv_basket = 17;
    // intentional gap in field number to accomodate new reward types
    NoReward no_reward = 20;
    ThriweBenefitsPackage thriwe_benefits_package = 21;
    USStockReward usstock_reward = 22;
    CreditCardBillEraser credit_card_bill_eraser = 23;
  }
}

message RewardUnitsCalculationInfo {
  // storing the results of reward calculations
  // each entry stores the intermediate reward value (and not the delta)
  repeated RewardUnitsCalculationEntry reward_units_calculation_entries = 1;

  // to decide how the claim flow would reveal the reward values for each calculation
  ClaimFlowRewardValueRevealType claim_flow_reward_value_reveal_type = 2;

  message RewardUnitsCalculationEntry {
    // todo: any id needed

    float reward_value = 1;

    DisplayDetails display_details = 2;

    message DisplayDetails {
      // tags associated with the calculation entry
      repeated RewardTag tags = 1;
      // title for the calculation entry, for e.g. "2x boost applied for Fi Plus accounts"
      string title = 2;
      // title color
      string title_color = 3;
      // bg color for displaying booster details
      string bg_color = 4;
      // note: not adding icon field as that will be inferred from the reward-type, i.e. used from the RewardOptionDisplay
      // of the associated RewardOption
    }
  }
}


enum ClaimFlowRewardValueRevealType {
  CLAIM_FLOW_REVEAL_TYPE_UNSPECIFIED = 0;
  // intermediate reward values (not the delta) ending with the final value
  CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_INTERMEDIATE_VALUES = 1;
  // only the base reward value will be shown for each reveal. The final value will be shown in the end of claim flow
  CLAIM_FLOW_REWARD_VALUE_REVEAL_TYPE_BASE_VALUE = 2;
}

// RewardTag contains tags which can be used for purposes such as display, filtering etc. The scope can be extended/limited later.
// For e.g., (display purpose) if ACCOUNT_TIER_2 is present, then we will show the tag on the reward tile.
enum RewardTag {
  REWARD_DISPLAY_TAG_UNSPECIFIED = 0;

  TIER_FI_BASIC = 1;
  TIER_FI_PLUS = 2;
  TIER_FI_INFINITE = 3;

  SALARY_PROGRAM = 4;

  CREDIT_CARD = 5;

  // booster/multiplier specific
  BOOSTER_1X = 6;
  BOOSTER_2X = 7;
  BOOSTER_3X = 8;
  BOOSTER_10_PERCENT = 9;
  BOOSTER_15_PERCENT = 10;
  BOOSTER_20_PERCENT = 11;
  BOOSTER_50_PERCENT = 12;
  BOOSTER_100_PERCENT = 13;


  CREDIT_CARD_SPENDS_2X_BOOSTER = 14;
  CREDIT_CARD_SPENDS_5X_BOOSTER = 15;
}

message RewardOptions {
  uint32 default_decide_time_in_secs = 1;
  google.protobuf.Timestamp unlock_date = 2;
  repeated RewardOption options = 3;
  string action_details = 4;
  // bool to decide whether reward should be unlocked for MinKYC users or not
  bool is_unlocked_for_min_kyc = 5 [deprecated = true];
  // time after which the reward's default option should be auto-claimed,
  // will be null if reward shouldn't be auto-claimed.
  google.protobuf.Timestamp auto_claim_time = 6;
  // bool to decide whether implicit locking is enabled for this reward or not
  bool is_implicit_locking_disabled = 7;
}

// metadata required to claim reward eg : nominee details for SD.
message RewardClaimMetadata {
  // useful for physical rewards like gift hampers.
  // not added to reward_type_specific_data as this field can be used for multiple reward api.typesv2.
  google.type.PostalAddress shipping_address = 1;

  // SDMetadata contains metadata specific to SD Reward.
  message SDMetadata {
    // todo (utkarsh) : remove it
    string nominee_details = 1;
    // nominee info for creating a new SD as part of reward
    repeated DepositNomineeInfo nominee_info_list = 2;

    string sd_name = 3;
  }
  oneof reward_type_specific_data {
    SDMetadata sd_metadata = 6;
  }
}

// any metadata related to the generated reward
message RewardMetadata {
  RewardOfferTypeSpecificMetadata offer_type_specific_metadata = 1;
  ProjectionMetadata projection_metadata = 2;
  // metadata related to the reward offer. for ex. name of top merchants for which the 5x reward was generated
  message RewardOfferTypeSpecificMetadata {
    oneof metadata {
      CcTopMerchantsSpendsRewardMetadata cc_top_merchants_spends_reward_metadata = 1;
      CcCuratedMerchantsSpendsRewardMetadata cc_curated_merchants_spends_reward_metadata = 2;
    }

    message CcTopMerchantsSpendsRewardMetadata {
      // value of reward multiplier that was used to calculate top merchant spends reward from 1x reward
      float applied_reward_multiplier = 1;
      // map of top merchant name to aggregated (in a specified duration based on reward construct) 1x reward amount that was given on txns to that merchant.
      // This aggregated 1x reward amount on top merchants was used to calculate the reward amount for cc top merchant spends reward.
      map<string, float> top_merchant_name_to_1x_reward_amount = 2;
    }
    message CcCuratedMerchantsSpendsRewardMetadata {
      // map of curated merchant name to aggregated (in a specified duration based on reward construct) 1x reward amount that was given on txns to that merchant.
      // This aggregated 1x reward amount on merchants was used to calculate the reward amount for cc curated merchants spends reward.
      map<string, float> curated_merchant_name_to_1x_reward_amount = 2;
    }
  }

  // ProjectionMetadata contains metadata related to the reward projections if the reward was generated against projections
  message ProjectionMetadata {
    // flag to denote if all projections against which this reward was generated are updated with the reward specific details or not
    // this is updated for all the rewards which are generated against projections
    bool are_all_projections_updated = 1;
  }
}

enum ClaimType {
  CLAIM_TYPE_UNSPECIFIED = 0;
  // denotes that the reward will be required to be claimed by the user on the app
  CLAIM_TYPE_MANUAL = 1;
  // denotes that the reward will be auto-claimed without need of user to claim the reward
  CLAIM_TYPE_AUTOMATIC = 2;
}

// RewardOptionMinimal contains just the reward type and reward units associated with a reward option.
// it can be used in situations where we don't need all the details related to a reward option (like display details)
// and just the reward type and units matter.
message RewardOptionMinimal {
  // type of reward for this option
  RewardType reward_type = 1;
  // number of units of given type
  float units = 2;
}
message USStockReward {
  // represent the stock id which is being given as reward
  string stock_id = 1;
  // represent the amount worth of stock given to user
  google.type.Money amount = 2;
}


