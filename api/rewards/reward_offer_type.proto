syntax = "proto3";

package rewards;

option go_package = "github.com/epifi/gamma/api/rewards";
option java_package = "com.github.epifi.gamma.api.rewards";

// Not added in api/rewards/rewardOffers as it was creating a dependency cycle
// between api/rewards/rewardoffers and api/rewards pkg in the auto generated GO code.
// RewardOfferType denotes type of reward offer.
enum RewardOfferType {
  UNSPECIFIED_REWARD_OFFER_TYPE = 0;
  // denotes a referral reward offer for referrer
  REFERRAL_REFERRER_OFFER = 1;
  // denotes a referral reward offer for referee
  REFERRAL_REFEREE_OFFER = 2;
  // denotes a reward offer for extra interest SD bonus payout
  EXTRA_INTEREST_SD_BONUS_PAYOUT_OFFER = 3;
  // denotes a reward offer for salary program
  SALARY_PROGRAM_OFFER = 4;
  // denotes a referral reward offer for referrer
  // used in cases where we need to give additional special reward to referrer for referring a referee.
  REFERRAL_REFERRER_SPECIAL_OFFER = 5;
  // denotes a referral reward offer for referee
  // used in cases where we need to give additional special reward to referee
  // it allows one referral reward per offer for a user unlike REFERRAL_REFEREE_OFFER which allows one reward globally for a user.
  REFERRAL_REFEREE_SPECIAL_OFFER = 30;
  // denotes a reward offer for giving 1x rewards on merchant spends using credit card.
  CREDIT_CARD_SPENDS_1X_OFFER = 6;
  // denotes a reward offer for giving rewards on top merchant spends (in a billing/monthly cycle) using credit card.
  CREDIT_CARD_TOP_MERCHANTS_SPENDS_OFFER = 7;
  // denotes a salary program referral reward offer for referrer as salary program is activated for referee
  SALARY_PROGRAM_INAPP_REFERRAL_REFERRER_OFFER = 8;
  // denotes a salary program referral reward offer for referee as salary program is activated for referee
  SALARY_PROGRAM_INAPP_REFERRAL_REFEREE_OFFER = 9;
  // denotes a welcome reward offer for credit card users
  CREDIT_CARD_WELCOME_OFFER = 10;
  // denotes a reward offer for giving an additional (on top of base 1x reward) reward for credit card spends on a curated set of merchants,
  // current construct is to give additional 1x reward on credit card spends done on a curated set of merchants in a billing cycle.
  CREDIT_CARD_CURATED_MERCHANTS_SPENDS_OFFER = 11;
  // denotes a reward offer for giving quarterly lounge access to credit card users on some qualifying action.
  CREDIT_CARD_QUARTERLY_AIRPORT_LOUNGE_OFFER = 12;
  // denotes an offer for giving rewards to users on pulling credit report through our app
  CREDIT_REPORT_DOWNLOAD_OFFER = 13;
  // denotes an offer for generating rewards on weekdays for secured CC
  SECURED_CREDIT_CARD_WEEKDAYS_OFFER = 14;
  // denotes an offer for generating rewards on weekdays for secured weekends
  SECURED_CREDIT_CARD_WEEKEND_OFFER = 15;
  // denotes a reward given to retain user's investment
  INVESTMENT_RETENTION_REWARD = 16;
  // denotes an offer for generating 1X rewards on AMPLIFI credit card on all spends other than done to a list of merchants
  UNSECURED_CREDIT_CARD_BASE_OFFER = 17;
  // denotes an offer for generating rewards on AMPLIFI credit card on all spends done to a list of curated merchants
  UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_BASE_OFFER = 18;
  // denotes an offer for generating rewards on AMPLIFI credit card on all spends done to a list of curated merchants
  UNSECURED_CREDIT_CARD_CURATED_MERCHANTS_ACCELERATED_OFFER = 19;
  // denotes mass unsecured credit card base reward offer
  MASS_UNSECURED_CREDIT_CARD_BASE_OFFER = 20;
  // denotes mass unsecured credit card accelerated reward offer
  MASS_UNSECURED_CREDIT_CARD_ACCELERATED_OFFER = 21;
  // reward which is being trigger during activation of usstocks
  // this is one time offer
  USSTOCKS_ACTIVATION_REWARD_OFFER = 22;
  // denotes an offer for generating first milestone reward for customers owning amplifi credit card for 1 year
  UNSECURED_CREDIT_CARD_MILESTONE_1_OFFER = 23;
  // denotes an offer for generating second milestone reward for customers owning amplifi credit card for 1 year
  UNSECURED_CREDIT_CARD_MILESTONE_2_OFFER = 24;
  // denotes an offer for generating reward for renewing amplifi credit card
  UNSECURED_CREDIT_CARD_RENEWAL_OFFER = 25;

  // tiering cashback reward offers
  // ordered according to the internal tier ranking

  // rewards earned for being in the plus tier
  PLUS_TIER_1_PERCENT_CASHBACK_OFFER = 35;
  // rewards earned for being in the infinite tier
  INFINITE_TIER_2_PERCENT_CASHBACK_OFFER = 36;
  // rewards earned for being in the aa salary tier band 1
  AA_SALARY_BAND_1_TIER_1_PERCENT_CASHBACK_OFFER = 27;
  // rewards earned for being in the aa salary tier band 2
  AA_SALARY_BAND_2_TIER_2_PERCENT_CASHBACK_OFFER = 28;
  // rewards earned for being in the aa salary tier band 3
  AA_SALARY_BAND_3_TIER_3_PERCENT_CASHBACK_OFFER = 29;
  // rewards earned for being in the salary lite tier
  SALARY_LITE_TIER_1_PERCENT_CASHBACK_OFFER = 26;
  // rewards earned for being in the salary tier
  SALARY_TIER_2_PERCENT_CASHBACK_OFFER = 37;
  // rewards earned for being in the infinite/salary tier
  INFINITE_OR_SALARY_TIER_2_PERCENT_CASHBACK_OFFER = 34;
  // rewards earned for being in the salary basic tier
  SALARY_BASIC_TIER_1_PERCENT_CASHBACK_OFFER = 38;

  // rewards earned for connecting epf account on fi
  EPF_PASSBOOK_IMPORT_OFFER = 31;
  // reward earned for connecting syncing account on fi
  CONNECTED_ACCOUNT_DATA_SYNC_OFFER = 32;
  // reward earned for connecting account on fi
  CONNECTED_ACCOUNT_DATA_CONNECT_OFFER = 33;
  // rewards earned for magnifi cvp
  MASS_UNSECURED_CREDIT_CARD_CVP_OFFER = 39;
}
