// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package datacollector;

import "api/bigdata/actor_profile.proto";
import "api/card/notification/service.proto";
import "api/casper/events/offer_redemption_status_event_event.proto";
import "api/connected_account/external/external.proto";
import "api/creditreportv2/notification/credit_report_download_event.proto";
import "api/firefly/accounting/consumer/service.proto";
import "api/firefly/billing/events/bill_generation_event.proto";
import "api/firefly/event/payload.proto";
import "api/fittt/action/action_execution.proto";
import "api/fittt/sports/events/events.proto";
import "api/investment/aggregator/events/events.proto";
import "api/order/order.proto";
import "api/queue/consumer_headers.proto";
import "api/rewards/collected_data_type.proto";
import "api/rewards/reward.proto";
import "api/salaryprogram/events/salary_detection_event.proto";
import "api/salaryprogram/events/salary_status_update_event.proto";
import "api/savings/savings_producer.proto";
import "api/search/events/rewards_event.proto";
import "api/tiering/external/tier_update_event.proto";
import "api/user/kyc_event.proto";
import "api/user/onboarding/notification.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "api/insights/epf/events.proto";
import "api/nudge/consumer/service.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/rewards/datacollector";
option java_package = "com.github.epifi.gamma.api.rewards.datacollector";

// Proto to communicate between reward collector and generator.
message CollectedData {
  queue.ConsumerRequestHeader request_header = 1;
  string id = 2;
  rewards.CollectedDataType data_type = 3;
  string actor_id = 4;
  // event type specific data json bytes
  bytes data_bytes = 5;
  // deprecated in favour of creation_time
  string created_at = 6 [deprecated = true];
  oneof data {
    order.OrderUpdate order_update_event = 7;
    search.events.UserSearchEvent user_search_event = 8;
    ManualGiveawayEvent manual_giveaway_event = 9;
    api.fittt.action.ActionExecutionUpdate fittt_action_exec_update_event = 10;
    ExtraInterestSdBonusPayoutEvent extra_interest_sd_bonus_payout_event = 11;
    MinBalanceEvent min_balance_event = 12;
    connected_account.external.AccountUpdateEvent ca_account_update_event = 13;
    user.KycEvent kyc_event = 14;
    api.fittt.sports.events.SportsEvent fittt_sports_event = 16;
    savings.AccountStateUpdateEvent savings_account_update_event = 17;
    salaryprogram.events.SalaryDetectionEvent salary_detection_event = 18;
    user.onboarding.OnboardingStageUpdate onboarding_stage_update_event = 19;
    RewardsCreditCardTxnEvent rewards_credit_card_txn_event = 20;
    firefly.billing.events.CreditCardBillGenerationEvent credit_card_billing_event = 21;
    salaryprogram.events.SalaryProgramStatusUpdateEvent salary_program_status_update_event = 22;
    firefly.event.CreditCardRequestStageUpdateEvent credit_card_request_stage_update_event = 23;
    creditreportv2.notification.CreditReportDownloadEvent credit_report_download_event = 24;
    casper.events.OfferRedemptionStatusUpdateEvent offer_redemption_status_update_event = 25;
    UnlockRewardEvent unlock_reward_event = 26;
    api.investment.aggregator.events.InvestmentEvent investment_event = 28;
    TieringPeriodicRewardEvent tiering_periodic_reward_event = 29;
    tiering.external.TierUpdateEvent tiering_tier_update_event = 30;
    card.notification.ProcessCardSwitchFinancialNotificationsRequest dc_card_switch_financial_notification_event = 31;
    insights.epf.EpfPassbookImportEvent epf_passbook_import_event = 32;
    connected_account.external.AccountDataSyncEvent ca_account_data_sync_event = 33;
    nudge.consumer.ActorNudgeStatusUpdateEvent actor_nudge_status_update_event = 34;
    VendorRewardFulfillmentEvent vendor_reward_fulfillment_event = 35;
  }
  // user action time associated with the event for which collected data is created.
  // For eg for SEARCH type collected data, action_time would be equal to the search timestamp.
  google.protobuf.Timestamp action_time = 15;
  // denotes the time at which collected data was created.
  google.protobuf.Timestamp creation_time = 27;
}

// ManualGiveawayEvent is triggered using dev action and is used for manually giveaway reward to a user.
message ManualGiveawayEvent {
  // common request header across all the consumer grpc services.
  queue.ConsumerRequestHeader request_header = 1;
  // actor for whom the reward needs to be triggered.
  string actor_id = 2;
  string event_id = 3;
  google.protobuf.Timestamp event_timestamp = 4;
  // denotes the reward_offer that should be used for triggering the giveaway reward.
  // this offer should be preconfigured in the system for MANUAL_GIVEAWAY action.
  string reward_offer_id = 5;
  // optional field, useful for specifying the reward amount to be given in giveaway reward.
  float reward_amount = 6;
}

// ExtraInterestSdBonusPayoutEvent is triggered for giving bonus payout as reward for extra interest Sd.
message ExtraInterestSdBonusPayoutEvent {
  // common request header across all the consumer grpc services.
  queue.ConsumerRequestHeader request_header = 1;
  // actor for whom bonus payout should happen.
  string actor_id = 2;
  // unique id generated by deposit service for each payout useful for de-duping payout.
  string bonus_payout_id = 3;
  // bonus payout amount
  google.type.Money bonus_payout_amount = 4;
  // date at which the bonus payout was expected.
  google.protobuf.Timestamp payout_date = 5;
  // account number of bonus SD for which payout is expected.
  string bonus_sd_account_number = 6;
  // payout number denoting the installment number of current payout, starts with 1.
  int32 bonus_payout_number = 7;
}

// MinBalanceEvent is triggered via cron to process reward for maintaining X min balance over Y days
message MinBalanceEvent {
  // common request header across all the consumer grpc services.
  queue.ConsumerRequestHeader request_header = 1;

  string actor_id = 2;

  // RefId will be a unique id per actor per date so can keep this `actor_id:date`
  string ref_id = 3;

  // This will be same as computed_date of the data engg pipeline
  google.protobuf.Timestamp action_timestamp = 4;

  bigdata.MinBalanceConstruct min_balance_construct = 5;
}

// RewardsCreditCardTxnEvent is a wrapper over existing credit card txn event
// to enrich it with additional details like txn category etc.
message RewardsCreditCardTxnEvent {
  firefly.accounting.consumer.CreditCardTransactionEvent credit_card_transaction_event = 1;

  // map containing actor Id (from/to) to ontologies mapping for the given transaction.
  map<string, CategoryOntologyIds> actor_id_to_txn_category_ontology_ids = 2;
}

message CategoryOntologyIds {
  repeated string ontology_ids = 1;
}

// event ONLY used for unlocking rewards
message UnlockRewardEvent {
  // ID of the reward to be unlocked, this reward will be unlocked if all conditions required for unlocking are satisfied
  string reward_id = 1;
}

// TieringPeriodicRewardEvent is triggered for giving monthly rewards to plus, infinite, salary tier users
// This event will be used to generate actual rewards from all projections generated in the specified period
message TieringPeriodicRewardEvent {
  // common request header across all the consumer grpc services.
  queue.ConsumerRequestHeader request_header = 1;
  // id to uniquely identify the event
  string event_id = 2;
  // actor for whom tiering reward projections should be actualised
  string actor_id = 3;
  // start time from which projections will be actualised
  google.protobuf.Timestamp from_time = 4;
  // end time till which projections will be actualised
  google.protobuf.Timestamp to_time = 5;
}

message VendorRewardFulfillmentEvent {
  queue.ConsumerRequestHeader request_header = 1;
  string actor_id = 2;
  // Name of the vendor (M2P)
  vendorgateway.Vendor vendor = 3;
  // Reward offer type (Pass offer type from reward)
  VendorRewardOfferType offer_type = 4;
  // Type of reward: VOUCHER, FI_COIN
  rewards.RewardType reward_type = 5;
  // reward value/units to be given
  double reward_value = 6;
  // id of package to be given in case of Voucher reward (it will come from minion reward)
  string reward_package_id = 7;
  // Vendor reference ID (minion reward external-id), Should be unique for each fulfillment
  string vendor_ref_id = 8;
  google.protobuf.Timestamp event_time = 9;
}

enum VendorRewardOfferType {
  // Removed MAGNIFI_CVP (1) since vendor side enums has been renamed.
  // It was removed in favour of MAGNIFI_CVP_FI_COIN, MAGNIFI_CVP_VOUCHER.
  reserved 1;
  VENDOR_REWARD_OFFER_TYPE_UNSPECIFIED = 0;
  MAGNIFI_CVP_FI_COIN = 2;
  MAGNIFI_CVP_VOUCHER = 3;
}


