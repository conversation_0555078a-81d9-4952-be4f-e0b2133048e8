//go:generate gen_queue_pb
// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package datacollector;

import "api/card/notification/service.proto";
import "api/casper/events/offer_redemption_status_event_event.proto";
import "api/connected_account/external/external.proto";
import "api/creditreportv2/notification/credit_report_download_event.proto";
import "api/firefly/accounting/consumer/service.proto";
import "api/firefly/billing/events/bill_generation_event.proto";
import "api/firefly/event/payload.proto";
import "api/fittt/action/action_execution.proto";
import "api/fittt/sports/events/events.proto";
import "api/investment/aggregator/events/events.proto";
import "api/order/order.proto";
import "api/queue/consumer_headers.proto";
import "api/rewards/datacollector/pipeline_events.proto";
import "api/salaryprogram/events/salary_detection_event.proto";
import "api/salaryprogram/events/salary_status_update_event.proto";
import "api/savings/savings_producer.proto";
import "api/search/events/rewards_event.proto";
import "api/tiering/external/tier_update_event.proto";
import "api/user/kyc_event.proto";
import "api/user/onboarding/notification.proto";
import "api/insights/epf/events.proto";
import "api/nudge/consumer/service.proto";

option go_package = "github.com/epifi/gamma/api/rewards/datacollector";
option java_package = "com.github.epifi.gamma.api.rewards.datacollector";

message ConsumerResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

// contains methods to process data from pipelines
service Consumer {
  rpc ProcessOrderUpdateEvents (order.OrderUpdate) returns (ConsumerResponse) {}
  rpc ProcessUserSearchEvents (search.events.UserSearchEvent) returns (ConsumerResponse) {}
  rpc ProcessUserSearchEventV1 (search.events.UserSearchEvent) returns (ConsumerResponse) {}
  rpc ProcessManualGiveawayEvents (datacollector.ManualGiveawayEvent) returns (ConsumerResponse) {}
  rpc ProcessFitttActionExecutionUpdateEvents (api.fittt.action.ActionExecutionUpdate) returns (ConsumerResponse) {}
  rpc ProcessExtraInterestSdBonusPayoutEvents (datacollector.ExtraInterestSdBonusPayoutEvent) returns (ConsumerResponse) {}
  rpc ProcessMinBalanceEvent (datacollector.MinBalanceEvent) returns (ConsumerResponse) {}
  rpc ProcessCAAccountUpdateEvent (connected_account.external.AccountUpdateEvent) returns (ConsumerResponse) {}
  rpc ProcessKYCEvent (user.KycEvent) returns (ConsumerResponse) {}
  rpc ProcessFitttSportsEvent (api.fittt.sports.events.SportsEvent) returns (ConsumerResponse) {};
  rpc ProcessSavingsAccountStateUpdateEvent (savings.AccountStateUpdateEvent) returns (ConsumerResponse) {};
  rpc ProcessSalaryDetectionEvent (salaryprogram.events.SalaryDetectionEvent) returns (ConsumerResponse) {};
  rpc ProcessSalaryProgramStatusUpdateEvent (salaryprogram.events.SalaryProgramStatusUpdateEvent) returns (ConsumerResponse) {};
  rpc ProcessOnboardingStageUpdateEvent (user.onboarding.OnboardingStageUpdate) returns (ConsumerResponse) {};
  rpc ProcessCreditCardTransactionEvent (firefly.accounting.consumer.CreditCardTransactionEvent) returns (ConsumerResponse);
  rpc ProcessCreditCardBillingEvent (firefly.billing.events.CreditCardBillGenerationEvent) returns (ConsumerResponse);
  rpc ProcessCreditCardRequestStageUpdateEvent (firefly.event.CreditCardRequestStageUpdateEvent) returns (ConsumerResponse);
  rpc ProcessCreditReportDownloadEvent (creditreportv2.notification.CreditReportDownloadEvent) returns (ConsumerResponse);
  rpc ProcessOfferRedemptionStatusUpdateEvent (casper.events.OfferRedemptionStatusUpdateEvent) returns (ConsumerResponse);
  rpc ProcessInvestmentRetentionRewardEvent (api.investment.aggregator.events.InvestmentEvent) returns (ConsumerResponse);
  rpc ProcessTieringPeriodicRewardEvent (datacollector.TieringPeriodicRewardEvent) returns (ConsumerResponse);
  rpc ProcessTieringTierUpdateEvent (tiering.external.TierUpdateEvent) returns (ConsumerResponse);
  rpc ProcessDebitCardSwitchNotificationEvent(card.notification.ProcessCardSwitchFinancialNotificationsRequest) returns (ConsumerResponse);
  rpc ProcessEpfPassbookImportEvent (insights.epf.EpfPassbookImportEvent) returns (ConsumerResponse);
  rpc ProcessCAAccountDataSyncEvent (connected_account.external.AccountDataSyncEvent) returns (ConsumerResponse);
  rpc ProcessActorNudgeStatusUpdateEvent (nudge.consumer.ActorNudgeStatusUpdateEvent) returns (ConsumerResponse);
  rpc ProcessVendorRewardFulfillmentEvent(datacollector.VendorRewardFulfillmentEvent) returns (ConsumerResponse);
}
