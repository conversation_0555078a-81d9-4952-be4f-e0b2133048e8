syntax = "proto3";

package rewards.pinot;

import "api/categorizer/enums.proto";
import "api/order/payment/payment_protocol.proto";
import "api/order/workflow.proto";
import "api/rewards/collected_data_type.proto";
import "api/rewards/reward.proto";
import "api/rewards/reward_offer_type.proto";
import "api/rpc/status.proto";
import "api/tiering/external/external.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/rewards/pinot";
option java_package = "com.github.epifi.gamma.api.rewards.pinot";

service RewardsAggregates {
  // GetRewardsAggregates returns rewards aggregates: aggregates reward units and reward count per reward type
  // mandatory fields: actorId and time_range (ex : created_at (from and to)).
  // note : currently only terminal rewards are aggregated using this rpc.
  rpc GetRewardsAggregates (GetRewardsAggregatesRequest) returns (GetRewardsAggregatesResponse);
  // GetMerchantRewardsAggregates returns merchant wise rewards aggregates:
  // aggregates reward units and reward count per reward type per merchant
  // mandatory fields: actorId and time range: created_at (from and to).
  rpc GetMerchantRewardsAggregates (GetMerchantRewardsAggregatesRequest) returns (GetMerchantRewardsAggregatesResponse);
  // GetActualizedRewardProjectionsAggregates returns actualized reward projections aggregates:
  // reward units aggregates per reward type for claimed and unclaimed actualized rewards
  // NOTE : unclaimed rewards aggregates may not be accurate as the reward type is not yet chosen by the user
  //        choose only one reward type for considering unclaimed actualized rewards
  // mandatory fields: actorId and time range(ex : created_at (from and to)).
  rpc GetActualizedRewardProjectionsAggregates (GetActualizedRewardProjectionsAggregatesRequest) returns (GetActualizedRewardProjectionsAggregatesResponse);
  // GetUnActualizedRewardProjectionsAggregates returns aggregates for un-actualized reward projections for given actor
  // this will contain the aggregates for all reward projections for which reward has not yet been generated.
  // all options are added to aggregates and only one reward type should be chosen to be considered for aggregates
  // This will return the aggregates for any reward projection that is not actualized yet and take the projected option with the input reward type
  // mandatory fields: actorId, rewardType, time_range(ex : created_at (from and to)).
  rpc GetUnActualizedRewardProjectionsAggregates (GetUnActualizedRewardProjectionsAggregatesRequest) returns (GetUnActualizedRewardProjectionsAggregatesResponse);
  // GetCustomUnActualizedRewardProjectionsAggregates returns aggregates for un-actualized reward projections based on a specific custom query type.
  // This allows for specialized aggregation logic, like tiering monthly aggregates, beyond standard filtering.
  // mandatory fields: actorId, rewardType, time_range, projection_custom_query
  rpc GetCustomUnActualizedRewardProjectionsAggregates (GetCustomUnActualizedRewardProjectionsAggregatesRequest) returns (GetCustomUnActualizedRewardProjectionsAggregatesResponse);
}

// TimeRangeFilter combines a TimeRangeType with its corresponding TimeRange.
message TimeRangeFilter {
  TimeRangeType type = 1;
  TimeRange range = 2;
}

// TimeRangeType defines the type of time range to filter on.
enum TimeRangeType {
  TIME_RANGE_TYPE_UNSPECIFIED = 0;
  TIME_RANGE_TYPE_CREATED_AT = 1; // Time when the record was created.
  TIME_RANGE_TYPE_ACTION_TIME = 2; // Time when the action associated with the record occurred.
}

// TimeRange defines a time interval with start and end timestamps.
// Example for March 2024:
// from: "2024-03-01T00:00:00Z" (inclusive)
// to:   "2024-04-01T00:00:00Z" (exclusive)
// This covers all events for the month of March 2024.
message TimeRange {
  google.protobuf.Timestamp from = 1; // Start time of the range (inclusive).
  google.protobuf.Timestamp to = 2;   // End time of the range (exclusive).
}

message GetRewardsAggregatesRequest {
  // mandatory: actorId of the user
  string actor_id = 1;
  // mandatory: start time of the time range
  // deprecated in favour of time_ranges in Filters
  google.protobuf.Timestamp from_created_at = 2 [deprecated = true];
  // mandatory: end time of the time range
  // deprecated in favour of time_ranges in Filters
  google.protobuf.Timestamp to_created_at = 3 [deprecated = true];
  // optional: collected data type for which reward aggregates are needed.
  // deprecated in favour of direct filter usage from Filters
  CollectedDataType action_type = 4 [deprecated = true];
  // Filters to be applied on rewards.
  // See the 'Filters' message definition for details on mandatory fields and how filters are combined.
  Filters filters = 5;
  // Mandatory: Time range filter for the aggregation.
  TimeRangeFilter time_range = 6;
}

// Filters to be applied on rewards.
// Notes:
// 1. Individual filter fields (e.g., reward_type, offer_id) work in an AND fashion. If multiple are set, all conditions must match.
// 2. Fields that are arrays (e.g., reward_statuses, include_l0_ontologies) work in an OR fashion within themselves. If multiple values are provided for a single array field, any one of those values matching is sufficient for that specific filter condition.
message Filters {
  RewardType reward_type = 1;
  string offer_id = 2;
  ClaimType claim_type = 3;
  tiering.external.Tier account_tier = 4;
  order.payment.PaymentProtocol payment_protocol = 5;
  order.OrderWorkflow order_workflow = 6;
  repeated RewardStatus reward_statuses = 7;
  repeated RewardOfferType reward_offer_types = 8;
  repeated CollectedDataType action_types = 9 [deprecated = true];
  repeated categorizer.L0 include_l0_ontologies = 10;
  repeated categorizer.L0 exclude_l0_ontologies = 11;
  repeated string include_ontology_ids = 12;
  repeated string exclude_ontology_ids = 13;
  repeated string merchant_ids = 14;
  repeated string merchant_names = 15;
  repeated CollectedDataType include_action_types = 16;
  repeated CollectedDataType exclude_action_types = 17;
}

message GetRewardsAggregatesResponse {
  rpc.Status status = 1;
  repeated RewardOptionAggregate reward_option_aggregates = 2;
}

message RewardOptionAggregate {
  RewardType reward_type = 1;
  double reward_units = 2;
  int64 reward_count = 3;
}

message GetMerchantRewardsAggregatesRequest {
  // mandatory: actorId of the user
  string actor_id = 1;
  // mandatory: start time of the time range
  google.protobuf.Timestamp from_created_at = 2;
  // mandatory: end time of the time range
  google.protobuf.Timestamp to_created_at = 3;
  // optional: collected data type for which reward aggregates are needed
  CollectedDataType action_type = 4;
  // optional: reward type
  RewardType reward_type = 5;
}

message GetMerchantRewardsAggregatesResponse {
  rpc.Status status = 1;
  repeated MerchantRewardAggregate merchant_reward_aggregates = 2;
}

message MerchantRewardAggregate {
  RewardOptionAggregate reward_aggregate = 1;
  string merchant_id = 2;
  string merchant_name = 3;
}

message GetActualizedRewardProjectionsAggregatesRequest {
  // mandatory: actorId of the user
  string actor_id = 1;
  // mandatory: start time of the time range
  // deprecated in favour of time_ranges in ProjectionFilters
  google.protobuf.Timestamp from_created_at = 2 [deprecated = true];
  // mandatory: end time of the time range
  // deprecated in favour of time_ranges in ProjectionFilters
  google.protobuf.Timestamp to_created_at = 3 [deprecated = true];
  // Filters to be applied on projections.
  // See the 'ProjectionFilters' message definition for details on mandatory fields and how filters are combined.
  ProjectionFilters filters = 4;
  // Mandatory: Time range filter for the aggregation.
  TimeRangeFilter time_range = 5;
}

// Filters to be applied on projections.
// Notes:
// 1. Individual filter fields (e.g., offer_id, account_id) work in an AND fashion. If multiple are set, all conditions must match.
// 2. Fields that are arrays (e.g., reward_offer_types, include_l0_ontologies) work in an OR fashion within themselves. If multiple values are provided for a single array field, any one of those values matching is sufficient for that specific filter condition.
message ProjectionFilters {
  string offer_id = 1;
  string account_id = 2;
  tiering.external.Tier account_tier = 3;
  order.payment.PaymentProtocol payment_protocol = 4;
  order.OrderWorkflow order_workflow = 5;
  repeated RewardOfferType reward_offer_types = 6;
  repeated CollectedDataType action_types = 7;
  repeated categorizer.L0 include_l0_ontologies = 8;
  repeated categorizer.L0 exclude_l0_ontologies = 9;
  repeated string include_ontology_ids = 10;
  repeated string exclude_ontology_ids = 11;
  repeated string merchant_ids = 12;
  repeated string merchant_names = 13;
}

message GetActualizedRewardProjectionsAggregatesResponse {
  rpc.Status status = 1;
  ActualizedRewardProjectionsAggregate reward_projections_aggregates = 2;
}

message ActualizedRewardProjectionsAggregate {
  repeated RewardProjectionOption claimed_reward_projections = 1;
  repeated RewardProjectionOption unclaimed_reward_projections = 2;
}

message RewardProjectionOption {
  RewardType reward_type = 1;
  double reward_units = 2;
}

message GetUnActualizedRewardProjectionsAggregatesRequest {
  // mandatory: actorId of the user
  string actor_id = 1;
  // mandatory: reward type
  RewardType reward_type = 2;
  // Mandatory: Time range filter for the aggregation.
  TimeRangeFilter time_range = 3;
  // Filters to be applied on projections.
  // See the 'ProjectionFilters' message definition for details.
  ProjectionFilters filters = 4;
}

message GetUnActualizedRewardProjectionsAggregatesResponse {
  rpc.Status status = 1;
  // aggregates for un-actualized reward projections
  // i.e. aggregates for all reward projections for which reward has not yet been generated for the given reward type
  RewardProjectionOption reward_projections_aggregates = 2;
}

// Request message for custom un-actualized reward projection aggregations.
message GetCustomUnActualizedRewardProjectionsAggregatesRequest {
  // mandatory: actorId of the user
  string actor_id = 1;
  // mandatory: reward type to aggregate on
  RewardType reward_type = 2;
  // Mandatory: Time range filter for the aggregation.
  TimeRangeFilter time_range = 3;
  // mandatory: Specifies the custom query type and its required metadata.
  ProjectionCustomQuery projection_custom_query = 4;
}

// Response message for custom un-actualized reward projection aggregations.
message GetCustomUnActualizedRewardProjectionsAggregatesResponse {
  rpc.Status status = 1;
  // The single aggregated result based on the custom query logic.
  RewardProjectionOption reward_projections_aggregates = 2;
}

// ProjectionCustomQuery defines the specific custom aggregation to perform.
// It includes the type of query and the necessary metadata for that query.
message ProjectionCustomQuery {
  // The type of custom query to execute.
  ProjectionCustomQueryType type = 1;
  // Metadata required for the specific custom query type.
  oneof metadata {
    // Metadata specific to tiering monthly projection aggregates.
    TieringMonthlyProjectionsAggregateMetadata tiering_monthly_projections_aggregate_metadata = 2;
  }
}

// ProjectionCustomQueryType enumerates the supported custom aggregation queries.
enum ProjectionCustomQueryType {
  // Default, indicates no custom query is specified.
  PROJECTION_CUSTOM_QUERY_TYPE_UNSPECIFIED = 0;
  // Indicates a request to aggregate tiering projections with daily/monthly caps.
  PROJECTION_CUSTOM_QUERY_TYPE_TIERING_MONTHLY_PROJECTIONS_AGGREGATE = 1;
}

// TieringMonthlyProjectionsAggregateMetadata contains parameters needed for tiering aggregation.
message TieringMonthlyProjectionsAggregateMetadata {
  // Mandatory: The specific action type to filter projections for (e.g., ORDER).
  // Ensure this corresponds to the actual action type enum/string used in your data.
  rewards.CollectedDataType action_type = 1;
  // Mandatory: The offer types to filter projections for (e.g., PLUS_TIER_1_PERCENT_CASHBACK_OFFER).
  // note: the offer types should be in accordance to the user tier
  repeated rewards.RewardOfferType offer_types = 2;
  // The daily cap to apply
  // note : the units here should be in accordance to the reward type and offer_types(which will be in accordance with the user tier)
  int64 daily_limit = 3;
  // The overall monthly cap to apply
  // note : the units here should be in accordance to the reward type and offer_types(which will be in accordance with the user tier)
  int64 monthly_limit = 4;
}
