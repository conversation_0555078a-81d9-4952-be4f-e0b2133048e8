//go:generate gen_queue_pb
syntax = "proto3";

package rewards.unlocker;

import "api/queue/consumer_headers.proto";
import "api/rewards/datacollector/pipeline_events.proto";

option go_package = "github.com/epifi/gamma/api/rewards/unlocker";
option java_package = "com.github.epifi.gamma.api.rewards.unlocker";

service Consumer {
  // this rpc method will be used to process rewards unlocking events
  rpc ProcessUnlockEvent (datacollector.CollectedData) returns (ConsumerResponse) {}
}

message ConsumerResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
