syntax = "proto3";

package bre;

import "api/preapprovedloan/enums.proto";
import "api/preapprovedloan/loan_offer_eligibility_criteria.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/user.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/bre";
option java_package = "com.github.epifi.gamma.api.bre";

service Bre {
  // This rpc will be used to get a decision on the loan eligibility for a user and a regulated entity.
  rpc GetLoanDecisioning (GetLoanDecisioningRequest) returns (GetLoanDecisioningResponse) {}
  // This rpc will be used to get a pre decision on the loan eligibility for a user and a regulated entity
  // and return pre offer bre decision and if Employment needs to be fetched check
  rpc GetLoanPreScreening (GetLoanPreScreeningRequest) returns (GetLoanPreScreeningResponse) {}

  // rpc for calling inhouse bre api for CC
  rpc InhouseBreCheckForCC (InhouseBRECheckForCCRequest) returns (InhouseBRECheckForCCResponse) {}

  // This rpc will be used to get a pre decision on the loan eligibility for a user and get applicable vendors.
  rpc GetPreBreEligibilityDetails (GetPreBreEligibilityDetailsRequest) returns (GetPreBreEligibilityDetailsResponse) {}

  // This rpc will be used to get a decision on the loan eligibility for a user specific to a vendor.
  // This returns the final decision and the offer details OR asks for extra data required for the final decision.
  rpc GetFinalBreEligibilityDetails (GetFinalBreEligibilityDetailsRequest) returns (GetFinalBreEligibilityDetailsResponse) {}

  // This rpc will be used to get a decision on the pre loan eligibility for a user
  rpc GetPreBreEligibilityOffer (GetPreBreEligibilityOfferRequest) returns (GetPreBreEligibilityOfferResponse) {}
}

message GetPreBreEligibilityDetailsRequest {
  string actor_id = 1;
  string request_id = 2;
  CustomerDetails customer_details = 3;
  bool is_etb_user = 4;
}

message GetPreBreEligibilityDetailsResponse {
  rpc.Status status = 1;
  PreBreDecision decision = 2;
  bytes raw_bre_response = 3;
}

message PreBreDecision {
  string actor_id = 1;
  string request_id = 2;
  google.protobuf.Timestamp evaluation_request_time = 3;
  google.protobuf.Timestamp evaluation_run_time = 4;
  google.protobuf.Timestamp valid_till = 5;
  repeated preapprovedloan.Vendor valid_lenders = 6;
  int32 number_valid_lenders = 7;
  preapprovedloan.PolicyParams policy_params = 8;
}

message GetFinalBreEligibilityDetailsRequest {
  string actor_id = 1;
  string request_id = 2;
  CustomerDetails customer_details = 3;
  preapprovedloan.Vendor vendor = 4;
  DataAvailability epfo = 5;
  DataAvailability aa = 6;
  DataAvailability cibil = 7;
  preapprovedloan.PolicyParams policy_params = 8;
  string product = 9;
}

message DataAvailability {
  bool is_available = 2;
  google.type.Date collection_date = 3;
}

message CustomerDetails {
  PersonalDetails personal_details = 1;
  EmploymentDetails employment_details = 2;
  api.typesv2.PostalAddress residential_address = 3;
  RequestedLoanDetails requested_loan_details = 5;
}

message RequestedLoanDetails {
  google.type.Money desired_loan_amount = 1;
}

message PersonalDetails {
  google.type.Date dob = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.Gender gender = 3;
  string pan = 4;
}

message EmploymentDetails {
  api.typesv2.EmploymentType employment_type = 1;
  google.type.Money monthly_income = 2;
  string employer_name = 3;
  string work_email = 4;
  api.typesv2.PostalAddress work_address = 5;
}

message GetFinalBreEligibilityDetailsResponse {
  rpc.Status status = 1;
  // key -> loan_program, value -> if true return send the required details in next call
  // ex. "dataRequirements":{"PL_SALARIED_P001": false,"PL_SALARIED_P002": true,"PL_SALARIED_P003": true}
  map<string, bool> data_requirements = 2;
  preapprovedloan.PolicyParams policy_params = 3;
  bool subsequent_call_allowed = 4;
  FinalBreDecision prioritized_decision = 5;
  repeated FinalBreDecision decisions = 6;
  google.protobuf.Timestamp evaluation_run_time = 7;
  bytes raw_bre_response = 8;
}

message FinalBreDecision {
  string lending_program = 1;
  Decision decision = 2;
  OfferDetails offer_details = 3;
  google.protobuf.Timestamp valid_till = 4;
}

message OfferDetails {
  google.type.Money max_amount = 1;
  google.type.Money min_amount = 2;
  google.type.Money max_emi_amount = 3;
  int32 max_tenure_in_months = 4;
  int32 min_tenure_in_months = 5;
  double interest_percentage = 6;
  double processing_fee_percentage = 7;
  double gst_percentage = 8;
  google.type.Date emi_due_date = 9;
  google.protobuf.Timestamp valid_till = 10;
}

message InhouseBRECheckForCCRequest {
  string actor_id = 1;
  // pin code of permanent address from kyc
  string pin_code = 2;
  google.type.Date date_of_birth = 3;
  string client_req_id = 4;
  InhouseBreProvenance provenance = 5;
  string card_request_stage_id = 6;
}

enum InhouseBreProvenance {
  INHOUSE_BRE_PROVENANCE_UNSPECIFIED = 0;
  INHOUSE_BRE_PROVENANCE_IN_APP = 1;
  INHOUSE_BRE_PROVENANCE_WEB_ELIGIBILITY = 2;
}

message InhouseBRECheckForCCResponse {
  rpc.Status status = 1;
  // result enum for the check
  BREDecision result = 2;
  // raw response from BRE in string format
  string raw_response = 3;
  // strings for failure reason
  repeated string failure_reason = 4;
  // enum for storing weather BRE decision was forcefully override
  api.typesv2.common.BooleanEnum is_override = 5;
}

enum BREDecision {
  BRE_DECISION_UNSPECIFIED = 0;
  BRE_DECISION_APPROVED = 1;
  BRE_DECISION_DECLINED = 2;
}

message GetLoanDecisioningRequest {
  // actor ID for whom to fetch loan decisioning
  string actor_id = 1;
  // regulated entity for whom to fetch loan decisioning
  vendorgateway.Vendor vendor = 2;
  google.type.Date dob = 3;
  api.typesv2.EmploymentType employment_type = 4;
  google.type.Money monthly_income = 5;
  api.typesv2.common.Name name = 6;
  api.typesv2.Gender gender = 7;
  string pan = 8;
  api.typesv2.PostalAddress address = 9;
  string employer_name = 10;
  string work_email = 11;
  preapprovedloan.LoanProgram loan_program = 12;
  string scheme_id = 13;
  string batch_id = 14;
  api.typesv2.KYCLevel kyc_level = 15;
  preapprovedloan.PolicyParams policy_params = 16;
}

message GetLoanDecisioningResponse {
  rpc.Status status = 1;
  Decision loan_decision = 2;
  bytes raw_bre_response = 3;
  OfferDetails offer_details = 4;
  string scheme_id = 5;
  string batch_id = 6;
  string actor_id = 7;
  preapprovedloan.LoanProgram loan_program = 8;
  preapprovedloan.Vendor vendor = 9;
  repeated string external_reasons = 10;
  preapprovedloan.PolicyParams policy_params = 11;

  message OfferDetails {
    google.type.Money min_amount = 1;
    google.type.Money max_amount = 2;
    google.type.Money max_emi_amount = 3;
    double interest_percentage = 4;
    double processing_fee_percentage = 5;
    double gst_percentage = 6;
    int32 min_tenure_in_months = 7;
    int32 max_tenure_in_months = 8;
    google.type.Date emi_due_date = 9;
    google.protobuf.Timestamp valid_till = 10;
  }
}

enum Decision {
  DECISION_UNSPECIFIED = 0;
  DECISION_APPROVED = 1;
  DECISION_REJECTED = 2;
}

message GetLoanPreScreeningRequest {
  // actor ID for whom to fetch loan decisioning
  string actor_id = 1;
  // regulated entity for whom to fetch loan decisioning
  vendorgateway.Vendor vendor = 2;
  google.type.Date dob = 3;
  api.typesv2.EmploymentType employment_type = 4;
  google.type.Money monthly_income = 5;
  api.typesv2.common.Name name = 6;
  api.typesv2.Gender gender = 7;
  string pan = 8;
  api.typesv2.PostalAddress address = 9;
  string employer_name = 10;
  string work_email = 11;
  preapprovedloan.LoanProgram loan_program = 12;
}

message GetLoanPreScreeningResponse {
  rpc.Status status = 1;
  Decision loan_decision = 2;
  bool is_epfo_data_needed = 3;
  bytes raw_bre_response = 4;
  string scheme_id = 5;
  string batch_id = 6;
  repeated string external_reasons = 7;
  FinalOfferBreRequirements final_offer_bre_requirements = 8;
  preapprovedloan.PolicyParams policy_params = 9;
  message FinalOfferBreRequirements {
    bool epfo = 1;
    bool aa = 2;
    bool cibil = 3;
  }
}

message GetPreBreEligibilityOfferRequest {
  string actor_id = 1;
  string request_id = 2;
  CustomerDetails customer_details = 3;
  DataAvailability experian = 4;
  preapprovedloan.PolicyParams policy_params = 5;
}

message GetPreBreEligibilityOfferResponse {
  rpc.Status status = 1;
  // key -> loan_program, value -> if true return send the required details in next call
  // ex. "dataRequirements":{"PL_SALARIED_P001": false,"PL_SALARIED_P002": true,"PL_SALARIED_P003": true}
  map<string, bool> data_requirements = 2;
  preapprovedloan.PolicyParams policy_params = 3;
  bool subsequent_call_allowed = 4;
  // caller should make final decision on comparing all the decisions
  // making final decision logic to be implemented on caller side
  repeated FinalBreDecision decisions = 5;
  google.protobuf.Timestamp evaluation_run_time = 6;
  bytes raw_bre_response = 7;
}
