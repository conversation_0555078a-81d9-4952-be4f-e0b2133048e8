syntax = "proto3";

package simulator.kyc.federal;

import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/kyc/federal";
option java_package = "com.github.epifi.gamma.api.simulator.kyc.federal";

message SearchRequest {
  string sender_code = 1 [json_name = "SenderCode"];
  string service_access_id = 2 [json_name = "ServiceAccessId"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];
  string request_id = 4 [json_name = "RequestId"];
  string id_type = 8 [json_name = "IdType"];
  string id_value = 9 [json_name = "IdNumber"];

  // Optional. Federal uses this field for tracing the request & debugging
  string mobile_number = 10 [json_name = "MobileNumber"];
}

message SearchResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  string request_id = 2 [json_name = "RequestId"];
  string device_token = 3 [json_name = "DeviceToken"];
  string search_status = 4 [json_name = "SearchStatus"];
  SearchResponsePidData pid = 5 [json_name = "Pid"];

  // response code for request. refer to the
  // [doc](https://docs.google.com/document/d/1gnHFtQMd_6Rq0P_Z7HxXVBFhGtqS3dEYbP8ytM-fNv8/)
  // for code mapping
  string response = 6 [json_name = "Response"];

  // description of response code
  string reason = 7 [json_name = "Reason"];
}

message SearchResponsePidData {
  string age = 1 [json_name = "Age"];
  string ckyc_no = 2 [json_name = "CkycNo"];
  string fathers_name = 3 [json_name = "FathersName"];
  string image_type = 4 [json_name = "ImageType"];
  string photo_base64 = 5 [json_name = "Photo"];
  // date when kyc was first recorded
  string kyc_date = 6 [json_name = "KycDate"];
  string name = 7 [json_name = "Name"];
  string updated_date = 8 [json_name = "UpdatedDate"];
  string ckyc_reference_id = 9 [json_name = "CkycReferenceId"];
}

// CKYC Download Data

message GetDataRequest {
  string sender_code = 1 [json_name = "SenderCode"];
  string service_access_id = 2 [json_name = "ServiceAccessId"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];
  string request_id = 4 [json_name = "RequestId"];
  DownloadRequestPidData pid_data = 8 [json_name = "PidData"];

  // Optional. Federal uses this field for tracing the request & debugging
  string mobile_number = 9 [json_name = "MobileNumber"];
}

message DownloadRequestPidData {
  string ckyc_no = 1 [json_name = "CkycNo"];
  string auth_factor = 2 [json_name = "AuthFactor"];
  string auth_factor_type = 3 [json_name = "AuthFactorType"];
  string download_status = 4 [json_name = "DownloadStatus"];
}

message GetDataResponse {
  CkycInq ckyc_inquiry = 1 [json_name = "CkycInq"];
  string request_id = 2 [json_name = "RequestId"];
  // response code is used to identify the status of the request and type of error
  string response_code = 3 [json_name = "Response"];
  // description of the response code
  string reason = 4 [json_name = "Reason"];
}

message GetDataResponseV3 {
  CkycInqV3 ckyc_inquiry = 1 [json_name = "CkycInq"];
  string request_id = 2 [json_name = "RequestId"];
  // response code is used to identify the status of the request and type of error
  string response_code = 3 [json_name = "Response"];
  // description of the response code
  string reason = 4 [json_name = "Reason"];
}

message CkycInqV3 {
  string download_status = 1 [json_name = "DownloadStatus"];
  string request_id = 2 [json_name = "Request_id"];
  string message = 3 [json_name = "Error"];
}

message CkycInq {
  DownloadResponsePidData pid_data = 1 [json_name = "PID"];
}

message DownloadResponsePidData {
  PersonalDetails personal_details_data = 1 [json_name = "PERSONAL_DETAILS"];
  IdentityDetails identity_details_data = 2 [json_name = "IdentityDetails"];
  RelatedPersonDetails related_person_details_data = 3 [json_name = "RelatedPersonDetails"];
  ImageDetails ImageDetails = 4 [json_name = "ImageDetails"];
}

message PersonalDetails {
  string acc_type = 1 [json_name = "AccType"];
  string ckyc_no = 2 [json_name = "CkycNo"];
  string prefix = 3 [json_name = "Prefix"];
  string fname = 4 [json_name = "Fname"];
  string mname = 5 [json_name = "Mname"];
  string lname = 6 [json_name = "Lname"];
  string maiden_prefix = 7 [json_name = "MaidenPrefix"];
  string maiden_fname = 8 [json_name = "MaidenFname"];
  string maiden_mname = 9 [json_name = "MaidenMname"];
  string maiden_lname = 10 [json_name = "MaidenLname"];
  string fatherspouse_flag = 11 [json_name = "FatherspouseFlag"];
  string father_prefix = 12 [json_name = "FatherPrefix"];
  string father_fname = 13 [json_name = "FatherFname"];
  string father_mname = 14 [json_name = "FatherMname"];
  string father_lname = 15 [json_name = "FatherLname"];
  string mother_prefix = 16 [json_name = "MotherPrefix"];
  string mother_fname = 17 [json_name = "MotherFname"];
  string mother_mname = 18 [json_name = "MotherMname"];
  string mother_lname = 19 [json_name = "MotherLname"];
  string gender = 20 [json_name = "Gender"];
  string martial_status = 21 [json_name = "MartialStatus"];
  string nationality = 22 [json_name = "Nationality"];
  string occupation = 23 [json_name = "Occupation"];
  string dob = 24 [json_name = "Dob"];
  string resi_status = 25 [json_name = "ResiStatus"];
  string juri_flag = 26 [json_name = "JuriFlag"];
  string juri_resi = 27 [json_name = "JuriResi"];
  string tax_num = 28 [json_name = "TaxNum"];
  string birth_country = 29 [json_name = "BirthCountry"];
  string birth_place = 30 [json_name = "BirthPlace"];
  string perm_type = 31 [json_name = "PermType"];
  string perm_line1 = 32 [json_name = "PermLine1"];
  string perm_line2 = 33 [json_name = "PermLine2"];
  string perm_line3 = 34 [json_name = "PermLine3"];
  string perm_city = 35 [json_name = "PermCity"];
  string perm_dist = 36 [json_name = "PermDist"];
  string perm_state = 37 [json_name = "PermState"];
  string perm_country = 38 [json_name = "PermCountry"];
  string perm_pin = 39 [json_name = "PermPin"];
  string perm_poa = 40 [json_name = "PermPoa"];
  string perm_poaothers = 41 [json_name = "PermPoaothers"];
  string PermCorresSameflag = 42 [json_name = "PermCorresSameflag"];
  string CorresLine1 = 43 [json_name = "CorresLine1"];
  string CorresLine2 = 44 [json_name = "CorresLine2"];
  string CorresLine3 = 45 [json_name = "CorresLine3"];
  string CorresCity = 46 [json_name = "CorresCity"];
  string CorresDist = 47 [json_name = "CorresDist"];
  string CorresState = 48 [json_name = "CorresState"];
  string CorresCountry = 49 [json_name = "CorresCountry"];
  string CorresPin = 50 [json_name = "CorresPin"];
  string juri_same_flag = 51 [json_name = "JuriSameFlag"];
  string juri_line1 = 52 [json_name = "JuriLine1"];
  string juri_line2 = 53 [json_name = "JuriLine2"];
  string juri_line3 = 54 [json_name = "JuriLine3"];
  string juri_city = 55 [json_name = "JuriCity"];
  string juri_state = 56 [json_name = "JuriState"];
  string juri_country = 57 [json_name = "JuriCountry"];
  string juri_pin = 58 [json_name = "JuriPin"];
  string resi_std_code = 59 [json_name = "ResiStdCode"];
  string resi_tel_num = 60 [json_name = "ResiTelNum"];
  string off_std_code = 61 [json_name = "OffStdCode"];
  string off_tel_num = 62 [json_name = "OffTelNum"];
  string mob_code = 63 [json_name = "MobCode"];
  string mob_num = 64 [json_name = "MobNum"];
  string fax_code = 65 [json_name = "FaxCode"];
  string fax_no = 66 [json_name = "FaxNo"];
  string email = 67 [json_name = "Email"];
  string remarks = 68 [json_name = "Remarks"];
  string dec_date = 69 [json_name = "DecDate"];
  string dec_place = 70 [json_name = "DecPlace"];
  string kyc_date = 71 [json_name = "KycDate"];
  string doc_sub = 72 [json_name = "DocSub"];
  string kyc_name = 73 [json_name = "KycName"];
  string kyc_designation = 74 [json_name = "KycDesignation"];
  string kyc_branch = 75 [json_name = "KycBranch"];
  string kyc_empcode = 76 [json_name = "KycEmpcode"];
  string org_name = 77 [json_name = "OrgName"];
  string org_code = 78 [json_name = "OrgCode"];
  string num_identity = 79 [json_name = "NumIdentity"];
  string num_related = 80 [json_name = "NumRelated"];
  string num_images = 81 [json_name = "NumImages"];
  string pan = 82 [json_name = "PAN"];
}

message IdentityDetails {
  repeated Identity identity = 1 [json_name = "Identity"];
}

message Identity {
  string sequence_no = 1 [json_name = "SequenceNo"];
  string ident_type = 2 [json_name = "IdentType"];
  string ident_num = 3 [json_name = "IdentNum"];
  string id_expiry_date = 4 [json_name = "IdExpiryDate"];
  string id_proof_submitted = 5 [json_name = "IdProofSubmitted"];
  string idver_status = 6 [json_name = "IdverStatus"];
}

message RelatedPersonDetails {
  repeated RelatedPerson related_person_data = 1 [json_name = "RelatedPerson"];
}

message RelatedPerson {
  string sequence_no = 1 [json_name = "SequenceNo"];
  string rel_type = 2 [json_name = "RelType"];
  string ckyc_no = 3 [json_name = "CkycNo"];
  string prefix = 4 [json_name = "Prefix"];
  string fname = 5 [json_name = "Fname"];
  string mname = 6 [json_name = "Mname"];
  string lname = 7 [json_name = "Lname"];
  string pan = 8 [json_name = "Pan"];
  string uid = 9 [json_name = "Uid"];
  string voterid = 10 [json_name = "Voterid"];
  string nrega = 11 [json_name = "Nrega"];
  string passport = 12 [json_name = "Passport"];
  string passport_exp = 13 [json_name = "PassportExp"];
  string driving_licence = 14 [json_name = "DrivingLicence"];
  string driving_exp = 15 [json_name = "DrivingExp"];
  string otherid_name = 16 [json_name = "OtheridName"];
  string otherid_no = 17 [json_name = "OtheridNo"];
  string dec_date = 18 [json_name = "DecDate"];
  string dec_place = 19 [json_name = "DecPlace"];
  string kyc_date = 20 [json_name = "KycDate"];
  string doc_sub = 21 [json_name = "DocSub"];
  string kyc_name = 22 [json_name = "KycName"];
  string kyc_designation = 23 [json_name = "KycDesignation"];
  string kyc_branch = 24 [json_name = "KycBranch"];
  string kyc_empcode = 25 [json_name = "KycEmpcode"];
  string orc_name = 26 [json_name = "OrcName"];
  string org_code = 27 [json_name = "OrgCode"];
}

message ImageDetails {
  repeated Image image = 1 [json_name = "Image"];
}

message Image {
  string sequence_no = 1 [json_name = "SequenceNo"];
  string image_type = 2 [json_name = "ImageType"];
  string image_code = 3 [json_name = "ImageCode"];
  string image_data = 4 [json_name = "ImageData"];
}

// Simulator service for Federal CKYC APIs.
// Reference Spec: https://drive.google.com/open?id=1f_hg-4n52qwvkkNKgPSZbznvshROU0vT
service Ckyc {
  rpc Search (SearchRequest) returns (SearchResponse) {
    option (google.api.http) = {
      post: "/ckyc/search"
      body: "*"
    };

  };
  rpc GetData (GetDataRequest) returns (GetDataResponse) {
    option (google.api.http) = {
      post: "/ckyc/download"
      body: "*"
    };
  };
}
