syntax = "proto3";

package casper;

import "api/casper/offer_catalog.proto";
import "api/casper/offer_type_specific_details.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/casper/redemption";
option java_package = "com.github.epifi.gamma.api.casper.redemption";

// RedemptionState defines the state of redemption of redeemed offer.
enum OfferRedemptionState {

  OFFER_REDEMPTION_STATE_UNSPECIFIED = 0;

  OFFER_REDEMPTION_INITIATED = 1;

  PURCHASED_OFFER_FROM_INVENTORY = 4;

  PURCHASE_FROM_INVENTORY_FAILED = 6;

  AMOUNT_DEBITED_FROM_ACCOUNT = 8;

  AMOUNT_DEBIT_FAILED = 10;

  VENDOR_REDEMPTION_SUCCESSFUL = 16;

  VENDOR_REDEMPTION_PENDING_UPDATE = 18;

  VENDOR_REDEMPTION_FAILED = 20;

  DEBITED_AMOUNT_REVERSED = 22;

  PURCHASE_FROM_INVENTORY_RETURNED = 24;

  OFFER_REDEMPTION_FAILED = 28;

  VENDOR_REDEMPTION_MANUAL_INTERVENTION = 29;

  OFFER_REDEMPTION_SUCCESSFUL = 32;
}


message RedeemedOffer {
  // uniquely represents a redeemed offer.
  string id = 1;

  // actor to whom the redeemed offer belongs
  string actor_id = 2;

  // redemption request id created during redemption initiation
  string redemption_request_id = 3;

  // identifier of the offer which was redeemed.
  // for eg if the user redeems a flipkart E-Voucher then we would have a offer_id
  // for flipkart E-Voucher which is same for all the redemptions of that voucher.
  string offer_id = 4;

  // type of offer
  casper.OfferType offer_type = 5;

  // state of redemption of redeemed offer
  OfferRedemptionState redemption_state = 6;

  // details of the redeemed offer, like coupon code, pin etc.
  message RedeemedOfferDetails {
    oneof offer_type_specific_details {
      EGiftCardDetails egift_card_details = 1;
      CharityDetails charity_details = 2;
      PhysicalMerchandiseDetails physical_merchandise_details = 3;
      PowerUpDetails power_up_details = 4;
      ThriweBenefitsPackageDetails thriwe_benefits_package_details = 5;
      VistaraAirMilesDetails vistara_air_miles_details = 6;
      CmsCouponDetails cms_coupon_details = 7;
      LoungeAccessDetails lounge_access_details = 9;
      ClubItcGreenPointsDetails club_itc_green_points_details = 10;
    }
    // key id used for encrypting redeemed offer details.
    string encryption_key_id = 8;
  }
  RedeemedOfferDetails redeemed_offer_details = 7;

  // external_id is equivalent to redeemed offer id, but this external_id can be displayed
  // on the app while Id shouldn't be due to security reasons.
  string external_id = 8;

  // price at which redemption was made
  float redemption_price = 9;

  // creation date
  google.protobuf.Timestamp created_at = 10;

  // time at which last update was made
  google.protobuf.Timestamp updated_at = 11;
  // expiry time of redeemed offer
  // can be nil for cases where there is no expiry for offer
  google.protobuf.Timestamp expires_at = 12;
}

enum RedeemedOfferFieldMask {
  RO_FIELD_MASK_UNSPECIFIED = 0;

  OFFER_DETAILS = 4;

  OFFER_EXPIRY_AT = 5;

  REDEMPTION_PRICE = 6;

  OFFER_REDEMPTION_STATE = 8;
}
