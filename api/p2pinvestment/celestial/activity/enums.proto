syntax = "proto3";

package p2pinvestment.celestial.activity;

option go_package = "github.com/epifi/gamma/api/p2pinvestment/celestial/activity";
option java_package = "com.github.epifi.gamma.api.p2pinvestment.celestial.activity";


enum MaturityStatus {
  MATURITY_STATUS_UNSPECIFIED = 0;
  // 'Pre-Maturity' is defined as T-1 of maturity.
  // 'BEFORE_PRE_MATURITY' denotes maturity date of transaction is more than 1 day away
  //  User can update the renewal action they took in this state
  MATURITY_STATUS_BEFORE_PRE_MATURITY = 1;
  // Maturity date is within 1 day away
  // User cannot update the renewal action they have taken
  MATURITY_STATUS_PRE_MATURITY = 2;
  // Maturity date has passed
  // transaction should have matured by now
  MATURITY_STATUS_MATURITY = 3;
}
