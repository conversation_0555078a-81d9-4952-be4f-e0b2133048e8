// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package search.auto_suggestions;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/image.proto";

option go_package = "github.com/epifi/gamma/api/search/auto_suggestions";
option java_package = "com.github.epifi.gamma.api.search.auto_suggestions";

// AutoSuggestions top-level message that serves as a container for one or more auto suggestion bundles.
message AutoSuggestions {
  repeated AutoSuggestionBundle auto_suggestion_bundles = 1;
}

// AutoSuggestionBundle represents a group of auto suggestions of the same type
message AutoSuggestionBundle {
  AutoSuggestionType type = 1;
  // rows is an optional field. This can be empty when type is AUTO_SUGGESTION_TYPE_CONTACTS
  repeated AutoSuggestionRow rows = 2;
}

// AutoSuggestionRow represents a single auto suggestion.
message AutoSuggestionRow {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text sub_title = 2;
  api.typesv2.common.Image primary_img = 3;
  api.typesv2.common.Image secondary_img = 4;
  frontend.deeplink.Deeplink deeplink = 5;
  // This hashed_phone_number will be an identifier for the clients
  // to de-dupe the contacts and the suggestions from the Backend.
  // This is not always present. Only required / present in
  // AUTO_SUGGESTION_TYPE_CONNECTIONS.
  string hashed_phone_number = 6;
}

// AutoSuggestionType is enumeration of possible types of auto suggestions.
enum AutoSuggestionType {
  AUTO_SUGGESTION_TYPE_UNSPECIFIED = 0;
  AUTO_SUGGESTION_TYPE_CONTACTS = 1;
  AUTO_SUGGESTION_TYPE_CONNECTIONS = 2;
  AUTO_SUGGESTION_TYPE_ASK_FI_QUERIES = 3;
  AUTO_SUGGESTION_TYPE_QUICK_LINKS = 4;
  AUTO_SUGGESTION_TYPE_FAQS = 5;
}

// AutoSuggestionsSource is enumeration of possible sources for auto suggestions.
enum AutoSuggestionsSource {
  AUTO_SUGGESTION_SOURCE_UNSPECIFIED = 0;
  AUTO_SUGGESTION_SOURCE_ASK_FI = 1;
  AUTO_SUGGESTION_SOURCE_PAY = 2;
  AUTO_SUGGESTION_SOURCE_INVEST = 3;
  AUTO_SUGGESTION_SOURCE_HELP_SUPPORT = 4;
}
