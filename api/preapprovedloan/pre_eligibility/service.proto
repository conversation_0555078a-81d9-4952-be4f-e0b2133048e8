syntax = "proto3";

package preapprovedloan.pre_eligibility;

import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/status.proto";
import "api/preapprovedloan/pre_eligibility_offer.proto";
import "api/preapprovedloan/loan_account.proto";
import "api/typesv2/money.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/employment_type.proto";
import "api/preapprovedloan/loan_offer.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan/pre_eligibility";
option java_package = "com.github.epifi.gamma.api.preapprovedloan.pre_eligibility";

// PreEligibility service handles the loan pre-approval process, including
// checking eligibility, collecting user data, and managing the evaluation flow.
service PreEligibility {
  // Retrieves loan-related details for the landing page, considering the pre-eligibility status.
  // Used to determine what to display on the initial loan page (offer, ongoing check, or existing loan).
  rpc GetLandingInfo (GetLandingInfoRequest) returns (GetLandingInfoResponse);

  // Determines the current status and next step in the pre-eligibility evaluation process.
  // Used to guide the user through the appropriate flow based on their evaluation status.
  rpc GetEvaluationStatus (GetEvaluationStatusRequest) returns (GetEvaluationStatusResponse);

  // Initiates a new pre-eligibility evaluation flow.
  // This creates a new request ID and begins the evaluation process.
  rpc InitiateEvaluation (InitiateEvaluationRequest) returns (InitiateEvaluationResponse);

  // Collects user data required for loan pre-eligibility processing.
  // This method is called multiple times with different data types during the evaluation flow.
  rpc CollectUserData (CollectUserDataRequest) returns (CollectUserDataResponse);
}

// Request to retrieve landing page details for a specific user.
message GetLandingInfoRequest {
  // Unique identifier of the user requesting loan information.
  string actor_id = 1;
}

// Response containing loan eligibility status, offer details, or ongoing evaluation information.
message GetLandingInfoResponse {
  // Status of the request, including success/failure and error codes if applicable.
  rpc.Status status = 1;

  // Only one of the following fields will be populated based on the user's current state.
  oneof data {
    // Active pre-eligible loan offer available to the user.
    // Present when the user has successfully completed evaluation and has an active pre-eligible offer.
    PreEligibilityOffer pre_eligibility_offer = 2;

    // Details of an ongoing pre-eligibility verification process.
    // Present when the user is in the middle of the evaluation flow and user not eligible for loan offer.
    EvaluationDetails evaluation_details = 3;

    // Indicates if the user has an active loan account.
    // Present when the user already has an active loan.
    LoanAccount loan_account = 4;

    // Indicates if the user has an active loan offer.
    LoanOffer loan_offer = 5;

  }
}

// Details of an ongoing pre-eligibility verification process.
message EvaluationDetails {
  // Unique identifier for the loan request associated with this pre-eligibility check.
  string request_id = 1;

  // Enumeration of possible evaluation status states for a user.
  enum EvaluationStatus {
    // Default undefined state, should not be used in responses.
    EVALUATION_STATUS_UNDEFINED = 0;

    // User is new and has not started the evaluation process yet.
    EVALUATION_STATUS_NEW_USER = 1;

    // User has started the evaluation process but has not completed all required steps.
    EVALUATION_STATUS_IN_PROGRESS = 2;

    // User has completed evaluation but was not found eligible for a loan offer.
    EVALUATION_STATUS_NOT_ELIGIBLE = 3;

    // User has completed evaluation and eligible for a loan offer.
    EVALUATION_STATUS_ELIGIBLE = 4;

    // User has completed a previous evaluation (regardless of outcome) and is
    // now eligible to start a new evaluation process (e.g., after cooling period, Expiry of current evaluation).
    EVALUATION_STATUS_ELIGIBLE_FOR_NEW_EVALUATION = 5;

    // For some users who already have an active or past bank account with us,
    // We need to stop it at the stage of actor detection itself and not proceed with further steps in web
    // To check the status they have to go to app
    EVALUATION_STATUS_GO_TO_APP_TO_CHECK_LATEST_STATUS = 6;
  }

  // Current evaluation status of the user.
  EvaluationStatus evaluation_status = 2;

  // Deeplink directing the user to the next required action in the evaluation flow.
  // This field is only present if the evaluation is in progress and the user needs to take further action.
  frontend.deeplink.Deeplink next_action = 4;

  PreEligibilityOffer pre_eligibility_offer = 5;
}

// Request to determine the current status and next step in the pre-eligibility process.
message GetEvaluationStatusRequest {
  // Unique identifier of the user.
  string actor_id = 1;

  // Unique Loan Request ID for the specific pre-eligibility process to check.
  string request_id = 2;
}

// Response specifying the current status and next action in the pre-eligibility process.
message GetEvaluationStatusResponse {
  // Status of the request, including success/failure and error codes if applicable.
  rpc.Status status = 1;

  // Details of the ongoing pre-eligibility verification process, including the next action.
  // Contains the evaluation status and a deeplink to the next step if applicable.
  EvaluationDetails evaluation_details = 2;
}

// Request to start a new loan pre-eligibility evaluation process.
message InitiateEvaluationRequest {
  // Unique identifier of the user requesting to start the evaluation.
  string actor_id = 1;
}

// Response containing details of the newly initiated pre-eligibility process.
message InitiateEvaluationResponse {
  // Status of the request, including success/failure and error codes if applicable.
  rpc.Status status = 1;

  // Details of the newly created evaluation process, including the request ID
  // and the first action to be taken.
  EvaluationDetails evaluation_details = 2;
}

// Request to collect various types of user details for loan pre-eligibility processing.
// This message is sent multiple times throughout the flow with different detail types.
message CollectUserDataRequest {
  // Unique identifier of the user.
  string actor_id = 1;

  // Loan Request ID for the ongoing pre-eligibility process.
  // This ties the submitted data to a specific evaluation flow.
  string request_id = 2;

  // User details required for eligibility assessment. Only one type is submitted per request.
  oneof details {
    // Basic identity information such as name, DOB, and PAN.
    BasicUserDetails basic_user_details = 3;

    // Employment and income information.
    EmploymentDetails employment_details = 4;

    // User consent information for credit checks and other requirements.
    ConsentDetails consent_details = 5;
  }

  // The screen from which this request originated, for analytics and tracking purposes.
  // Helps identify which part of the UI flow the user is currently in.
  frontend.deeplink.Screen deeplink_screen = 6;
}

// Response after collecting user data, providing direction to the next step in the process.
message CollectUserDataResponse {
  // Status of the request, including success/failure and error codes if applicable.
  rpc.Status status = 1;

  // Deeplink directing the user to the next required action in the evaluation flow.
  // For example, this might point to the next data collection screen or to a results page.
  frontend.deeplink.Deeplink next_action = 2;
}

// Basic identity information about the user, typically collected first in the evaluation flow.
message BasicUserDetails {
  // Full name of the user as it appears on their PAN card.
  // Must match official records for successful verification.
  api.typesv2.common.Name name = 1;

  // Date of birth of the user.
  // Used for identity verification and age eligibility checks.
  google.type.Date dob = 2;

  // Permanent Account Number (PAN) of the user.
  // Must be a valid 10-character alphanumeric PAN as per Indian standards.
  string pan = 3;
}

// Employment and income information for loan eligibility assessment.
message EmploymentDetails {
  // Employment type of the user (e.g., salaried, self-employed, business owner).
  // Different employment types may have different eligibility criteria.
  api.typesv2.EmploymentType employment_type = 1;

  // Monthly income of the user.
  // Used to determine loan amount eligibility and repayment capacity.
  api.typesv2.Money monthly_income = 2;

  // PIN code (postal index number) of the user's current residential address.
  // Used for address verification and location-based eligibility.
  string pin_code = 3;
}

// User consent information for various checks and processing requirements.
message ConsentDetails {
  // List of consent identifiers that the user has agreed to.
  // These may include consent for credit checks, data sharing, and other requirements.
  repeated string consent_ids = 1;
}
