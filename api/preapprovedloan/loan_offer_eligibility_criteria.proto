//go:generate gen_sql -types=LoanOfferEligibilityCriteria,VendorResponse,PolicyParams,DataRequirementDetails
syntax = "proto3";

package preapprovedloan;

import "api/preapprovedloan/enums.proto";
import "api/preapprovedloan/enums/form_data.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

message LoanOfferEligibilityCriteria {
  string id = 1;
  string actor_id = 2;
  Vendor vendor = 3;
  LoanOfferEligibilityCriteriaStatus status = 4;
  VendorResponse vendor_response = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp deleted_at = 8;
  string offer_id = 9;
  // for identifying the approved base from Fi for the same batch
  string batch_id = 10;
  // for identifying if vendor rejected the offer or fi rejected the offer
  LoanOfferEligibilityCriteriaSubStatus sub_status = 11;
  string loan_scheme = 12;
  LoanProgram loan_program = 13;
  // vendor_request_id is the unique request id sent to the vendor for checking user eligibility,
  // it should be different each time a scrub is sent to  the vendor, for moneyview vendor this id is used to create offers at their end.
  string vendor_request_id = 14;
  // will be used to store loan policy related parameters, for eg: we can store pricingScheme in this,
  // which corresponds to roi, pf etc for a batch of offer
  PolicyParams policy_params = 15;
  google.protobuf.Timestamp expired_at = 16;
  // this will be used to store data requirements of bre to generate final offer.
  // it contains list of data type (eg: cibil, aa, epfo) alongwith its data availability details (is_required, is_collected) required for offer generation of different policies
  // and loan request ids for tracking purpose
  DataRequirementDetails data_requirement_details = 17;
}

message VendorResponse {
  google.protobuf.Timestamp vendor_response_timestamp = 1;
  bytes raw_pre_bre_response = 2;
  bytes raw_final_bre_response = 3;
  string loan_id = 4;
  // we will apply for loan against this id for vendors like lenden etc.
  string reference_id = 5;
  bytes raw_vendor_bre_rejection_reasons = 6;
}

message PolicyParams {
  string pricing_scheme = 1;
  string ever_vkyc_attempted = 2;
  double pd_score = 3;
  string pd_score_version = 4;
  string scheme_id = 5;
  string pricing_scheme_bre = 6;
  string batch_id = 7;
  // new version of the policy_params has pre and final params separately which is live for non fi core subvention and non fi core stpl
  //  old version  has flat params which is live for fi core subvention and stpl
  Pre pre = 8;
  message Pre {
    double pd_score = 1;
    string pd_score_version = 2;
    string scheme_id = 3;
    string batch_id = 4;
    string ever_vkyc_attempted = 5;
    string pricing_scheme = 6;
  }
  Final final = 9;
  message Final {
    string scheme_id = 1;
    string batch_id = 2;
    string pricing_scheme_bre = 3;
  }
  ExecutionInfo execution_info = 10;
  DataInfo data_info = 11;
}

// contains information that is generated by BRE for consumption in subsequent calls
message ExecutionInfo {
  repeated PolicyParamsDetails pre = 1;
  repeated PolicyParamsDetails final = 2;
}

// common policy params for both pre-bre and final-bre
message PolicyParamsDetails {
  string batch_id = 1;
  string scheme_id = 2;
  string lending_program = 3;
  string pre_bre_request_id = 4;
  string final_bre_request_id = 5;
}

message DataInfo {
  AaData aa_data = 1;
  bool is_etb_user = 2;
  message AaData {
    double median_amount_salary_last_180_days = 1;
  }
  bool is_b2b_salary_user = 3;
  double monthly_income = 4;
  int32 months_since_salary_active = 5;
  int32 salary_credit_day = 6;

}

message DataRequirementDetails {
  repeated DataRequirement data_requirements = 1;
  string pre_screening_decision_lr_id = 2;
  string final_bre_decision_lr_id = 3;
  string vendor_bre_decision_lr_id = 4;
  // GetPreBreEligibilityDetails request id
  string pre_bre_request_id = 5;
  // GetFinalBreEligibilityDetails request id
  string final_bre_request_id = 6;
  // vendor returned bre reference id
  string vendor_bre_reference_id = 7;
  // desired_loan_amount is the loan amount requested by the user.
  google.type.Money desired_loan_amount = 8;
  // loan_purpose is the purpose for which the user is requesting the loan.
  preapprovedloan.enums.LoanPurpose loan_purpose = 9;
}

message DataRequirement {
  preapprovedloan.DataRequirementType data_requirement_type = 1;
  // deprecated. we can assume that this data is required if it is present in the list
  bool is_required = 2 [deprecated = true];
  bool is_collected = 3;
  oneof Data {
    PreBreDataLoanPreferences pre_bre_data_loan_preferences = 4;
    PreBreConsent pre_bre_consent = 5;
    // if vendor wants us to fetch AA data then we send them and store the bank account details here
    AaAnalysisBankDetails aa_analysis_bank_details = 6;
  }

  message AaAnalysisBankDetails {
    string account_holder_name = 1;
    string account_number = 2;
    string ifsc = 3;
    string type = 4;
    string bank_name = 5;
  }

  message PreBreDataLoanPreferences {
    int64 loan_amount = 1;
    int64 interest = 2;
  }

  message PreBreConsent {
    // as returned by consent client after taking consents.
    repeated string consent_ids = 1;
    // the client request id against which we store consents
    string request_id = 2;
  }
}

