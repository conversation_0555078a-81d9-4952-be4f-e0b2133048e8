syntax = "proto3";

package preapprovedloan.cx;

import "api/persistentqueue/queue_element.proto";
import "api/preapprovedloan/cx/enums.proto";
import "api/preapprovedloan/enums.proto";
import "api/preapprovedloan/loan_account.proto";
import "api/preapprovedloan/loan_applicant.proto";
import "api/preapprovedloan/loan_installment_payout.proto";
import "api/preapprovedloan/loan_offer.proto";
import "api/preapprovedloan/loan_payment_request.proto";
import "api/preapprovedloan/loan_request.proto";
import "api/preapprovedloan/loan_step_execution.proto";
import "api/rpc/status.proto";
import "api/typesv2/bank_account_details.proto";
import "api/vendors/fiftyfin/loan_servicing.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan/cx";
option java_package = "com.github.epifi.gamma.api.preapprovedloan.cx";

service Cx {
  // update loan step execution records. This is to be used only via dev actions and that too for Op agents to approve/reject
  // liveness or facematch manual review. Other than this step executions are expected to be updated via workflows.
  rpc SubmitManualReview (SubmitManualReviewRequest) returns (SubmitManualReviewResponse) {}
  // RPC to be used to get details of all the loan offers for an actor
  rpc GetLoanOfferSummary (GetLoanOfferSummaryRequest) returns (GetLoanOfferSummaryResponse) {}
  // RPC to be used to get details of all the loan requests for an actor
  rpc GetLoanRequestSummary (GetLoanRequestSummaryRequest) returns (GetLoanRequestSummaryResponse) {}
  // RPC to be used to get details of all the loan account for an actor
  rpc GetLoanAccountSummary (GetLoanAccountSummaryRequest) returns (GetLoanAccountSummaryResponse) {}
  // RPC to be used to get basic loan details for an actor
  rpc GetLoanUserDetails (GetLoanUserDetailsRequest) returns (GetLoanUserDetailsResponse) {}
  // RPC to be used to get details of loan availed by an actor
  rpc GetLoanDetails (GetLoanDetailsRequest) returns (GetLoanDetailsResponse) {}
  // rpc to fetch the elements in the persistent queue based on a specific payload type
  rpc GetQueueElements (GetQueueElementsRequest) returns (GetQueueElementsResponse);
  // rpc to update Loan Request status and sub status
  rpc MarkLoanRequestCancel (MarkLoanRequestCancelRequest) returns (MarkLoanRequestCancelResponse);
  // rpc to return fetch foreclosure details of a user's loan account
  rpc GetForeclosureDetails (GetForeclosureRequest) returns (GetForeclosureResponse);
  // This rpc is used for fetching additional details related to loan request table rows
  rpc GetLoanRequestAdditionalDetails(GetLoanRequestAdditionalDetailsRequest) returns (GetLoanRequestAdditionalDetailsResponse);
  // This rpc is used for fetching additional details related to loan account table rows
  rpc GetLoanAccountAdditionalDetails(GetLoanAccountAdditionalDetailsRequest) returns (GetLoanAccountAdditionalDetailsResponse);
  // RPC to update expired_at for loan offer eligibility criteria
  rpc ExpireLoec(ExpireLoecRequest) returns (ExpireLoecResponse);
}

message SubmitManualReviewRequest {
  string actor_id = 1;
  string orch_id = 2;
  Verdict verdict = 3;
  ReviewerDetails reviewer_details = 4;
  message ReviewerDetails {
    string email = 1;
    google.protobuf.Timestamp reviewed_at = 2;
    string reason = 3;
  }
}

message SubmitManualReviewResponse {
  rpc.Status status = 1;
}

message GetLoanOfferSummaryRequest {
  string actor_id = 1;
  Vendor vendor = 2;
}

message GetLoanOfferSummaryResponse {
  // rpc response status
  rpc.Status status = 1;
  LoanOfferForCX loanOffer = 2;
  message LoanOfferForCX {
    string vendor_offer_id = 1;
    preapprovedloan.Vendor vendor = 2;
    preapprovedloan.OfferConstraints offer_constraints = 3;
    preapprovedloan.OfferProcessingInfo processing_info = 4;
  }
}

message GetLoanRequestSummaryRequest {
  string actor_id = 1;
  Vendor vendor = 2;
}

message GetLoanRequestSummaryResponse {
  // rpc response status
  rpc.Status status = 1;
  repeated LoanRequestForCX loanRequests = 2;
  message LoanRequestForCX {
    string offer_id = 1;
    string loan_account_id = 2;
    string vendor_request_id = 3;
    preapprovedloan.Vendor vendor = 4;
    preapprovedloan.LoanRequestDetails details = 5;
    preapprovedloan.LoanRequestType type = 6;
    preapprovedloan.LoanRequestStatus status = 7;
    preapprovedloan.LoanRequestSubStatus sub_status = 8;
  }
}

message GetLoanAccountSummaryRequest {
  string actor_id = 1;
  Vendor vendor = 2;
}

message GetLoanAccountSummaryResponse {
  // rpc response status
  rpc.Status status = 1;
  repeated LoanAccountForCX loanAccounts = 2;
  message LoanAccountForCX {
    string account_number = 1;
    preapprovedloan.Vendor vendor = 2;
    string ifsc_code = 3;
    preapprovedloan.LoanType loan_type = 4;
    preapprovedloan.LoanAmountInfo loan_amount_info = 5;
    google.type.Date loan_end_date = 6;
    google.type.Date maturity_date = 7;
    preapprovedloan.LoanAccountDetails details = 8;
    preapprovedloan.LoanAccountStatus status = 9;
    preapprovedloan.LoanProgram loan_program = 10;
  }
}

message GetLoanUserDetailsRequest {
  string actor_id = 1;
  LoanHeader loan_header = 2;
}

message GetLoanUserDetailsResponse {
  // rpc response status
  rpc.Status status = 1;
  CurrentMonthUserLoanEligibilityDetails current_month_user_loan_eligibility_details = 2;
  CurrentUserLoanApplicationDetails current_user_loan_application_details = 3;
  repeated LoanOfferDetails loan_offer_details = 4;
  repeated preapprovedloan.LoanRequest eligibility_loan_requests = 5;
  repeated preapprovedloan.LoanRequest application_loan_requests = 6;
  repeated preapprovedloan.LoanApplicant applicants = 7;

  message CurrentMonthUserLoanEligibilityDetails {
    bool is_user_suggested_for_loan = 1;
    string reason_for_ineligibility = 2;
  }
  message CurrentUserLoanApplicationDetails {
    string loan_application_status = 1;
    string loan_application_sub_status = 2;
    google.type.Money loan_amount_applied = 3;
    int32 tenure_applied_months = 4;
    string vendor = 5;
    string loan_program = 6;
  }
  message LoanOfferDetails {
    string loan_offer_id = 1;
    google.type.Money min_loan_amount = 2;
    google.type.Money max_loan_amount = 3;
    google.type.Money max_emi_amount = 4;
    double interest = 5;
    int32 min_tenure = 6;
    int32 max_tenure = 7;
    google.type.Date offer_start_date = 8;
    google.type.Date offer_end_date = 9;
    string agent_input_user_feedback = 10;
    string vendor = 11;
    string loan_program = 12;
    // this field will contain additional constraint details
    oneof additional_constraints {
      // constraints specified by FIFTYFIN vendor for LAMF loan program
      FiftyFinLamfConstraintInfo fiftyfin_lamf_constraint_info = 13;
    }
    LoanOfferType loan_offer_type = 20;
  }
}

message GetLoanDetailsRequest {
  string actor_id = 1;
  LoanHeader loan_header = 2;
}

message GetLoanDetailsResponse {
  // rpc response status
  rpc.Status status = 1;
  repeated LoanDetailsForCx loan_details = 2;

  message LoanDetailsForCx {
    string loan_account_number = 1;
    google.protobuf.Timestamp loan_open_date = 2;
    google.type.Money loan_amount = 3;
    double interest_rate = 4;
    int32 tenure_in_months = 5;
    google.type.Money processing_fee = 6;
    google.type.Money outstanding_amount = 7;
    google.type.Money gst = 8;
    google.type.Money broken_period_interest = 9;
    google.type.Money pre_closure_fee = 10;
    repeated LoanPastTransaction loan_past_transactions = 11;
    repeated LoanUpcomingEmi loan_upcoming_emis = 12;
    string vendor = 13;
    string loan_program = 14;
    repeated MfPledgeDetails mf_pledge_details = 15;
    message LoanPastTransaction {
      google.protobuf.Timestamp timestamp = 1;
      string transaction_type = 2;
      string status = 3;
      string order_id = 4;
      string fi_utr_number = 5;
      string loan_account_number = 6;
      google.type.Money amount = 7;
      string mode_of_payment = 8;
      google.type.Money charges = 9;
    }

    message LoanUpcomingEmi {
      google.type.Date next_emi_date = 1;
      string loan_account_number = 2;
      google.type.Money amount = 3;
    }

    message MfPledgeDetails {
      string isin = 1;
      double units = 2;
    }
    MandateAccount mandate_account = 16;
    api.typesv2.BankAccountDetails bank_account_details = 17;
    LoanAccount loan_account = 18;
    ForeclosureDetails fore_closure_details = 19;
  }
}

message ForeclosureDetails {
  google.type.Money total_outstanding_amount = 1;
  google.type.Money principal_outstanding_amount = 2;
  google.type.Money interest_outstanding_amount = 3;
  google.type.Money penalty_amt = 4;
  google.type.Money fees_amt = 5;
  google.type.Money other_charges = 6;
}

message GetQueueElementsRequest {
  // type of payload required from the queue
  persistentqueue.PayloadType payload_type = 1;
  // Number of elements required from the queue, max supported is 100
  int32 limit = 2;
  int32 page_num = 3;
  // From time for date filter
  google.protobuf.Timestamp from_time = 4;
  // To time for date filter
  google.protobuf.Timestamp to_time = 5;
}

message GetQueueElementsResponse {
  rpc.Status status = 1;
  // queue elemnet is either for liveness or facematch
  repeated QueueElement elements = 2;
  message QueueElement {
    string id = 1;
    oneof payload {
      persistentqueue.LivenessReview liveness_review = 2;
      persistentqueue.FacematchReview facematch_review = 3;
    }
  }
}

message MarkLoanRequestCancelRequest {
  string loan_request_id = 1;
}

message MarkLoanRequestCancelResponse {
  rpc.Status status = 1;
}

message MandateAccount {
  // Masked account no of mandate account. Only last 4 digits of account number will be exposed
  string masked_acc_no = 1;
  string bank_name = 2;
}

message GetForeclosureRequest {
  string loan_account_number = 1;
  LoanHeader loan_header = 2;
}

message GetForeclosureResponse {
  rpc.Status status = 1;
  google.type.Money total_outstanding_amt = 2;
  google.type.Money principal_outstanding_amt = 3;
  google.type.Money interest_outstanding_amt = 4;
  google.type.Money penalty_amt = 5;
  google.type.Money fees_amt = 6;
  google.type.Money other_charges = 7;
}

message GetLoanAccountAdditionalDetailsRequest {
  LoanHeader loan_header = 1;
  string account_id = 2;
  // Common Data will be returned based on the given field masks. If no field mask is given then no common data will be given.
  repeated LoanAccountAdditionalDetailsFieldMask field_masks = 3;
}

message GetLoanAccountAdditionalDetailsResponse {
  rpc.Status status = 1;
  LoanAccount loan_account = 2;
  repeated LoanPaymentRequest loan_payment_requests = 3;
  repeated LoanInstallmentPayout installment_payouts = 4;
  repeated LoanRequest loan_account_closure_requests = 5;
  oneof details {
    FiftyfinLamfLoanAccountDetails fiftyfin_lamf_details = 6;
  }
  repeated LoanUpcomingEmi loan_upcoming_emis = 7;
  api.typesv2.BankAccountDetails bank_account_details = 17;
}

message LoanUpcomingEmi {
  google.type.Date next_emi_date = 1;
  string loan_account_number = 2;
  google.type.Money amount = 3;
}

message FiftyfinLamfLoanAccountDetails {
  LoanStatement soa = 1;
  bool is_loan_account_closed_on_vendor = 2;
  message LoanStatement {
    repeated vendors.fiftyfin.LoanStatementTxn transactions = 1;
  }
}

message GetLoanRequestAdditionalDetailsRequest {
  LoanHeader loan_header = 1;
  string loan_request_id = 2;
  // Common Data will be returned based on the given field masks. If no field mask is given then no common data will be given.
  repeated LoanRequestAdditionalDetailsFieldMask field_masks = 3;
}

message GetLoanRequestAdditionalDetailsResponse {
  rpc.Status status = 1;
  LoanRequest loan_request = 2;
  repeated LoanStepExecution loan_steps = 3;
}

message ExpireLoecRequest {
  repeated string loec_ids = 1;
  preapprovedloan.Vendor vendor = 2;
}

message ExpireLoecResponse {
  rpc.Status status = 1;
  int32 rows_affected = 2;
}
