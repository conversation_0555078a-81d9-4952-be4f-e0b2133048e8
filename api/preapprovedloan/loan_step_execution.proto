syntax = "proto3";

package preapprovedloan;

import "api/preapprovedloan/collections.proto";
import "api/preapprovedloan/enums.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/money.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

//go:generate gen_sql -types=LoanStepExecution,LoanStepExecutionDetails,ListOfString,ReviewerDetails
message LoanStepExecution {
  string id = 1;
  string actor_id = 2;
  // references LoanRequest.Id
  string ref_id = 3;
  LoanStepExecutionFlow flow = 4;
  string orch_id = 5;
  LoanStepExecutionStepName step_name = 6;
  LoanStepExecutionDetails details = 7;
  LoanStepExecutionStatus status = 8;
  LoanStepExecutionSubStatus sub_status = 9;
  google.protobuf.Timestamp staled_at = 10;
  google.protobuf.Timestamp completed_at = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  google.protobuf.Timestamp deleted_at = 14;
  GroupStage group_stage = 15;
}

message LivenessStepData {
  string attempt_id = 1;
  string otp = 2;
  map<string, ListOfString> notification_type_id_map = 3;
}

message FaceMatchStepData {
  string attempt_id = 1;
}

message ESignStepData {
  // pre-sign-url for storing the e-sign agreement if there is only one document to be signed
  string sign_url = 1;
  google.protobuf.Timestamp expiry_at = 2;
  map<string, ListOfString> notification_type_id_map = 3;
  OtpInfo otp_info = 4;
  string document_id = 5;
  // path of e-sign agreement pdf stored in s3
  string aws_destination_path = 6;

  /* Below are the fields to be used when there are multiple documents to be signed */
  // Used for storing kfs document details
  LoanDocument kfsDocument = 7;
  // Used for storing loan agreement document details
  LoanDocument loanAgreementDocument = 8;

  // The time till which ROI can be modified
  google.protobuf.Timestamp roi_modification_deadline = 9;
}

message LoanDocument {
  // store pre-sign url in s3
  string sign_url = 1;
  // path of e-sign agreement pdf stored in s3
  string aws_destination_path = 2;
  // expiry fo pre-sign url
  google.protobuf.Timestamp expiry_at = 3;

  // Path inside a S3 bucket at which the lender-signed document is stored
  string signed_doc_s3_path = 4;
}

message OtpInfo {
  string otp = 1;
  int32 max_attempts = 2;
  int32 attempts_count = 3;
  // In case of failure, last entered otp becomes null
  // In case of success, update last entered otp
  string last_entered_otp = 4;
  // For OTPs generated internally, this will store the latest auth token corresponding to which OTP needs to be verified.
  string token = 5;
}

message VkycStepData {
  string notification_id = 1 [deprecated = true];
  map<string, ListOfString> notification_type_id_map = 2;
  string application_id = 3;
  string call_id = 4;
}

// used to store data when kyc is done by vendor
message KycStepData {
  // vendor's kyc url
  string kyc_url = 1;
  // unique id to track kyc on vendor's end
  string kyc_tracking_id = 2;
  // denotes the approximate time at which we received the KYC URL from vendor
  google.protobuf.Timestamp url_generated_at = 3;
}

// used when kyc is done on LSP side
message CkycStepData {
  string ckyc_image_path = 1;
  api.typesv2.EmploymentType employment_type = 2;
  string ckyc_id = 3;
  CkycPositiveConfirmationStatus positive_confirmation = 4;
  string PAN = 5;
  api.typesv2.common.Name name = 6;
  api.typesv2.Gender gender = 7;
  google.type.Date dob = 8;
  api.typesv2.PostalAddress permanent_address = 9;
  api.typesv2.PostalAddress correspondence_address = 10;
  api.typesv2.common.Name fathers_name = 11;
  api.typesv2.common.Name mothers_name = 12;
  string vendor_kyc_id = 15;
  // this address comes from CKYC from one of the ovd documents list like Aadhaar, Voter Id, DL, Passport etc.
  api.typesv2.PostalAddress kyc_address = 17;
  // properties applicable only for specific vendors
  oneof vendor_specific_details {
    // applicable only for Vendor_IDFC
    IdfcCkycStepData idfc = 13;
    // applicable only for Vendor_ABFL
    AbflCkycStepData abfl = 14;
    // applicable only for Vendor_STOCK_GUARDIAN_LSP
    SgCkycStepData sg_data = 16;
  }
}

message AbflMandateData {
  string rp_cust_id = 1;
  string short_url = 2;
}

message SgMandateData {
  string orch_id = 1;
}

message FedMandateData {
  repeated string mandate_attempt_orch_id_list = 1;
}

message LLMandateData {
  string mandate_id = 1;
  int32 mandate_attempt = 2;
  // denotes the latest time at which mandate was attempted by the user
  google.protobuf.Timestamp latest_mandate_attempt_timestamp = 3;
}

message IdfcCkycStepData {
  IdfcCkycAddressPinCodeType address_pin_code_type = 1;
}

message SgCkycStepData {
  bool is_kyc_already_done = 1;
}

message AbflCkycStepData {
  string request_id = 1;
  string aadhaar_base64_image = 2;
  string photograph_base64_image = 3;
  string ekyc_auth_base64_image = 4;
  string pan_base64_image = 5;
  string driving_licence_base64_image = 6;
  string signature_base64_image = 7;
  string voter_id_base64_image = 8;
  string passport_base64_image = 9;
  api.typesv2.PostalAddress permanent_address = 10;
  api.typesv2.PostalAddress corresponding_address = 11;
  string pan = 12;
  api.typesv2.common.Name full_name = 13;
  google.type.Date dob = 14;
  string ckyc_id = 15;
}

message ManualReviewStepData {
  string notification_id = 1 [deprecated = true];
  map<string, ListOfString> notification_type_id_map = 2;
  string reason = 3;
  ReviewerDetails reviewer_details = 4;
  message ReviewerDetails {
    string email = 1;
    google.protobuf.Timestamp reviewed_at = 2;
  }
}

message OnboardingData {
  AddressDetails address_details = 1;
  EmploymentDetails employment_details = 2;
  BankingDetails banking_details = 3;

  message AddressDetails {
    api.typesv2.PostalAddress address_details = 1;
    api.typesv2.AddressType address_type = 2;
    // location token we get from client side when adding address details.
    string location_token = 3;
    api.typesv2.ResidenceType residence_type = 4;
    // applicable only for rental and PG residence type
    google.type.Money monthly_rent = 5;
  }
  message EmploymentDetails {
    api.typesv2.EmploymentType occupation = 1;
    string organization_name = 2;
    google.type.Money monthly_income = 3;
    string work_email = 4;
    api.typesv2.PostalAddress office_address = 5;
    google.type.Money annual_revenue = 6;
    string GSTIN = 7;
  }
  message BankingDetails {
    string account_number = 1;
    string account_holder_name = 2;
    string ifsc_code = 3;
    string bank_name = 4;
  }
}

message MandateData {
  string url = 1;
  string recurring_payment_id = 2;
  string merchant_txn_id = 3;
  // maximum amount we can deduct over one single transaction of repayment
  google.type.Money max_txn_amount = 4;
  // denotes the time before which user needs to complete mandate
  google.protobuf.Timestamp mandate_link_expiry = 5;
  // denotes the approximate time at which we received the URL to setup auto-repayment mandate for a loan
  google.protobuf.Timestamp url_generated_at = 9;

  oneof vendor_specific_details {
    AbflMandateData abfl = 14;
    LLMandateData ll_mandate_data = 15;
    SgMandateData sg_mandate_data = 16;
    FedMandateData fed_mandate_data = 17;
  }

  BankingDetails banking_details = 6;
  // denotes whether a user is not eligible to set up mandate with fi-federal bank account
  bool mandate_with_fi_account_not_allowed = 7;
  FiAccountIneligibleForMandateReason fi_account_ineligible_for_mandate_reason = 8;

  // FiAccountIneligibleForMandateReason denotes the list of reasons why user is not allowed to setup the mandate on
  // fi-federal savings account. This info can be used to decide the appropriate flow/screens for the user
  enum FiAccountIneligibleForMandateReason {
    FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_UNSPECIFIED = 0;
    FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_MIN_KYC_EXPIRY = 1;
    FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_RE_KYC_DUE = 2;
    FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_ACCOUNT_NOT_OPERATIONAL = 3;
    FI_ACCOUNT_INELIGIBLE_FOR_MANDATE_REASON_DEFAULT_ACCOUNT_SETTING_NOT_NEEDED = 4;
  }

  message BankingDetails {
    message AccountDetails {
      string account_number = 1;
      string account_holder_name = 2;
      string ifsc_code = 3;
      string bank_name = 4;
    }

    // list of accounts used by the user during the mandate setup flow
    // by default federal account will be populated
    // user can choose to add a new account, details should be reflected in this map
    map<string, AccountDetails> alternate_acc_details = 1;

    // acc details which was finally used/selected by the user
    // and was also sent to vendor
    // this acc can be Fi-Federal account or non Fi-Federal account
    AccountDetails final_acc_details_used = 2;
  }
}

message LoanStepExecutionDetails {
  oneof Details {
    LivenessStepData liveness_step_data = 1;
    FaceMatchStepData face_match_step_data = 2;
    ESignStepData e_sign_step_data = 3;
    VkycStepData vkyc_step_data = 4;
    ManualReviewStepData manual_review_step_data = 5;
    OnboardingData onboarding_data = 6;
    MandateData mandate_data = 7;
    HunterData hunter_data = 8;
    CkycStepData ckyc_step_data = 9;
    CollectionData collection_data = 10;
    ApplicantData applicant_data = 11;
    OtpVerificationData otp_verification_data = 12;
    BreData bre_data = 13;
    SelfieData selfie_data = 14;
    CreateLeadStepData create_lead_step_data = 17;
    VendorPWAStagesStepData vendor_pwa_stages_step_data = 18;
    KycStepData kyc_step_data = 19;
    // intentionally keeping it 23 to acccomodate for more vendor specific details
    IncomeEstimateData income_estimate_data = 23;
    AadhaarData aadhaar_data = 24;
    LoanDetailsVerificationData loan_details_verification_data = 25;
    ResetVendorLoanApplicationData reset_vendor_loan_application_data = 26;
    ContactabilityDetailsData contactability_details_data = 27;
    VendorIdentifiersData vendor_identifiers_data = 28;
    ROIModificationData roi_modification_data = 29;
    PreEligibilityData pre_eligibility_data = 30;
  }

  // intentionally marked as 15 to accommodate the oneof fields in case they increase
  string vendor_data = 15;
  oneof vendor_specific_details {
    // applicable only for Vendor_ABFL
    AbflLoanDisbursementData abfl = 16;
  }
}

// This data field is created to store the pre-eligibility flow data
message PreEligibilityData {
  int64 credit_report_attempt_count = 1;
}

message AadhaarData {
  oneof vendor_specific_details {
    AbflAadhaarData abfl = 1;
  }
  string last_four_digit = 2;
}

message AbflAadhaarData {
  string profile_id = 1;
  string partner_req_id = 2;
  string capture_link = 3;
  api.typesv2.PostalAddress address = 4;
  int32 age = 5;
  api.typesv2.Gender gender = 6;
  api.typesv2.common.Name name = 7;
  string street_address = 8;
  google.type.Date dob = 9;
}

message IncomeEstimateData {
  api.typesv2.Money predicted_income = 1;
  float confidence = 2;
  string raw_response = 3;
  IncomeDataSource income_data_source = 4;
  rpc.Status income_estimator_resp_status = 5;
  ItrIncomeInfo itr_income_info = 6;
}

enum IncomeDataSource {
  INCOME_DATA_SOURCE_UNSPECIFIED = 0;
  INCOME_DATA_SOURCE_AA = 1;
  INCOME_DATA_SOURCE_ITR_INTIMATION = 2;
}

message ItrIncomeInfo {
  repeated ItrAttemptData itr_attempt_data = 1;
  message ItrAttemptData {
    string attempt_id = 1;
    ItrStatus itr_status = 2;
  }
  ItrIntimationData itr_intimation = 2;
  message ItrIntimationData {
    // name of the taxpayer
    api.typesv2.common.Name name = 1;
    // phone number of the taxpayer
    api.typesv2.common.PhoneNumber phone_number = 2;
    // pan number of the taxpayer
    string pan = 3;
    // gross total income provided by the taxpayer
    api.typesv2.Money gross_total_income_provided = 4;
    // gross total income computed under section 143(1)
    api.typesv2.Money gross_total_income_computed = 5;
    // total income provided by the taxpayer
    api.typesv2.Money total_income_provided = 6;
    // total income computed under section 143(1)
    api.typesv2.Money total_income_computed = 7;
    google.protobuf.Timestamp date_of_filing = 8;
    string assessment_year = 9;
    ItrFormType itr_form_type = 10;
    // status of the taxpayer - "Individual"
    ItrTaxpayerStatus taxpayer_status = 11;
    // residential status of the taxpayer - "Resident"
    ItrTaxpayerResidentialStatus residential_status = 12;
  }
  enum ItrStatus {
    ITR_STATUS_UNSPECIFIED = 0;
    ITR_STATUS_PENDING = 1;
    ITR_STATUS_FAILED = 2;
    ITR_STATUS_SUCCESS = 3;
    ITR_STATUS_NOT_FOUND = 4;
    ITR_STATUS_PAN_MISMATCH = 5;
    ITR_STATUS_IN_PROGRESS = 6;
  }
}

message AbflLoanDisbursementData {
  string loan_unique_id = 1;
  string loan_number = 2;
  string deal_number = 3;
}

message ListOfString {
  repeated string values = 1;
}

message HunterData {
  repeated string rules = 1;
  int64 score = 2;
  string rule = 3;
}

message CollectionData {
  repeated RepaymentBreakupData repayment_breakup = 1;
}

message ApplicantData {
  string pan = 1;
  google.type.Date dob = 2;
  api.typesv2.common.PhoneNumber phone_number = 3;
  string email = 4;
  string employment_type = 5;
  string marital_status = 6;
  string residence_type = 7;
  string father_name = 8;
  string mother_name = 9;
  repeated Reference references = 10;
  string purpose_of_loan = 11;
  google.type.Money desired_loan_amount = 12;
}

message OtpVerificationData {
  repeated OtpData otp_data = 1;
  // flag to instruct otp verification processes to skip
  // verifying remaining otps
  bool skip_remaining_otps = 2;
  // flag to signal worker that otp verification is in progress.
  bool otp_verification_in_progress = 3;
  // timestamp when otp verification was last started. will be used by worker
  // to determine if the verification process has expired.
  google.protobuf.Timestamp otp_verification_started_at = 4;
  message OtpData {
    string token = 1;
    OtpType otp_type = 2;
    OtpStatus otp_status = 3;
    api.typesv2.common.PhoneNumber phone_number = 4;
    string email = 5;
    string vendor_session_id = 6;
    // used to store the asset details that are to be pledged after otp verification
    map<string, double> assetDetails = 7;
    // can be used to specify conditions that are to be run before starting otp generation.
    // if field is present and evaluates to false then otp is skipped.
    Condition otp_validation_condition = 8;
    // serial number for the otp
    int32 otp_serial_no = 9;
  }
}

// Condition represents expression that evaluate to a boolean value
message Condition {
  oneof condition {
    OtpStatusCondition otp_status_condition = 1;
    NotCondition not_condition = 2;
  }
}

// OtpStatusCondition checks if otp status is present in `statuses` array.
message OtpStatusCondition {
  string otp_id = 1;
  repeated OtpStatus statuses = 2;
}

// returns NOT of base_condition outcome
message NotCondition {
  Condition base_condition = 1;
}

message BreData {
  bool is_epfo_data_needed = 1;
  // Generic identifier field. Can be used differently for all the vendors
  // This id could be used to fetch bre data against. Or can be used as an identifier from CKYC to be stored and used in further flow
  string identifier = 2;
}

message SelfieData {
  api.typesv2.common.Image selfie_image = 1;
}

message Reference {
  api.typesv2.common.Name name = 1;
  api.typesv2.common.PhoneNumber phone_number = 2;
}

message CreateLeadStepData {
  // denotes the id of lead which got created at create_lead step.
  string lead_id = 1;
  // denotes the user's pan number shared with the vendor.
  string pan = 2;
  // denotes the user's name shared with the vendor.
  api.typesv2.common.Name name = 3;
  // denotes the user's phone-number shared with the vendor.
  api.typesv2.common.PhoneNumber phone_number = 4;
  api.typesv2.Gender gender = 5;
  // denotes the user's date of birth shared with the vendor.
  google.type.Date dob = 6;
  // denotes the user's employment type shared with the vendor.
  string employment_type = 7;
  // denotes the user's declared income shared with the vendor.
  google.type.Money declared_income = 8;
  // denotes the user's current address shared with the vendor.
  api.typesv2.PostalAddress current_address = 9;
  // denotes the user's email sent shared with the vendor.
  string email = 10;
}

message VendorPWAStagesStepData {
  // denotes info of stages which were initiated, completed or failed in vendor pwa flow.
  repeated StageInfo stage_infos = 1;
  // string pwa url here to redirect user to vendor pwa flow.
  // with the expiry of the url.
  string pwa_url = 2;
  google.protobuf.Timestamp pwa_url_expiry_time = 3;

  enum PWAStage {
    PWA_STAGE_UNSPECIFIED = 0;
    PWA_STAGE_MANDATE = 1;
    PWA_STAGE_OFFER_SELECTION = 2;
    PWA_STAGE_OFFER_REFRESH = 3;
    PWA_STAGE_KYC = 4;
    PWA_STAGE_ADD_BANK_DETAILS = 5;
    PWA_STAGE_COMPLIANCE_REVIEW = 6;
    PWA_STAGE_INCOME_VERIFICATION = 7;
    PWA_STAGE_AGREEMENT_SIGNING = 8;
    PWA_STAGE_APPLICATION_SUBMISSION = 9;
    PWA_STAGE_APPLICATION_RESUBMISSION = 10;
    PWA_STAGE_OFFER_RECEIVED = 11;
    PWA_STAGE_OFFER_GENERATION = 12;
    PWA_STAGE_USER_REGISTRATION = 13;
    PWA_STAGE_DISBURSAL = 14;
  }
  enum PWAStageStatus {
    PWA_STAGE_STATUS_UNSPECIFIED = 0;
    PWA_STAGE_STATUS_INITIATED = 1;
    PWA_STAGE_STATUS_COMPLETED = 2;
    PWA_STAGE_STATUS_FAILED = 3;
  }
  message StageInfo {
    // denotes the pwa stage name
    PWAStage stage_name = 1;
    // denotes the current status of pwa stage.
    PWAStageStatus stage_status = 2;
    // denotes when the stage status was updated in our system.
    // **Note** : this does not denotes stage update time at vendor's end.
    google.protobuf.Timestamp updated_at = 3;
    // denoted the exact stage name which is being piped from Vendor itself
    string vendor_stage_name = 4;
    // denotes the vendor updated_at timestamp for the event received
    google.protobuf.Timestamp vendor_updated_at = 5;
  }
}

message LoanDetailsVerificationData {
  oneof data {
    LamfLoanDetailsVerificationData lamf = 1;
  }
}

message LamfLoanDetailsVerificationData {
  bool user_action_taken = 1;
  repeated PfFetchData pf_fetch_Data = 2;
  // Mobile link details contains list of folios and the mobile number to which the folios need to update
  MfMobileLinkDetails mobileLinkDetails = 3;
  // Email link details contains list of folios and the email to which the folios need to update
  MfEmailLinkDetails emailLinkDetails = 4;
  UserAction user_action = 5;

  message PfFetchData {
    string req_id = 1;
    bool fetch_completed = 2;
    bool is_fetch_success = 3;
    google.protobuf.Timestamp completion_time = 4;
  }
  message MfMobileLinkDetails {
    // The phone number of all the folios will be updated to this number (given that they are linked to the below email)
    api.typesv2.common.PhoneNumber new_mobile = 1;
    // all the folios should be linked to this email. This will be used for user authentication.
    string linked_email = 2;
    repeated FolioData folios = 3;
    // list of nft req ids for tracking the nft loan request and its history
    repeated NftRequestDetails nft_req_details = 4;
  }
  message MfEmailLinkDetails {
    // The email of all the folios will be updated to this new email (given that they are linked to the below mobile)
    string new_email = 1;
    // all the folios should be linked to this phone number. This will be used for user authentication.
    api.typesv2.common.PhoneNumber linked_mobile = 2;
    repeated FolioData folios = 3;
    // list of nft req ids for tracking the nft loan request and its history
    repeated NftRequestDetails nft_req_details = 4;
  }
  message FolioData {
    string folio_number = 1;
    string isin = 2;
    // units of mf that the user has
    double quantity = 3;
    string amc = 4;
  }
  message NftRequestDetails {
    string client_req_id = 1;
    google.protobuf.Timestamp created_at = 2;
  }
  message UserAction {
    RecordUserActionIdentifier identifier = 1;
  }
}

message ResetVendorLoanApplicationData {
  oneof data {
    LamfResetVendorLoanApplicationData lamf = 1;
  }
}

message LamfResetVendorLoanApplicationData {
  bool user_action_taken = 1;
}

message ContactabilityDetailsData {
  api.typesv2.common.PhoneNumber phone_number = 1;
  string token = 2;
  // max number of otp allowed before blocking user and putting in cool down
  int32 max_otp_attempts = 3;
  // current otp attempt count
  int32 otp_attempt_count = 4;
  // current phone number attempt count
  int32 phone_number_attempt_count = 5;
  // max number of times the user can change the phone numbers for requesting otp
  int32 max_phone_attempt_allowed = 6;
  // last time wrong otp was entered
  google.protobuf.Timestamp last_attempt_time = 7;
}

// VendorIdentifiersData can be used to persist identifiers returned from vendor for a step, example: vendorRequestID for a loan step, vendorReferenceID.
// These identifiers can later be used to interact with vendor APIs.
// NOTE : This field is not expected to hold any loan related ids like loan account id etc...
message VendorIdentifiersData {
  string request_id = 1;
}

message ROIModificationData {
  // The modified interest rate chosen by user from the list of allowed interest rates
  double chosen_roi = 1;

  // For storing key fact statement document after a user has modified the ROI
  LoanDocument kfs_doc = 2;

  // For storing loan agreement document after a user has modified the ROI
  LoanDocument loan_agreement_doc = 3;

  // New EMI amount based on chosen ROI
  google.type.Money installment_amount = 4;

  repeated double allowed_roi_for_modification = 5;
}
