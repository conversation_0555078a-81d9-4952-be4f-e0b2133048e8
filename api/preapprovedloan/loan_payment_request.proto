syntax = "proto3";

package preapprovedloan;

import "api/order/payment/payment_protocol.proto";
import "api/preapprovedloan/enums.proto";
import "api/preapprovedloan/enums/payment_provenance.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

//go:generate gen_sql -types=LoanPaymentRequest,LoanPaymentRequestDetails
message LoanPaymentRequest {
  string id = 1;
  string actor_id = 2;
  string account_id = 3; // Represents Loan Account ID
  string orch_id = 4;
  google.type.Money amount = 5;
  LoanPaymentRequestDetails details = 6;
  LoanPaymentRequestType type = 7;
  LoanPaymentRequestStatus status = 8;
  LoanPaymentRequestSubStatus sub_status = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
  google.protobuf.Timestamp deleted_at = 12;
  string parent_id = 13;
  LoanPaymentAccountType account_type = 14;
}

message LoanPaymentRequestDetails {
  string utr = 1;
  google.protobuf.Timestamp txn_initiation_time = 2;
  google.protobuf.Timestamp txn_settlement_time = 3;
  order.payment.PaymentProtocol payment_protocol = 4;
  enums.PaymentProvenance txn_provenance = 5;
  // contribution towards late payment interest charges out of the total amount
  // will be nil if payment allocation is not known
  // moved this field inside LL specific details since this represents the amount posted to LL
  google.type.Money lpi_charges = 6 [deprecated = true];
  // contribution towards other charges out of the total amount
  // will be nil if payment allocation is not known
  // moved this field inside LL specific details since this represents the amount posted to LL
  google.type.Money other_charges = 7 [deprecated = true];
  // additional vendor specific details
  oneof vendor_specific_details {
    LiquiloansPaymentDetails liquiloans_payment_details = 8;
    LendenPaymentDetails lenden_payment_details = 11;
  }
  // will denote the expiration time of the payment request.
  // if the order is not created in the payments service post this time then the payment request can be considered at expired/failed
  // but if the order is created and is in an in_progress state, then the payment request shouldn't be considered as expired/failed.
  google.protobuf.Timestamp request_expiry_time = 9;
  string payment_link = 10;
}

message LendenPaymentDetails {
  string order_id = 1;
}

message LiquiloansPaymentDetails {
  // represents charges posted to LL using the SaveCharges API when this payment was made, used to mirror the charge value in LL LMS (when Fi owns the primary LMS)
  // this need not be equal to the sum of the charges collected out of the total amount for this payment
  //
  // NOTE: we are posting all the currently applied charges to LL even if we are not collecting all the applied charges as part of this payment.
  // This is done because LL closes the loan if the entire outstanding amount is paid. When the user has pending applied charges and chooses to pay only
  // the outstanding Principal and Interest, if we don't post the applied charges, LL will close the loan since there is not outstanding amount left as per LL LMS
  repeated ChargeDetails charges_posted = 1;
  // contribution towards late payment interest charges sent to LL out of the total paid amount
  google.type.Money lpi_charges = 2;
  // contribution towards other charges sent to LL out of the total paid amount
  google.type.Money other_charges = 3;
  //if this is true, no amount will be allocated towards charges even if charges are pending, this can be passed as true when posting very old payments
  bool skip_charge_collection = 4;

  message ChargeDetails {
    // unique id sent to LL for the charge
    string id = 1;
    google.type.Money amount = 2;
    // charge date posted to LL
    google.type.Date date = 3;
  }
}
