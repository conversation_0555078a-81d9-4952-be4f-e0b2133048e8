// protolint:disable MAX_LINE_LENGTH

// Upi RPC for operations to simulate payments
// in dev environment
// ** NOT TO BE USED IN PRODUCTION **

syntax = "proto3";

package upi.simulation;

import "api/rpc/method_options.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/upi/simulation";
option java_package = "com.github.epifi.gamma.api.upi.simulation";

service Simulation {
  // This RPC is meant to be used for testing Add Funds flow in staging and demo env
  // purely. Please refrain from using in production code
  rpc SimulateReqPay(SimulateReqPayRequest) returns (SimulateReqPayResponse) {
    option (rpc.auth_required) = true;
  }


  // This RPC is meant to be used for testing Create Mandate flow in staging and demo env
  // purely. Please refrain from using in production code
  rpc CreateMandate(SimulateMandateRequest) returns (SimulateMandateResponse) {

  }
}

message SimulateMandateRequest {
  bytes raw_data = 1;
}

message SimulateMandateResponse {
  rpc.Status status = 1;
}



message SimulateReqPayRequest {
  bytes raw_data = 1;
}

message SimulateReqPayResponse {

  // Denotes the status of the request
  rpc.Status status = 1;
}
