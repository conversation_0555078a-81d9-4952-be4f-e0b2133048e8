// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package upi.mandate;

import "api/recurringpayment/recurring_payments_action.proto";
import "api/rpc/status.proto";
import "api/upi/cred_block.proto";
import "api/upi/device.proto";
import "api/upi/mandate/mandate.proto";
import "api/upi/mandate/mandate_entity.proto";
import "api/upi/mandate/mandate_requests.proto";
import "api/vendorgateway/vendor.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/upi/mandate";
option java_package = "com.github.epifi.gamma.api.upi.mandate";

service MandateService {
  // Creates the mandate entity and mandate request
  rpc CreateMandate (CreateMandateRequest) returns (CreateMandateResponse) {}
  // GetMandate fetches the mandate for the given request params
  // current supported methods to fetch the mandate
  // 1) req_id
  // 2) recurring_payment_id
  // 3) umn
  rpc GetMandate (GetMandateRequest) returns (GetMandateResponse) {}
  // AuthoriseMandateAction authorises the mandate action.
  // Calls the vendor to authorises mandate action and updates the mandate request's state/stage
  rpc AuthoriseMandateAction (AuthoriseMandateActionRequest) returns (AuthoriseMandateActionResponse) {}
  // FetchAndUpdateRequestStatus fetches the mandate request status and updates the mandate request status
  rpc FetchAndUpdateRequestStatus (FetchAndUpdateRequestStatusRequest) returns (FetchAndUpdateRequestStatusResponse) {}
  // DeclineMandateAction declines the mandate action.
  // It checks if the request is in a state that can be declined and move the mandate request to declined status
  // NOTE - We are not sending a response to NCPI as of now. Will be taking this up later
  rpc DeclineMandateAction (DeclineMandateActionRequest) returns (DeclineMandateActionResponse) {}
  // GetMandateRequestParameters fetches the mandate request parameters for given req id
  rpc GetMandateRequestParameters (GetMandateRequestParametersRequest) returns (GetMandateRequestParametersResponse) {}
  // GetMandateDetails fetches the mandate details for given recurring payment id
  rpc GetMandateDetails (GetMandateDetailsRequest) returns (GetMandateDetailsResponse) {}
  // ModifyMandate creates a mandate request with action as modify
  rpc ModifyMandate (ModifyMandateRequest) returns (ModifyMandateResponse) {}
  // RevokeMandate creates a mandate request with action as revoke
  rpc RevokeMandate (RevokeMandateRequest) returns (RevokeMandateResponse) {}
  // PauseUnpause creates a mandate request with action as pause
  rpc PauseUnpauseMandate (PauseUnpauseMandateRequest) returns (PauseUnpauseMandateResponse) {}
  // InitiateMandateExecution initiates mandate execution by making call to vendor
  rpc InitiateMandateExecution (InitiateMandateExecutionRequest) returns (InitiateMandateExecutionResponse) {};
}

message InitiateMandateExecutionRequest {
  message AuthHeader {
    // Device ID or fingerprint of the device that is registered with the partner bank
    upi.Device device = 1 [(validate.rules).message.required = true];

    // Credential contains a cred block that acts as second auth factor for the authorisation
    //
    // They typically contain Salt parameters in addition to PIN such as, but not limited to:
    // 1. Transaction ID
    // 2. Amount
    // 3. Timestamp
    upi.CredBlock npci_cred_block = 2;
  }

  // header containing details for authorisation of mandate initiation
  AuthHeader auth_header = 1;
  // order id for which the execution needs to be authorised
  string order_id = 2;
  // actor id who is initiating transaction
  string actor_id = 4;
}
message InitiateMandateExecutionResponse {
  enum Status {
    OK = 0;

    // transaction is in in-valid state
    // it can be mean either transaction is in terminal state or it has already been initiated.
    FAILED_PRECONDITION = 9;

    // transaction failed to initiate.
    // this can happen due to various reasons e.g. in-sufficient funds, account frozen temporarily.
    TRANSACTION_FAILED = 100;
  }
  rpc.Status status = 1;
}

message CreateMandateRequest {
  // recurring payment id
  string recurring_payment_id = 1;
  // req_id shared with the vendor
  string req_id = 2;
  // unique mandate reference number
  string umn = 3;
  // denotes if the mandate is revokeable
  bool revokeable = 4;
  // denotes if the mandate needs to be shared with payee
  bool share_to_payee = 5;
  // denotes if the funds will be blocked fo this mandate
  bool block_fund = 6;
  // initiator of the mandate - PAYER
  .upi.mandate.MandateInitiatedBy initiated_by = 7;
  // signed token used to authorise the mandate execution for payer
  string signed_token = 8;
  // payload for the mandate request
  .upi.mandate.Payload mandate_request_payload = 9;

  // current actor role- payer/payee
  ActorRole current_actor_role = 10;

  // partner bank which the mandate is getting initialised with
  vendorgateway.Vendor partner_bank = 11 [(validate.rules).enum = {not_in: [0]}];
  // current actor id who is initiating the request
  string current_actor_id = 12;
}

message CreateMandateResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;
    // modify for the given req id already exists
    ALREADY_EXISTS = 6;
    // permission denied for creation of mandate
    PERMISSION_DENIED = 7;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
  // created mandate
  .upi.mandate.MandateEntity mandate = 2;
}

message GetMandateRequest {
  oneof identifier {
    string req_id = 1;
    string recurring_payment_id = 2;
    string umn = 3;
  }
}

message GetMandateResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // mandate not found
    RECORD_NOT_FOUND = 5;
    PERMISSION_DENIED = 7;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;

  .upi.mandate.MandateEntity mandate = 2;
}

message AuthoriseMandateActionRequest {

  message AuthHeader {
    // Device ID or fingerprint of the device that is registered with the partner bank
    upi.Device device = 1 [(validate.rules).message.required = true];

    // Credential contains a cred block that acts as second auth factor for the authorisation
    //
    // They typically contain Salt parameters in addition to PIN such as, but not limited to:
    // 1. Transaction ID
    // 2. Amount
    // 3. Timestamp
    upi.CredBlock npci_cred_block = 2;
  }

  // header containing details for authorisation of mandate initiation
  AuthHeader auth_header = 1;

  string req_id = 2;
  // actor id of the one initialising the mandate
  string current_actor_id = 3;

  string from_actor_id = 4;

  string to_actor_id = 5;

  string from_pi_id = 6;

  string to_pi_id = 7;

  string remarks = 8;
  // validity of the mandate
  .upi.mandate.Validity mandate_validity = 9;

  // partner bank which the mandate is getting initialised with
  vendorgateway.Vendor partner_bank = 10 [(validate.rules).enum = {not_in: [0]}];

  // amount for which the mandate is created
  google.type.Money amount = 11;

  // amount rule for the mandate eg. MAX, EXACT
  .upi.mandate.AmountRule amount_rule = 12;

  // recurrence params for te mandate
  .upi.mandate.Recurrence recurrence = 13;
}

message AuthoriseMandateActionResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    PERMISSION_DENIED = 7;
    // permanent failure
    PERMANENT_FAILURE = 100;
    // transient failure
    TRANSIENT_FAILURE = 101;
    // request has expired
    EXPIRED = 102;
  }
  // Status of the request
  rpc.Status status = 1;

  recurringpayment.ActionDetailedStatus action_detailed_status = 2;

}

message FetchAndUpdateRequestStatusRequest {
  // request if for which the status needs to fetched
  string req_id = 1;
  // partner bank involved in the mandate
  vendorgateway.Vendor partner_bank = 2;
  // pi id of the payment instrument belonging to epifi user
  string epifi_customer_pi = 3;
  // Context: As per NPCI guidelines, vendor status enquiry (ReqChkTxnStatus) for UPI mandate actions (creation or modification)
  // should not be made within a minimum delay period (e.g., 90 seconds) from the time the action was initiated.
  // This flag allows bypassing the delay period in exceptional cases.
  // Default: false (recommended for production to comply with NPCI guidelines)
  // Note: Setting this to true should be used with caution as it may violate NPCI guidelines.
  bool force_immediate_vendor_enquiry = 4;
}

message FetchAndUpdateRequestStatusResponse {

  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // mandate request is in progress
    IN_PROGRESS = 51;
    // transient failure
    TRANSIENT_FAILURE = 100;
    // permanent failure
    PERMANENT_FAILURE = 101;
    // As per NPCI guidelines, vendor status enquiry  for UPI mandate creation or modification
    // should not be made within a minimum delay period (e.g., 90 seconds) from the action's creation time.
    // If the action is too recent, this RPC returns a special status (TOO_EARLY_FOR_VENDOR_CALL) to signal the caller to retry later.
    TOO_EARLY_FOR_VENDOR_CALL = 102;
  }
  // Status of the request
  rpc.Status status = 1;

  recurringpayment.ActionDetailedStatus action_detailed_status = 2;

}

message DeclineMandateActionRequest {
  // string req id of action to be declined
  string req_id = 1;
}

message DeclineMandateActionResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // invalid argument passed
    INVALID_ARGUMENT = 3;
    // internal error occurred
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
}

message GetMandateRequestParametersRequest {
  string req_id = 1;
}

message GetMandateRequestParametersResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // mandate not found
    RECORD_NOT_FOUND = 5;
    PERMISSION_DENIED = 7;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;

  string req_id = 2;

  // reference id corresponds to order-id in case of merchant payment. It has no significance in case of P2P
  // e.g. a merchant shows a dynamic QR and wants a reference id in the payment that ties it to their internal order system.
  string merchant_ref_id = 3;

  // transaction reference url should be a URL when clicked provides customer with further transaction details
  // like complete bill details, bill copy, order copy, ticket details, etc.
  //
  // for dynamic QR and intent based payments.. merchant system can send this information
  // in other cases we will be using a default string which may redirect to epifi
  string ref_url = 4;

  // vpa of the payer involved in the transaction
  // we are storing this as even though the vpa is case insensitive, we need to pass the exact
  // vpa which we received, for salt generation and while sending a call back to NPCI
  string payer_vpa = 5;

  // vpa of the payee involved in the transaction
  // we are storing this as even though the vpa is case insensitive, we need to pass the exact
  // vpa which we received, for salt generation and while sending a call back to NPCI
  string payee_vpa = 6;
}

message GetMandateDetailsRequest {
  string recurring_payment_id = 1;
}

message GetMandateDetailsResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // mandate not found
    RECORD_NOT_FOUND = 5;
    PERMISSION_DENIED = 7;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;

  // umn of the mandate
  string umn = 2;
}
message ModifyMandateRequest {
  // recurring payment id
  string recurring_payment_id = 1;
  // req_id shared with the vendor
  string req_id = 2;
  // payload for the mandate request
  .upi.mandate.Payload mandate_request_payload = 3;

  // current actor role- payer/payee
  ActorRole current_actor_role = 4;

  // partner bank which the mandate is getting initialised with
  vendorgateway.Vendor partner_bank = 5 [(validate.rules).enum = {not_in: [0]}];
}

message ModifyMandateResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // mandate not found
    RECORD_NOT_FOUND = 5;
    // modify for the given req id already exists
    ALREADY_EXISTS = 6;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
}

message RevokeMandateRequest {
  // recurring payment id
  string recurring_payment_id = 1;
  // req_id shared with the vendor
  string req_id = 2;
  // payload for the mandate request
  .upi.mandate.Payload mandate_request_payload = 3;

  // current actor role- payer/payee
  ActorRole current_actor_role = 4;

  // partner bank which the mandate is getting initialised with
  vendorgateway.Vendor partner_bank = 5 [(validate.rules).enum = {not_in: [0]}];
  // initiator of the mandate - PAYER
  .upi.mandate.MandateInitiatedBy initiated_by = 6;
}

message RevokeMandateResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // mandate not found
    RECORD_NOT_FOUND = 5;
    // modify for the given req id already exists
    ALREADY_EXISTS = 6;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
}

enum ActorRole {
  ACTOR_ROLE_UNSPECIFIED = 0;
  // actor is payer
  ACTOR_ROLE_PAYER = 1;
  // actor is payee
  ACTOR_ROLE_PAYEE = 2;
}

message PauseUnpauseMandateRequest {
  // recurring payment id
  string recurring_payment_id = 1;
  // req_id shared with the vendor
  string req_id = 2;
  // payload for the mandate request
  .upi.mandate.Payload mandate_request_payload = 3;

  // current actor role- payer/payee
  ActorRole current_actor_role = 4;

  // partner bank which the mandate is getting initialised with
  vendorgateway.Vendor partner_bank = 5 [(validate.rules).enum = {not_in: [0]}];

  // request action that can either be pause or unpause
  .upi.mandate.MandateType req_action = 6 [(validate.rules).enum = {in: [4, 5]}];
}

message PauseUnpauseMandateResponse {
  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // mandate not found
    RECORD_NOT_FOUND = 5;
    // modify for the given req id already exists
    ALREADY_EXISTS = 6;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;
}
