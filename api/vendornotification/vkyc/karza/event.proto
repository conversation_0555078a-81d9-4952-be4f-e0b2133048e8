syntax = "proto3";
package vendornotification.vkyc.karza;

import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/vkyc/karza";

// This event will be published from VN server when received from karza
message CallEvent {
  // standard header to be added to all requests
  queue.ConsumerRequestHeader consumer_request_header = 1;
  string transaction_id = 2;
  message Data {
    string timestamp = 1;
    string username = 2;
    string start_date = 3;
    string end_date = 4;
    string agent_id = 5;
    string session_id = 6;
    string user_agent_string = 7;
    string agent_employee_id = 8;
    int32 expected_wait_time = 9;
    string application_id = 10;
    string customer_arrival_time = 11;
    string customer_id = 12;
    string last_stage_completed = 17;
    bool is_customer_blocked = 18;
    string blocked_reason = 19;
    string call_connection_time = 20;
    string call_end_time = 21;
    string agent_user_name = 22;
    google.protobuf.Timestamp call_start_time = 23;
    string approved_by_agent = 24;
    int32 call_duration = 25;
    string agent_remark = 26;
    string auditor_failure_reason = 27;
    string auditor_status = 28;
    string auditor_remark = 29;
  }
  Data data = 3;
  string event = 4;
  string request_id = 5;
  google.protobuf.Timestamp event_timestamp = 6;
  string status = 7;
  string failure_reason = 8;
  string event_version = 9;
  string event_type = 10;
}

message AuditorCallbackEvent {
  queue.ConsumerRequestHeader consumer_request_header = 1;
  string applicationId = 2;
  string transaction_id = 3;
  string auditor_employee_id = 4;
  string approved_by_auditor = 5;
  google.protobuf.Timestamp event_timestamp = 6;
}

// TODO (hardik) : Add more fields from callback response when required
message AgentCallbackEvent {
  queue.ConsumerRequestHeader consumer_request_header = 1;
  bool call_success = 2;
  string error = 3;
  string failure_reason = 4;
  string application_id = 5;
  string customer_id = 6;
  string transaction_id = 7;
  string agent_employee_id = 8;
  string call_start_time = 9;
  string call_end_time = 10;
  double call_duration = 11;
  string approved_by_agent = 12;
  google.protobuf.Timestamp event_timestamp = 13;
}
