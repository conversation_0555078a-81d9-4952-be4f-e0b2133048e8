// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.lending.loans.federal;

import "api/vendors/federal/payment.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/lending/loans/federal";
option java_package = "com.github.epifi.gamma.api.vendornotification.lending.loans.federal";

// The Loans Service helps in processing notifications from the Federal bank for all loan accounts
service Loans {
  // All notifications belonging to loan account transactions from Federal are posted to this RPC
  // This will not include the callbacks for status updates of a transaction
  rpc ProcessLoanInboundTxn (vendors.federal.ProcessInboundTxnRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/lending/loans/federal/notification"
      body: "*"
    };
  }

  // LoanCustomerAndAccountCreation federal callback api for ntb loans flow which they use for sending callbacks for both customer creation and loan account creation
  rpc LoanCustomerAndAccountCreation (google.protobuf.Struct) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/loans/account/create"
      body: "*"
    };
  }
}

