syntax = "proto3";

package vendornotification.cx.chatbot;

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/chatbot";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.chatbot";

// Data collected for nugget chatbot resource API
message CollectedData {
  oneof data {
    FreezeBotData freeze_bot_data = 1;
    TransactionBotData transaction_bot_data = 2;
  }
}

// Data collected for freeze bot
message FreezeBotData {
  string account_stats = 1;
  string processed_freeze_reason = 2;
  string freeze_type = 3;
  string form_id = 4;
  string form_status = 5;
  string form_expiry_date = 6;
  string lea_complaint_details = 7;
}

// Data collected for transaction bot
message TransactionBotData {
  string payment_protocol = 1;
  string provenance = 2;
  string order_created_at_ist = 3;
  string transaction_amount = 4;
  string tags = 5;
  string transaction_type =  6;
  string error_code  = 7;
}
