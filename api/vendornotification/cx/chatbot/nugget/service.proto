syntax = "proto3";

package vendornotification.cx.chatbot.nugget;

import "google/protobuf/struct.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/chatbot/nugget";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.chatbot.nugget";

// ChatbotNuggetService provides APIs for nugget chatbot servers to fetch specific data nuggets from the Fi backend.
// This service is typically used by chatbot applications to retrieve user-specific or contextual information
// required for generating responses or performing actions.
service ChatbotNuggetService {
  // Fetches requested data fields for a chatbot session.
  // The chatbot provides an access token and a list of fields it needs.
  // The backend responds with the requested key-value pairs.
  rpc FetchData (FetchDataRequest) returns (FetchDataResponse) {
    option (google.api.http) = {
      post: "/cx/chatbot/nugget/fetch-data"
      body: "*"
    };
  }
}

// Request message for FetchData.
// Contains the access token for authentication and a list of fields to fetch.
message FetchDataRequest {
  // Access token representing the chatbot session or user.
  string access_token = 1 [json_name = "access_token"];
  // List of fields the chatbot wants to retrieve along with any input parameters.
  repeated RequestedFieldInfo requested_fields = 2 [json_name = "requested_fields"];
  // User ID for which data fields are requested
  string user_id = 3 [json_name = "user_id"];
}

message RequestedFieldInfo {
  /*
    Example -
    "requested_fields":
    [
     {
        "field_name": "TRANSACTION_DETAILS",
        "input_parameters": [
          {
            "Key": "TRANSACTION_ID",
            "Value": "TXN123"
          },
        ]
     },
     {
        "field_name": "FREEZE_REASON",
        "input_parameters": []
     }
    ]
  */

  // a field is a string form of the types mentioned in - api/typesv2/chat.proto:ChatbotRequestedDataField
  string field_name = 1 [json_name = "field_name"] ;
  // input parameters - example : Transaction ID for fetching transaction details
  // note - it is not mandatory that an input parameter has to be passed, some requested fields may not require it
  repeated KeyValuePair input_parameters = 2 [json_name = "input_parameters"];
}

// Key-value pair representing a single data field and its value.
message KeyValuePair {
  string key = 1 [json_name = "key"];
  google.protobuf.Value value = 2 [json_name = "value"];
}

message FetchDataResponse {
  int32 status = 1 [json_name = "status"];
  // List of key-value pairs for the requested data fields.
  repeated KeyValuePair data = 2 [json_name = "data"];
}
