syntax = "proto3";

package vendornotification.cx.chatbot.inapphelp.freshchat;

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/chatbot/inapphelp/freshchat";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.chatbot.inapphelp.freshchat";

// Refers to the request that will come from freshchat sdk with user query
message FreshchatAiBotQueryRequest {
    // Ref Doc https://docs.google.com/document/d/1N1ipP1RBSEN3oqu4q9vqxZYSxj8gF-iJ-pPtbQXIjP8/edit?tab=t.0
    // this can either be an issue selection by user or user typed freetext query
    string user_query = 1 [json_name = "user_query"];
    // this is the previous selection context that will be passed from freshchat
    // todo: validate this post call from sdk
    string query_context = 2 [json_name = "query_context"];
    // unique identifier for the user
    string actor_id = 3 [json_name = "actor_id"];
    // unique id for the chat session
    string session_id = 4 [json_name = "session_id"];
    // Timestamp of when the message when query is raised by user
    // The timestamp is added as part of the request object
    string query_raised_at = 5 [json_name = "query_raised_at"];
    string phone_number = 6 [json_name = "phone_number"];
    // NOTE - email is currently being used to extract ref ID for the user
    string email = 7 [json_name = "email"];
}

message FreshchatAiBotQueryResponse {
    // response from the ds model for user query
    string query_response = 1 [json_name = "query_response"];
}
