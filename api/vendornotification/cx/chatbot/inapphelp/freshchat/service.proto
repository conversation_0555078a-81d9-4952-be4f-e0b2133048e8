syntax = "proto3";

package vendornotification.cx.chatbot.inapphelp.freshchat;

import "api/vendornotification/cx/chatbot/inapphelp/freshchat/message.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/chatbot/inapphelp/freshchat";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.chatbot.inapphelp.freshchat";

service FreshchatAIBotService {
    rpc ProcessLLMBotQuery (FreshchatAiBotQueryRequest) returns (FreshchatAiBotQueryResponse) {
        option (google.api.http) = {
            post: "/cx/chatbot/freshchat/ai/query-detail"
            body: "*"
        };
    }
}
