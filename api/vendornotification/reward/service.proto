syntax = "proto3";

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "api/vendors/reward/fulfill_reward.proto";
import "api/vendors/reward/reward_status.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/reward";
option java_package = "com.github.epifi.gamma.api.vendornotification.reward";

package reward;


service Reward {
  rpc FulfillReward(api.vendors.reward.FulfillRewardRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/rewards/fulfill"
      body: "*"
    };
  }

  rpc GetRewardStatus(api.vendors.reward.GetRewardStatusRequest) returns (api.vendors.reward.GetRewardStatusResponse) {
    option (google.api.http) = {
      post: "/rewards/get-status"
      body: "*"
    };
  }
}



