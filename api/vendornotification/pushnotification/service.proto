syntax = "proto3";

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "api/vendors/pushnotification/push_notification.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/pushnotification";
option java_package = "com.github.epifi.gamma.api.vendornotification.pushnotification";

package pushnotification;

service PushNotification {
  // SendPushNotification is used to receive notifications from vendors to send app PNs,
  // particularly where we do not directly control the user flow e.g. flow is controlled by Saven SDK
  rpc SendPushNotification(api.vendors.pushnotification.SendPushNotificationRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/push-notification/send"
      body: "*"
    };
  }
}
