syntax = "proto3";

package vendornotification.auth;

import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/auth";
option java_package = "com.github.epifi.gamma.api.vendornotification.auth";

// Auth service provides authentication/authorization RPCs for the mux router to route the HTTP requests from external partners
service Auth {
  // GetAuthToken validates the input and generates an auth token that the clients can use to make subsequent calls
  // Returns below http status codes based on basic validations
  // 200 - Success
  // 401 - Unauthorized
  // 500 - Internal server error
  rpc GetAuthToken (GetAuthTokenRequest) returns (GetAuthTokenResponse) {
    option (google.api.http) = {
      post: "/v1/auth/token"
      body: "*"
    };
  };
}

message GetAuthTokenRequest {
  string client_id = 1 [json_name = "client_id"];
  string client_secret = 2 [json_name = "client_secret"];
}

message GetAuthTokenResponse {
  Status status = 1 [json_name = "status"];
  // auth_token to be used for subsequent calls
  string auth_token = 2 [json_name = "auth_token"];
  // duration after which auth_token is no longer valid
  uint64 expiry_in_seconds = 3 [json_name = "expiry_in_seconds"];
}

message Status {
  // custom status codes
  uint32 code = 1 [json_name = "code"];
  string message = 2 [json_name = "message"];
}
