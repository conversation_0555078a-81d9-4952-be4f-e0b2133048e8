syntax = "proto3";

package vendornotification.notifications.moengage;

import "api/vendors/moengage/connector.proto";
import "api/vendors/moengage/contentapi.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/notifications/moengage";
option java_package = "com.github.epifi.gamma.api.vendornotification.notifications.moengage";

// Moengage service to serve different APIs needed by Moengage
service Moengage {
  // GetUserAttributes will serve as the content API required by Moengage for fetching PII data from Fi's services
  // for sending comms containing PII data.
  rpc GetUserAttributes (vendors.moengage.GetUserAttributesRequest) returns (vendors.moengage.GetUserAttributesResponse) {
    option (google.api.http) = {
      get: "/notifications/moengage/user-attributes"
    };
  }

  // ProcessConnectorWebhook will serve as the webhook API required by Moengage for processing the webhook data
  // This can be used to send callback from a moengage user journey using the custom connector webhook endpoint
  rpc ProcessConnectorWebhook (vendors.moengage.ProcessConnectorWebhookRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/notifications/moengage/connector-webhook"
      body: "*"
    };
  }
}

// Area is defined to create a logical separation between groups of different attributes of different services
// that GetUserAttributes RPC will serve. it will also help in reusing same field names to fetch different information
// from different services. for example `summary` field can be fetched for both rewards and investments if area are
// defined as REWARDS and INVESTMENTS respectively.
enum Area {
  AREA_UNSPECIFIED = 0;
  REWARDS = 1;
  QUEST = 2;
  INSIGHTS = 3;
  PRE_APPROVED_LOAN = 4;
  ACCOUNTS = 5;
  AREA_CREDIT_CARD = 6;
  AREA_DEBIT_CARD = 7;
  FI_STORE = 8;
  AREA_STOCKS = 9;
  AA_SALARY_PROGRAM = 10 [deprecated = true];
  SALARY_PROGRAM = 11;
  UPI_ID = 12;
  PORTFOLIO_TRACKER = 13;
  WEEKLY_PORTFOLIO_TRACKER = 14;
}

// UseCase is defined to create a logical separation between different use cases within an area
// It will define the behaviour in which the data will be processed against a webhook callback
enum UseCase {
  USE_CASE_UNSPECIFIED = 0;
  DROP_OFF_OUTCALL = 1;
}
