syntax = "proto3";

package stockguardian.sgvendorgateway.ckyc;

import "api/rpc/status.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/image_type.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vendor_status.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/ckyc";

service Ckyc {
  // GetCkycSearch RPC is used for getting ckyc info from ckyc search api
  rpc GetCkycSearch (GetCkycSearchRequest) returns (GetCkycSearchResponse);
  // SearchCKYC RPC is used for searching ckyc info using SHA256 hash and API version 1.3
  rpc SearchCKYC (SearchCKYCRequest) returns (SearchCKYCResponse);
  // CkycDownload RPC is used for downloading ckyc info from ckyc download api
  rpc CkycDownload (CkycDownloadRequest) returns (CkycDownloadResponse);
  //CkycDownloadV2 is used to initate the otp verification procedure for first time for a customer
  rpc CkycDownloadV2 (CkycDownloadV2Request) returns (CkycDownloadV2Response);
  // VerifyOTP is used to verify the otp, resend the otp, if the otp is verified the it returns the data of coustomer as well
  rpc VerifyOTP (VerifyOTPRequest) returns (VerifyOTPResponse);
}

message GetCkycSearchRequest {
  vendorgateway.RequestHeader header = 1;
  // customer pan
  string pan = 2;
  // request id
  string req_id = 3;
}


message GetCkycSearchResponse {
  rpc.Status status = 1;
  string ckyc_number = 2;
  // customer name
  api.typesv2.common.Name name = 3;
  api.typesv2.common.Name fathers_name = 4;
  // customer age
  uint32 age = 5;
  // customer photo
  api.typesv2.common.Image photo = 6;
  // date when kyc was first recorded
  google.type.Date kyc_date = 7;
  // date when kyc record was last updated
  google.type.Date updated_date = 8;
  // raw response
  vendorgateway.VendorStatus vendor_status = 9;
  // constitution type is populated for legal entities
  string constitution_type = 10;

  // As per the circular from CKYC, If we send anything other than the CKYC number in request we will get CKYC Reference ID which should be used to fetch the CKYC Download data
  // Sending the reference ID from VG response
  string ckyc_reference_id = 11;
}

message SearchCKYCRequest {
  vendorgateway.RequestHeader header = 1;
  // customer pan
  string pan = 2;
  // request id
  string req_id = 3;
}

message SearchCKYCResponse {
  rpc.Status status = 1;

  // CKYC search data containing all customer information
  CKYCSearchData data = 2;
}

// CKYC search data containing customer information
message CKYCSearchData {
  string ckyc_number = 1;
  // customer name
  api.typesv2.common.Name name = 2;
  api.typesv2.common.Name fathers_name = 3;
  // customer age
  uint32 age = 4;
  // customer photo
  api.typesv2.common.Image photo = 5;
  // date when kyc was first recorded
  google.type.Date kyc_date = 6;
  // date when kyc record was last updated
  google.type.Date updated_date = 7;
  // raw response
  vendorgateway.VendorStatus vendor_status = 8;
  // constitution type is populated for legal entities
  string constitution_type = 9;

  // As per the circular from CKYC, If we send anything other than the CKYC number in request we will get CKYC Reference ID which should be used to fetch the CKYC Download data
  // Sending the reference ID from VG response
  string ckyc_reference_id = 10;
}

message CkycDownloadRequest {
  vendorgateway.RequestHeader header = 1;
  // customer ckyc_no
  string ckyc_no = 2;
  // dob of customer
  string dob = 3;
  // request id
  string req_id = 4;
}

message CkycDownloadResponse {
  rpc.Status status = 1;
  // personal details of customer
  PersonalDetails personal_details = 2;
  // identity details of customer
  repeated IdentityDetail identity_details = 3;
  // raw response
  vendorgateway.VendorStatus vendor_status = 4;
  // error message
  string error_message = 5;
  // image data of customer
  repeated ImageDetails images_data = 6;
  // Raw response from the CKYC download API, we are storing all the data except the images data
  string raw_response = 7;
}

message PersonalDetails {
  // account type of customer
  AccountType account_type = 1;
  // name of customer
  api.typesv2.common.Name name = 2;
  // fathers name of customer
  api.typesv2.common.Name fathers_name = 3;
  // mothers name of customer
  api.typesv2.common.Name mothers_name = 4;
  // gender of customer
  api.typesv2.common.Gender gender = 5;
  // date of birth of the customer
  google.type.Date dob = 6;
  // nationality of customer
  //  types.Nationality nationality = 7; // TODO(Ankit): Move to common and use
  // permanent address of customer
  api.typesv2.common.PostalAddress permanent_address = 8;
  // current address of customer
  api.typesv2.common.PostalAddress current_address = 9;
  // office tel number of customer
  api.typesv2.common.Landline tel_office = 10;
  // office tel number of customer
  api.typesv2.common.Landline tel_residential = 11;
  // phone number of customer
  api.typesv2.common.PhoneNumber mobile_number = 12;
  // email id of customer
  string email_id = 13;
  // list of customer photo
  repeated ImageDetails image_details_list = 14;
  // pan of user
  string pan = 15;
  // proof of address
  //  types.DocumentProofType proof_of_address = 16; // TODO(Ankit): Move to common and use
  // remarks if any
  string remarks = 17;
  // maiden name
  api.typesv2.common.Name maiden_name = 18;
  // ckyc number
  string ckyc_no = 19;
}

message IdentityDetail {
  // identity type
  IdentityType identity_type = 1;
  // identity
  string identity_number = 2;
  // verification status of identity
  VerificationStatus verification_status = 3;
}

message ImageDetails {
  IdentityType document_type = 1;
  // type of image
  api.typesv2.common.ImageType image_type = 2;
  // base64 encoded image data
  string image_data = 3;
}

enum AccountType {
  ACCOUNT_TYPE_TYPE_UNSPECIFIED = 0;
  ACCOUNT_TYPE_NORMAL = 1;
  ACCOUNT_TYPE_SMALL = 2;
  ACCOUNT_TYPE_SIMPLIFIED = 3;
  ACCOUNT_TYPE_OTP_EKYC = 4;
}

enum IdentityType {
  IDENTITY_TYPE_TYPE_UNSPECIFIED = 0;
  IDENTITY_TYPE_PASSPORT = 1;
  IDENTITY_TYPE_VOTER_ID = 2;
  IDENTITY_TYPE_PAN = 3;
  IDENTITY_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR = 4;
  IDENTITY_TYPE_DRIVING_LICENSE = 5;
  IDENTITY_TYPE_NREGA = 6;
  IDENTITY_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER = 7;
  IDENTITY_TYPE_EKYC_AUTHENTICATION = 8;
  IDENTITY_TYPE_OFFLINE_AADHAAR_VERIFICATION = 9;
  IDENTITY_TYPE_OFFLINE_OTHERS = 10;
  IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT = 11;
  IDENTITY_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED = 12;
}

enum VerificationStatus {
  VERIFICATION_STATUS_TYPE_UNSPECIFIED = 0;
  VERIFICATION_STATUS_YES = 1;
  VERIFICATION_STATUS_NO = 2;
}

enum AuthFactorType {
  AUTH_FACTOR_TYPE_UNSPECIFIED = 0;
  // DOB/DOI authentication
  AUTH_FACTOR_TYPE_DOB = 1;
  // PINCODE and Year of Birth authentication
  AUTH_FACTOR_TYPE_PIN_CODE_YEAR_OF_BIRTH = 2;
  // Mobile Number authentication
  AUTH_FACTOR_TYPE_MOBILE_NUMBER = 3;
}

message CkycDownloadV2Request {
  vendorgateway.RequestHeader header = 1;
  // customer ckyc_no
  string ckyc_no = 2;
  // request id
  string req_id = 3;
  // authentication factor type to determine which fields to use
  AuthFactorType auth_factor_type = 4;

  // Authentication factor data - only one of these should be set based on auth_factor_type
  oneof auth_factor_data {
    // DOB authentication factor (used when auth_factor_type = AUTH_FACTOR_TYPE_DOB)
    DobAuthFactor dob_auth = 5;
    // Mobile number authentication factor (used when auth_factor_type = AUTH_FACTOR_TYPE_MOBILE_NUMBER)
    MobileNumberAuthFactor mobile_auth = 6;
    // PIN code and year of birth authentication factor (used when auth_factor_type = AUTH_FACTOR_TYPE_PIN_CODE_YEAR_OF_BIRTH)
    PinCodeYearOfBirthAuthFactor pin_code_yob_auth = 7;
  }
}

// DOB authentication factor
message DobAuthFactor {
  // date of birth of customer
  string dob = 1;
}

// Mobile number authentication factor
message MobileNumberAuthFactor {
  // phone number of customer
  string phone_number = 1;
}

// PIN code and year of birth authentication factor
message PinCodeYearOfBirthAuthFactor {
  // PIN code (Postal Index Number code) of customer
  string pin_code = 1;
  // year of birth of customer
  string year_of_birth = 2;
}

message CkycDownloadV2Response {
  rpc.Status status = 1;
  vendorgateway.VendorStatus vendor_status = 9;
}

message VerifyOTPRequest {
  vendorgateway.RequestHeader header = 1;
  string reqId = 2;
  string otp = 3;
}

message VerifyOTPResponse {
  rpc.Status status = 1;
  vendorgateway.VendorStatus vendor_status = 9;
  // personal details of customer
  PersonalDetails personal_details = 2;
  // identity details of customer
  repeated IdentityDetail identity_details = 3;
  // image data of customer
  repeated ImageDetails images_data = 6;
  // Raw response from the CKYC download API, we are storing all the data except the images data
  string raw_response = 7;
}

