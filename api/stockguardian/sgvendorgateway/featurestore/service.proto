syntax = "proto3";

package stockguardian.sgvendorgateway.featurestore;

import "api/rpc/status.proto";
import "google/protobuf/struct.proto";
import "api/stockguardian/creditreport/enums.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/featurestore";

service FeatureStoreService {
  rpc ExtractFeatures (ExtractFeaturesRequest) returns (ExtractFeaturesResponse);
  // RPC to fetch users predictive delinquency score using their credit report
  rpc GetPdScore (GetPdScoreRequest) returns (GetPdScoreResponse);
}

message ExtractFeaturesRequest {
  // List of features names to extract
  // Ex: \["a", "b"]
  repeated string feature_name_list = 1;
  string credit_report_data_raw = 2;
  string phone_number = 3;
}

message ExtractFeaturesResponse {
  rpc.Status status = 1;
  // List of all the output features against an identifier
  // Ex: {"name": "a", "value": \["aa1", ""aa2]}, "name": "b", "value": \["bb", "bb2"]}
  map<string, google.protobuf.Value> feature_map = 2;
}

message GetPdScoreRequest {
  string customer_id = 1;
  bytes raw_credit_report = 2;
  stockguardian.creditreport.Bureau bureau = 3;
}

message GetPdScoreResponse {
  rpc.Status status = 1;
  double score = 2;
  string model_context = 3;
  string model_version = 4;
}
