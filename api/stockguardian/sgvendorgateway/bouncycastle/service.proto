syntax = "proto3";

package stockguardian.sgvendorgateway.bouncycastle;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/bouncycastle";

enum HashFunction {
  HASH_FUNCTION_UNSPECIFIED = 0;
  HASH_FUNCTION_SHA1 = 1;
  HASH_FUNCTION_SHA256 = 2;
}

service BouncyCastle {
  rpc CkycEncryptAndSign (CkycEncryptAndSignRequest) returns (CkycEncryptAndSignResponse);
  rpc CkycVerifyAndDecrypt (CkycVerifyAndDecryptRequest) returns (CkycVerifyAndDecryptResponse);
  rpc GenerateNSDLSignature (GenerateNSDLSignatureRequest) returns (GenerateNSDLSignatureResponse);
}

message CkycEncryptAndSignRequest {
  vendorgateway.RequestHeader header = 1;
  string data = 2;
  HashFunction hash_function = 3;
}

message CkycEncryptAndSignResponse {
  rpc.Status status = 1;
  string data = 2;
}

message CkycVerifyAndDecryptRequest {
  vendorgateway.RequestHeader header = 1;
  string data = 2;
  HashFunction hash_function = 3;
}

message CkycVerifyAndDecryptResponse {
  rpc.Status status = 1;
  string data = 2;
}

message GenerateNSDLSignatureRequest {
  vendorgateway.RequestHeader header = 1;
  string data = 2;
}

message GenerateNSDLSignatureResponse {
  rpc.Status status = 1;
  string data = 2;
}
