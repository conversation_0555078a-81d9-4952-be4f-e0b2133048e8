syntax = "proto3";

package stockguardian.sgvendorgateway.creditreport;


import "api/rpc/status.proto";
import "api/stockguardian/vendors/cibil/customer_risk_and_income_assessment.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/date.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/address.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/creditreport";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgvendorgateway.creditreport";

service CreditReportService{
  rpc PerformHardPullInquiry(PerformHardPullInquiryRequest) returns (PerformHardPullInquiryResponse);
  rpc PerformExperianHardPullInquiry(PerformExperianHardPullInquiryRequest) returns (PerformExperianHardPullInquiryResponse);
}

message PerformHardPullInquiryRequest{
  string customer_id = 1;
  UserDetails user_details = 2;
  string bureau_request_id = 3;
  // amount selected bu user against which we want to make the request
  google.type.Money loan_amount = 4;
}

message UserDetails {
  string pan = 1;
  repeated api.typesv2.common.PhoneNumber telephones = 2;
  api.typesv2.common.Name name = 3;
  api.typesv2.common.Gender gender = 4;
  repeated api.typesv2.common.PostalAddress addresses = 5;
  api.typesv2.common.Date dob = 6;
}


message PerformHardPullInquiryResponse{
  rpc.Status status = 1;
  vendors.cibil.ConsumerCreditRiskAndIncomeAssessmentResponse vendor_response = 2;
}

message ExperianIdentification {
  string xml_user = 1;
  string xml_password = 2;
}

message ExperianApplication {
  int64 ft_reference_number = 1;
  string customer_reference_id = 2;
  string enquiry_reason = 3;
  string finance_purpose = 4;
  google.type.Money amount_financed = 5;
  int64 duration_of_agreement = 6;
  string score_flag = 7;
  string psv_flag = 8;
}

message ExperianApplicant {
  api.typesv2.common.Name name = 1;
  api.typesv2.common.Gender gender = 2;
  // Maps to IncomeTaxPAN
  string income_tax_pan = 3;
  // Maps to PANIssueDate
  api.typesv2.common.Date pan_issue_date = 4;
  // Maps to PANExpirationDate
  api.typesv2.common.Date pan_expiration_date = 5;
  // Maps to PassportNumber
  string passport_number = 6;
  // Maps to PassportIssueDate
  api.typesv2.common.Date passport_issue_date = 7;
  // Maps to PassportExpirationDate
  api.typesv2.common.Date passport_expiration_date = 8;
  // Maps to VoterIdentityCard
  string voter_identity_card = 9;
  // Maps to VoterIDIssueDate
  api.typesv2.common.Date voter_id_issue_date = 10;
  // Maps to VoterIDExpirationDate
  api.typesv2.common.Date voter_id_expiration_date = 11;
  // Maps to DriverLicenseNumber
  string driver_license_number = 12;
  // Maps to DriverLicenseIssueDate
  api.typesv2.common.Date driver_license_issue_date = 13;
  // Maps to DriverLicenseExpirationDate
  api.typesv2.common.Date driver_license_expiration_date = 14;
  // Maps to RationCardNumber
  string ration_card_number = 15;
  // Maps to RationCardIssueDate
  api.typesv2.common.Date ration_card_issue_date = 16;
  // Maps to RationCardExpirationDate
  api.typesv2.common.Date ration_card_expiration_date = 17;
  // Maps to UniversalIDNumber
  string universal_id_number = 18;
  // Maps to UniversalIDIssueDate
  api.typesv2.common.Date universal_id_issue_date = 19;
  // Maps to UniversalIDExpirationDate
  api.typesv2.common.Date universal_id_expiration_date = 20;
  // Maps to DateOfBirth
  api.typesv2.common.Date date_of_birth = 21;
  // STD Phone Number
  int64 std_phone_number = 22;
  // Maps to PhoneNumber
  api.typesv2.common.PhoneNumber phone_number = 23;
  // Telephone Extension
  string telephone_extension = 24;
  // Telephone Type
  TelephoneType telephone_type = 25;
  // Maps to MobilePhoneNumber
  api.typesv2.common.PhoneNumber mobile_number = 26;
  // Maps to EMailId
  string email_id = 27;
}

message ExperianDetails {
  // Maps to Income
  google.type.Money income = 1;
  // Maps to MaritalStatus
  string marital_status = 2;
  // Maps to EmploymentStatus
  string employment_status = 3;
  // Maps to TimeWithEmployer
  int64 time_with_employer = 4;
  // Maps to NumberOfMajorCreditCardHeld
  int64 number_of_major_credit_card_held = 5;
}

message PerformExperianHardPullInquiryRequest {
  ExperianIdentification identification = 1;
  ExperianApplication application = 2;
  ExperianApplicant applicant = 3;
  ExperianDetails details = 4;
  api.typesv2.common.PostalAddress address = 5;
  // Maps to AdditionalAddressFlag/Flag = "Y" / "N"
  bool additional_address_flag = 6;
  api.typesv2.common.PostalAddress additional_address = 7;
}

message ExperianResponseHeader {
  string system_code = 1 [json_name = "SystemCode"];
  string message_text = 2 [json_name = "MessageText"];
  string report_date = 3 [json_name = "ReportDate"];
  string report_time = 4 [json_name = "ReportTime"];
}

message ExperianUserMessage {
  string user_message_text = 1 [json_name = "UserMessageText"];
}

message ExperianCreditProfileHeader {
  string enquiry_username = 1 [json_name = "Enquiry_Username"];
  string report_date = 2 [json_name = "ReportDate"];
  int64 report_time = 3 [json_name = "ReportTime"];
  string version = 4 [json_name = "Version"];
  int64 report_number = 5 [json_name = "ReportNumber"];
  string subscriber = 6 [json_name = "Subscriber"];
  string subscriber_name = 7 [json_name = "Subscriber_Name"];
}

message ExperianAddress_Proto {
  string flat_no_plot_no_house_no = 1 [json_name = "FlatNoPlotNoHouseNo"];
  string bldg_no_society_name = 2 [json_name = "BldgNoSocietyName"];
  string road_no_name_area_locality = 3 [json_name = "RoadNoNameAreaLocality"];
  string city = 4 [json_name = "City"];
  string landmark = 5 [json_name = "Landmark"];
  string state = 6 [json_name = "State"];
  string pin_code = 7 [json_name = "PINCode"];
  string country_code = 8 [json_name = "Country_Code"];
}

message ExperianCurrentApplicantDetails {
  int64 std_phone_number = 1 [json_name = "STDPhoneNumber"];
  string gender_code = 2 [json_name = "Gender_Code"];
  string first_name = 3 [json_name = "First_Name"];
  string surname = 4 [json_name = "Last_Name"];
  string middle_name1 = 5 [json_name = "Middle_Name1"];
  string middle_name2 = 6 [json_name = "Middle_Name2"];
  string middle_name3 = 7 [json_name = "Middle_Name3"];
  string income_tax_pan = 8 [json_name = "IncomeTaxPan"];
  int64 pan_issue_date = 9 [json_name = "PAN_Issue_Date"];
  int64 pan_expiration_date = 10 [json_name = "PAN_Expiration_Date"];
  string passport_number = 11 [json_name = "Passport_Number"];
  int64 passport_issue_date = 12 [json_name = "Passport_Issue_Date"];
  int64 passport_expiration_date = 13 [json_name = "Passport_Expiration_Date"];
  string voter_identity_card = 14 [json_name = "Voter_s_Identity_Card"];
  int64 voter_id_issue_date = 15 [json_name = "Voter_ID_Issue_Date"];
  int64 voter_id_expiration_date = 16 [json_name = "Voter_ID_Expiration_Date"];
  int64 driver_license_number = 17 [json_name = "Driver_License_Number"];
  int64 driver_license_issue_date = 18 [json_name = "Driver_License_Issue_Date"];
  int64 driver_license_expiration_date = 19 [json_name = "Driver_License_Expiration_Date"];
  string ration_card_number = 20 [json_name = "Ration_Card_Number"];
  int64 ration_card_issue_date = 21 [json_name = "Ration_Card_Issue_Date"];
  int64 ration_card_expiration_date = 22 [json_name = "Ration_Card_Expiration_Date"];
  string universal_id_number = 23 [json_name = "Universal_ID_Number"];
  int64 universal_id_issue_date = 24 [json_name = "Universal_ID_Issue_Date"];
  int64 universal_id_expiration_date = 25 [json_name = "Universal_ID_Expiration_Date"];
  int64 date_of_birth = 26 [json_name = "Date_Of_Birth_Applicant"];
  string phone_number_str = 27 [json_name = "Telephone_Number_Applicant_1st"];
  string telephone_extension = 28 [json_name = "Telephone_Extension"];
  string telephone_type_str = 29 [json_name = "Telephone_Type"];
  string mobile_phone_str = 30 [json_name = "MobilePhoneNumber"];
  string email_id = 31 [json_name = "EMailId"];
}

message ExperianCurrentOtherDetails {
  int64 income = 1 [json_name = "Income"];
  string marital_status = 2 [json_name = "Marital_Status"];
  string employment_status = 3 [json_name = "Employment_Status"];
  int64 time_with_employer = 4 [json_name = "Time_with_Employer"];
  int64 number_of_major_credit_card_held = 5 [json_name = "Number_of_Major_Credit_Card_Held"];
}

message ExperianCurrentApplicationDetails {
  string enquiry_reason = 1 [json_name = "Enquiry_Reason"];
  string finance_purpose = 2 [json_name = "Finance_Purpose"];
  int64 amount_financed = 3 [json_name = "Amount_Financed"];
  int64 duration_of_agreement = 4 [json_name = "Duration_Of_Agreement"];
  ExperianCurrentApplicantDetails current_applicant_details = 5 [json_name = "Current_Applicant_Details"];
  ExperianCurrentOtherDetails current_other_details = 6 [json_name = "Current_Other_Details"];
  ExperianAddress_Proto current_applicant_address_details = 7 [json_name = "Current_Applicant_Address_Details"];
  ExperianAddress_Proto current_applicant_additional_address_details = 8 [json_name = "Current_Applicant_Additional_Address_Details"];
}

message ExperianCurrentApplication {
  ExperianCurrentApplicationDetails current_application_details = 1 [json_name = "Current_Application_Details"];
  string asset_classification = 4 [json_name = "AssetClassification"];
}

message ExperianCAISCreditAccount {
  int64 credit_account_total = 1 [json_name = "CreditAccountTotal"];
  int64 credit_account_active = 2 [json_name = "CreditAccountActive"];
  int64 credit_account_default = 3 [json_name = "CreditAccountDefault"];
  int64 credit_account_closed = 4 [json_name = "CreditAccountClosed"];
  int64 cad_suit_filed_current_balance = 5 [json_name = "CADSuitFiledCurrentBalance"];
}

message ExperianCAISTotalOutstandingBalance {
  int64 outstanding_balance_secured = 1 [json_name = "Outstanding_Balance_Secured"];
  int64 outstanding_balance_secured_percentage = 2 [json_name = "Outstanding_Balance_Secured_Percentage"];
  int64 outstanding_balance_unsecured = 3 [json_name = "Outstanding_Balance_UnSecured"];
  int64 outstanding_balance_unsecured_percentage = 4 [json_name = "Outstanding_Balance_UnSecured_Percentage"];
  int64 outstanding_balance_all = 5 [json_name = "Outstanding_Balance_All"];
}

message ExperianCAISSummary {
  ExperianCAISCreditAccount credit_account = 1 [json_name = "Credit_Account"];
  ExperianCAISTotalOutstandingBalance total_outstanding_balance = 2 [json_name = "Total_Outstanding_Balance"];
}

message ExperianCAISAccountHistory {
  int64 year = 1 [json_name = "Year"];
  int64 month = 2 [json_name = "Month"];
  int64 days_past_due = 3 [json_name = "Days_Past_Due"];
  string asset_classification = 4 [json_name = "Asset_Classification"];
}

message ExperianAdvancedAccountHistory_Proto {
  int64 year = 1 [json_name = "Year"];
  int64 month = 2 [json_name = "Month"];
  string account_status = 3 [json_name = "AccountStatus"];
  int64 actual_payment_amount = 4 [json_name = "ActualPaymentAmount"];
  int64 current_balance = 5 [json_name = "CurrentBalance"];
  int64 credit_limit_amount = 6 [json_name = "CreditLimitAmount"];
  int64 amount_past_due = 7 [json_name = "AmountPastDue"];
  string payment_rating = 8 [json_name = "PaymentRating"];
  int64 cash_limit = 9 [json_name = "CashLimit"];
  int64 highest_credit_or_original_loan_amount = 10 [json_name = "HighestCreditOrOriginalLoanAmount"];
  int64 emi_amount = 11 [json_name = "EMIAmount"];
}

message ExperianCAISHolderDetails {
  string surname_non_normalized = 1 [json_name = "Surname_Non_Normalized"];
  string first_name_non_normalized = 2 [json_name = "First_Name_Non_Normalized"];
  string middle_name1_non_normalized = 3 [json_name = "Middle_Name_1_Non_Normalized"];
  string middle_name2_non_normalized = 4 [json_name = "Middle_Name_2_Non_Normalized"];
  string middle_name3_non_normalized = 5 [json_name = "Middle_Name_3_Non_Normalized"];
  string alias = 6 [json_name = "Alias"];
  string gender_code = 7 [json_name = "Gender_Code"];
  string income_tax_pan = 8 [json_name = "Income_TAX_PAN"];
  string date_of_birth = 9 [json_name = "Date_of_birth"];
}

message ExperianCAISHolderPhoneDetails_Proto {
  string telephone_number = 1 [json_name = "Telephone_Number"];
  string telephone_type = 2 [json_name = "Telephone_Type"];
  string telephone_extension = 3 [json_name = "TelephoneExtension"];
  string mobile_telephone_number = 4 [json_name = "MobileTelephoneNumber"];
  string fax_number = 5 [json_name = "FaxNumber"];
  string email_id = 6 [json_name = "EMailId"];
}

message ExperianCAISHolderIDDetails {
  string income_tax_pan = 1 [json_name = "Income_TAX_PAN"];
  int64 pan_issue_date = 2 [json_name = "PAN_Issue_Date"];
  int64 pan_expiration_date = 3 [json_name = "PAN_Expiration_Date"];
  string driver_license_number = 4 [json_name = "Driver_License_Number"];
  int64 driver_license_issue_date = 5 [json_name = "Driver_License_Issue_Date"];
  int64 driver_license_expiration_date = 6 [json_name = "Driver_License_Expiration_Date"];
  string email_id = 7 [json_name = "EMailId"];
  string passport_number = 8 [json_name = "PassportNumber"];
  int64 passport_issue_date = 9 [json_name = "PassportIssueDate"];
  int64 passport_expiration_date = 10 [json_name = "PassportExpirationDate"];
  string voter_id_number = 11 [json_name = "VoterIDNumber"];
  int64 voter_id_issue_date = 12 [json_name = "VoterIDIssueDate"];
  int64 voter_id_expiration_date = 13 [json_name = "VoterIDExpirationDate"];
  string ration_card_number = 14 [json_name = "RationCardNumber"];
  int64 ration_card_issue_date = 15 [json_name = "RationCardIssueDate"];
  int64 ration_card_expiration_date = 16 [json_name = "RationCardExpirationDate"];
  string universal_id_number = 17 [json_name = "UniversalIDNumber"];
  int64 universal_id_issue_date = 18 [json_name = "UniversalIDIssueDate"];
  int64 universal_id_expiration_date = 19 [json_name = "UniversalIDExpirationDate"];
}

message ExperianCAISAccountDetails {
  string identification_number = 1 [json_name = "Identification_Number"];
  string subscriber_name = 2 [json_name = "Subscriber_Name"];
  string account_number = 3 [json_name = "Account_Number"];
  string portfolio_type = 4 [json_name = "Portfolio_Type"];
  string account_type = 5 [json_name = "Account_Type"];
  string open_date = 6 [json_name = "Open_Date"];
  int64 highest_credit_or_original_loan_amount = 7 [json_name = "Highest_Credit_or_Original_Loan_Amount"];
  string terms_duration = 8 [json_name = "Terms_Duration"];
  string terms_frequency = 9 [json_name = "Terms_Frequency"];
  int64 scheduled_monthly_payment_amount = 10 [json_name = "Scheduled_Monthly_Payment_Amount"];
  string account_status = 11 [json_name = "Account_Status"];
  string payment_rating = 12 [json_name = "Payment_Rating"];
  string payment_history_profile = 13 [json_name = "Payment_History_Profile"];
  string special_comment = 14 [json_name = "Special_Comment"];
  int64 current_balance = 15 [json_name = "Current_Balance"];
  int64 amount_past_due = 16 [json_name = "Amount_Past_Due"];
  int64 original_charge_off_amount = 17 [json_name = "Original_Charge_off_Amount"];
  string date_reported = 18 [json_name = "Date_Reported"];
  string date_of_first_delinquency = 19 [json_name = "Date_Of_First_Delinquency"];
  int64 date_closed = 20 [json_name = "Date_Closed"];
  int64 date_of_last_payment = 21 [json_name = "Date_of_Last_Payment"];
  string suit_filed_willful_default_written_off_status = 22 [json_name = "SuitFiledWillfulDefaultWrittenOffStatus"];
  string suit_filed_wilful_default = 23 [json_name = "SuitFiled_WilfulDefault"];
  string written_off_settled_status = 24 [json_name = "Written_off_Settled_Status"];
  int64 value_of_credits_last_month = 25 [json_name = "Value_of_Credits_Last_Month"];
  string occupation_code = 26 [json_name = "Occupation_Code"];
  int64 settlement_amount = 27 [json_name = "Settlement_Amount"];
  int64 value_of_collateral = 28 [json_name = "Value_of_Collateral"];
  string type_of_collateral = 29 [json_name = "Type_of_Collateral"];
  int64 written_off_amt_total = 30 [json_name = "Written_Off_Amt_Total"];
  int64 written_off_amt_principal = 31 [json_name = "Written_Off_Amt_Principal"];
  double rate_of_interest = 32 [json_name = "Rate_of_Interest"];
  int64 repayment_tenure = 33 [json_name = "Repayment_Tenure"];
  string promotional_rate_flag = 34 [json_name = "Promotional_Rate_Flag"];
  int64 income = 35 [json_name = "Income"];
  string income_indicator = 36 [json_name = "Income_Indicator"];
  string income_frequency_indicator = 37 [json_name = "Income_Frequency_Indicator"];
  string default_status_date = 38 [json_name = "DefaultStatusDate"];
  string litigation_status_date = 39 [json_name = "LitigationStatusDate"];
  string write_off_status_date = 40 [json_name = "WriteOffStatusDate"];
  string date_of_addition = 41 [json_name = "DateOfAddition"];
  string currency_code = 42 [json_name = "CurrencyCode"];
  string subscriber_comments = 43 [json_name = "Subscriber_comments"];
  string consumer_comments = 44 [json_name = "Consumer_comments"];
  string account_holder_type_code = 45 [json_name = "AccountHoldertypeCode"];
  repeated ExperianCAISAccountHistory cais_account_history = 46 [json_name = "CAIS_Account_History"];
  ExperianCAISHolderDetails cais_holder_details = 47 [json_name = "CAIS_Holder_Details"];
  ExperianAddress_Proto cais_holder_address_details = 48 [json_name = "CAIS_Holder_Address_Details"];
  ExperianCAISHolderPhoneDetails_Proto cais_holder_phone_details = 49 [json_name = "CAIS_Holder_Phone_Details"];
  ExperianCAISHolderIDDetails cais_holder_id_details = 50 [json_name = "CAIS_Holder_ID_Details"];
  int64 credit_limit_amount = 51 [json_name = "CreditLimitAmount"];
  repeated ExperianAdvancedAccountHistory_Proto advanced_account_history = 52 [json_name = "Advanced_Account_History"];
  string customer_segment = 53 [json_name = "CustomerSegment"];
}

message ExperianCAISAccount {
  ExperianCAISSummary cais_summary = 1 [json_name = "CAIS_Summary"];
  repeated ExperianCAISAccountDetails cais_account_details = 2 [json_name = "CAIS_Account_DETAILS"];
}

message ExperianMatchResult {
  string exact_match = 1 [json_name = "Exact_match"];
}

message ExperianTotalCAPSSummary {
  int32 total_caps_last_7_days = 1 [json_name = "TotalCAPSLast7Days"];
  int32 total_caps_last_30_days = 2 [json_name = "TotalCAPSLast30Days"];
  int32 total_caps_last_90_days = 3 [json_name = "TotalCAPSLast90Days"];
  int32 total_caps_last_180_days = 4 [json_name = "TotalCAPSLast180Days"];
}

message ExperianCAPSSummary {
  int32 caps_last_7_days = 1 [json_name = "CAPSLast7Days"];
  int32 caps_last_30_days = 2 [json_name = "CAPSLast30Days"];
  int32 caps_last_90_days = 3 [json_name = "CAPSLast90Days"];
  int32 caps_last_180_days = 4 [json_name = "CAPSLast180Days"];
}

message ExperianCAPSApplicationDetails {
  string subscriber_code = 1 [json_name = "Subscriber_code"];
  string subscriber_name = 2 [json_name = "Subscriber_Name"];
  string date_of_request = 3 [json_name = "Date_of_Request"];
  string report_time = 4 [json_name = "ReportTime"];
  string report_number = 5 [json_name = "ReportNumber"];
  string enquiry_reason = 6 [json_name = "Enquiry_Reason"];
  string finance_purpose = 7 [json_name = "Finance_Purpose"];
  int64 amount_financed = 8 [json_name = "Amount_Financed"];
  int64 duration_of_agreement = 9 [json_name = "Duration_Of_Agreement"];
  ExperianCurrentApplicantDetails caps_applicant_details = 10 [json_name = "CAPS_Applicant_Details"];
  ExperianCurrentOtherDetails caps_other_details = 11 [json_name = "CAPS_Other_Details"];
  ExperianAddress_Proto caps_applicant_address_details = 12 [json_name = "CAPS_Applicant_Address_Details"];
  ExperianAddress_Proto caps_applicant_additional_address_details = 13 [json_name = "CAPS_Applicant_Additional_Address_Details"];
}

message ExperianCAPS {
  ExperianCAPSSummary caps_summary = 1 [json_name = "CAPS_Summary"];
  repeated ExperianCAPSApplicationDetails caps_application_details = 2 [json_name = "CAPS_Application_Details"];
}

message ExperianNonCreditCAPSSummary {
  int32 non_credit_caps_last_7_days = 1 [json_name = "NonCreditCAPSLast7Days"];
  int32 non_credit_caps_last_30_days = 2 [json_name = "NonCreditCAPSLast30Days"];
  int32 non_credit_caps_last_90_days = 3 [json_name = "NonCreditCAPSLast90Days"];
  int32 non_credit_caps_last_180_days = 4 [json_name = "NonCreditCAPSLast180Days"];
}

message ExperianNonCreditCAPS {
  ExperianNonCreditCAPSSummary non_credit_caps_summary = 1 [json_name = "NonCreditCAPS_Summary"];
  repeated ExperianCAPSApplicationDetails caps_application_details = 2 [json_name = "CAPS_Application_Details"];
}

message ExperianScore {
  int32 bureau_score = 1 [json_name = "BureauScore"];
  string bureau_score_confid_level = 2 [json_name = "BureauScoreConfidLevel"];
  string credit_rating = 3 [json_name = "CreditRating"];
}

message ExperianINProfileResponse {
  ExperianResponseHeader header = 1 [json_name = "Header"];
  ExperianUserMessage user_message = 2 [json_name = "UserMessage"];
  ExperianCreditProfileHeader credit_profile_header = 3 [json_name = "CreditProfileHeader"];
  ExperianCurrentApplication current_application = 4 [json_name = "Current_Application"];
  ExperianCAISAccount cais_account = 5 [json_name = "CAIS_Account"];
  ExperianMatchResult match_result = 6 [json_name = "Match_result"];
  ExperianTotalCAPSSummary total_caps_summary = 7 [json_name = "TotalCAPS_Summary"];
  ExperianCAPS caps = 8 [json_name = "CAPS"];
  ExperianNonCreditCAPS non_credit_caps = 9 [json_name = "NonCreditCAPS"];
  ExperianScore score = 10 [json_name = "SCORE"];
}

message PerformExperianHardPullInquiryResponse {
  rpc.Status status = 1;
  ExperianHardPullResponse experian_response = 2;

  message ExperianHardPullResponse {
    ExperianINProfileResponse response = 1 [json_name = "INProfileResponse"];
  }
}

enum TelephoneType {
  TELEPHONE_TYPE_UNSPECIFIED = 0;
  TELEPHONE_TYPE_MOBILE_PHONE = 1;
  TELEPHONE_TYPE_HOME_PHONE = 2;
  TELEPHONE_TYPE_OFFICE_PHONE = 3;
}
