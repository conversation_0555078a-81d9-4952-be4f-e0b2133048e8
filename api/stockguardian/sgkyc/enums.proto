//go:generate gen_sql -types=KYCVendorDataType

syntax = "proto3";

package stockguardian.sgkyc;

option go_package = "github.com/epifi/gringott/api/stockguardian/sgkyc";

// KYCVendorDataType defines the type of payload for a given KYC Attempt.
// There can be different types of payload with different TTL.
// For e.g. CKYC Download response data.
enum KYCVendorDataType {
  KYC_VENDOR_DATA_TYPE_UNSPECIFIED = 0;
  // CKYC Record fetched from CERSAI CKYC Download API
  KYC_VENDOR_DATA_TYPE_CKYC_DOWNLOAD = 1;
  // OKYC stands for Offline KYC
  KYC_VENDOR_DATA_TYPE_OKYC = 2;
}

enum IdProofType {
  ID_PROOF_TYPE_UNSPECIFIED = 0;
  ID_PROOF_TYPE_PASSPORT = 1;
  ID_PROOF_TYPE_VOTER_ID = 2;
  ID_PROOF_TYPE_PAN = 3;
  ID_PROOF_TYPE_DRIVING_LICENSE = 4;
  ID_PROOF_TYPE_UID = 5;  // Aadhaar number
  ID_PROOF_TYPE_NREGA_JOB_CARD = 6;
  ID_PROOF_TYPE_NATIONAL_POPULATION_REGISTER_LETTER = 7;
  ID_PROOF_TYPE_CKYC_RECORD = 8;
  ID_PROOF_TYPE_EKYC_AUTHENTICATION = 9;
  ID_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION = 10;
  ID_PROOF_TYPE_OFFLINE_OTHERS = 11;
  ID_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT = 12;
  ID_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED = 13;
}

enum SubmittedDocType {
  SUBMITTED_DOC_TYPE_UNSPECIFIED = 0;
  SUBMITTED_DOC_TYPE_CERTIFIED_COPIES = 1;
}

enum RelatedPersonType {
  RELATED_PERSON_TYPE_UNSPECIFIED = 0;
  RELATED_PERSON_TYPE_GUARDIAN_OF_MINOR = 1;
  RELATED_PERSON_TYPE_ASSIGNEE = 2;
  RELATED_PERSON_TYPE_AUTHORISED_REPRESENTATIVE = 3;
}

enum AddressType {
  ADDRESS_TYPE_UNSPECIFIED = 0;
  ADDRESS_TYPE_RESIDENTIAL_OR_BUSINESS = 1;
  ADDRESS_TYPE_RESIDENTIAL = 2;
  ADDRESS_TYPE_BUSINESS = 3;
  ADDRESS_TYPE_REGI_OFFICE = 4;
}
