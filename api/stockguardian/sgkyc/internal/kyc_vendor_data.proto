//go:generate gen_sql -types=KYCVendorDataPayload

syntax = "proto3";

package stockguardian.sgkyc;

import "api/stockguardian/sgkyc/enums.proto";
import "api/stockguardian/sgkyc/internal/ckyc_download.proto";
import "api/stockguardian/sgkyc/internal/okyc.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgkyc";

// KYCVendorData has user KYC data that is fetched from vendor.
// As per regulation, this data can't be kept permanently. Records are deleted
// regularly with TTL assigned on each record.
message KYCVendorData {
  // id is the unique record identifier
  string id = 1;

  // reference_id maps kyc vendor data record to kyc summary of each kyc type
  string reference_id = 2;

  // KYCVendorDataType defines the type of payload for a given KYC Attempt.
  // There can be different types of payload with different TTL.
  // For e.g. CKYC Download response data.
  KYCVendorDataType payload_type = 3;

  // KYCVendorDataPayload is union of all the different KYC data api.typesv2.
  // Based on the KYCVendorDataType, specific KYC payload is extracted.
  KYCVendorDataPayload payload = 4;

  // Time by which record payload must be purged
  // null represents payload need not be deleted
  google.protobuf.Timestamp delete_by = 5;

  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp deleted_at = 8;
}

// KYCVendorDataPayload is union of all the different KYC data api.typesv2.
// Based on the KYCVendorDataType, specific KYC payload is extracted.
message KYCVendorDataPayload {
  oneof Payload {
    // ckyc_download stores the response from vendor's CKYC Download API.
    // Details from this response are used in stockguardian customer creation.
    CKYCDownloadVendorData ckyc_download = 1;

    // OKYC stores the response from vendor's OKYC.
    OKYCPayload okyc = 2;
  }
}

// CKYCDownloadVendorData stores data for all the CKYC Download
// response payloads fetched for a user. Contains PII.
message CKYCDownloadVendorData {
  repeated CKYCDownloadPayload payloads = 1;
}
