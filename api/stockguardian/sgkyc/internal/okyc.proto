syntax = "proto3";

package stockguardian.sgkyc;

import "api/typesv2/common/address.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/name.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgkyc";

// OKYCPayload contains data fetched from OKYC verification.
message OKYCPayload {
  OKYCPersonalDetails personal_data = 1;
  string aadhaar_xml = 2;
  string user_selfie_url = 3;
  string aadhaar_photo_url = 4;
}

// OKYCPersonalDetails contains personal information extracted from <PERSON>adhaar during OKYC.
message OKYCPersonalDetails {
  string masked_aadhaar = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.common.Gender gender = 3;
  google.type.Date dob = 4;
  api.typesv2.common.PostalAddress address = 5;
}
