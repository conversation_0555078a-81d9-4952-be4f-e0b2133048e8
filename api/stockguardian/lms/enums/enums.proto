//go:generate gen_sql -types=LmsPartner,PaymentProtocol,PaymentProvenance,LoanRepaymentType,RecurringPaymentExecutionType
syntax = "proto3";

package stockguardian.lms.enums;

option go_package = "github.com/epifi/gringott/api/stockguardian/lms/enums";
option java_package = "com.github.epifi.gringott.api.stockguardian.lms.enums";

enum LmsPartner {
  LMS_PARTNER_UNSPECIFIED = 0;
  LMS_PARTNER_FINFLUX = 1;
}

// denotes the payment protocol using which the loan transaction (disbursal, repayment etc) was performed.
enum PaymentProtocol {
  PAYMENT_PROTOCOL_UNSPECIFIED = 0;
  PAYMENT_PROTOCOL_NACH = 1;
  PAYMENT_PROTOCOL_PG = 2;
  PAYMENT_PROTOCOL_UPI = 3;
  PAYMENT_PROTOCOL_NEFT = 4;
  PAYMENT_PROTOCOL_RTGS = 5;
  PAYMENT_PROTOCOL_ONLINE_TRANSFER = 6;
}

// RequestProvenance defines the source from which the payment posting request was initiated.
enum PaymentProvenance {
  PAYMENT_PROVENANCE_UNSPECIFIED = 0;
  // Request initiated by an LSP (Loan Service Provider).
  PAYMENT_PROVENANCE_LSP = 1;
  // Request initiated internally (within NBFC services).
  PAYMENT_PROVENANCE_INTERNAL = 2;
}

enum LoanRepaymentType {
  LOAN_REPAYMENT_TYPE_UNSPECIFIED = 0;
  // partial repayment of loan which does not lead to closure or cancellation of loan.
  LOAN_REPAYMENT_TYPE_PARTIAL = 1;
  // payment that leads to foreclosure of a loan.
  // pro rated interest will be applicable for foreclosure unless waived.
  LOAN_REPAYMENT_TYPE_FORECLOSURE = 2;
  // automated deduction of EMI
  LOAN_REPAYMENT_TYPE_EMI = 3;
  // payment that leads to cancellation of a loan.
  // interest will be waived of in case of cancellation.
  // cancellation is allowed only until a certain time period after loan disbursal.
  LOAN_REPAYMENT_TYPE_CANCELLATION = 4;
}

enum RecurringPaymentExecutionType {
  EXECUTION_TYPE_UNSPECIFIED = 0;
  // first recurring payment execution which usually happens on the EMI due date
  EXECUTION_TYPE_FIRST = 1;
  // second recurring payment execution which usually happens at the end of grace period
  EXECUTION_TYPE_SECOND = 2;
}

enum LoanStatus {
  LOAN_STATUS_UNSPECIFIED = 0;
  LOAN_STATUS_SUBMITTED_AND_AWAITING_APPROVAL = 1;
  LOAN_STATUS_APPROVED = 2;
  LOAN_STATUS_ACTIVE = 3;
  LOAN_STATUS_TRANSFER_IN_PROGRESS = 4;
  LOAN_STATUS_TRANSFER_ON_HOLD = 5;
  LOAN_STATUS_WITHDRAWN_BY_CLIENT = 6;
  LOAN_STATUS_REJECTED = 7;
  LOAN_STATUS_CLOSED = 8;
  LOAN_STATUS_WRITTEN_OFF = 9;
  LOAN_STATUS_RESCHEDULED = 10;
  LOAN_STATUS_OVERPAID = 11;
}
