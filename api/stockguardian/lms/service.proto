syntax = "proto3";

package stockguardian.lms;

import "api/rpc/status.proto";
import "api/stockguardian/lms/enums/enums.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/lms";
option java_package = "com.github.epifi.gringott.api.stockguardian.lms";

service LmsService {
  // CreateUser creates a user in the partner LMS system
  rpc CreateUser (CreateUserRequest) returns (CreateUserResponse);
  // CalculateLoanSchedule can be used for previewing before creating the loan in our system
  rpc CalculateLoanSchedule (CalculateLoanScheduleRequest) returns (CalculateLoanScheduleResponse);
  // CreateLoan will create a loan in the partner LMS system in pending status
  rpc CreateLoan (CreateLoanRequest) returns (CreateLoanResponse);
  // DisburseLoan will mark an existing loan as disbursed in the partner LMS system
  rpc DisburseLoan (DisburseLoanRequest) returns (DisburseLoanResponse);
  // GetLoanSchedule fetches the repayment schedule of an already created loan.
  rpc GetLoanSchedule (GetLoanScheduleRequest) returns (GetLoanScheduleResponse);
  // GetLoanCancellationDetails returns cancellation amount if cancellation is allowed,
  // returns FAILED_PRECONDITION if cancellation is not allowed
  rpc GetLoanCancellationDetails (GetLoanCancellationDetailsRequest) returns (GetLoanCancellationDetailsResponse);
  // GetLoanForeclosureDetails returns foreclosure amount for a loan,
  // will be 0 if the loan is already closed
  rpc GetLoanForeclosureDetails (GetLoanForeclosureDetailsRequest) returns (GetLoanForeclosureDetailsResponse);
  // RecordLoanRepayment can be used to record repayments towards a loan in the LMS system,
  // based on the payment amount and date, the loan can either be Foreclosed or a Regular repayment will be recorded
  rpc RecordLoanRepayment (RecordLoanRepaymentRequest) returns (RecordLoanRepaymentResponse);
  // InitiateBatchRecurringPaymentExecution creates the recurring payment execution entries in the DB and
  // publishes a message for each execution to a queue for processing the recurring payments.
  // Initiation of the payment and status polling will be done by the queue consumer
  rpc InitiateBatchRecurringPaymentExecution (InitiateBatchRecurringPaymentExecutionRequest) returns (InitiateBatchRecurringPaymentExecutionResponse);
  // GetLoanDetails fetches the current details of a loan from the partner LMS system
  rpc GetLoanDetails (GetLoanDetailsRequest) returns (GetLoanDetailsResponse);
}

message InitiateBatchRecurringPaymentExecutionRequest {
  enums.RecurringPaymentExecutionType execution_type = 1;
  // lms partner from which the input is coming
  enums.LmsPartner lms_partner = 2;
  oneof batch_input {
    // input csv file containing loan accounts and corresponding due amounts need to be passed in case of finflux
    bytes input_file = 3;
  }
}
message GetLoanDetailsRequest {
  string loan_external_id = 1;
}

message GetLoanDetailsResponse {
  rpc.Status status = 1;
  // unique identifier of the loan in Finflux
  string loan_id = 2;
  // account number of loan account
  string loan_account_number = 3;
  // status of the loan
  enums.LoanStatus loan_status = 4;
}

message InitiateBatchRecurringPaymentExecutionResponse {
  rpc.Status status = 1;
  // identifier that can be used to fetch the executions that are initiated in this batch
  string batch_id = 2;
}

message RecordLoanRepaymentRequest {
  string loan_account_id = 1 [(validate.rules).string.min_len = 1];
  // date on which amount was credited to the NBFC pool account
  // this should be in IST
  google.type.Date transaction_date = 2 [(validate.rules).message.required = true];
  google.type.Money amount = 3 [(validate.rules).message.required = true];
  // Unique Transaction Reference number
  string transaction_utr = 4 [(validate.rules).string.min_len = 1];
  lms.enums.PaymentProtocol payment_protocol = 5 [(validate.rules).enum = {not_in: [0]}];
  // Denotes a unique request id with which the client initiated the payment posting request.
  string client_request_id = 6 [(validate.rules).string.min_len = 1];
  LoanRepaymentType repayment_type = 7 [(validate.rules).enum = {not_in: [0]}];
  lms.enums.PaymentProvenance payment_provenance = 8 [(validate.rules).enum = {not_in: [0]}];
  // This will be a remark related to repayment that will be passed to LMS
  string note = 9;
  enum LoanRepaymentType {
    LOAN_REPAYMENT_TYPE_UNSPECIFIED = 0;
    // Lumpsum is internally categorised into foreclosure or partial payment
    LOAN_REPAYMENT_TYPE_LUMPSUM = 1;
    // automatic deduction of EMI
    LOAN_REPAYMENT_TYPE_EMI = 2;
    // should be used in case the intent of the user is to cancel the loan
    LOAN_REPAYMENT_TYPE_CANCELLATION = 3;
  }
}

message RecordLoanRepaymentResponse {
  rpc.Status status = 1;
}

message GetLoanForeclosureDetailsRequest {
  string loan_account_id = 1 [(validate.rules).string.min_len = 1];
  // foreclosure amount will be calculated as on foreclosure_date
  // this should be in IST
  google.type.Date foreclosure_date = 2 [(validate.rules).message.required = true];
}

message GetLoanForeclosureDetailsResponse {
  rpc.Status status = 1;
  google.type.Money foreclosure_amount = 2;
  google.type.Money interest_amount = 3;
  google.type.Money principal_amount = 4;
  google.type.Money other_charges = 5;
  google.type.Money penalty_charges = 6;
  google.type.Money fee_charges = 7;
}

message GetLoanCancellationDetailsRequest {
  string loan_account_id = 1 [(validate.rules).string.min_len = 1];
  // cancellation amount will be calculated as on cancellation_date
  // this should be in IST
  google.type.Date cancellation_date = 2 [(validate.rules).message.required = true];
}

message GetLoanCancellationDetailsResponse {
  rpc.Status status = 1;
  google.type.Money cancellation_amount = 2;
}

message CreateUserRequest {
  // External id will be shared with external systems like partner LMS system for deduplication and reference purposes
  string customer_external_id = 1 [(validate.rules).string.min_len = 1];
  api.typesv2.common.Name name = 2 [(validate.rules).message.required = true];
  api.typesv2.common.PhoneNumber phone_number = 3 [(validate.rules).message.required = true];
  string email = 4;
  api.typesv2.common.PostalAddress permanent_addresses = 5 [(validate.rules).message.required = true];
  google.type.Date date_of_birth = 6 [(validate.rules).message.required = true];
  string pan = 7 [(validate.rules).string.min_len = 1];
  api.typesv2.common.PostalAddress communication_addresses = 8 [(validate.rules).message.required = true];
  api.typesv2.common.PostalAddress kyc_address = 9 [(validate.rules).message.required = true];
  // only male, female and transgender are supported in credit bureau
  api.typesv2.common.Gender gender = 10 [(validate.rules).enum = {in: [1, 2, 3]}];
}

message CreateUserResponse {
  rpc.Status status = 1;
}

message CalculateLoanScheduleRequest {
  // internal identifier for the loan product
  // this product id will be mapped to a corresponding product in partner LMS system if we are using one
  string product_id = 1 [(validate.rules).string.min_len = 1];
  google.type.Money principal_amount = 2 [(validate.rules).message.required = true];
  // annualised interest rate
  double interest_rate = 3;
  // tenure in months
  int32 tenure_in_months = 4;
  double processing_fee_percentage_including_gst = 5;
  google.type.Date expected_disbursement_date = 6;
}

message CalculateLoanScheduleResponse {
  rpc.Status status = 1;
  google.type.Money total_repayment_expected = 2;
  google.type.Money net_disbursal_amount = 3;
  google.type.Money broken_period_interest = 4;
  double annual_percentage_rate = 5;
  google.type.Money total_interest_charged = 6;
  google.type.Money total_fee_charges_charged = 7;
  google.type.Money total_penalty_charges_charged = 8;
  google.type.Date loan_maturity_date = 9;
  repeated LoanInstallment installments = 10;
  google.type.Money charges_due_at_disbursement = 11;
  message LoanInstallment {
    google.type.Date due_date = 1;
    google.type.Money principal = 2;
    google.type.Money interest = 3;
    google.type.Money fees_charges = 4;
    google.type.Money penalty_charges = 5;
    google.type.Money total_due = 6;
    int32 installment_number = 7;
  }
}

message CreateLoanRequest {
  // internal identifier for the loan product
  // this product id will be mapped to a corresponding product in partner LMS system if we are using one
  string product_id = 1 [(validate.rules).string.min_len = 1];
  google.type.Money principal_amount = 2 [(validate.rules).message.required = true];
  // annualised interest rate
  double interest_rate = 3;
  // tenure in months
  int32 tenure_in_months = 4;
  double processing_fee_percentage_including_gst = 5;
  // used for communicating with partner LMS system
  string customer_external_id = 6 [(validate.rules).string.min_len = 1];
  // used for communicating with partner LMS system
  string loan_external_id = 7 [(validate.rules).string.min_len = 1];
  // repayment schedule will be created assuming that the loan is starting from expected_disbursement_date
  google.type.Date expected_disbursement_date = 8 [(validate.rules).message.required = true];
  google.type.Money amount_for_upfront_collection = 9;
}

message CreateLoanResponse {
  rpc.Status status = 1;
}

message DisburseLoanRequest {
  // internal identifier of an NBFC customer
  string customer_id = 1 [(validate.rules).string.min_len = 1];
  // used for communicating with partner LMS system
  string loan_external_id = 2 [(validate.rules).string.min_len = 1];
  // repayment schedule will be created with the loan starting from disbursement_date
  google.type.Date disbursement_date = 3 [(validate.rules).message.required = true];
  // denotes the details of the bank account where the loan amount is disbursed.
  BankAccountDetails disbursement_account_details = 4 [(validate.rules).message.required = true];
  // denotes the payment protocol used for disbursing the loan amount.
  enums.PaymentProtocol disbursement_txn_payment_protocol = 5;
  // denotes the UTR of the disbursement transaction.
  string disbursement_utr = 6;
  // identifier used to communicate with TSP to execute eNACH payments
  string recurring_payment_id = 7 [(validate.rules).string.min_len = 1];

  message BankAccountDetails {
    string account_number = 1 [(validate.rules).string.min_len = 1];
    string ifsc_code = 2 [(validate.rules).string.min_len = 1];
    string account_holder_name = 3 [(validate.rules).string.min_len = 1];
  }
}

message DisburseLoanResponse {
  rpc.Status status = 1;
  string loan_account_id = 2;
}

message GetLoanScheduleRequest {
  oneof loan_identifier {
    option (validate.required) = true;
    // internal identifier of a loan account
    string loan_account_id = 1;
    // external identifier that is generated by the loan origination system
    string loan_external_id = 2;
  }
}

message GetLoanScheduleResponse {
  rpc.Status status = 1;

  message LoanInstallment {
    int32 installment_number = 1;
    google.type.Date due_date = 2;
    google.type.Money principal_due = 3;
    google.type.Money interest_due = 4;
    google.type.Money fees_charges_due = 5;
    google.type.Money penalty_charges_due = 6;
    google.type.Money principal_paid = 7;
    google.type.Money interest_paid = 8;
    google.type.Money fees_charges_paid = 9;
    google.type.Money penalty_charges_paid = 10;
    google.type.Money total_due = 11;
    google.type.Money total_paid = 12;
    // true if all the EMI components (P, I & C) are completely paid off
    bool is_complete = 13;
    // date when all the components of the emi (P, I & C) are cleared off
    google.type.Date obligations_met_on = 14;
    google.type.Money principal_balance_outstanding = 15;
  }
  repeated LoanInstallment loan_installments = 2;

  // denotes the outstanding amount summary of the given loan.
  OutstandingAmountSummary outstanding_amount_summary = 3;
  // denotes the total overpaid amount towards the given loan.
  google.type.Money total_overpaid_amount = 4;
  google.type.Money charges_due_at_disbursement = 5;
  google.type.Money processing_fee_including_gst = 6;
  google.type.Money broken_period_interest = 7;
  google.type.Money total_repayment_expected = 8;
  double annual_percentage_rate = 9;
  // can be used to show to the user
  // this will be same as the loan account id that will be printed on all loan documents
  string customer_loan_id = 10;
  google.type.Money net_disbursal_amount = 11;
  google.type.Date disbursement_date = 12;
  // internal identifier of a loan account
  string loan_account_id = 13;

  message OutstandingAmountSummary {
    // denotes the total outstanding amount of the loan.
    google.type.Money total_outstanding_amount = 1;
    // denotes the principal component of the outstanding amount.
    google.type.Money principal_component = 2;
    // denotes the interest component of the outstanding amount.
    google.type.Money interest_component = 3;
    // denotes the fees and charges component of the outstanding amount.
    google.type.Money fees_charges_component = 4;
    // denotes the penalty charges component of the outstanding amount.
    google.type.Money penalty_charges_component = 5;
  }
}


