//go:generate gen_sql -types=RequestType,Status,FailureReason,Stage,Product
syntax = "proto3";

package stockguardian.ckyc;

option go_package = "github.com/epifi/gringott/api/stockguardian/ckyc";

enum RequestType {
  REQUEST_TYPE_UNSPECIFIED = 0;
  // Search request type is to be used when we just want to search if CKYC data is present and get the CKYC number
  REQUEST_TYPE_SEARCH = 1;
  // Download request type is to be used when we want to download the CKYC data after the CKYC search is successful
  REQUEST_TYPE_DOWNLOAD = 2 [deprecated = true];
  // Upload request type is to be used when we want to upload the data to CERSAI
  REQUEST_TYPE_UPLOAD = 3;

  // To be used when we want to download the CKYC data after the CKYC search is successful and OTP verification is done
  REQUEST_TYPE_OTP_VERIFICATION_AND_DOWNLOAD = 4;
}

enum Status {
  STATUS_UNSPECIFIED = 0;
  STATUS_IN_PROGRESS = 1;
  STATUS_SUCCESS = 2;
  STATUS_FAILED = 3;
  STATUS_EXPIRED = 4;
  STATUS_TEMPORARILY_BLOCKED = 5;
}

enum FailureReason {
  FAILURE_REASON_UNSPECIFIED = 0;
}

enum Stage {
  STAGE_UNSPECIFIED = 0;
  STAGE_CKYC_SEARCH = 1;
  STAGE_CKYC_DOWNLOAD = 2 [deprecated = true];
  STAGE_CKYC_DATA_VALIDATION = 3;
  STAGE_CKYC_UPLOAD = 4;
  STAGE_CKYC_OTP_VERIFICATION_AND_DOWNLOAD = 5;
}

enum Product {
  PRODUCT_UNSPECIFIED = 0;
  PRODUCT_PERSONAL_LOANS = 1;
}

enum CkycSummaryFieldMask {
  CKYC_SUMMARY_FIELD_MASK_UNSPECIFIED = 0;
  CKYC_SUMMARY_FIELD_MASK_STATUS = 1;
  CKYC_SUMMARY_FIELD_MASK_FAILURE_REASON = 2;
  CKYC_SUMMARY_FIELD_MASK_CURRENT_STAGE = 3;
  CKYC_SUMMARY_FIELD_MASK_STAGE_DETAILS = 4;
  CKYC_SUMMARY_FIELD_MASK_SEARCH_METADATA = 5;
  CKYC_SUMMARY_FIELD_MASK_DOWNLOAD_METADATA = 6;
  CKYC_SUMMARY_FIELD_MASK_UPLOAD_METADATA = 7;
}

enum OtpFlowStatus {
  OTP_FLOW_STATUS_UNSPECIFIED = 0;
  OTP_FLOW_STATUS_INITIATED = 1;
  OTP_FLOW_STATUS_FAILED = 2;
  OTP_FLOW_STATUS_SUCCESSFUL = 3;
  // User temporarily blocked to prevent excessive retries
  OTP_FLOW_STATUS_USER_TEMP_BLOCKED = 4;
  OTP_FLOW_STATUS_FAILED_MOBILE_NO_NOT_REGISTERED_IN_KYC_RECORD = 5;
  OTP_FLOW_STATUS_FAILED_AUTH_FACTOR_DOES_NOT_MATCH_IN_KYC = 6;
}
