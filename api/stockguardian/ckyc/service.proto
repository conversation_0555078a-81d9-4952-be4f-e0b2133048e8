syntax = "proto3";

package stockguardian.ckyc;

import "api/rpc/status.proto";
import "api/stockguardian/ckyc/enums.proto";
import "api/stockguardian/ckyc/internal/ckyc_summary.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/file/file.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/ckyc";

service Ckyc {
  rpc InitiateCKYC (InitiateCKYCRequest) returns (InitiateCKYCResponse);
  rpc CheckCKYCStatus (CheckCKYCStatusRequest) returns (CheckCKYCStatusResponse);
  rpc UploadRedactedCKYCDocuments (UploadRedactedCKYCDocumentsRequest) returns (UploadRedactedCKYCDocumentsResponse);
  rpc GetS3PreSignedURL (GetS3PreSignedURLRequest) returns (GetS3PreSignedURLResponse);
  rpc GetCKYCSummary (GetCKYCSummaryRequest) returns (GetCKYCSummaryResponse);
  // this rpc is used generate or resend the OTP
  // service decides whether to generate the OTP or resend it
  rpc GenerateOtp(GenerateOtpRequest) returns (GenerateOtpResponse);
  // this rpc is used for otp verification
  // once the otp verification done, it stores the downloaded data of user
  rpc VerifyOtp (VerifyOtpRequest) returns (VerifyOtpResponse);
}


message GenerateOtpRequest {
  string client_request_id = 1;
  string application_id = 2;
}
message GenerateOtpResponse {
  // Note: Keep these in sync with those in SG API gateway
  enum Status {
    STATUS_UNSPECIFIED = 0;
    // User's mobile number is not registered in CKYC
    STATUS_MOBILE_NOT_REGISTERED = 101;
    // User's auth factor doesn't match with that registered in CKYC
    // This can happen when user is trying to use a non-personal PAN
    STATUS_AUTH_FACTOR_MISMATCH = 102;
    // User tried to re-send OTP to their phone too quickly
    STATUS_OTP_RESEND_BLOCKED = 103;
    // User temporarily blocked to prevent excessive retries
    STATUS_GENERATION_TEMP_BLOCKED = 104;
  }

  rpc.Status status = 1;
  string display_msg = 2;
  google.protobuf.Timestamp next_attempt_at = 3;
}

message VerifyOtpRequest {
  string client_request_id = 1;
  string application_id = 2;
  string otp = 3;
}

message VerifyOtpResponse {
  // Note: Keep these in sync with those in SG API gateway
  enum Status {
    STATUS_UNSPECIFIED = 0;
    // User entered a wrong OTP
    STATUS_INVALID_OTP = 101;
    // User submitted OTP after it's expiry
    STATUS_OTP_EXPIRED = 102;
    // User temporarily blocked to prevent excessive retries
    STATUS_VERIFICATION_TEMP_BLOCKED = 103;
  }
  rpc.Status status = 1;
  string display_msg = 2;
  google.protobuf.Timestamp next_attempt_at = 3;
}

message GetCKYCSummaryRequest {
  string client_request_id = 1;
  string application_id = 2;
}

message GetCKYCSummaryResponse {
  rpc.Status status = 1;
  CkycSummary ckyc_summary = 2;
}

message CheckCKYCStatusRequest {
  string client_request_id = 1;
  string application_id = 2;
}

message CheckCKYCStatusResponse {
  rpc.Status status = 1;
  Status ckyc_status = 2;
  Stage current_stage = 3;
  FailureReason failure_reason = 4;
}

message InitiateCKYCRequest {
  string client_request_id = 1;
  RequestType request_type = 2;
  string application_id = 3;
  ClientRequestOptions client_request_options = 4;
  SensitiveClientRequestOptions sensitive_client_request_options = 5;
}

message InitiateCKYCResponse {
  rpc.Status status = 1;
  Status ckyc_status = 2;
  Stage current_stage = 3;
  FailureReason failure_reason = 4;
}

message UploadRedactedCKYCDocumentsRequest {
  string ckyc_reference_id = 1;
  string application_id = 2;
  repeated api.typesv2.common.file.File redacted_documents = 3;
  string agent_email = 4;
  // map to store document type is redacted or not
  map<string, api.typesv2.common.BooleanEnum> file_name_to_redaction_map = 5;
}

message UploadRedactedCKYCDocumentsResponse {
  rpc.Status status = 1;
}

message GetS3PreSignedURLRequest {
  // FileName to S3 URL Map
  map<string, string> file_name_s3_u_r_l_map = 1;
}

message GetS3PreSignedURLResponse {
  rpc.Status status = 1;
  // FileName to S3 Pre-Signed URL Map
  map<string, string> file_name_s3_pre_signed_u_r_l_map = 2;
}

