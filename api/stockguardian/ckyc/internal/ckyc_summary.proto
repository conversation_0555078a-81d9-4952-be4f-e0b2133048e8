//go:generate gen_sql -types=ClientRequestOptions,SensitiveClientRequestOptions,StageDetails,SearchMetadata,DownloadMetadata,UploadMetadata
syntax = "proto3";

package stockguardian.ckyc;

import "api/stockguardian/ckyc/enums.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/date.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/phone_number.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/ckyc";

message CkycSummary {
  string id = 1;
  string client_request_id = 2;
  RequestType request_type = 3;
  string application_id = 4;
  ClientRequestOptions client_request_options = 5;
  SensitiveClientRequestOptions sensitive_client_request_options = 6;
  Status status = 7;
  FailureReason failure_reason = 8;
  Stage current_stage = 9;
  StageDetails stage_details = 10;
  SearchMetadata search_metadata = 11;
  DownloadMetadata download_metadata = 12;
  UploadMetadata upload_metadata = 13;
  int64 deleted_at_unix = 14;
  google.protobuf.Timestamp completed_at = 15;
  Product product = 16;
}

message ClientRequestOptions {}

message SensitiveClientRequestOptions {
  string pan = 1;
  api.typesv2.common.Date date_of_birth = 2;
  api.typesv2.common.PhoneNumber phone_number = 3;
}

message StageDetails {
  // Details of each stage in the CKYC flow
  // This will contain the history of all the stages - search, download, upload etc.
  // The key for the map is Stage.string()
  map<string, StageInfo> stage_mapping = 1;
}

message SearchMetadata {
  // ckyc number received from the CKYC search API
  string ckyc_no = 1;
  // user photo received from the CKYC search API
  api.typesv2.common.Image user_image = 6;
  // This is the CKYC reference id, received from the CKYC search API
  string ckyc_reference_id = 3;
}

message DownloadMetadata {
  ManualRedactionInfo redaction_info = 1;
  OTPMetaData otp_meta_data = 2;
}

message UploadMetadata {}

// Message contains required information of a single stage
message StageInfo {
  // The state of current stage - InProgress, Success etc.
  Status state = 1;
  // The timestamp at which the stage was updated with the state.
  // This information will be useful for debugging in case of failures,
  // and to analyze which stages have taken longer to completion etc.
  google.protobuf.Timestamp last_updated_at = 2;
  // The timestamp at which the stage was deemed to be started.
  google.protobuf.Timestamp started_at = 3;
}

message ManualRedactionInfo {
  string reviewed_by_email = 1;
  google.protobuf.Timestamp redacted_at = 2;
  map<string, api.typesv2.common.BooleanEnum> file_name_redaction_map = 3;
}

message OTPMetaData {
  string req_id = 1;
  int32 otp_generation_request_count = 2;
  OtpFlowStatus ckyc_otp_flow_status = 3;
  // we generate the new req id once a day to prevent customer to do excessive retries
  // this captures the current req generation time
  google.protobuf.Timestamp req_id_generated_at = 4;
}
