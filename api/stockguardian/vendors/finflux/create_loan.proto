syntax = "proto3";

package stockguardian.vendors.finflux;

import "api/stockguardian/vendors/finflux/types/common.proto";
import "google/protobuf/wrappers.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/vendors/finflux";
option java_package = "com.github.epifi.gringott.api.stockguardian.vendors.finflux";

/*
 * Create Loan API - To create a new loan in the finflux system with "Submitted and pending approval" state
 * Request body is CreateLoanRequest
 * Response body is CreateLoanResponse
 */

// Example JSON:
//  {
//    "clientId": "3",
//    "productId": 4,
//    "fundId": "1",
//    "principal": 10000,
//    "numberOfRepayments": 1,
//    "externalId": "XYZ456",
//    "repaymentEvery": 1,
//    "loanTermFrequencyType": 2,
//    "repaymentFrequencyType": 2,
//    "amortizationType": 1,
//    "repaymentsStartingFromDate": "",
//    "interestType": 0,
//    "interestCalculationPeriodType": 0,
//    "brokenPeriodMethodType": "1",
//    "charges": [
//        {
//            "amount": 1.18,
//            "chargeId": 1
//        }
//    ],
//    "officeId": 1,
//    "repaymentFrequencyNthDayType": -2,
//    "interestRatePerPeriod": 0,
//    "loanTermFrequency": 1,
//    "overdueCharges": [
//        {
//            "productChargeId": 24,
//            "amount": 1.18
//        }
//    ],
//    "locale": "en",
//    "dateFormat": "dd MMMM yyyy",
//    "loanType": "individual",
//    "submittedOnDate": "10 February 2024",
//    "repeatsOnDayOfMonth": [
//        5
//    ],
//    "transactionProcessingStrategyId": "6"
//  }
message CreateLoanRequest {
  // unique identifier of the customer in Finflux system
  // e.g.: "3"
  // (mandatory)
  string client_id = 1 [json_name = "clientId"];
  // e.g.: 4
  // (mandatory)
  int32 product_id = 2 [json_name = "productId"];
  // it's basically refers to the pool fund account from which the loan will be disbursed
  // would be sending a hardcoding value for now as the disbursal is done from lender's side
  // e.g.: "1"
  // (optional)
  string fund_id = 3 [json_name = "fundId"];
  // e.g.: 10000
  // (mandatory)
  double principal = 4 [json_name = "principal"];
  // e.g.: 1
  // (mandatory)
  int32 number_of_repayments = 5 [json_name = "numberOfRepayments"];
  // e.g.: "XYZ456"
  // (mandatory)
  string external_id = 6 [json_name = "externalId"];
  // to define the repayment frequency
  // e.g.: every 1 month or every 7 days or onday (5th of every month)
  // (mandatory, use fetchProduct API to get the repayment every)
  google.protobuf.Int32Value repayment_every = 7 [json_name = "repaymentEvery"];
  // to define the loanTermFrequency's unit of time
  // e.g.: 0 – days, 1 – weeks, 2 – months, 3 - year
  // (mandatory, use fetchProduct API to get the loan term frequency type)
  google.protobuf.Int32Value loan_term_frequency_type = 8 [json_name = "loanTermFrequencyType"];
  // 0 – days, 1 – weeks, 2 - months
  // (mandatory, use fetchProduct API to get the repayment frequency type)
  google.protobuf.Int32Value repayment_frequency_type = 9 [json_name = "repaymentFrequencyType"];
  // to define the loan repayment schedule amortization
  // e.g.: 1 - Equal installments, 0 - Equal principle payments
  // (mandatory, use fetchProduct API to get the amortization type)
  google.protobuf.Int32Value amortization_type = 10 [json_name = "amortizationType"];
  // (optional)
  string repayments_starting_from_date = 11 [json_name = "repaymentsStartingFromDate"];
  // to define the interest calculation method
  // https://groww.in/calculators/flat-vs-reducing-rate-calculator
  // e.g.: 0 - declining Balance, 1 - flat
  // (mandatory, use fetchProduct API to get the interest type)
  google.protobuf.Int32Value interest_type = 12 [json_name = "interestType"];
  // to define interest calculation period
  // e.g.: 0 – daily, 1 - same as repayment period
  // (mandatory, use fetchProduct API to get the interest calculation period type)
  google.protobuf.Int32Value interest_calculation_period_type = 13 [json_name = "interestCalculationPeriodType"];
  // e.g.: "1"
  // (optional)
  string broken_period_method_type = 14 [json_name = "brokenPeriodMethodType"];
  // (optional)
  repeated types.Charge charges = 15 [json_name = "charges"];
  // represents the branch office from which the loan is being disbursed
  // (optional)
  int32 office_id = 16 [json_name = "officeId"];
  // Onday - (-2)
  // (mandatory, use fetchProduct API to get the repayment frequency nth day type)
  google.protobuf.Int32Value repayment_frequency_nth_day_type = 17 [json_name = "repaymentFrequencyNthDayType"];
  // to define interest rate for the loan
  // e.g.: 18 (for 18%)
  // (mandatory)
  google.protobuf.DoubleValue interest_rate_per_period = 18 [json_name = "interestRatePerPeriod"];
  // e.g.: 12 (for 12 months, if loanTermFrequencyType is 2), 52 (for 52 weeks, if loanTermFrequencyType is 1)
  // (mandatory, use fetchProduct API to get the loan term frequency type)
  google.protobuf.Int32Value loan_term_frequency = 19 [json_name = "loanTermFrequency"];
  // (optional)
  repeated types.OverdueCharge overdue_charges = 20 [json_name = "overdueCharges"];
  // e.g.: "en"
  // (mandatory)
  string locale = 21 [json_name = "locale"];
  // e.g.: "dd MMMM yyyy"
  // (mandatory)
  string date_format = 22 [json_name = "dateFormat"];
  // e.g.: "individual"
  // (optional)
  string loan_type = 23 [json_name = "loanType"];
  // (mandatory)
  string submitted_on_date = 24 [json_name = "submittedOnDate"];
  // (optional)
  repeated int32 repeats_on_day_of_month = 25 [json_name = "repeatsOnDayOfMonth"];
  //  to define the transaction allocation repayment strategy:
  //    Penalties, Fees, Interest, Principal order - 1
  //    Overdue/Due Fee/Int,Principal- 4
  //    Principal, Interest, Penalties, Fees Order - 5
  //    Interest, Principal, Penalties, Fees Order - 6
  //    Overdue/Int/Principal,Fees - 8
  //    Overdue/fee/int,Adv/principal - 9
  //    Adv/Principal,Penalties,Fees,Interest,Principal order - 10
  //    Overdue/Principal/Interest,Fees -12
  //    Overdue/Due Interest,Overdue/Due Penalties/Fees, Principal Order -13
  //    Overdue/Due/Adv Principal/Interest, Overdue/Due Penalties, Fees Order- 14
  //    Overdue/Due/Adv Principal/Interest, Overdue/Due Penalties/Fees Order - 15
  //    Adv/Principal,Interest,Principal,Penalties,Fees - 16
  // e.g.: 5 (for Principal, Interest, Penalties, Fees Order)
  // (mandatory, use fetchProduct API to get the transaction processing strategy id)
  string transaction_processing_strategy_id = 26 [json_name = "transactionProcessingStrategyId"];
  // (mandatory)
  string expected_disbursement_date = 27 [json_name = "expectedDisbursementDate"];
  double amount_for_upfront_collection = 28 [json_name = "amountForUpfrontCollection"];
}

// Example JSON:
//  {
//    "officeId": 1,
//    "clientId": 3,
//    "loanId": 14,
//    "resourceId": 14,
//    "status": "ACTIVE",
//    "loanAccountNo": "*********"
//  }
message CreateLoanResponse {
  // represents the branch office from which the loan is being disbursed
  int32 office_id = 1 [json_name = "officeId"];
  // unique identifier of the customer in Finflux system
  int32 client_id = 2 [json_name = "clientId"];
  // unique identifier of the loan in Finflux system
  int32 loan_id = 3 [json_name = "loanId"];
  int32 resource_id = 4 [json_name = "resourceId"];
  // e.g.: "ACTIVE"
  string status = 5 [json_name = "status"];
  // e.g.: "*********"
  string loan_account_no = 6 [json_name = "loanAccountNo"];
}
