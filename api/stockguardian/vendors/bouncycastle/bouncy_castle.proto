syntax = "proto3";

package stockguardian.vendors.bouncycastle;

option go_package = "github.com/epifi/gringott/api/stockguardian/vendors/bouncycastle";

message CkycEncryptAndSignRequest {
  string data = 1 [json_name = 'data'];
  string hash_function = 2 [json_name = 'hashFunction'];
}

message CkycEncryptAndSignResponse {
  string data = 1 [json_name = 'data'];
  ErrorInfo error_info = 2 [json_name = 'error_info'];
}

message CkycVerifyAndDecryptRequest {
  string data = 1 [json_name = 'data'];
  string hash_function = 2 [json_name = 'hashFunction'];
}

message CkycVerifyAndDecryptResponse {
  string data = 1 [json_name = 'data'];
  ErrorInfo error_info = 2 [json_name = 'error_info'];
}

message ErrorInfo {
  string errorCode = 1 [json_name = "errorCode"];
  string errorMessage = 2 [json_name = "errorMessage"];
}

message NsdlSignatureRequest {
  string data = 1 [json_name = 'data'];
}

message NsdlSignatureResponse {
  string signature = 1 [json_name = 'signature'];
  ErrorInfo error_info = 2 [json_name = 'errorInfo'];
}
