syntax = "proto3";

package stockguardian.vendors.inhouse.bre;

import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/vendors/inhouse/bre";
option java_package = "com.github.epifi.gringott.api.stockguardian.vendors.inhouse.bre";

message GetLoanDecisioningRequest {
  Values values = 1 [json_name = "values"];
  message Values {
    Input input = 1 [json_name = "input"];
    message Input {
      string customer_id = 1 [json_name = "customerId"];
      string name = 2 [json_name = "name"];
      string gender = 3 [json_name = "gender"];
      string pan = 4 [json_name = "pan"];
      string dob = 5 [json_name = "dob"];
      string address = 6 [json_name = "address"];
      int32 pincode = 7 [json_name = "pincode"];
      string employment_type = 8 [json_name = "employmentType"];
      string employer_name = 9 [json_name = "employerName"];
      string work_email = 10 [json_name = "workEmail"];
      int32 declared_income = 11 [json_name = "declaredIncome"];
      string scheme_id = 12 [json_name = "schemeId"];
      string batch_id = 13 [json_name = "batchId"];
      string client_id = 15 [json_name = "clientId"];
      PolicyParams policy_params = 16 [json_name = "policyParams"];
      string application_id = 17 [json_name = "applicationId"];
      // amount selected bu user against which we want to make the request
      int64 loan_amount = 18 [json_name = "loanAmount"];
      message PolicyParams {
        double pd_score = 3 [json_name = "pdScore"];
        string pricing_scheme = 4 [json_name = "pricingScheme"];
        int32 ever_vkyc_attempted = 5 [json_name = "everVkycAttempted"];
        string pd_score_version = 6 [json_name = "pdScoreVersion"];
        string scheme_id = 7 [json_name = "schemeId"];
        string pricing_scheme_bre = 8 [json_name = "pricingSchemeBRE"];
        string batch_id = 9 [json_name = "batchId"];
      }
    }
  }
}

message GetLoanDecisioningResponse {
  repeated Decision decision = 1 [json_name = "decision"];
  message Decision {
    string action = 1 [json_name = "action"];
    OfferDetails offer_details = 2 [json_name = "offerDetails"];
    string loan_program = 3 [json_name = "loanProgram"];
    string scheme_id = 4 [json_name = "schemeId"];
    string cust_id = 5 [json_name = "custId"];
    string client_id = 6 [json_name = "clientId"];
    string batch_id = 7 [json_name = "batchId"];
    repeated string reasons = 8 [json_name = "reasons"];
    repeated string external_reasons = 9 [json_name = "externalReasons"];
    PolicyParams policy_params = 10 [json_name = "policyParams"];

    message OfferDetails {
      double min_amount = 1 [json_name = "minAmount"];
      double max_amount = 2 [json_name = "maxAmount"];
      double max_emi_amount = 3 [json_name = "maxEmiAmount"];
      double interest_percentage = 4 [json_name = "interestPercentage"];
      double processing_fee_percentage = 5 [json_name = "processingFeePercentage"];
      int32 min_tenure_in_months = 6 [json_name = "minTenureInMonths"];
      int32 max_tenure_in_months = 7 [json_name = "maxTenureInMonths"];
      string emi_due_date = 8 [json_name = "emiDueDate"];
      double gst_percentage = 9 [json_name = "gstPercentage"];
      string valid_till_timestamp = 10 [json_name = "validTill"];
    }
  }
}

message PolicyParams {
  Pre pre = 1 [json_name = "pre"];
  Final final = 2 [json_name = "final"];
  ExecutionInfo execution_info = 3 [json_name = "executionInfo"];
  DataInfo data_info = 4 [json_name = "dataInfo"];
  ProductSpecificDataInfo product_specific_data_info = 5 [json_name = "productSpecificDataInfo"];
  message Pre {
    double pd_score = 1 [json_name = "pdScore"];
    string pd_score_version = 2 [json_name = "pdScoreVersion"];
    string scheme_id = 3 [json_name = "schemeId"];
    string batch_id = 4 [json_name = "batchId"];
    int32 ever_vkyc_attempted = 5 [json_name = "everVkycAttempted"];
    string pricing_scheme = 6 [json_name = "pricingScheme"];
  }
  message Final {
    string scheme_id = 1 [json_name = "schemeId"];
    string batch_id = 2 [json_name = "batchId"];
    string pricing_scheme_bre = 3 [json_name = "pricingSchemeBRE"];
  }
}

message ExecutionInfo {
  repeated ExecutionInfoDetails pre = 1 [json_name = "pre"];
  repeated ExecutionInfoDetails final = 2 [json_name = "final"];
}

message ExecutionInfoDetails {
  string batch_id = 1 [json_name = "batchId"];
  string pricing_scheme = 2 [json_name = "pricingSchemeBRE"];
}

message DataInfo {
  AaData aa_data = 1 [json_name = "aaData"];
  bool is_etb_user = 2 [json_name = "isEtbUser"];
  double current_emi_obligation = 3 [json_name = "currentEmiObligation"];
  message AaData {
    double median_amount_salary_last_180_days = 1 [json_name = "medianAmountSalaryLast180Days"];
  }
  bool is_b2b_salary_user = 4 [json_name = "isB2bSalaryUser"];
  double monthly_income = 5 [json_name = "monthlyIncome"];
  int32 months_since_salary_active = 6 [json_name = "monthSinceSalaryActive"];
  int32 salary_credit_day = 7 [json_name = "salaryCreditDay"];
}

// ProductSpecificDataInfo provides loan product specific data inputs to the BRE
message ProductSpecificDataInfo {
  // Details specific to insurance policy that user intends to purchase with the loan
  InsurancePolicyDetails insurance_policy_details = 1 [json_name = "insurancePolicyDetails"];
  message InsurancePolicyDetails {
    string policy_type = 1 [json_name = "policyType"];
    int32 policy_tenure_in_months = 2 [json_name = "policyTenureInMonths"];
    string policy_reference_id = 3 [json_name = "policyReferenceId"];
    double policy_premium_amount = 4 [json_name = "policyPremiumAmount"];
  }
}

message PersonalDetails {
  string dob = 1 [json_name = "dob"];
  string gender = 2 [json_name = "gender"];
  string name = 3 [json_name = "name"];
  string pan = 4 [json_name = "pan"];
  string phone_number = 5 [json_name = "phoneNumber"];
  string email = 6 [json_name = "email"];
}

message EmploymentDetails {
  string employer_name = 1 [json_name = "employerName"];
  string employment_type = 2 [json_name = "employmentType"];
  double declared_monthly_income = 3 [json_name = "declaredMonthlyIncome"];
  string work_email = 4 [json_name = "workEmail"];
  string work_address = 5 [json_name = "workAddress"];
  int32 work_pincode = 6 [json_name = "workPincode"];
}

message AddressDetails {
  string address = 1 [json_name = "address"];
  int32 pincode = 2 [json_name = "pincode"];
  string address_type = 3 [json_name = "addressType"];
}

message CustomerDetails {
  PersonalDetails personal_details = 1 [json_name = "personalDetails"];
  EmploymentDetails employment_details = 2 [json_name = "employmentDetails"];
  AddressDetails address_details = 3 [json_name = "addressDetails"];
  RequestedLoanDetails requested_loan_details = 4 [json_name = "requestedLoanDetails"];
  KycDetails kyc_details = 5 [json_name = "kycDetails"];
  AddressDetails current_location_details = 6 [json_name = "currentLocationDetails"];

  message RequestedLoanDetails {
    double desired_loan_amount = 1 [json_name = "desiredLoanAmount"];
    Duration tenure = 2 [json_name = "tenure"];
    string roi = 3 [json_name = "roi"];
    double pf_plus_gst = 4 [json_name = "pfAmountWithGst"];
    double upfront_payment_amount = 5 [json_name = "upfrontPaymentAmount"];
  }
  message KycDetails {
    int32 address_pincode = 1 [json_name = "addressPincode"];
  }
}

message GetLoanDecisioningRequestV2 {
  Values values = 1 [json_name = "values"];
  message Values {
    Input input = 1 [json_name = "input"];
    message Input {
      string customer_id = 1 [json_name = "customerId"];
      string application_id = 2 [json_name = "applicationId"];
      double loan_amount = 3 [json_name = "loanAmount"];
      string evaluation_request_time = 4 [json_name = "evaluationRequestTime"];
      string request_id = 5 [json_name = "requestId"];
      CustomerDetails customer_details = 6 [json_name = "customerDetails"];
      string product = 7 [json_name = "product"];
      PolicyParams policy_params = 8 [json_name = "policyParams"];
      string bureau = 9 [json_name = "bureau"];
      string client_id = 10 [json_name = "clientId"];
    }
  }
}

message OfferDetails {
  string emiDueDate = 1 [json_name = "emiDueDate"];
  double gstPercentage = 2 [json_name = "gstPercentage"];
  double interestPercentage = 3 [json_name = "interestPercentage"];
  double maxAmount = 4 [json_name = "maxAmount"];
  double maxEmiAmount = 5 [json_name = "maxEmiAmount"];
  int32 maxTenureInMonths = 6 [json_name = "maxTenureInMonths"];
  double minAmount = 7 [json_name = "minAmount"];
  int32 minTenureInMonths = 8 [json_name = "minTenureInMonths"];
  double processingFeePercentage = 9 [json_name = "processingFeePercentage"];
  string pricingScheme = 10 [json_name = "pricingScheme"];
}

message Decision {
  string lending_program = 1 [json_name = "lendingProgram"];
  string action = 2 [json_name = "action"];
  string valid_till = 3 [json_name = "validTill"];
  string bureau = 4 [json_name = "bureau"];
  repeated string conditions = 5 [json_name = "conditions"];
  repeated string rejection_reasons = 6 [json_name = "rejectionReasons"];
  repeated string external_reasons = 7 [json_name = "externalReasons"];
  string report_date = 8 [json_name = "reportDate"];
  string report_id = 9 [json_name = "reportId"];
  string scheme_id = 10 [json_name = "schemeId"];
  string strategy = 11 [json_name = "strategy"];
  OfferDetails offer_details = 12 [json_name = "offerDetails"];
}

message GetLoanDecisioningResponseV2 {
  string customer_id = 1 [json_name = "customerId"];
  string application_id = 2 [json_name = "applicationId"];
  string evaluation_request_time = 3 [json_name = "evaluationRequestTime"];
  string request_id = 4 [json_name = "requestId"];
  string product = 5 [json_name = "product"];
  PolicyParams policy_params = 6 [json_name = "policyParams"];
  Decision prioritized_decision = 7 [json_name = "prioritizedDecision"];
  repeated Decision decision = 8 [json_name = "decision"];
  google.protobuf.Struct details = 10 [json_name = "details"];
}

message Duration {
  uint32 duration = 1 [json_name = "duration"];
  string duration_unit = 2 [json_name = "durationUnit"];
}
