//go:generate gen_sql -types=PDFTemplate,Ownership,DocType,DocumentFieldMask
syntax = "proto3";

package stockguardian.sgdocs;

option go_package = "github.com/epifi/gringott/api/stockguardian/sgdocs";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgdocs";

enum PDFTemplate {
  PDF_TEMPLATE_UNSPECIFIED = 0;
  PDF_TEMPLATE_SG_LOAN_AGREEMENT = 1;
  // test is a small template and only needs a single value to fill unlike other templates
  // it is easier to add UTs for the same
  PDF_TEMPLATE_TEST = 2;
  PDF_TEMPLATE_SG_EARLY_SALARY_LOAN_AGREEMENT = 3;
}

enum Ownership {
  OWNERSHIP_UNSPECIFIED = 0;
  OWNERSHIP_LMS = 1;
  OWNERSHIP_ESIGN = 2;
  OWNERSHIP_SGAPPLICATION = 3;
  OWNERSHIP_MATRIX = 4;
}

enum DocType {
  DOC_TYPE_UNSPECIFIED = 0;
  DOC_TYPE_KFS = 1;
  DOC_TYPE_NOC = 2;
  DOC_TYPE_PRE_SIGNED_KFS = 3;
  DOC_TYPE_CONSENT = 4;
  DOC_TYPE_AADHAAR = 5;
  DOC_TYPE_USER_SELFIE = 6;
  DOC_TYPE_AADHAAR_PHOTO = 7;
}

enum DocumentFieldMask {
  DOCUMENT_FIELD_MASK_UNSPECIFIED = 0;
  DOCUMENT_FIELD_MASK_ID = 1;
  DOCUMENT_FIELD_MASK_RAW = 2;
  DOCUMENT_FIELD_MASK_REQUEST_ID = 3;
  DOCUMENT_FIELD_MASK_CUSTOMER_ID = 4;
  DOCUMENT_FIELD_MASK_OWNERSHIP = 5;
  DOCUMENT_FIELD_MASK_CREATED_AT = 6;
  DOCUMENT_FIELD_MASK_UPDATED_AT = 7;
  DOCUMENT_FIELD_MASK_DELETED_AT = 8;
}

// this enum defines the signing entity identifier for the document or other data signing by RE use-case
enum SigningEntity {
  SIGNING_ENTITY_UNSPECIFIED = 0;
  SIGNING_ENTITY_STOCK_GUARDIAN = 1;
}

// this enum defines the purpose of the document or other data signing by RE
enum SigningPurpose {
  SIGNING_PURPOSE_UNSPECIFIED = 0;
  SIGNING_PURPOSE_COMBINED_LOAN_AGREEMENT = 1;
}
