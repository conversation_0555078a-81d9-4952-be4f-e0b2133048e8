syntax = "proto3";

package stockguardian.sgdocs;

import "api/stockguardian/sgdocs/enums.proto";
import "api/rpc/status.proto";


option go_package = "github.com/epifi/gringott/api/stockguardian/sgdocs";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgdocs";

service Docs {
  // Generates the document for the given template and data.
  rpc GenerateDocument(GenerateDocumentRequest) returns (GenerateDocumentResponse);
  // Fetches the document using one of its unique identifiers.
  rpc GetDocument(GetDocumentRequest) returns (GetDocumentResponse);
  // Deletes the document using one of its unique identifiers.
  rpc DeleteDocument(DeleteDocumentRequest) returns (DeleteDocumentResponse);
  // Saves the document with the given template and data.
  rpc SaveDocument(SaveDocumentRequest) returns (SaveDocumentResponse);
  // Signs the document with SG and Saves it.
  rpc SignAndSaveDocument(SignAndSaveDocumentRequest) returns (SignAndSaveDocumentResponse);
}

message GenerateDocumentRequest {
  PDFTemplate pdf_template = 1;
  bytes data = 2;
  // eg; LMS, Esign etc
  Ownership ownership = 3;
  // eg: NOC,KFS etc.
  DocType doc_type = 4;
  // Identifier with which owner service will fetch the document.
  string request_id = 5;
  string customer_id = 6;
  // signing data for the document: optional
  oneof signing_data {
    DscBasedSigningData dsc_based_signing_data = 7;
  }
}

message GenerateDocumentResponse {
  rpc.Status status = 1;
  bytes document_bytes = 2;
}

message GetDocumentRequest{
  oneof request_type{
    string request_id = 1;
  }
  Ownership ownership = 2;
}

message GetDocumentResponse{
  rpc.Status status = 1;
  bytes document_bytes = 2;
}

message DeleteDocumentRequest{
  oneof request_type{
    string request_id = 1;
  }
  Ownership ownership = 2;
}

message DeleteDocumentResponse{
  rpc.Status status = 1;
}

// this defines the field required for signing the document by RE using DSC
message DscBasedSigningData {
  // Signing entity identifier for signing the document
  SigningEntity signing_entity = 1;
  // Signing purpose for signing the document
  SigningPurpose purpose = 2;
  // Signing entity legal name that will go as part of signature
  string signing_entity_legal_name = 3;
  // Signing entity location that will go as part of signature visible on document/data
  // ex: "Location: Mumbai"
  string signing_entity_location = 4;
  // signature will be visible in a rectangle on the pdf page signingBoxWidth, signingBoxHeight are width and height
  // of the rectangle
  int32 sign_box_height = 5;
  int32 sign_box_width = 6;
  // signature will be visible in a rectangle on the pdf page signingPosX, signingPosY are the x,y coordinates of the
  // rectangle with respect to bottom left corner of the page
  int32 sign_pos_x = 7;
  int32 sign_pos_y = 8;
}

message SaveDocumentRequest {
  bytes document_bytes = 1;
  Ownership ownership = 2;
  DocType doc_type = 3;
  string request_id = 4;
  string customer_id = 5;
  string document_extension = 6;
}

message SaveDocumentResponse {
  rpc.Status status = 1;
  string document_id = 2;
}

message SignAndSaveDocumentRequest {
  bytes document_bytes = 1;
  Ownership ownership = 2;
  DocType doc_type = 3;
  string request_id = 4;
  string customer_id = 5;
  string document_extension = 6;
  oneof signing_data {
    DscBasedSigningData dsc_based_signing_data = 7;
  }
}

message SignAndSaveDocumentResponse {
  rpc.Status status = 1;
  string document_id = 2;
}
