//go:generate gen_sql -types=Bureau,CreditReportInquiryType,CreditReportStatus,CreditReportSubStatus
syntax = "proto3";

package stockguardian.creditreport;

option go_package = "github.com/epifi/gringott/api/stockguardian/creditreport";
option java_package = "com.github.epifi.gringott.api.stockguardian.creditreport";

enum Bureau{
    BUREAU_UNSPECIFIED = 0;
    BUREAU_EXPERIAN = 1;
    BUREAU_CIBIL = 2;
}

enum CreditReportInquiryType{
    CREDIT_REPORT_INQUIRY_TYPE_UNSPECIFIED = 0;
    CREDIT_REPORT_INQUIRY_TYPE_SOFT_PULL = 1;
    CREDIT_REPORT_INQUIRY_TYPE_HARD_PULL = 2;
}

enum CreditReportStatus{
    CREDIT_REPORT_STATUS_UNSPECIFIED = 0;
    CREDIT_REPORT_STATUS_CREATED = 1;
    CREDIT_REPORT_STATUS_SUCCESS = 2;
    CREDIT_REPORT_STATUS_FAILED = 3;
}

enum CreditReportSubStatus{
  CREDIT_REPORT_SUB_STATUS_UNSPECIFIED = 0;
  CREDIT_REPORT_SUB_STATUS_REPORT_NOT_FOUND = 1;
}

enum CreditReportFieldMask {
    CREDIT_REPORT_FIELD_MASK_UNSPECIFIED = 0;
    CREDIT_REPORT_FIELD_MASK_ID = 1;
    CREDIT_REPORT_FIELD_MASK_CUSTOMER_ID = 2;
    CREDIT_REPORT_FIELD_MASK_CLIENT_ID = 3;
    CREDIT_REPORT_FIELD_MASK_BUREAU = 4;
    CREDIT_REPORT_FIELD_MASK_BUREAU_REQUEST_ID = 5;
    CREDIT_REPORT_FIELD_MASK_INQUIRY_TYPE = 6;
    CREDIT_REPORT_FIELD_MASK_RAW_REPORT = 7;
    CREDIT_REPORT_FIELD_MASK_CONSENT_VALID_TILL = 8;
    CREDIT_REPORT_FIELD_MASK_EXPIRED_AT = 9;
    CREDIT_REPORT_FIELD_MASK_CREATED_AT = 10;
    CREDIT_REPORT_FIELD_MASK_UPDATED_AT = 11;
    CREDIT_REPORT_FIELD_MASK_DELETED_AT = 12;
    CREDIT_REPORT_FIELD_MASK_STATUS = 13;
    CREDIT_REPORT_FIELD_MASK_SUB_STATUS = 14;
}
