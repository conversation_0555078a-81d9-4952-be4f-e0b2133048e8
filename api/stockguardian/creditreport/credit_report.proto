// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=CreditReport
syntax = "proto3";

package stockguardian.creditreport;

import "api/stockguardian/creditreport/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/creditreport";
option java_package = "com.github.epifi.gringott.api.stockguardian.creditreport";

message CreditReport {
    string id = 1;
    string customer_id = 2;
    string client_id = 3;
    Bureau bureau = 4;
    string bureau_request_id = 5;
    CreditReportInquiryType inquiry_type = 6;
    // byte array of report as sent by vendor
    bytes raw_report = 7;
    google.protobuf.Timestamp consent_valid_till = 8;
    google.protobuf.Timestamp expired_at = 9;
    google.protobuf.Timestamp created_at = 10;
    google.protobuf.Timestamp updated_at = 11;
    google.protobuf.Timestamp deleted_at = 12;
    CreditReportStatus status = 13;
    CreditReportSubStatus sub_status = 14;
}


