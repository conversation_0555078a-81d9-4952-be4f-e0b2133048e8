syntax = "proto3";

package stockguardian.creditreport;

import "api/rpc/status.proto";
import "api/stockguardian/creditreport/enums.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "api/typesv2/common/bureau.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/creditreport";
option java_package = "com.github.epifi.gringott.api.stockguardian.creditreport";

service CreditReportService {
  // GetCreditReport rpc will fetch the credit report provided any of the identifier. The rpc will make a db call
  // to fetch the credit report if present.
  rpc GetCreditReport (GetCreditReportRequest) returns (GetCreditReportResponse);
  // PerformHardPull takes in customer id as request param and hard pulls the credit report either from db or bureau
  // keeping in mind previous hard pull request was not made within some given time frame
  rpc PerformHardPull (PerformHardPullRequest) returns (PerformHardPullResponse);
  // PurgeCreditReport takes in timestamp as parameter and purges credit reports older than that timestamp.
  rpc PurgeCreditReport (PurgeCreditReportRequest) returns (PurgeCreditReportResponse);
}

message GetCreditReportRequest {
  oneof identifiers {
    string report_id = 1;
    string customer_id = 2;
  }
}

message GetCreditReportResponse {
  rpc.Status status = 1;
  bytes credit_report = 2;
  // the timestamp when last credit report was was hardpulled.
  google.protobuf.Timestamp hardpull_time = 3;
  CreditReportStatus credit_report_status = 4;
  CreditReportSubStatus credit_report_sub_status = 5;
}

message PerformHardPullRequest {
  string customer_id = 1;
  // we need this id to fetch details like loan amount and kyc details.
  string application_id = 2;
  google.type.Money loan_amount = 4;
  api.typesv2.common.Bureau bureau = 3;
}

message PerformHardPullResponse {
  rpc.Status status = 1;
  bytes credit_report = 2;
  // time when we got the response from bureau
  google.protobuf.Timestamp hardpull_time = 3;
  CreditReportStatus credit_report_status = 4;
  CreditReportSubStatus credit_report_sub_status = 5;
}

message PurgeCreditReportRequest {
  google.protobuf.Timestamp purge_timestamp = 1;
}

message PurgeCreditReportResponse {
  rpc.Status status = 1;
}
