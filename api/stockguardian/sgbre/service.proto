syntax = "proto3";

package stockguardian.sgbre;

import "api/rpc/status.proto";
import "api/stockguardian/vendors/inhouse/bre.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/employment_type.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "api/typesv2/common/bureau.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgbre";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgbre";

service BreService {
  // RPC to check user's loan eligibility using the BRE tool
  // need to send all the raw data and hit http server of iris/scienaptic
  // returns with if user is ineligible, or eligible with loan offer details
  rpc GetLoanDecisioning (GetLoanDecisioningRequest) returns (GetLoanDecisioningResponse);

  // RPC to check user's loan eligibility using the BRE tool with V2 request
  rpc GetLoanDecisioningV2 (GetLoanDecisioningRequestV2) returns (GetLoanDecisioningResponseV2);
}

message GetLoanDecisioningRequest {
  // customer for whom to fetch loan decisioning
  string customer_id = 1;
  string client_id = 2;
  string scheme_id = 3;
  string batch_id = 4;
  vendors.inhouse.bre.PolicyParams policy_params = 5;
  PersonalDetails personal_details = 6;
  EmploymentDetails employment_details = 7;
  string application_id = 8;
  // amount selected bu user against which we want to make the request
  google.type.Money loan_amount = 9;
  // entity id can be any id which is being passed from the entity - currently using for LOEC id
  string entity_id = 10;
  message PersonalDetails {
    api.typesv2.common.Name name = 1;
    google.type.Date dob = 2;
    api.typesv2.common.Gender gender = 3;
    string pan = 4;
    api.typesv2.common.PostalAddress address = 5;
  }
  message EmploymentDetails {
    api.typesv2.common.EmploymentType employment_type = 1;
    google.type.Money monthly_income = 2;
    string employer_name = 3;
    string work_email = 4;
  }
}

message GetLoanDecisioningResponse {
  rpc.Status status = 1;
  Decision loan_decision = 2;
  bytes raw_bre_response = 3;
  OfferDetails offer_details = 4;
  string scheme_id = 5;
  string batch_id = 6;
  string cust_id = 7;
  string loan_program = 8;
  repeated string external_reasons = 9;
  vendors.inhouse.bre.PolicyParams policy_params = 10;
  message OfferDetails {
    google.type.Money min_amount = 1;
    google.type.Money max_amount = 2;
    google.type.Money max_emi_amount = 3;
    double interest_percentage = 4;
    double processing_fee_percentage = 5;
    double gst_percentage = 6;
    int32 min_tenure_in_months = 7;
    int32 max_tenure_in_months = 8;
    google.type.Date emi_due_date = 9;
    google.protobuf.Timestamp valid_till = 10;
  }
}

enum Decision {
  DECISION_UNSPECIFIED = 0;
  DECISION_APPROVED = 1;
  DECISION_REJECTED = 2;
}

message GetLoanDecisioningRequestV2 {
  // customer for whom to fetch loan decisioning
  string customer_id = 1;
  string application_id = 2;
  google.type.Money loan_amount = 3;
  google.protobuf.Timestamp evaluation_request_time = 4;
  string request_id = 5;
  CustomerDetails customer_details = 6;
  vendors.inhouse.bre.PolicyParams policy_params = 7;
  api.typesv2.common.Bureau bureau = 8;
  string client_id = 9;
  string product = 10;
}

message CustomerDetails {
  PersonalDetails personal_details = 1;
  EmploymentDetails employment_details = 2;
  api.typesv2.common.PostalAddress residential_address = 3;
  api.typesv2.common.PostalAddress permanent_address = 4;
  RequestedLoanDetails requested_loan_details = 5;
  api.typesv2.common.PostalAddress current_location_address = 6;

  message RequestedLoanDetails {
    google.type.Money desired_loan_amount = 1;
    api.typesv2.common.Duration tenure = 3;
    string roi = 4;
    google.type.Money pf_plus_gst = 5;
    google.type.Money upfront_payment_amount = 6;
  }
}

message PersonalDetails {
  google.type.Date dob = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.common.Gender gender = 3;
  string pan = 4;
}

message EmploymentDetails {
  api.typesv2.common.EmploymentType employment_type = 1;
  google.type.Money monthly_income = 2;
  string employer_name = 3;
  string work_email = 4;
  api.typesv2.common.PostalAddress work_address = 5;
}

message OfferDetails {
  google.type.Money max_amount = 1;
  google.type.Money min_amount = 2;
  google.type.Money max_emi_amount = 3;
  int32 max_tenure_in_months = 4;
  int32 min_tenure_in_months = 5;
  double interest_percentage = 6;
  double processing_fee_percentage = 7;
  double gst_percentage = 8;
  google.type.Date emi_due_date = 9;
  google.protobuf.Timestamp valid_till = 10;
}

message FinalBreDecision {
  string lending_program = 1;
  Decision decision = 2;
  OfferDetails offer_details = 3;
  google.protobuf.Timestamp valid_till = 4;
}

message GetLoanDecisioningResponseV2 {
  rpc.Status status = 1;
  string customer_id = 2;
  string application_id = 3;
  google.protobuf.Timestamp evaluation_request_time = 4;
  string request_id = 5;
  vendors.inhouse.bre.PolicyParams policy_params = 6;
  FinalBreDecision prioritized_decision = 7;
  repeated FinalBreDecision final_decision = 8;
  bytes raw_loan_decision_response = 9;
}
