syntax = "proto3";

package stockguardian.creditrisk;

import "api/rpc/status.proto";
import "api/stockguardian/creditrisk/enums.proto";
import "api/stockguardian/vendors/inhouse/bre.proto";
import "api/typesv2/common/address.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "api/typesv2/common/duration.proto";
import "api/typesv2/common/bureau.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/creditrisk";
option java_package = "com.github.epifi.gringott.api.stockguardian.creditrisk";

service CreditRiskService {
  // rpc to initiate loan offer eligibility check for any customer
  rpc CheckLoanEligibility (CheckLoanEligibilityRequest) returns (CheckLoanEligibilityResponse);
  // rpc to check status of the loan offer eligibility checks for any customer request
  rpc CheckLoanEligibilityStatus (CheckLoanEligibilityStatusRequest) returns (CheckLoanEligibilityStatusResponse);
  // rpc to get loan offer if present
  rpc GetLoanOffer (GetLoanOfferRequest) returns (GetLoanOfferResponse);
  // rpc to lock loan offer if and when customer starts application
  rpc LockOffer (LockOfferRequest) returns (LockOfferResponse);
  // rpc to unlock loan offer if and when customer's application reaches terminal stage
  rpc UnlockOffer (UnlockOfferRequest) returns (UnlockOfferResponse);
  // This rpc will unlock the offer (if not already) and then mark the loan offer deactivated
  rpc UnlockAndDeactivateLoanOffer (UnlockAndDeactivateLoanOfferRequest) returns (UnlockAndDeactivateLoanOfferResponse);
}

message CheckLoanEligibilityRequest {
  string client_id = 1;
  string customer_id = 2;
  string request_id = 3;
}

message CheckLoanEligibilityResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    // This status denotes that the loan offer is not expired or had not been deactivated.
    LOAN_OFFER_AVAILABLE = 100;
  }
  rpc.Status status = 1;
  string request_id = 2;
  // If loan offer already exists then loan offer id will be returned
  string loan_offer_id = 3;
}

message CheckLoanEligibilityStatusRequest {
  string client_id = 1;
  string request_id = 2;
  string application_id = 3;
  // the amount against which  user wants the loan.
  google.type.Money loan_amount = 4;
  vendors.inhouse.bre.PolicyParams policy_params = 5;
  RequestedLoanDetails requested_loan_details = 6;
  string kyc_application_id = 7 [deprecated = true];
  api.typesv2.common.PostalAddress kyc_address = 8;
  api.typesv2.common.Bureau bureau = 9;
  string product_id = 10;

  message RequestedLoanDetails {
    google.type.Money desired_loan_amount = 1;
    api.typesv2.common.Duration tenure = 3;
    string roi = 4;
    google.type.Money pf_plus_gst = 5;
    google.type.Money upfront_payment_amount = 6;
  }
}

message CheckLoanEligibilityStatusResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    OFFER_IS_INACTIVE = 100;
  }
  rpc.Status status = 1;
  Eligibility eligibility = 2;
  LoanOfferDetails loan_offer_details = 3;
  enum Eligibility {
    UNSPECIFIED = 0;
    ELIGIBLE = 1;
    REJECTED = 2;
    IN_PROGRESS = 3;
  }
}

message LoanOfferDetails {
  // loan offer external id
  string id = 1;
  google.type.Money min_amount = 2;
  google.type.Money max_amount = 3;
  google.type.Money max_emi_amount = 4;
  double interest_percentage = 5;
  double processing_fee_percentage = 6;
  double gst_percentage = 7;
  int32 min_tenure_in_months = 8;
  int32 max_tenure_in_months = 9;
  google.protobuf.Timestamp valid_till = 10;
  LoanOfferStatus status = 11;
}

message GetLoanOfferRequest {
  string client_id = 1;
  oneof identifier {
    string id = 2;
  }
}

message GetLoanOfferResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    OFFER_IS_INACTIVE = 100;
  }
  rpc.Status status = 1;
  LoanOfferDetails loan_offer_details = 2;
}

message LockOfferRequest {
  string client_id = 1;
  string offer_id = 2;
  // client_request_id is used to identify the request that locked the offer
  // offer can be unlocked using the same client_request_id
  string client_request_id = 3;
}

message LockOfferResponse {
  enum Status {
    OK = 0;
    RECORD_NOT_FOUND = 5;
    INTERNAL = 13;
    OFFER_LOCKED = 100;
    OFFER_LOCKED_BY_ANOTHER_REQUEST = 101;
  }
  rpc.Status status = 1;
}

message UnlockOfferRequest {
  string client_id = 1;
  string offer_id = 2;
  // client_request_id is used to identify the request that locked the offer
  // same client_request_id which is used to lock offer should be provided here
  string client_request_id = 3;
}

message UnlockOfferResponse {
  enum Status {
    OK = 0;
    RECORD_NOT_FOUND = 5;
    INTERNAL = 13;
    OFFER_LOCKED_BY_ANOTHER_REQUEST = 100;
  }
  rpc.Status status = 1;
}

message UnlockAndDeactivateLoanOfferRequest {
  string client_id = 1;
  string offer_id = 2;
  // This id will be required to unlock the offer if it is locked. If the offer is locked with the same id then it will be unlocked.
  string client_request_id = 3;
}

message UnlockAndDeactivateLoanOfferResponse {
  enum Status {
    OK = 0;
    RECORD_NOT_FOUND = 5;
    INTERNAL = 13;
    OFFER_LOCKED_BY_ANOTHER_REQUEST = 100;
  }
  rpc.Status status = 1;
}
