//go:generate gen_sql -types=LoanApplicationStatus,LoanApplicationSubStatus,LoanApplicationStageName,LoanApplicationStageStatus,LoanApplicationStageSubStatus
syntax = "proto3";

package stockguardian.sgapplication.enums;

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapplication/enums";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgapplication.enums";

enum LoanApplicationStatus {
  LOAN_APPLICATION_STATUS_UNSPECIFIED = 0;
  LOAN_APPLICATION_STATUS_CREATED = 1;
  LOAN_APPLICATION_STATUS_IN_PROGRESS = 2;
  LOAN_APPLICATION_STATUS_MANUAL_INTERVENTION = 3;
  LOAN_APPLICATION_STATUS_SUCCESS = 4;
  LOAN_APPLICATION_STATUS_FAILED = 5;
  LOAN_APPLICATION_STATUS_CANCELLED = 6;
}

enum LoanApplicationSubStatus {
  LOAN_APPLICATION_SUB_STATUS_UNSPECIFIED = 0;
}

enum LoanApplicationStageName {
  LOAN_APPLICATION_STAGE_NAME_UNSPECIFIED = 0;
  LOAN_APPLICATION_STAGE_NAME_KYC = 1;
  LOAN_APPLICATION_STAGE_NAME_OFFER_GENERATION = 2;
  LOAN_APPLICATION_STAGE_NAME_DRAWDOWN = 3;
  LOAN_APPLICATION_STAGE_NAME_MANDATE = 4;
  LOAN_APPLICATION_STAGE_NAME_ESIGN = 5;
  LOAN_APPLICATION_STAGE_NAME_DISBURSEMENT = 6;
  LOAN_APPLICATION_STAGE_NAME_PENNY_DROP = 7;
}

enum LoanApplicationStageStatus {
  LOAN_APPLICATION_STAGE_STATUS_UNSPECIFIED = 0;
  LOAN_APPLICATION_STAGE_STATUS_CREATED = 1;
  LOAN_APPLICATION_STAGE_STATUS_IN_PROGRESS = 2;
  LOAN_APPLICATION_STAGE_STATUS_SUCCESS = 3;
  LOAN_APPLICATION_STAGE_STATUS_FAILED = 4;
  LOAN_APPLICATION_STAGE_STATUS_MANUAL_INTERVENTION = 5;
  LOAN_APPLICATION_STAGE_STATUS_EXPIRED = 6;
  LOAN_APPLICATION_STAGE_STATUS_CANCELLED = 7;
}

enum LoanApplicationStageSubStatus {
  LOAN_APPLICATION_STAGE_SUB_STATUS_UNSPECIFIED = 0;
  LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_REJECTED = 1;
  LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_EXPIRED = 2;
  LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_LOCKED_BY_ANOTHER_APPLICATION = 3;
  LOAN_APPLICATION_STAGE_SUB_STATUS_DRAWDOWN_VALIDATION_FAILED = 4;
  LOAN_APPLICATION_STAGE_SUB_STATUS_KYC_ALREADY_COMPLETED = 5;
  LOAN_APPLICATION_STAGE_SUB_STATUS_OFFER_INACTIVE = 6;
  LOAN_APPLICATION_STAGE_SUB_STATUS_CKYC_GENDER_INVALID = 7;
  LOAN_APPLICATION_STAGE_SUB_STATUS_DISBURSAL_MANUAL_RETRY = 8;
  LOAN_APPLICATION_STAGE_SUB_STATUS_KYC_EXISTS_FOR_OTHER_LSP = 9;
}

enum LoanApplicationFieldMask {
  LOAN_APPLICATION_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_APPLICATION_FIELD_MASK_EXTERNAL_ID = 1;
  LOAN_APPLICATION_FIELD_MASK_DETAILS = 2;
  LOAN_APPLICATION_FIELD_MASK_STATUS = 3;
  LOAN_APPLICATION_FIELD_MASK_SUB_STATUS = 4;
}

enum LoanApplicationStageFieldMask {
  LOAN_APPLICATION_STAGE_FIELD_MASK_UNSPECIFIED = 0;
  LOAN_APPLICATION_STAGE_FIELD_MASK_EXTERNAL_ID = 1;
  LOAN_APPLICATION_STAGE_FIELD_MASK_DETAILS = 2;
  LOAN_APPLICATION_STAGE_FIELD_MASK_STATUS = 3;
  LOAN_APPLICATION_STAGE_FIELD_MASK_SUB_STATUS = 4;
  LOAN_APPLICATION_STAGE_FIELD_MASK_COMPLETED_AT = 5;
}

enum ConsentType {
  CONSENT_TYPE_UNSPECIFIED = 0;
  CONSENT_TYPE_CKYC_DOWNLOAD = 1;
  CONSENT_TYPE_CREDIT_REPORT_HARD_PULL = 2;
  CONSENT_TYPE_DATA_USAGE_FOR_PROMOTIONS = 3;
}

enum UpdateUserDetailsFieldMask {
  UPDATE_USER_DETAILS_FIELD_MASK_UNSPECIFIED = 0;
  UPDATE_USER_DETAILS_FIELD_MASK_ADDRESS = 1;
  UPDATE_USER_DETAILS_FIELD_MASK_EMPLOYMENT_DETAILS = 2;
  UPDATE_USER_DETAILS_FIELD_MASK_BANK_ACCOUNT_DETAILS = 3;
  UPDATE_USER_DETAILS_FIELD_MASK_PERSONAL_DETAILS = 4;
  UPDATE_USER_DETAILS_FIELD_MASK_LOCATION_DETAILS = 5;
}

enum SubmitApplicationFieldMask {
  SUBMIT_APPLICATION_FIELD_MASK_UNSPECIFIED = 0;
  SUBMIT_APPLICATION_FIELD_MASK_USER_DETAILS = 1;
  SUBMIT_APPLICATION_FIELD_MASK_KYC_DETAILS = 2;
  SUBMIT_APPLICATION_FIELD_MASK_MANDATE_DETAILS = 3;
  SUBMIT_APPLICATION_FIELD_MASK_ESIGN_DETAILS = 4;
  SUBMIT_APPLICATION_FIELD_MASK_LOAN_REQUIREMENT_DETAILS = 5;
  SUBMIT_APPLICATION_FIELD_MASK_BRE_PARAMS = 6;
  SUBMIT_APPLICATION_FIELD_MASK_PRODUCT_SPECIFIC_BRE_PARAMS = 7;
  SUBMIT_APPLICATION_FIELD_MASK_CONSENT_DETAILS = 8;
  SUBMIT_APPLICATION_FIELD_MASK_VERIFICATION_DETAILS = 9;
  SUBMIT_APPLICATION_FIELD_MASK_DOWN_PAYMENT_DETAILS = 10;
  SUBMIT_APPLICATION_FIELD_MASK_SERVICE_PROVIDER_DETAILS = 11;
}

enum KycOrchestrationFlow {
  KYC_ORCHESTRATION_FLOW_UNSPECIFIED = 0;
  KYC_ORCHESTRATION_FLOW_LENDING_FULL_KYC = 1;
  KYC_ORCHESTRATION_FLOW_OFFLINE_KYC = 2;
}
