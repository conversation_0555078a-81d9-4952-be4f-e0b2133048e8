syntax = "proto3";

package stockguardian.sgapplication;

import "api/rpc/status.proto";
import "api/stockguardian/creditrisk/service.proto";
import "api/stockguardian/sgapplication/enums/enums.proto";
import "api/stockguardian/sgapplication/internal/loan_application.proto";
import "api/stockguardian/vendors/inhouse/bre.proto";
import "api/typesv2/common/bank_account_details.proto";
import "api/typesv2/common/device.proto";
import "google/type/money.proto";
import "api/stockguardian/sgapplication/internal/loan_application_stage.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapplication";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgapplication";

service Application {
  // Creates Loan application entry using the personal details of user and returns the application id of the created applicant.
  rpc StartApplication (StartApplicationRequest) returns (StartApplicationResponse);
  // Triggers the orchestrator to submit the loan application using the provided data.
  rpc SubmitApplication (SubmitApplicationRequest) returns (SubmitApplicationResponse);
  // Cancels the loan application based on the application id given cancellation is valid.
  rpc CancelApplication (CancelApplicationRequest) returns (CancelApplicationResponse);
  // Fetches the status of each stage for a given application.
  rpc GetApplicationStatus (GetApplicationStatusRequest) returns (GetApplicationStatusResponse);
  // Updates the user selected details in loan application if offer is not locked yet.
  rpc UpdateApplicationDetails (UpdateApplicationDetailsRequest) returns (UpdateApplicationDetailsResponse);
  // UpdateAddress updates the address details in loan application.
  rpc UpdateUserDetails (UpdateUserDetailsRequest) returns (UpdateUserDetailsResponse);
  // RecordConsent records various types of consent which are need in the application management.
  rpc RecordConsent (RecordConsentRequest) returns (RecordConsentResponse);
  // Fetches the loan offer for the given application id.
  rpc GetLoanOffer (GetLoanOfferRequest) returns (GetLoanOfferResponse);
  // Initiates the kyc for the given application id.
  rpc InitiateKyc (InitiateKycRequest) returns (InitiateKycResponse);
  // Initiates Drawdown for the given application id.
  rpc InitDrawDown (InitDrawDownRequest) returns (InitDrawDownResponse);
  // InitPennyDrop will start the penny drop flow for the user for the provided bank account details.
  rpc InitPennyDrop (InitPennyDropRequest) returns (InitPennDropResponse);
  // Initiates Esign for the given application id.
  rpc InitEsign (InitEsignRequest) returns (InitEsignResponse);
  // InitiateMandate rpc will be used for initiating the mandate for the loan amount with the bank account on which penny drop was already done.
  rpc InitMandate (InitMandateRequest) returns (InitMandateResponse);
  // InitiateDisbursement rpc will be used for initiating the disbursement of the loan amount to the bank account on which penny drop was already done.
  // It will validate all the requirements before initiating the disbursement.
  // The successful response from this api means the disbursement is initiated. And the status of the disbursement can be fetched using GetApplicationStatus rpc.
  rpc InitDisbursement (InitDisbursementRequest) returns (InitDisbursementResponse);
  //this rpc is used to retry loan disbursal for stockguardian which have not reached the stage of success or failure, it internally calls InitDisbursement rpc after doing some mandatory checks
  rpc InitDisbusralRetry (InitDisbursalRetryRequest) returns (InitDisbursalRetryResponse);
  //this rpc is used to get the kyc application id which is also the custAppId for custApp of onboarding in gringott
  rpc GetKycApplicationId(GetKycApplicationIdRequest) returns (GetKycApplicationIdResponse) {
    option deprecated = true;
  }
  // this rpc is used to get the loan applications for a given applicant id, client id and product id
  rpc GetLoanApplications(GetLoanApplicationsRequest) returns (GetLoanApplicationsResponse);
  // GetLoanApplicationStage fetches details of a specific loan application stage for a given application and stage name.
  rpc GetLoanApplicationStage (GetLoanApplicationStageRequest) returns (GetLoanApplicationStageResponse);
  // GetLatestLoanApplicationStage fetches the latest loan application stage and its status for a given application.
  rpc GetLatestLoanApplicationStage (GetLatestLoanApplicationStageRequest) returns (GetLatestLoanApplicationStageResponse);
}

message GetLoanApplicationsRequest {
  string applicant_id = 1;
  string client_id = 2;
  string product_id = 3;
  string loan_application_id = 4;
}

message GetLoanApplicationsResponse {
  rpc.Status status = 1;
  repeated LoanApplication loan_applications = 2;
}

message GetKycApplicationIdRequest {
  option deprecated = true;
  string loan_application_id = 1;
}

message GetKycApplicationIdResponse {
  option deprecated = true;
  rpc.Status status = 1;
  string kyc_application_id = 2;
}

message InitDisbursalRetryRequest {
  repeated string loan_application_ids = 1;
}

message InitDisbursalRetryResponse {
  rpc.Status status = 1;
  repeated string success_loan_application_ids = 2;
  repeated string failure_loan_application_ids = 3;
  repeated string invalid_loan_application_ids = 4;
}

message StartApplicationRequest {
  string client_id = 1;
  string product_id = 2;
  string applicant_id = 3;
}

message StartApplicationResponse {
  enum Status {
    OK = 0;
    // If application already exist
    ALREADY_EXISTS = 6;
    // internal error while processing the request
    INTERNAL = 13;
    FAILED_PRECONDITION_APPLICANT_DOES_NOT_EXIST = 100;
  }
  rpc.Status status = 1;
  string application_id = 2;
}

message SubmitApplicationRequest {
  string application_id = 1;
  repeated enums.SubmitApplicationFieldMask field_masks = 2;
  PersonalDetails personal_details = 3;
  AddressDetails address_details = 4;
  EmploymentDetails employment_details = 5;
  LocationDetails location_details = 6;
  ConsentDetails consent_details = 7;
  KycDetails kyc_details = 8;
  MandateStageDetails recurring_payment_details = 9;
  EsignStageDetails esign_document_details = 10;
  OfferGenerationStageDetails loan_offer_details = 11;
  DownPaymentDetails down_payment_details = 12;
  ServiceProviderDetails service_provider_details = 13;
  VerificationDetails verification_details = 14;
}

message SubmitApplicationResponse {
  rpc.Status status = 1;
}

message CancelApplicationRequest {
  string application_id = 1;
}

message CancelApplicationResponse {
  rpc.Status status = 1;
}

message GetApplicationStatusRequest {
  string application_id = 1;
}

message GetApplicationStatusResponse {
  rpc.Status status = 1;
  repeated StageStatus stage_statuses = 2;
  enums.LoanApplicationStatus loan_application_status = 3;
}

message StageStatus {
  enums.LoanApplicationStageName stage_name = 1;
  enums.LoanApplicationStageStatus status = 2;
}

message GetLoanOfferRequest {
  string application_id = 1;
  // open market user is someone who was not initially evaluated offline by LSP and all evaluation is happening real time.
  bool is_open_market_user = 2;
  // this is metadata sent by LSP to be sent to BRE for evaluation of offer generation
  BreParams bre_params = 3;

  message BreParams {
    // policy params used to generate offer from BRE
    vendors.inhouse.bre.PolicyParams policy_params = 1;

    google.type.Money desired_loan_amount = 2;
  }
}

message GetLoanOfferResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    OFFER_GENERATION_IN_PROGRESS = 102;
    OFFER_REJECTED = 103;
    OFFER_EXPIRED = 104;
    OFFER_LOCKED_BY_ANOTHER_APPLICATION = 105;
  }
  rpc.Status status = 1;
  creditrisk.LoanOfferDetails details = 2;
}

message InitiateKycRequest {
  string application_id = 1;
}

message InitiateKycResponse {
  enum Status {
    OK = 0;
    // internal error while processing the request
    INTERNAL = 13;
    KYC_ALREADY_COMPLETED = 100;
  }
  rpc.Status status = 1;
  string applicant_id = 2;
  // External id for KYC stage in loan application stages.
  string client_ref_id = 3;
  string kyc_application_id = 4;
}

message UpdateApplicationDetailsRequest {
  string application_id = 1;
  UserSelectedDetails details = 2;
}

message UpdateApplicationDetailsResponse {
  rpc.Status status = 1;
}

message InitDrawDownRequest {
  string application_id = 1;
}

message InitDrawDownResponse {
  enum Status {
    STATUS_OK = 0;
    STATUS_VALIDATION_FAILED = 100;
    PERMANENT_FAILURE = 101;
  }
  rpc.Status status = 1;
}

message InitMandateRequest {
  string application_id = 1;
  bytes redirect_link = 2;
}

message InitMandateResponse {
  enum Status {
    OK = 0;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  string orch_id = 2;
}

message InitEsignRequest {
  string application_id = 1;
}

message InitEsignResponse {
  rpc.Status status = 1;
  // id returned by esign service for orchestration.
  string orch_id = 2;
}

message InitDisbursementRequest {
  //external application id
  string application_id = 1;
  // internal application id
  string internal_application_id = 2;
  // optional field to pass the down payment details.
  DownPaymentDetails down_payment_details = 3;
  // optional field to pass the service provider details.
  ServiceProviderDetails service_provider_details = 4;
}

message InitDisbursementResponse {
  enum Status {
    OK = 0;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message UpdateUserDetailsRequest {
  string application_id = 1;
  enums.UpdateUserDetailsFieldMask update_field_mask = 2;
  oneof details {
    AddressDetails address_details = 3;
    EmploymentDetails employment_details = 4;
    api.typesv2.common.BankAccountDetails bank_account_details = 5;
    PersonalDetails personal_details = 6;
    LocationDetails location_details = 7;
  }
}

message UpdateUserDetailsResponse {
  rpc.Status status = 1;
}

message RecordConsentRequest {
  string application_id = 1;
  repeated enums.ConsentType consent_types = 2;
  api.typesv2.common.Device device = 3;
  string ip_address = 4;
}

message RecordConsentResponse {
  rpc.Status status = 1;
}

message InitPennyDropRequest {
  string application_id = 1;
  api.typesv2.common.BankAccountDetails bank_account_details = 2;
}

message InitPennDropResponse {
  rpc.Status status = 1;
  string orch_id = 2;
}

message GetLoanApplicationStageRequest {
  string application_id = 1;
  enums.LoanApplicationStageName stage_name = 2;
}

message GetLoanApplicationStageResponse {
  rpc.Status status = 1;
  LoanApplicationStage stage = 2;
}

message GetLatestLoanApplicationStageRequest {
  string application_id = 1;
}

message GetLatestLoanApplicationStageResponse {
  rpc.Status status = 1;
  enums.LoanApplicationStatus application_status = 2;
  enums.LoanApplicationStageName stage_name = 3;
  enums.LoanApplicationStageStatus stage_status = 4;
  enums.LoanApplicationStageSubStatus stage_sub_status = 5;
  LoanApplicationStageDetails  stage_details = 6;
}

