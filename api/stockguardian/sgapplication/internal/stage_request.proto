syntax = "proto3";

package stockguardian.sgapplication;

import "api/stockguardian/sgapplication/internal/loan_application.proto";
import "api/stockguardian/sgapplication/internal/loan_application_stage.proto";
import "api/stockguardian/sgapplication/enums/enums.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapplication";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgapplication";

message InitiateStageRequest {
	LoanApplication loan_application = 1;
	enums.LoanApplicationStageName stage_name = 2;
  StageRequestDetails stage_details = 3;
}

message InitiateStageResponse {
  LoanApplicationStage stage = 1;
  LoanApplication loan_application = 2;
}

message ExecuteStageRequest {
  LoanApplication loan_application = 1;
  LoanApplicationStage stage = 2;
  StageRequestDetails stage_details = 3;
}

message ExecuteStageResponse {
  LoanApplication loan_application = 1;
  repeated enums.LoanApplicationFieldMask loan_application_update_field_masks = 2;
  LoanApplicationStage stage = 3;
  repeated enums.LoanApplicationStageFieldMask stage_update_field_masks = 4;
}

message StageRequestDetails {
  oneof details {
    MandateRequestDetails mandate_details = 1;
    OfferGenerationStageDetails offer_generation_stage_details = 2;
    DisbursementRequestDetails disbursement_details = 3;
    EsignStageDetails esign_details = 4;
    KycDetails kyc_details = 5;
  }

  message MandateRequestDetails {
    bytes redirect_link = 1;
    MandateStageDetails mandate_stage_details = 2;
  }

  message DisbursementRequestDetails {
    DownPaymentDetails down_payment_details = 1;
    ServiceProviderDetails service_provider_details = 2;
  }
}
