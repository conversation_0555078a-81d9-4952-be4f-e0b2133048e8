//go:generate gen_sql -types=LoanApplicationDetails
syntax = "proto3";

package stockguardian.sgapplication;

import "api/stockguardian/creditrisk/service.proto";
import "api/stockguardian/sgapplication/enums/enums.proto";
import "api/stockguardian/vendors/inhouse/bre.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/bank_account_details.proto";
import "api/typesv2/common/duration.proto";
import "api/typesv2/common/employment_type.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/marital_status.proto";
import "api/typesv2/common/name.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/latlng.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapplication";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgapplication";

message LoanApplication {
  string id = 1;
  string external_id = 2;
  // A unique identifier used for external references, such as vendor communications.
  string applicant_id = 3;
  // The unique identifier for the Loan Service Provider (LSP)
  string client_id = 4;
  // Identifies various loan programs offered by NBFC, which may differ based on the stages and validations required in the loan application process.
  string product_id = 5;
  LoanApplicationDetails details = 6;
  enums.LoanApplicationStatus status = 7;
  enums.LoanApplicationSubStatus sub_status = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  google.protobuf.Timestamp deleted_at = 11;
}

message LoanApplicationDetails {
  UserSelectedDetails user_selected_details = 1;
  LoanOfferDetails loan_offer_details = 2;
  AddressDetails address_details = 4;
  EmploymentDetails employment_details = 5;
  BankAccountDetails bank_account_details = 6;
  ConsentDetails consent_details = 7;
  PersonalDetails personal_details = 8;
  RecurringPaymentDetails recurring_payment_details = 9;
  EsignDocumentDetails esign_document_details = 10;
  LocationDetails location_details = 11;
  VerificationDetails verification_details = 12;
}

message BankAccountDetails {
  api.typesv2.common.BankAccountDetails bank_account_details = 1;
  string registered_pi_id = 2;
}

message AddressDetails {
  api.typesv2.common.PostalAddress address_details = 1;
  api.typesv2.common.AddressType address_type = 2;
}


message EmploymentDetails {
  api.typesv2.common.EmploymentType occupation = 1;
  string organization_name = 2;
  google.type.Money monthly_income = 3;
  string work_email = 4;
  api.typesv2.common.PostalAddress office_address = 5;
}

message ConsentDetails {
  repeated Consent recorded_consents = 1;

  message Consent {
    enums.ConsentType consent_type = 1;
    // refers the consent id from the tsp consent service.
    string reference_id = 2;
    // The type of consent that is being recorded.
    // The timestamp when the consent was recorded.
    google.protobuf.Timestamp recorded_at = 3;
    // url of the externally accepted consent document
    string consent_doc_url = 4;
  }
}

// All the fields should be passed when updating the user selected details in the loan application.
message UserSelectedDetails {
  google.type.Money loan_amount = 1;
  int32 tenure_in_months = 2;
  double interest_rate = 3;
}

message LoanOfferDetails {
  reserved 2;
  creditrisk.LoanOfferDetails loan_offer = 1;
  // This denotes the request id used for locking the offer.
  string lock_request_id = 3;
  // open market user is someone who was not initially evaluated offline by LSP and all evaluation is happening real time
  bool is_open_market_user = 4;
  // this is metadata sent by LSP to be sent to BRE for evaluation of offer generation
  BreParams bre_params = 5;

  message BreParams {
    // policy params used to generate offer from BRE
    vendors.inhouse.bre.PolicyParams policy_params = 1;

    google.type.Money desired_loan_amount = 2;
    api.typesv2.common.Duration tenure = 3;
    string roi = 4;
    google.type.Money pf_plus_gst = 5;
    google.type.Money upfront_payment_amount = 6;
  }
}

message PersonalDetails {
  google.type.Date dob = 1;
  string email = 2;
  api.typesv2.common.Name pan_name = 3;
  api.typesv2.common.Gender gender = 4;
  api.typesv2.common.Name father_name = 5;
  api.typesv2.common.Name mother_name = 6;
  api.typesv2.common.MaritalStatus marital_status = 7;
  string qualification = 8;
}

message LocationDetails {
  // latitude and longitude of the location
  google.type.LatLng latitude_longitude = 1;
  // pincode corresponding to the user's lat long
  string latitude_longitude_pincode = 2;
}

message KycDetails {
  KYCPersonalDetails personal_data = 1;
  string aadhaar_xml = 2;
  string user_selfie_url = 3;
  string aadhaar_photo_url = 4;
  enums.KycOrchestrationFlow orchestration_flow = 5;
}

message KYCPersonalDetails {
  string masked_aadhaar = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.common.Gender gender = 3;
  google.type.Date dob = 4;
  api.typesv2.common.PostalAddress address = 5;
}

message RecurringPaymentDetails {
  string recurring_payment_id = 1;
}

message EsignDocumentDetails {
  string request_id = 1;
}

message DownPaymentDetails {
  // The unique identifier for the transaction
  string transaction_id = 1;
  // Unique Transaction Reference number for the transaction
  string utr = 2;
  // The amount of down payment made
  google.type.Money amount = 3;
}

// ServiceProviderDetails contains details of the service provider to which we are disbursing the loan.
// Why? Sometimes we will transfer the loan amount to some entity other than the applicant.
message ServiceProviderDetails {
  oneof payment_details {
    // This will contain the insurer details, eg - when disbursing the loan for health insurance
    InsurerDetails insurer_details = 1;
  }
}

message InsurerDetails {
  // Id of the insurer
  string id = 1;
  // Bank account number of the insurer
  string bank_account_number = 2;
  // Ifsc code of the insurer
  string bank_account_ifsc = 3;
  // Bank branch of the insurer
  string bank_account_branch = 4;
}

// VerificationDetails contains results of various data verification checks performed during the application process.
message VerificationDetails {
  // Result or score of the face match check between selfie and Aadhaar photo.
  double facematch_score = 1;
  // Result of matching the applicant's name with the name on PAN.
  double name_match_with_pan_score = 2;
  // Result of matching the applicant's name with the bank account holder's name.
  double bank_name_match_score = 3;
  // Result of matching the applicant's address pincode with the pincode from Aadhaar.
  double pincode_match_score = 4;
}
