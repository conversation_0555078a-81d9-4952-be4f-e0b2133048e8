//go:generate gen_sql -types=LoanApplicationStageDetails
syntax = "proto3";

package stockguardian.sgapplication;

import "api/stockguardian/sgapplication/internal/loan_application.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

import "api/stockguardian/sgapplication/enums/enums.proto";
import "api/stockguardian/creditrisk/service.proto";
import "api/stockguardian/vendors/inhouse/bre.proto";
import "api/typesv2/common/duration.proto";
import "api/typesv2/common/bureau.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapplication";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgapplication";

message LoanApplicationStage {
  string id = 1;
  string loan_application_id = 2;
  string applicant_id = 3;
  // A unique identifier used for external references, such as vendor communications.
  string external_id = 4;
  enums.LoanApplicationStageName stage_name = 5;
  LoanApplicationStageDetails details = 6;
  enums.LoanApplicationStageStatus status = 7;
  enums.LoanApplicationStageSubStatus sub_status = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  google.protobuf.Timestamp completed_at = 11;
  google.protobuf.Timestamp deleted_at = 12;
}

message LoanApplicationStageDetails {
  oneof stage_details{
    OfferGenerationStageDetails offer_generation_stage_details = 1;
    MandateStageDetails mandate_stage_details = 2;
    KycStageDetails kyc_stage_details = 3;
    DisbursementStageDetails disbursement_stage_details = 4;
    EsignStageDetails esign_stage_details = 5;
  }
}

message OfferGenerationStageDetails{
  creditrisk.LoanOfferDetails loan_offer_details = 1;
  // This id will be used instead of the to track the loan offer eligibility in case LOEC already exists.
  string client_request_id = 2;
  api.typesv2.common.Bureau bureau = 3;
  BreParams bre_params = 4;

  message BreParams {
    // policy params used to generate offer from BRE
    vendors.inhouse.bre.PolicyParams policy_params = 1;

    google.type.Money desired_loan_amount = 2;
    api.typesv2.common.Duration tenure = 3;
    string roi = 4;
    google.type.Money pf_plus_gst = 5;
    google.type.Money upfront_payment_amount = 6;
  }
}

message MandateStageDetails {
  // id in the recurring payment service
  string recurring_payment_id = 1;
  // mandate bank account number
  string bank_account_number = 2;
  // mandate bank account ifsc
  string bank_account_ifsc = 3;
  // duration of the mandate
  google.protobuf.Duration duration = 4;
  // maximum amount per debit transaction for the mandate
  google.type.Money amount = 5;
}

message KycStageDetails {
  string application_id = 1;
  KycDetails details = 2;
}

message EsignStageDetails {
  string doc_url = 1;
}

message DisbursementStageDetails {
  // optional field to store the down payment details
  DownPaymentDetails down_payment_details = 1;
  // optional field to store details of the service provider to which we are disbursing the loan.
  ServiceProviderDetails service_provider_details = 2;
}
