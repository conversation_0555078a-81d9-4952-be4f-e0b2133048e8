//go:generate gen_queue_pb
syntax = "proto3";

package stockguardian.sgapplication.consumer;

import "api/queue/consumer_headers.proto";
import "api/stockguardian/sgapplication/internal/loan_application.proto";
import "api/stockguardian/sgapplication/enums/enums.proto";
import "api/stockguardian/sgapplication/internal/loan_application_stage.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapplication/consumer";

service Consumer {
  // ProcessSubmitApplicationEvent runs the orchestrator to process the loan application using the submitted data.
  rpc ProcessSubmitApplicationEvent (ProcessSubmitApplicationEventRequest) returns (ProcessSubmitApplicationEventResponse) {}
}

message ProcessSubmitApplicationEventRequest {
  // common request header across all the consumer grpc services.
  queue.ConsumerRequestHeader request_header = 1;
  string application_id = 2;
  repeated enums.SubmitApplicationFieldMask field_masks = 3;
  PersonalDetails personal_details = 4;
  AddressDetails address_details = 5;
  EmploymentDetails employment_details = 6;
  LocationDetails location_details = 7;
  ConsentDetails consent_details = 8;
  KycDetails kyc_details = 9;
  MandateStageDetails recurring_payment_details = 10;
  EsignStageDetails esign_document_details = 11;
  OfferGenerationStageDetails loan_offer_details = 12;
  DownPaymentDetails down_payment_details = 13;
  ServiceProviderDetails service_provider_details = 14;
  VerificationDetails verification_details = 15;
}

message ProcessSubmitApplicationEventResponse {
  // common response header across all the consumer grpc services.
  queue.ConsumerResponseHeader response_header = 1;
}
