syntax = "proto3";

package stockguardian.sginhousebre;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";
import "api/typesv2/common/bureau.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sginhousebre";

service InhouseBre {
  // GetCibilReportFeatures rpc to fetch the cibil report features for a user
  rpc GetCibilReportFeatures (GetCibilReportFeaturesRequest) returns (GetCibilReportFeaturesResponse) {
    option deprecated = true;
    option (google.api.http) = {
      post: "/inhouse/getCibilReportFeatures"
      body: "*"
    };
  }

  // GetPdScore rpc to fetch pd score for a user according to the credit report. This is being evaluated by DS apis
  rpc GetPdScore (GetPdScoreRequest) returns (GetPdScoreResponse) {
    option (google.api.http) = {
      post: "/inhouse/getPdScore"
      body: "*"
    };
  }

  // GetCreditReportFeatures rpc to fetch the credit report features for a user
  rpc GetCreditReportFeatures (GetCreditReportFeaturesRequest) returns (GetCreditReportFeaturesResponse) {
    option (google.api.http) = {
      post: "/inhouse/getCreditReportFeatures"
      body: "*"
    };
  }
}

message GetCibilReportFeaturesRequest {
  option deprecated = true;
  string customer_id = 1 [json_name = "customerId"];
  repeated string features_names = 2 [json_name = "featureNames"];
  string application_id = 3 [json_name = "applicationId"];
  // amount selected bu user against which we want to make the request
  int64 loan_amount = 4 [json_name = "loanAmount"];
  // flag that checks whether we should skip hardpull or not.
  bool hardpull_skip_flag = 5 [json_name = "hardpullSkipFlag"];
}

message GetCibilReportFeaturesResponse {
  option deprecated = true;
  map<string, google.protobuf.Value> feature_value_map = 1 [json_name = "featureValueMap"];
  // the timestamp when credit report was was hardpulled.
  string hardpull_time = 3;
}

message GetPdScoreRequest {
  string customer_id = 1 [json_name = "customerId"];
  string bureau = 2 [json_name = "bureau"]; // accepts CIBIL and EXPERIAN strings
}

message GetPdScoreResponse {
  double pd_score = 1 [json_name = "pdScore"];
  string pd_score_version = 2 [json_name = "pdScoreVersion"];
}

message GetCreditReportFeaturesRequest {
  string customer_id = 1 [json_name = "customerId"];
  repeated string features_names = 2 [json_name = "featureNames"];
  string application_id = 3 [json_name = "applicationId"];
  // amount selected by user against which we want to make the request
  int64 loan_amount = 4 [json_name = "loanAmount"];
  // flag that checks whether we should skip hardpull or not.
  bool hardpull_skip_flag = 5 [json_name = "hardpullSkipFlag"];
  // bureau for which the features are requested
  api.typesv2.common.Bureau bureau = 6 [json_name = "bureau"];
}

message GetCreditReportFeaturesResponse {
  map<string, google.protobuf.Value> feature_value_map = 1 [json_name = "featureValueMap"];
  // the timestamp when credit report was hardpulled.
  string hardpull_time = 2 [json_name = "hardpullTime"];
  // denotes the status of the credit report data availability
  CreditReportDataAvailabilityStatus credit_report_data_availability_status = 3 [json_name = "creditReportDataAvailabilityStatus"];
}

// This enum is used to denote the status of the credit report data availability
enum CreditReportDataAvailabilityStatus {
  CREDIT_REPORT_DATA_AVAILABILITY_STATUS_UNSPECIFIED = 0;
  // Credit report is available in the DB with the complete history
  CREDIT_REPORT_DATA_AVAILABILITY_STATUS_FOUND = 1;
  // Credit report is not found for the user
  CREDIT_REPORT_DATA_AVAILABILITY_STATUS_NOT_FOUND = 2;
}
