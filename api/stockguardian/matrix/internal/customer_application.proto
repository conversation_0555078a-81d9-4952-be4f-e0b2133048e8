//go:generate gen_sql -types=OverrideOptions,ApplicationData,OrchestrationFlowData
syntax = "proto3";

package matrix;

import "api/stockguardian/matrix/enums.proto";
import "api/typesv2/common/date.proto";
import "api/typesv2/common/name.proto";
import "google/protobuf/timestamp.proto";
import "api/typesv2/common/gender.proto";
import "google/type/date.proto";
import "api/typesv2/common/address.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/matrix";

message CustomerApplication {
  string id = 1;

  // ApplicantID of the user
  string applicant_id = 2;

  // Request ID sent by client
  string client_request_id = 3;

  // Product for which the application is being processed. Personal Loans, SA, Wealth.
  // These are for data analysis for now, but can be used for business logic if needed.
  Product product = 4;

  // LENDING_MIN_KYC, LENDING_FULL_KYC
  OrchestrationFlow orchestration_flow = 5;

  // Overall Application status - InProgress, Completed, Failed, Expired
  ApplicationStatus status = 6;

  // Current stage of the application
  Stage current_stage = 7;

  // If the client wants to override certain parameters, like enforce a type of KYC
  OverrideOptions override_options = 8;

  google.protobuf.Timestamp expiry_at = 9;

  // This will store the data which is needed for the application, client is expected to send this data in StartCustomerApplication rpc
  ApplicationData application_data = 10;

  // OrchestrationFlowData contains data related to the orchestration flow like OKYC Data
  OrchestrationFlowData orchestration_flow_data = 11;

  // LSP for customer application
  LoanServiceProvider LSP = 12;
}

message OverrideOptions {

}

message ApplicationData {
  api.typesv2.common.Date date_of_birth = 1;
  api.typesv2.common.Name pan_name = 2;
}

message OrchestrationFlowData {
  oneof data {
    OKYCData okyc_data = 1;
  }
}

// OKYCData contains data fetched from OKYC verification.
message OKYCData {
  OKYCPersonalDetails personal_data = 1;
  string aadhaar_xml = 2;
  string user_selfie_url = 3;
  string aadhaar_photo_url = 4;
}

// OKYCPersonalDetails contains personal information extracted from Aadhaar during OKYC.
message OKYCPersonalDetails {
  string masked_aadhaar = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.common.Gender gender = 3;
  google.type.Date dob = 4;
  api.typesv2.common.PostalAddress address = 5;
}
