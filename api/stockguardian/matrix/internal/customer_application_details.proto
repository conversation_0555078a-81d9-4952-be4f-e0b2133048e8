//go:generate gen_sql -types=StageData
syntax = "proto3";

package matrix;

import "api/stockguardian/matrix/enums.proto";
import "google/protobuf/timestamp.proto";
import "api/stockguardian/matrix/internal/aml.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/gender.proto";
import "google/type/date.proto";
import "api/typesv2/common/address.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/matrix";

message CustomerApplicationDetails {
  string id = 1;

  // ID of the customer application the stage is linked to
  string customer_application_id = 2;

  // Request ID sent by client
  string client_request_id = 3;

  // Stage of the application: EKYC, CKYC, VKYC, CROSS_DATA_VALIDATION
  Stage stage = 4;

  // Stage status - Initiated, InProgress, Completed, Failed, Expired
  StageStatus status = 5;

  // Granular failure status - CKYC_SEARCH_FAILED, EKYC_DATA_VALIDATION_FAILED
  FailureReason failure_reason = 6;

  // metadata needed for a stage
  StageData stage_data = 7;

  google.protobuf.Timestamp completed_at = 8;

  int64 deleted_at_unix = 9;

  // Reason for deletion of a stage, reason can be RETRIES, EXPIRED
  DeletionReason deletion_reason = 10;
}

message VkycData {
  // application id is for nbfc vkyc, used to get applicant details
  string application_id = 1;
  // call id is the id of the call initiated by omegle, used to check call status
  string call_id = 2;
}

message AmlScreeningData {
  // client request id with which the Screening is initiated
  string client_req_id = 1;
  // status of the screening - in progress, failed, success
  AmlScreeningStatus status = 2;
  // result of the screening - match, no match, ...
  AmlScreeningResult result = 3;
  // result of the review in case match is found
  AmlScreeningReviewResult review_result = 4;
  // if match is found then aml action data will be populated
  AmlActionData aml_action_data = 5;
}

message DigilockerKycData {
}

enum AmlScreeningReviewResult {
  AML_SCREENING_REVIEW_RESULT_UNSPECIFIED = 0;
  AML_SCREENING_REVIEW_RESULT_IN_PROGRESS = 1;
  AML_SCREENING_REVIEW_RESULT_APPROVED = 2;
  AML_SCREENING_REVIEW_RESULT_REJECTED = 3;
}

enum AmlScreeningResult {
  AML_SCREENING_RESULT_UNSPECIFIED = 0;
  AML_SCREENING_RESULT_MATCH = 1;
  AML_SCREENING_RESULT_NO_MATCH = 2;
  AML_SCREENING_RESULT_ERROR = 3;
}

// Screening status if screening of the user is attempted or failed or completed
enum AmlScreeningStatus {
  AML_SCREENING_STATUS_UNSPECIFIED = 0;
  // Success - screening attempt in progress
  AML_SCREENING_STATUS_ATTEMPT_IN_PROGRESS = 1;
  // Failed - screening attempt failed
  AML_SCREENING_STATUS_ATTEMPT_FAILED = 2;
  // Completed - screening is completed
  AML_SCREENING_STATUS_ATTEMPT_SUCCESS = 3;
}

message StageData {
  oneof data {
    VkycData vkyc_data = 1;
    AmlScreeningData aml_screening_data = 2;
    DigilockerKycData digilocker_kyc_data = 3;
    SaveOKYCData save_okyc_data = 4;
  }
}

message SaveOKYCData {
  KYCPersonalDetails personal_data = 1;
  string aadhaar_xml = 2;
  string user_selfie_url = 3;
  string aadhaar_photo_url = 4;
}

message KYCPersonalDetails {
  string masked_aadhaar = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.common.Gender gender = 3;
  google.type.Date dob = 4;
  api.typesv2.common.PostalAddress address = 5;
}
