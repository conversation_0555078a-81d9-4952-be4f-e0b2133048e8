syntax = "proto3";

package matrix;

import "api/rpc/status.proto";
import "api/stockguardian/matrix/enums.proto";
import "api/stockguardian/matrix/internal/customer_application.proto";
import "api/stockguardian/matrix/internal/customer_application_details.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/matrix";

/*
This service orchestrates the verification processes for a customer. These verification processes can include:
  •  New Customer Application: Handling the onboarding process for a new customer.
  •  ReKYC (Re-Know Your Customer) Application: Managing the periodic verification and updating of customer information.
  •  Profile Update Application: Facilitating changes to existing customer profiles.
*/
service Matrix {
  // This rpc will start a new customer application with a given applicant id, and client request id.
  // Clients can send supported override options in case they want to customise the flow for their users.
  rpc StartCustomerApplication (StartCustomerApplicationRequest) returns (StartCustomerApplicationResponse);

  // This rpc can be used to check the status of a customer application. It will give the current stage and its status.
  // It will also give a next action if any to be taken from clients
  rpc GetCustomerApplicationStatus (GetCustomerApplicationStatusRequest) returns (GetCustomerApplicationStatusResponse);

  rpc GetCustomerApplicationDetails (GetCustomerApplicationDetailsRequest) returns (GetCustomerApplicationDetailsResponse);

  // This rpc retrieves a customer application by its ID
  rpc GetCustomerApplication (GetCustomerApplicationRequest) returns (GetCustomerApplicationResponse);

  // This rpc can be used to update a specific stage in the customer application flow.
  // It updates the stage status
  // NOTE: Only whitelisted stages can be updated.
  rpc UpdateCustomerApplicationStage (UpdateCustomerApplicationStageRequest) returns (UpdateCustomerApplicationStageResponse);

  // This rpc checks if a customer application exists for a different Loan Service Provider (LSP).
  rpc CheckCustomerApplicationForOtherLsp (CheckCustomerApplicationForOtherLspRequest) returns (CheckCustomerApplicationForOtherLspResponse);
}

message StartCustomerApplicationRequest {
  string applicant_id = 1;
  string client_req_id = 2;
  OrchestrationFlow orch_flow = 3;
  OverrideOptions override_options = 4;
  ApplicationData application_data = 5;
  OrchestrationFlowData orchestration_flow_data = 6;
  Product product = 7;
  LoanServiceProvider LSP = 8;
}

message StartCustomerApplicationResponse {
  rpc.Status status = 1;
  string application_id = 2;
  Stage current_stage = 3;
  StageStatus stage_status = 4;
  Action next_action = 5;

  // contains the metadata needed to perform the next action
  ActionMetadata action_metadata = 6;
}

message GetCustomerApplicationStatusRequest {
  message ApplicantIdAndClientReqId {
    string applicant_id = 1;
    string client_req_id = 2;
  }

  oneof ApplicationIdentifier {
    string application_id = 1;
    ApplicantIdAndClientReqId applicant_id_and_client_req_id = 2;
  }
}

message GetCustomerApplicationStatusResponse {
  rpc.Status status = 1;
  ApplicationStatus application_status = 2;
  Stage current_stage = 3;
  StageStatus stage_status = 4;
  Action next_action = 5;

  // contains the metadata needed to perform the next action
  ActionMetadata action_metadata = 6;
  string application_id = 7;
}

message ActionMetadata {
  oneof Metadata {
    VkycMetadata vkyc_metadata = 1;
    DigilockerKycMetadata digilocker_kyc_metadata = 2;
  }
}

message DigilockerKycMetadata {
  string oauth_url = 1;
}
message VkycMetadata {
  string application_id = 1;
  string call_id = 2;
  // Keeping the status as string, since we just need to pass it to clients
  string call_status = 3;
}

message GetCustomerApplicationDetailsRequest {
  string application_id = 1;
  Stage stage = 2;
}

message GetCustomerApplicationDetailsResponse {
  rpc.Status status = 1;
  CustomerApplicationDetails application_details = 2;
}

message UpdateCustomerApplicationStageRequest {
  string customer_application_id = 1;

  string client_req_id = 2;

  Stage stage = 3;

  StageStatus new_stage_status = 4;
}

message UpdateCustomerApplicationStageResponse {
  // Status of the update operation
  rpc.Status status = 1;
}

message GetCustomerApplicationRequest {
  string application_id = 1;
}

message GetCustomerApplicationResponse {
  rpc.Status status = 1;
  CustomerApplication customer_application = 2;
}

// This message is used to check if a customer application exists for a different LSP.
message CheckCustomerApplicationForOtherLspRequest {
  string applicant_id = 1;
  LoanServiceProvider lsp = 2; // The LSP to check against
}

message CheckCustomerApplicationForOtherLspResponse {
  rpc.Status status = 1;
  bool exists = 2;
}
