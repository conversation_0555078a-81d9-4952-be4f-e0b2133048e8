//go:generate gen_sql -types=OrchestrationFlow,Stage,StageStatus,Action,Product,FailureReason,ApplicationStatus,DeletionReason,LoanServiceProvider
syntax = "proto3";

package matrix;

option go_package = "github.com/epifi/gringott/api/stockguardian/matrix";

enum OrchestrationFlow {
  ORCHESTRATION_FLOW_UNSPECIFIED = 0;
  ORCHESTRATION_FLOW_LENDING_FULL_KYC = 1;
  ORCHESTRATION_FLOW_OFFLINE_KYC = 2;
}

enum Stage {
  STAGE_UNSPECIFIED = 0;
  STAGE_CKYC = 1;
  STAGE_VKYC = 2;
  STAGE_LIVENESS = 3;
  STAGE_AML = 4;
  STAGE_RISK_SCREENING = 5;
  STAGE_PAN_VALIDATION = 6;
  STAGE_CUSTOMER_CREATION = 7;
  STAGE_CROSS_DATA_VALIDATION = 8;
  STAGE_DIGILOCKER_KYC = 9;
  STAGE_ENSURE_KYC_AVAILABILITY = 10;
  STAGE_SAVE_OFFLINE_KYC_DATA = 11;
  STAGE_AADHAAR_SIGNATURE_VALIDATION = 12;
}

enum StageStatus {
  STAGE_STATUS_UNSPECIFIED = 0;
  STAGE_STATUS_IN_PROGRESS = 1;
  STAGE_STATUS_SUCCESS = 2;
  STAGE_STATUS_FAILED = 3;
  STAGE_STATUS_SKIPPED = 4;
  STAGE_STATUS_MANUAL_INTERVENTION = 5;
  // When a stage is forcefully reset after going into a terminal state.
  // the stage can be retried if it's in this state.
  STAGE_STATUS_RESET = 6;
  STAGE_STATUS_TEMPORARILY_BLOCKED = 7;
}

// Note: These actions should match exactly with the ones in sgapigateway/matrix
// TODO(Brijesh): Move actions to a common package for reuse across CKYC and matrix services
enum Action {
  ACTION_UNSPECIFIED = 0;
  ACTION_VKYC = 1;
  ACTION_DIGILOCKER_KYC = 2;
  ACTION_CKYC_OTP_VERIFICATION = 3;
  ACTION_CKYC_OTP_VERIFICATION_USER_TEMP_BLOCKED = 4;
}

enum Product {
  PRODUCT_UNSPECIFIED = 0;
  PRODUCT_PERSONAL_LOANS = 1;
  PRODUCT_FINSALL_HEALTH_INSURANCE = 2;
}

enum FailureReason {
  FAILURE_REASON_UNSPECIFIED = 0;
}

enum ApplicationStatus {
  APPLICATION_STATUS_UNSPECIFIED = 0;
  APPLICATION_STATUS_IN_PROGRESS = 1;
  APPLICATION_STATUS_COMPLETED = 2;
  APPLICATION_STATUS_FAILED = 3;
}

enum DeletionReason {
  DELETION_REASON_UNSPECIFIED = 0;
  DELETION_REASON_EXPIRED = 1;
}

enum CustomerApplicationFieldMask {
  CUSTOMER_APPLICATION_FIELD_MASK_UNSPECIFIED = 0;
  CUSTOMER_APPLICATION_FIELD_MASK_STATUS = 1;
  CUSTOMER_APPLICATION_FIELD_MASK_CURRENT_STAGE = 2;
  CUSTOMER_APPLICATION_FIELD_MASK_OVERRIDE_OPTIONS = 3;
}

enum CustomerApplicationDetailsFieldMask {
  CUSTOMER_APPLICATION_DETAILS_FIELD_MASK_UNSPECIFIED = 0;
  CUSTOMER_APPLICATION_DETAILS_FIELD_MASK_STAGE = 1;
  CUSTOMER_APPLICATION_DETAILS_FIELD_MASK_STATUS = 2;
  CUSTOMER_APPLICATION_DETAILS_FIELD_MASK_FAILURE_REASON = 3;
  CUSTOMER_APPLICATION_DETAILS_FIELD_MASK_STAGE_DATA = 4;
  CUSTOMER_APPLICATION_DETAILS_FIELD_MASK_COMPLETED_AT = 5;
  CUSTOMER_APPLICATION_DETAILS_FIELD_MASK_DELETION_REASON = 6;
}

enum LoanServiceProvider {
  LOAN_SERVICE_PROVIDER_UNSPECIFIED = 0;
  LOAN_SERVICE_PROVIDER_FINSALL = 1;
  LOAN_SERVICE_PROVIDER_FI_MONEY = 2;
}
