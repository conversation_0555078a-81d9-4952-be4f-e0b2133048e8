syntax = "proto3";

package stockguardian.esign;

import "api/stockguardian/sgdocs/enums.proto";
import "api/rpc/status.proto";
import "api/stockguardian/esign/enums.proto";
import "api/stockguardian/esign/esign.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/esign";

service EsignService{
    // Generates the document with the given request id and template.
    rpc GenerateDocument(GenerateDocumentRequest) returns (GenerateDocumentResponse);
    // Generates otp for the given request id.
    rpc GenerateOtp(GenerateOtpRequest) returns (GenerateOtpResponse);
    // Verifies otp for the given request.
    rpc VerifyOtp(VerifyOtpRequest) returns (VerifyOtpResponse);
    // Expires the document associated with the given request id.
    rpc ExpireDocument(ExpireDocumentRequest) returns (ExpireDocumentResponse);
    // Gets the status of esign for the given req-id and client id.
    rpc GetEsignStatus(GetEsignStatusRequest) returns (GetEsignStatusResponse);
    // GetDocument rpc will return the document corresponding to the given request_id and client_id. If no doc exists it will return record not found.
    rpc GetDocument(GetDocumentRequest) returns (GetDocumentResponse);
  // SavePreSignedDocument persists a document that was signed outside the e-signing flow.
    rpc SavePreSignedDocument(SavePreSignedDocumentRequest) returns (SavePreSignedDocumentResponse);
}

message GenerateDocumentRequest{
  string request_id = 1;
  sgdocs.PDFTemplate pdf_template = 2;
  sgdocs.DocType doc_type = 3;
  bytes data = 4;
  string customer_id = 5;
  string client_id = 6;
  esign.AuthMethod auth_method = 7;
  google.protobuf.Timestamp valid_till = 8;
}

message GenerateDocumentResponse{
  rpc.Status status = 1;
  string document = 2;
}

message GenerateOtpRequest{
  string request_id = 1;
  string client_id = 2;
}

message GenerateOtpResponse{
  rpc.Status status = 1;
}

message VerifyOtpRequest{
  string request_id = 1;
  string client_id = 2;
  string otp = 3;
}

message VerifyOtpResponse{
  rpc.Status status = 1;
  VerificationStatus verification_status = 2;
  enum VerificationStatus {
    UNSPECIFIED = 0;
    VERIFIED = 1;
    REJECTED = 2;
    EXHAUSTED = 3;
  }
}

message ExpireDocumentRequest{
    string request_id = 1;
    string client_id = 2;
}

message ExpireDocumentResponse{
    rpc.Status status = 1;
}

message GetEsignStatusRequest{
    string request_id =1;
    string client_id = 2;
}

message GetEsignStatusResponse{
    rpc.Status status = 1;
    EsignStatus esign_doc_status = 2;
}

message GetDocumentRequest {
  string client_id = 1;
  string request_id = 2;
}

message GetDocumentResponse{
  enum Status {
    OK = 0;
    RECORD_NOT_FOUND = 5;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // Base64 document
  string document = 2;
}

message SavePreSignedDocumentRequest{
  string request_id = 1;
  sgdocs.DocType doc_type = 2;
  bytes document_bytes = 3;
  string customer_id = 4;
  string client_id = 5;
  google.protobuf.Timestamp valid_till = 6;
}

message SavePreSignedDocumentResponse{
  rpc.Status status = 1;
  string document_ref_id = 2;
}
