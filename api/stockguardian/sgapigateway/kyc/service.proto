syntax = "proto3";

package stockguardian.sgapigateway.kyc;

import "api/rpc/status.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/date.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/file/file.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapigateway/kyc";

service KYC {
  rpc GetKYCData (GetKYCDataRequest) returns (GetKYCDataResponse);

  // RPC to call when redacted CKYC images are uploaded from Sherlock
  rpc UploadRedactedCKYCDocuments (UploadRedactedCKYCDocumentsRequest) returns (UploadRedactedCKYCDocumentsResponse);

  rpc GetS3PreSignedURL (GetS3PreSignedURLRequest) returns (GetS3PreSignedURLResponse);
  // this rpc is used generate or resend the OTP
  // service decides whether to generate the OTP or resend it
  rpc GenerateOtp(GenerateOtpRequest) returns (GenerateOtpResponse);
  // this rpc is used for otp verification
  // once the otp verification done, it stores the downloaded data of user
  rpc VerifyOtp (VerifyOtpRequest) returns (VerifyOtpResponse);
}

message GenerateOtpRequest {
  string application_id = 1;
}
message GenerateOtpResponse {
  // Note: Keep these in sync with those in CKYC
  enum Status {
    STATUS_UNSPECIFIED = 0;
    // User's mobile number is not registered in CKYC
    STATUS_MOBILE_NOT_REGISTERED = 101;
    // User's auth factor doesn't match with that registered in CKYC
    // This can happen when user is trying to use a non-personal PAN
    STATUS_AUTH_FACTOR_MISMATCH = 102;
    // User tried to re-send OTP to their phone too quickly
    STATUS_OTP_RESEND_BLOCKED = 103;
    // User temporarily blocked to prevent excessive retries
    STATUS_GENERATION_TEMP_BLOCKED = 104;
  }

  rpc.Status status = 1;
  string display_msg = 2;
  google.protobuf.Timestamp next_attempt_at = 3;
}

message VerifyOtpRequest {
  string application_id = 1;
  string otp = 2;
}

message VerifyOtpResponse {
  // Note: Keep these in sync with those in SG API gateway
  enum Status {
    STATUS_UNSPECIFIED = 0;
    // User entered a wrong OTP
    STATUS_INVALID_OTP = 101;
    // User submitted OTP after it's expiry
    STATUS_OTP_EXPIRED = 102;
    // User temporarily blocked to prevent excessive retries
    STATUS_VERIFICATION_TEMP_BLOCKED = 103;
  }
  rpc.Status status = 1;
  string display_msg = 2;
  google.protobuf.Timestamp next_attempt_at = 3;
}

message CKYCPayload {
  string ckyc_number = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.common.Date date_of_birth = 3;
  api.typesv2.common.PostalAddress correspondence_address = 4;
  string pan = 5;
  api.typesv2.common.Image user_image = 6;
  api.typesv2.common.PostalAddress permanent_address = 7;
  repeated IdentityDocument identity_documents = 8;
  // flag to indicate if the correspondence address received is from OVD document
  bool is_correspondence_address_ovd = 9;
  // flag to indicate if the permanent address received is from OVD document
  bool is_permanent_address_ovd = 10;
}

message GetKYCDataRequest {
  KYCRequestIdentifier identifier = 1;
}

message KYCRequestIdentifier {
  oneof identifier {
    string application_id = 1;
    // NOTE: Applicant ID can only be used after customer has been successfully created
    string applicant_id = 2;
    string customer_id = 3;
  }
}

message GetKYCDataResponse {
  rpc.Status status = 1;
  CKYCPayload ckyc_data = 2;
}

message IdentityDocument {
  IdProofType type = 1;
  // The identifier based on the type of the id. e.g PAN Number / Aadhar Number
  string id_value = 2;
  api.typesv2.common.Image document_image = 3;
}

enum IdProofType {
  ID_PROOF_TYPE_UNSPECIFIED = 0;
  ID_PROOF_TYPE_PASSPORT = 1;
  ID_PROOF_TYPE_VOTER_ID = 2;
  ID_PROOF_TYPE_PAN = 3;
  ID_PROOF_TYPE_DRIVING_LICENSE = 4;
  ID_PROOF_TYPE_UID = 5;  // Aadhaar number
  ID_PROOF_TYPE_NREGA_JOB_CARD = 6;
  ID_PROOF_TYPE_NATIONAL_POPULATION_REGISTER_LETTER = 7;
  ID_PROOF_TYPE_CKYC_RECORD = 8;
  ID_PROOF_TYPE_EKYC_AUTHENTICATION = 9;
  ID_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION = 10;
  ID_PROOF_TYPE_OFFLINE_OTHERS = 11;
  ID_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT = 12;
  ID_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED = 13;
}

message UploadRedactedCKYCDocumentsRequest {
  string ckyc_reference_id = 1;
  string application_id = 2;
  repeated api.typesv2.common.file.File redacted_documents = 3;
  string reviewed_by_email = 4;
  // map to store document type is redacted or not
  map<string, api.typesv2.common.BooleanEnum> file_name_to_redaction_map = 5;
}

message UploadRedactedCKYCDocumentsResponse {
  rpc.Status status = 1;
}

message GetS3PreSignedURLRequest {
  // FileName to S3 URL Map
  map<string, string> file_name_s3_u_r_l_map = 1;
}

message GetS3PreSignedURLResponse {
  rpc.Status status = 1;
  // FileName to S3 Pre-Signed URL Map
  map<string, string> file_name_s3_pre_signed_u_r_l_map = 2;
}
