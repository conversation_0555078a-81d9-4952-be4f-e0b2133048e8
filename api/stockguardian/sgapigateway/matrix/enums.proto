syntax = "proto3";

package stockguardian.sgapigateway.matrix;

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapigateway/matrix";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgapigateway.matrix";

enum Stage {
  STAGE_UNSPECIFIED = 0;
  STAGE_CKYC = 1;
  STAGE_VKYC = 2;
  STAGE_LIVENESS = 3;
  STAGE_AML = 4;
  STAGE_RISK_SCREENING = 5;
  STAGE_PAN_VALIDATION = 6;
  STAGE_CUSTOMER_CREATION = 7;
  STAGE_CROSS_DATA_VALIDATION = 8;
  STAGE_DIGILOCKER_KYC = 9;
}

enum StageStatus {
  STAGE_STATUS_UNSPECIFIED = 0;
  STAGE_STATUS_IN_PROGRESS = 1;
  STAGE_STATUS_SUCCESS = 2;
  STAGE_STATUS_FAILED = 3;
  STAGE_STATUS_SKIPPED = 4;
  STAGE_STATUS_RESET = 5;
}

// Note: These actions should match exactly with the ones in sgapigateway/matrix
// TODO(Brijesh): Move actions to a common package for reuse across CKYC and matrix services
enum Action {
  ACTION_UNSPECIFIED = 0;
  ACTION_VKYC = 1;
  // this action is used to send intimation to caller to initiate the OTP verification procedure
  ACTION_CKYC_OTP_VERIFICATION = 2;
  // this action is used to send intimation to caller to Block the user temporarily during otp verification procedure
  // info on the exact time after with retry will be enabled again will be given to by ckyc service
  ACTION_CKYC_OTP_VERIFICATION_USER_TEMP_BLOCKED = 3;
}

enum ApplicationStatus {
  APPLICATION_STATUS_UNSPECIFIED = 0;
  APPLICATION_STATUS_IN_PROGRESS = 1;
  APPLICATION_STATUS_COMPLETED = 2;
  APPLICATION_STATUS_FAILED = 3;
}

enum FailureReason {
  FAILURE_REASON_UNSPECIFIED = 0;
}
