syntax = "proto3";

package stockguardian.sgexternalgateway.common;

import "api/stockguardian/sgexternalgateway/common/enums.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgexternalgateway/common";

// Address represents a physical postal address.
message Address {
  // Address lines, typically including street address, building name/number, area etc.
  repeated string address_lines = 1 [json_name = "address_lines"];
  // City name.
  string city = 2 [json_name = "city"];
  // Postal code (e.g., ZIP code).
  string pincode = 3 [json_name = "pincode"];
  // State name.
  State state = 4 [json_name = "state"];
}

// Name represents a person's full name, split into first and last name.
message Name {
  // The person's first name (given name).
  string first = 1 [json_name = "first"];
  // The person's middle name (if any).
  string middle = 2 [json_name = "middle"];
  // The person's last name (surname).
  string last = 3 [json_name = "last"];
}

// MobileNumber represents a mobile phone number including the country code.
message MobileNumber {
  // The main part of the mobile number, without the country code.
  uint64 number = 1 [json_name = "number"];
  // The international dialing country code (e.g., 91 for India).
  uint32 country_code = 2 [json_name = "country_code"];
}

// BankAccount represents basic bank account details.
message BankAccount {
  // The bank account number.
  string number = 1 [json_name = "number"];
  // The Indian Financial System Code (IFSC) identifying the bank branch.
  string ifsc = 2 [json_name = "ifsc"];
}

// Duration represents a time duration specified with a unit.
message Duration {
  // The numeric value of the duration.
  uint32 duration = 1 [json_name = "duration"];
  // The unit of the duration. Expected values: "DURATION_UNIT_DAYS", "DURATION_UNIT_MONTHS", "DURATION_UNIT_YEARS".
  DurationUnit duration_unit = 2 [json_name = "duration_unit"];
}
