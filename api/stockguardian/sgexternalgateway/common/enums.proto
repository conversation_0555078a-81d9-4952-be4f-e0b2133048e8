syntax = "proto3";

package stockguardian.sgexternalgateway.common;

option go_package = "github.com/epifi/gringott/api/stockguardian/sgexternalgateway/common";

enum Gender {
  GENDER_UNSPECIFIED = 0;
  GENDER_MALE = 1;
  GENDER_FEMALE = 2;
  GENDER_OTHER = 15;
}

enum Occupation {
  OCCUPATION_UNSPECIFIED = 0;
  OCCUPATION_SALARIED = 1;
  OCCUPATION_SELF_EMPLOYED = 2;
  OCCUPATION_RETIRED = 3;
  OCCUPATION_HOMEMAKER = 4;
  OCCUPATION_UNEMPLOYED = 5;
  OCCUPATION_OTHER = 31;
}

enum Qualification {
  QUALIFICATION_UNSPECIFIED = 0;
  QUALIFICATION_DIPLOMA_HOLDER = 1;
  QUALIFICATION_DOCTORAL = 2;
  QUALIFICATION_GRADUATE = 3;
  QUALIFICATION_POSTGRADUATE = 4;
  QUALIFICATION_UNDERGRADUATE = 5;
  QUALIFICATION_OTHER = 15;
}

enum MaritalStatus {
  MARITAL_STATUS_UNSPECIFIED = 0;
  MARITAL_STATUS_SINGLE = 1;
  MARITAL_STATUS_MARRIED = 2;
  MARITAL_STATUS_OTHER = 15;
}

enum ResidentialStatus {
  RESIDENTIAL_STATUS_UNSPECIFIED = 0;
  RESIDENTIAL_STATUS_OWNED = 1;
  RESIDENTIAL_STATUS_RENTED = 2;
  RESIDENTIAL_STATUS_OTHER = 15;
}

enum Bureau {
  BUREAU_UNSPECIFIED = 0;
  BUREAU_CIBIL = 1;
  BUREAU_EXPERIAN = 2;
}

enum DurationUnit {
  DURATION_UNIT_UNSPECIFIED = 0;
  DURATION_UNIT_DAYS = 1;
  DURATION_UNIT_MONTHS = 2;
  DURATION_UNIT_YEARS = 3;
}

enum InsurancePolicyType {
  INSURANCE_POLICY_TYPE_UNSPECIFIED = 0;
  INSURANCE_POLICY_TYPE_HEALTH = 1;
  INSURANCE_POLICY_TYPE_HEALTH_MULTI_YEAR = 2;
  INSURANCE_POLICY_TYPE_LIFE = 3;
  INSURANCE_POLICY_TYPE_OTHER = 15;
}

enum State {
  STATE_UNSPECIFIED = 0;
  STATE_JAMMU_AND_KASHMIR = 1;
  STATE_HIMACHAL_PRADESH = 2;
  STATE_PUNJAB = 3;
  STATE_CHANDIGARH = 4;
  STATE_UTTARANCHAL = 5;
  STATE_HARYANA = 6;
  STATE_DELHI = 7;
  STATE_RAJASTHAN = 8;
  STATE_UTTAR_PRADESH = 9;
  STATE_BIHAR = 10;
  STATE_SIKKIM = 11;
  STATE_ARUNACHAL_PRADESH = 12;
  STATE_NAGALAND = 13;
  STATE_MANIPUR = 14;
  STATE_MIZORAM = 15;
  STATE_TRIPURA = 16;
  STATE_MEGHALAYA = 17;
  STATE_ASSAM = 18;
  STATE_WEST_BENGAL = 19;
  STATE_JHARKHAND = 20;
  STATE_ORISSA = 21;
  STATE_CHHATTISGARH = 22;
  STATE_MADHYA_PRADESH = 23;
  STATE_GUJARAT = 24;
  STATE_DAMAN_AND_DIU = 25;
  STATE_DADRA_AND_NAGAR_HAVELI = 26;
  STATE_MAHARASHTRA = 27;
  STATE_ANDHRA_PRADESH = 28;
  STATE_KARNATAKA = 29;
  STATE_GOA = 30;
  STATE_LAKSHADWEEP = 31;
  STATE_KERALA = 32;
  STATE_TAMIL_NADU = 33;
  STATE_PONDICHERRY = 34;
  STATE_ANDAMAN_AND_NICOBAR_ISLANDS = 35;
  STATE_TELANGANA = 36;
  STATE_APO_ADDRESS = 99;
}
