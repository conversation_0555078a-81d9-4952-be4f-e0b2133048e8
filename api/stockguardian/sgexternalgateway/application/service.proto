syntax = "proto3";

package stockguardian.sgexternalgateway.application;

import "api/stockguardian/sgexternalgateway/application/enums.proto";
import "api/stockguardian/sgexternalgateway/common/common.proto";
import "api/stockguardian/sgexternalgateway/common/enums.proto";
import "api/stockguardian/sgexternalgateway/header/header.proto";
import "google/api/annotations.proto";
import "api/rpc/method_options.proto";
import "google/type/latlng.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgexternalgateway/application";

// Application service handles loan application creation, submission, and status retrieval.
service Application {
  // StartApplication creates a new loan application for the applicant and returns the application ID.
  rpc StartApplication (StartApplicationRequest) returns (StartApplicationResponse) {
    option (rpc.auth_required) = true;
    option (google.api.http) = {
      post: "/v1/applications/start-application"
      body: "*"
    };
  }

  // SubmitApplication takes the details provided by the client to submit the loan application.
  // It then triggers the orchestrator to process the application as far as possible using the provided data.
  // Clients can call this repeatedly with additional data to progress the application, until pre-disbursement stage.
  // Processing happens asynchronously. Use the GetApplicationStatus RPC to poll for the current status.
  rpc SubmitApplication (SubmitApplicationRequest) returns (SubmitApplicationResponse) {
    option (rpc.auth_required) = true;
    option (google.api.http) = {
      post: "/v1/applications/submit-application"
      body: "*"
    };
  }

  // InitiateDisbursement initiates the disbursement process for the loan application using the application ID.
  // It will also verify any down-payment made against the loan amount using the down payment information.
  rpc InitiateDisbursement (InitiateDisbursementRequest) returns (InitiateDisbursementResponse) {
    option (rpc.auth_required) = true;
    option (google.api.http) = {
      post: "/v1/applications/initiate-disbursement"
      body: "*"
    };
  }

  // GetApplicationStatus retrieves the current status of a loan application using the application ID.
  rpc GetApplicationStatus (GetApplicationStatusRequest) returns (GetApplicationStatusResponse) {
    option (rpc.auth_required) = true;
    option (google.api.http) = {
      post: "/v1/applications/get-application-status"
      body: "*"
    };
  }
}

// StartApplicationRequest contains the details required to start a loan application
message StartApplicationRequest {
  // Loan header information will be automatically populated by the interceptor after auth
  header.LoanHeader header = 1;
  // Applicant's identifier
  string applicant_id = 2 [json_name = "applicant_id"];
  // Loan product identifier
  string product_id = 3 [json_name = "product_id"];
}

message StartApplicationResponse {
  // Application ID generated by the system upon successful creation.
  string application_id = 1 [json_name = "application_id"];
}

// SubmitApplicationRequest contains the details provided by the client to submit the loan application.
// This can include partial or complete information depending on the current stage of the application.
message SubmitApplicationRequest {
  // Loan header information will be automatically populated by the interceptor after auth.
  header.LoanHeader header = 1;
  // Application ID to which the details are being submitted.
  string application_id = 2 [json_name = "application_id"];
  // Mask to specify which fields are being submitted in this request.
  repeated SubmitApplicationFieldMask field_masks = 3 [json_name = "field_masks"];
  // UserDetails details contains all the details of the applying user.
  UserDetails user_details = 4 [json_name = "user_details"];
  // KYC (Know Your Customer) details specific to verification artifacts.
  KycDetails kyc_details = 5 [json_name = "kyc_details"];
  // Mandate details for loan repayment.
  MandateDetails mandate_details = 6 [json_name = "mandate_details"];
  // E-sign details related to the loan agreement.
  ESignDetails e_sign_details = 7 [json_name = "e_sign_details"];
  // Loan requirement details.
  LoanRequirementDetails loan_requirement_details = 8 [json_name = "loan_requirement_details"];
  // BreParams contains common parameters used by the bre to generate the loan offer.
  BreParams bre_params = 9 [json_name = "bre_params"];
  // Parameters used by the bre to generate the loan offer for the specified loan product.
  ProductSpecificBreParams product_specific_bre_params = 10 [json_name = "product_specific_bre_params"];
  // Consent details provided by the applicant.
  ConsentDetails consent_details = 11 [json_name = "consent_details"];
  // Details related to various verification checks performed.
  VerificationDetails verification_details = 12 [json_name = "verification_details"];
}

// UserDetails contains all the details of the applying user.
message UserDetails {
  // Date of Birth provided by the user (Format: YYYY-MM-DD).
  string dob = 1 [json_name = "dob"];
  // Full name provided by the user.
  common.Name name = 2 [json_name = "name"];
  // Gender of the user (e.g., GENDER_MALE, GENDER_FEMALE, GENDER_OTHER).
  common.Gender gender = 3 [json_name = "gender"];
  // Current residential address provided by the user.
  common.Address address = 4 [json_name = "address"];
  // Email address provided by the user.
  string email_id = 5 [json_name = "email_id"];
  // Latitude and Longitude of the user's location.
  google.type.LatLng lat_long = 6 [json_name = "lat_long"];
  // Pincode corresponding to the latitude and longitude of user's location
  string lat_long_pincode = 7 [json_name = 'lat_long_pincode'];
  // Type of occupation (e.g., OCCUPATION_SALARIED, OCCUPATION_STUDENT).
  common.Occupation occupation_type = 8 [json_name = "occupation_type"];
  // Monthly income of the user.
  google.type.Money monthly_income = 9 [json_name = "monthly_income"];
  // Mother's name.
  common.Name mother_name = 10 [json_name = "mother_name"];
  // Father's name.
  common.Name father_name = 11 [json_name = "father_name"];
  // Marital status of the user (e.g., MARITAL_STATUS_SINGLE, MARITAL_STATUS_OTHER).
  common.MaritalStatus marital_status = 12 [json_name = "marital_status"];
  // Highest educational qualification of the user. (e.g., QUALIFICATION_UNDERGRADUATE)
  common.Qualification qualification = 13 [json_name = "qualification"];
}

// KycDetails contains the KYC verification artifacts.
message KycDetails {
  // Masked Aadhaar number (last 4 digits visible).
  string masked_aadhaar = 1 [json_name = "masked_aadhaar"];
  // Base64 encoded Aadhaar XML content.
  string aadhaar_xml = 2 [json_name = "aadhaar_xml"];
  // Gender as per Aadhaar (e.g., GENDER_MALE, GENDER_FEMALE, GENDER_OTHER).
  common.Gender aadhaar_gender = 3 [json_name = "aadhaar_gender"];
  // Name as per Aadhaar.
  common.Name aadhaar_name = 4 [json_name = "aadhaar_name"];
  // Date of Birth as per Aadhaar (Format: YYYY-MM-DD).
  string aadhaar_dob = 5 [json_name = "aadhaar_dob"];
  // Address details extracted from Aadhaar.
  common.Address aadhaar_address = 6 [json_name = "aadhaar_address"];
  // URL of the photograph extracted from Aadhaar.
  string aadhaar_photo_url = 7 [json_name = "aadhaar_photo_url"];
  // URL of the selfie provided by the user during the process.
  string user_selfie_url = 8 [json_name = "user_selfie_url"];
}

// MandateDetails contains details for setting up automatic loan repayments.
message MandateDetails {
  // Bank account details for setting up the mandate.
  common.BankAccount bank_account = 1 [json_name = "bank_account"];
  // Mandate registration token or identifier.
  string token = 2 [json_name = "token"];
  // Duration for which the mandate is valid.
  common.Duration duration = 3 [json_name = "duration"];
  // Maximum amount per debit transaction for the mandate.
  google.type.Money amount = 4 [json_name = "amount"];
}

// ESignDetails contains details related to the E-signed document.
message ESignDetails {
  // URL of the E-signed loan agreement document.
  string doc_url = 1 [json_name = "doc_url"];
}

// LoanRequirementDetails contains the applicant's requested loan parameters such as amount, tenure, etc
message LoanRequirementDetails {
  // Requested loan amount.
  google.type.Money loan_amount = 1 [json_name = "loan_amount"];
  // Requested Loan tenure.
  common.Duration tenure = 2 [json_name = "tenure"];
  // Requested Rate of Interest (ROI) per annum. Represented as a string, e.g., "15.5" for 15.5%.
  string roi = 3 [json_name = "roi"];
  // Expected Processing Fee (PF) plus applicable Goods and Services Tax (GST).
  google.type.Money pf_plus_gst = 4 [json_name = "pf_plus_gst"];
  // Expected upfront payment (e.g., down payment).
  google.type.Money upfront_payment_amount = 5 [json_name = "upfront_payment_amount"];
}

// BreParams contains common parameters used by the bre to generate the loan offer.
message BreParams {
  // Current EMI obligation of the user.
  google.type.Money current_emi_obligation = 1 [json_name = "current_emi_obligation"];
}

// ProductSpecificBreParams will be used by the bre to generate the loan offer for the specified loan product.
message ProductSpecificBreParams {
  // Optional: Details specific to insurance policy that user intends to purchase with the loan
  InsurancePolicyDetails insurance_policy_details = 1 [json_name = "insurance_policy_details"];
}

// InsurancePolicyDetails contains details specific to insurance policy that user intends to purchase with the loan
message InsurancePolicyDetails {
  // Policy type (e.g., INSURANCE_POLICY_TYPE_HEALTH, INSURANCE_POLICY_TYPE_LIFE, etc.).
  common.InsurancePolicyType policy_type = 1 [json_name = "policy_type"];
  // Policy tenure.
  common.Duration policy_tenure = 2 [json_name = "policy_tenure"];
  // Policy reference ID.
  string policy_reference_id = 3 [json_name = "policy_reference_id"];
  // Premium amount for the insurance policy.
  google.type.Money policy_premium = 4 [json_name = "policy_premium"];
}

// ConsentDetails contains details related to user consents obtained during the application process.
message ConsentDetails {
  // URL of the document detailing the consents provided by the user.
  string doc_url = 1 [json_name = "doc_url"];
}

// VerificationDetails contains results of various data verification checks performed during the application process.
message VerificationDetails {
  // Result or score of the face match check between selfie and Aadhaar photo.
  double facematch_score = 1 [json_name = "facematch_score"];
  // Result of matching the applicant's name with the name on PAN.
  double name_match_with_pan_score = 2 [json_name = "name_match_with_pan_score"];
  // Result of matching the applicant's name with the bank account holder's name.
  double bank_name_match_score = 3 [json_name = "bank_name_match_score"];
  // Result of matching the applicant's address pincode with the pincode from Aadhaar.
  double pincode_match_score = 4 [json_name = "pincode_match_score"];
}

message SubmitApplicationResponse {
  // Application ID for the submitted application.
  string application_id = 1 [json_name = "application_id"];
}

// InitiateDisbursementRequest contains the details needed to initiate the loan disbursement process.
message InitiateDisbursementRequest {
  // Loan header information will be automatically populated by the interceptor after auth
  header.LoanHeader header = 1;
  // Application ID to initiate disbursement
  string application_id = 2 [json_name = "application_id"];
  // Optional: Down payment details
  DownPaymentDetails down_payment_details = 3 [json_name = "down_payment_details"];
  // Optional: Service provider details
  ServiceProviderDetails service_provider_details = 4 [json_name = "service_provider_details"];
}

// DownPaymentDetails contains details of a payment transaction, typically for down-payment verification.
message DownPaymentDetails {
  // Transaction ID from the payment gateway or partner system.
  string transaction_id = 1 [json_name = "transaction_id"];
  // Amount of the down payment.
  google.type.Money amount = 2 [json_name = "amount"];
}

// ServiceProviderDetails contains details of the service provider to which we are disbursing the loan.
// Why? In some cases, we will transfer the loan amount to some entity other than the applicant.
// Eg - For health insurance loan, we will transfer the loan amount to insurer's bank account.
message ServiceProviderDetails {
  // Insurer details, if applicable.
  InsurerDetails insurer_details = 1 [json_name = "insurer_details"];
}

// InsurerDetails contains identifier for the insurance provider.
message InsurerDetails {
  // Identifier for the insurer.
  string id = 1 [json_name = "id"];
  // Bank account number of the insurer.
  string bank_account_number = 2 [json_name = "bank_account_number"];
  // Ifsc code of the insurer.
  string bank_account_ifsc = 3 [json_name = "bank_account_ifsc"];
  // Bank branch of the insurer.
  string bank_account_branch = 5 [json_name = "bank_account_branch"];
}

message InitiateDisbursementResponse {}

// GetApplicationStatusRequest retrieves the status of a loan application using the application ID
message GetApplicationStatusRequest {
  // Loan header information will be automatically populated by the interceptor after auth
  header.LoanHeader header = 1;
  // Application ID to retrieve the status
  string application_id = 2 [json_name = "application_id"];
}

message GetApplicationStatusResponse {
  // Current status of the loan application (e.g., CREATED, IN_PROGRESS, MANUAL_INTERVENTION, SUCCESS, FAILED, CANCELLED).
  string application_status = 1 [json_name = "application_status"];
  // Current stage of the loan application (e.g., KYC, E_SIGN, MANDATE, DISBURSEMENT).
  string application_stage = 2 [json_name = "application_stage"];
  // Status of the current stage of the loan application (e.g., CREATED, IN_PROGRESS, MANUAL_INTERVENTION, SUCCESS, FAILED, CANCELLED).
  string application_stage_status = 3 [json_name = "application_stage_status"];
  // Sub Status of the current stage of the loan application (e.g., OFFER_REJECTED, OFFER_EXPIRED, etc)
  string application_stage_sub_status = 4 [json_name = "application_stage_sub_status"];
  // Will be populated only if the current application stage is OFFER_GENERATION.
  OfferGenerationStageDetails offer_generation_stage_details = 5 [json_name = "offer_generation_stage_details"];
}

message OfferGenerationStageDetails {
  // Id of the loan offer
  string offer_id = 1 [json_name = "offer_id"];
  // Minimum loan amount
  google.type.Money min_amount = 2 [json_name = "min_amount"];
  // Maximum loan amount
  google.type.Money max_amount = 3 [json_name = "max_amount"];
  // Maximum EMI amount
  google.type.Money max_emi_amount = 4 [json_name = "max_emi_amount"];
  // Interest percentage
  double interest_percentage = 5 [json_name = "interest_percentage"];
  // Processing fee percentage
  double processing_fee_percentage = 6 [json_name = "processing_fee_percentage"];
  // GST percentage
  double gst_percentage = 7 [json_name = "gst_percentage"];
  // Minimum tenure in months
  int32 min_tenure_in_months = 8 [json_name = "min_tenure_in_months"];
  // Maximum tenure in months
  int32 max_tenure_in_months = 9 [json_name = "max_tenure_in_months"];
  // Valid till date
  string valid_till = 10 [json_name = "valid_till"];
}
