syntax = "proto3";

package stockguardian.sgexternalgateway.application;

option go_package = "github.com/epifi/gringott/api/stockguardian/sgexternalgateway/application";

enum SubmitApplicationFieldMask {
  SUBMIT_APPLICATION_FIELD_MASK_UNSPECIFIED = 0;
  SUBMIT_APPLICATION_FIELD_MASK_USER_DETAILS = 1;
  SUBMIT_APPLICATION_FIELD_MASK_KYC_DETAILS = 2;
  SUBMIT_APPLICATION_FIELD_MASK_MANDATE_DETAILS = 3;
  SUBMIT_APPLICATION_FIELD_MASK_ESIGN_DETAILS = 4;
  SUBMIT_APPLICATION_FIELD_MASK_LOAN_REQUIREMENT_DETAILS = 5;
  SUBMIT_APPLICATION_FIELD_MASK_BRE_PARAMS = 6;
  SUBMIT_APPLICATION_FIELD_MASK_PRODUCT_SPECIFIC_BRE_PARAMS = 7;
  SUBMIT_APPLICATION_FIELD_MASK_CONSENT_DETAILS = 8;
  SUBMIT_APPLICATION_FIELD_MASK_VERIFICATION_DETAILS = 9;
}
