syntax = "proto3";

package stockguardian.sgexternalgateway.lead;

import "api/stockguardian/sgexternalgateway/header/header.proto";
import "api/stockguardian/sgexternalgateway/common/common.proto";
import "api/stockguardian/sgexternalgateway/common/enums.proto";
import "api/stockguardian/sgexternalgateway/common/status.proto";
import "google/api/annotations.proto";
import "api/rpc/method_options.proto";
import "google/type/money.proto";
import "api/stockguardian/sgexternalgateway/lead/metadata.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgexternalgateway/lead";

// Lead service handles the creation of new leads for potential loan applications.
service Lead {
  // CreateLead first creates a new applicant and tsp user if it doesn't exist already
  // It then performs a dedupe on the existing applications and loans for the same applicant.
  // Only if the dedupe check passes, we create a new lead in the system and return the id
  rpc CreateLead (CreateLeadRequest) returns (CreateLeadResponse) {
    option (rpc.auth_required) = true;
    option (google.api.http) = {
      post: "/v1/leads/create"
      body: "*"
    };
  };
}

// CreateLeadRequest contains the initial details provided by a potential applicant to create a lead.
message CreateLeadRequest {
  // Loan header information, automatically populated by the interceptor after authentication.
  header.LoanHeader header = 1;
  // Identifier for the specific loan product the applicant is interested in.
  string product_id = 2 [json_name = "product_id"];
  // Applicant's full name.
  common.Name name = 3 [json_name = "name"];
  // Applicant's current residential address.
  common.Address residential_address = 4 [json_name = "residential_address"];
  // Applicant's permanent address.
  common.Address permanent_address = 5 [json_name = "permanent_address"];
  // Applicant's primary mobile phone number.
  common.MobileNumber phone = 6 [json_name = "phone"];
  // Applicant's Permanent Account Number (PAN).
  string pan = 7 [json_name = "pan"];
  // Applicant's email address.
  string email = 8 [json_name = "email"];
  // Applicant's date of birth (Format: YYYY-MM-DD).
  string dob = 9 [json_name = "dob"];
  // Applicant's gender (e.g., GENDER_MALE, GENDER_FEMALE, GENDER_OTHER).
  common.Gender gender = 10 [json_name = "gender"];
  // Applicant's highest educational qualification (e.g., QUALIFICATION_UNDERGRADUATE, etc).
  common.Qualification qualification = 11 [json_name = "qualification"];
  // Applicant's marital status (e.g., MARITAL_STATUS_SINGLE, MARITAL_STATUS_OTHER).
  common.MaritalStatus marital_status = 12 [json_name = "marital_status"];
  // Applicant's age in years.
  int32 age = 13 [json_name = "age"];
  // Status of the applicant's residence (e.g., RESIDENTIAL_STATUS_OWNED, RESIDENTIAL_STATUS_RENTED).
  common.ResidentialStatus residential_status = 14 [json_name = "residential_status"];
  // Applicant's father's full name.
  common.Name father_name = 15 [json_name = "father_name"];
  // Applicant's mother's full name.
  common.Name mother_name = 16 [json_name = "mother_name"];
  // Desired loan tenure (duration).
  common.Duration tenure = 17 [json_name = "tenure"];
  // Down payment amount the applicant intends to pay.
  google.type.Money down_payment_amount = 18 [json_name = "down_payment_amount"];
  // Requested loan amount.
  google.type.Money loan_amount = 19 [json_name = "loan_amount"];
  // Processing fee amount associated with the loan.
  google.type.Money pf_plus_gst = 20 [json_name = "pf_plus_gst"];
  // Applicant's declared monthly income.
  google.type.Money monthly_income = 21 [json_name = "monthly_income"];
  // Product specific metadata.
  ProductMetadata product_metadata = 22 [json_name = "product_metadata"];
}

message CreateLeadResponse {
  enum StatusCode {
    STATUS_CODE_UNSPECIFIED = 0;
    // Indicates that an active loan application already exists for the applicant.
    STATUS_CODE_ACTIVE_LOAN_APPLICATION_ALREADY_EXISTS = 101;
    // Indicates that the applicant already has an active loan account.
    STATUS_CODE_ACTIVE_LOAN_ACCOUNT_ALREADY_EXISTS = 102;
    // Indicates that an active lead already exists for the applicant.
    STATUS_CODE_ACTIVE_LEAD_ALREADY_EXISTS = 103;
    // Indicates that AML check failed
    STATUS_CODE_AML_CHECK_FAILED = 104;
  }
  // Custom status indicating the result of the lead creation operation.
  common.Status status = 1 [json_name = "status"];
  // Unique identifier assigned to the newly created lead.
  string lead_id = 2 [json_name = "lead_id"];
  // Unique identifier for the applicant associated with the lead.
  string applicant_id = 3 [json_name = "applicant_id"];
}
