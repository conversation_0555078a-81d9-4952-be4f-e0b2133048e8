// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package recurringpayment;

import "api/celestial/workflow/client.proto";
import "api/celestial/workflow_req.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/order/domain/request.proto";
import "api/order/order.proto";
import "api/order/payment/accounting_entry_type.proto";
import "api/order/payment/payment_protocol.proto";
import "api/order/payment/transaction.proto";
import "api/queue/consumer_headers.proto";
import "api/recurringpayment/auth_metadata.proto";
import "api/recurringpayment/enums/enums.proto";
import "api/recurringpayment/payload/create_recurring_payment_v1.proto";
import "api/recurringpayment/payload/execute_recurring_payment_v1.proto";
import "api/recurringpayment/recurring_payment.proto";
import "api/recurringpayment/recurring_payments_action.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/interval.proto";
import "api/upi/cred_block.proto";
import "api/upi/device.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/recurringpayment";
option java_package = "com.github.epifi.gamma.api/recurringpayment";

service RecurringPaymentService {
  // CreateRecurringPayment triggers creations of a new recurring payment in our system which can be of the type Standing Instruction,
  // UPI Mandate etc. It will return an order-id, recurring payment id and a txn id for which cred block needs to be generated from the client.
  //
  // It accepts a unique UUID `client_request_id` which is passed on to order. If an order already exists with
  // this id, existing orderId, recurringPaymentId will be returned. So if the caller intends to retry the operation a new unique
  // `client_request_id` needs to be passed.
  //
  // Now, to initiate a recurring payment creation at vendor post pin entry and cred block generation a separate AuthoriseRecurringPaymentCreation call needs to
  // be initiated by client. Any failure to do so leads to an `abandoned order` in the system.
  // Thus, an order is defined as an abandoned order of it's recurring payment creation is not initiated beyond a particular threshold.
  rpc CreateRecurringPayment (CreateRecurringPaymentRequest) returns (CreateRecurringPaymentResponse) {};

  // AuthoriseRecurringPaymentCreation initiates the creation of recurring payment at vendor
  // We will return an ack response from this api for successful initiation at vendor. Client needs to fetch the status
  // from the server via the order id for terminal state.
  rpc AuthoriseRecurringPaymentCreation (AuthoriseRecurringPaymentCreationRequest) returns (AuthoriseRecurringPaymentCreationResponse) {};

  // ProcessRecurringPaymentCreation checks the status of the recurring payment creation
  // This RPC is called by central order orchestrator
  // The RPC is responsible for checking the status of the recurring payment creation for the domain services.
  // If recurring payment creation is successful then DomainProcessingStatus SUCCESS is returned and if txn failed then DomainProcessingStatus
  // PERMANENT_FAILURE is returned.
  // If recurring payment creation is in progress state then status is checked with the domain services and returned accordingly
  rpc ProcessRecurringPaymentCreation (order.domain.ProcessFulfilmentRequest) returns (order.domain.ProcessFulfilmentResponse) {};

  // ExecuteRecurringPayment rpc validates the request parameters and fast fails the request in case of any failures
  // Validation checks includes :
  // 1. Recurring payment should be in CREATED state.
  // 2. Amount to be transferred should be less than the maximum amount - amount already transferred in the frequency range.
  // 3. Number of execution should be less than the maximum allowed executions for a frequency range.
  // 4. Current balance should be greater than the amount to be transferred.
  //
  // It accepts a unique UUID `client_request_id` which is passed on to order. If an order already exists with
  // this id, existing orderId will be returned. So if the caller intends to retry the operation a new unique
  // `client_request_id` needs to be passed.
  //
  // After successfully validating the request params we will create an order with workflow as Recurring Payment Execution
  // and returns the order id with order and transaction parameters
  // NOTE that successful response here does not mean completion of payment transfer. Client needs to fetch the status of the payment via
  // the order id returned in response.
  rpc ExecuteRecurringPayment (ExecuteRecurringPaymentRequest) returns (ExecuteRecurringPaymentResponse) {};

  // ProcessRecurringPaymentExecution rpc does the following :
  // 1. Creates an entry in the transaction schema if not already exists and initiates payment with the domain services.
  // 2. In case entry already exists then, transaction status is checked from the domain services and action is taken accordingly.
  //    a. If the transaction is in a terminal state, then the corresponding order domain status is returned. i.e.
  //       if txn was successful then DomainProcessingStatus SUCCESS is returned and if txn failed then DomainProcessingStatus
  //       PERMANENT_FAILURE is returned.
  //    b. If the transaction is in suspected state, then status for the request is checked with the domain services and after
  //       updating the state machine of the transaction one of DomainProcessingStatus is returned in the response.
  rpc ProcessRecurringPaymentExecution (order.domain.ProcessPaymentRequest) returns (order.domain.ProcessPaymentResponse) {};

  // CreateModifyAttempt rpc creates a new request for modifying of a recurring payment. We will create an order
  // with workflow as MODIFY_RECURRING_PAYMENT and initiate creation at domain service and update the recurring payment
  // state to MODIFY_QUEUED.
  // It will return an order-id which can be used for fetching the status of the modify request
  // and an txn id corresponding to which cred block needs to be generated.
  rpc CreateModifyAttempt (CreateModifyAttemptRequest) returns (CreateModifyAttemptResponse) {};

  // Authorise recurring payment modify initiates the modify of recurring payment parameters at vendor
  // We will return an ack response from this api for successful initiation at vendor. Client needs to fetch the status
  // from the server via the order id for terminal state.
  rpc AuthoriseRecurringPaymentModify (AuthoriseRecurringPaymentModifyRequest) returns (AuthoriseRecurringPaymentModifyResponse) {};

  // ProcessRecurringPaymentModify checks the status of the recurring payment modify
  // This RPC is called by central order orchestrator
  // The RPC is responsible for checking the status of the recurring payment modify from the domain services.
  // If recurring payment modify is successful then DomainProcessingStatus SUCCESS is returned and if failed then DomainProcessingStatus
  // PERMANENT_FAILURE is returned.
  // If recurring payment modify is in progress state then status is checked with the domain services and returned accordingly
  rpc ProcessRecurringPaymentModify (order.domain.ProcessFulfilmentRequest) returns (order.domain.ProcessFulfilmentResponse) {};

  // CreateRevokeAttempt rpc triggers a new request for revoke of a recurring payment. We will create an order
  // with workflow as REVOKE_RECURRING_PAYMENT and initiate creation at domain service and update the recurring payment
  // state to REVOKE_QUEUED.
  // It will return an order-id which can be used for fetching the status of the revoke request
  // and an txn id corresponding to which cred block needs to be generated.
  rpc CreateRevokeAttempt (CreateRevokeAttemptRequest) returns (CreateRevokeAttemptResponse) {};

  // AuthoriseRecurringPaymentRevoke initiates the revoke of recurring payment parameters at vendor
  // We will return an ack response from this api for successful initiation at vendor. Client needs to fetch the status
  // from the server via the order id for terminal state.
  rpc AuthoriseRecurringPaymentRevoke (AuthoriseRecurringPaymentRevokeRequest) returns (AuthoriseRecurringPaymentRevokeResponse) {};

  // ProcessRecurringPaymentRevoke checks the status of the recurring payment revoke
  // This RPC is called by central order orchestrator
  // The RPC is responsible for checking the status of the recurring payment modify from the domain services.
  // If recurring payment modify is successful then DomainProcessingStatus SUCCESS is returned and if failed then DomainProcessingStatus
  // PERMANENT_FAILURE is returned.
  // If recurring payment revoke is in progress state then status is checked with the domain services and returned accordingly
  rpc ProcessRecurringPaymentRevoke (order.domain.ProcessFulfilmentRequest) returns (order.domain.ProcessFulfilmentResponse) {};

  // GetRecurringPaymentDetailsById fetches all params needed to surface a recurring payment on UI
  // It includes the name, image_url of the payee, execution count of the recurring payment and additional details from the domain
  // services. For eg : umn, urn in case of upi mandate
  // It also return details such as transaction id, expiry time if any action is requested
  rpc GetRecurringPaymentDetailsById (GetRecurringPaymentDetailsByIdRequest) returns (GetRecurringPaymentDetailsByIdResponse) {};

  // GetRecurringPaymentExternalId fetches recurring payment for the given external id
  rpc GetRecurringPaymentByExternalId (GetRecurringPaymentByExternalIdRequest) returns (GetRecurringPaymentByExternalIdResponse) {};

  // GetExecutionsForRecurringPayment fetches a list of recurring payment executions
  // Thus, rpc method returns the list in pages. It returns all the executions from specified start_timestamp.
  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true, executions until the given start timestamp are returned.
  // If marked false, executions post the given timestamp are returned.
  // The max number of activities returned is bounded by the page_size specified in the request, while offset helps in
  // eliminating initial entries which are already returned in the last call.
  rpc GetExecutionsForRecurringPayment (GetExecutionsForRecurringPaymentRequest) returns (GetExecutionsForRecurringPaymentResponse) {};

  // DeclineRecurringPaymentAction to decline pending actions for a recurring payment
  // Pending actions includes creation, modify, revoke of a recurring payment initiated by payer which needs
  // authorisation from the payer.
  rpc DeclineRecurringPaymentAction (DeclineRecurringPaymentActionRequest) returns (DeclineRecurringPaymentActionResponse) {};

  // GetRecurringPaymentActionStatus fetches the state of the given action request
  rpc GetRecurringPaymentActionStatus (GetRecurringPaymentActionStatusRequest) returns (GetRecurringPaymentActionStatusResponse) {};

  // GetRecurringPaymentsForActor fetches the list of recurring payments for an actor
  rpc GetRecurringPaymentsForActor (GetRecurringPaymentsForActorRequest) returns (GetRecurringPaymentsForActorResponse) {};

  // GetRecurringPaymentCount fetches count of recurring payments for different states.
  rpc GetRecurringPaymentsCountForActor (GetRecurringPaymentsCountForActorRequest) returns (GetRecurringPaymentsCountForActorResponse) {};

  // CreatePauseOrUnpauseAttempt rpc creates a new request for pause or unpause of a recurring payment. We will create an order
  // with workflow as PAUSE_OR_UNPAUSE_RECURRING_PAYMENT and initiate pause/unpause at domain service and update the
  // recurring payment state accordingly based on the actor role initiating the request.
  rpc CreatePauseOrUnpauseAttempt (CreatePauseOrUnpauseAttemptRequest) returns (CreatePauseOrUnpauseAttemptResponse) {};

  // AuthoriseRecurringPaymentAction authorises the any recurring payment action
  rpc AuthoriseRecurringPaymentAction (AuthoriseRecurringPaymentActionRequest) returns (AuthoriseRecurringPaymentActionResponse) {};

  // ProcessPauseUnpause checks the status of the pause/unpause request
  // This RPC is called by central order orchestrator
  // The RPC is responsible for checking the status of the recurring payment pause/unpause from the domain services.
  // If recurring payment pause/unpause is successful then DomainProcessingStatus SUCCESS is returned and if
  // failed then DomainProcessingStatus PERMANENT_FAILURE is returned.
  // If recurring payment pause/unpause is in progress state then status is checked with the domain services and returned accordingly
  rpc ProcessPauseUnpause (order.domain.ProcessFulfilmentRequest) returns (order.domain.ProcessFulfilmentResponse) {};

  // ProcessOneTimeCollectRecurringPayment process collect request for one time execution of recurring payment.
  // This RPC will be called by central order orchestrator. It will check payment status of one time executed transaction
  // and update the transactions status.
  // NOTE: It will not create new transactions if current transaction execution failed.
  rpc ProcessOneTimeCollectRecurringPayment (order.domain.ProcessPaymentRequest) returns (order.domain.ProcessPaymentResponse) {};

  // AuthoriseExecute will initiate a recurring payment with auth (if required). Some recurring payment
  // transactions do not need auth (like one time pre-approved collect mandate request with qr), in this scenario,
  // caller service do not need to pass auth.
  //
  // It will initiate transaction only if transaction is in created state.
  rpc AuthoriseExecute (AuthoriseExecuteRequest) returns (AuthoriseExecuteResponse) {};

  // ProcessRecurringPaymentsNotification sends notifications for recurring payment orders
  rpc ProcessRecurringPaymentsNotification (order.OrderUpdate) returns (ProcessRecurringPaymentsNotificationResponse) {};

  // GetRecurringPaymentById returns recurring payment for the given id
  rpc GetRecurringPaymentById (GetRecurringPaymentByIdRequest) returns (GetRecurringPaymentByIdResponse) {};

  // UpdateRecurringPaymentActions updates the recurring payment actions
  // Note: Currently only updating action detailed status is supported.
  rpc UpdateRecurringPaymentActions (UpdateRecurringPaymentActionsRequest) returns (UpdateRecurringPaymentActionsResponse) {};

  // GetRecurringPayments fetches recurring payment for an actor based on the filters provided in the request
  rpc GetRecurringPayments (GetRecurringPaymentsRequest) returns (GetRecurringPaymentsResponse) {};

  // GetRecurringPaymentDetailsByClientReqId returns the recurring payment details for given client req id
  // NOTE: currently the rpc returns only recurring payment id, we will add other details later when required
  rpc GetRecurringPaymentDetailsByClientReqId (GetRecurringPaymentDetailsByClientReqIdRequest) returns (GetRecurringPaymentDetailsByClientReqIdResponse) {};

  // CreateRecurringPaymentV1 triggers creations of a new recurring payment in our system which can be of the type Standing Instruction, ENach etc.
  //
  // It accepts the list of details needed for creation of the recurring payment along with payload which is needed for
  // creation of the recurring payment and is different for each recurring payment type
  rpc CreateRecurringPaymentV1 (CreateRecurringPaymentV1Request) returns (CreateRecurringPaymentV1Response) {};

  // GetActionStatusV1 returns the current status of an already initiated action,
  // it also returns the redirection deeplink to the next step to be performed by the user in case user intervention is needed to complete the action.
  rpc GetActionStatusV1 (GetActionStatusV1Request) returns (GetActionStatusV1Response);

  // InitiateCreationAuthorizationV1 rpc is useful to initiate authorization for recurring payment creation,
  // it returns deeplink to the screen where the user needs to authorize the recurring payment creation like FEDERAL_SECURE_PIN screen, REDIRECTION_URL deeplink for enach authorization.
  rpc InitiateCreationAuthorizationV1 (InitiateCreationAuthorizationV1Request) returns (InitiateCreationAuthorizationV1Response);

  // AuthorizeCreationV1 rpc is called once the user performs the recurring payment creation authorization.
  // for off app authorization by user like authorization on bank portal, this rpc will be called once we receive authorization callback from the vendor.
  rpc AuthorizeCreationV1 (AuthorizeCreationV1Request) returns (AuthorizeCreationV1Response);

  // GetRecurringPaymentIdByVendorRequestId rpc is useful to fetch recurring payment by recurring payment type and vendor request id that was used for recurring payment creation.
  rpc GetRecurringPaymentIdByVendorRequestId (GetRecurringPaymentIdByVendorRequestIdRequest) returns (GetRecurringPaymentIdByVendorRequestIdResponse);

  // GetRecurringPaymentDetailsFromVendor fetches the recurring payment essentials for the given request identifier from the respective vendor
  // for eg. In case of enach mandates, we can fetch the recurring payment details from the vendor
  rpc GetRecurringPaymentEssentials (GetRecurringPaymentEssentialsRequest) returns (GetRecurringPaymentEssentialsResponse) {};

  // ExecuteRecurringPaymentV1 rpc is used to execute the recurring payment corresponding to the given recurring payment id
  // steps followed for exeuction:
  // 1. Validate the execution request
  //     a) recurring payment should be in CREATED state
  //     b) amount checks based upon the amount type of the recurring payment (maximum or exact)
  //     c) number of execution should be less than the maximum allowed executions
  // 2. Initate recurring payment execution workflow
  rpc ExecuteRecurringPaymentV1 (ExecuteRecurringPaymentV1Request) returns (ExecuteRecurringPaymentV1Response) {};

  // GetRecurringPaymentsByIds returns recurring payment for the given id
  // given the list of recurring payment ids, if no recurring payments are found, the rpc returns status record not found
  // If some of the recurring payments are not found, the rpc just skips them and returns the list of recurring payments
  // it was able to fetch.
  rpc GetRecurringPaymentsByIds (GetRecurringPaymentsByIdsRequest) returns (GetRecurringPaymentsByIdsResponse) {};

  // GetUpcomingTransactions is used to fetch the upcoming transactions for a user that can be auto pay or manual pay
  // for e.g. Fittt txn (auto pay), monthly fund transfer to landlord (manual pay)
  // It is useful when (but not limited to):
  // 1. user has to be notified to add funds when the expected debit amount is more then the current balance of the user
  // NOTE: upcoming transactions are fetched directly from the UpcomingTransactions service and not from the recurring payment tables
  // reason being, recurring payment is a platform service which provides service to execute an auto-pay and it orchestrates that flow.
  // The flexibility of execution of an auto-pay is with the calling service hence most of the time recurring payment service does not know when exactly auto-pay will be executed
  // The same can be built in recurrin payment service but it would require a fuzzy logic which is already written in UpcomingTransactions service
  // todo(Harleen Singh): follow up with DS team to provide filteration on auto-pay and manual-pay
  rpc GetUpcomingTransactions (GetUpcomingTransactionsRequest) returns (GetUpcomingTransactionsResponse) {};

  // RevokeRecurringPaymentV1 is used to initiate orchestration of the recurring payment corresponding to the given
  // recurring payment id. This RPC internally invokes RevokeRecurringPaymentV1 workflow which is a generic workflow
  // that supports the revoke flow with/without authorisation and provides an abstraction for orchestrating the
  // revoke flow independent of the exact recurring payment instrument type that is being revoked.
  rpc RevokeRecurringPaymentV1 (RevokeRecurringPaymentV1Request) returns (RevokeRecurringPaymentV1Response) {};

  // GetRecurringPaymentsActionByVendorRequestId returns recurring payments Action for the given vendor request id.
  // given the vendor request id, if no recurring payments action is found, the rpc returns status record not found
  rpc GetRecurringPaymentsActionByVendorRequestId (GetRecurringPaymentsActionByVendorRequestIdRequest) returns (GetRecurringPaymentsActionByVendorRequestIdResponse) {};
}

message ProcessRecurringPaymentsNotificationResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}

message CreateRecurringPaymentRequest {
  // actor for which recurring payment needs to be created
  string from_actor_id = 1 [(validate.rules).string.min_len = 1];

  // actor to which amount will be transferred
  string to_actor_id = 2 [(validate.rules).string.min_len = 1];

  // type of recurring payment which needs to be created
  recurringpayment.RecurringPaymentType type = 3 [(validate.rules).enum = {not_in: [0]}];

  // remitter payment instrument
  string pi_from = 4;

  // beneficiary payment
  // instrument
  string pi_to = 5;

  google.type.Money amount = 6;

  // amount can either be the fixed amount to be transferred or the maximum amount which can be transferred
  recurringpayment.AmountType amount_type = 21;

  // start and end date of the recurring payment
  api.typesv2.Interval interval = 7;

  // Recurrence rule includes the allowed frequency and day of execution
  // If the day is sent as 0 then it will be treated as default case.
  // If allowed frequency is MONTHLY then day denotes the day of the month on which transaction is expected
  // Allowed Frequency is the time frequency after which the count of maximum allowed txns and
  // maximum allowed limit gets reset.
  // For example : If we have allowed frequency as DAILY and maximum amount as 100 and maximum allowed txns is 2
  // the we can have maximum 2 txns with total amount <= 100 in a day
  recurringpayment.RecurrenceRule recurrence_rule = 9;

  // maximum txns allowed over the allowed frequency time window
  int32 maximum_allowed_txns = 10;

  vendorgateway.Vendor partner_bank = 11 [(validate.rules).enum = {not_in: [0]}];

  order.payment.PaymentProtocol preferred_payment_protocol = 12;

  // Ownership of a recurring payment. Values can be EPIFI_TECH or EPIFI_WEALTH
  recurringpayment.RecurringPaymentOwnership ownership = 13 [(validate.rules).enum = {not_in: [0]}];

  recurringpayment.RecurrencePaymentProvenance provenance = 14 [(validate.rules).enum = {not_in: [0]}];

  recurringpayment.UIEntryPoint ui_entry_point = 15;

  // request id to be sent by caller in case idempotency around
  // order creation is important for the use case.
  // client_req_id must be a valid UUID (via RFC 4122)
  // This was primarily used when order was used for orchestration purposes. We are working on migrating workflows
  // to celestial where client related information is taken as a combination of client id and a client enum value
  // corresponding to the service initiating the request. Hence use of client_request_id must be deprecated
  string client_request_id = 16 [(validate.rules).string = {min_len: 4, max_len: 100}];

  recurringpayment.InitiatedBy initiated_by = 17;

  // payload specific to a recurring payment type
  bytes payload = 18;

  // actor who has requested for creation of recurring payment
  string current_actor_id = 19;

  // transaction id to be used for creation of recurring payment
  string transaction_id = 20;

  // remarks for a recurring payment
  string remarks = 22;

  // denotes if the recurring payment details needs to be shared with the payee
  bool share_to_payee = 23;

  // expiry of the create request
  // user needs to authorise before request gets expired
  google.protobuf.Timestamp expiry = 24;

  // Client details corresponding to the service initiating request. The combination of client and client_req_id must be unique.
  // deprecated - use client_request_id as the unique identifier here.
  celestial.ClientReqId client_id = 25 [deprecated = true];

  // Optional: This deeplink is where user is redirected post recurring payment create authorisation.
  frontend.deeplink.Deeplink post_authorisation_action = 26;

  // Optional: This deeplink is where user is redirected post recurring payment creation completion. Irrespective of the
  // success or failure.
  frontend.deeplink.Deeplink post_recurring_payment_creation_deeplink = 27;
}
message CreateRecurringPaymentResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
    // In case user is blocked
    BLOCKED_USER = 101;

    // invalid start and end date provided in recurring payment creation
    // for eg- start date is greater than the end date
    INVALID_START_AND_END_DATE = 102;

    // amount limit exceeded for recurring payment creation
    AMOUNT_LIMIT_EXCEEDED = 103;
  }
  rpc.Status status = 1;

  // unique identifier for the order
  // order_id in the create recurring payment response corresponds to the order entity created
  // for orchestration purposes of recurring payment.
  // With the migration from OMS to Celestial, we need not create an order in create recurring payment
  // stage. Hence use of order_id in response is deprecated
  string order_id = 2 [deprecated = true];
  // unique identifier for a recurring payment
  string recurring_payment_id = 3;
  // flag denoting if we need any user authentication for processing request
  bool is_authentication_required = 4;
  // transaction id corresponding to which cred block needs to be generated
  string transaction_id = 5;

  recurringpayment.CredBlockType cred_block_type = 6;

  // a list of accounts along with their transaction attribute for making payment
  // Will be sent when auth is required and cred block type is NPCI
  // Deprecated: in favour of next_action
  repeated recurringpayment.TransactionAttribute transaction_attributes = 7 [deprecated = true];

  // Deeplink corresponding to the next action to be done post recurring payment creation initiation
  frontend.deeplink.Deeplink next_action = 8;
}

enum CredBlockType {
  CRED_BLOCK_TYPE_UNSPECIFIED = 0;

  PARTNER_SDK = 1;

  NPCI = 2;
}

message AuthoriseRecurringPaymentCreationRequest {
  // recurring payment id against which creation is to initiated at vendor
  string recurring_payment_id = 1;

  // id of the request to be authorised
  // Not required client needs to just send the client request id
  string req_id = 2 [deprecated = true];

  // actor id of the actor authorising the recurring payment creation
  string current_actor_id = 3;

  recurringpayment.Credential credential = 4;

  // request id to be sent by caller in case idempotency around
  // order creation is important for the use case.
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_request_id = 6;

  // Client details corresponding to the service initiating request. The combination of client and client_req_id must be unique.
  // deprecated - use client_request_id as the unique identifier here.
  celestial.ClientReqId client_id = 7 [deprecated = true];
}

message Credential {
  oneof Params {
    string partner_sdk_cred_block = 1;
    MandateAuthHeader mandate_header = 2;
  }
}

message AuthoriseRecurringPaymentCreationResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  recurringpayment.ActionDetailedStatus.DetailedStatus action_detailed_status = 2;

  // deeplink which we get when action is authorised, based on this client decides which screen to move
  frontend.deeplink.Deeplink post_authorise_deeplink = 3;
}

message ExecuteRecurringPaymentRequest {
  // identifier of the recurring payment through which payment needs to be executed
  string recurring_payment_id = 1;

  // amount to be transferred
  google.type.Money amount = 2;

  // request id to be sent by caller in case idempotency around
  // order creation is important for the use case.
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_request_id = 3 [(validate.rules).string = {min_len: 4, max_len: 100}];

  oneof execution_payload {
    UpiMandateExecuteInfo upi_mandate_execution_info = 4;
  }

  // status with which the order needs to be created. Based on the execution flow the order creations status will vary
  order.OrderStatus order_status = 5;

  // expiry of the execute request
  // user needs to authorise before request gets expired
  google.protobuf.Timestamp expiry = 6;

  // tags to classify recurring payment execution. We can have multiple tags for an execution
  repeated recurringpayment.RecurringPaymentTag tags = 7;

  // Client details corresponding to the service initiating request. The combination of client and client_req_id must be unique.
  // deprecated - use client_request_id as the unique identifier here.
  celestial.ClientReqId client_id = 8 [deprecated = true];
}

enum RecurringPaymentTag {
  RECURRING_PAYMENT_TAG_UNSPECIFIED = 0;

  // Recurring payment execution for mutual fund
  MUTUAL_FUND = 1;

  // Recurring payment execution through FIT for auto pay rules
  FIT_AUTO_PAY = 2;

  // Recurring payment execution for loans SI
  LOANS_ES_AUTO_PAY = 3;

  // Recurring payments to US stocks pool account for SIPs
  US_STOCKS = 4;
}

message ExecuteRecurringPaymentResponse {
  enum Status {
    OK = 0;
    // Amount to be transferred is greater than the maximum amount - amount already transferred in the frequency range.
    MAXIMUM_AMOUNT_LIMIT_BREACHED = 100;
    // Number of transactions is greater than the maximum count allowed in the frequency range
    MAXIMUM_TRANSACTION_COUNT_BREACHED = 101;
    // Amount to be transferred is greater than user account balance
    INSUFFICIENT_BALANCE = 102;
    // invalid pi passed in execute payment request info
    // ( pi_from and pi_to not matches with pi id used to create recurring payment)
    INVALID_PI = 103;
    // invalid sequence number
    // current sequence number is not next to the previous sequence number
    INVALID_SEQ_NUM = 104;
    // mandate is already honoured/executed
    MANDATE_ALREADY_HONOURED = 105;
    // mandate already revoked
    MANDATE_REVOKED = 106;
    // mandate is currently paused
    MANDATE_PAUSED = 107;
    // mandate has been expired
    MANDATE_EXPIRED = 108;
    // account has no access level (cannot call vg in this case)
    ACCOUNT_NO_LEVEL_ACCESS = 109;
  }
  rpc.Status status = 1;
  // unique order id corresponding to an execution request
  // client can use this order id to fetch the terminal status
  string order_id = 2;

  // order with transactions created after execute recurring payment call.
  order.OrderWithTransactions order_with_txn = 3;
}

message CreateModifyAttemptRequest {
  // unique identifier for the recurring payment
  string recurring_payment_id = 1;

  // updated params
  // needs to pass all the fields in the mutable params
  // if the field is not updated, pass the original value
  recurringpayment.MutableParams mutable_params = 2;

  // request id to be sent by caller in case idempotency around
  // order creation is important for the use case.
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_request_id = 3 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // provenance for the modification attempt
  recurringpayment.RecurrencePaymentProvenance provenance = 4 [(validate.rules).enum = {not_in: [0]}];

  // actor id of the user who has initiated the request.
  // If empty we will use initiated by to determine who has initiated the request
  string current_actor_id = 10;

  // the one who is initiating the request
  // To be sent only in case if current actor id is not present
  recurringpayment.InitiatedBy initiated_by = 5;

  // payload specific to a recurring payment type
  bytes payload = 6;

  // request id to be used for vendor initialisation
  // if empty rpc will create one
  string req_id = 7;

  // Request can be initiated by either payer of payee
  // Enum to determine if the current actor is payer or payee
  recurringpayment.ActorRole current_actor_role = 8;

  // a list of accounts along with their transaction attribute for making payment
  // Will be sent when auth is required and cred block type is NPCI
  repeated recurringpayment.TransactionAttribute transaction_attributes = 9;

  // user remarks for a recurring payment
  string remarks = 11;

  // expiry of the modify request
  // user needs to authorise before request gets expired
  google.protobuf.Timestamp expiry = 12;

  // Client details corresponding to the service initiating request. The combination of client and client_req_id must be unique.
  // deprecated - use client_request_id as the unique identifier here.
  celestial.ClientReqId client_id = 13 [deprecated = true];
}

enum ActorRole {
  ACTOR_ROLE_UNSPECIFIED = 0;
  // actor is payer
  ACTOR_ROLE_PAYER = 1;
  // actor is payee
  ACTOR_ROLE_PAYEE = 2;
}

message CreateModifyAttemptResponse {
  enum Status {
    OK = 0;

    NOT_FOUND = 5;

    PERMISSION_DENIED = 7;

    INTERNAL = 13;

    // modification is not allowed
    MODIFICATION_TIME_EXCEEDED = 100;

    // new end date is greater than the allowed end date
    INVALID_END_DATE = 101;
    // mandate already revoked
    MANDATE_REVOKED = 102;
    // mandate is currently paused
    MANDATE_PAUSED = 103;
    // mandate has been expired
    MANDATE_EXPIRED = 104;
  }
  rpc.Status status = 1;
  // unique order id corresponding to an modify request
  // client can use this order id to fetch the terminal status
  // With the migration from OMS to Celestial, we need not create an order in modify recurring payment
  // stage. Hence use of order_id in response is deprecated
  string order_id = 2 [deprecated = true];

  // transaction id corresponding to which cred block needs to be generated
  string txn_id = 3;
  // flag denoting if we need any user authentication for processing request
  bool is_authentication_required = 4;
  recurringpayment.CredBlockType cred_block_type = 5;
  // a list of accounts along with their transaction attribute for making payment
  // Will be sent when auth is required and cred block type is NPCI
  repeated recurringpayment.TransactionAttribute transaction_attributes = 6;
}

message AuthoriseRecurringPaymentModifyRequest {
  string recurring_payment_id = 1;

  recurringpayment.Credential credential = 2;

  // request id to be sent by caller in case idempotency around
  // order creation is important for the use case.
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_request_id = 3;

  // actor id of the actor authorising the recurring payment modify
  string current_actor_id = 4;

  // Client details corresponding to the service initiating request. The combination of client and client_req_id must be unique.
  // deprecated - use client_request_id as the unique identifier here.
  celestial.ClientReqId client_id = 5 [deprecated = true];
}
message AuthoriseRecurringPaymentModifyResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  recurringpayment.ActionDetailedStatus.DetailedStatus action_detailed_status = 2;
}

message CreateRevokeAttemptRequest {
  string recurring_payment_id = 1;

  // request id to be sent by caller in case idempotency around
  // order creation is important for the use case.
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_request_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // actor id of the user who has initiated the request.
  // If empty we will use initiated by to determine who has initiated the request
  string current_actor_id = 8;

  // the one who is initiating the request
  // To be sent only in case if current actor id is not present
  recurringpayment.InitiatedBy initiated_by = 3;

  string transaction_id = 4;

  // Request can be initiated by either payer of payee
  // Enum to determine if the current actor is payer or payee
  recurringpayment.ActorRole current_actor_role = 5;

  // provenance for the modification attempt
  recurringpayment.RecurrencePaymentProvenance provenance = 6 [(validate.rules).enum = {not_in: [0]}];

  // payload specific to a recurring payment type
  bytes payload = 7;

  // user remarks for a recurring payment
  string remarks = 9;

  // expiry of the revoke request
  // user needs to authorise before request gets expired
  google.protobuf.Timestamp expiry = 10;

  // Client details corresponding to the service initiating request. The combination of client and client_req_id must be unique.
  // deprecated - use client_request_id as the unique identifier here.
  celestial.ClientReqId client_id = 11 [deprecated = true];
}
message CreateRevokeAttemptResponse {
  enum Status {
    OK = 0;

    PERMISSION_DENIED = 7;

    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // unique order id corresponding to an revoke request
  // client can use this order id to fetch the terminal status
  // With the migration from OMS to Celestial, we need not create an order in revoke recurring payment
  // stage. Hence use of order_id in response is deprecated
  string order_id = 2 [deprecated = true];
  // transaction id corresponding to which cred block needs to be generated
  string txn_id = 3;
  // flag denoting if we need any user authentication for processing request
  bool is_authentication_required = 4;
  // common library via which cred block needs to be generated
  recurringpayment.CredBlockType cred_block_type = 5;
  // a list of accounts along with their transaction attribute for making payment
  // Will be sent when auth is required and cred block type is NPCI
  repeated recurringpayment.TransactionAttribute transaction_attributes = 6;
}

message AuthoriseRecurringPaymentRevokeRequest {
  string recurring_payment_id = 1;

  recurringpayment.Credential credential = 2;

  // unique request id sent by the client during recurring payment revoke
  string client_request_id = 3;

  // actor id of the actor authorising the recurring payment revoke
  string current_actor_id = 4;

  // Client details corresponding to the service initiating request. The combination of client and client_req_id must be unique.
  // deprecated - use client_request_id as the unique identifier here.
  celestial.ClientReqId client_id = 5 [deprecated = true];
}
message AuthoriseRecurringPaymentRevokeResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  recurringpayment.ActionDetailedStatus.DetailedStatus action_detailed_status = 2;
}

message GetRecurringPaymentDetailsByIdRequest {
  string recurring_payment_id = 1;

  string current_actor_id = 2;
}
message GetRecurringPaymentDetailsByIdResponse {
  enum Status {
    OK = 0;

    NOT_FOUND = 5;

    PERMISSION_DENIED = 7;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  recurringpayment.RecurringPayment recurring_payment = 2;

  int32 execution_count = 3;

  api.typesv2.common.Name name = 4;

  // image url of the payee
  string image_url = 5;

  // if image_url is null we will send background color
  string bg_color = 6;

  oneof AdditionalParams {
    recurringpayment.UPIMandateParams upi_mandate_params = 7;
    recurringpayment.EnachMandateParams enach_mandate_params = 11;
  }

  oneof StateParams {
    recurringpayment.PendingActionParams pending_action_params = 8;
  }

  // contains the actions that will be supported for a mandate like revoke,create etc
  repeated recurringpayment.Action supported_actions = 9;

  // the other actor for a recurring payment
  string secondary_actor_id = 10;
}

message GetRecurringPaymentByExternalIdRequest {
  // external id for which the recurring payment needs to be fetched
  string external_id = 1;
}
message GetRecurringPaymentByExternalIdResponse {
  enum Status {
    OK = 0;

    NOT_FOUND = 5;

    PERMISSION_DENIED = 7;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  recurringpayment.RecurringPayment recurring_payment = 2;
}

message UPIMandateParams {
  // unique internal identifier for a mandate
  string mandate_identifier = 1 [deprecated = true];

  // unique mandate number
  string umn = 2;

  // urn to be provided for QR/intent based payments
  string urn = 3;

  // other actor vpa
  string vpa = 4;
}

message EnachMandateParams {
  string umrn = 1;
}

message PendingActionParams {
  string transaction_id = 1;

  recurringpayment.CredBlockType cred_block_type = 2;

  // expiry of the pending request
  google.protobuf.Timestamp expiry = 3;
  // Additional parameters to be shown in case of a specific action
  // Eg: For modify request we need to show the updated parameters
  oneof AdditionalParams {
    recurringpayment.MutableParams updated_params = 4;
  }

  string client_request_id = 5;

  // a list of accounts along with their transaction attribute for making payment
  repeated TransactionAttribute transaction_attributes = 6;

  // list of supported pending actions basically use to authorize or decline a recurring payment
  // also contains info about pin required on not
  repeated RecurringPaymentPendingRequestAction pending_request_actions = 7;
}

message GetExecutionsForRecurringPaymentRequest {
  string recurring_payment_id = 1;
  // timestamp starting from which executions are fetched.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp start_timestamp = 2;
  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  int32 page_size = 3;
  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 executions starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 offset = 4;
  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true, descending ordered executions are returned from the given start timestamp,
  // If marked false, ascending ordered executions are returned from the given timestamp.
  bool descending = 5;

  // actor id of the actor requesting recurring payment execution details
  string current_actor_id = 6;
}
message GetExecutionsForRecurringPaymentResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // no activity found for the actor
    RECORD_NOT_FOUND = 5;

    PERMISSION_DENIED = 7;

    // In case RP is not created or in failed state
    FAILED_PRECONDITION = 9;

    // Internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  message ExecutionInfo {
    google.type.Money amount = 1;

    google.protobuf.Timestamp executed_at = 2;

    // image url of the payee
    string image_url = 3;

    string payee_name = 4;

    // if image_url is null we will send background color
    string bg_color = 5;

    string order_id = 6;

    // created timestamp for the action corresponding to recurring payment execution
    google.protobuf.Timestamp created_at = 7;

    RecurringPaymentTypeSpecificExecutionInfo recurring_payment_type_specific_execution_info = 8;

    recurringpayment.ActionState execution_action_state = 9;
  }

  repeated ExecutionInfo executions = 4;
}

message DeclineRecurringPaymentActionRequest {
  string recurring_payment_id = 1;

  recurringpayment.Action action = 2;

  // unique request id sent by the client during recurring payment action decline
  string client_request_id = 3;

  // actor id of the actor declining recurring payment action
  string current_actor_id = 4;
}
message DeclineRecurringPaymentActionResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // no activity found for the actor
    RECORD_NOT_FOUND = 5;

    PERMISSION_DENIED = 7;
    // In case RP is not created or in failed state
    FAILED_PRECONDITION = 9;
    // Internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message GetRecurringPaymentActionStatusRequest {
  string recurring_payment_id = 1;

  recurringpayment.Action action = 2;

  // request id to be sent by caller in case idempotency around
  // order creation is important for the use case.
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_request_id = 3;

  // actor id of the actor requesting for action status
  string current_actor_id = 4;

  // Client details corresponding to the service initiating request. The combination of client and client_req_id must be unique.
  // deprecated - use client_request_id as the unique identifier
  celestial.ClientReqId client_id = 5 [deprecated = true];
}
message GetRecurringPaymentActionStatusResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // no activity found for the actor
    RECORD_NOT_FOUND = 5;
    // Internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  enum ActionState {
    ACTION_STATE_UNSPECIFIED = 0;

    SUCCESS = 1;

    FAILURE = 2;

    IN_PROGRESS = 3;
  }
  ActionState action_state = 2;

  oneof metaData {
    /*
      If the PaymentAction was execute, then execution_meta_data will have details regarding execution.
    */
    recurringpayment.ExecutionMetaData execution_meta_data = 3;
  }

  recurringpayment.ActionDetailedStatus.DetailedStatus action_detailed_status = 4;

  // warning message to be shown to the user - For E.g. For mcc - 7322
  // we will show a warning that 'Transaction declined due to insufficiency of funds for this UPI Autopay is a punishable
  // offence under Section 25 of the Payment & Settlement Act, 2007 (PSS Act)'
  api.typesv2.common.Text warning_message = 5;

  // Deeplink corresponding to the next action to be done post recurring payment creation initiation
  frontend.deeplink.Deeplink post_action_deeplink = 6;
}

message GetRecurringPaymentsForActorRequest {

  string current_actor_id = 1;

  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp start_timestamp = 2;

  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  int32 page_size = 3;

  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 offset = 4;

  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  bool descending = 5;

  // states for which recurring payments needs to be fetched
  repeated recurringpayment.RecurringPaymentState states = 6;

}
message GetRecurringPaymentsForActorResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // no activity found for the actor
    RECORD_NOT_FOUND = 5;
    // Internal error while processing the request
    INTERNAL = 13;
  }

  // rpc status for the request
  rpc.Status status = 1;

  message RecurringPaymentTile {
    string id = 1;

    recurringpayment.RecurringPaymentType type = 2;

    recurringpayment.RecurringPaymentState state = 3;

    recurringpayment.AllowedFrequency allowed_frequency = 4;

    google.type.Money amount = 5;

    string name = 6;

    string image_url = 7;

    google.protobuf.Timestamp creation_timestamp = 8;

    // expiry in case of pending request state
    google.protobuf.Timestamp expiry = 9;

    // Background color in case if image url is null
    string bg_color = 10;

  }

  repeated RecurringPaymentTile recurring_payments = 2;

}

message MandateAuthHeader {
  // Device ID or fingerprint of the device that is registered with the partner bank
  upi.Device device = 1 [(validate.rules).message.required = true];

  // All the UPI transactions require Cred block generated through NPCI common library
  // Cred block is to be passed to NPCI in XML format
  // To prevent errors in data transformation and to keep business logic out of client,
  // frontend expects NPCI's cred block in a structured fashion
  upi.CredBlock npci_cred_block = 2;
}

message GetRecurringPaymentsCountForActorRequest {
  // actor for which recurring payment needs to be fetched
  string actor_id = 1;
}
message GetRecurringPaymentsCountForActorResponse {
  enum Status {
    OK = 0;

    NOT_FOUND = 5;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  map<string, int32> count = 2;
}

message TransactionAttribute {
  // A unique identifier belonging to the payer account
  string payer_account_id = 1;

  // A unique identifier to represent the P2P transaction itself
  // Transaction ID will be used in the request payloads with partner banks
  // 1. Be it as a unique transaction reference ID
  // 2. Part of secure PIN block

  // In certain workflows, e.g UPI dynamic QR scans or UPI intent based payments,
  // a transaction_id is already present in the QR or the intent. For those cases
  // the`transaction_id` in the request is echo-ed back in this field.
  //
  // The client is supposed to use this txn-id to generate cred block for UPI
  // as well as for non-UPI payments.
  string transaction_id = 2;

  // A unique identifier referring to a order number, subscription number, Bill ID,
  // booking ID, insurance, renewal reference, etc. from the merchant's system.
  //
  // In certain workflows, e.g UPI dynamic QR scans or UPI intent based payments,
  // a merchant_ref_id is already present in the QR or the intent. For those cases
  // the`merchant_ref_id` in the request is echo-ed back in this field.
  //
  // The client is supposed to use this merchant reference id as a part of `payInfo`
  // to generate cred block for UPI payments.
  // merchant-ref-id is referred to as `refId` in NPCI's CL.
  string merchant_ref_id = 3;

  // Payment protocol suggested by decision engine for the transaction.
  order.payment.PaymentProtocol payment_protocol = 4;

  // transaction reference URL is used by client to get credentials. This should be a URL when clicked provides customer with further transaction details
  // like complete bill details, bill copy, order copy, ticket details, etc.
  //
  // In certain workflows, e.g UPI dynamic QR scans or UPI intent based payments,
  // a reference url is already present in the QR or the intent. For those cases
  // the `reference_url` in the request is echo-ed back in this field.
  //
  // The client is supposed to use this reference url as a part of `payInfo`
  // to generate cred block for UPI payments.
  // reference url is referred to as `refUrl` in NPCI's CL.
  string reference_url = 5;

  // payee actor name ot be used by client to get credentials.

  // The client is supposed to use this merchant-ref-id to generate cred block for UPI payments.
  // reference url is referred to as refUrl in NPCI's CL.
  //
  // The client is supposed to use this field as a part of `payInfo`
  // to generate cred block for UPI payments.
  // payee actor name is referred as `payeeName` in NPCI's CL.
  string payee_actor_name = 6;

  // masked account number to belonging to the account to which payer pi belongs to
  // It is meant to be used by client to call NPCI CL's `Get Credential` service
  //
  // The client is supposed to use this field as a part of `payInfo`
  // to generate cred block for UPI payments.
  // masked account number is referred as `account` in NPCI's CL.
  string payer_masked_account_number = 7;

  // Amount of monies involved in the transaction
  google.type.Money amount = 8;

  // A note on the transaction by the user
  // This can either be one of the system generated tags or a custom note entered by the user
  string remarks = 9;

  // payment instrument information via which money is being sent. The payment instrument
  // can vary depending on the payment protocol. e.g.
  // In case of bank transfer based transfer it will be the masked account number of payer
  // In case of UPI based transfer it will be the the VPA belonging to the payer
  //
  // The client should consume this information as `payerAddr` as a part of `payInfo`
  // while generating cred block for UPI transfers
  //
  // NOTE: using any other address apart from the one passed from server while generating
  // cred block will lead to failure in transaction
  string payer_payment_instrument = 10;

  // payment instrument information via which money is being received. The payment instrument
  // can vary depending on the payment protocol. e.g.
  // In case of bank transfer based transfer it will be the masked account number of payee
  // In case of UPI based transfer it will be the the VPA belonging to the payee
  //
  // The client should consume this information as `payeeAddr` as a part of `payInfo`
  // while generating cred block for UPI transfers
  //
  // NOTE: using any other address apart from the one passed from server while generating
  // cred block will lead to failure in transaction
  string payee_payment_instrument = 11;

  // payment instrument info to be displayed.The payment instrument
  // can vary depending on the payment protocol. e.g.
  // In case of bank transfer based transfer it will be the masked account number of payee
  // In case of UPI based transfer if account@ifsc based then masked account number else vpa
  string display_payee_payment_instrument = 12;
}

message RecurringPaymentPendingRequestAction {

  PendingRequestAction pending_action = 1;
  // denotes if authorisation is required for the current action
  bool is_auth_required = 2;
}

enum PendingRequestAction {
  SUPPORTED_PENDING_ACTION_UNSPECIFIED = 0;
  // action to decline a pending request
  DECLINE = 1;
  // action to authorize a pending request
  AUTHORIZE = 2;
}

message UpiMandateExecuteInfo {
  order.payment.PaymentRequestInformation payment_request_info = 1;
  // Optional: Unique Transaction Reference for Online Txns like UPI
  string utr = 2;
  // user remarks / auto generated remarks
  string note = 3;
  // denotes the sequence number of execution. For example if a execution request comes for a recurring payment
  // for the first time it should be 1, for second time it should be 2.
  // If sequence number is not in order then validation will fail for mandate execution
  int32 seq_num = 4;
}

message ExecutionMetaData {
  // utr_ref_number represent the UTR Reference number that gets generated when a transaction is successful
  string utr_ref_number = 1;
  // transaction_timestamp represents the time at which the transaction moved to terminal state
  google.protobuf.Timestamp transaction_timestamp = 2;
  // failure response code will sent in case of txn failure
  // client can use this response code to fetch more details of txn failure such as payer response
  // reason, payee response reason which can be shown to the user.
  // In case of FE layer this response code can be used to generate the error view of the failure
  // Mapping for the status code to error view is present here : https://github.com/epifi/gamma/blob/d2353f54791c70c1b9aca7aa9f1b46050368f017/cmd/frontend/config/mappingJson/errorViewMapping.json
  string failure_response_code = 3;
  // payment protocol present in the transaction in which the SI was executed
  order.payment.PaymentProtocol exec_protocol = 4;
}

message AuthoriseExecuteRequest {
  // request id to be sent by caller in case idempotency around
  // order creation is important for the use case.
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_request_id = 1;

  // actor id who is initiating transaction
  string current_actor_id = 2;

  recurringpayment.Credential credential = 3;

  // Client details corresponding to the service initiating request. The combination of client and client_req_id must be unique.
  // deprecated - use client_request_id as the unique identifier here
  celestial.ClientReqId client_id = 4 [deprecated = true];
}
message AuthoriseExecuteResponse {
  enum Status {
    OK = 0;

    // transaction is in in-valid state
    // it can be mean either transaction is in terminal state or it has already been initiated.
    FAILED_PRECONDITION = 9;

    // transaction failed to initiate.
    // this can happen due to various reasons e.g. in-sufficient funds, account frozen temporarily.
    TRANSACTION_FAILED = 100;
  }
  rpc.Status status = 1;
}

message GetRecurringPaymentByIdRequest {
  // id for which the recurring payment needs to be fetched
  string id = 1;
}
message GetRecurringPaymentByIdResponse {
  enum Status {
    OK = 0;
    RECORD_NOT_FOUND = 5;
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  recurringpayment.RecurringPayment recurring_payment = 2;
}

message UpdateRecurringPaymentActionsRequest {

  // vendor req id for which the action needs to be updated
  string req_id = 1;

  recurringpayment.ActionDetailedStatus action_detailed_status = 2;
}
message UpdateRecurringPaymentActionsResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // no record found to update
    RECORD_NOT_FOUND = 5;
    // Internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;
}

message GetRecurringPaymentsRequest {
  // remitter actor
  string from_actor_id = 1;

  // beneficiary actor
  string to_actor_id = 2;
}
message GetRecurringPaymentsResponse {
  enum Status {
    OK = 0;

    NOT_FOUND = 5;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  repeated recurringpayment.RecurringPayment recurring_payments = 2;
}

message CreatePauseOrUnpauseAttemptRequest {
  string recurring_payment_id = 1;

  // request id to be sent by caller in case idempotency around
  // order creation is important for the use case.
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_request_id = 2;

  // action can either be pause or unpause
  recurringpayment.Action action = 3 [(validate.rules).enum = {in: [5, 6]}];

  // actor id of the user who has initiated the request.
  // If empty we will use initiated by to determine who has initiated the request
  string current_actor_id = 4;

  // the one who is initiating the request
  // To be sent only in case if current actor id is not present
  recurringpayment.InitiatedBy initiated_by = 5;

  string transaction_id = 6;

  // Request can be initiated by either payer of payee
  // Enum to determine if the current actor is payer or payee
  recurringpayment.ActorRole current_actor_role = 7;

  // provenance for the pause attempt
  recurringpayment.RecurrencePaymentProvenance provenance = 8 [(validate.rules).enum = {not_in: [0]}];

  // payload specific to a recurring payment type
  bytes payload = 9;

  // user can pause recurring payment for any time interval with start time greater than current time
  // post end time we will move recurring payment back to unpause/activated state
  api.typesv2.Interval pause_interval = 10;

  // user remarks
  string remarks = 11;

  // expiry of pause/unpause request
  google.protobuf.Timestamp expiry = 12;

  // celestial client request id
  // deprecated - use client_request_id as the unique identifier here
  celestial.workflow.ClientReqId client_id = 13 [deprecated = true];
}
message CreatePauseOrUnpauseAttemptResponse {
  enum Status {
    OK = 0;

    NOT_FOUND = 5;

    PERMISSION_DENIED = 7;

    FAILED_PRECONDITION = 9;

    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // transaction id corresponding to which cred block needs to be generated
  string txn_id = 2;
  // flag denoting if we need any user authentication for processing request
  bool is_authentication_required = 3;
  recurringpayment.CredBlockType cred_block_type = 4;
  // a list of accounts along with their transaction attribute for making payment
  // Will be sent when auth is required and cred block type is NPCI
  repeated recurringpayment.TransactionAttribute transaction_attributes = 5;
}

message AuthoriseRecurringPaymentActionRequest {
  string recurring_payment_id = 1;

  recurringpayment.Credential credential = 2;

  // unique request id sent by the client during recurring payment action
  string client_request_id = 3;

  // actor id of the actor authorising the recurring payment action
  string current_actor_id = 4;

  // action to be authorised
  recurringpayment.Action action = 5;

  // celestial client request id
  // deprecated - use client_request_id as the unique identifier here
  celestial.workflow.ClientReqId client_id = 6 [deprecated = true];
}
message AuthoriseRecurringPaymentActionResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  recurringpayment.ActionDetailedStatus.DetailedStatus action_detailed_status = 2;
}

message GetRecurringPaymentDetailsByClientReqIdRequest {
  // client request id for which the recurring payment details needs to be fetched
  string client_req_id = 1;
}
message GetRecurringPaymentDetailsByClientReqIdResponse {
  enum Status {
    OK = 0;

    NOT_FOUND = 5;

    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // recurring payment id for the client req id
  string recurring_payment_id = 2;
}

message CreateRecurringPaymentV1Request {
  // request id to be sent by caller in case idempotency around
  // order creation is important for the use case.
  // client_req_id must be a valid UUID (via RFC 4122)
  // This was primarily used when order was used for orchestration purposes. We are working on migrating workflows
  // to celestial where client related information is taken as a combination of client id and a client enum value
  // corresponding to the service initiating the request. Hence use of client_request_id must be deprecated
  string client_request_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // actor who has requested for creation of recurring payment
  string current_actor_id = 2 [(validate.rules).string.min_len = 1];

  // Details for the recurring which needs to be created
  RecurringPaymentCreationDetails recurring_payment_details = 3 [(validate.rules).message.required = true];

  // expiry of the create request
  // user needs to authorise before request gets expired
  google.protobuf.Timestamp expiry = 4;

  // payload contains any domain specific additional information client may want to provide
  // which is helpful for creating a recurring payment
  payload.RecurringPaymentTypeSpecificCreationPayload recurring_payment_type_specific_payload = 5;
  // [Optional] : Route via which the recurring payment is supposed to be fulfilled. If this is not
  // provided, this will be determined based on the other parameters in the request
  RecurringPaymentRoute hard_payment_route = 6;
  // deeplink which should be used to redirect user to the screen when the recurring payment
  // creation goes to a terminal state. This is to be provided by the domain so that we can
  // give the control back to the domain so that further handling can be done by them.
  frontend.deeplink.Deeplink post_creation_domain_deeplink = 7;
}
message CreateRecurringPaymentV1Response {
  enum Status {
    OK = 0;

    INTERNAL = 13;
    // In case user is blocked
    BLOCKED_USER = 101;

    // invalid start and end date provided in recurring payment creation
    // for eg- start date is greater than the end date
    INVALID_START_AND_END_DATE = 102;

    // amount limit exceeded for recurring payment creation
    AMOUNT_LIMIT_EXCEEDED = 103;
  }
  rpc.Status status = 1;

  // unique identifier for a recurring payment
  string recurring_payment_id = 2;

  // Deeplink corresponding to the next action to be done post recurring payment creation initiation
  frontend.deeplink.Deeplink next_action_deeplink = 3;
}

message RecurringPaymentCreationDetails {
  // actor for which recurring payment needs to be created
  string from_actor_id = 1 [(validate.rules).string.min_len = 1];

  // actor to which amount will be transferred
  string to_actor_id = 2 [(validate.rules).string.min_len = 1];

  // type of recurring payment which needs to be created
  recurringpayment.RecurringPaymentType type = 3 [(validate.rules).enum = {not_in: [0]}];

  // remitter payment instrument
  string pi_from = 4;

  // beneficiary payment instrument
  string pi_to = 5 [(validate.rules).string.min_len = 1];

  google.type.Money amount = 6 [(validate.rules).message.required = true];

  // start and end date of the recurring payment
  api.typesv2.Interval interval = 7 [(validate.rules).message.required = true];

  // Recurrence rule includes the allowed frequency and day of execution
  // If the day is sent as 0 then it will be treated as default case.
  // If allowed frequency is MONTHLY then day denotes the day of the month on which transaction is expected
  // Allowed Frequency is the time frequency after which the count of maximum allowed txns and
  // maximum allowed limit gets reset.
  // For example : If we have allowed frequency as DAILY and maximum amount as 100 and maximum allowed txns is 2
  // the we can have maximum 2 txns with total amount <= 100 in a day
  recurringpayment.RecurrenceRule recurrence_rule = 8 [(validate.rules).message.required = true];

  // maximum txns allowed over the allowed frequency time window
  int32 maximum_allowed_txns = 9;

  vendorgateway.Vendor partner_bank = 10;

  // Ownership of a recurring payment. Values can be EPIFI_TECH or EPIFI_WEALTH
  recurringpayment.RecurringPaymentOwnership ownership = 11 [(validate.rules).enum = {not_in: [0]}];

  recurringpayment.RecurrencePaymentProvenance provenance = 12 [(validate.rules).enum = {not_in: [0]}];

  recurringpayment.UIEntryPoint ui_entry_point = 13 [(validate.rules).enum = {not_in: [0]}];

  // amount can either be the fixed amount to be transferred or the maximum amount which can be transferred
  recurringpayment.AmountType amount_type = 14 [(validate.rules).enum = {not_in: [0]}];
  // denotes if the recurring payment details needs to be shared with the payee
  bool share_to_payee = 15;
}

message GetRecurringPaymentEssentialsRequest {
  // actor id of the actor for which recurring payment details is fetched
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // vendor from which recurring payment details is fetched
  vendorgateway.Vendor vendor = 2 [(validate.rules).enum = {not_in: [0]}];
  oneof Identifier {
    Enach enach_identifier = 3;
  }
  message Enach {
    string umrn = 1;
  }
}
message GetRecurringPaymentEssentialsResponse {
  rpc.Status status = 1;
  recurringpayment.RecurringPaymentEssentials recurring_payment_essentials = 2;
  // data fetched from vendor that is specific to the mandate
  // this data can be required to other services in the scenrios like (but not limited to):
  // creation/updation of order,transaction and payment instruments
  oneof recurring_payment_type_specific_data {
    EnachData enach_data = 3;
  }
  message EnachData {
    // unique mandate reference number used to uniquely identify an enach mandate
    string umrn = 1;
    // organisation name that presents the enach mandate
    string org_name = 2;
  }
}

message GetActionStatusV1Request {
  // type of action whose status needs to be fetched
  recurringpayment.Action action_type = 1;
  // request id which was used to initiate the action.
  string client_request_id = 2;
  // number of continous polls client has done on the polling screen
  // this is useful to prevent client from entering an infinite loop
  // todo(Harleen): evaluate moving deeplink logic to different rpc
  int32 poll_attempt = 3;
}
message GetActionStatusV1Response {
  // rpc status
  rpc.Status status = 1;
  // current status of action
  recurringpayment.ActionState action_status = 2;
  // current substatus of action
  recurringpayment.ActionSubState action_sub_status = 3;
  // deeplink where the user should be redirected to complete the action if user intervention is required,
  // can return a success/failure/in_progress screen deeplink if the action is already completed or is in a halt state.
  frontend.deeplink.Deeplink next_step_deeplink = 4;
  // ActionDetailedStatusInfo is useful to communicate the detailed status for the action taken on the recurring payment to the clients
  // Based upon the info, client can decide the next action
  // for e.g. for a specific FiStatusCode, client can show specific error view to the user
  recurringpayment.ActionDetailedStatusInfo action_detailed_status_info = 5;
}

message InitiateCreationAuthorizationV1Request {
  // request id which was used to initiate the action.
  string client_request_id = 1;
}
message InitiateCreationAuthorizationV1Response {
  // rpc status
  rpc.Status status = 1;

  // deeplink where the user should be redirected to perform auth, it would have all the necessary data to perform authorization,
  // e.g. can be deeplink to MPIN_SCREEN or npci web app for enach auth cases.
  frontend.deeplink.Deeplink auth_deeplink = 2;

  // screen where the user should be redirected to post the authorization action is done.
  // Note : the app should be redirected to this deeplink even if user dropped off from the authorization flow.
  frontend.deeplink.Deeplink post_auth_redirection = 3;
}

message AuthorizeCreationV1Request {
  // recurring payment which needs to be authorized
  string recurring_payment_id = 1;

  // needed for upi/standing instructions recurring payments where authorization happens on app once users enters the credentials in the inapp flow
  // would be empty for enach as there the authorization happens off app (user authorizes the creation on their bank site), so this rpc is called once auth callback is received from bank.
  recurringpayment.Credential auth_credential = 2;

  // additional metadata needed during authorization like UMRN in case of enach recurring payment
  recurringpayment.RecurringPaymentCreationAuthMetadata auth_metadata = 3;
}
message AuthorizeCreationV1Response {
  // rpc status
  rpc.Status status = 1;
}

message GetRecurringPaymentIdByVendorRequestIdRequest {
  RecurringPaymentType recurring_payment_type = 1;
  // vendor request id used for recurring payment creation
  string vendor_request_id = 2;
}
message GetRecurringPaymentIdByVendorRequestIdResponse {
  // rpc status
  rpc.Status status = 1;

  string recurring_payment_id = 2;
}

message ExecuteRecurringPaymentV1Request {
  // unique identifier for a recurring payment for which execution has to be done
  string recurring_payment_id = 1 [(validate.rules).string.min_len = 1];
  // request id to be sent by caller in case to uniquely identify the execute action for taken on the recurring payment
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_request_id = 2 [(validate.rules).string.min_len = 1];
  // amount of money for the execution
  google.type.Money amount = 3 [(validate.rules).message.required = true];
  // payload contains any domain specific additional information calling service may want to provide
  // which is useful for the execution of the recurring payment
  payload.RecurringPaymentTypeSpecificExecutionPayload recurring_payment_type_specific_payload = 4;
}
message ExecuteRecurringPaymentV1Response {
  enum Status {
    OK = 0;
    // Amount to be transferred is greater than the maximum amount - amount already transferred in the frequency range.
    MAXIMUM_AMOUNT_LIMIT_BREACHED = 100;
    // Number of transactions is greater than the maximum count allowed in the frequency range
    MAXIMUM_TRANSACTION_COUNT_BREACHED = 101;
    // mandate already revoked
    MANDATE_REVOKED = 102;
    // mandate is currently paused
    MANDATE_PAUSED = 103;
    // mandate has been expired
    MANDATE_EXPIRED = 104;
    // mandate registration has failed but mandate execution is being requested
    MANDATE_FAILED = 105;
  }
  rpc.Status status = 1;
}

message GetRecurringPaymentsByIdsRequest {
  // ids of the recurring payments we wish to retrieve
  repeated string ids = 1;
}
message GetRecurringPaymentsByIdsResponse {
  rpc.Status status = 1;

  repeated recurringpayment.RecurringPayment recurring_payments = 2;
}

message GetUpcomingTransactionsRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // time after which the upcoming txn is expected to happen
  // if not specified, current timestamp is takes as from_time
  google.protobuf.Timestamp from_time = 2;
  // time before which the upcoming txn is expected to happen
  google.protobuf.Timestamp to_time = 3 [(validate.rules).timestamp.required = true];
  // source from which upcoming transactions needs to be fetched
  repeated enums.UpcomingTransactionSource source = 4 [(validate.rules).repeated = {min_items: 1, unique: true}];

  // txns should be in credit/debit for the actor. If not specified, both are considered
  order.payment.AccountingEntryType accounting_entry = 5;
}

message GetUpcomingTransactionsResponse {
  rpc.Status status = 1;
  repeated UpcomingTransaction upcoming_txns = 2;
  // funds to be added to the given account to make sure all the debit upcoming transactions are successful
  // NOTE: as of now upcoming transactions service does not have PI level info i.e. it does not know from which account, the transaction will take place
  // so it will have info only for SAVINGS ACCOUNTS
  message AccountDetails {
    string account_id = 1;
    google.type.Money funds_to_add = 2;
  }
  repeated AccountDetails account_details = 11;
}

message UpcomingTransaction {
  enums.UpcomingTransactionEntityType type = 1;
  // source of upcoming transactions e.g. fittt, ds, etc
  enums.UpcomingTransactionSource source = 2;
  // unique identifier for the entity involved in the upcoming transaction
  // 1. actor id for actor
  // 2. merchant id for merchant
  // 3. sd account id for sd deposit account
  // 4. mutual fund id for mutual fund
  string entity_id = 3;

  string EntityName = 4;

  string EntityIconUrl = 5;

  // min_time after which the txn is expected to happen
  google.protobuf.Timestamp min_time = 6;

  // max_time before which the txn is expected to happen
  google.protobuf.Timestamp max_time = 7;

  // min amount expected to be debited/credited in txn
  google.type.Money min_amount = 8;

  // max amount expected to be debited/credited in txn
  google.type.Money max_amount = 9;
  // entry type defines the flow of money wrt the requested actor
  order.payment.AccountingEntryType entry_type = 10;

}

message RevokeRecurringPaymentV1Request {
  // unique identifier for a recurring payment for which execution has to be done
  string recurring_payment_id = 1 [(validate.rules).string.min_len = 1];
  // request id to be sent by caller in case to uniquely identify the revoke action taken on the recurring payment
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_request_id = 2 [(validate.rules).string.min_len = 1];
}

message RevokeRecurringPaymentV1Response {
  rpc.Status status = 1;
}

message GetRecurringPaymentsActionByVendorRequestIdRequest {
  // vendor request id for the recurring payment action which we want to fetch
  string vendor_request_id = 1;
}

message GetRecurringPaymentsActionByVendorRequestIdResponse {
  enum Status {
    // success
    OK = 0;
    // if request validation fails
    INVALID_ARGUMENT = 3;
    // no recurring payment action found for given vendor request id
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  recurringpayment.RecurringPaymentsAction recurring_payments_action = 2;
}
