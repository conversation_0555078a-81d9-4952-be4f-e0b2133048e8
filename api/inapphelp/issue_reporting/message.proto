syntax = "proto3";

package inapphelp.issue_reporting;

import "api/cx/issue_category/issue_category.proto";
import "api/inapphelp/issue_reporting/enums.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/inapphelp/issue_reporting";
option java_package = "com.github.epifi.gamma.api.inapphelp.issue_reporting";

// proto message to log all the queries asked by user to LLM.
message UserQueryLog {
  // row id used to DB
  string id = 1;
  string actor_id = 2;
  // query / question asked by user
  string user_query = 3;
  // user context (additional info related to the user to be provided in prompt, for ex : recent activity, support tickets, etc.)
  // fetched from backend service when user asked the query
  string user_context = 4;
  // response provided by model to user based on query and context provided
  string model_response = 5;
  // timestamp at which this row was created in DB
  google.protobuf.Timestamp created_at = 9;
  // timestamp at which this row was last updated
  google.protobuf.Timestamp updated_at = 10;
  // raw response from the model, to help debug why model reached a certain conclusion
  google.protobuf.Struct model_raw_response = 11;
  // metrics from the model, to observe how the model has evaluated the query
  google.protobuf.Struct model_metrics = 12;
  // issue category id (representing L1, L2, L3) for the given query
  string issue_category_id = 13;
  // id of ticket created for the given query
  int64 ticket_id = 14;
}

// Message to represent format in which customer 360 data is stored in redis
message Customer360Record {
  string kyc_level = 1 [json_name = "kyc_level"];
  string city = 2 [json_name = "city"];
  string employment_type = 3 [json_name = "employment_type"];
  string account_tier = 4 [json_name = "account_tier"];
  int64 amplifi_card_holder = 5 [json_name = "amplifi_card_holder"];
  int64 simplifi_card_holder = 6 [json_name = "simplifi_card_holder"];
  int64 magnifi_card_holder = 7 [json_name = "magnifi_card_holder"];
  string highest_spent_category_last_month = 8 [json_name = "highest_spent_category_last_month"];
}

// Message to represent Customer360 details for backend services
// note: we are creating new message to avoid coupling our backend service with DAG (which writes master record)
message Customer360Details {
  string kyc_level = 1;
  string city = 2;
  string employment_type = 3;
  string account_tier = 4;
  bool is_amplifi_card_holder = 5;
  bool is_simplifi_card_holder = 6;
  bool is_magnifi_card_holder = 7;
  string highest_spent_category_last_month = 8;
  bool is_physical_debit_card_holder = 9;
}

message UserIssue {
  // query that explains what issue is about
  // it can come from the query bank or user manually typed
  string query = 1;
  // source helps us identify how user has asked / raised the issues
  // E.g.: Manually typed, trending issues etc.
  IssueSource source = 2;
  // category of the issue
  cx.issue_category.IssueCategory issue_category = 3;
  // unique identifier for each query query
  // it helps us track user behaviour across a single query
  string query_id = 4;
}

// Proto message used to represent any type of issue that is known and added into the query bank
// we are indexing these issues in elastic search to suggest issues to users
message KnownIssue {
  // defines the issue from user's perspective
  string query_text = 1;
  // keywords defined by OPS team, searching for keywords should suggest the issue if they are matching
  repeated string keywords = 2;
  // issue category for the query
  cx.issue_category.IssueCategory issue_category = 3;
  // unique identifier for the issue
  string id = 4;
  // ideal query should be used internally whenever a user asks for this issue,
  // this query should be tailored to be the best query for this issue (i.e., canonical query)
  string ideal_query = 5;
  // concatenated value of (L1, L2, L3) this is done to simplify the aggregation of search results using this a key
  string combined_product_category = 6;
}

// Proto message to represent request and response for completion API of smart GPT service
// Contract shared can be found here: https://docs.google.com/document/d/1t8AMDJ4aJo6Mngq6zGxw4zcVhwrR8u_xfhzb6xjOlW8/edit
message SmartGptCompletionRequest {
  // user query for which we need to get completion
  string user_query = 1 [json_name = "query"];
  // context to be passed along with the query
  string query_context = 2 [json_name = "query_context"];
  string actor_id = 3 [json_name = "actor_id"];
  string request_id = 4 [json_name = "request_id"];
}

message SmartGptCompletionResponse {
  // completion generated by model
  string answer = 1 [json_name = "answer"];
  string actor_id = 2 [json_name = "actor_id"];
  string request_id = 3 [json_name = "request_id"];
  google.protobuf.Struct raw_response = 4 [json_name = "raw_response"];
  google.protobuf.Struct metrics = 5 [json_name = "metrics"];
}

// Proto message to represent request and response for chat API of smart GPT service
message SmartGptChatRequest {
  // context to be passed along with the query
  string query_context = 1 [json_name = "query_context"];
  string actor_id = 2 [json_name = "actor_id"];
  string request_id = 3 [json_name = "request_id"];
  // session_id needs to be constant for a single chat with the user
  string session_id = 4 [json_name = "session_id"];
  /*
  Each message will be of the following format -
  [
      "user",
      "Hi"
  ]
  where the first argument is the role - ['user', 'model', 'system']
  and the second argument is the message
   */
  repeated google.protobuf.ListValue messages = 5 [json_name = "messages"];
}

message SmartGptChatResponse {
  // completion generated by model
  string answer = 1 [json_name = "answer"];
  string actor_id = 2 [json_name = "actor_id"];
  string request_id = 3 [json_name = "request_id"];
  google.protobuf.Struct raw_response = 4 [json_name = "raw_response"];
  google.protobuf.Struct metrics = 5 [json_name = "metrics"];
  string session_id = 6 [json_name = "session_id"];
}