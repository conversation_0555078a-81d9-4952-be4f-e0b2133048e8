syntax = "proto3";

package inapphelp.issue_reporting;

import "api/cx/issue_config/config_payloads.proto";
import "api/cx/issue_config/enums.proto";
import "api/cx/method_options.proto";
import "api/cx/ticket/ticket.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/inapphelp/issue_reporting/enums.proto";
import "api/inapphelp/issue_reporting/message.proto";
import "api/rpc/status.proto";
import "api/search/summary/summary_v2.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/inapphelp/issue_reporting";
option java_package = "com.github.epifi.gamma.api.inapphelp.issue_reporting";

// Service that powers Fi app's issue reporting flow
// This service internally communicates with various backend service and also communicates with LLM to resolve user issue
service Service {
  // GetUserContextualPrompt RPC provides a string which allows us to set user's context to LLM before resolving user's query
  // It requires actorId (mandatory) in request and provides a string in response containing user's context
  rpc GetUserContextualPrompt (GetUserContextualPromptRequest) returns (GetUserContextualPromptResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // The RespondUserQuery RPC handles user queries and provides context-aware responses.
  // It utilizes a LLM to generate replies based on the user's input.
  rpc RespondUserQuery (RespondUserQueryRequest) returns (RespondUserQueryResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Returns issues grouped by their source, for all the sources provided in request
  // there are few additional parameters required to fetch issues from a particular source
  // (E.g.: Recent issues require actor_id, Search-suggested issues require user_query)
  rpc GetIssueBySource (GetIssueBySourceRequest) returns (GetIssueBySourceResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Returns help material, open support tickets, related FAQs, and customer care contact options based on the issue
  rpc GetIssueResolutionDetails (GetIssueResolutionDetailsRequest) returns (GetIssueResolutionDetailsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetIssueCategoryRecommendation returns issue category recommendation for a query string
  // It utilizes a data science model to categorize the issue present in query string
  rpc GetIssueCategoryRecommendation (GetIssueCategoryRecommendationRequest) returns (GetIssueCategoryRecommendationResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message GetIssueBySourceRequest {
  // required when we want to fetch recent issues (ISSUE_SOURCE_SEARCH_HISTORY)
  string actor_id = 1;
  // required when we want to fetch search-suggested issues (ISSUE_SOURCE_SEARCH_SUGGESTIONS)
  string user_query = 2;
  repeated IssueSource sources = 3;
  // Unique session identifier used for analytics purposes.
  // This session ID helps the analytics team correlate events triggered during the flow
  string session_id = 4;
}

message GetIssueBySourceResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
  }
  message IssueResponse {
    IssueSource source = 1;
    repeated UserIssue issues = 2;
  }

  rpc.Status status = 1;
  repeated IssueResponse issue_responses = 2;
}

message GetIssueResolutionDetailsRequest {
  UserIssue issue = 1 [(validate.rules).message.required = true];
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // Unique session identifier used for analytics purposes.
  // This session ID helps the analytics team correlate events triggered during the flow
  string session_id = 3;
}

message GetIssueResolutionDetailsResponse {
  enum Status {
    OK = 0;
    RECORD_NOT_FOUND = 5;
    INTERNAL = 13;
  }

  rpc.Status status = 1;
  cx.ticket.Ticket ticket = 2;
  string model_response = 3;
  repeated ContactOption contact_options = 4;
  repeated UserIssue similar_issues = 5;
  repeated cx.issue_config.AppResolutionDeeplink app_resolution_deeplinks = 6;
  // flag to indicate if ticket creation is required for the issue
  // this is passed in response so caller can decide if they want to create ticket or not
  bool is_ticket_creation_required = 7;
  string issue_category_id = 8;
  // unique identifier for user query, in-case user has typed multiple query in single session
  string query_id = 9;
  bool is_ai_generated_content_tag_required = 10;
  // summary widget from ask fi for the user query
  search.summary.SummaryV2 ask_fi_summary = 11;
  frontend.deeplink.Deeplink ask_fi_view_more_cta = 12;
  cx.issue_config.IssuePriority issue_priority = 13;
}

message RespondUserQueryRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  string user_query = 2 [(validate.rules).string.min_len = 1];
  // Unique session identifier used for analytics purposes.
  // This session ID helps the analytics team correlate events triggered during the flow
  string session_id = 3;
  // unique identifier for user query, in-case user has typed multiple query in single session
  string query_id = 4;
  // this flag will be used to identify if query source is for freshchat experimentation purpose and needs to be redirected to freshchat endpoint
  // temp flag : delete once experiment is concluded
  bool is_freshchat_experiment_enabled = 5;
  string conversation_history = 6;
}

message RespondUserQueryResponse {
  rpc.Status status = 1;
  string response = 2;
  int64 context_reference_score = 3;
}

message GetUserContextualPromptRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // Unique session identifier used for analytics purposes.
  // This session ID helps the analytics team correlate events triggered during the flow
  string session_id = 2;
  // unique identifier for user query, in-case user has typed multiple query in single session
  string query_id = 3;
}
message GetUserContextualPromptResponse {
  rpc.Status status = 1;
  string user_contextual_prompt = 2;
}

message GetIssueCategoryRecommendationRequest {
  // actor id of the user whose query is to be categorized
  string actor_id = 1;
  // query which is to be categorized
  string query = 2 [(validate.rules).string.min_len = 1];
  // Unique session identifier used for analytics purposes.
  // This session ID helps the analytics team correlate events triggered during the flow
  string session_id = 3;
  // unique identifier for user query, in-case user has typed multiple query in single session
  string query_id = 4;
  int64 number_of_recommendations = 5;
}

message GetIssueCategoryRecommendationResponse {
  rpc.Status status = 1;
  IssueCategoryRecommendation issue_category_recommendation = 2;
  repeated IssueCategoryRecommendation recommendations = 3;
}

message IssueCategoryRecommendation {
  string product_category = 1;
  string product_category_details = 2;
  string sub_category = 3;
  double confidence = 4;
}
