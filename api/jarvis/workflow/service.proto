syntax = "proto3";

package api.jarvis.workflow;

import "api/celestial/workflow/header.proto";
import "api/jarvis/frontend/form_ticket.proto";
import "api/jarvis/header.proto";
import "api/rpc/status.proto";


option go_package = "github.com/epifi/gamma/api/jarvis/workflow";

message CreateNewTicketWorkflowRequest {
  celestial.workflow.RequestHeader request_header = 1;
  .jarvis.Header jarvis_header = 2;
  // details of forms to be created
  api.jarvis.frontend.Ticket ticket_form = 3;
  // type of ticket to be created
  string ticket_type = 4;
  string form_id = 5;
}

message CreateNewTicketWorkflowResponse {
  celestial.workflow.ResponseHeader response_header = 1;
  // newly created form with version id
  api.jarvis.frontend.Ticket ticket_form = 2;
}


enum Environment {
  ENV_UNSPECIFIED = 0;
  ENV_QA = 1;
  ENV_PROD = 2;
}
// DeploymentParams specific deployment configuration
message JenkinsJobPaths {
  repeated string server_job_path = 1;
  repeated WorkerJobPath worker_job_paths = 2;
  string sync_dynamic_config_job_path = 3;
}

message WorkerJobPath {
  string worker_name = 1;                    // "card-worker"
  string binary_build_path = 2;              // "K8s/job/deploy/job/build/job/card-worker/"
  string deploy_path = 3;                    // "K8s/job/qa/job/card-worker/" (environment specific)
  optional string image_promotion_path = 4;  // "K8s/job/promote-image/" (only for prod)
}
message DeploymentParams {
  Environment environment = 1;
  string cherry_pick_branch = 2;
  JenkinsJobPaths jenkins_job_paths = 4;
  // Optional cherry-pick PR number for this environment
  optional int64 cherry_pick_pr_number = 5;
  reserved 3;
  reserved "jenkins_job_path";
}

message PRDeploymentWorkflowRequest {
  celestial.workflow.RequestHeader request_header = 1;
  .jarvis.Header jarvis_header = 2;
  int64 pr_number = 3;
  string repo_name = 4;
  // List of deployment parameters for different environments
  repeated DeploymentParams deployment_params = 5;
  // Slack channel ID where notifications will be sent
  string slack_channel_id = 6;
  // User email ID to tag the user on Slack
  string user_email_id = 7;
  // Optional metadata fields
  optional string pr_title = 8 [deprecated = true];
  optional string monorail_ticket = 9 [deprecated = true];
  // DeploymentMetadata contains information about the deployment for visibility and tracking
  DeploymentMetadata deployment_metadata = 10;
  // servers to deploy
  repeated string servers = 11;
  repeated string workers = 12;
  // servers to run config sync job
  repeated string sync_servers_cfg = 13;
  // workers to run config sync job
  repeated string sync_workers_cfg = 14;
}

message PRDeploymentWorkflowResponse {
  celestial.workflow.ResponseHeader response_header = 1;
  rpc.Status status = 2;
  string workflow_id = 3;
}

message DeploymentMetadata {
  // Title of the PR
  string pr_title = 1;
  // Monorail ticket link
  string monorail_ticket = 2;
  // Type of change being deployed
  string change_type = 3;
   // Who can see the change
  string visibility_type = 4;
  // Whether QA testing is required
  bool qa_testing_required = 5;
  // Whether the change has been tested by developers
  bool dev_tested = 6;
}
