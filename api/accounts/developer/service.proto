// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package api.accounts.developer;

import "api/cx/developer/db_state/db_state.proto";

option go_package = "github.com/epifi/gamma/api/accounts/developer";
option java_package = "com.github.epifi.gamma.api.accounts.developer";

// gRPC service to facilitate sherlock debugging tool for accounts service related entities
service AccountsDbStates {
  // service to fetch list of entities for which accounts can return the data
  rpc GetEntityList(cx.developer.db_state.GetEntityListRequest) returns (cx.developer.db_state.GetEntityListResponse) {}

  // For each entity as defined above, the parameter required to fetch that data will be different
  // This service will return appropriate params based on entity passed in request
  rpc GetParameterList(cx.developer.db_state.GetParameterListRequest) returns (cx.developer.db_state.GetParameterListResponse) {}

  // The actual get data API call where we will make a DB call to get the required data
  rpc GetData(cx.developer.db_state.GetDataRequest) returns (cx.developer.db_state.GetDataResponse) {}
}
